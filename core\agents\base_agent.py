#!/usr/bin/env python3
"""
Base Agent Module - Core Agent Classes and Utilities
Extracted from core/agent.py for modular organization
"""

import logging
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


class AgentError(Exception):
    """Custom exception for agent operations"""

    def __init__(
        self, message: str, error_code: str = "AGENT_ERROR", details: Dict = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class AgentLogger:
    """Centralized logging for the agent"""

    def __init__(self, log_level: str = "INFO", log_file: str = "logs/agent.log"):
        self.log_level = getattr(logging, log_level.upper())
        self.log_file = log_file

        # Ensure log directory exists
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=self.log_level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)],
        )

        self.logger = logging.getLogger("AI_Coding_Agent")

    def info(self, message: str, **kwargs):
        """Log info message"""
        # Filter out reserved logging parameters
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k not in ["args", "exc_info", "stack_info"]
        }
        self.logger.info(message, extra=safe_kwargs)

    def error(self, message: str, error: Exception = None, **kwargs):
        """Log error message with optional exception details"""
        # Filter out reserved logging parameters
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k not in ["args", "exc_info", "stack_info"]
        }
        if error:
            self.logger.error(f"{message}: {str(error)}", extra=safe_kwargs)
            self.logger.debug(f"Traceback: {traceback.format_exc()}")
        else:
            self.logger.error(message, extra=safe_kwargs)

    def warning(self, message: str, **kwargs):
        """Log warning message"""
        # Filter out reserved logging parameters
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k not in ["args", "exc_info", "stack_info"]
        }
        self.logger.warning(message, extra=safe_kwargs)

    def debug(self, message: str, **kwargs):
        """Log debug message"""
        # Filter out reserved logging parameters
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k not in ["args", "exc_info", "stack_info"]
        }
        self.logger.debug(message, extra=safe_kwargs)


class ErrorHandler:
    """Centralized error handling for the agent"""

    def __init__(self, logger: AgentLogger):
        self.logger = logger

    def handle_error(
        self, error: Exception, context: str = "Unknown"
    ) -> Dict[str, Any]:
        """Handle and log errors with context"""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "traceback": traceback.format_exc(),
        }

        # Log the error
        self.logger.error(
            f"Error in {context}: {error_info['error_message']}",
            error=error,
            error_info=error_info,
        )

        # Return structured error response
        return {
            "success": False,
            "error": error_info,
            "error_code": getattr(error, "error_code", "UNKNOWN_ERROR"),
        }
