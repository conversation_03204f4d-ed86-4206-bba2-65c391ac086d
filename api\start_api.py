#!/usr/bin/env python3
"""
API Startup Script for AI Coding Agent
Runs the FastAPI server with proper configuration and error handling.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def main():
    parser = argparse.ArgumentParser(description="AI Coding Agent API Server")
    parser.add_argument(
        "--host", default="127.0.0.1", help="Host to bind to (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", type=int, default=8000, help="Port to bind to (default: 8000)"
    )
    parser.add_argument(
        "--reload", action="store_true", help="Enable auto-reload for development"
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    args = parser.parse_args()

    # Get project root
    project_root = Path(__file__).parent.parent.absolute()

    # Check if virtual environment is activated
    if not hasattr(sys, "real_prefix") and not (
        hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
    ):
        print(
            "⚠️  Warning: Virtual environment not detected. Please activate your venv first."
        )
        print("   Run: .\\.venv\\Scripts\\Activate.ps1")
        return

    # Set environment variables
    env = os.environ.copy()
    env["PYTHONPATH"] = str(project_root)

    if args.debug:
        env["DEBUG"] = "true"

    # Build uvicorn command
    uvicorn_cmd = [
        sys.executable,
        "-m",
        "uvicorn",
        "api.main:app",
        "--host",
        args.host,
        "--port",
        str(args.port),
        "--log-level",
        "debug" if args.debug else "info",
    ]

    if args.reload:
        uvicorn_cmd.append("--reload")

    print(f"🚀 Starting AI Coding Agent API on {args.host}:{args.port}")
    print(f"📁 Project root: {project_root}")
    print(f"🔧 Command: {' '.join(uvicorn_cmd)}")
    print("=" * 60)

    try:
        # Run the FastAPI server
        subprocess.run(uvicorn_cmd, env=env, cwd=project_root)
    except KeyboardInterrupt:
        print("\n🛑 API server stopped by user")
    except Exception as e:
        print(f"❌ Error starting API server: {e}")
        print("\n💡 Make sure you have the required dependencies installed:")
        print("   pip install -r config/requirements.txt")


if __name__ == "__main__":
    main()
