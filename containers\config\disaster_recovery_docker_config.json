{"disaster_recovery_service": {"enabled": true, "port": 8086, "host": "0.0.0.0", "log_level": "INFO", "environment": "production"}, "container": {"name": "ai-coding-disaster-recovery", "image": "ai-coding-disaster-recovery:latest", "restart_policy": "unless-stopped", "resources": {"limits": {"cpus": "0.5", "memory": "1G"}, "reservations": {"cpus": "0.25", "memory": "512M"}}, "volumes": ["./data:/app/data", "./logs:/app/logs", "./backups:/app/backups", "./config:/app/config", "./models:/app/models", "./disaster_recovery:/app/disaster_recovery"], "environment_variables": {"PYTHONPATH": "/app", "DISASTER_RECOVERY_ENABLED": "true", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "DATABASE_URL": "******************************************************/ai_coding_agent", "OLLAMA_URL": "http://ollama:11434", "REDIS_URL": "redis://redis:6379"}}, "dependencies": {"required_services": ["api", "db", "redis", "ollama"], "health_check_dependencies": true}, "disaster_recovery_components": {"backup_system": {"enabled": true, "backup_types": ["full", "incremental", "differential"], "retention_days": 30, "compression": true, "encryption": true}, "recovery_system": {"enabled": true, "recovery_modes": ["full", "selective", "point_in_time"], "validation_required": true, "rollback_enabled": true}, "drill_system": {"enabled": true, "drill_types": ["full", "partial", "automated"], "schedule": "weekly", "success_threshold": 95.0}, "validation_system": {"enabled": true, "validation_checks": ["integrity", "size", "content", "accessibility"], "automated_validation": true, "alert_on_failure": true}}, "monitoring": {"health_check": {"endpoint": "/health", "interval": 30, "timeout": 10, "retries": 3, "start_period": 40}, "metrics": {"enabled": true, "endpoint": "/metrics", "collection_interval": 60}, "logging": {"level": "INFO", "format": "json", "output": "stdout"}}, "security": {"non_root_user": true, "user": "disasterrecovery", "group": "disasterrecovery", "capabilities": [], "read_only_root": false, "security_opt": ["no-new-privileges"]}, "networking": {"network": "ai-coding-network", "port_mapping": "8086:8086", "internal_port": 8086, "external_port": 8086}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 7, "backup_path": "/app/backups/disaster-recovery"}, "performance": {"max_concurrent_operations": 5, "operation_timeout": 3600, "connection_pool_size": 10, "cache_enabled": true, "cache_ttl": 300}, "api_endpoints": {"health": {"method": "GET", "path": "/health", "description": "Health check endpoint"}, "status": {"method": "GET", "path": "/status", "description": "Get disaster recovery system status"}, "backups_summary": {"method": "GET", "path": "/backups/summary", "description": "Get backups summary"}, "backups_list": {"method": "GET", "path": "/backups/list", "description": "Get list of available backups"}, "recovery_components": {"method": "GET", "path": "/recovery/components", "description": "Get disaster recovery components status"}, "create_backup": {"method": "POST", "path": "/backups/create", "description": "Create a new backup"}, "perform_recovery": {"method": "POST", "path": "/recovery/perform", "description": "Perform recovery from backup"}, "run_drill": {"method": "POST", "path": "/drills/run", "description": "Run a recovery drill"}, "validate_backup": {"method": "POST", "path": "/backups/validate", "description": "Validate backup integrity"}, "metrics": {"method": "GET", "path": "/metrics", "description": "Get disaster recovery metrics"}, "export": {"method": "GET", "path": "/export", "description": "Export disaster recovery data"}}}