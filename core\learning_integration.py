"""
Enhanced Learning Integration
Integrates with LearningAgent to enable pattern learning and continuous improvement
"""

import asyncio
import json
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class LearningPattern:
    """Represents a learned pattern"""

    id: str
    pattern_type: str
    description: str
    success_rate: float
    usage_count: int
    last_used: datetime
    created_at: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TaskOutcome:
    """Represents the outcome of a task for learning"""

    task_id: str
    task_type: str
    success: bool
    duration_minutes: float
    agent_used: str
    completion_time: datetime
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    user_satisfaction: Optional[int] = None  # 1-5 scale


class LearningIntegration:
    """Integrates learning capabilities with ArchitectAgent"""

    def __init__(self, learning_agent):
        self.learning_agent = learning_agent
        self.patterns: Dict[str, LearningPattern] = {}
        self.task_outcomes: List[TaskOutcome] = []
        self.pattern_counter = 0

    async def record_task_outcome(self, task_outcome: TaskOutcome):
        """Record the outcome of a task for learning"""
        self.task_outcomes.append(task_outcome)

        # Update patterns based on outcome
        await self._update_patterns_from_outcome(task_outcome)

        # Store in learning agent
        await self._store_in_learning_agent(task_outcome)

        logger.info(f"Recorded task outcome for {task_outcome.task_id}")

    async def get_suggested_approach(
        self, task_description: str, task_type: str
    ) -> Optional[Dict[str, Any]]:
        """Get suggested approach based on learned patterns"""
        # Find relevant patterns
        relevant_patterns = await self._find_relevant_patterns(
            task_description, task_type
        )

        if not relevant_patterns:
            return None

        # Get best pattern
        best_pattern = max(relevant_patterns, key=lambda p: p.success_rate)

        return {
            "pattern_id": best_pattern.id,
            "confidence": best_pattern.success_rate,
            "suggested_approach": best_pattern.description,
            "usage_count": best_pattern.usage_count,
            "metadata": best_pattern.metadata,
        }

    async def learn_from_user_feedback(self, task_id: str, feedback: Dict[str, Any]):
        """Learn from user feedback"""
        # Find the task outcome
        task_outcome = next(
            (to for to in self.task_outcomes if to.task_id == task_id), None
        )

        if task_outcome:
            # Update satisfaction score
            if "satisfaction" in feedback:
                task_outcome.user_satisfaction = feedback["satisfaction"]

            # Update patterns based on feedback
            await self._update_patterns_from_feedback(task_outcome, feedback)

            logger.info(f"Learned from user feedback for task {task_id}")

    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights from learning data"""
        if not self.task_outcomes:
            return {"message": "No learning data available"}

        total_tasks = len(self.task_outcomes)
        successful_tasks = len([to for to in self.task_outcomes if to.success])
        success_rate = successful_tasks / total_tasks if total_tasks > 0 else 0

        # Agent performance
        agent_performance = {}
        for outcome in self.task_outcomes:
            agent = outcome.agent_used
            if agent not in agent_performance:
                agent_performance[agent] = {
                    "total": 0,
                    "successful": 0,
                    "avg_duration": 0,
                }

            agent_performance[agent]["total"] += 1
            if outcome.success:
                agent_performance[agent]["successful"] += 1

        # Calculate averages
        for agent, stats in agent_performance.items():
            agent_tasks = [to for to in self.task_outcomes if to.agent_used == agent]
            if agent_tasks:
                stats["avg_duration"] = sum(
                    to.duration_minutes for to in agent_tasks
                ) / len(agent_tasks)
                stats["success_rate"] = stats["successful"] / stats["total"]

        # Pattern insights
        pattern_insights = []
        for pattern in self.patterns.values():
            pattern_insights.append(
                {
                    "id": pattern.id,
                    "type": pattern.pattern_type,
                    "success_rate": pattern.success_rate,
                    "usage_count": pattern.usage_count,
                    "last_used": pattern.last_used.isoformat(),
                }
            )

        return {
            "total_tasks": total_tasks,
            "success_rate": success_rate,
            "agent_performance": agent_performance,
            "patterns": pattern_insights,
            "top_patterns": sorted(
                pattern_insights, key=lambda p: p["success_rate"], reverse=True
            )[:5],
        }

    async def _update_patterns_from_outcome(self, task_outcome: TaskOutcome):
        """Update patterns based on task outcome"""
        # Create or update pattern based on task type and success
        pattern_key = f"{task_outcome.task_type}_{task_outcome.success}"

        if pattern_key in self.patterns:
            pattern = self.patterns[pattern_key]
            pattern.usage_count += 1
            pattern.last_used = task_outcome.completion_time

            # Update success rate
            total_uses = pattern.usage_count
            current_success = 1 if task_outcome.success else 0
            pattern.success_rate = (
                (pattern.success_rate * (total_uses - 1)) + current_success
            ) / total_uses
        else:
            # Create new pattern
            pattern_id = f"pattern_{self.pattern_counter:06d}"
            self.pattern_counter += 1

            pattern = LearningPattern(
                id=pattern_id,
                pattern_type=task_outcome.task_type,
                description=f"Pattern for {task_outcome.task_type} tasks",
                success_rate=1.0 if task_outcome.success else 0.0,
                usage_count=1,
                last_used=task_outcome.completion_time,
                created_at=datetime.now(),
                metadata={
                    "agent_used": task_outcome.agent_used,
                    "avg_duration": task_outcome.duration_minutes,
                },
            )

            self.patterns[pattern_key] = pattern

    async def _update_patterns_from_feedback(
        self, task_outcome: TaskOutcome, feedback: Dict[str, Any]
    ):
        """Update patterns based on user feedback"""
        pattern_key = f"{task_outcome.task_type}_{task_outcome.success}"

        if pattern_key in self.patterns:
            pattern = self.patterns[pattern_key]

            # Adjust success rate based on user satisfaction
            if task_outcome.user_satisfaction is not None:
                satisfaction_factor = task_outcome.user_satisfaction / 5.0
                pattern.success_rate = (pattern.success_rate + satisfaction_factor) / 2

            # Update metadata with feedback
            if "improvements" in feedback:
                pattern.metadata["suggested_improvements"] = feedback["improvements"]

            if "positive_aspects" in feedback:
                pattern.metadata["positive_aspects"] = feedback["positive_aspects"]

    async def _find_relevant_patterns(
        self, task_description: str, task_type: str
    ) -> List[LearningPattern]:
        """Find patterns relevant to the task"""
        relevant_patterns = []

        for pattern in self.patterns.values():
            # Check if pattern matches task type
            if pattern.pattern_type == task_type:
                relevant_patterns.append(pattern)

            # Check if pattern description matches task description keywords
            description_keywords = task_description.lower().split()
            pattern_keywords = pattern.description.lower().split()

            if any(keyword in pattern_keywords for keyword in description_keywords):
                relevant_patterns.append(pattern)

        return relevant_patterns

    async def _store_in_learning_agent(self, task_outcome: TaskOutcome):
        """Store task outcome in the learning agent"""
        try:
            # Create learning data
            learning_data = {
                "task_id": task_outcome.task_id,
                "task_type": task_outcome.task_type,
                "success": task_outcome.success,
                "duration_minutes": task_outcome.duration_minutes,
                "agent_used": task_outcome.agent_used,
                "completion_time": task_outcome.completion_time.isoformat(),
                "user_satisfaction": task_outcome.user_satisfaction,
            }

            if task_outcome.error_type:
                learning_data["error_type"] = task_outcome.error_type
                learning_data["error_message"] = task_outcome.error_message

            # Store in learning agent
            await self.learning_agent.store_experience(learning_data)

        except Exception as e:
            logger.error(f"Error storing in learning agent: {e}")

    async def get_pattern_recommendations(
        self, task_description: str
    ) -> List[Dict[str, Any]]:
        """Get pattern recommendations for a task"""
        recommendations = []

        # Analyze task description
        task_type = self._extract_task_type(task_description)

        # Get relevant patterns
        relevant_patterns = await self._find_relevant_patterns(
            task_description, task_type
        )

        # Sort by success rate and usage count
        sorted_patterns = sorted(
            relevant_patterns,
            key=lambda p: (p.success_rate, p.usage_count),
            reverse=True,
        )

        # Return top recommendations
        for pattern in sorted_patterns[:3]:
            recommendations.append(
                {
                    "pattern_id": pattern.id,
                    "confidence": pattern.success_rate,
                    "description": pattern.description,
                    "usage_count": pattern.usage_count,
                    "metadata": pattern.metadata,
                }
            )

        return recommendations

    def _extract_task_type(self, task_description: str) -> str:
        """Extract task type from description"""
        description_lower = task_description.lower()

        if any(
            word in description_lower
            for word in ["website", "frontend", "ui", "react", "vue"]
        ):
            return "frontend"
        elif any(
            word in description_lower
            for word in ["api", "backend", "server", "database"]
        ):
            return "backend"
        elif any(
            word in description_lower for word in ["container", "docker", "deploy"]
        ):
            return "container"
        elif any(
            word in description_lower for word in ["security", "auth", "permission"]
        ):
            return "security"
        elif any(word in description_lower for word in ["monitor", "log", "health"]):
            return "monitoring"
        else:
            return "general"


# Global instance (will be initialized with learning agent)
learning_integration = None
