#!/usr/bin/env python3
"""
Migration Manager for Supabase Integration

This module provides functionality for managing database migration files,
templates, and validation for the Supabase integration.
"""

import hashlib
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional


class MigrationManager:
    """Manages database migration files and templates"""

    def __init__(self, migrations_dir: str = "migrations"):
        """Initialize migration manager.

        Args:
            migrations_dir: Directory for migration files
        """
        self.migrations_dir = Path(migrations_dir)
        self.migrations_dir.mkdir(exist_ok=True)

        # Migration templates
        self.templates = {
            "create_table": self._get_create_table_template(),
            "add_column": self._get_add_column_template(),
            "create_index": self._get_create_index_template(),
            "add_foreign_key": self._get_add_foreign_key_template(),
            "custom": self._get_custom_template(),
        }

    def get_templates(self) -> Dict[str, str]:
        """Get available migration templates.

        Returns:
            Dictionary of template names and their SQL content
        """
        return self.templates

    def create_migration_file(
        self, name: str, sql_content: str, description: str = ""
    ) -> str:
        """Create a new migration file.

        Args:
            name: Migration name
            sql_content: SQL content for the migration
            description: Migration description

        Returns:
            Path to the created migration file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{name}.sql"
        file_path = self.migrations_dir / filename

        # Create migration content
        content = f"""-- Migration: {name}
-- Description: {description}
-- Created: {datetime.now().isoformat()}

{sql_content}
"""

        # Write file
        with open(file_path, "w") as f:
            f.write(content)

        return str(file_path)

    def read_migration_file(self, file_path: str) -> str:
        """Read migration file content.

        Args:
            file_path: Path to the migration file

        Returns:
            Content of the migration file
        """
        with open(file_path, "r") as f:
            return f.read()

    def validate_sql(self, sql_content: str) -> bool:
        """Validate SQL syntax (basic validation).

        Args:
            sql_content: SQL content to validate

        Returns:
            True if SQL appears valid, False otherwise
        """
        if not sql_content or not sql_content.strip():
            return False

        # Basic validation - check for common SQL keywords
        sql_upper = sql_content.upper()
        valid_keywords = [
            "CREATE",
            "ALTER",
            "DROP",
            "INSERT",
            "UPDATE",
            "DELETE",
            "SELECT",
            "TABLE",
            "COLUMN",
            "INDEX",
            "CONSTRAINT",
            "FOREIGN",
            "PRIMARY",
            "UNIQUE",
            "NOT",
            "NULL",
            "DEFAULT",
            "REFERENCES",
            "CASCADE",
        ]

        # Check if at least one valid keyword is present
        has_valid_keyword = any(keyword in sql_upper for keyword in valid_keywords)

        # Check for basic SQL structure (semicolon at end)
        has_semicolon = sql_content.strip().endswith(";")

        return has_valid_keyword and has_semicolon

    def get_migration_checksum(self, sql_content: str) -> str:
        """Generate checksum for migration content.

        Args:
            sql_content: SQL content to generate checksum for

        Returns:
            SHA-256 checksum of the content
        """
        return hashlib.sha256(sql_content.encode()).hexdigest()

    def list_migrations(self) -> List[Dict[str, Any]]:
        """List all migration files.

        Returns:
            List of migration file information
        """
        migrations = []

        for file_path in self.migrations_dir.glob("*.sql"):
            try:
                content = self.read_migration_file(str(file_path))
                stat = file_path.stat()

                migration_info = {
                    "filename": file_path.name,
                    "path": str(file_path),
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_ctime),
                    "modified": datetime.fromtimestamp(stat.st_mtime),
                    "checksum": self.get_migration_checksum(content),
                }

                # Parse migration header
                lines = content.split("\n")
                for line in lines[:5]:  # Check first 5 lines for metadata
                    if line.startswith("-- Migration:"):
                        migration_info["name"] = line.split(":", 1)[1].strip()
                    elif line.startswith("-- Description:"):
                        migration_info["description"] = line.split(":", 1)[1].strip()
                    elif line.startswith("-- Created:"):
                        migration_info["created_date"] = line.split(":", 1)[1].strip()

                migrations.append(migration_info)

            except Exception as e:
                # Skip files that can't be read
                continue

        # Sort by creation time
        migrations.sort(
            key=lambda x: (
                float(str(x.get("created", 0))) if x.get("created") is not None else 0.0
            ),
            reverse=False,
        )
        return migrations

    def delete_migration_file(self, file_path: str) -> bool:
        """Delete a migration file.

        Args:
            file_path: Path to the migration file to delete

        Returns:
            True if file was deleted, False otherwise
        """
        try:
            path = Path(file_path)
            if path.exists() and path.suffix == ".sql":
                path.unlink()
                return True
            return False
        except Exception:
            return False

    def _get_create_table_template(self) -> str:
        """Get create table template."""
        return """-- Create a new table
CREATE TABLE table_name (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_table_name_created_at ON table_name(created_at);

-- Add RLS (Row Level Security)
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own data" ON table_name
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own data" ON table_name
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own data" ON table_name
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own data" ON table_name
    FOR DELETE USING (auth.uid() = user_id);"""

    def _get_add_column_template(self) -> str:
        """Get add column template."""
        return """-- Add a new column to an existing table
ALTER TABLE table_name
ADD COLUMN new_column_name TEXT;

-- Add a column with a default value
ALTER TABLE table_name
ADD COLUMN status TEXT DEFAULT 'active';

-- Add a column with a NOT NULL constraint
ALTER TABLE table_name
ADD COLUMN required_field TEXT NOT NULL DEFAULT 'default_value';"""

    def _get_create_index_template(self) -> str:
        """Get create index template."""
        return """-- Create a single column index
CREATE INDEX idx_table_name_column_name ON table_name(column_name);

-- Create a composite index
CREATE INDEX idx_table_name_col1_col2 ON table_name(column1, column2);

-- Create a unique index
CREATE UNIQUE INDEX idx_table_name_unique_field ON table_name(unique_field);

-- Create a partial index
CREATE INDEX idx_table_name_active ON table_name(column_name)
WHERE status = 'active';"""

    def _get_add_foreign_key_template(self) -> str:
        """Get add foreign key template."""
        return """-- Add a foreign key constraint
ALTER TABLE child_table
ADD CONSTRAINT fk_child_parent
FOREIGN KEY (parent_id) REFERENCES parent_table(id);

-- Add a foreign key with cascade delete
ALTER TABLE child_table
ADD CONSTRAINT fk_child_parent_cascade
FOREIGN KEY (parent_id) REFERENCES parent_table(id)
ON DELETE CASCADE;"""

    def _get_custom_template(self) -> str:
        """Get custom template."""
        return """-- Write your custom SQL migration here
-- This template is for custom migrations that don't fit the standard patterns

-- Example: Complex data transformation
UPDATE users
SET email_verified = true
WHERE email_verified IS NULL;

-- Example: Data migration
INSERT INTO new_table (id, name, created_at)
SELECT id, name, created_at FROM old_table
WHERE status = 'active';"""


class MigrationFileManager:
    """Manages migration file operations for specific projects"""

    def __init__(self, project_path: str):
        """Initialize migration file manager.

        Args:
            project_path: Path to the project directory
        """
        self.project_path = Path(project_path)
        self.migrations_dir = self.project_path / "migrations"
        self.migrations_dir.mkdir(exist_ok=True)
        self.migration_manager = MigrationManager(str(self.migrations_dir))

    def create_migration_from_template(
        self, template: str, variables: Dict[str, str], name: str
    ) -> tuple[bool, str]:
        """Create migration from template.

        Args:
            template: Template name
            variables: Template variables
            sql_content: SQL content
            name: Migration name

        Returns:
            Tuple of (success, result)
        """
        try:
            templates = self.migration_manager.get_templates()
            if template not in templates:
                return False, f"Template '{template}' not found"

            sql_content = templates[template]
            # Simple variable substitution
            for key, value in variables.items():
                sql_content = sql_content.replace(f"{{{key}}}", str(value))

            file_path = self.migration_manager.create_migration_file(name, sql_content)
            return True, file_path
        except Exception as e:
            return False, str(e)

    def create_custom_migration(self, name: str, sql_content: str) -> tuple[bool, str]:
        """Create custom migration.

        Args:
            name: Migration name
            sql_content: SQL content

        Returns:
            Tuple of (success, result)
        """
        try:
            file_path = self.migration_manager.create_migration_file(name, sql_content)
            return True, file_path
        except Exception as e:
            return False, str(e)


class MigrationDeploymentManager:
    """Manages migration deployment to Supabase"""

    def __init__(self, project_path: str, supabase_cli):
        """Initialize migration deployment manager.

        Args:
            project_path: Path to the project directory
            supabase_cli: Supabase CLI instance
        """
        self.project_path = Path(project_path)
        self.supabase_cli = supabase_cli

    def deploy_migration(
        self, migration_file: str, force: bool = False, dry_run: bool = False
    ):
        """Deploy a migration to Supabase.

        Args:
            migration_file: Path to migration file
            force: Force deployment
            dry_run: Perform dry run

        Returns:
            Deployment result
        """
        try:
            if dry_run:
                # Simulate deployment
                return DeploymentResult(success=True, message="Dry run completed")

            # Use Supabase CLI to deploy migration
            result = self.supabase_cli.db_push(migration_file, force=force)
            return DeploymentResult(success=result.success, message=result.message)
        except Exception as e:
            return DeploymentResult(success=False, error=str(e))

    def rollback_migration(
        self, migration_name: str, force: bool = False, dry_run: bool = False
    ):
        """Rollback a migration from Supabase.

        Args:
            migration_name: Name of migration to rollback
            force: Force rollback
            dry_run: Perform dry run

        Returns:
            Rollback result
        """
        try:
            if dry_run:
                # Simulate rollback
                return DeploymentResult(
                    success=True, message="Dry run rollback completed"
                )

            # Use Supabase CLI to rollback migration
            result = self.supabase_cli.db_reset(migration_name, force=force)
            return DeploymentResult(success=result.success, message=result.message)
        except Exception as e:
            return DeploymentResult(success=False, error=str(e))


class DeploymentResult:
    """Result of a deployment operation"""

    def __init__(self, success: bool, message: str = "", error: str = ""):
        self.success = success
        self.message = message
        self.error = error
