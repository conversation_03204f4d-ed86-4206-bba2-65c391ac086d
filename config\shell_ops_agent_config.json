{"agent_name": "ShellOpsAgent", "version": "1.0.0", "description": "Specialized agent for shell operations and file management tasks", "model_settings": {"model_name": "qwen2.5-coder:3b", "system_prompt": "You are a shell operations expert, proficient in bash, PowerShell, and file system management. Your primary responsibility is to execute shell commands and manage files safely and efficiently. Adhere to the following principles:\n1.  **Safety First:** Always prioritize safety. Use dry-run mode when available, and ask for confirmation before executing potentially destructive commands (e.g., `rm -rf`).\n2.  **Cross-Platform:** Write commands that are compatible with both Linux (bash) and Windows (PowerShell) environments whenever possible.\n3.  **Idempotency:** Ensure that your operations are idempotent, meaning they can be run multiple times without changing the result beyond the initial application.\n4.  **Error Handling:** Implement robust error handling for all shell commands and file operations. Check for command success and handle failures gracefully."}, "safety_settings": {"safe_operations": true, "confirmation_required": true, "dry_run_mode": false, "backup_before_operations": true, "rollback_capability": true, "operation_logging": true}, "file_operations": {"file_manager": {"enabled": true, "unified_operations": true, "cross_platform": true, "error_handling": true, "progress_tracking": true}, "file_validation": {"path_validation": true, "permission_checking": true, "existence_verification": true, "integrity_checking": true}, "file_backup": {"auto_backup": true, "backup_strategy": "incremental", "backup_retention": 7, "backup_compression": true, "backup_verification": true}}, "shell_operations": {"command_execution": {"timeout_seconds": 300, "output_capture": true, "error_handling": true, "retry_mechanism": true, "command_validation": true}, "environment_management": {"env_variable_handling": true, "path_management": true, "working_directory": true, "shell_compatibility": true}, "process_management": {"process_monitoring": true, "resource_tracking": true, "cleanup_operations": true, "signal_handling": true}}, "security_settings": {"command_sanitization": true, "path_traversal_protection": true, "privilege_escalation_prevention": true, "dangerous_command_blocking": true, "audit_logging": true, "access_control": true}, "backup_operations": {"backup_manager": {"enabled": true, "config_file": "config/backup_config.json", "automated_backups": true, "backup_scheduling": true, "backup_verification": true}, "backup_strategies": {"full_backup": true, "incremental_backup": true, "differential_backup": true, "snapshot_backup": true}, "backup_storage": {"local_storage": true, "remote_storage": false, "compression": true, "encryption": false}}, "monitoring": {"operation_monitoring": {"performance_tracking": true, "resource_usage": true, "error_tracking": true, "success_rate": true}, "system_monitoring": {"disk_usage": true, "memory_usage": true, "cpu_usage": true, "network_usage": true}}, "project_structure": {"operations_directory": "operations", "backup_directory": "backups", "logs_directory": "logs/operations", "temp_directory": "temp", "config_directory": "config"}, "dependencies": {"core_dependencies": ["psutil", "pathlib", "shutil", "subprocess"], "monitoring_dependencies": ["watchdog", "schedule"], "security_dependencies": ["cryptography", "<PERSON><PERSON><PERSON>"]}, "logging": {"level": "INFO", "format": "json", "file_logging": true, "console_logging": true, "log_rotation": true, "structured_logging": true}, "development": {"debug_mode": false, "development_features": true, "test_mode": false, "experimental_features": false}}