"""
Waveform generation functions for signal processing.

This module provides various waveform generation operations.
"""

import numpy as np
from typing import Any, Optional, Tuple, Union


def chirp(
    t: np.ndarray,
    f0: float,
    f1: float,
    t1: float,
    method: str = "linear",
    phi: float = 0,
    vertex_zero: bool = True,
) -> np.ndarray:
    """
    Frequency-swept cosine generator.

    Args:
        t: Times at which to evaluate the waveform
        f0: Frequency at time t=0
        f1: Frequency at time t=t1
        t1: Time at which f1 is specified
        method: Kind of frequency sweep
        phi: Phase offset
        vertex_zero: If True, the vertex of the parabola is at t=0

    Returns:
        Time-series of the chirp signal
    """
    # This is a simplified implementation
    if method == "linear":
        # Linear frequency sweep
        f = f0 + (f1 - f0) * t / t1
    elif method == "quadratic":
        # Quadratic frequency sweep
        f = f0 + (f1 - f0) * (t / t1) ** 2
    elif method == "logarithmic":
        # Logarithmic frequency sweep
        f = f0 * (f1 / f0) ** (t / t1)
    else:
        raise ValueError(f"Unknown method: {method}")

    # Generate the chirp signal
    return np.cos(2 * np.pi * f * t + phi)


def gausspulse(
    t: np.ndarray,
    fc: float = 1000,
    bw: float = 0.5,
    bwr: float = -6,
    tpr: float = -60,
    retquad: bool = False,
    retenv: bool = False,
) -> Union[np.ndarray, Tuple[np.ndarray, ...]]:
    """
    Return a Gaussian-modulated sinusoid.

    Args:
        t: Time array
        fc: Center frequency
        bw: Fractional bandwidth in frequency domain
        bwr: Reference level at which fractional bandwidth is calculated
        tpr: If tpr is not None, the function returns the time at which
             the pulse magnitude falls below tpr dB
        retquad: If True, return the quadrature (imaginary) part
        retenv: If True, return the envelope

    Returns:
        Gaussian pulse
    """
    # This is a simplified implementation
    # Generate Gaussian envelope
    sigma = 1 / (2 * np.pi * bw * fc)
    envelope = np.exp(-t**2 / (2 * sigma**2))

    # Generate carrier
    carrier = np.cos(2 * np.pi * fc * t)

    # Combine
    signal = envelope * carrier

    if retquad and retenv:
        return signal, envelope * np.sin(2 * np.pi * fc * t), envelope
    elif retquad:
        return signal, envelope * np.sin(2 * np.pi * fc * t)
    elif retenv:
        return signal, envelope
    else:
        return signal


def sawtooth(
    t: np.ndarray,
    width: float = 1.0,
) -> np.ndarray:
    """
    Return a periodic sawtooth or triangle waveform.

    Args:
        t: Time array
        width: Width of the rising ramp as a proportion of the total cycle

    Returns:
        Sawtooth waveform
    """
    # This is a simplified implementation
    # Normalize time to [0, 1) range
    t_norm = (t % 1.0)

    # Generate sawtooth
    if t_norm < width:
        return 2 * t_norm / width - 1
    else:
        return 2 * (t_norm - width) / (1 - width) - 1


def square(
    t: np.ndarray,
    duty: float = 0.5,
) -> np.ndarray:
    """
    Return a periodic square-wave waveform.

    Args:
        t: Time array
        duty: Duty cycle

    Returns:
        Square waveform
    """
    # This is a simplified implementation
    # Normalize time to [0, 1) range
    t_norm = (t % 1.0)

    # Generate square wave
    return np.where(t_norm < duty, 1, -1)


def sweep_poly(
    t: np.ndarray,
    poly: np.ndarray,
    phi: float = 0,
) -> np.ndarray:
    """
    Frequency-swept cosine generator, with a time-dependent frequency.

    Args:
        t: Times at which to evaluate the waveform
        poly: Coefficients of the polynomial
        phi: Phase offset

    Returns:
        Time-series of the sweep signal
    """
    # This is a simplified implementation
    # Calculate instantaneous frequency
    f = np.polyval(poly, t)

    # Generate the sweep signal
    return np.cos(2 * np.pi * f * t + phi)


# Export the main functions
__all__ = ["chirp", "gausspulse", "sawtooth", "square", "sweep_poly"]
