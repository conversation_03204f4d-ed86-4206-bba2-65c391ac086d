name: Docker CI/CD Pipeline
true:
  pull_request:
    branches:
    - main
  push:
    branches:
    - main
    - develop
  release:
    types:
    - published
env:
  IMAGE_NAME: ${{ github.repository }}
  REGISTRY: ghcr.io
jobs:
  build:
    if: github.event_name == 'push' || github.event_name == 'release'
    name: Build and Push
    needs: test
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        password: ${{ secrets.GITHUB_TOKEN }}
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
    - id: meta
      name: Extract metadata
      uses: docker/metadata-action@v5
      with:
        images: '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api

          ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-ollama

          '
        tags: 'type=ref,event=branch

          type=ref,event=pr

          type=semver,pattern={{version}}

          type=semver,pattern={{major}}.{{minor}}

          type=sha

          '
    - name: Build and push API image
      uses: docker/build-push-action@v5
      with:
        cache-from: type=gha
        cache-to: type=gha,mode=max
        context: .
        file: ./api/Dockerfile
        labels: ${{ steps.meta.outputs.labels }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
    - name: Build and push Ollama image
      uses: docker/build-push-action@v5
      with:
        cache-from: type=gha
        cache-to: type=gha,mode=max
        context: .
        file: ./ollama/Dockerfile
        labels: ${{ steps.meta.outputs.labels }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
  deploy-production:
    environment: production
    if: github.event_name == 'release'
    name: Deploy to Production
    needs: build
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Deploy to production
      run: 'echo "Deploying to production environment..."

        # Add your production deployment commands here

        # Example: docker-compose -f docker-compose.yml -f docker-compose.prod.yml
        up -d

        '
  deploy-staging:
    environment: staging
    if: github.ref == 'refs/heads/develop'
    name: Deploy to Staging
    needs: build
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Deploy to staging
      run: 'echo "Deploying to staging environment..."

        # Add your staging deployment commands here

        # Example: docker-compose -f docker-compose.yml -f docker-compose.staging.yml
        up -d

        '
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        format: sarif
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api:${{ github.sha }}
        output: trivy-results.sarif
    - if: always()
      name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: trivy-results.sarif
  test:
    name: Test
    runs-on: ubuntu-latest
    services:
      postgres:
        env:
          POSTGRES_DB: test_db
          POSTGRES_PASSWORD: postgres
        image: postgres:15
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s
          --health-retries 5
        ports:
        - 5432:5432
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Build API image
      run: docker build -f api/Dockerfile -t $IMAGE_NAME-api:${{ github.sha }} .
    - name: Build Ollama image
      run: docker build -f ollama/Dockerfile -t $IMAGE_NAME-ollama:${{ github.sha
        }} .
    - name: Start services
      run: 'docker-compose up -d db redis

        sleep 10

        '
    - name: Check cursor rules monitor status
      run: "# Ensure monitor is running\npython scripts/check_monitor_status.py ||\
        \ { echo \"Monitor not running \xE2\u20AC\u201C aborting\"; exit 1; }\n"
    - name: Run tests
      run: "docker run --rm \\\n  --network host \\\n  -e DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_db\
        \ \\\n  -e REDIS_URL=redis://localhost:6379 \\\n  -e OLLAMA_URL=http://localhost:11434\
        \ \\\n  $IMAGE_NAME-api:${{ github.sha }} \\\n  python -m pytest tests/ -v\
        \ --cov=api --cov=core --cov-report=xml\n"
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
'on':
- push
- pull_request
