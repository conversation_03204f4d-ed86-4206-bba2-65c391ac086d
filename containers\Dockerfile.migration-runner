# Database Migration Runner Dockerfile
# Multi-stage build for production deployment safety

# Stage 1: Builder
FROM python:3.11-slim AS builder

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY config/requirements.txt .
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir --prefix=/install -r requirements.txt

# Stage 2: Runtime
FROM python:3.11-slim AS runtime

# Create non-root user for security
RUN groupadd -r migrationrunner && useradd -r -g migrationrunner migrationrunner

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /install /usr/local

# Copy application code
COPY core/ ./core/
COPY database/ ./database/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY migrations/ ./migrations/

# Create necessary directories
RUN mkdir -p /app/logs /app/backups /app/migrations /app/data \
    && chown -R migrationrunner:migrationrunner /app

# Switch to non-root user
USER migrationrunner

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV MIGRATION_ENVIRONMENT=production
ENV LOG_LEVEL=INFO
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Expose port for health checks
EXPOSE 8086

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8086/health || exit 1

# Default command
CMD ["python", "-m", "scripts.migration_runner"]
