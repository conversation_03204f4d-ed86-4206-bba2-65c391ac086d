"""
Supabase project management commands.

This module provides CLI commands for linking, unlinking, and configuring
Supabase projects.
"""

import logging
from typing import Optional

import click

from cli.error_handler import error_handler
from cli.state_tracker import track_event
from db import project_manager, supabase_config_manager
from db.models import Project
from utils.decorators import CliError, require_project_access, with_db
from utils.tasks import add_infra_task

logger = logging.getLogger(__name__)


@click.group()
def supabase() -> None:
    """Supabase project management commands"""
    pass


@supabase.command()
@click.argument("project_id", type=int)
@click.option("--project-url", required=True, help="Supabase project URL")
@click.option("--api-key", required=True, help="Supabase API key")
@click.option("--service-role-key", help="Supabase service role key")
@click.option("--database-url", help="Database connection URL")
@click.option("--region", help="Supabase region")
@click.option("--organization-id", help="Supabase organization ID")
@click.pass_context
@error_handler
@with_db
@require_project_access
def link(
    ctx,
    project_id: int,
    project_url: str,
    api_key: str,
    service_role_key: Optional[str],
    database_url: Optional[str],
    region: Optional[str],
    organization_id: Optional[str],
) -> None:
    """Link a project to Supabase backend"""
    track_event("supabase", "Linking project to Supabase", {"project_id": project_id})

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Linking project {project_id} to Supabase at {project_url}")

    # Check if config already exists
    existing_config = supabase_config_manager.get_by_project(db, project_id)

    config_data = {
        "project_id": project_id,
        "project_url": project_url,
        "api_key": api_key,
        "service_role_key": service_role_key,
        "database_url": database_url,
        "region": region,
        "organization_id": organization_id,
    }

    if existing_config:
        # Update existing config
        supabase_config_manager.update(db, db_obj=existing_config, obj_in=config_data)
        logger.info(f"Updated Supabase configuration for project {project_id}")
        click.echo(f"Updated Supabase configuration for project {project_id}")
    else:
        # Create new config
        supabase_config_manager.create(db, obj_in=config_data)
        logger.info(f"Created Supabase configuration for project {project_id}")
        click.echo(f"Created Supabase configuration for project {project_id}")

    # Update project backend type
    project_manager.update(db, db_obj=project, obj_in={"backend_type": "supabase"})

    # Add task for review
    add_infra_task(f"Review Supabase link for project {project_id}")

    click.echo("✅ Project successfully linked to Supabase!")


@supabase.command()
@click.argument("project_id", type=int)
@click.pass_context
@error_handler
@with_db
@require_project_access
def unlink(ctx, project_id: int) -> None:
    """Unlink a project from Supabase backend"""
    track_event(
        "supabase", "Unlinking project from Supabase", {"project_id": project_id}
    )

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Unlinking project {project_id} from Supabase")

    # Get Supabase config
    config = supabase_config_manager.get_by_project(db, project_id)
    if not config:
        raise CliError("Project is not linked to Supabase")

    # Delete config
    supabase_config_manager.remove(db, id=config.id)
    logger.info(f"Deleted Supabase configuration for project {project_id}")

    # Update project backend type
    project_manager.update(db, db_obj=project, obj_in={"backend_type": "none"})

    # Add task for review
    add_infra_task(f"Review Supabase unlink for project {project_id}", priority="low")

    click.echo("✅ Project successfully unlinked from Supabase!")


@supabase.command()
@click.argument("project_id", type=int)
@click.pass_context
@error_handler
@with_db
@require_project_access
def config(ctx, project_id: int) -> None:
    """Show Supabase configuration for a project"""
    track_event("supabase", "Viewing Supabase config", {"project_id": project_id})

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Viewing Supabase configuration for project {project_id}")

    # Get Supabase config
    config = supabase_config_manager.get_by_project(db, project_id)
    if not config:
        raise CliError("Project is not linked to Supabase")

    click.echo(f"Supabase Configuration for Project {project_id}:")
    click.echo(f"  Project URL: {config.project_url}")
    click.echo(f"  API Key: {config.api_key[:10]}...")
    if config.service_role_key:
        click.echo(f"  Service Role Key: {config.service_role_key[:10]}...")
    if config.database_url:
        click.echo(f"  Database URL: {config.database_url}")
    if config.region:
        click.echo(f"  Region: {config.region}")
    if config.organization_id:
        click.echo(f"  Organization ID: {config.organization_id}")
    click.echo(f"  Created: {config.created_at}")
    click.echo(f"  Updated: {config.updated_at}")
