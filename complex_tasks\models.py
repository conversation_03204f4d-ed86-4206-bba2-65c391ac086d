"""
Data models for complex code tasks

Defines the structure for complex tasks, their status, complexity levels,
resource allocation, quality metrics, and progress tracking.
"""

import json
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from models.model_manager import ModelProvider


class TaskStatus(Enum):
    """Status of a complex task"""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    REVIEW = "review"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskComplexity(Enum):
    """Complexity levels for tasks"""

    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"
    EXTREME = "extreme"


class TaskType(Enum):
    """Types of complex tasks"""

    ARCHITECTURE_DESIGN = "architecture_design"
    SYSTEM_INTEGRATION = "system_integration"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    COMPLEX_PROBLEM_SOLVING = "complex_problem_solving"
    CODE_REFACTORING = "code_refactoring"
    SYSTEM_MIGRATION = "system_migration"
    SECURITY_AUDIT = "security_audit"
    SCALABILITY_ANALYSIS = "scalability_analysis"


@dataclass
class ResourceAllocation:
    """Resource allocation for a task"""

    cpu_cores: int = 1
    memory_gb: int = 2
    gpu_required: bool = False
    gpu_memory_gb: int = 0
    storage_gb: int = 10
    network_bandwidth_mbps: int = 100
    estimated_duration_hours: float = 1.0
    max_concurrent_tasks: int = 1
    priority_level: int = 5  # 1-10, higher is more important

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "cpu_cores": self.cpu_cores,
            "memory_gb": self.memory_gb,
            "gpu_required": self.gpu_required,
            "gpu_memory_gb": self.gpu_memory_gb,
            "storage_gb": self.storage_gb,
            "network_bandwidth_mbps": self.network_bandwidth_mbps,
            "estimated_duration_hours": self.estimated_duration_hours,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "priority_level": self.priority_level,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ResourceAllocation":
        """Create from dictionary"""
        return cls(**data)


@dataclass
class QualityMetrics:
    """Quality metrics for task completion"""

    code_quality_score: float = 0.0  # 0-100
    performance_improvement: float = 0.0  # Percentage
    test_coverage: float = 0.0  # Percentage
    complexity_reduction: float = 0.0  # Percentage
    maintainability_score: float = 0.0  # 0-100
    security_score: float = 0.0  # 0-100
    documentation_quality: float = 0.0  # 0-100
    user_satisfaction: float = 0.0  # 0-100
    bugs_found: int = 0
    bugs_fixed: int = 0
    review_comments: int = 0
    review_approvals: int = 0

    def calculate_overall_score(self) -> float:
        """Calculate overall quality score"""
        weights = {
            "code_quality": 0.25,
            "performance": 0.20,
            "test_coverage": 0.15,
            "maintainability": 0.15,
            "security": 0.15,
            "documentation": 0.10,
        }

        return (
            self.code_quality_score * weights["code_quality"]
            + self.performance_improvement * weights["performance"]
            + self.test_coverage * weights["test_coverage"]
            + self.maintainability_score * weights["maintainability"]
            + self.security_score * weights["security"]
            + self.documentation_quality * weights["documentation"]
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "code_quality_score": self.code_quality_score,
            "performance_improvement": self.performance_improvement,
            "test_coverage": self.test_coverage,
            "complexity_reduction": self.complexity_reduction,
            "maintainability_score": self.maintainability_score,
            "security_score": self.security_score,
            "documentation_quality": self.documentation_quality,
            "user_satisfaction": self.user_satisfaction,
            "bugs_found": self.bugs_found,
            "bugs_fixed": self.bugs_fixed,
            "review_comments": self.review_comments,
            "review_approvals": self.review_approvals,
            "overall_score": self.calculate_overall_score(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "QualityMetrics":
        """Create from dictionary"""
        return cls(**{k: v for k, v in data.items() if k != "overall_score"})


@dataclass
class ProgressReport:
    """Progress report for a task"""

    task_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    status: TaskStatus = TaskStatus.PENDING
    progress_percentage: float = 0.0
    current_phase: str = ""
    estimated_completion: Optional[datetime] = None
    actual_completion: Optional[datetime] = None
    milestones_completed: List[str] = field(default_factory=list)
    milestones_pending: List[str] = field(default_factory=list)
    issues_encountered: List[str] = field(default_factory=list)
    solutions_implemented: List[str] = field(default_factory=list)
    resource_usage: Dict[str, float] = field(default_factory=dict)
    quality_metrics: Optional[QualityMetrics] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "task_id": self.task_id,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "progress_percentage": self.progress_percentage,
            "current_phase": self.current_phase,
            "estimated_completion": (
                self.estimated_completion.isoformat()
                if self.estimated_completion
                else None
            ),
            "actual_completion": (
                self.actual_completion.isoformat() if self.actual_completion else None
            ),
            "milestones_completed": self.milestones_completed,
            "milestones_pending": self.milestones_pending,
            "issues_encountered": self.issues_encountered,
            "solutions_implemented": self.solutions_implemented,
            "resource_usage": self.resource_usage,
            "quality_metrics": (
                self.quality_metrics.to_dict() if self.quality_metrics else None
            ),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProgressReport":
        """Create from dictionary"""
        data_copy = data.copy()
        data_copy["timestamp"] = datetime.fromisoformat(data["timestamp"])
        if data.get("estimated_completion"):
            data_copy["estimated_completion"] = datetime.fromisoformat(
                data["estimated_completion"]
            )
        if data.get("actual_completion"):
            data_copy["actual_completion"] = datetime.fromisoformat(
                data["actual_completion"]
            )
        if data.get("quality_metrics"):
            data_copy["quality_metrics"] = QualityMetrics.from_dict(
                data["quality_metrics"]
            )
        return cls(**data_copy)


@dataclass
class ComplexTask:
    """Complex task definition"""

    task_id: str
    title: str
    description: str
    task_type: TaskType
    complexity: TaskComplexity
    resource_allocation: ResourceAllocation
    requirements: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    deliverables: List[str] = field(default_factory=list)
    acceptance_criteria: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    assigned_to: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    priority: int = 5
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    progress_reports: List[ProgressReport] = field(default_factory=list)
    quality_metrics: Optional[QualityMetrics] = None

    def add_progress_report(self, report: ProgressReport) -> None:
        """Add a progress report"""
        self.progress_reports.append(report)
        self.updated_at = datetime.now()
        self.status = report.status

    def get_latest_progress(self) -> Optional[ProgressReport]:
        """Get the latest progress report"""
        if not self.progress_reports:
            return None
        return max(self.progress_reports, key=lambda x: x.timestamp)

    def calculate_completion_percentage(self) -> float:
        """Calculate completion percentage based on milestones"""
        latest = self.get_latest_progress()
        if latest:
            return latest.progress_percentage
        return 0.0

    def is_overdue(self) -> bool:
        """Check if task is overdue"""
        if not self.deadline:
            return False
        return datetime.now() > self.deadline and self.status != TaskStatus.COMPLETED

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "task_id": self.task_id,
            "title": self.title,
            "description": self.description,
            "task_type": self.task_type.value,
            "complexity": self.complexity.value,
            "resource_allocation": self.resource_allocation.to_dict(),
            "requirements": self.requirements,
            "constraints": self.constraints,
            "dependencies": self.dependencies,
            "deliverables": self.deliverables,
            "acceptance_criteria": self.acceptance_criteria,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "deadline": self.deadline.isoformat() if self.deadline else None,
            "assigned_to": self.assigned_to,
            "status": self.status.value,
            "priority": self.priority,
            "tags": self.tags,
            "metadata": self.metadata,
            "progress_reports": [r.to_dict() for r in self.progress_reports],
            "quality_metrics": (
                self.quality_metrics.to_dict() if self.quality_metrics else None
            ),
            "completion_percentage": self.calculate_completion_percentage(),
            "is_overdue": self.is_overdue(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ComplexTask":
        """Create from dictionary"""
        data_copy = data.copy()

        # Remove computed fields that are not part of the dataclass
        data_copy.pop("completion_percentage", None)
        data_copy.pop("is_overdue", None)

        data_copy["task_type"] = TaskType(data["task_type"])
        data_copy["complexity"] = TaskComplexity(data["complexity"])
        data_copy["resource_allocation"] = ResourceAllocation.from_dict(
            data["resource_allocation"]
        )
        data_copy["created_at"] = datetime.fromisoformat(data["created_at"])
        data_copy["updated_at"] = datetime.fromisoformat(data["updated_at"])
        if data.get("deadline"):
            data_copy["deadline"] = datetime.fromisoformat(data["deadline"])
        data_copy["status"] = TaskStatus(data["status"])
        data_copy["progress_reports"] = [
            ProgressReport.from_dict(r) for r in data.get("progress_reports", [])
        ]
        if data.get("quality_metrics"):
            data_copy["quality_metrics"] = QualityMetrics.from_dict(
                data["quality_metrics"]
            )
        return cls(**data_copy)


# Import ModelProvider from the correct location
