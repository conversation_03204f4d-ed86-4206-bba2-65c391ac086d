{"practices": [{"practice_id": "ts_001", "category": "tech_stack", "subcategory": "python", "title": "Use Type Hints", "description": "Always use type hints in Python code for better code clarity and IDE support", "importance": "high", "difficulty": "easy", "impact": "medium", "code_examples": ["def calculate_total(items: List[float]) -> float:\n    return sum(items)", "user_id: Optional[str] = None"], "anti_patterns": ["def process_data(data):\n    return data * 2", "result = None"], "tools": ["mypy", "pylint"], "resources": ["https://docs.python.org/3/library/typing.html"], "tags": ["python", "type_hints", "code_quality"], "created_at": "2025-07-24T21:21:40.923468", "last_updated": "2025-07-24T21:21:40.923470", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ts_002", "category": "tech_stack", "subcategory": "react", "title": "Use Functional Components with Hooks", "description": "Prefer functional components with React hooks over class components", "importance": "high", "difficulty": "medium", "impact": "high", "code_examples": ["const MyComponent = () => {\n  const [state, setState] = useState();\n  return <div>Hello</div>;\n};"], "anti_patterns": ["class MyComponent extends React.Component {\n  render() {\n    return <div>Hello</div>;\n  }\n}"], "tools": ["eslint", "prettier"], "resources": ["https://react.dev/learn/hooks"], "tags": ["react", "hooks", "functional_components"], "created_at": "2025-07-24T21:21:40.923474", "last_updated": "2025-07-24T21:21:40.923474", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ts_003", "category": "tech_stack", "subcategory": "typescript", "title": "Strict Type Checking", "description": "Enable strict type checking in TypeScript for better type safety", "importance": "critical", "difficulty": "medium", "impact": "high", "code_examples": ["// tsconfig.json\n{\n  \"compilerOptions\": {\n    \"strict\": true,\n    \"noImplicitAny\": true\n  }\n}"], "anti_patterns": ["let data: any = {};", "function process(input) { return input; }"], "tools": ["typescript", "eslint"], "resources": ["https://www.typescriptlang.org/tsconfig"], "tags": ["typescript", "type_safety", "strict_mode"], "created_at": "2025-07-24T21:21:40.923477", "last_updated": "2025-07-24T21:21:40.923477", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "wd_001", "category": "web_dev", "subcategory": "performance", "title": "Optimize Bundle Size", "description": "Keep JavaScript bundle sizes small for faster loading times", "importance": "high", "difficulty": "medium", "impact": "high", "code_examples": ["// Use dynamic imports\nconst LazyComponent = lazy(() => import('./LazyComponent'));", "// Tree shaking\nimport { specificFunction } from 'large-library';"], "anti_patterns": ["import * as everything from 'large-library';", "// No code splitting"], "tools": ["webpack-bundle-analyzer", "lighthouse"], "resources": ["https://web.dev/fast/"], "tags": ["performance", "bundle_size", "loading_speed"], "created_at": "2025-07-24T21:21:40.923479", "last_updated": "2025-07-24T21:21:40.923480", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "wd_002", "category": "web_dev", "subcategory": "security", "title": "Input Validation", "description": "Always validate and sanitize user inputs to prevent security vulnerabilities", "importance": "critical", "difficulty": "medium", "impact": "critical", "code_examples": ["// Server-side validation\nif not re.match(r'^[a-zA-Z0-9_]+$', username):\n    raise ValueError('Invalid username')", "// Client-side validation\nconst isValidEmail = (email) => /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);"], "anti_patterns": ["// No validation\nuser_input = request.form['data']", "// Direct DOM manipulation\ninnerHTML = userInput"], "tools": ["eslint-plugin-security", "bandit"], "resources": ["https://owasp.org/www-project-top-ten/"], "tags": ["security", "input_validation", "xss_prevention"], "created_at": "2025-07-24T21:21:40.923482", "last_updated": "2025-07-24T21:21:40.923482", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "wd_003", "category": "web_dev", "subcategory": "accessibility", "title": "Semantic HTML", "description": "Use semantic HTML elements for better accessibility and SEO", "importance": "high", "difficulty": "easy", "impact": "medium", "code_examples": ["<nav>\n  <ul>\n    <li><a href='/'>Home</a></li>\n  </ul>\n</nav>", "<main>\n  <article>\n    <h1>Title</h1>\n    <p>Content</p>\n  </article>\n</main>"], "anti_patterns": ["<div class='nav'>\n  <div class='menu'>\n    <div><a href='/'>Home</a></div>\n  </div>\n</div>", "<div class='title'>Title</div>"], "tools": ["axe-core", "lighthouse"], "resources": ["https://developer.mozilla.org/en-US/docs/Web/HTML"], "tags": ["accessibility", "semantic_html", "seo"], "created_at": "2025-07-24T21:21:40.923484", "last_updated": "2025-07-24T21:21:40.923485", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ad_001", "category": "app_dev", "subcategory": "architecture", "title": "Separation of Concerns", "description": "Separate business logic, presentation, and data access layers", "importance": "high", "difficulty": "medium", "impact": "high", "code_examples": ["# Service layer\nclass UserService:\n    def get_user(self, user_id):\n        return self.repository.find_by_id(user_id)", "# Repository layer\nclass UserRepository:\n    def find_by_id(self, user_id):\n        return self.db.query(User).filter_by(id=user_id).first()"], "anti_patterns": ["# Mixed concerns\nclass UserController:\n    def get_user(self, user_id):\n        return db.query(User).filter_by(id=user_id).first()\n        # Direct database access in controller"], "tools": ["architecture_diagrams", "code_review"], "resources": ["https://en.wikipedia.org/wiki/Separation_of_concerns"], "tags": ["architecture", "clean_code", "maintainability"], "created_at": "2025-07-24T21:21:40.923486", "last_updated": "2025-07-24T21:21:40.923487", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ad_002", "category": "app_dev", "subcategory": "testing", "title": "Comprehensive Testing", "description": "Write unit, integration, and end-to-end tests for all critical functionality", "importance": "critical", "difficulty": "hard", "impact": "high", "code_examples": ["def test_user_creation():\n    user = UserService().create_user('<EMAIL>')\n    assert user.email == '<EMAIL>'", "test('should create user', async () => {\n  const user = await createUser('<EMAIL>');\n  expect(user.email).toBe('<EMAIL>');\n});"], "anti_patterns": ["# No tests\nclass UserService:\n    def create_user(self, email):\n        return User(email=email)", "// No test coverage"], "tools": ["pytest", "jest", "cypress"], "resources": ["https://martinfowler.com/articles/microservices-testing/"], "tags": ["testing", "quality_assurance", "reliability"], "created_at": "2025-07-24T21:21:40.923488", "last_updated": "2025-07-24T21:21:40.923489", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ad_003", "category": "app_dev", "subcategory": "error_handling", "title": "Graceful Error <PERSON>ling", "description": "Implement comprehensive error handling with meaningful error messages", "importance": "high", "difficulty": "medium", "impact": "high", "code_examples": ["try:\n    result = process_data(data)\nexcept ValueError as e:\n    logger.error(f'Invalid data: {e}')\n    raise ValidationError('Invalid input data')", "try {\n  const result = await processData(data);\n} catch (error) {\n  console.error('Processing failed:', error);\n  throw new ProcessingError('Failed to process data');\n}"], "anti_patterns": ["# Silent failures\ntry:\n    result = process_data(data)\nexcept:\n    pass", "// Generic error handling\ntry {\n  processData(data);\n} catch (error) {\n  console.log('Error');\n}"], "tools": ["logging", "error_tracking"], "resources": ["https://12factor.net/logs"], "tags": ["error_handling", "logging", "debugging"], "created_at": "2025-07-24T21:21:40.923491", "last_updated": "2025-07-24T21:21:40.923491", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ux_001", "category": "ui_ux", "subcategory": "usability", "title": "Consistent Design System", "description": "Use a consistent design system with standardized components and spacing", "importance": "high", "difficulty": "medium", "impact": "high", "code_examples": ["// Design tokens\nconst spacing = {\n  xs: '4px',\n  sm: '8px',\n  md: '16px',\n  lg: '24px'\n};", "// Consistent component\nconst Button = ({ variant = 'primary', children }) => (\n  <button className={`btn btn-${variant}`}>\n    {children}\n  </button>\n);"], "anti_patterns": ["// Inconsistent styling\n<div style={{ margin: '5px', padding: '10px' }}>", "// No design system\n<button style={{ backgroundColor: '#007bff' }}>"], "tools": ["storybook", "figma", "design_tokens"], "resources": ["https://www.designsystems.com/"], "tags": ["design_system", "consistency", "usability"], "created_at": "2025-07-24T21:21:40.923493", "last_updated": "2025-07-24T21:21:40.923493", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ux_002", "category": "ui_ux", "subcategory": "accessibility", "title": "Keyboard Navigation", "description": "Ensure all interactive elements are accessible via keyboard navigation", "importance": "high", "difficulty": "medium", "impact": "medium", "code_examples": ["<button\n  onKeyDown={(e) => e.key === 'Enter' && handleClick()}\n  tabIndex={0}\n>\n  Click me\n</button>", "<div\n  role='button'\n  tabIndex={0}\n  onKeyDown={handleKeyDown}\n>\n  Custom button\n</div>"], "anti_patterns": ["<div onClick={handleClick}>\n  Click me\n</div>", "// No keyboard support\n<button onClick={handleClick}>Click</button>"], "tools": ["axe-core", "keyboard_testing"], "resources": ["https://www.w3.org/WAI/WCAG21/quickref/"], "tags": ["accessibility", "keyboard_navigation", "wcag"], "created_at": "2025-07-24T21:21:40.923495", "last_updated": "2025-07-24T21:21:40.923495", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ux_003", "category": "ui_ux", "subcategory": "performance", "title": "Loading States", "description": "Provide clear loading states and feedback for user actions", "importance": "medium", "difficulty": "easy", "impact": "medium", "code_examples": ["const [loading, setLoading] = useState(false);\n\nconst handleSubmit = async () => {\n  setLoading(true);\n  try {\n    await submitData();\n  } finally {\n    setLoading(false);\n  }\n};", "{loading ? <Spinner /> : <SubmitButton />}"], "anti_patterns": ["// No loading state\nconst handleSubmit = async () => {\n  await submitData();\n};", "// Silent operations\n<button onClick={handleSubmit}>Submit</button>"], "tools": ["react_loading", "skeleton_components"], "resources": ["https://www.nngroup.com/articles/response-times-3-important-limits/"], "tags": ["loading_states", "user_feedback", "ux"], "created_at": "2025-07-24T21:21:40.923497", "last_updated": "2025-07-24T21:21:40.923497", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ai_001", "category": "ai_agent", "subcategory": "prompt_engineering", "title": "Clear and Specific Prompts", "description": "Write clear, specific, and context-rich prompts for better AI responses", "importance": "critical", "difficulty": "hard", "impact": "high", "code_examples": ["prompt = '''\nYou are a Python expert. Write a function that:\n- Takes a list of numbers\n- Returns the sum of even numbers only\n- Includes type hints\n- Has error handling\n\nExample input: [1, 2, 3, 4, 5]\nExample output: 6\n'''", "// Context-rich prompt\nconst prompt = `Given the following React component:\n${componentCode}\n\nOptimize it for performance by:\n1. Memoizing expensive calculations\n2. Using React.memo where appropriate\n3. Avoiding unnecessary re-renders`;"], "anti_patterns": ["prompt = 'Write a function'", "// Vague prompt\nconst prompt = 'Fix this code';"], "tools": ["prompt_templates", "context_management"], "resources": ["https://platform.openai.com/docs/guides/prompt-engineering"], "tags": ["prompt_engineering", "ai_optimization", "context"], "created_at": "2025-07-24T21:21:40.923499", "last_updated": "2025-07-24T21:21:40.923499", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ai_002", "category": "ai_agent", "subcategory": "model_selection", "title": "Appropriate Model Selection", "description": "Choose the right AI model for the specific task and requirements", "importance": "high", "difficulty": "medium", "impact": "high", "code_examples": ["# For code generation\nmodel = 'deepseek-coder:1.3b'  # Specialized for coding\n\n# For general conversation\nmodel = 'mistral:7b-instruct-q4_0'  # General purpose", "// Model selection based on task\nconst modelMap = {\n  'code_generation': 'deepseek-coder:1.3b',\n  'text_analysis': 'qwen2.5-coder:3b',\n  'general': 'mistral:7b-instruct-q4_0'\n};"], "anti_patterns": ["# Using same model for everything\nmodel = 'mistral:7b-instruct-q4_0'  # For all tasks", "// No model selection strategy\nconst model = 'default';"], "tools": ["model_router", "performance_monitoring"], "resources": ["https://ollama.ai/library"], "tags": ["model_selection", "ai_optimization", "performance"], "created_at": "2025-07-24T21:21:40.923501", "last_updated": "2025-07-24T21:21:40.923501", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}, {"practice_id": "ai_003", "category": "ai_agent", "subcategory": "safety", "title": "AI Safety and Validation", "description": "Implement safety measures and validation for AI-generated content", "importance": "critical", "difficulty": "hard", "impact": "critical", "code_examples": ["# Code validation\ndef validate_generated_code(code: str) -> bool:\n    try:\n        ast.parse(code)\n        return True\n    except SyntaxError:\n        return False", "// Content validation\nconst validateResponse = (response) => {\n  if (response.includes('malicious_code')) {\n    throw new Error('Potentially unsafe content detected');\n  }\n  return response;\n};"], "anti_patterns": ["# No validation\nreturn ai_generated_code", "// Trusting AI output without validation\nreturn aiResponse;"], "tools": ["code_validation", "content_filtering"], "resources": ["https://openai.com/safety"], "tags": ["ai_safety", "validation", "security"], "created_at": "2025-07-24T21:21:40.923503", "last_updated": "2025-07-24T21:21:40.923503", "usage_count": 0, "success_rate": 1.0, "validation_rules": []}], "violations": [], "recommendations": [], "stats": {"total_practices": 15, "total_violations": 0, "total_recommendations": 0, "practices_by_category": {"tech_stack": 3, "web_dev": 3, "app_dev": 3, "ui_ux": 3, "ai_agent": 3}, "violations_by_severity": {}}}