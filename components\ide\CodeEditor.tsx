import React, { useState, useRef, useEffect, memo, useCallback } from 'react';
import Editor, { Monaco, OnMount, BeforeMount } from '@monaco-editor/react';
import { FileText, X, Split, Maximize2, Settings, Save } from 'lucide-react';
import { useFileStore } from '@/store/file';

export interface EditorFile {
  id: string;
  name: string;
  path: string;
  content: string;
  language: string;
  isModified: boolean;
  isActive: boolean;
}

interface CodeEditorProps {
  files: EditorFile[];
  onFileChange: (fileId: string, content: string) => void;
  onFileSave: (fileId: string) => void;
  onFileClose: (fileId: string) => void;
  onFileSelect: (fileId: string) => void;
  theme?: 'vs' | 'vs-dark' | 'hc-black';
  className?: string;
}

export const CodeEditor: React.FC<Partial<CodeEditorProps>> = ({
  theme = 'vs-dark',
  className = ''
}) => {
  const files = useFileStore(state => state.files);
  const activeFileId = useFileStore(state => state.activeFileId);
  const setActiveFile = useFileStore(state => state.setActiveFile);
  const updateFile = useFileStore(state => state.updateFile);
  const saveFile = useFileStore(state => state.saveFile);
  const deleteFile = useFileStore(state => state.deleteFile);

  const activeFile = files.find(f => f.id === activeFileId) || null;
  const [splitView, setSplitView] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);

  // Remove useEffect for activeFile

  const handleBeforeMount: BeforeMount = (monaco) => {
    monacoRef.current = monaco;

    // Configure editor options
    monaco.editor.defineTheme('custom-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4',
        'editor.lineHighlightBackground': '#2a2a2a',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41'
      }
    });

    // Add custom language support
    monaco.languages.register({ id: 'html' });
    monaco.languages.register({ id: 'css' });
    monaco.languages.register({ id: 'javascript' });
    monaco.languages.register({ id: 'typescript' });
  };

  const handleEditorDidMount: OnMount = (editor, monaco) => {
    editorRef.current = editor;

    // Configure editor settings
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'Consolas, "Courier New", monospace',
      lineNumbers: 'on',
      roundedSelection: false,
      scrollBeyondLastLine: false,
      readOnly: false,
      automaticLayout: true,
      minimap: {
        enabled: true,
        size: 'proportional'
      },
      wordWrap: 'on',
      folding: true,
      showFoldingControls: 'always',
      suggestOnTriggerCharacters: true,
      quickSuggestions: true,
      parameterHints: {
        enabled: true
      }
    });

    // Add auto-save functionality
    let saveTimeout: NodeJS.Timeout;
    editor.onDidChangeModelContent(() => {
      if (activeFile) {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(() => {
          const content = editor.getValue();
          updateFile(activeFile.id, content);
        }, 1000); // Auto-save after 1 second of inactivity
      }
    });

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      if (activeFile) {
        saveFile(activeFile.id);
      }
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyW, () => {
      if (activeFile) {
        deleteFile(activeFile.id);
      }
    });
  };

  const getLanguageFromFileName = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'html':
      case 'htm':
        return 'html';
      case 'css':
        return 'css';
      case 'js':
        return 'javascript';
      case 'jsx':
        return 'javascript';
      case 'ts':
        return 'typescript';
      case 'tsx':
        return 'typescript';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      default:
        return 'plaintext';
    }
  };

  const handleTabClick = useCallback((fileId: string) => {
    setActiveFile(fileId);
  }, [setActiveFile]);

  const handleTabClose = useCallback((e: React.MouseEvent, fileId: string) => {
    e.stopPropagation();
    deleteFile(fileId);
  }, [deleteFile]);

  const FileTab = memo(({ file }: { file: EditorFile }) => (
    <div
      key={file.id}
      onClick={() => handleTabClick(file.id)}
      className={`flex items-center gap-2 px-3 py-2 text-sm cursor-pointer border-r border-gray-200 dark:border-gray-700 min-w-0 ${
        file.isActive
          ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100'
          : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
      }`}
    >
      <FileText className="w-4 h-4 flex-shrink-0" />
      <span className="truncate">{file.name}</span>
      {file.isModified && (
        <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></div>
      )}
      <button
        onClick={(e) => handleTabClose(e, file.id)}
        className="w-4 h-4 flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600 rounded flex-shrink-0"
      >
        <X className="w-3 h-3" />
      </button>
    </div>
  ));

  const handleSave = () => {
    if (activeFile) {
      saveFile(activeFile.id);
    }
  };

  const handleSplitView = () => {
    setSplitView(!splitView);
  };

  const handleMaximize = () => {
    // Toggle fullscreen mode
    const editorContainer = document.querySelector('.monaco-editor-container');
    if (editorContainer) {
      editorContainer.classList.toggle('fullscreen');
    }
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSave}
            className="flex items-center gap-1 px-2 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            title="Save (Ctrl+S)"
          >
            <Save className="w-4 h-4" />
            Save
          </button>

          <button
            onClick={handleSplitView}
            className={`flex items-center gap-1 px-2 py-1 text-sm rounded transition-colors ${
              splitView
                ? 'bg-green-500 text-white'
                : 'bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-400 dark:hover:bg-gray-500'
            }`}
            title="Split View"
          >
            <Split className="w-4 h-4" />
            Split
          </button>

          <button
            onClick={handleMaximize}
            className="flex items-center gap-1 px-2 py-1 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
            title="Maximize"
          >
            <Maximize2 className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="flex items-center gap-1 px-2 py-1 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
            title="Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* File Tabs */}
      <div className="flex items-center bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
        {files.map((file) => (
          <FileTab key={file.id} file={file} />
        ))}
      </div>

      {/* Editor */}
      <div className="flex-1 relative">
        {activeFile ? (
          <Editor
            height="100%"
            language={getLanguageFromFileName(activeFile.name)}
            theme={theme}
            value={activeFile.content}
            beforeMount={handleBeforeMount}
            onMount={handleEditorDidMount}
            options={{
              automaticLayout: true,
              fontSize: 14,
              fontFamily: 'Consolas, "Courier New", monospace',
              lineNumbers: 'on',
              roundedSelection: false,
              scrollBeyondLastLine: false,
              readOnly: false,
              minimap: {
                enabled: true,
                size: 'proportional'
              },
              wordWrap: 'on',
              folding: true,
              showFoldingControls: 'always',
              suggestOnTriggerCharacters: true,
              quickSuggestions: true,
              parameterHints: {
                enabled: true
              },
              tabSize: 2,
              insertSpaces: true,
              detectIndentation: true,
              trimAutoWhitespace: true,
              largeFileOptimizations: true,
              suggest: {
                showKeywords: true,
                showSnippets: true,
                showClasses: true,
                showFunctions: true,
                showVariables: true,
                showConstants: true,
                showEnums: true,
                showModules: true,
                showProperties: true,
                showEvents: true,
                showOperators: true,
                showUnits: true,
                showValues: true,
                showColors: true,
                showFiles: true,
                showReferences: true,
                showFolders: true,
                showTypeParameters: true,
                showWords: true
              }
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No file selected</p>
              <p className="text-sm">Open a file to start editing</p>
            </div>
          </div>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
            Editor Settings
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm text-gray-700 dark:text-gray-300">
                Font Size
              </label>
              <select className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700">
                <option value="12">12px</option>
                <option value="14" selected>14px</option>
                <option value="16">16px</option>
                <option value="18">18px</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm text-gray-700 dark:text-gray-300">
                Theme
              </label>
              <select className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700">
                <option value="vs">Light</option>
                <option value="vs-dark" selected>Dark</option>
                <option value="hc-black">High Contrast</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <label className="text-sm text-gray-700 dark:text-gray-300">
                Word Wrap
              </label>
              <input
                type="checkbox"
                defaultChecked
                className="w-4 h-4"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
