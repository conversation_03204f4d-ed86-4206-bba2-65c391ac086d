#!/usr/bin/env python3
"""
Ollama CLI Commands
Provides command-line interface for Ollama model management

Commands:
- Check Ollama connection and status
- List available models
- Switch between models
- Pull new models
- Generate responses
- Get performance statistics
- Optimize model selection
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict, Optional

from models.ollama_manager import OllamaModelManager, get_ollama_manager

# Add project root to path for imports
project_root = Path(__file__).parent.parent

sys.path.insert(0, str(project_root))


class OllamaCommands:
    """CLI commands for Ollama model management"""

    def __init__(self, agent=None):
        self.agent = agent
        self.ollama_manager = get_ollama_manager()

    async def check_connection(self, **kwargs) -> Dict[str, Any]:
        """Check Ollama connection status"""
        try:
            result = await self.ollama_manager.check_ollama_connection()
            return {
                "success": True,
                "command": "check_connection",
                "result": result,
                "message": "Ollama connection check completed",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "check_connection",
                "error": str(e),
                "message": "Failed to check Ollama connection",
            }

    async def list_models(self, **kwargs) -> Dict[str, Any]:
        """List all available models and their status"""
        try:
            result = await self.ollama_manager.list_available_models()
            return {
                "success": True,
                "command": "list_models",
                "result": result,
                "message": "Model list retrieved successfully",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "list_models",
                "error": str(e),
                "message": "Failed to list models",
            }

    async def switch_model(self, model_name: str, **kwargs) -> Dict[str, Any]:
        """Switch to a different model"""
        try:
            result = await self.ollama_manager.switch_model(model_name)
            return {
                "success": True,
                "command": "switch_model",
                "result": result,
                "message": f"Model switching completed for {model_name}",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "switch_model",
                "error": str(e),
                "message": f"Failed to switch to model {model_name}",
            }

    async def pull_model(self, model_name: str, **kwargs) -> Dict[str, Any]:
        """Pull a model from Ollama"""
        try:
            result = await self.ollama_manager.pull_model(model_name)
            return {
                "success": True,
                "command": "pull_model",
                "result": result,
                "message": f"Model pull completed for {model_name}",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "pull_model",
                "error": str(e),
                "message": f"Failed to pull model {model_name}",
            }

    async def generate_response(
        self, prompt: str, model_name: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """Generate response using Ollama model"""
        try:
            result = await self.ollama_manager.generate_response(
                prompt, model_name, **kwargs
            )
            return {
                "success": True,
                "command": "generate_response",
                "result": result,
                "message": "Response generation completed",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "generate_response",
                "error": str(e),
                "message": "Failed to generate response",
            }

    async def get_performance_stats(
        self, model_name: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """Get performance statistics for models"""
        try:
            result = await self.ollama_manager.get_performance_stats(model_name)
            return {
                "success": True,
                "command": "get_performance_stats",
                "result": result,
                "message": "Performance statistics retrieved",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "get_performance_stats",
                "error": str(e),
                "message": "Failed to get performance statistics",
            }

    async def optimize_model_selection(self, **kwargs) -> Dict[str, Any]:
        """Optimize model selection based on performance"""
        try:
            result = await self.ollama_manager.optimize_model_selection()
            return {
                "success": True,
                "command": "optimize_model_selection",
                "result": result,
                "message": "Model optimization completed",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "optimize_model_selection",
                "error": str(e),
                "message": "Failed to optimize model selection",
            }

    async def get_status(self, **kwargs) -> Dict[str, Any]:
        """Get comprehensive status of Ollama manager"""
        try:
            result = await self.ollama_manager.get_status()
            return {
                "success": True,
                "command": "get_status",
                "result": result,
                "message": "Ollama status retrieved",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "get_status",
                "error": str(e),
                "message": "Failed to get Ollama status",
            }

    async def test_model(
        self,
        model_name: str,
        test_prompt: str = "Write a simple Python function to add two numbers",
        **kwargs,
    ) -> Dict[str, Any]:
        """Test a specific model with a sample prompt"""
        try:
            # Switch to the model first
            switch_result = await self.ollama_manager.switch_model(model_name)
            if not switch_result["success"]:
                return switch_result

            # Generate test response
            result = await self.ollama_manager.generate_response(
                test_prompt, model_name
            )
            return {
                "success": True,
                "command": "test_model",
                "result": {
                    "model": model_name,
                    "test_prompt": test_prompt,
                    "response": result.get("response", ""),
                    "response_time": result.get("response_time", 0),
                    "tokens_used": result.get("tokens_used", 0),
                },
                "message": f"Model {model_name} test completed successfully",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "test_model",
                "error": str(e),
                "message": f"Failed to test model {model_name}",
            }

    async def compare_models(
        self, prompt: str, models: list = None, **kwargs
    ) -> Dict[str, Any]:
        """Compare multiple models with the same prompt"""
        try:
            if models is None:
                models = [
                    "mistral:7b-instruct-q4_0",
                    "qwen2.5-coder:3b",
                    "starcoder2:3b",
                ]

            results = {}
            for model_name in models:
                if model_name in self.ollama_manager.approved_models:
                    result = await self.ollama_manager.generate_response(
                        prompt, model_name
                    )
                    results[model_name] = {
                        "success": result["success"],
                        "response": result.get("response", ""),
                        "response_time": result.get("response_time", 0),
                        "tokens_used": result.get("tokens_used", 0),
                    }
                else:
                    results[model_name] = {
                        "success": False,
                        "error": f"Model {model_name} is not approved",
                    }

            return {
                "success": True,
                "command": "compare_models",
                "result": {"prompt": prompt, "models": results},
                "message": f"Model comparison completed for {len(models)} models",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "compare_models",
                "error": str(e),
                "message": "Failed to compare models",
            }

    async def setup_learning_integration(self, **kwargs) -> Dict[str, Any]:
        """Setup learning integration with the advanced learning system"""
        try:
            # Add learning callback to capture patterns
            if self.agent and hasattr(self.agent, "record_learning_event"):
                self.ollama_manager.add_learning_callback(
                    self.agent.record_learning_event
                )

            return {
                "success": True,
                "command": "setup_learning_integration",
                "result": {
                    "learning_callbacks": len(self.ollama_manager.learning_callbacks),
                    "integration_enabled": True,
                },
                "message": "Learning integration setup completed",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "setup_learning_integration",
                "error": str(e),
                "message": "Failed to setup learning integration",
            }

    async def get_approved_models(self, **kwargs) -> Dict[str, Any]:
        """Get list of approved models for this project"""
        try:
            return {
                "success": True,
                "command": "get_approved_models",
                "result": {
                    "approved_models": self.ollama_manager.approved_models,
                    "current_model": self.ollama_manager.current_model,
                    "total_approved": len(self.ollama_manager.approved_models),
                },
                "message": "Approved models list retrieved",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "get_approved_models",
                "error": str(e),
                "message": "Failed to get approved models",
            }

    async def validate_model(self, model_name: str, **kwargs) -> Dict[str, Any]:
        """Validate if a model is approved and available"""
        try:
            is_approved = model_name in self.ollama_manager.approved_models
            available_models = await self.ollama_manager.list_available_models()
            is_available = available_models["success"] and available_models[
                "models"
            ].get(model_name, {}).get("available", False)

            return {
                "success": True,
                "command": "validate_model",
                "result": {
                    "model_name": model_name,
                    "is_approved": is_approved,
                    "is_available": is_available,
                    "can_use": is_approved and is_available,
                    "recommendation": (
                        "pull_model"
                        if is_approved and not is_available
                        else "ready_to_use"
                    ),
                },
                "message": f"Model {model_name} validation completed",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "validate_model",
                "error": str(e),
                "message": f"Failed to validate model {model_name}",
            }


# CLI command mapping for integration with main agent
OLLAMA_COMMANDS = {
    "ollama_check_connection": OllamaCommands().check_connection,
    "ollama_list_models": OllamaCommands().list_models,
    "ollama_switch_model": OllamaCommands().switch_model,
    "ollama_pull_model": OllamaCommands().pull_model,
    "ollama_generate_response": OllamaCommands().generate_response,
    "ollama_get_performance_stats": OllamaCommands().get_performance_stats,
    "ollama_optimize_model_selection": OllamaCommands().optimize_model_selection,
    "ollama_get_status": OllamaCommands().get_status,
    "ollama_test_model": OllamaCommands().test_model,
    "ollama_compare_models": OllamaCommands().compare_models,
    "ollama_setup_learning_integration": OllamaCommands().setup_learning_integration,
    "ollama_get_approved_models": OllamaCommands().get_approved_models,
    "ollama_validate_model": OllamaCommands().validate_model,
}


if __name__ == "__main__":
    # Test the CLI commands
    async def test_commands():
        commands = OllamaCommands()

        print("Testing Ollama CLI commands...")

        # Test connection check
        print("\n1. Testing connection check...")
        result = await commands.check_connection()
        print(f"Connection check: {result}")

        # Test model listing
        print("\n2. Testing model listing...")
        result = await commands.list_models()
        print(f"Model listing: {result}")

        # Test status
        print("\n3. Testing status...")
        result = await commands.get_status()
        print(f"Status: {result}")

        # Test approved models
        print("\n4. Testing approved models...")
        result = await commands.get_approved_models()
        print(f"Approved models: {result}")

    asyncio.run(test_commands())
