"""
Utility functions for unique value operations.
"""

from typing import Any, List, Optional
import numpy as np


def cached_unique(array: Any) -> tuple:
    """
    Return unique elements of an array with caching.

    Parameters
    ----------
    array : array-like
        Input array

    Returns
    -------
    tuple
        Tuple of (unique_values, unique_indices, inverse_indices, counts)
    """
    if hasattr(array, 'unique'):
        # For numpy arrays and similar
        unique_values, unique_indices, inverse_indices, counts = np.unique(
            array, return_index=True, return_inverse=True, return_counts=True
        )
    else:
        # Fallback for other array types
        unique_values = np.unique(array)
        unique_indices = np.array([np.where(array == val)[0][0] for val in unique_values])
        inverse_indices = np.array([np.where(unique_values == val)[0][0] for val in array])
        counts = np.array([np.sum(array == val) for val in unique_values])

    return unique_values, unique_indices, inverse_indices, counts


__all__ = ["cached_unique"]
