"""
Enhanced Code Generation Module

This module provides advanced code generation capabilities using deepseek-coder
and other AI models for intelligent code completion, bug detection, optimization,
and pattern recognition.

Phase 19 Implementation - Enhanced Code Generation
"""

from code_generation.bug_detector import BugDetector
from code_generation.code_analyzer import CodeAnalyzer
from code_generation.code_optimizer import CodeOptimizer
from code_generation.deepseek_integration import DeepseekIntegration
from code_generation.editor_integration import EditorIntegration
from code_generation.enhanced_code_generator import EnhancedCodeGenerator
from code_generation.pattern_recognizer import PatternRecognizer

__version__ = "1.0.0"
__author__ = "AI Coding Agent Team"

__all__ = [
    "EnhancedCodeGenerator",
    "DeepseekIntegration",
    "CodeAnalyzer",
    "PatternRecognizer",
    "BugDetector",
    "CodeOptimizer",
    "EditorIntegration",
]
