#!/usr/bin/env python3
"""
CursorAgent - AI Agent with Automatic Cursor Rules Integration

This agent automatically loads cursor rules from config/cursorrules.md and
prepends them to every system prompt, ensuring consistent application of
project standards across all code generation tasks.
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from config import get_config
from core.agents.base_agent import Agent<PERSON><PERSON><PERSON>, Agent<PERSON>og<PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>
from models.ollama_manager import OllamaModelManager

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


logger = logging.getLogger(__name__)


class CursorAgent:
    """
    AI Agent with automatic cursor rules integration.

    This agent ensures that all cursor rules are automatically applied
    to every code generation task by loading them from config/cursorrules.md
    and prepending them to the system prompt.
    """

    def __init__(self, config_path: str = "config/cursor_agent_config.json"):
        """
        Initialize the CursorAgent with automatic rules integration.

        Args:
            config_path: Path to the agent configuration file
        """
        self.config = self._load_config(config_path)
        self.project_root = Path(__file__).parent.parent.parent

        # Load cursor rules
        self.cursor_rules = self._load_cursor_rules()

        # Initialize Ollama manager
        self.ollama_manager = OllamaModelManager()

        # Load model settings
        model_settings = self.config.get("model_settings", {})
        self.model_name = model_settings.get(
            "model_name", self.ollama_manager.current_model
        )

        # Create system prompt with cursor rules prepended
        self.system_prompt = self._create_system_prompt()

        # Initialize logging
        self.logger = AgentLogger()
        self.error_handler = ErrorHandler(self.logger)

        logger.info(
            f"CursorAgent initialized successfully with model {self.model_name}"
        )
        logger.info(
            f"Cursor rules loaded from: {self.config.get('cursor_rules_path', 'config/cursorrules.md')}"
        )

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load agent configuration from JSON file."""
        try:
            if os.path.exists(config_path):
                with open(config_path, "r") as f:
                    config = json.load(f)
            else:
                # Default configuration if file doesn't exist
                config = {
                    "agent_name": "CursorAgent",
                    "version": "1.0.0",
                    "description": "AI Agent with automatic cursor rules integration",
                    "cursor_rules_path": "config/cursorrules.md",
                    "model_settings": {
                        "model_name": "yi-coder:1.5b",
                        "system_prompt": "You are a helpful AI assistant specializing in code generation and development tasks.",
                    },
                    "enforcement": {
                        "strict_mode": True,
                        "auto_validate": True,
                        "pre_commit_checks": True,
                    },
                }

                # Create the config file if it doesn't exist
                os.makedirs(os.path.dirname(config_path), exist_ok=True)
                with open(config_path, "w") as f:
                    json.dump(config, f, indent=2)

            return config
        except Exception as e:
            logger.error(f"Failed to load config from {config_path}: {e}")
            raise AgentError(f"Configuration loading failed: {e}")

    def _load_cursor_rules(self) -> str:
        """Load cursor rules from the specified path."""
        rules_path = self.config.get("cursor_rules_path", "config/cursorrules.md")
        full_rules_path = self.project_root / rules_path

        try:
            if full_rules_path.exists():
                with open(full_rules_path, "r", encoding="utf-8") as f:
                    rules_content = f.read()
                logger.info(f"Cursor rules loaded successfully from {full_rules_path}")
                return rules_content
            else:
                # Try alternative paths
                alternative_paths = [
                    self.project_root / ".cursor" / "rules" / "cursorrules.md",
                    self.project_root / "docs" / "cursorrules.md",
                    self.project_root / "cursorrules.md",
                ]

                for alt_path in alternative_paths:
                    if alt_path.exists():
                        with open(alt_path, "r", encoding="utf-8") as f:
                            rules_content = f.read()
                        logger.info(
                            f"Cursor rules loaded from alternative path: {alt_path}"
                        )
                        return rules_content

                # If no rules file found, create a basic one
                logger.warning(
                    f"Cursor rules file not found at {full_rules_path}. Creating basic rules."
                )
                basic_rules = self._create_basic_rules()

                # Create the rules file
                os.makedirs(os.path.dirname(full_rules_path), exist_ok=True)
                with open(full_rules_path, "w", encoding="utf-8") as f:
                    f.write(basic_rules)

                return basic_rules

        except Exception as e:
            logger.error(f"Failed to load cursor rules from {full_rules_path}: {e}")
            # Return basic rules as fallback
            return self._create_basic_rules()

    def _create_basic_rules(self) -> str:
        """Create basic cursor rules as fallback."""
        return """# 🎯 CURSOR AI ASSISTANT RULES - CRITICAL TOP-LEVEL

**CRITICAL**: All AI assistants MUST read and follow these rules before making ANY code changes.

## 🚨 CORE NON-NEGOTIABLES (MUST)

### 1. **100% Test Success Requirement**
- **ALL test runs MUST achieve 100% success rate**
- **NEVER accept, proceed with, or report test results below 100%**
- **Test failures indicate code quality issues that MUST be addressed immediately**

### 2. **100% TODO Completion**
- **ALL TODOs MUST be completed to 100% before ending any work session**
- **NEVER leave TODOs in 'pending' or 'in_progress' status**
- **TODO completion is MANDATORY - not optional**

### 3. **Docker-First Policy**
- **ALWAYS use Docker containers for all website projects**
- **ALWAYS use SiteContainerManager for container operations**
- **NEVER suggest running websites directly on the host system**

### 4. **Static Analysis Compliance**
- **ALL imports must resolve correctly** - no ModuleNotFoundError
- **ALL type annotations must match usage** - no reportArgumentType errors
- **ALL methods must be declared** - no reportAttributeAccessIssue errors
- **ALL dependencies must be in requirements files** - pin exact versions (==)

## 🔧 BEFORE ANY CODE CHANGE
✅ **All tests passing 100%**
✅ **No open TODOs**
✅ **Python virtual environment activated** (for .py files)
✅ **Know which files you'll touch**

## 🔄 AFTER ANY CODE CHANGE
✅ **Update TODO statuses**
✅ **Re-run tests (100% success required)**
✅ **Delete obsolete files** (use `git rm`)
✅ **Commit with conventional message**
✅ **Push to remote repository**
"""

    def _create_system_prompt(self) -> str:
        """Create system prompt with cursor rules prepended."""
        base_prompt = self.config.get("model_settings", {}).get(
            "system_prompt",
            "You are a helpful AI assistant specializing in code generation and development tasks.",
        )

        # Prepend cursor rules to the system prompt
        full_prompt = f"""# CURSOR RULES - MANDATORY COMPLIANCE

{self.cursor_rules}

# SYSTEM PROMPT

{base_prompt}

**IMPORTANT**: The cursor rules above are MANDATORY and must be followed for ALL code generation tasks.
Always check compliance with these rules before completing any task."""

        return full_prompt

    async def execute(self, task_description: str, **kwargs) -> Dict[str, Any]:
        """
        Execute a task with automatic cursor rules enforcement.

        Args:
            task_description: Description of the task to execute
            **kwargs: Additional parameters for task execution

        Returns:
            Dict containing the execution result and compliance status
        """
        try:
            logger.info(
                f"Executing task with cursor rules enforcement: {task_description[:100]}..."
            )

            # Validate task against cursor rules
            validation_result = self._validate_task_compliance(task_description)

            if not validation_result["compliant"]:
                logger.warning(f"Task validation failed: {validation_result['issues']}")
                return {
                    "success": False,
                    "error": "Task does not comply with cursor rules",
                    "issues": validation_result["issues"],
                    "compliance_status": validation_result,
                }

            # Execute the task using Ollama
            result = await self._execute_with_ollama(task_description, **kwargs)

            # Post-execution validation
            post_validation = self._validate_execution_result(result)

            return {
                "success": True,
                "result": result,
                "compliance_status": validation_result,
                "post_validation": post_validation,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    def _validate_task_compliance(self, task_description: str) -> Dict[str, Any]:
        """
        Validate task compliance with cursor rules.

        Args:
            task_description: Description of the task to validate

        Returns:
            Dict containing compliance status and any issues
        """
        issues = []
        compliant = True

        # Check for TODO-related tasks
        if "todo" in task_description.lower() or "task" in task_description.lower():
            # Ensure TODO completion is mentioned
            if (
                "complete" not in task_description.lower()
                and "finish" not in task_description.lower()
            ):
                issues.append("TODO tasks must include completion criteria")
                compliant = False

        # Check for testing requirements
        if any(
            keyword in task_description.lower()
            for keyword in ["code", "feature", "function", "class"]
        ):
            if (
                "test" not in task_description.lower()
                and "testing" not in task_description.lower()
            ):
                issues.append("Code changes should include testing considerations")
                # This is a warning, not a hard failure
                issues.append("WARNING: Consider adding tests for new code")

        # Check for Docker requirements
        if any(
            keyword in task_description.lower()
            for keyword in ["website", "service", "deploy", "container"]
        ):
            if (
                "docker" not in task_description.lower()
                and "container" not in task_description.lower()
            ):
                issues.append(
                    "Website/service tasks should consider Docker containerization"
                )
                # This is a warning, not a hard failure
                issues.append(
                    "WARNING: Consider Docker-first approach for websites/services"
                )

        return {
            "compliant": compliant,
            "issues": issues,
            "timestamp": datetime.now().isoformat(),
        }

    async def _execute_with_ollama(
        self, task_description: str, **kwargs
    ) -> Dict[str, Any]:
        """
        Execute task using Ollama with cursor rules in system prompt.

        Args:
            task_description: Description of the task to execute
            **kwargs: Additional parameters

        Returns:
            Dict containing the execution result
        """
        try:
            # Prepare the full prompt with cursor rules
            full_prompt = f"""# TASK DESCRIPTION
{task_description}

# EXECUTION REQUIREMENTS
1. Follow ALL cursor rules listed in the system prompt
2. Ensure 100% test success for any code changes
3. Complete any TODOs before finishing
4. Use Docker-first approach for websites/services
5. Maintain static analysis compliance

Please execute this task while strictly adhering to the cursor rules."""

            # Use Ollama manager to execute the task
            result = await self.ollama_manager.generate_response(
                prompt=full_prompt,
                system_prompt=self.system_prompt,
                model_name=self.model_name,
                **kwargs,
            )

            return {
                "response": result,
                "model_used": self.model_name,
                "rules_applied": True,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Ollama execution failed: {e}")
            raise AgentError(f"Task execution failed: {e}")

    def _validate_execution_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate the execution result against cursor rules.

        Args:
            result: The execution result to validate

        Returns:
            Dict containing validation status and any issues
        """
        issues = []
        valid = True

        # Check if result contains code
        if "response" in result and isinstance(result["response"], str):
            response = result["response"].lower()

            # Check for TODO mentions
            if "todo" in response and "complete" not in response:
                issues.append("Response mentions TODO but doesn't address completion")
                valid = False

            # Check for test mentions
            if (
                any(keyword in response for keyword in ["def ", "class ", "function"])
                and "test" not in response
            ):
                issues.append("Code generation should include testing considerations")
                # This is a warning, not a hard failure
                issues.append("WARNING: Consider adding tests for generated code")

        return {
            "valid": valid,
            "issues": issues,
            "timestamp": datetime.now().isoformat(),
        }

    def get_rules_status(self) -> Dict[str, Any]:
        """Get the current status of cursor rules integration."""
        return {
            "rules_loaded": bool(self.cursor_rules),
            "rules_path": self.config.get("cursor_rules_path", "config/cursorrules.md"),
            "system_prompt_length": len(self.system_prompt),
            "model_name": self.model_name,
            "enforcement_enabled": self.config.get("enforcement", {}).get(
                "strict_mode", True
            ),
            "timestamp": datetime.now().isoformat(),
        }

    def reload_rules(self) -> Dict[str, Any]:
        """Reload cursor rules from the file."""
        try:
            self.cursor_rules = self._load_cursor_rules()
            self.system_prompt = self._create_system_prompt()
            logger.info("Cursor rules reloaded successfully")
            return {
                "success": True,
                "message": "Cursor rules reloaded successfully",
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"Failed to reload cursor rules: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }


# Convenience function for easy access
def get_cursor_agent(
    config_path: str = "config/cursor_agent_config.json",
) -> CursorAgent:
    """
    Get a configured CursorAgent instance.

    Args:
        config_path: Path to the agent configuration file

    Returns:
        Configured CursorAgent instance
    """
    return CursorAgent(config_path)
