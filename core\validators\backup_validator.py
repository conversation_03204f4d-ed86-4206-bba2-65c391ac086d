#!/usr/bin/env python3
"""
Backup Validator for AI Coding Agent
Handles backup integrity checks and validation.
"""

import hashlib
import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class BackupValidator:
    """Validates backup integrity and completeness."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backup_config = config.get("backup", {})
        self.enabled = self.backup_config.get("verification", {}).get("enabled", False)

        logger.info("Backup Validator initialized successfully")

    def validate_backup(self, backup_id: str) -> Dict[str, Any]:
        """Validate a specific backup."""
        try:
            if not self.enabled:
                return {"error": "Backup validation is disabled"}

            logger.info(f"Validating backup: {backup_id}")

            # Find backup directory
            backup_path = self._find_backup_path(backup_id)
            if not backup_path:
                return {"error": f"Backup {backup_id} not found"}

            # Load backup manifest
            manifest = self._load_backup_manifest(backup_path)
            if not manifest:
                return {"error": f"Backup manifest not found for {backup_id}"}

            # Perform validation checks
            validation_results: Dict[str, Any] = {
                "backup_id": backup_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "checks": {},
            }

            # Check manifest integrity
            manifest_check = self._validate_manifest(manifest)
            validation_results["checks"]["manifest"] = manifest_check

            # Check file integrity
            file_check = self._validate_files(backup_path, manifest)
            validation_results["checks"]["files"] = file_check

            # Check backup completeness
            completeness_check = self._validate_completeness(manifest)
            validation_results["checks"]["completeness"] = completeness_check

            # Check backup size
            size_check = self._validate_size(backup_path, manifest)
            validation_results["checks"]["size"] = size_check

            # Determine overall validation result
            all_passed = all(
                check.get("status") == "passed"
                for check in validation_results["checks"].values()
            )
            validation_results["overall_status"] = "passed" if all_passed else "failed"

            return validation_results

        except Exception as e:
            logger.error(f"Error validating backup {backup_id}: {e}")
            return {"error": str(e)}

    def _find_backup_path(self, backup_id: str) -> Optional[Path]:
        """Find backup directory path."""
        try:
            backup_base_path = Path(
                self.backup_config.get("storage", {})
                .get("local", {})
                .get("path", "backups/local")
            )
            backup_path = backup_base_path / backup_id

            if backup_path.exists() and backup_path.is_dir():
                return backup_path

            return None

        except Exception as e:
            logger.error(f"Error finding backup path: {e}")
            return None

    def _load_backup_manifest(self, backup_path: Path) -> Optional[Dict[str, Any]]:
        """Load backup manifest file."""
        try:
            manifest_path = backup_path / "manifest.json"
            if manifest_path.exists():
                with open(manifest_path, "r") as f:
                    return json.load(f)
            return None

        except Exception as e:
            logger.error(f"Error loading backup manifest: {e}")
            return None

    def _validate_manifest(self, manifest: Dict[str, Any]) -> Dict[str, Any]:
        """Validate backup manifest."""
        try:
            required_fields = ["backup_id", "backup_type", "timestamp", "checksum"]
            missing_fields = [
                field for field in required_fields if field not in manifest
            ]

            if missing_fields:
                return {
                    "status": "failed",
                    "error": f"Missing required fields: {missing_fields}",
                }

            return {
                "status": "passed",
                "backup_type": manifest.get("backup_type"),
                "timestamp": manifest.get("timestamp"),
                "checksum": manifest.get("checksum"),
            }

        except Exception as e:
            logger.error(f"Error validating manifest: {e}")
            return {"status": "failed", "error": str(e)}

    def _validate_files(
        self, backup_path: Path, manifest: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate backup files."""
        try:
            files_validated = 0
            files_failed = 0
            file_errors = []

            # Check if all files mentioned in manifest exist
            for file_path in backup_path.rglob("*"):
                if file_path.is_file() and file_path.name != "manifest.json":
                    files_validated += 1

                    # Check file accessibility
                    try:
                        with open(file_path, "rb") as f:
                            # Read first 1KB to test accessibility
                            f.read(1024)
                    except Exception as e:
                        files_failed += 1
                        file_errors.append(f"Cannot read {file_path.name}: {e}")

            return {
                "status": "passed" if files_failed == 0 else "failed",
                "files_validated": files_validated,
                "files_failed": files_failed,
                "errors": file_errors,
            }

        except Exception as e:
            logger.error(f"Error validating files: {e}")
            return {"status": "failed", "error": str(e)}

    def _validate_completeness(self, manifest: Dict[str, Any]) -> Dict[str, Any]:
        """Validate backup completeness."""
        try:
            backup_type = manifest.get("backup_type", "")
            components = manifest.get("components", {})

            expected_components = {
                "full": ["database", "configuration", "code", "user_data", "system"],
                "database": ["database"],
                "configuration": ["configuration"],
                "code": ["code"],
                "user_data": ["user_data"],
            }

            expected = expected_components.get(backup_type, [])
            actual = list(components.keys())

            # For configuration backups, check if configuration files are present
            if backup_type == "configuration":
                config_files = manifest.get("configuration_files", [])
                if config_files:
                    return {
                        "status": "passed",
                        "expected_components": expected,
                        "actual_components": ["configuration"],
                        "missing_components": [],
                    }

            missing_components = [comp for comp in expected if comp not in actual]

            return {
                "status": "passed" if not missing_components else "failed",
                "expected_components": expected,
                "actual_components": actual,
                "missing_components": missing_components,
            }

        except Exception as e:
            logger.error(f"Error validating completeness: {e}")
            return {"status": "failed", "error": str(e)}

    def _validate_size(
        self, backup_path: Path, manifest: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate backup size."""
        try:
            # Calculate actual backup size
            actual_size = sum(
                f.stat().st_size for f in backup_path.rglob("*") if f.is_file()
            )
            expected_size = manifest.get("total_size_bytes", 0)

            # If expected size is 0, use actual size as expected (first backup)
            if expected_size == 0:
                expected_size = actual_size

            # Allow 20% tolerance for size variations
            # At least 1KB tolerance
            tolerance = max(expected_size * 0.2, 1000)
            size_difference = abs(actual_size - expected_size)

            return {
                "status": "passed" if size_difference <= tolerance else "failed",
                "expected_size_bytes": expected_size,
                "actual_size_bytes": actual_size,
                "size_difference_bytes": size_difference,
                "tolerance_bytes": tolerance,
            }

        except Exception as e:
            logger.error(f"Error validating size: {e}")
            return {"status": "failed", "error": str(e)}

    def health_check(self) -> Dict[str, Any]:
        """Perform backup validator health check."""
        try:
            return {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "enabled": self.enabled,
                "checks": {
                    "validator_enabled": (
                        "passed" if self.enabled else "warning: disabled"
                    )
                },
            }

        except Exception as e:
            logger.error(f"Error in backup validator health check: {e}")
            return {"status": "failed", "error": str(e)}

    def update_config(self, new_config: Dict[str, Any]):
        """Update backup validator configuration."""
        try:
            self.config = new_config
            self.backup_config = new_config.get("backup", {})
            self.enabled = self.backup_config.get("verification", {}).get(
                "enabled", False
            )

            logger.info("Backup validator configuration updated successfully")

        except Exception as e:
            logger.error(f"Error updating backup validator configuration: {e}")
