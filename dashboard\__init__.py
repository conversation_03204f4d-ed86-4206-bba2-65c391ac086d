"""
Dashboard Package
Dashboard backend for AI Coding Agent
"""

__version__ = "1.0.0"
__author__ = "AI Coding Agent"
__description__ = (
    "Dashboard backend with WebSocket support for real-time user interface"
)

# Import main components
try:
    from dashboard.auth import (
        authenticate_user,
        create_access_token,
        create_user,
        get_current_active_user,
        get_current_superuser,
        get_current_user,
        verify_token,
    )
    from dashboard.database import (
        backup_database,
        create_tables,
        get_database_stats,
        get_db_context,
        health_check,
        init_db,
        restore_database,
    )
    from dashboard.gpu_monitor import (
        get_gpu_metrics,
        get_gpu_status,
        get_gpu_summary,
        get_model_performance,
    )
    from dashboard.main import app
    from dashboard.models import (
        DeploymentCreate,
        DeploymentResponse,
        LoginRequest,
        ProjectCreate,
        ProjectResponse,
        UserCreate,
        UserResponse,
    )
except ImportError as e:
    # Handle import errors gracefully
    import logging

    logger = logging.getLogger(__name__)
    logger.warning(f"Some dashboard components could not be imported: {e}")

# Define what should be available when importing the package
__all__ = [
    # Main application
    "app",
    # Authentication
    "get_current_user",
    "get_current_active_user",
    "get_current_superuser",
    "create_access_token",
    "verify_token",
    "authenticate_user",
    "create_user",
    # Database
    "get_db_context",
    "create_tables",
    "init_db",
    "health_check",
    "get_database_stats",
    "backup_database",
    "restore_database",
    # Models
    "UserCreate",
    "UserResponse",
    "LoginRequest",
    "ProjectCreate",
    "ProjectResponse",
    "DeploymentCreate",
    "DeploymentResponse",
    # GPU Monitoring
    "get_gpu_status",
    "get_gpu_metrics",
    "get_gpu_summary",
    "get_model_performance",
]
