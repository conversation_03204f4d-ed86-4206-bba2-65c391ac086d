{"aggregation_frequency": 3600, "privacy_threshold": 5, "pattern_similarity_threshold": 0.8, "max_pattern_age": 86400, "encryption_enabled": true, "sensitive_keywords": ["password", "token", "key", "secret", "auth", "user", "email", "ip", "address", "hostname", "domain", "internal", "private"], "data_processing": {"outlier_removal": {"enabled": true, "percentile_low": 10, "percentile_high": 90}, "normalization": {"enabled": true, "method": "min_max"}, "min_data_points": 10}, "confidence_calculation": {"volume_factor_weight": 0.6, "consistency_weight": 0.4, "max_data_points": 1000}, "aggregation_strategies": {"weighted_average": {"enabled": true, "weight_by_volume": true}, "federated_averaging": {"enabled": true, "rounds": 10}}, "privacy_protection": {"differential_privacy": {"enabled": false, "epsilon": 1.0}, "secure_aggregation": {"enabled": true, "homomorphic_encryption": false}}}