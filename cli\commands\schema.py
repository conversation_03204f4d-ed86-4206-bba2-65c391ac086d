"""
Database schema management commands.

This module provides CLI commands for syncing and generating types
from Supabase schema.
"""

import logging
import os
from typing import Optional

import click

from cli.error_handler import error_handler
from cli.state_tracker import track_event
from database.supabase_cli import SupabaseCLI
from db import supabase_config_manager
from db.models import Project
from utils.decorators import CliError, require_project_access, with_db
from utils.tasks import add_typescript_task

logger = logging.getLogger(__name__)


@click.group()
def schema() -> None:
    """Database schema management commands"""
    pass


@schema.command()
@click.argument("project_id", type=int)
@click.pass_context
@error_handler
@with_db
@require_project_access
def sync(ctx, project_id: int) -> None:
    """Sync schema with Supabase"""
    track_event("schema", "Syncing schema", {"project_id": project_id})

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Syncing schema for project {project_id}")

    # Get Supabase config
    config = supabase_config_manager.get_by_project(db, project_id)
    if not config:
        raise CliError("Project is not linked to Supabase")

    # Get project path
    project_path = project.path or os.getcwd()

    # Initialize Supabase CLI
    supabase_cli = SupabaseCLI(project_path)

    logger.info("Syncing schema with Supabase")
    click.echo("🔄 Syncing schema with Supabase...")
    result = supabase_cli.pull_schema()

    if result.success:
        logger.info("Schema sync completed successfully")
        click.echo("✅ Schema synced successfully!")
        click.echo(f"📁 Schema files updated in: {project_path}")
    else:
        error_msg = f"Schema sync failed: {result.error}"
        logger.error(error_msg)
        click.echo(f"❌ {error_msg}")


@schema.command()
@click.argument("project_id", type=int)
@click.option("--output", type=click.Path(), help="Output file for generated types")
@click.pass_context
@error_handler
@with_db
@require_project_access
def generate_types(ctx, project_id: int, output: Optional[str]) -> None:
    """Generate TypeScript types from Supabase schema"""
    track_event("schema", "Generating types", {"project_id": project_id})

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Generating TypeScript types for project {project_id}")

    # Get Supabase config
    config = supabase_config_manager.get_by_project(db, project_id)
    if not config:
        raise CliError("Project is not linked to Supabase")

    # Get project path
    project_path = project.path or os.getcwd()

    # Initialize Supabase CLI
    supabase_cli = SupabaseCLI(project_path)

    # Determine output path
    if output:
        output_path = output
    else:
        output_path = os.path.join(project_path, "types", "supabase.ts")

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    logger.info(f"Generating types to: {output_path}")
    click.echo(f"🔧 Generating TypeScript types...")

    result = supabase_cli.generate_types(output_path)

    if result.success:
        logger.info("Type generation completed successfully")
        click.echo("✅ TypeScript types generated successfully!")
        click.echo(f"📄 Types saved to: {output_path}")

        # Add task for review
        add_typescript_task(f"Review generated types for project {project_id}")
    else:
        error_msg = f"Type generation failed: {result.error}"
        logger.error(error_msg)
        click.echo(f"❌ {error_msg}")
