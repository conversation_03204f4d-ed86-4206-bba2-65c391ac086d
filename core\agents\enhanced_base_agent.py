#!/usr/bin/env python3
"""
Enhanced Base Agent - Advanced base class with memory, verification, and cooldown systems

Provides comprehensive functionality for:
- Memory tracking of applied fixes and tasks
- Verification of task/fix success
- Cooldown periods between attempts
- Attempt limits per task/fix
- Automatic reset on success
- Performance monitoring
- Resource optimization
"""

import asyncio
import json
import logging
import re
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from models.ollama_manager import OllamaModelManager

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task status enumeration"""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    VERIFIED = "verified"


class VerificationLevel(Enum):
    """Verification level enumeration"""

    NONE = "none"
    BASIC = "basic"
    COMPREHENSIVE = "comprehensive"


@dataclass
class TaskMemory:
    """Memory structure for tracking tasks and fixes"""

    task_id: str
    task_type: str
    description: str
    status: TaskStatus
    attempts: int
    max_attempts: int
    last_attempt: datetime
    cooldown_until: Optional[datetime]
    verification_level: VerificationLevel
    verification_result: Optional[bool]
    execution_time: float
    created_at: datetime
    completed_at: Optional[datetime]
    error_message: Optional[str]
    metadata: Dict[str, Any]


class EnhancedBaseAgent:
    """
    Enhanced base class for all specialized agents with memory, verification, and cooldown systems
    """

    def __init__(self, config_path: str, default_config: Dict[str, Any]):
        """
        Initialize the enhanced base agent

        Args:
            config_path: Path to configuration file
            default_config: Default configuration dictionary
        """
        self.config = self._load_config(config_path, default_config)

        # Initialize memory tracking system
        self.task_memory: Dict[str, TaskMemory] = {}
        self.applied_fixes: Set[str] = set()
        self.fix_attempts: Dict[str, Dict[str, Any]] = {}

        # Load memory and verification configuration
        memory_config = self.config.get("memory_tracking", {})
        self.max_attempts = memory_config.get("max_attempts_per_task", 3)
        self.cooldown_seconds = memory_config.get("cooldown_seconds", 300)  # 5 minutes
        self.verify_tasks = memory_config.get("verify_tasks", True)
        self.reset_on_success = memory_config.get("reset_on_success", True)
        self.verification_level = VerificationLevel(
            memory_config.get("verification_level", "basic")
        )

        # Performance tracking
        self.task_history: List[Dict[str, Any]] = []
        self.performance_metrics = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "skipped_tasks": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0,
        }

        # Initialize Ollama manager
        self.ollama_manager = OllamaModelManager()

        # Load model settings
        model_settings = self.config.get("model_settings", {})
        self.model_name = model_settings.get(
            "model_name", self.ollama_manager.current_model
        )
        self.system_prompt = model_settings.get(
            "system_prompt", "You are a helpful AI assistant."
        )

        # Create memory persistence directory
        self.memory_dir = Path("data/agent_memory")
        self.memory_dir.mkdir(parents=True, exist_ok=True)

        # Load existing memory
        self._load_memory()

        logger.info(
            f"{self.__class__.__name__} initialized successfully with memory tracking enabled"
        )

    def _load_config(
        self, config_path: str, default_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Load configuration from JSON file with memory tracking defaults

        Args:
            config_path: Path to configuration file
            default_config: Default configuration to use if file not found

        Returns:
            Configuration dictionary
        """
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {config_path}")
        except FileNotFoundError:
            logger.warning(
                f"Configuration file {config_path} not found, using defaults"
            )
            config = default_config
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            config = default_config

        # Ensure memory tracking configuration exists
        if "memory_tracking" not in config:
            config["memory_tracking"] = {
                "enabled": True,
                "max_attempts_per_task": 3,
                "cooldown_seconds": 300,
                "verify_tasks": True,
                "reset_on_success": True,
                "verification_level": "basic",
                "persistence": True,
                "cleanup_interval": 86400,  # 24 hours
            }

        return config

    def _load_memory(self):
        """Load existing memory from persistent storage"""
        try:
            memory_file = self.memory_dir / f"{self.__class__.__name__}_memory.json"
            if memory_file.exists():
                with open(memory_file, "r") as f:
                    memory_data = json.load(f)

                # Restore task memory
                for task_id, task_data in memory_data.get("tasks", {}).items():
                    task_data["last_attempt"] = datetime.fromisoformat(
                        task_data["last_attempt"]
                    )
                    task_data["created_at"] = datetime.fromisoformat(
                        task_data["created_at"]
                    )
                    if task_data.get("completed_at"):
                        task_data["completed_at"] = datetime.fromisoformat(
                            task_data["completed_at"]
                        )
                    if task_data.get("cooldown_until"):
                        task_data["cooldown_until"] = datetime.fromisoformat(
                            task_data["cooldown_until"]
                        )

                    task_data["status"] = TaskStatus(task_data["status"])
                    task_data["verification_level"] = VerificationLevel(
                        task_data["verification_level"]
                    )

                    self.task_memory[task_id] = TaskMemory(**task_data)

                # Restore applied fixes
                self.applied_fixes = set(memory_data.get("applied_fixes", []))

                # Restore fix attempts
                self.fix_attempts = memory_data.get("fix_attempts", {})

                logger.info(f"Loaded {len(self.task_memory)} tasks from memory")
        except Exception as e:
            logger.warning(f"Could not load memory: {e}")

    def _save_memory(self):
        """Save memory to persistent storage"""
        try:
            memory_file = self.memory_dir / f"{self.__class__.__name__}_memory.json"

            # Convert task memory to serializable format
            tasks_data = {}
            for task_id, task_memory in self.task_memory.items():
                task_dict = asdict(task_memory)
                task_dict["last_attempt"] = task_memory.last_attempt.isoformat()
                task_dict["created_at"] = task_memory.created_at.isoformat()
                if task_memory.completed_at:
                    task_dict["completed_at"] = task_memory.completed_at.isoformat()
                if task_memory.cooldown_until:
                    task_dict["cooldown_until"] = task_memory.cooldown_until.isoformat()
                task_dict["status"] = task_memory.status.value
                task_dict["verification_level"] = task_memory.verification_level.value
                tasks_data[task_id] = task_dict

            memory_data = {
                "tasks": tasks_data,
                "applied_fixes": list(self.applied_fixes),
                "fix_attempts": self.fix_attempts,
                "last_saved": datetime.now().isoformat(),
            }

            with open(memory_file, "w") as f:
                json.dump(memory_data, f, indent=2)

        except Exception as e:
            logger.error(f"Could not save memory: {e}")

    def _get_task_key(self, task_description: str) -> str:
        """Generate a unique key for a task to track attempts"""
        # Normalize the task description for consistent tracking
        normalized = task_description.lower().strip()
        # Remove timestamps and other variable parts
        normalized = re.sub(r"\d{4}-\d{2}-\d{2}", "", normalized)
        normalized = re.sub(r"\d{2}:\d{2}:\d{2}", "", normalized)
        normalized = re.sub(r"\s+", " ", normalized)
        return normalized

    def _check_task_memory(
        self, task_description: str, task_id: str
    ) -> Tuple[bool, Optional[str]]:
        """
        Check if task should be executed based on memory

        Returns:
            Tuple of (should_execute, reason)
        """
        task_key = self._get_task_key(task_description)
        current_time = datetime.now()

        # Check if task exists in memory
        if task_key in self.task_memory:
            task_memory = self.task_memory[task_key]

            # Check if max attempts reached
            if task_memory.attempts >= task_memory.max_attempts:
                return False, f"Max attempts ({task_memory.max_attempts}) reached"

            # Check if still in cooldown
            if task_memory.cooldown_until and current_time < task_memory.cooldown_until:
                remaining = (task_memory.cooldown_until - current_time).total_seconds()
                return False, f"Still in cooldown period ({remaining:.0f}s remaining)"

            # Check if task was already successfully completed
            if (
                task_memory.status == TaskStatus.COMPLETED
                and task_memory.verification_result
            ):
                return False, "Task already completed successfully"

        return True, None

    def _update_task_memory(
        self,
        task_description: str,
        task_id: str,
        status: TaskStatus,
        execution_time: float,
        error_message: Optional[str] = None,
        verification_result: Optional[bool] = None,
    ) -> TaskMemory:
        """Update task memory with execution results"""
        task_key = self._get_task_key(task_description)
        current_time = datetime.now()

        if task_key in self.task_memory:
            task_memory = self.task_memory[task_key]
            task_memory.attempts += 1
            task_memory.last_attempt = current_time
            task_memory.status = status
            task_memory.execution_time = execution_time
            task_memory.error_message = error_message
            task_memory.verification_result = verification_result

            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                task_memory.completed_at = current_time

            # Set cooldown for next attempt
            if status == TaskStatus.FAILED:
                task_memory.cooldown_until = current_time + timedelta(
                    seconds=self.cooldown_seconds
                )
        else:
            # Create new task memory
            task_memory = TaskMemory(
                task_id=task_id,
                task_type=self.__class__.__name__,
                description=task_description,
                status=status,
                attempts=1,
                max_attempts=self.max_attempts,
                last_attempt=current_time,
                cooldown_until=None,
                verification_level=self.verification_level,
                verification_result=verification_result,
                execution_time=execution_time,
                created_at=current_time,
                completed_at=None,
                error_message=error_message,
                metadata={},
            )
            self.task_memory[task_key] = task_memory

        # Save memory
        self._save_memory()
        return task_memory

    async def _verify_task_success(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Verify if a task was actually successful

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        try:
            if not self.verify_tasks:
                return result.get("success", False)

            # Basic verification - check if result indicates success
            if not result.get("success", False):
                return False

            # Agent-specific verification
            verification_result = await self._verify_task_specific(
                task_description, task_id, result
            )

            if verification_result:
                logger.info(f"✅ Task verification passed: {task_id}")
            else:
                logger.warning(f"⚠️ Task verification failed: {task_id}")

            return verification_result

        except Exception as e:
            logger.error(f"❌ Error during task verification: {e}")
            return False

    async def _verify_task_specific(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Agent-specific task verification - to be overridden by subclasses

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        # Default implementation - subclasses should override
        return result.get("success", False)

    async def execute_task(self, task_description: str, task_id: str) -> Dict[str, Any]:
        """
        Enhanced task execution with memory tracking, verification, and cooldown

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        start_time = datetime.now()

        try:
            logger.info(f"Executing {self.__class__.__name__} task: {task_description}")

            # Check memory for previous attempts
            should_execute, reason = self._check_task_memory(task_description, task_id)

            if not should_execute:
                logger.info(f"⏳ Skipping task '{task_description}' - {reason}")
                return {
                    "success": False,
                    "task_id": task_id,
                    "agent_type": self.__class__.__name__,
                    "status": "skipped",
                    "reason": reason,
                    "execution_time": 0.0,
                    "timestamp": datetime.now().isoformat(),
                }

            # Parse task requirements
            requirements = await self._parse_task_requirements(task_description)

            # Execute the specific task implementation
            result = await self._execute_specific_task(requirements, task_id)

            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()

            # Verify task success
            verification_result = await self._verify_task_success(
                task_description, task_id, result
            )

            # Determine final status
            if result.get("success", False) and verification_result:
                status = TaskStatus.COMPLETED
            elif result.get("success", False) and not verification_result:
                status = TaskStatus.FAILED
                result["success"] = False
                result["error"] = "Task verification failed"
            else:
                status = TaskStatus.FAILED

            # Update task memory
            task_memory = self._update_task_memory(
                task_description,
                task_id,
                status,
                execution_time,
                error_message=result.get("error"),
                verification_result=verification_result,
            )

            # Add metadata to result
            result.update(
                {
                    "task_id": task_id,
                    "agent_type": self.__class__.__name__,
                    "execution_time": execution_time,
                    "timestamp": datetime.now().isoformat(),
                    "attempts": task_memory.attempts,
                    "max_attempts": task_memory.max_attempts,
                    "verification_result": verification_result,
                }
            )

            # Store in task history
            self.task_history.append(
                {
                    "task_id": task_id,
                    "description": task_description,
                    "result": result,
                    "execution_time": execution_time,
                    "timestamp": datetime.now().isoformat(),
                    "attempts": task_memory.attempts,
                    "verification_result": verification_result,
                }
            )

            # Update performance metrics
            self._update_performance_metrics(result, execution_time)

            # Reset memory if task was successful and reset_on_success is enabled
            if status == TaskStatus.COMPLETED and self.reset_on_success:
                self._reset_task_memory(task_description)

            logger.info(
                f"Task {task_id} completed with status {status.value} in {execution_time:.2f}s"
            )
            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_result = {
                "success": False,
                "task_id": task_id,
                "agent_type": self.__class__.__name__,
                "error": str(e),
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "message": f"Task failed: {str(e)}",
            }

            # Update task memory for failed task
            self._update_task_memory(
                task_description,
                task_id,
                TaskStatus.FAILED,
                execution_time,
                error_message=str(e),
            )

            logger.error(f"Task {task_id} failed after {execution_time:.2f}s: {e}")
            return error_result

    def _update_performance_metrics(
        self, result: Dict[str, Any], execution_time: float
    ):
        """Update performance metrics"""
        self.performance_metrics["total_tasks"] += 1
        self.performance_metrics["total_execution_time"] += execution_time

        if result.get("success", False):
            self.performance_metrics["successful_tasks"] += 1
        else:
            if result.get("status") == "skipped":
                self.performance_metrics["skipped_tasks"] += 1
            else:
                self.performance_metrics["failed_tasks"] += 1

        self.performance_metrics["average_execution_time"] = (
            self.performance_metrics["total_execution_time"]
            / self.performance_metrics["total_tasks"]
        )

    def _reset_task_memory(self, task_description: str):
        """Reset memory for a specific task"""
        task_key = self._get_task_key(task_description)
        if task_key in self.task_memory:
            del self.task_memory[task_key]
            logger.info(f"🔄 Reset memory for task: {task_description}")

    def get_task_memory(self, task_description: str = None) -> Dict[str, Any]:
        """
        Get task memory information

        Args:
            task_description: Specific task description to get memory for

        Returns:
            Task memory information
        """
        if task_description:
            task_key = self._get_task_key(task_description)
            if task_key in self.task_memory:
                return asdict(self.task_memory[task_key])
            return {}

        return {
            "total_tasks": len(self.task_memory),
            "tasks": {k: asdict(v) for k, v in self.task_memory.items()},
            "applied_fixes": list(self.applied_fixes),
            "fix_attempts": self.fix_attempts,
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.performance_metrics,
            "agent_type": self.__class__.__name__,
            "memory_tasks": len(self.task_memory),
            "applied_fixes": len(self.applied_fixes),
        }

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description - to be overridden by subclasses

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        # Default implementation - subclasses should override
        return {"description": task_description, "parsed": False}

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation - to be overridden by subclasses

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        raise NotImplementedError("Subclasses must implement _execute_specific_task")

    async def shutdown(self):
        """Shutdown the agent and save memory"""
        logger.info(f"Shutting down {self.__class__.__name__}")

        # Save memory
        self._save_memory()

        # Clear task history to free memory
        self.task_history.clear()
