{"service_name": "fine-tuner", "description": "Fine-tuning service for AI models with GPU acceleration", "version": "1.0.0", "health_check": {"endpoint": "/health", "interval": "30s", "timeout": "10s", "start_period": "40s", "retries": 3}, "resources": {"cpu": "2.0", "memory": "8G", "gpu": {"count": 1, "capabilities": ["compute", "utility"]}}, "network": {"name": "ai-coding-network", "mode": "bridge"}, "volumes": [{"source": "./fine_tuning", "target": "/app/fine_tuning", "mode": "ro"}, {"source": "./logs", "target": "/app/logs"}, {"source": "./data/fine_tuning", "target": "/app/data"}], "environment": {"PYTHONPATH": "/app", "PYTHONUNBUFFERED": "1", "FINE_TUNER_HOST": "0.0.0.0", "FINE_TUNER_PORT": "8002", "NVIDIA_VISIBLE_DEVICES": "all", "NVIDIA_DRIVER_CAPABILITIES": "compute,utility"}, "dependencies": ["api", "ollama", "db"], "logging": {"driver": "json-file", "options": {"max-size": "10m", "max-file": "5"}}, "restart_policy": "unless-stopped", "security": {"user": "appuser", "group": "appgroup", "capabilities": [], "readonly": false}, "port_mapping": [{"host": "8002", "container": "8002"}], "build": {"context": ".", "dockerfile": "Dockerfile.fine-tuner"}}