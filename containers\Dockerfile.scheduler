FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
WORKDIR /app
RUN apt-get update && apt-get install -y --no-install-recommends curl \
 && rm -rf /var/lib/apt/lists/*
COPY config/requirements.txt ./requirements.txt
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app DEBIAN_FRONTEND=noninteractive
WORKDIR /app
RUN apt-get update && apt-get install -y --no-install-recommends curl ca-certificates cron \
 && rm -rf /var/lib/apt/lists/*
COPY --from=builder /opt/venv /opt/venv
COPY core/ ./core/
COPY trend_monitoring/ ./trend_monitoring/
COPY ai_optimization/ ./ai_optimization/
COPY utils/ ./utils/
COPY config/ ./config/
COPY scripts/ ./scripts/
RUN addgroup --system appuser && adduser --system --ingroup appuser appuser \
 && mkdir -p logs data backups \
 && chown -R appuser:appuser /app
USER appuser
EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 CMD curl -f http://localhost:8080/health || exit 1
CMD ["python", "-m", "core.maintenance_engine"]
