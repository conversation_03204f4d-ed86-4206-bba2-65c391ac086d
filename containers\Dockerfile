# AI Coding Agent - Main Application
# Follows cursorrules.md Docker-First Policy requirements
# Multi-stage build with security hardening

# Stage 1: Python Builder
FROM python:3.11-slim AS python-builder

ENV PIP_NO_CACHE_DIR=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    libssl-dev \
    libffi-dev \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy Python requirements
COPY requirements.txt ./requirements.txt
COPY requirements-api.txt ./requirements-api.txt
COPY requirements-learning.txt ./requirements-learning.txt

# Create virtual environment and install Python dependencies
RUN python -m venv /opt/venv \
    && . /opt/venv/bin/activate \
    && pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir -r requirements-api.txt \
    && pip install --no-cache-dir -r requirements-learning.txt

# Stage 2: Node.js Builder
FROM node:18-alpine AS node-builder

WORKDIR /app

# Copy Node.js package files
COPY package.json package-lock.json ./

# Install Node.js dependencies
RUN npm ci --omit=dev --frozen-lockfile

# Stage 3: Runtime
FROM python:3.11-slim AS runtime

ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# Install runtime dependencies only
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from python-builder
COPY --from=python-builder /opt/venv /opt/venv

# Copy Node.js dependencies from node-builder
COPY --from=node-builder /app/node_modules ./node_modules

# Copy only necessary application code
COPY api/ ./api/
COPY core/ ./core/
COPY models/ ./models/
COPY db/ ./db/
COPY utils/ ./utils/
COPY security/ ./security/
COPY config/ ./config/
COPY cli/ ./cli/
COPY scripts/ ./scripts/
COPY package.json ./

# Create non-root user for security
RUN groupadd --system --gid 1000 appuser \
    && useradd --system --uid 1000 --gid appuser --create-home --shell /bin/bash appuser \
    && mkdir -p /app/logs /app/data /app/backups /app/test_reports /app/sites /app/uploads /app/ssl /app/database /app/tmp \
    && chown -R appuser:appuser /app \
    && chmod -R 755 /app \
    && chmod +x scripts/*.py

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=15s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command
CMD ["python", "-m", "uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
