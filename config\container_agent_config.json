{"agent_name": "ContainerAgent", "version": "1.0.0", "description": "Specialized agent for container and orchestration tasks", "model_settings": {"model_name": "yi-coder:1.5b", "system_prompt": "You are a DevOps expert specializing in containerization with <PERSON><PERSON> and <PERSON>er Compose. Your primary responsibility is to create and manage the containerized infrastructure for the project. Adhere to the following principles:\n1.  **Dockerfiles:** Always generate multi-stage Dockerfiles that are optimized for size and security. Use non-root users.\n2.  **Docker Compose:** Use Docker Compose for orchestrating all services. Define health checks, resource limits, and networks.\n3.  **Security:** Implement security best practices, including image scanning and network isolation.\n4.  **Automation:** Generate scripts and commands to automate container builds, deployment, and management."}, "container_engine": {"primary_engine": "docker", "secondary_engines": ["podman", "containerd"], "version": "latest", "compose_version": "v2", "buildkit_enabled": true, "experimental_features": false}, "orchestration": {"primary_orchestrator": "docker-compose", "secondary_orchestrators": ["kubernetes", "swarm"], "service_discovery": true, "load_balancing": true, "auto_scaling": false, "health_checks": true}, "container_settings": {"multi_stage_builds": true, "non_root_users": true, "security_scanning": true, "resource_limits": true, "network_isolation": true, "volume_management": true}, "port_management": {"port_range_start": 8080, "port_range_end": 9000, "port_allocation": "dynamic", "port_conflict_resolution": "auto", "port_mapping": "host", "port_validation": true}, "network_configuration": {"default_network": "ai-coding-network", "network_driver": "bridge", "dns_resolution": true, "service_communication": true, "external_connectivity": true, "network_security": true}, "volume_management": {"persistent_volumes": true, "backup_strategy": "automated", "volume_cleanup": true, "data_migration": true, "storage_optimization": true}, "security_settings": {"image_scanning": true, "vulnerability_assessment": true, "secrets_management": true, "access_control": true, "audit_logging": true, "compliance_checking": true}, "monitoring": {"container_monitoring": true, "resource_usage": true, "performance_metrics": true, "log_aggregation": true, "alerting": true, "dashboard_integration": true}, "deployment": {"blue_green_deployment": true, "rolling_updates": true, "rollback_capability": true, "zero_downtime": true, "deployment_validation": true, "post_deployment_tests": true}, "project_structure": {"dockerfile_directory": "containers", "compose_directory": ".", "config_directory": "config", "scripts_directory": "scripts", "logs_directory": "logs"}, "templates": {"dockerfile_template": "containers/Dockerfile.template", "compose_template": "containers/docker-compose.template.yml", "nginx_template": "containers/nginx.template.conf", "health_check_template": "containers/health.template.py"}, "dependencies": {"core_dependencies": ["docker", "docker-compose", "docker-py"], "monitoring_dependencies": ["prometheus", "grafana"], "security_dependencies": ["trivy", "clair"]}, "logging": {"level": "INFO", "format": "json", "file_logging": true, "console_logging": true, "log_rotation": true, "structured_logging": true}, "development": {"debug_mode": false, "development_features": true, "test_mode": false, "local_development": true}}