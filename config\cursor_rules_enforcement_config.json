{"enforcement": {"enabled": true, "strict_mode": true, "auto_fix": false, "check_interval_minutes": 5, "report_interval_hours": 24}, "checks": {"file_organization": {"enabled": true, "critical": true, "description": "Check file organization compliance"}, "todo_completion": {"enabled": true, "critical": true, "description": "Check TODO completion compliance"}, "test_success": {"enabled": true, "critical": true, "description": "Check test success compliance"}, "dependency_management": {"enabled": true, "critical": true, "description": "Check dependency management compliance"}, "cli_api_compliance": {"enabled": true, "critical": true, "description": "Check CLI and API creation compliance"}, "ai_model_compliance": {"enabled": true, "critical": false, "description": "Check AI model compliance (warning only for future flexibility)"}, "git_workflow": {"enabled": true, "critical": false, "description": "Check git workflow compliance"}, "security_compliance": {"enabled": true, "critical": false, "description": "Check security compliance"}, "file_cleanup": {"enabled": true, "critical": false, "description": "Check file cleanup compliance"}, "mock_data_cleanup": {"enabled": true, "critical": false, "description": "Check mock data cleanup compliance"}, "virtual_environment": {"enabled": true, "critical": false, "description": "Check virtual environment compliance"}}, "git_hooks": {"pre_commit": {"enabled": true, "description": "Run cursor rules check before commit"}, "pre_push": {"enabled": true, "description": "Run comprehensive check before push"}, "commit_msg": {"enabled": true, "description": "Validate commit message format"}}, "ci_cd": {"github_actions": {"enabled": true, "description": "Run checks in GitHub Actions"}, "block_on_failure": true, "report_to_slack": false, "report_to_email": false}, "reporting": {"daily_reports": {"enabled": true, "retention_days": 30, "description": "Generate daily compliance reports"}, "dashboard": {"enabled": true, "auto_refresh_minutes": 5, "description": "Web-based compliance dashboard"}, "email_reports": {"enabled": false, "recipients": [], "schedule": "daily", "description": "Email compliance reports"}}, "notifications": {"critical_violations": {"enabled": true, "channels": ["console", "log"], "description": "Notify on critical violations"}, "warnings": {"enabled": true, "channels": ["console"], "description": "Notify on warnings"}, "compliance_score": {"enabled": true, "threshold": 90.0, "description": "Notify when compliance score drops below threshold"}}, "ai_models": {"current_mode": "local_only", "future_mode": "hybrid", "allowed_models": ["deepseek-coder:1.3b", "yi-coder:1.5b", "qwen2.5-coder:3b", "starcoder2:3b", "mistral:7b-instruct-q4_0"], "blocked_patterns": ["openai.com", "api.anthropic.com", "gpt-", "claude-", "text-da<PERSON><PERSON>", "gpt-4", "gpt-3.5"], "allowed_cloud_patterns": ["openai.com", "api.anthropic.com", "gpt-4", "gpt-3.5-turbo", "claude-3"], "description": "AI model usage restrictions - currently local only, future hybrid support"}, "file_organization": {"root_allowed_files": [".giti<PERSON>re", "README.md", "setup.py", "pyproject.toml", "package.json", "package-lock.json", "tsconfig.json", "next.config.js", "tailwind.config.js", "postcss.config.js"], "required_directories": ["docs", "config", "scripts", "tests", "data", "logs", "database"], "description": "File organization rules - package.json and package-lock.json allowed in root as standard Node.js files"}, "testing": {"required_success_rate": 100.0, "timeout_seconds": 300, "coverage_threshold": 80.0, "description": "Testing requirements"}, "dependencies": {"requirements_file": "config/requirements.txt", "exact_version_pinning": true, "security_scanning": true, "description": "Dependency management rules"}, "security": {"blocked_patterns": ["password\\s*=\\s*['\"][^'\"]+['\"]", "api_key\\s*=\\s*['\"][^'\"]+['\"]", "secret\\s*=\\s*['\"][^'\"]+['\"]", "token\\s*=\\s*['\"][^'\"]+['\"]"], "description": "Security scanning patterns"}, "logging": {"level": "INFO", "file": "logs/cursor_rules_enforcement.log", "max_size_mb": 10, "backup_count": 5, "description": "Logging configuration"}}