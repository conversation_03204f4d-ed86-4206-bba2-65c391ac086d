import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Folder,
  File,
  FileText,
  FileCode,
  Image,
  Plus,
  MoreVertical,
  Upload,
  Edit,
  Trash2,
  Download
} from 'lucide-react';
import { useFileStore } from '@/store/file';
import toast from 'react-hot-toast';
import { UploadZone } from '@/frontend/components/UploadZone';

interface Site {
  name: string;
  framework: string;
  status: string;
  uploaded_at: string;
  file_count: number;
  total_size_mb: number;
}

interface IDESidebarUnifiedProps {
  onSiteSelect?: (siteName: string) => void;
  onSitePreview?: (siteName: string) => void;
  onSiteValidate?: (siteName: string) => void;
  onSiteOpen?: (siteName: string) => void;
  onSiteManifest?: (siteName: string) => void;
  onSiteCommands?: (siteName: string) => void;
  onSiteGit?: (siteName: string) => void;
}

export const IDESidebarUnified: React.FC<IDESidebarUnifiedProps> = ({
  onSiteSelect,
  onSitePreview,
  onSiteValidate,
  onSiteOpen,
  onSiteManifest,
  onSiteCommands,
  onSiteGit
}) => {
  // Project/Site state
  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'sites' | 'upload'>('sites');
  const [selectedSite, setSelectedSite] = useState<string | null>(null);
  const [uploadComplete, setUploadComplete] = useState(false);

  // File state (Zustand)
  const files = useFileStore(state => state.files);
  const activeFileId = useFileStore(state => state.activeFileId);
  const setActiveFile = useFileStore(state => state.setActiveFile);
  const createFile = useFileStore(state => state.createFile);
  const createFolder = useFileStore(state => state.createFolder);
  const deleteFile = useFileStore(state => state.deleteFile);

  // FileExplorer local state
  const [showContextMenu, setShowContextMenu] = useState<string | null>(null);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [newFileType, setNewFileType] = useState<'file' | 'folder'>('file');

  // Fetch sites
  const fetchSites = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/sites/list');
      const data = await response.json();
      if (data.status === 'success') {
        setSites(data.sites || []);
      }
    } catch (error) {
      toast.error('Failed to fetch sites');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchSites();
  }, []);

  // Upload logic (reuse from IDESidebar)
  const handleUploadComplete = (result: any) => {
    setUploadComplete(true);
    setTimeout(() => {
      fetchSites();
      setUploadComplete(false);
    }, 1000);
  };

  // File upload (reuse from FileExplorer)
  const onDrop = async (acceptedFiles: File[]) => {
    for (const file of acceptedFiles) {
      try {
        const content = await file.text();
        createFile(file.name, `/${file.name}`, content);
        toast.success(`Uploaded ${file.name}`);
      } catch (error) {
        toast.error('Error uploading file');
      }
    }
  };
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/html': ['.html', '.htm'],
      'text/css': ['.css'],
      'text/javascript': ['.js', '.jsx', '.ts', '.tsx'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg']
    }
  });

  // File tree logic
  const handleFileSelect = (fileId: string) => {
    setActiveFile(fileId);
  };
  const handleFileDelete = (fileId: string) => {
    deleteFile(fileId);
    setShowContextMenu(null);
  };
  const handleContextMenu = (e: React.MouseEvent, fileId: string) => {
    e.preventDefault();
    setShowContextMenu(fileId);
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
  };
  const handleCreateFile = () => {
    setShowCreateDialog(true);
    setNewFileName('');
    setNewFileType('file');
  };
  const handleCreateConfirm = () => {
    if (!newFileName.trim()) return;
    if (newFileType === 'file') {
      const fileExtension = newFileName.includes('.') ? '' : '.txt';
      const fileName = newFileName + fileExtension;
      const content = '';
      createFile(fileName, `/${fileName}`, content);
      toast.success(`Created file ${fileName}`);
    } else {
      // Implement folder creation
      createFolder(newFileName, `/${newFileName}`);
      toast.success(`Created folder ${newFileName}`);
    }
    setShowCreateDialog(false);
    setNewFileName('');
  };
  const handleCreateCancel = () => {
    setShowCreateDialog(false);
    setNewFileName('');
  };
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'html':
      case 'htm': return <FileText className="w-4 h-4 text-orange-500" />;
      case 'css': return <FileCode className="w-4 h-4 text-blue-500" />;
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx': return <FileCode className="w-4 h-4 text-yellow-500" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg': return <Image className="w-4 h-4 text-green-500" />;
      default: return <File className="w-4 h-4 text-gray-500" />;
    }
  };
  const renderFileTree = (fileList: any[], level: number = 0) => {
    return fileList.map(file => (
      <div key={file.id}>
        <div
          className={`flex items-center px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 group ${activeFileId === file.id ? 'bg-blue-100 dark:bg-blue-900' : ''}`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => handleFileSelect(file.id)}
          onContextMenu={(e) => handleContextMenu(e, file.id)}
          tabIndex={0}
          role="treeitem"
          aria-selected={activeFileId === file.id}
          onKeyDown={e => {
            if (e.key === 'Enter') handleFileSelect(file.id);
            if (e.key === 'Delete') handleFileDelete(file.id);
          }}
        >
          {file.type === 'folder' ? (
            <Folder className="w-4 h-4 text-blue-500 mr-2" />
          ) : (
            <div className="w-4 h-4 mr-2 flex items-center justify-center">{getFileIcon(file.name)}</div>
          )}
          <span className="flex-1 truncate">{file.name}</span>
          <button className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
            <MoreVertical className="w-3 h-3" />
          </button>
        </div>
      </div>
    ));
  };

  // Site actions
  const handleSiteAction = (action: string, siteName: string) => {
    setSelectedSite(siteName);
    switch (action) {
      case 'select': onSiteSelect?.(siteName); break;
      case 'preview': onSitePreview?.(siteName); break;
      case 'validate': onSiteValidate?.(siteName); break;
      case 'open': onSiteOpen?.(siteName); break;
      case 'manifest': onSiteManifest?.(siteName); break;
      case 'commands': onSiteCommands?.(siteName); break;
      case 'git': onSiteGit?.(siteName); break;
    }
  };

  // Accessibility: ARIA roles
  // Keyboard navigation: Tab, Enter, Delete for file tree

  return (
    <aside
      className="flex flex-col h-full w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700"
      aria-label="IDE Sidebar"
      role="complementary"
    >
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold" id="sidebar-title">Project Manager</h3>
        <div className="flex gap-2" role="tablist" aria-labelledby="sidebar-title">
          <button
            className={`px-2 py-1 rounded ${activeTab === 'sites' ? 'bg-blue-500 text-white' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'}`}
            onClick={() => setActiveTab('sites')}
            aria-selected={activeTab === 'sites'}
            role="tab"
            aria-controls="sites-panel"
            id="sites-tab"
          >
            Sites
          </button>
          <button
            className={`px-2 py-1 rounded ${activeTab === 'upload' ? 'bg-blue-500 text-white' : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'}`}
            onClick={() => setActiveTab('upload')}
            aria-selected={activeTab === 'upload'}
            role="tab"
            aria-controls="upload-panel"
            id="upload-tab"
          >
            Upload
          </button>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'sites' && (
          <div role="tabpanel" id="sites-panel" aria-labelledby="sites-tab">
            {loading ? (
              <div className="text-center text-gray-500" role="status" aria-live="polite">Loading sites...</div>
            ) : sites.length === 0 ? (
              <div className="text-center text-gray-500">
                <p>No sites imported yet</p>
                <button
                  className="block mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  onClick={() => setActiveTab('upload')}
                  aria-label="Import your first project"
                >
                  Import Your First Project
                </button>
              </div>
            ) : (
              <div role="listbox" aria-label="Site List" aria-describedby="site-list-description">
                {sites.map(site => (
                  <div key={site.name} className="mb-4 border rounded p-2 bg-gray-50 dark:bg-gray-800">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-bold">{site.name}</span>
                      <span className="text-xs text-gray-500">{site.framework}</span>
                      <span className="ml-auto text-xs px-2 py-1 rounded" style={{ background: '#e0e7ef' }}>{site.status}</span>
                    </div>
                    <div className="flex gap-2 mb-2">
                      <span className="text-xs">{site.file_count} files</span>
                      <span className="text-xs">{site.total_size_mb.toFixed(1)}MB</span>
                    </div>
                    <div className="flex flex-wrap gap-2" role="group" aria-label={`Actions for ${site.name}`}>
                      <button
                        className="px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                        onClick={() => handleSiteAction('select', site.name)}
                        aria-label={`Open ${site.name}`}
                      >
                        Open
                      </button>
                      <button
                        className="px-2 py-1 bg-gray-300 text-gray-800 rounded text-xs hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                        onClick={() => handleSiteAction('preview', site.name)}
                        aria-label={`Preview ${site.name}`}
                      >
                        Preview
                      </button>
                      <button
                        className="px-2 py-1 bg-gray-300 text-gray-800 rounded text-xs hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                        onClick={() => handleSiteAction('open', site.name)}
                        aria-label={`Open files for ${site.name}`}
                      >
                        Files
                      </button>
                      <button
                        className="px-2 py-1 bg-gray-300 text-gray-800 rounded text-xs hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                        onClick={() => handleSiteAction('commands', site.name)}
                        aria-label={`Run commands for ${site.name}`}
                      >
                        Commands
                      </button>
                      <button
                        className="px-2 py-1 bg-gray-300 text-gray-800 rounded text-xs hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                        onClick={() => handleSiteAction('git', site.name)}
                        aria-label={`Git operations for ${site.name}`}
                      >
                        Git
                      </button>
                      {site.status === 'needs_review' && (
                        <button
                          className="px-2 py-1 bg-yellow-400 text-black rounded text-xs hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1"
                          onClick={() => handleSiteAction('validate', site.name)}
                          aria-label={`Validate ${site.name}`}
                        >
                          Validate
                        </button>
                      )}
                      <button
                        className="px-2 py-1 bg-gray-200 text-gray-800 rounded text-xs hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                        onClick={() => handleSiteAction('manifest', site.name)}
                        aria-label={`Show info for ${site.name}`}
                      >
                        Info
                      </button>
                    </div>
                    {/* Show file tree for selected site */}
                    {selectedSite === site.name && (
                      <div className="mt-4 border-t pt-2">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-semibold" id="files-section-title">Files</span>
                          <div className="flex gap-1" role="group" aria-labelledby="files-section-title">
                            <button
                              onClick={handleCreateFile}
                              className="p-1 hover:bg-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                              title="Create New File"
                              aria-label="Create new file"
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {}}
                              className="p-1 hover:bg-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                              title="Upload Files"
                              aria-label="Upload files"
                            >
                              <Upload className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        <div
                          {...getRootProps()}
                          className={`overflow-y-auto ${isDragActive ? 'bg-blue-50 border-2 border-dashed border-blue-300' : ''}`}
                          aria-label="File upload area"
                          role="region"
                        >
                          <input {...getInputProps()} aria-label="File upload input" />
                          {isDragActive && (
                            <div className="text-center text-blue-600 py-4" role="status" aria-live="polite">
                              Drop files here to upload
                            </div>
                          )}
                          <div role="tree" aria-label="File Tree" aria-describedby="files-section-title">
                            {renderFileTree(files)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        {activeTab === 'upload' && (
          <div role="tabpanel" id="upload-panel" aria-labelledby="upload-tab">
            <UploadZone
              onUpload={onDrop}
              onUploadComplete={handleUploadComplete}
            />
            {uploadComplete && (
              <div className="mt-4 text-green-600" role="status" aria-live="polite">
                Upload completed! Check the Sites tab.
              </div>
            )}
          </div>
        )}
      </div>
      {/* Context Menu for files */}
      {showContextMenu && (
        <div
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1"
          style={{ left: contextMenuPosition.x, top: contextMenuPosition.y }}
          role="menu"
          aria-label="File context menu"
        >
          <button
            onClick={() => { setShowContextMenu(null); }}
            className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
            role="menuitem"
            aria-label="Edit file"
          >
            <Edit className="w-4 h-4" aria-hidden="true" />
            Edit
          </button>
          <button
            onClick={() => { setShowContextMenu(null); }}
            className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
            role="menuitem"
            aria-label="Download file"
          >
            <Download className="w-4 h-4" aria-hidden="true" />
            Download
          </button>
          <hr className="my-1 border-gray-200" role="separator" />
          <button
            onClick={() => handleFileDelete(showContextMenu!)}
            className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50"
            role="menuitem"
            aria-label="Delete file"
          >
            <Trash2 className="w-4 h-4" aria-hidden="true" />
            Delete
          </button>
        </div>
      )}
      {/* Create File Dialog */}
      {showCreateDialog && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          role="dialog"
          aria-modal="true"
          aria-labelledby="create-dialog-title"
          aria-describedby="create-dialog-description"
        >
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 id="create-dialog-title" className="text-lg font-medium mb-4">
              Create New {newFileType === 'file' ? 'File' : 'Folder'}
            </h3>
            <div className="space-y-4">
              <div>
                <label htmlFor="new-file-name" className="block text-sm font-medium mb-2">
                  Name
                </label>
                <input
                  id="new-file-name"
                  type="text"
                  value={newFileName}
                  onChange={e => setNewFileName(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={newFileType === 'file' ? 'filename.txt' : 'foldername'}
                  autoFocus
                  onKeyPress={e => e.key === 'Enter' && handleCreateConfirm()}
                  aria-describedby="create-dialog-description"
                />
                <p id="create-dialog-description" className="text-sm text-gray-500 mt-1">
                  Enter the name for your new {newFileType === 'file' ? 'file' : 'folder'}
                </p>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={handleCreateCancel}
                  className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateConfirm}
                  disabled={!newFileName.trim()}
                  className="px-4 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-describedby={!newFileName.trim() ? 'create-button-error' : undefined}
                >
                  Create
                </button>
                {!newFileName.trim() && (
                  <span id="create-button-error" className="sr-only">
                    Please enter a name to create the {newFileType === 'file' ? 'file' : 'folder'}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </aside>
  );
};
