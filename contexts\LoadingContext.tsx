import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface LoadingContextType {
  loadingStates: Record<string, LoadingState>;
  setLoading: (key: string, state: LoadingState) => void;
  clearLoading: (key: string) => void;
  clearAllLoading: () => void;
  isAnyLoading: boolean;
  getLoadingMessage: (key: string) => string | undefined;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});

  const setLoading = (key: string, state: LoadingState) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: state,
    }));
  };

  const clearLoading = (key: string) => {
    setLoadingStates(prev => {
      const newStates = { ...prev };
      delete newStates[key];
      return newStates;
    });
  };

  const clearAllLoading = () => {
    setLoadingStates({});
  };

  const isAnyLoading = Object.values(loadingStates).some(state => state.isLoading);

  const getLoadingMessage = (key: string): string | undefined => {
    return loadingStates[key]?.message;
  };

  const value: LoadingContextType = {
    loadingStates,
    setLoading,
    clearLoading,
    clearAllLoading,
    isAnyLoading,
    getLoadingMessage,
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};

export const useLoading = (): LoadingContextType => {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};
