import React, { memo } from 'react';
import FileEditor from '@/frontend/components/FileEditor';
import LivePreview from '@/frontend/components/LivePreview';
import CommandRunner from '@/frontend/components/CommandRunner';
import GitVersionControl from '@/frontend/components/GitVersionControl';
import { PreviewPanel } from './PreviewPanel';

interface MainContentPanelProps {
  selectedSite: string | null;
  activePanel: 'editor' | 'preview' | 'commands' | 'git' | 'none';
  selectedFile: string;
  handleClosePanel: () => void;
}

export const MainContentPanel = memo<MainContentPanelProps>(({
  selectedSite,
  activePanel,
  selectedFile,
  handleClosePanel
}) => {
  if (!selectedSite) {
    return <PreviewPanel />;
  }
  switch (activePanel) {
    case 'editor':
      return selectedFile ? (
        <FileEditor siteName={selectedSite} filePath={selectedFile} onClose={handleClosePanel} />
      ) : (
        <div className="flex items-center justify-center h-full text-gray-500">Select a file to edit</div>
      );
    case 'preview':
      return <LivePreview siteName={selectedSite} onClose={handleClosePanel} />;
    case 'commands':
      return <CommandRunner siteName={selectedSite} onClose={handleClosePanel} />;
    case 'git':
      return <GitVersionControl siteName={selectedSite} onClose={handleClosePanel} />;
    default:
      return <PreviewPanel />;
  }
});
