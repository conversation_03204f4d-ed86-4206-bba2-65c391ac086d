{"automatic_resolution": {"enabled": true, "strict_mode": true, "max_retries": 3, "retry_delay_seconds": 30, "timeout_seconds": 300}, "violation_resolvers": {"test_failures": {"enabled": true, "actions": ["update_dependencies", "run_tests", "fix_common_test_issues"], "max_attempts": 2}, "todo_violations": {"enabled": false, "actions": ["scan_todos", "mark_completed"], "max_attempts": 1}, "import_violations": {"enabled": true, "actions": ["install_missing_packages", "fix_import_paths", "update_pip"], "max_attempts": 2}, "syntax_violations": {"enabled": false, "actions": ["parse_code", "fix_syntax_errors"], "max_attempts": 1}, "file_organization": {"enabled": false, "actions": ["move_files", "update_imports"], "max_attempts": 1}, "dependency_violations": {"enabled": true, "actions": ["update_requirements", "install_dependencies", "check_versions"], "max_attempts": 2}}, "notifications": {"enabled": true, "channels": {"console": true, "log": true, "email": false, "slack": false}, "success_notifications": true, "failure_notifications": true}, "logging": {"level": "INFO", "include_actions": true, "include_results": true}}