"""
SSL Certificate Management CLI Commands
Provides command-line interface for SSL certificate operations
"""

import json
import sys
from pathlib import Path
from typing import Any, Dict

import click

from security.ssl_manager import SSLManager


@click.group()
def ssl():
    """SSL Certificate Management Commands"""
    pass


@ssl.command()
@click.argument("domain")
@click.option("--email", help="Email for Let's Encrypt notifications")
@click.option(
    "--self-signed",
    is_flag=True,
    help="Create self-signed certificate instead of Let's Encrypt",
)
@click.option(
    "--config", default="config/ssl_config.json", help="SSL configuration file path"
)
def setup(domain: str, email: str, self_signed: bool, config: str):
    """Setup SSL certificates for a domain"""
    try:
        ssl_manager = SSLManager(config)

        if self_signed:
            # Temporarily enable self-signed certificates
            ssl_manager.config["self_signed"]["enabled"] = True
            ssl_manager.config["lets_encrypt"]["enabled"] = False
            result = ssl_manager.setup_ssl(domain)
        else:
            result = ssl_manager.setup_ssl(domain, email)

        if result["status"] == "success":
            click.echo(f"✅ {result['message']}")
            click.echo(f"Certificate: {result.get('cert_path', 'N/A')}")
            click.echo(f"Private Key: {result.get('key_path', 'N/A')}")
        else:
            click.echo(f"❌ {result['message']}", err=True)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@ssl.command()
@click.argument("domain")
@click.option(
    "--config", default="config/ssl_config.json", help="SSL configuration file path"
)
def check(domain: str, config: str):
    """Check SSL certificate expiry for a domain"""
    try:
        ssl_manager = SSLManager(config)
        result = ssl_manager.check_certificate_expiry(domain)

        if result["status"] == "success":
            click.echo(f"Domain: {domain}")
            click.echo(f"Expiry Date: {result['expiry_date']}")
            click.echo(f"Days Until Expiry: {result['days_until_expiry']}")
            click.echo(f"Needs Renewal: {'Yes' if result['needs_renewal'] else 'No'}")
        else:
            click.echo(f"❌ {result['message']}", err=True)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@ssl.command()
@click.argument("domain")
@click.option(
    "--config", default="config/ssl_config.json", help="SSL configuration file path"
)
def renew(domain: str, config: str):
    """Renew SSL certificate for a domain"""
    try:
        ssl_manager = SSLManager(config)
        result = ssl_manager.renew_certificate(domain)

        if result["status"] == "success":
            click.echo(f"✅ {result['message']}")
        else:
            click.echo(f"❌ {result['message']}", err=True)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@ssl.command()
@click.option(
    "--config", default="config/ssl_config.json", help="SSL configuration file path"
)
def list(config: str):
    """List all SSL certificates and their status"""
    try:
        ssl_manager = SSLManager(config)
        certificates = ssl_manager.list_certificates()

        if not certificates:
            click.echo("No certificates found.")
            return

        click.echo("SSL Certificates:")
        click.echo("-" * 80)

        for cert in certificates:
            click.echo(f"Domain: {cert['domain']}")
            click.echo(f"Certificate: {cert['cert_path']}")
            click.echo(f"Private Key: {cert['key_path']}")

            expiry_info = cert["expiry_info"]
            if expiry_info["status"] == "success":
                click.echo(f"Expiry: {expiry_info['expiry_date']}")
                click.echo(f"Days Left: {expiry_info['days_until_expiry']}")
                click.echo(
                    f"Needs Renewal: {'Yes' if expiry_info['needs_renewal'] else 'No'}"
                )
            else:
                click.echo(f"Expiry Check: {expiry_info['message']}")

            click.echo("-" * 80)

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@ssl.command()
@click.option(
    "--config", default="config/ssl_config.json", help="SSL configuration file path"
)
def config_show(config: str):
    """Show SSL configuration"""
    try:
        ssl_manager = SSLManager(config)
        click.echo("SSL Configuration:")
        click.echo(json.dumps(ssl_manager.config, indent=2))

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    ssl()
