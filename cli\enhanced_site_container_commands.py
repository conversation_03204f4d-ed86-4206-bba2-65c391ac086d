# cli/enhanced_site_container_commands.py
"""
Enhanced Site Container Management CLI Commands
Provides comprehensive command-line interface for managing site containers with all requested features.
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.site_container_manager import EnvironmentType, SiteContainerManager

logger = logging.getLogger(__name__)


class EnhancedSiteContainerCommands:
    """Enhanced CLI commands for site container management"""

    def __init__(self, agent):
        self.agent = agent
        self.container_manager = SiteContainerManager()

    async def create_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Create an enhanced Docker container for a website"""
        try:
            site_config = {
                "name": site_name,
                "port": kwargs.get("port"),
                "environment": kwargs.get("environment", "production"),
                "ssl_enabled": kwargs.get("ssl_enabled", False),
                "backup_enabled": kwargs.get("backup_enabled", True),
                "monitoring_enabled": kwargs.get("monitoring_enabled", True),
            }

            result = await self.container_manager.create_site_container(
                site_name, site_config
            )

            if result["success"]:
                logger.info(f"✅ Enhanced container created for site {site_name}")
                return {
                    "success": True,
                    "message": f"Enhanced container created for site {site_name}",
                    "container": result["container"],
                    "port": result["port"],
                    "url": result["url"],
                }
            else:
                logger.error(
                    f"❌ Failed to create enhanced container: {result['error']}"
                )
                return result

        except Exception as e:
            logger.error(f"Error creating enhanced site container: {e}")
            return {"success": False, "error": str(e)}

    async def start_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Start a site container with enhanced features"""
        try:
            result = await self.container_manager.start_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Enhanced container started for site {site_name}")
                return {
                    "success": True,
                    "message": f"Enhanced container started for site {site_name}",
                    "url": result["url"],
                }
            else:
                logger.error(
                    f"❌ Failed to start enhanced container: {result['error']}"
                )
                return result

        except Exception as e:
            logger.error(f"Error starting enhanced site container: {e}")
            return {"success": False, "error": str(e)}

    async def stop_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Stop a site container"""
        try:
            result = await self.container_manager.stop_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Enhanced container stopped for site {site_name}")
                return {
                    "success": True,
                    "message": f"Enhanced container stopped for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to stop enhanced container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error stopping enhanced site container: {e}")
            return {"success": False, "error": str(e)}

    async def delete_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Delete a site container and clean up resources"""
        try:
            result = await self.container_manager.delete_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Enhanced container deleted for site {site_name}")
                return {
                    "success": True,
                    "message": f"Enhanced container deleted for site {site_name}",
                }
            else:
                logger.error(
                    f"❌ Failed to delete enhanced container: {result['error']}"
                )
                return result

        except Exception as e:
            logger.error(f"Error deleting enhanced site container: {e}")
            return {"success": False, "error": str(e)}

    async def list_site_containers(self, **kwargs) -> Dict[str, Any]:
        """List all site containers with enhanced information"""
        try:
            result = await self.container_manager.list_containers()

            if result["success"]:
                containers = result["containers"]
                logger.info(f"📋 Found {len(containers)} enhanced containers")

                # Format output for CLI
                formatted_containers = []
                for container in containers:
                    formatted_containers.append(
                        {
                            "site_name": container["site_name"],
                            "port": container["port"],
                            "status": container["status"],
                            "environment": container["environment"],
                            "health_status": container["health_status"],
                            "url": f"http://localhost:{container['port']}",
                            "ssl_enabled": container["ssl_enabled"],
                            "monitoring_enabled": container["monitoring_enabled"],
                            "last_started": container.get("last_started"),
                            "last_backup": container.get("last_backup"),
                        }
                    )

                return {
                    "success": True,
                    "containers": formatted_containers,
                    "total": len(formatted_containers),
                }
            else:
                logger.error(
                    f"❌ Failed to list enhanced containers: {result['error']}"
                )
                return result

        except Exception as e:
            logger.error(f"Error listing enhanced site containers: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_status(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Get detailed container status with resource usage"""
        try:
            result = await self.container_manager.get_container_status(site_name)

            if result["success"]:
                container = result["container"]
                logger.info(f"📊 Status for site {site_name}: {container['status']}")

                return {
                    "success": True,
                    "container": container,
                    "summary": {
                        "site_name": container["site_name"],
                        "status": container["status"],
                        "health": container["health_status"],
                        "port": container["port"],
                        "url": f"http://localhost:{container['port']}",
                        "environment": container["environment"],
                        "resource_usage": container.get("resource_usage"),
                        "ssl_enabled": container["ssl_enabled"],
                        "monitoring_enabled": container["monitoring_enabled"],
                    },
                }
            else:
                logger.error(f"❌ Failed to get container status: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting enhanced container status: {e}")
            return {"success": False, "error": str(e)}

    async def rebuild_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Rebuild a site container with latest changes"""
        try:
            logger.info(f"🔨 Rebuilding enhanced container for site {site_name}")
            result = await self.container_manager.rebuild_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Enhanced container rebuilt for site {site_name}")
                return {
                    "success": True,
                    "message": f"Enhanced container rebuilt for site {site_name}",
                    "url": result.get("url"),
                }
            else:
                logger.error(
                    f"❌ Failed to rebuild enhanced container: {result['error']}"
                )
                return result

        except Exception as e:
            logger.error(f"Error rebuilding enhanced site container: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_logs(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Get container logs with filtering options"""
        try:
            lines = kwargs.get("lines", 100)
            result = await self.container_manager.get_container_logs(site_name, lines)

            if result["success"]:
                logs = result["logs"]
                logger.info(f"📝 Retrieved {len(logs)} log lines for site {site_name}")

                return {
                    "success": True,
                    "logs": logs,
                    "total_lines": len(logs),
                    "site_name": site_name,
                }
            else:
                logger.error(f"❌ Failed to get container logs: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting enhanced container logs: {e}")
            return {"success": False, "error": str(e)}

    async def backup_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Create a backup of a site container"""
        try:
            logger.info(f"💾 Creating backup for site {site_name}")
            result = await self.container_manager.backup_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Backup created for site {site_name}")
                return {
                    "success": True,
                    "message": f"Backup created for site {site_name}",
                    "backup_file": result["backup_file"],
                    "timestamp": result["timestamp"],
                }
            else:
                logger.error(f"❌ Failed to create backup: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error creating backup for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def enable_hot_reload(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Enable hot reload for development"""
        try:
            logger.info(f"🔥 Enabling hot reload for site {site_name}")
            result = await self.container_manager.enable_hot_reload(site_name)

            if result["success"]:
                logger.info(f"✅ Hot reload enabled for site {site_name}")
                return {
                    "success": True,
                    "message": f"Hot reload enabled for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to enable hot reload: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error enabling hot reload for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def configure_ssl(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Configure SSL for a site container"""
        try:
            ssl_config = {
                "cert_path": kwargs.get("cert_path"),
                "key_path": kwargs.get("key_path"),
                "provider": kwargs.get(
                    "provider", "self_signed"
                ),  # self_signed, lets_encrypt
            }

            logger.info(f"🔒 Configuring SSL for site {site_name}")
            result = await self.container_manager.configure_ssl(site_name, ssl_config)

            if result["success"]:
                logger.info(f"✅ SSL configured for site {site_name}")
                return {
                    "success": True,
                    "message": f"SSL configured for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to configure SSL: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error configuring SSL for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_port_allocations(self, **kwargs) -> Dict[str, Any]:
        """Get current port allocations"""
        try:
            allocations = self.container_manager.port_manager.list_allocations()

            formatted_allocations = []
            for site_name, port in allocations.items():
                formatted_allocations.append(
                    {
                        "site_name": site_name,
                        "port": port,
                        "url": f"http://localhost:{port}",
                    }
                )

            logger.info(f"🔌 Found {len(formatted_allocations)} port allocations")

            return {
                "success": True,
                "allocations": formatted_allocations,
                "total": len(formatted_allocations),
            }

        except Exception as e:
            logger.error(f"Error getting port allocations: {e}")
            return {"success": False, "error": str(e)}

    async def restart_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Restart a site container"""
        try:
            logger.info(f"🔄 Restarting enhanced container for site {site_name}")

            # Stop container
            stop_result = await self.container_manager.stop_site_container(site_name)
            if not stop_result["success"]:
                return stop_result

            # Start container
            start_result = await self.container_manager.start_site_container(site_name)
            if not start_result["success"]:
                return start_result

            logger.info(f"✅ Enhanced container restarted for site {site_name}")
            return {
                "success": True,
                "message": f"Enhanced container restarted for site {site_name}",
                "url": start_result["url"],
            }

        except Exception as e:
            logger.error(f"Error restarting enhanced site container: {e}")
            return {"success": False, "error": str(e)}

    async def export_site(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to external hosting"""
        try:
            # static, vercel, netlify, github
            target = kwargs.get("target", "static")
            logger.info(f"📤 Exporting site {site_name} to {target}")

            # This would integrate with the existing ExternalHostingManager
            # For now, we'll create a basic export
            export_dir = Path("exports") / site_name
            export_dir.mkdir(parents=True, exist_ok=True)

            # Copy site files to export directory
            site_path = Path("sites") / site_name
            if site_path.exists():
                import shutil

                shutil.copytree(site_path, export_dir / "site", dirs_exist_ok=True)

                # Create export metadata
                metadata = {
                    "site_name": site_name,
                    "export_target": target,
                    "exported_at": datetime.now().isoformat(),
                    "container_info": None,
                }

                # Get container info if available
                if site_name in self.container_manager.site_containers:
                    container = self.container_manager.site_containers[site_name]
                    metadata["container_info"] = {
                        "port": container.port,
                        "environment": container.environment.value,
                        "ssl_enabled": container.ssl_enabled,
                    }

                with open(export_dir / "export_metadata.json", "w") as f:
                    json.dump(metadata, f, indent=2)

                logger.info(f"✅ Site {site_name} exported to {export_dir}")
                return {
                    "success": True,
                    "message": f"Site {site_name} exported to {target}",
                    "export_path": str(export_dir),
                    "metadata": metadata,
                }
            else:
                return {"success": False, "error": f"Site {site_name} not found"}

        except Exception as e:
            logger.error(f"Error exporting site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_site_dashboard(self, **kwargs) -> Dict[str, Any]:
        """Get a comprehensive dashboard of all sites"""
        try:
            # Get all containers
            containers_result = await self.list_site_containers()
            if not containers_result["success"]:
                return containers_result

            containers = containers_result["containers"]

            # Get port allocations
            ports_result = await self.get_port_allocations()
            if not ports_result["success"]:
                return ports_result

            # Calculate statistics
            total_sites = len(containers)
            running_sites = len([c for c in containers if c["status"] == "running"])
            healthy_sites = len(
                [c for c in containers if c["health_status"] == "healthy"]
            )
            ssl_enabled_sites = len([c for c in containers if c["ssl_enabled"]])

            # Group by environment
            environments = {}
            for container in containers:
                env = container["environment"]
                if env not in environments:
                    environments[env] = []
                environments[env].append(container)

            dashboard = {
                "summary": {
                    "total_sites": total_sites,
                    "running_sites": running_sites,
                    "healthy_sites": healthy_sites,
                    "ssl_enabled_sites": ssl_enabled_sites,
                    "uptime_percentage": (
                        (healthy_sites / total_sites * 100) if total_sites > 0 else 0
                    ),
                },
                "environments": environments,
                "port_allocations": ports_result["allocations"],
                "recent_activity": [],  # Could be enhanced with activity tracking
            }

            logger.info(
                f"📊 Dashboard generated: {running_sites}/{total_sites} sites running"
            )

            return {"success": True, "dashboard": dashboard}

        except Exception as e:
            logger.error(f"Error generating site dashboard: {e}")
            return {"success": False, "error": str(e)}

    async def setup_development_environment(
        self, site_name: str, **kwargs
    ) -> Dict[str, Any]:
        """Set up a complete development environment for a site"""
        try:
            logger.info(f"🛠️ Setting up development environment for site {site_name}")

            # Create site container with development settings
            site_config = {
                "name": site_name,
                "environment": "development",
                "ssl_enabled": False,
                "backup_enabled": True,
                "monitoring_enabled": True,
            }

            # Create container
            create_result = await self.create_site_container(site_name, **site_config)
            if not create_result["success"]:
                return create_result

            # Enable hot reload
            hot_reload_result = await self.enable_hot_reload(site_name)
            if not hot_reload_result["success"]:
                logger.warning(f"Hot reload setup failed: {hot_reload_result['error']}")

            # Start container
            start_result = await self.start_site_container(site_name)
            if not start_result["success"]:
                return start_result

            logger.info(f"✅ Development environment set up for site {site_name}")
            return {
                "success": True,
                "message": f"Development environment set up for site {site_name}",
                "url": start_result["url"],
                "features": {
                    "hot_reload": hot_reload_result["success"],
                    "monitoring": True,
                    "backups": True,
                },
            }

        except Exception as e:
            logger.error(
                f"Error setting up development environment for site {site_name}: {e}"
            )
            return {"success": False, "error": str(e)}

    async def setup_production_environment(
        self, site_name: str, **kwargs
    ) -> Dict[str, Any]:
        """Set up a complete production environment for a site"""
        try:
            logger.info(f"🚀 Setting up production environment for site {site_name}")

            # Create site container with production settings
            site_config = {
                "name": site_name,
                "environment": "production",
                "ssl_enabled": kwargs.get("ssl_enabled", True),
                "backup_enabled": True,
                "monitoring_enabled": True,
            }

            # Create container
            create_result = await self.create_site_container(site_name, **site_config)
            if not create_result["success"]:
                return create_result

            # Configure SSL if requested
            if kwargs.get("ssl_enabled", True):
                ssl_result = await self.configure_ssl(site_name)
                if not ssl_result["success"]:
                    logger.warning(f"SSL setup failed: {ssl_result['error']}")

            # Start container
            start_result = await self.start_site_container(site_name)
            if not start_result["success"]:
                return start_result

            logger.info(f"✅ Production environment set up for site {site_name}")
            return {
                "success": True,
                "message": f"Production environment set up for site {site_name}",
                "url": start_result["url"],
                "features": {
                    "ssl": kwargs.get("ssl_enabled", True),
                    "monitoring": True,
                    "backups": True,
                    "security": True,
                },
            }

        except Exception as e:
            logger.error(
                f"Error setting up production environment for site {site_name}: {e}"
            )
            return {"success": False, "error": str(e)}
