"""
Utility functions for the API module.

This module provides various utility functions for API operations.
"""

import hashlib
import logging as std_logging
import os
from typing import Any, Dict, Iterable, List, Optional, Union

import tqdm

# Constants
FORBIDDEN_FOLDERS = {".git", ".hg", ".svn", "__pycache__", ".pytest_cache"}

# Enums
class XetTokenType:
    """Xet token types."""
    ACCESS = "access"
    REFRESH = "refresh"


# Create a custom logging module that provides get_logger
class LoggingModule:
    """Custom logging module that provides get_logger function."""

    @staticmethod
    def get_logger(name: str) -> std_logging.Logger:
        """Get a logger with the specified name."""
        return std_logging.getLogger(name)


# Create the logging instance
logging = LoggingModule()


def are_progress_bars_disabled() -> bool:
    """Check if progress bars are disabled."""
    return os.environ.get("HF_HUB_DISABLE_PROGRESS_BARS", "0") == "1"


def chunk_iterable(iterable: Iterable[Any], chunk_size: int) -> Iterable[List[Any]]:
    """Split an iterable into chunks of specified size."""
    chunk = []
    for item in iterable:
        chunk.append(item)
        if len(chunk) >= chunk_size:
            yield chunk
            chunk = []
    if chunk:
        yield chunk


def fetch_xet_connection_info_from_repo_info(repo_info: Dict[str, Any]) -> Dict[str, Any]:
    """Fetch Xet connection info from repository info."""
    # This is a simplified implementation
    return {
        "endpoint": repo_info.get("xet_endpoint", "https://xet.huggingface.co"),
        "token": repo_info.get("xet_token"),
        "repo_id": repo_info.get("repo_id")
    }


def get_session() -> Any:
    """Get a requests session."""
    # This is a simplified implementation
    # In a full implementation, this would return a requests.Session
    return None


def hf_raise_for_status(response: Any) -> None:
    """Raise an exception for bad HTTP status codes."""
    # This is a simplified implementation
    # In a full implementation, this would check the response status
    pass


def sha(data: bytes) -> str:
    """Compute SHA1 hash of data."""
    return hashlib.sha1(data).hexdigest()


def git_hash(data: bytes) -> str:
    """Compute Git hash of data."""
    return hashlib.sha1(data).hexdigest()


def tqdm_stream_file(file_path: Union[str, os.PathLike]) -> tqdm.tqdm:
    """Create a tqdm progress bar for streaming a file."""
    file_size = os.path.getsize(file_path)
    return tqdm.tqdm(
        total=file_size,
        unit='B',
        unit_scale=True,
        desc=os.path.basename(file_path)
    )


def validate_hf_hub_args(func):
    """Decorator to validate Hugging Face Hub arguments."""
    def wrapper(*args, **kwargs):
        # This is a simplified implementation
        # In a full implementation, this would validate the arguments
        return func(*args, **kwargs)
    return wrapper


# Export all the functions and classes
__all__ = [
    "FORBIDDEN_FOLDERS",
    "XetTokenType",
    "logging",
    "are_progress_bars_disabled",
    "chunk_iterable",
    "fetch_xet_connection_info_from_repo_info",
    "get_session",
    "hf_raise_for_status",
    "sha",
    "git_hash",
    "tqdm_stream_file",
    "validate_hf_hub_args"
]
