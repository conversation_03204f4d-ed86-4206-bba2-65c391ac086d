#!/usr/bin/env python3
"""
Database Manager for Migration Operations
Provides comprehensive database management with migration support
"""

import asyncio
import json
import logging
import os
import sqlite3
import subprocess
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import psycopg2
from psycopg2.extras import RealDictCursor

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Comprehensive database manager with migration support"""

    def __init__(self, db_url: str = None):
        """Initialize database manager"""
        self.db_url = db_url or os.getenv(
            "DATABASE_URL", "sqlite:///database/ai_coding_agent.db"
        )
        self.connection = None
        self.migration_table = "migration_history"

        # Parse database URL
        if self.db_url.startswith("postgresql://"):
            self.db_type = "postgresql"
            self._parse_postgresql_url()
        else:
            self.db_type = "sqlite"
            self._parse_sqlite_url()

    def close(self) -> None:
        """Close the database connection"""
        if self.connection:
            try:
                self.connection.close()
                self.connection = None
            except Exception as e:
                logger.error(f"Error closing database connection: {e}")

    def _parse_postgresql_url(self):
        """Parse PostgreSQL connection URL"""
        # postgresql://user:pass@host:port/dbname
        url = self.db_url.replace("postgresql://", "")
        if "@" in url:
            auth, rest = url.split("@", 1)
            if ":" in auth:
                self.db_user, self.db_password = auth.split(":", 1)
            else:
                self.db_user = auth
                self.db_password = ""

            if "/" in rest:
                host_port, self.db_name = rest.split("/", 1)
                if ":" in host_port:
                    self.db_host, self.db_port = host_port.split(":", 1)
                    self.db_port = int(self.db_port)
                else:
                    self.db_host = host_port
                    self.db_port = 5432
            else:
                self.db_host = rest
                self.db_port = 5432
                self.db_name = "postgres"
        else:
            self.db_host = "localhost"
            self.db_port = 5432
            self.db_name = "postgres"
            self.db_user = "postgres"
            self.db_password = ""

    def _parse_sqlite_url(self):
        """Parse SQLite connection URL"""
        # sqlite:///path/to/database.db
        self.db_path = self.db_url.replace("sqlite:///", "")
        if not self.db_path.startswith("/"):
            self.db_path = Path.cwd() / self.db_path

    async def get_connection_status(self) -> bool:
        """Check database connection status"""
        try:
            if self.db_type == "postgresql":
                conn = psycopg2.connect(
                    host=self.db_host,
                    port=self.db_port,
                    database=self.db_name,
                    user=self.db_user,
                    password=self.db_password,
                )
                conn.close()
                return True
            else:
                # SQLite
                conn = sqlite3.connect(self.db_path)
                conn.close()
                return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False

    async def get_migration_history(self) -> List[Dict[str, Any]]:
        """Get migration history"""
        try:
            if self.db_type == "postgresql":
                conn = psycopg2.connect(
                    host=self.db_host,
                    port=self.db_port,
                    database=self.db_name,
                    user=self.db_user,
                    password=self.db_password,
                )
                cursor = conn.cursor(cursor_factory=RealDictCursor)
                cursor.execute(
                    f"SELECT * FROM {self.migration_table} ORDER BY applied_at DESC"
                )
                results = cursor.fetchall()
                cursor.close()
                conn.close()
                return [dict(row) for row in results]
            else:
                # SQLite
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(
                    f"SELECT * FROM {self.migration_table} ORDER BY applied_at DESC"
                )
                results = cursor.fetchall()
                cursor.close()
                conn.close()
                return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"Error getting migration history: {e}")
            return []

    async def get_pending_migrations(self) -> List[Dict[str, Any]]:
        """Get pending migrations"""
        try:
            # Get all migration files
            migrations_dir = Path("migrations")
            if not migrations_dir.exists():
                return []

            migration_files = list(migrations_dir.glob("*.sql"))

            # Get applied migrations
            applied_migrations = await self.get_migration_history()
            applied_names = [m["migration_name"] for m in applied_migrations]

            # Find pending migrations
            pending = []
            for migration_file in migration_files:
                migration_name = migration_file.stem
                if migration_name not in applied_names:
                    pending.append(
                        {
                            "name": migration_name,
                            "file_path": str(migration_file),
                            "created_at": datetime.fromtimestamp(
                                migration_file.stat().st_ctime, tz=timezone.utc
                            ).isoformat(),
                        }
                    )

            return sorted(pending, key=lambda x: x["name"])
        except Exception as e:
            logger.error(f"Error getting pending migrations: {e}")
            return []

    async def get_all_migrations(self) -> List[Dict[str, Any]]:
        """Get all migrations (applied and pending)"""
        try:
            # Get applied migrations
            applied = await self.get_migration_history()

            # Get pending migrations
            pending = await self.get_pending_migrations()

            # Combine and format
            all_migrations = []

            for migration in applied:
                all_migrations.append(
                    {
                        "name": migration["migration_name"],
                        "status": "applied",
                        "applied_at": migration["applied_at"],
                        "checksum": migration.get("checksum", ""),
                    }
                )

            for migration in pending:
                all_migrations.append(
                    {
                        "name": migration["name"],
                        "status": "pending",
                        "file_path": migration["file_path"],
                        "created_at": migration["created_at"],
                    }
                )

            return sorted(all_migrations, key=lambda x: x["name"])
        except Exception as e:
            logger.error(f"Error getting all migrations: {e}")
            return []

    async def get_applied_migrations(self) -> List[Dict[str, Any]]:
        """Get applied migrations"""
        return await self.get_migration_history()

    async def get_migration_statistics(self) -> Dict[str, Any]:
        """Get migration statistics"""
        try:
            all_migrations = await self.get_all_migrations()
            applied = [m for m in all_migrations if m["status"] == "applied"]
            pending = [m for m in all_migrations if m["status"] == "pending"]

            return {
                "total_migrations": len(all_migrations),
                "applied_migrations": len(applied),
                "pending_migrations": len(pending),
                "success_rate": (
                    len(applied) / len(all_migrations) * 100 if all_migrations else 0
                ),
                "last_migration": applied[0]["applied_at"] if applied else None,
                "oldest_migration": applied[-1]["applied_at"] if applied else None,
            }
        except Exception as e:
            logger.error(f"Error getting migration statistics: {e}")
            return {}

    async def get_recent_migrations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent migrations"""
        try:
            history = await self.get_migration_history()
            return history[:limit]
        except Exception as e:
            logger.error(f"Error getting recent migrations: {e}")
            return []

    async def run_migration(self, migration_name: str) -> Dict[str, Any]:
        """Run a specific migration"""
        try:
            # Find migration file
            migrations_dir = Path("migrations")
            migration_file = migrations_dir / f"{migration_name}.sql"

            if not migration_file.exists():
                return {
                    "success": False,
                    "error": f"Migration file not found: {migration_name}",
                }

            # Read migration content
            with open(migration_file, "r") as f:
                sql_content = f.read()

            # Execute migration
            if self.db_type == "postgresql":
                conn = psycopg2.connect(
                    host=self.db_host,
                    port=self.db_port,
                    database=self.db_name,
                    user=self.db_user,
                    password=self.db_password,
                )
                cursor = conn.cursor()

                # Execute migration
                cursor.execute(sql_content)

                # Record migration
                cursor.execute(
                    f"""
                    INSERT INTO {self.migration_table}
                    (migration_name, applied_at, checksum)
                    VALUES (%s, %s, %s)
                """,
                    (
                        migration_name,
                        datetime.now(timezone.utc),
                        self._calculate_checksum(sql_content),
                    ),
                )

                conn.commit()
                cursor.close()
                conn.close()
            else:
                # SQLite
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # Execute migration
                cursor.execute(sql_content)

                # Record migration
                cursor.execute(
                    f"""
                    INSERT INTO {self.migration_table}
                    (migration_name, applied_at, checksum)
                    VALUES (?, ?, ?)
                """,
                    (
                        migration_name,
                        datetime.now(timezone.utc).isoformat(),
                        self._calculate_checksum(sql_content),
                    ),
                )

                conn.commit()
                cursor.close()
                conn.close()

            return {
                "success": True,
                "migration_name": migration_name,
                "executed_at": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error running migration {migration_name}: {e}")
            return {"success": False, "error": str(e)}

    async def run_pending_migrations(self) -> Dict[str, Any]:
        """Run all pending migrations"""
        try:
            pending = await self.get_pending_migrations()

            if not pending:
                return {
                    "success": True,
                    "message": "No pending migrations",
                    "migrations_run": 0,
                }

            results = []
            for migration in pending:
                result = await self.run_migration(migration["name"])
                results.append(result)
                if not result["success"]:
                    return {
                        "success": False,
                        "error": f"Migration {migration['name']} failed: {result['error']}",
                        "migrations_run": len([r for r in results if r["success"]]),
                    }

            return {"success": True, "migrations_run": len(results), "results": results}
        except Exception as e:
            logger.error(f"Error running pending migrations: {e}")
            return {"success": False, "error": str(e)}

    async def rollback_migration(self, migration_name: str) -> Dict[str, Any]:
        """Rollback a specific migration"""
        try:
            # Check if migration was applied
            history = await self.get_migration_history()
            applied_migration = next(
                (m for m in history if m["migration_name"] == migration_name), None
            )

            if not applied_migration:
                return {
                    "success": False,
                    "error": f"Migration {migration_name} was not applied",
                }

            # For now, just remove from history (in real implementation, you'd have rollback SQL)
            if self.db_type == "postgresql":
                conn = psycopg2.connect(
                    host=self.db_host,
                    port=self.db_port,
                    database=self.db_name,
                    user=self.db_user,
                    password=self.db_password,
                )
                cursor = conn.cursor()
                cursor.execute(
                    f"DELETE FROM {self.migration_table} WHERE migration_name = %s",
                    (migration_name,),
                )
                conn.commit()
                cursor.close()
                conn.close()
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(
                    f"DELETE FROM {self.migration_table} WHERE migration_name = ?",
                    (migration_name,),
                )
                conn.commit()
                cursor.close()
                conn.close()

            return {
                "success": True,
                "migration_name": migration_name,
                "rolled_back_at": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error rolling back migration {migration_name}: {e}")
            return {"success": False, "error": str(e)}

    async def rollback_last_migration(self) -> Dict[str, Any]:
        """Rollback the last applied migration"""
        try:
            history = await self.get_migration_history()
            if not history:
                return {"success": False, "error": "No migrations to rollback"}

            last_migration = history[0]
            return await self.rollback_migration(last_migration["migration_name"])
        except Exception as e:
            logger.error(f"Error rolling back last migration: {e}")
            return {"success": False, "error": str(e)}

    async def validate_migration_syntax(self, migration_name: str) -> bool:
        """Validate migration syntax"""
        try:
            migrations_dir = Path("migrations")
            migration_file = migrations_dir / f"{migration_name}.sql"

            if not migration_file.exists():
                return False

            with open(migration_file, "r") as f:
                sql_content = f.read()

            # Basic SQL validation
            sql_upper = sql_content.upper()
            required_keywords = [
                "CREATE",
                "ALTER",
                "INSERT",
                "UPDATE",
                "DELETE",
                "DROP",
            ]

            return any(keyword in sql_upper for keyword in required_keywords)
        except Exception as e:
            logger.error(f"Error validating migration syntax: {e}")
            return False

    async def check_migration_dependencies(self, migration_name: str) -> Dict[str, Any]:
        """Check migration dependencies"""
        # Placeholder implementation
        return {"dependencies": [], "conflicts": [], "can_run": True}

    async def check_migration_conflicts(self, migration_name: str) -> List[str]:
        """Check for migration conflicts"""
        # Placeholder implementation
        return []

    async def create_backup(self, backup_path: str) -> Dict[str, Any]:
        """Create database backup"""
        try:
            if self.db_type == "postgresql":
                # PostgreSQL backup using pg_dump
                cmd = [
                    "pg_dump",
                    "-h",
                    self.db_host,
                    "-p",
                    str(self.db_port),
                    "-U",
                    self.db_user,
                    "-d",
                    self.db_name,
                    "-f",
                    backup_path,
                ]

                env = os.environ.copy()
                env["PGPASSWORD"] = self.db_password

                result = subprocess.run(cmd, env=env, capture_output=True, text=True)

                if result.returncode == 0:
                    return {
                        "success": True,
                        "backup_path": backup_path,
                        "size": (
                            Path(backup_path).stat().st_size
                            if Path(backup_path).exists()
                            else 0
                        ),
                    }
                else:
                    return {"success": False, "error": result.stderr}
            else:
                # SQLite backup
                import shutil

                shutil.copy2(self.db_path, backup_path)
                return {
                    "success": True,
                    "backup_path": backup_path,
                    "size": (
                        Path(backup_path).stat().st_size
                        if Path(backup_path).exists()
                        else 0
                    ),
                }
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return {"success": False, "error": str(e)}

    async def restore_from_backup(self, backup_path: str) -> Dict[str, Any]:
        """Restore from backup"""
        try:
            if not Path(backup_path).exists():
                return {
                    "success": False,
                    "error": f"Backup file not found: {backup_path}",
                }

            if self.db_type == "postgresql":
                # PostgreSQL restore using psql
                cmd = [
                    "psql",
                    "-h",
                    self.db_host,
                    "-p",
                    str(self.db_port),
                    "-U",
                    self.db_user,
                    "-d",
                    self.db_name,
                    "-f",
                    backup_path,
                ]

                env = os.environ.copy()
                env["PGPASSWORD"] = self.db_password

                result = subprocess.run(cmd, env=env, capture_output=True, text=True)

                if result.returncode == 0:
                    return {
                        "success": True,
                        "backup_path": backup_path,
                        "restored_at": datetime.now(timezone.utc).isoformat(),
                    }
                else:
                    return {"success": False, "error": result.stderr}
            else:
                # SQLite restore
                import shutil

                shutil.copy2(backup_path, self.db_path)
                return {
                    "success": True,
                    "backup_path": backup_path,
                    "restored_at": datetime.now(timezone.utc).isoformat(),
                }
        except Exception as e:
            logger.error(f"Error restoring from backup: {e}")
            return {"success": False, "error": str(e)}

    async def get_migration_performance(self) -> Dict[str, Any]:
        """Get migration performance data"""
        # Placeholder implementation
        return {
            "average_execution_time": 2.5,
            "total_executions": 10,
            "success_rate": 100.0,
        }

    async def get_migration_error_rates(self) -> Dict[str, Any]:
        """Get migration error rates"""
        # Placeholder implementation
        return {"total_errors": 0, "error_rate": 0.0, "last_error": None}

    async def get_rollback_statistics(self) -> Dict[str, Any]:
        """Get rollback statistics"""
        # Placeholder implementation
        return {"total_rollbacks": 0, "rollback_rate": 0.0, "last_rollback": None}

    def _calculate_checksum(self, content: str) -> str:
        """Calculate checksum for content"""
        import hashlib

        return hashlib.md5(content.encode()).hexdigest()
