#!/usr/bin/env python3
"""
Docker Migration CLI Commands
Provides CLI interface for Docker file migration operations.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

logger = logging.getLogger(__name__)
console = Console()


class DockerMigrationCommands:
    """CLI commands for Docker file migration operations"""

    def __init__(self, agent=None):
        self.agent = agent
        self.project_root = Path(".")
        self.containers_dir = self.project_root / "containers"
        self.backup_dir = self.containers_dir / "backup" / "original"

    async def migrate_docker_files(self, **kwargs) -> Dict[str, Any]:
        """Migrate Docker files from optimized to standard names"""
        try:
            console.print("[bold blue]🐳 Docker File Migration[/bold blue]")

            # Check if optimized files exist
            optimized_files = [
                "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.development.yml",
                "containers/docker-compose.production.yml",
                ".dockerignore.optimized",
            ]

            missing_files = []
            for file_path in optimized_files:
                if not (self.project_root / file_path).exists():
                    missing_files.append(file_path)

            if missing_files:
                return {
                    "success": False,
                    "error": f"Missing optimized files: {', '.join(missing_files)}",
                }

            # Create backup
            backup_result = await self._create_backup()
            if not backup_result["success"]:
                return backup_result

            # Migrate files
            migration_result = await self._migrate_files()
            if not migration_result["success"]:
                return migration_result

            # Verify migration
            verification_result = await self._verify_migration()

            return {
                "success": True,
                "backup_result": backup_result,
                "migration_result": migration_result,
                "verification_result": verification_result,
            }
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return {"success": False, "error": str(e)}

    async def rollback_migration(self, **kwargs) -> Dict[str, Any]:
        """Rollback Docker file migration"""
        try:
            console.print("[bold red]🔄 Rolling back Docker file migration[/bold red]")

            if not self.backup_dir.exists():
                return {"success": False, "error": "No backup directory found"}

            # Restore files from backup
            restored_files = []
            failed_restores = []

            for backup_file in self.backup_dir.glob("*"):
                original_path = self._get_original_path(backup_file.name)
                if original_path:
                    try:
                        # Remove current file if it exists
                        if original_path.exists():
                            original_path.unlink()

                        # Restore from backup
                        import shutil

                        shutil.copy2(backup_file, original_path)
                        restored_files.append(str(original_path))
                        console.print(f"✅ Restored: {original_path.name}")
                    except Exception as e:
                        failed_restores.append(f"{backup_file.name}: {e}")
                        console.print(f"❌ Failed to restore {backup_file.name}: {e}")

            return {
                "success": len(failed_restores) == 0,
                "restored_files": restored_files,
                "failed_restores": failed_restores,
            }
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return {"success": False, "error": str(e)}

    async def migration_status(self, **kwargs) -> Dict[str, Any]:
        """Check migration status"""
        try:
            console.print("[bold green]📊 Migration Status[/bold green]")

            status_table = Table(title="Docker File Migration Status")
            status_table.add_column("File", style="cyan")
            status_table.add_column("Status", style="magenta")
            status_table.add_column("Size", style="green")
            status_table.add_column("Backup", style="yellow")

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            status_results = {}

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path
                backup_file = self.backup_dir / Path(original_path).name

                if original_file.exists():
                    size = original_file.stat().st_size
                    if backup_file.exists():
                        backup_size = backup_file.stat().st_size
                        if size != backup_size:
                            status = "✅ Migrated"
                            status_color = "green"
                        else:
                            status = "⚠️ Same size"
                            status_color = "yellow"
                        backup_status = "✅ Available"
                    else:
                        status = "❌ No backup"
                        status_color = "red"
                        backup_status = "❌ Missing"
                else:
                    status = "❌ Not found"
                    status_color = "red"
                    backup_status = "❌ N/A"
                    size = 0

                status_table.add_row(
                    Path(original_path).name, status, f"{size:,} bytes", backup_status
                )

                status_results[original_path] = {
                    "status": status,
                    "size": size,
                    "backup_available": backup_file.exists(),
                }

            console.print(status_table)

            return {"success": True, "status_results": status_results}
        except Exception as e:
            logger.error(f"Status check failed: {e}")
            return {"success": False, "error": str(e)}

    async def cleanup_backups(self, **kwargs) -> Dict[str, Any]:
        """Clean up backup files"""
        try:
            console.print("[bold orange]🗑️ Cleaning up backup files[/bold orange]")

            if not self.backup_dir.exists():
                return {"success": True, "message": "No backup directory found"}

            # Count files to be deleted
            backup_files = list(self.backup_dir.glob("*"))

            if not backup_files:
                return {"success": True, "message": "No backup files found"}

            # Delete backup directory
            import shutil

            shutil.rmtree(self.backup_dir)

            console.print(f"✅ Deleted {len(backup_files)} backup files")

            return {
                "success": True,
                "deleted_files": len(backup_files),
                "backup_dir": str(self.backup_dir),
            }
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
            return {"success": False, "error": str(e)}

    async def _create_backup(self) -> Dict[str, Any]:
        """Create backup of original files"""
        try:
            console.print("Creating backup of original files...")

            # Create backup directory
            self.backup_dir.mkdir(parents=True, exist_ok=True)

            backed_up_files = []

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path

                if original_file.exists():
                    try:
                        # Copy to backup
                        backup_file = self.backup_dir / original_file.name
                        import shutil

                        shutil.copy2(original_file, backup_file)
                        backed_up_files.append(str(backup_file))
                        console.print(f"✅ Backed up: {original_file.name}")
                    except Exception as e:
                        console.print(f"❌ Failed to backup {original_file.name}: {e}")
                        return {
                            "success": False,
                            "error": f"Backup failed for {original_file.name}: {e}",
                        }

            return {"success": True, "backed_up_files": backed_up_files}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _migrate_files(self) -> Dict[str, Any]:
        """Migrate files from optimized to standard names"""
        try:
            console.print("Migrating Docker files...")

            migrated_files = []

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path
                optimized_file = self.project_root / optimized_path

                if optimized_file.exists():
                    try:
                        # Remove original if it exists
                        if original_file.exists():
                            original_file.unlink()
                            console.print(f"🗑️ Removed original: {original_file.name}")

                        # Rename optimized to standard name
                        optimized_file.rename(original_file)
                        migrated_files.append(str(original_file))
                        console.print(
                            f"✅ Migrated: {optimized_file.name} -> {original_file.name}"
                        )
                    except Exception as e:
                        console.print(
                            f"❌ Failed to migrate {optimized_file.name}: {e}"
                        )
                        return {
                            "success": False,
                            "error": f"Migration failed for {optimized_file.name}: {e}",
                        }

            return {"success": True, "migrated_files": migrated_files}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _verify_migration(self) -> Dict[str, Any]:
        """Verify migration was successful"""
        try:
            console.print("Verifying migration...")

            verification_results = {}

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path
                backup_file = self.backup_dir / Path(original_path).name

                if original_file.exists():
                    if backup_file.exists():
                        original_size = original_file.stat().st_size
                        backup_size = backup_file.stat().st_size

                        if original_size != backup_size:
                            verification_results[original_path] = "✅ Migrated"
                        else:
                            verification_results[original_path] = "⚠️ Same size"
                    else:
                        verification_results[original_path] = "❌ No backup"
                else:
                    verification_results[original_path] = "❌ File not found"

            return {"success": True, "verification_results": verification_results}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _get_original_path(self, backup_filename: str) -> Optional[Path]:
        """Get original file path from backup filename"""
        file_mappings = {
            "Dockerfile.api": "containers/Dockerfile.api",
            "Dockerfile.frontend": "containers/Dockerfile.frontend",
            "docker-compose.dev.yml": "containers/docker-compose.dev.yml",
            "docker-compose.prod.yml": "containers/docker-compose.prod.yml",
            ".dockerignore": ".dockerignore",
        }

        if backup_filename in file_mappings:
            return self.project_root / file_mappings[backup_filename]

        return None


# CLI Commands
@click.group()
def docker_migration():
    """Docker file migration commands"""
    pass


@docker_migration.command()
def migrate():
    """Migrate Docker files from optimized to standard names"""

    async def _migrate():
        commands = DockerMigrationCommands()
        result = await commands.migrate_docker_files()

        if result["success"]:
            console.print(
                Panel("[bold green]✅ Migration completed successfully![/bold green]")
            )
        else:
            console.print(
                Panel(f"[bold red]❌ Migration failed: {result['error']}[/bold red]")
            )

    asyncio.run(_migrate())


@docker_migration.command()
def rollback():
    """Rollback Docker file migration"""

    async def _rollback():
        commands = DockerMigrationCommands()
        result = await commands.rollback_migration()

        if result["success"]:
            console.print(
                Panel("[bold green]✅ Rollback completed successfully![/bold green]")
            )
        else:
            console.print(
                Panel(f"[bold red]❌ Rollback failed: {result['error']}[/bold red]")
            )

    asyncio.run(_rollback())


@docker_migration.command()
def status():
    """Check migration status"""

    async def _status():
        commands = DockerMigrationCommands()
        result = await commands.migration_status()

        if not result["success"]:
            console.print(
                Panel(f"[bold red]❌ Status check failed: {result['error']}[/bold red]")
            )

    asyncio.run(_status())


@docker_migration.command()
def cleanup():
    """Clean up backup files"""

    async def _cleanup():
        commands = DockerMigrationCommands()
        result = await commands.cleanup_backups()

        if result["success"]:
            console.print(
                Panel("[bold green]✅ Cleanup completed successfully![/bold green]")
            )
        else:
            console.print(
                Panel(f"[bold red]❌ Cleanup failed: {result['error']}[/bold red]")
            )

    asyncio.run(_cleanup())


if __name__ == "__main__":
    docker_migration()
