{"enabled_features": {"api_documentation": true, "user_guides": true, "technical_docs": true, "code_comments": true, "maintenance": true, "version_control": true}, "model_config": {"qwen_coder": {"model_name": "qwen2.5-coder:3b", "temperature": 0.2, "max_tokens": 4096, "top_p": 0.9, "frequency_penalty": 0.0, "presence_penalty": 0.0}}, "documentation_settings": {"output_formats": ["markdown", "html", "pdf", "rst"], "languages": ["python", "javascript", "typescript", "java", "cpp"], "style_guides": {"python": "google", "javascript": "jsdoc", "typescript": "tsdoc", "java": "javadoc", "cpp": "doxygen"}, "quality_thresholds": {"minimum_quality_score": 0.7, "minimum_completeness_score": 0.8, "maximum_issues_per_document": 10}, "generation_settings": {"include_examples": true, "include_troubleshooting": true, "include_best_practices": true, "auto_generate_toc": true, "include_version_info": true}}, "version_control": {"auto_commit": true, "branch_strategy": "feature-based", "review_required": true, "commit_message_template": "docs: {type} - {description}", "merge_strategy": "squash", "protected_branches": ["main", "production"]}, "access_control": {"roles": ["admin", "writer", "reviewer", "viewer"], "permissions": {"admin": ["read", "write", "delete", "approve", "manage_users", "manage_workflows"], "writer": ["read", "write", "submit_for_approval"], "reviewer": ["read", "approve", "reject", "comment"], "viewer": ["read", "comment"]}, "default_role": "viewer", "session_timeout": 3600, "max_failed_attempts": 5}, "maintenance": {"auto_update": true, "update_frequency": "daily", "outdated_threshold_days": 30, "quality_checks": {"check_links": true, "check_examples": true, "check_completeness": true, "check_consistency": true}, "backup_enabled": true, "backup_frequency": "weekly"}, "analytics": {"track_metrics": true, "track_usage": true, "track_performance": true, "retention_days": 90, "export_enabled": true, "export_formats": ["json", "csv", "pdf"]}, "performance": {"max_concurrent_generations": 5, "timeout_seconds": 300, "cache_enabled": true, "cache_ttl_hours": 24, "rate_limiting": {"requests_per_minute": 60, "burst_limit": 10}}, "logging": {"level": "INFO", "file_logging": true, "log_file_path": "logs/documentation_generation.log", "max_file_size_mb": 100, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "notifications": {"email_enabled": false, "webhook_enabled": false, "slack_enabled": false, "notification_events": ["documentation_generated", "quality_check_failed", "approval_required", "maintenance_completed"]}, "integration": {"git_enabled": true, "ci_cd_enabled": false, "api_enabled": true, "webhook_endpoints": [], "external_tools": {"spell_checker": "enabled", "grammar_checker": "enabled", "link_validator": "enabled"}}}