{"stress_thresholds": {"low": 0.3, "medium": 0.6, "high": 0.8, "critical": 0.95}, "quality_levels": ["fast", "good", "comprehensive"], "user_priority_levels": ["critical", "high", "normal", "low"], "resource_allocation_strategies": {"critical": {"cpu": 0.8, "memory": 0.8, "priority": 1.0}, "high": {"cpu": 0.6, "memory": 0.6, "priority": 0.8}, "normal": {"cpu": 0.4, "memory": 0.4, "priority": 0.5}, "low": {"cpu": 0.2, "memory": 0.2, "priority": 0.2}}, "stress_factors": {"cpu_usage": 0.4, "memory_usage": 0.3, "queue_length": 0.2, "error_rate": 0.1}, "quality_mapping": {"normal": "comprehensive", "low": "good", "medium": "good", "high": "fast", "critical": "fast"}, "stress_adjustments": {"normal": 1.0, "low": 0.9, "medium": 0.8, "high": 0.6, "critical": 0.4}, "fallback_models": {"fast": ["yi-coder:1.5b", "mistral:7b-instruct-q4_0"], "good": ["deepseek-coder:6.7b-instruct", "qwen2.5:3b"], "comprehensive": ["deepseek-coder:6.7b-instruct", "starcoder2:3b"]}, "performance_estimates": {"base_performance": {"response_time": 2.0, "success_rate": 0.85, "throughput": 100.0}, "quality_adjustments": {"fast": {"response_time": 0.7, "success_rate": 0.9, "throughput": 1.2}, "good": {"response_time": 1.0, "success_rate": 1.0, "throughput": 1.0}, "comprehensive": {"response_time": 1.5, "success_rate": 1.1, "throughput": 0.8}}}}