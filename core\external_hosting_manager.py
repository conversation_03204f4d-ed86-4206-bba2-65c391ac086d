"""
External Hosting Manager
Handles export and deployment to external hosting providers (Netlify, GitHub Pages, Vercel)
"""

import asyncio
import json
import logging
import os
import shutil
import subprocess
import tempfile
import zipfile
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import requests
import yaml

logger = logging.getLogger(__name__)


class HostingProvider(Enum):
    """Supported hosting providers"""

    NETLIFY = "netlify"
    GITHUB_PAGES = "github_pages"
    VERCEL = "vercel"
    STATIC_EXPORT = "static_export"


@dataclass
class ExportConfig:
    """Configuration for site export"""

    site_name: str
    provider: HostingProvider
    build_dir: str = "build"
    export_dir: str = "export"
    optimize: bool = True
    minify: bool = True
    compress: bool = True
    custom_domain: Optional[str] = None
    environment: str = "production"


@dataclass
class ExportResult:
    """Result of site export"""

    success: bool
    provider: HostingProvider
    site_name: str
    export_path: Optional[str] = None
    deployment_url: Optional[str] = None
    error_message: Optional[str] = None
    build_artifacts: List[str] = None
    export_time: datetime = None


class ExternalHostingManager:
    """Manages external hosting exports and deployments"""

    def __init__(self, sites_dir: str = "sites", export_dir: str = "export"):
        self.sites_dir = Path(sites_dir)
        self.export_dir = Path(export_dir)
        self.build_dir = Path("build")

        # Ensure directories exist
        self.sites_dir.mkdir(exist_ok=True)
        self.export_dir.mkdir(exist_ok=True)
        self.build_dir.mkdir(exist_ok=True)

        # Provider configurations
        self.provider_configs = self._load_provider_configs()

        logger.info("ExternalHostingManager initialized")

    def _load_provider_configs(self) -> Dict[str, Any]:
        """Load provider configurations"""
        config_file = Path("config/external_hosting_config.json")
        if config_file.exists():
            try:
                with open(config_file, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load provider configs: {e}")

        return {
            "netlify": {
                "enabled": False,
                "api_token": "",
                "team_id": "",
                "site_id": "",
            },
            "github_pages": {
                "enabled": False,
                "repository": "",
                "branch": "gh-pages",
                "token": "",
            },
            "vercel": {
                "enabled": False,
                "api_token": "",
                "project_id": "",
                "team_id": "",
            },
        }

    async def export_site(
        self,
        site_name: str,
        provider: HostingProvider,
        config: Optional[ExportConfig] = None,
    ) -> ExportResult:
        """Export a site for external hosting"""
        try:
            logger.info(f"Exporting site {site_name} to {provider.value}")

            # Validate site exists
            site_path = self.sites_dir / site_name
            if not site_path.exists():
                return ExportResult(
                    success=False,
                    provider=provider,
                    site_name=site_name,
                    error_message=f"Site {site_name} not found",
                )

            # Use default config if not provided
            if config is None:
                config = ExportConfig(site_name=site_name, provider=provider)

            # Build the site
            build_result = await self._build_site(site_name, config)
            if not build_result["success"]:
                return ExportResult(
                    success=False,
                    provider=provider,
                    site_name=site_name,
                    error_message=build_result["error"],
                )

            # Export based on provider
            if provider == HostingProvider.NETLIFY:
                return await self._export_to_netlify(site_name, config, build_result)
            elif provider == HostingProvider.GITHUB_PAGES:
                return await self._export_to_github_pages(
                    site_name, config, build_result
                )
            elif provider == HostingProvider.VERCEL:
                return await self._export_to_vercel(site_name, config, build_result)
            elif provider == HostingProvider.STATIC_EXPORT:
                return await self._export_static(site_name, config, build_result)
            else:
                return ExportResult(
                    success=False,
                    provider=provider,
                    site_name=site_name,
                    error_message=f"Unsupported provider: {provider.value}",
                )

        except Exception as e:
            logger.error(f"Error exporting site {site_name}: {e}")
            return ExportResult(
                success=False,
                provider=provider,
                site_name=site_name,
                error_message=str(e),
            )

    async def _build_site(self, site_name: str, config: ExportConfig) -> Dict[str, Any]:
        """Build the site for export"""
        try:
            site_path = self.sites_dir / site_name
            build_path = self.build_dir / site_name

            # Clean previous build
            if build_path.exists():
                shutil.rmtree(build_path)
            build_path.mkdir(parents=True)

            # Copy site files
            shutil.copytree(site_path, build_path, dirs_exist_ok=True)

            # Optimize if requested
            if config.optimize:
                await self._optimize_site(build_path)

            # Minify if requested
            if config.minify:
                await self._minify_site(build_path)

            return {
                "success": True,
                "build_path": str(build_path),
                "build_artifacts": list(build_path.rglob("*")),
            }

        except Exception as e:
            logger.error(f"Error building site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def _optimize_site(self, build_path: Path):
        """Optimize site assets"""
        try:
            # Optimize images
            for img_file in build_path.rglob("*.{jpg,jpeg,png,gif}"):
                if img_file.stat().st_size > 1024 * 1024:  # > 1MB
                    await self._optimize_image(img_file)

            # Optimize CSS
            for css_file in build_path.rglob("*.css"):
                await self._optimize_css(css_file)

            # Optimize JS
            for js_file in build_path.rglob("*.js"):
                await self._optimize_js(js_file)

        except Exception as e:
            logger.warning(f"Error optimizing site: {e}")

    async def _minify_site(self, build_path: Path):
        """Minify site assets"""
        try:
            # Minify CSS
            for css_file in build_path.rglob("*.css"):
                await self._minify_css(css_file)

            # Minify JS
            for js_file in build_path.rglob("*.js"):
                await self._minify_js(js_file)

            # Minify HTML
            for html_file in build_path.rglob("*.html"):
                await self._minify_html(html_file)

        except Exception as e:
            logger.warning(f"Error minifying site: {e}")

    async def _export_to_netlify(
        self, site_name: str, config: ExportConfig, build_result: Dict[str, Any]
    ) -> ExportResult:
        """Export site to Netlify"""
        try:
            netlify_config = self.provider_configs.get("netlify", {})
            if not netlify_config.get("enabled"):
                return ExportResult(
                    success=False,
                    provider=HostingProvider.NETLIFY,
                    site_name=site_name,
                    error_message="Netlify not configured",
                )

            # Create netlify.toml
            netlify_toml = build_result["build_path"] / "netlify.toml"
            netlify_config_content = f"""
[build]
  publish = "."
  command = ""

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
"""
            netlify_toml.write_text(netlify_config_content)

            # Create deployment package
            export_path = (
                self.export_dir
                / f"{site_name}-netlify-{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            )
            await self._create_deployment_package(
                build_result["build_path"], export_path
            )

            # Deploy to Netlify (if API token is configured)
            deployment_url = None
            if netlify_config.get("api_token"):
                deployment_url = await self._deploy_to_netlify_api(
                    export_path, netlify_config
                )

            return ExportResult(
                success=True,
                provider=HostingProvider.NETLIFY,
                site_name=site_name,
                export_path=str(export_path),
                deployment_url=deployment_url,
                build_artifacts=[str(p) for p in build_result["build_artifacts"]],
                export_time=datetime.now(),
            )

        except Exception as e:
            logger.error(f"Error exporting to Netlify: {e}")
            return ExportResult(
                success=False,
                provider=HostingProvider.NETLIFY,
                site_name=site_name,
                error_message=str(e),
            )

    async def _export_to_github_pages(
        self, site_name: str, config: ExportConfig, build_result: Dict[str, Any]
    ) -> ExportResult:
        """Export site to GitHub Pages"""
        try:
            github_config = self.provider_configs.get("github_pages", {})
            if not github_config.get("enabled"):
                return ExportResult(
                    success=False,
                    provider=HostingProvider.GITHUB_PAGES,
                    site_name=site_name,
                    error_message="GitHub Pages not configured",
                )

            # Create GitHub Pages configuration
            github_config_file = (
                build_result["build_path"] / ".github" / "workflows" / "deploy.yml"
            )
            github_config_file.parent.mkdir(parents=True, exist_ok=True)

            workflow_content = f"""
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{{{ secrets.GITHUB_TOKEN }}}}
        publish_dir: .
"""
            github_config_file.write_text(workflow_content)

            # Create deployment package
            export_path = (
                self.export_dir
                / f"{site_name}-github-pages-{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            )
            await self._create_deployment_package(
                build_result["build_path"], export_path
            )

            return ExportResult(
                success=True,
                provider=HostingProvider.GITHUB_PAGES,
                site_name=site_name,
                export_path=str(export_path),
                deployment_url=f"https://{github_config.get('repository', '').split('/')[0]}.github.io/{site_name}",
                build_artifacts=[str(p) for p in build_result["build_artifacts"]],
                export_time=datetime.now(),
            )

        except Exception as e:
            logger.error(f"Error exporting to GitHub Pages: {e}")
            return ExportResult(
                success=False,
                provider=HostingProvider.GITHUB_PAGES,
                site_name=site_name,
                error_message=str(e),
            )

    async def _export_to_vercel(
        self, site_name: str, config: ExportConfig, build_result: Dict[str, Any]
    ) -> ExportResult:
        """Export site to Vercel"""
        try:
            vercel_config = self.provider_configs.get("vercel", {})
            if not vercel_config.get("enabled"):
                return ExportResult(
                    success=False,
                    provider=HostingProvider.VERCEL,
                    site_name=site_name,
                    error_message="Vercel not configured",
                )

            # Create vercel.json
            vercel_json = build_result["build_path"] / "vercel.json"
            vercel_config_content = {
                "version": 2,
                "builds": [{"src": "**/*", "use": "@vercel/static"}],
                "routes": [{"src": "/(.*)", "dest": "/$1"}],
            }
            vercel_json.write_text(json.dumps(vercel_config_content, indent=2))

            # Create deployment package
            export_path = (
                self.export_dir
                / f"{site_name}-vercel-{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            )
            await self._create_deployment_package(
                build_result["build_path"], export_path
            )

            return ExportResult(
                success=True,
                provider=HostingProvider.VERCEL,
                site_name=site_name,
                export_path=str(export_path),
                deployment_url=f"https://{site_name}.vercel.app",
                build_artifacts=[str(p) for p in build_result["build_artifacts"]],
                export_time=datetime.now(),
            )

        except Exception as e:
            logger.error(f"Error exporting to Vercel: {e}")
            return ExportResult(
                success=False,
                provider=HostingProvider.VERCEL,
                site_name=site_name,
                error_message=str(e),
            )

    async def _export_static(
        self, site_name: str, config: ExportConfig, build_result: Dict[str, Any]
    ) -> ExportResult:
        """Export site as static files"""
        try:
            # Create static export directory
            export_path = (
                self.export_dir
                / f"{site_name}-static-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            if export_path.exists():
                shutil.rmtree(export_path)
            export_path.mkdir(parents=True)

            # Copy build files
            shutil.copytree(build_result["build_path"], export_path, dirs_exist_ok=True)

            # Create README
            readme_content = f"""
# {site_name} - Static Export

This is a static export of the {site_name} website.

## Deployment

You can deploy these files to any static hosting service:

- **Netlify**: Drag and drop this folder to Netlify
- **Vercel**: Import this folder to Vercel
- **GitHub Pages**: Push to a GitHub repository
- **AWS S3**: Upload to an S3 bucket with static website hosting
- **Any web server**: Serve these files from any web server

## Files

- `index.html` - Main page
- `assets/` - CSS, JS, and image files
- `*.html` - Additional pages

Generated on: {datetime.now().isoformat()}
"""
            (export_path / "README.md").write_text(readme_content)

            return ExportResult(
                success=True,
                provider=HostingProvider.STATIC_EXPORT,
                site_name=site_name,
                export_path=str(export_path),
                deployment_url=None,
                build_artifacts=[str(p) for p in build_result["build_artifacts"]],
                export_time=datetime.now(),
            )

        except Exception as e:
            logger.error(f"Error creating static export: {e}")
            return ExportResult(
                success=False,
                provider=HostingProvider.STATIC_EXPORT,
                site_name=site_name,
                error_message=str(e),
            )

    async def _create_deployment_package(self, source_path: str, target_path: Path):
        """Create a deployment package (ZIP file)"""
        try:
            with zipfile.ZipFile(target_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for file_path in Path(source_path).rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(source_path)
                        zipf.write(file_path, arcname)

        except Exception as e:
            logger.error(f"Error creating deployment package: {e}")
            raise

    async def _deploy_to_netlify_api(
        self, package_path: Path, config: Dict[str, Any]
    ) -> Optional[str]:
        """Deploy to Netlify via API"""
        try:
            # This would implement actual Netlify API deployment
            # For now, return a placeholder URL
            return f"https://{config.get('site_id', 'placeholder')}.netlify.app"
        except Exception as e:
            logger.error(f"Error deploying to Netlify API: {e}")
            return None

    async def _optimize_image(self, image_path: Path):
        """Optimize an image file"""
        # Placeholder for image optimization
        pass

    async def _optimize_css(self, css_path: Path):
        """Optimize a CSS file"""
        # Placeholder for CSS optimization
        pass

    async def _optimize_js(self, js_path: Path):
        """Optimize a JavaScript file"""
        # Placeholder for JS optimization
        pass

    async def _minify_css(self, css_path: Path):
        """Minify a CSS file"""
        # Placeholder for CSS minification
        pass

    async def _minify_js(self, js_path: Path):
        """Minify a JavaScript file"""
        # Placeholder for JS minification
        pass

    async def _minify_html(self, html_path: Path):
        """Minify an HTML file"""
        # Placeholder for HTML minification
        pass

    async def list_exports(
        self, site_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """List available exports"""
        try:
            exports = []
            for export_path in self.export_dir.iterdir():
                if export_path.is_dir() or export_path.suffix == ".zip":
                    export_info = {
                        "name": export_path.name,
                        "path": str(export_path),
                        "size": (
                            export_path.stat().st_size if export_path.exists() else 0
                        ),
                        "created": datetime.fromtimestamp(export_path.stat().st_ctime),
                        "type": "directory" if export_path.is_dir() else "zip",
                    }

                    if site_name is None or site_name in export_path.name:
                        exports.append(export_info)

            return sorted(exports, key=lambda x: x["created"], reverse=True)

        except Exception as e:
            logger.error(f"Error listing exports: {e}")
            return []

    async def delete_export(self, export_name: str) -> bool:
        """Delete an export"""
        try:
            export_path = self.export_dir / export_name
            if export_path.exists():
                if export_path.is_dir():
                    shutil.rmtree(export_path)
                else:
                    export_path.unlink()
                logger.info(f"Deleted export: {export_name}")
                return True
            else:
                logger.warning(f"Export not found: {export_name}")
                return False

        except Exception as e:
            logger.error(f"Error deleting export {export_name}: {e}")
            return False
