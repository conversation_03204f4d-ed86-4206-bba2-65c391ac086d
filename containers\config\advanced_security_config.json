{"mfa": {"enabled": true, "methods": ["totp", "sms", "email", "webauthn"], "required_for_admin": true, "required_for_sensitive_operations": true, "backup_codes_count": 10, "totp": {"issuer": "AI Coding Agent", "algorithm": "SHA1", "digits": 6, "period": 30}, "sms": {"provider": "twi<PERSON>", "template": "Your verification code is: {code}", "expiry_minutes": 10}, "email": {"template": "verification_email.html", "subject": "Security Verification Code", "expiry_minutes": 15}, "webauthn": {"rp_name": "AI Coding Agent", "rp_id": "localhost", "user_verification": "preferred", "authenticator_attachment": "platform"}}, "oauth2": {"enabled": true, "providers": {"google": {"client_id": "", "client_secret": "", "enabled": true, "scopes": ["openid", "email", "profile"], "authorization_endpoint": "https://accounts.google.com/o/oauth2/auth", "token_endpoint": "https://oauth2.googleapis.com/token", "userinfo_endpoint": "https://www.googleapis.com/oauth2/v2/userinfo"}, "github": {"client_id": "", "client_secret": "", "enabled": true, "scopes": ["read:user", "user:email"], "authorization_endpoint": "https://github.com/login/oauth/authorize", "token_endpoint": "https://github.com/login/oauth/access_token", "userinfo_endpoint": "https://api.github.com/user"}, "microsoft": {"client_id": "", "client_secret": "", "enabled": false, "scopes": ["openid", "email", "profile"], "authorization_endpoint": "https://login.microsoftonline.com/common/oauth2/v2.0/authorize", "token_endpoint": "https://login.microsoftonline.com/common/oauth2/v2.0/token", "userinfo_endpoint": "https://graph.microsoft.com/oidc/userinfo"}}, "session_management": {"session_timeout_minutes": 60, "refresh_token_expiry_days": 30, "max_concurrent_sessions": 5}}, "password_policy": {"enabled": true, "min_length": 12, "require_uppercase": true, "require_lowercase": true, "require_digits": true, "require_special_chars": true, "max_age_days": 90, "history_count": 5, "breach_detection": {"enabled": true, "api_url": "https://api.pwnedpasswords.com/range/", "check_on_registration": true, "check_on_change": true}}, "rbac": {"enabled": true, "default_roles": {"admin": {"permissions": ["*"], "description": "Full system access"}, "user": {"permissions": ["read:own_projects", "write:own_projects", "delete:own_projects"], "description": "Standard user access"}, "viewer": {"permissions": ["read:own_projects"], "description": "Read-only access"}}, "resource_types": ["projects", "files", "deployments", "users", "settings", "logs"], "actions": ["create", "read", "update", "delete", "list", "export", "import"]}, "audit": {"enabled": true, "log_level": "INFO", "retention_days": 365, "encryption": true, "correlation_enabled": true, "events": {"authentication": {"enabled": true, "log_failed_attempts": true, "log_successful_logins": true, "log_logouts": true}, "authorization": {"enabled": true, "log_permission_checks": true, "log_access_denials": true, "log_role_changes": true}, "data_access": {"enabled": true, "log_file_access": true, "log_database_queries": false, "log_export_operations": true}, "system_events": {"enabled": true, "log_configuration_changes": true, "log_deployment_events": true, "log_security_events": true}}, "storage": {"type": "database", "backup_enabled": true, "compression_enabled": true, "partitioning": {"enabled": true, "partition_by": "month"}}}, "compliance": {"gdpr": {"enabled": true, "data_retention_days": 30, "auto_deletion": true, "data_portability": {"enabled": true, "formats": ["json", "csv", "xml"]}, "right_to_be_forgotten": {"enabled": true, "cascade_deletion": true, "anonymization": true}, "consent_management": {"enabled": true, "explicit_consent": true, "consent_tracking": true}}, "soc2": {"enabled": true, "audit_frequency": "monthly", "controls": {"access_control": true, "change_management": true, "risk_assessment": true, "incident_response": true, "backup_recovery": true}, "reporting": {"automated_reports": true, "manual_reviews": true, "external_audits": false}}, "iso27001": {"enabled": true, "controls": {"information_security_policy": true, "asset_management": true, "human_resource_security": true, "physical_environmental_security": true, "communications_operations": true, "access_control": true, "information_systems_acquisition": true, "incident_management": true, "business_continuity": true, "compliance": true}}}, "threat_detection": {"enabled": true, "ml_enabled": true, "auto_response": true, "alert_threshold": 0.8, "detection_rules": {"brute_force": {"enabled": true, "max_attempts": 5, "time_window_minutes": 15, "action": "block_ip"}, "suspicious_activity": {"enabled": true, "unusual_login_time": true, "unusual_location": true, "unusual_device": true, "action": "require_mfa"}, "data_exfiltration": {"enabled": true, "large_data_exports": true, "bulk_downloads": true, "action": "alert_admin"}, "privilege_escalation": {"enabled": true, "role_changes": true, "permission_granted": true, "action": "require_approval"}}, "intelligence": {"ip_reputation": {"enabled": true, "providers": ["abuseipdb", "virustotal"], "cache_duration_hours": 24}, "malware_detection": {"enabled": true, "file_scanning": true, "url_scanning": true, "providers": ["virustotal", "urlscan"]}, "anomaly_detection": {"enabled": true, "user_behavior": true, "system_behavior": true, "network_behavior": true}}}, "encryption": {"data_at_rest": {"enabled": true, "algorithm": "AES-256-GCM", "key_rotation_days": 90, "encrypt_sensitive_fields": true, "encrypt_files": true}, "data_in_transit": {"enabled": true, "tls_version": "1.3", "cipher_suites": ["TLS_AES_256_GCM_SHA384", "TLS_CHACHA20_POLY1305_SHA256", "TLS_AES_128_GCM_SHA256"], "hsts": {"enabled": true, "max_age_seconds": 31536000, "include_subdomains": true, "preload": false}}, "key_management": {"type": "software", "hsm_enabled": false, "key_storage": "database", "backup_keys": true, "key_recovery": true}, "key": "bjJFLVZteU9HaUtXRDg3NmxFU3VXZmRwRUZXa1NIRnlrQUpuZjZjNHlLQT0=", "algorithm": "AES-256", "key_rotation_days": 90, "hsm_enabled": false}, "vulnerability_management": {"enabled": true, "scanning": {"frequency": "daily", "dependency_scanning": true, "code_scanning": true, "infrastructure_scanning": true, "configuration_scanning": true}, "patch_management": {"auto_patch": false, "patch_notification": true, "patch_approval": true, "rollback_enabled": true}, "tools": {"dependency_scanner": "safety", "code_scanner": "bandit", "infrastructure_scanner": "nmap", "configuration_scanner": "custom"}}, "incident_response": {"enabled": true, "automated_response": {"enabled": true, "block_suspicious_ips": true, "require_mfa": true, "alert_admins": true, "isolate_affected_systems": false}, "escalation": {"levels": [{"level": 1, "description": "Low severity", "response_time_minutes": 60, "notify": ["admin"]}, {"level": 2, "description": "Medium severity", "response_time_minutes": 30, "notify": ["admin", "security_team"]}, {"level": 3, "description": "High severity", "response_time_minutes": 15, "notify": ["admin", "security_team", "management"]}]}, "documentation": {"incident_logging": true, "post_incident_reviews": true, "lessons_learned": true, "improvement_tracking": true}}, "monitoring": {"enabled": true, "metrics": {"security_events": true, "authentication_attempts": true, "authorization_failures": true, "data_access_patterns": true, "system_performance": true}, "alerting": {"email": {"enabled": true, "recipients": [], "smtp_config": {}}, "webhook": {"enabled": false, "url": "", "headers": {}}, "slack": {"enabled": false, "webhook_url": "", "channel": "#security"}}, "dashboard": {"enabled": true, "real_time_updates": true, "historical_data": true, "custom_views": true}}, "policies": {"encryption_policy": {"name": "encryption_policy", "rules": {"data_at_rest": {"algorithm": "AES-256", "key_rotation": 90}, "data_in_transit": {"protocol": "TLS-1.3"}}, "created_at": "2025-07-28T22:07:33.662923+00:00", "enabled": true}, "test_policy": {"name": "test_policy", "rules": {"ip_whitelist": ["***********/24"]}, "created_at": "2025-07-28T22:07:34.476365+00:00", "enabled": true}}, "audit_rules": {"high_risk_auth": {"name": "high_risk_auth", "conditions": {"event_type": "authentication", "risk_score": ">80"}, "actions": ["log", "alert", "block"], "created_at": "2025-07-28T22:07:33.966377+00:00", "enabled": true}, "auth_rule": {"name": "auth_rule", "conditions": {"event_type": "authentication"}, "actions": ["log", "alert"], "created_at": "2025-07-28T22:07:34.508492+00:00", "enabled": true}}, "scan_schedule": {"frequency": "weekly", "time": "02:00"}}