"""
Container Monitor CLI Commands
Provides command-line interface for container monitoring and management.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from core.container_monitor import ContainerMonitor, MonitorConfig

logger = logging.getLogger(__name__)


class ContainerMonitorCommands:
    """CLI commands for container monitoring"""

    def __init__(self, agent):
        self.agent = agent
        self.monitor = ContainerMonitor()

    async def start_monitoring(self, **kwargs) -> Dict[str, Any]:
        """Start container monitoring"""
        try:
            await self.monitor.start_monitoring()

            logger.info("✅ Container monitoring started")
            return {
                "success": True,
                "message": "Container monitoring started successfully",
                "status": "running",
            }

        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
            return {"success": False, "error": str(e)}

    async def stop_monitoring(self, **kwargs) -> Dict[str, Any]:
        """Stop container monitoring"""
        try:
            await self.monitor.stop_monitoring()

            logger.info("✅ Container monitoring stopped")
            return {
                "success": True,
                "message": "Container monitoring stopped successfully",
                "status": "stopped",
            }

        except Exception as e:
            logger.error(f"Error stopping monitoring: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_status(
        self, container_name: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """Get status of monitored containers"""
        try:
            result = await self.monitor.get_container_status(container_name)

            if result["success"]:
                if container_name:
                    logger.info(f"✅ Container {container_name} status retrieved")
                else:
                    logger.info(
                        f"✅ Retrieved status for {result.get('count', 0)} containers"
                    )

                return result
            else:
                logger.error(f"❌ Failed to get container status: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return {"success": False, "error": str(e)}

    async def get_restart_history(
        self, container_name: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """Get restart history for containers"""
        try:
            result = await self.monitor.get_restart_history(container_name)

            if result["success"]:
                if container_name:
                    logger.info(f"✅ Restart history for {container_name} retrieved")
                else:
                    logger.info(
                        f"✅ Retrieved restart history for {result.get('total_containers', 0)} containers"
                    )

                return result
            else:
                logger.error(f"❌ Failed to get restart history: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting restart history: {e}")
            return {"success": False, "error": str(e)}

    async def force_restart(self, container_name: str, **kwargs) -> Dict[str, Any]:
        """Force restart a container"""
        try:
            result = await self.monitor.force_restart(container_name)

            if result["success"]:
                logger.info(f"✅ Container {container_name} force restarted")
                return result
            else:
                logger.error(f"❌ Failed to force restart container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error force restarting container: {e}")
            return {"success": False, "error": str(e)}

    async def update_monitor_config(self, **kwargs) -> Dict[str, Any]:
        """Update monitor configuration"""
        try:
            # Create new config from kwargs
            config = MonitorConfig(
                check_interval=kwargs.get("check_interval", 30),
                restart_threshold=kwargs.get("restart_threshold", 3),
                restart_cooldown=kwargs.get("restart_cooldown", 300),
                health_check_timeout=kwargs.get("health_check_timeout", 10),
                memory_threshold=kwargs.get("memory_threshold", 90.0),
                cpu_threshold=kwargs.get("cpu_threshold", 90.0),
                enable_auto_restart=kwargs.get("enable_auto_restart", True),
                enable_notifications=kwargs.get("enable_notifications", True),
                log_restarts=kwargs.get("log_restarts", True),
            )

            result = await self.monitor.update_config(config)

            if result["success"]:
                logger.info("✅ Monitor configuration updated")
                return result
            else:
                logger.error(f"❌ Failed to update configuration: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error updating monitor configuration: {e}")
            return {"success": False, "error": str(e)}

    async def get_monitor_status(self, **kwargs) -> Dict[str, Any]:
        """Get monitor status"""
        try:
            status = {
                "is_monitoring": self.monitor.is_monitoring,
                "monitored_containers": len(self.monitor.monitored_containers),
                "config": {
                    "check_interval": self.monitor.config.check_interval,
                    "restart_threshold": self.monitor.config.restart_threshold,
                    "restart_cooldown": self.monitor.config.restart_cooldown,
                    "enable_auto_restart": self.monitor.config.enable_auto_restart,
                    "enable_notifications": self.monitor.config.enable_notifications,
                },
            }

            logger.info("✅ Monitor status retrieved")
            return {"success": True, "status": status}

        except Exception as e:
            logger.error(f"Error getting monitor status: {e}")
            return {"success": False, "error": str(e)}

    async def list_monitored_containers(self, **kwargs) -> Dict[str, Any]:
        """List all monitored containers"""
        try:
            containers = []
            for container_id, status in self.monitor.monitored_containers.items():
                containers.append(
                    {
                        "id": container_id,
                        "name": status.name,
                        "status": status.status,
                        "health": status.health.value,
                        "restart_count": status.restart_count,
                        "uptime": str(status.uptime) if status.uptime else None,
                        "memory_usage": status.memory_usage,
                        "cpu_usage": status.cpu_usage,
                        "port_mappings": status.port_mappings,
                    }
                )

            logger.info(f"✅ Listed {len(containers)} monitored containers")
            return {"success": True, "containers": containers, "count": len(containers)}

        except Exception as e:
            logger.error(f"Error listing monitored containers: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_health(
        self, container_name: str, **kwargs
    ) -> Dict[str, Any]:
        """Get detailed health information for a container"""
        try:
            # Find container status
            container_status = None
            for status in self.monitor.monitored_containers.values():
                if status.name == container_name:
                    container_status = status
                    break

            if not container_status:
                return {
                    "success": False,
                    "error": f"Container {container_name} not found in monitored containers",
                }

            # Get restart history
            restart_history = await self.monitor.get_restart_history(container_name)

            health_info = {
                "name": container_status.name,
                "status": container_status.status,
                "health": container_status.health.value,
                "restart_count": container_status.restart_count,
                "last_restart": (
                    container_status.last_restart.isoformat()
                    if container_status.last_restart
                    else None
                ),
                "uptime": (
                    str(container_status.uptime) if container_status.uptime else None
                ),
                "memory_usage": container_status.memory_usage,
                "cpu_usage": container_status.cpu_usage,
                "port_mappings": container_status.port_mappings,
                "created": (
                    container_status.created.isoformat()
                    if container_status.created
                    else None
                ),
                "last_check": container_status.last_check.isoformat(),
                "restart_history": (
                    restart_history.get("restarts", [])
                    if restart_history["success"]
                    else []
                ),
            }

            logger.info(f"✅ Health information for {container_name} retrieved")
            return {"success": True, "health": health_info}

        except Exception as e:
            logger.error(f"Error getting container health: {e}")
            return {"success": False, "error": str(e)}

    async def enable_auto_restart(self, **kwargs) -> Dict[str, Any]:
        """Enable automatic container restart"""
        try:
            config = MonitorConfig(
                check_interval=self.monitor.config.check_interval,
                restart_threshold=self.monitor.config.restart_threshold,
                restart_cooldown=self.monitor.config.restart_cooldown,
                health_check_timeout=self.monitor.config.health_check_timeout,
                memory_threshold=self.monitor.config.memory_threshold,
                cpu_threshold=self.monitor.config.cpu_threshold,
                enable_auto_restart=True,
                enable_notifications=self.monitor.config.enable_notifications,
                log_restarts=self.monitor.config.log_restarts,
            )

            result = await self.monitor.update_config(config)

            if result["success"]:
                logger.info("✅ Auto restart enabled")
                return {"success": True, "message": "Auto restart enabled successfully"}
            else:
                return result

        except Exception as e:
            logger.error(f"Error enabling auto restart: {e}")
            return {"success": False, "error": str(e)}

    async def disable_auto_restart(self, **kwargs) -> Dict[str, Any]:
        """Disable automatic container restart"""
        try:
            config = MonitorConfig(
                check_interval=self.monitor.config.check_interval,
                restart_threshold=self.monitor.config.restart_threshold,
                restart_cooldown=self.monitor.config.restart_cooldown,
                health_check_timeout=self.monitor.config.health_check_timeout,
                memory_threshold=self.monitor.config.memory_threshold,
                cpu_threshold=self.monitor.config.cpu_threshold,
                enable_auto_restart=False,
                enable_notifications=self.monitor.config.enable_notifications,
                log_restarts=self.monitor.config.log_restarts,
            )

            result = await self.monitor.update_config(config)

            if result["success"]:
                logger.info("✅ Auto restart disabled")
                return {
                    "success": True,
                    "message": "Auto restart disabled successfully",
                }
            else:
                return result

        except Exception as e:
            logger.error(f"Error disabling auto restart: {e}")
            return {"success": False, "error": str(e)}

    async def clear_restart_history(
        self, container_name: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """Clear restart history for containers"""
        try:
            if container_name:
                # Clear history for specific container
                container_id = None
                for status in self.monitor.monitored_containers.values():
                    if status.name == container_name:
                        container_id = status.container_id
                        break

                if container_id and container_id in self.monitor.restart_history:
                    del self.monitor.restart_history[container_id]
                    self.monitor._save_restart_history()

                    logger.info(f"✅ Restart history cleared for {container_name}")
                    return {
                        "success": True,
                        "message": f"Restart history cleared for {container_name}",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"No restart history found for {container_name}",
                    }
            else:
                # Clear all restart history
                self.monitor.restart_history.clear()
                self.monitor._save_restart_history()

                logger.info("✅ All restart history cleared")
                return {"success": True, "message": "All restart history cleared"}

        except Exception as e:
            logger.error(f"Error clearing restart history: {e}")
            return {"success": False, "error": str(e)}
