"""
Retry Manager and Clarification System
Handles task retries, error analysis, and user clarification requests
"""

import asyncio
import json
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

logger = logging.getLogger(__name__)


class RetryStrategy(Enum):
    """Retry strategies"""

    IMMEDIATE = "immediate"
    LINEAR_BACKOFF = "linear_backoff"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    FIXED_DELAY = "fixed_delay"


class ClarificationType(Enum):
    """Types of clarification requests"""

    MISSING_INFORMATION = "missing_information"
    AMBIGUOUS_REQUEST = "ambiguous_request"
    CONFLICTING_REQUIREMENTS = "conflicting_requirements"
    RESOURCE_CONSTRAINTS = "resource_constraints"
    PERMISSION_REQUIRED = "permission_required"


@dataclass
class RetryConfig:
    """Configuration for retry behavior"""

    max_retries: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay_seconds: int = 5
    max_delay_seconds: int = 300
    backoff_multiplier: float = 2.0


@dataclass
class ClarificationRequest:
    """Represents a request for user clarification"""

    id: str
    task_id: str
    clarification_type: ClarificationType
    message: str
    options: List[str] = field(default_factory=list)
    required_fields: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    resolved_at: Optional[datetime] = None
    response: Optional[Dict[str, Any]] = None
    timeout_minutes: int = 30


class RetryManager:
    """Manages task retries and error recovery"""

    def __init__(self):
        self.retry_configs: Dict[str, RetryConfig] = {}
        self.retry_history: Dict[str, List[Dict[str, Any]]] = {}
        self.clarification_requests: Dict[str, ClarificationRequest] = {}
        self.clarification_callbacks: Dict[str, Callable] = {}

    def set_retry_config(self, task_type: str, config: RetryConfig):
        """Set retry configuration for a task type"""
        self.retry_configs[task_type] = config
        logger.info(f"Set retry config for {task_type}: {config}")

    def get_retry_config(self, task_type: str) -> RetryConfig:
        """Get retry configuration for a task type"""
        return self.retry_configs.get(task_type, RetryConfig())

    async def should_retry(
        self, task_id: str, task_type: str, error: Exception
    ) -> bool:
        """Determine if a task should be retried"""
        config = self.get_retry_config(task_type)

        # Get retry history
        history = self.retry_history.get(task_id, [])
        retry_count = len(history)

        if retry_count >= config.max_retries:
            logger.info(f"Task {task_id} exceeded max retries ({config.max_retries})")
            return False

        # Analyze error to determine if retryable
        if await self._is_retryable_error(error):
            logger.info(
                f"Task {task_id} will be retried (attempt {retry_count + 1}/{config.max_retries})"
            )
            return True

        logger.info(f"Task {task_id} failed with non-retryable error: {error}")
        return False

    async def calculate_retry_delay(self, task_id: str, task_type: str) -> int:
        """Calculate delay before next retry"""
        config = self.get_retry_config(task_type)
        history = self.retry_history.get(task_id, [])
        retry_count = len(history)

        if config.strategy == RetryStrategy.IMMEDIATE:
            return 0
        elif config.strategy == RetryStrategy.FIXED_DELAY:
            return config.base_delay_seconds
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay_seconds * (retry_count + 1)
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay_seconds * (config.backoff_multiplier**retry_count)
        else:
            delay = config.base_delay_seconds

        return min(delay, config.max_delay_seconds)

    async def record_retry(self, task_id: str, task_type: str, error: Exception):
        """Record a retry attempt"""
        if task_id not in self.retry_history:
            self.retry_history[task_id] = []

        self.retry_history[task_id].append(
            {
                "timestamp": datetime.now().isoformat(),
                "error": str(error),
                "error_type": type(error).__name__,
                "task_type": task_type,
            }
        )

        logger.info(f"Recorded retry for task {task_id}")

    async def _is_retryable_error(self, error: Exception) -> bool:
        """Determine if an error is retryable"""
        # List of retryable error types
        retryable_errors = [
            "ConnectionError",
            "TimeoutError",
            "TemporaryFailure",
            "RateLimitExceeded",
            "ServiceUnavailable",
            "NetworkError",
        ]

        error_type = type(error).__name__
        return error_type in retryable_errors or "timeout" in str(error).lower()


class ClarificationManager:
    """Manages user clarification requests"""

    def __init__(self):
        self.requests: Dict[str, ClarificationRequest] = {}
        self.callbacks: Dict[str, Callable] = {}
        self.request_counter = 0
        self.timeout_tasks: Dict[str, asyncio.Task] = {}

    async def request_clarification(
        self,
        task_id: str,
        clarification_type: ClarificationType,
        message: str,
        options: List[str] = None,
        required_fields: List[str] = None,
        timeout_minutes: int = 30,
        callback: Callable = None,
    ) -> str:
        """Request clarification from user"""
        request_id = f"clarification_{self.request_counter:06d}"
        self.request_counter += 1

        request = ClarificationRequest(
            id=request_id,
            task_id=task_id,
            clarification_type=clarification_type,
            message=message,
            options=options or [],
            required_fields=required_fields or [],
            timeout_minutes=timeout_minutes,
        )

        self.requests[request_id] = request
        if callback:
            self.callbacks[request_id] = callback

        logger.info(f"Created clarification request {request_id} for task {task_id}")

        # Start timeout timer
        task = asyncio.create_task(self._handle_timeout(request_id, timeout_minutes))
        self.timeout_tasks[request_id] = task

        return request_id

    async def provide_clarification(
        self, request_id: str, response: Dict[str, Any]
    ) -> bool:
        """Provide clarification response"""
        if request_id not in self.requests:
            logger.error(f"Clarification request {request_id} not found")
            return False

        request = self.requests[request_id]
        request.response = response
        request.resolved_at = datetime.now()

        logger.info(f"Clarification request {request_id} resolved")

        # Call callback if provided
        if request_id in self.callbacks:
            try:
                await self.callbacks[request_id](response)
            except Exception as e:
                logger.error(f"Error in clarification callback: {e}")

        return True

    async def get_pending_clarifications(self) -> List[Dict[str, Any]]:
        """Get all pending clarification requests"""
        pending = []
        for request in self.requests.values():
            if request.resolved_at is None:
                pending.append(
                    {
                        "id": request.id,
                        "task_id": request.task_id,
                        "type": request.clarification_type.value,
                        "message": request.message,
                        "options": request.options,
                        "required_fields": request.required_fields,
                        "created_at": request.created_at.isoformat(),
                        "timeout_minutes": request.timeout_minutes,
                    }
                )
        return pending

    async def _handle_timeout(self, request_id: str, timeout_minutes: int):
        """Handle clarification request timeout"""
        await asyncio.sleep(timeout_minutes * 60)

        if request_id in self.requests:
            request = self.requests[request_id]
            if request.resolved_at is None:
                logger.warning(f"Clarification request {request_id} timed out")
                # Mark as resolved with timeout
                request.resolved_at = datetime.now()
                request.response = {"timeout": True, "message": "Request timed out"}

    async def analyze_error_for_clarification(
        self, error: Exception, task_context: Dict[str, Any]
    ) -> Optional[ClarificationType]:
        """Analyze error to determine if clarification is needed"""
        error_message = str(error).lower()

        if "missing" in error_message or "required" in error_message:
            return ClarificationType.MISSING_INFORMATION
        elif "ambiguous" in error_message or "unclear" in error_message:
            return ClarificationType.AMBIGUOUS_REQUEST
        elif "conflict" in error_message or "incompatible" in error_message:
            return ClarificationType.CONFLICTING_REQUIREMENTS
        elif (
            "resource" in error_message
            or "memory" in error_message
            or "disk" in error_message
        ):
            return ClarificationType.RESOURCE_CONSTRAINTS
        elif "permission" in error_message or "access" in error_message:
            return ClarificationType.PERMISSION_REQUIRED

        return None

    async def shutdown(self):
        """Shutdown the clarification manager and cancel all timeout tasks"""
        # Cancel all timeout tasks
        for request_id in list(self.requests.keys()):
            if request_id in self.timeout_tasks:
                task = self.timeout_tasks[request_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

        # Clear all data
        self.requests.clear()
        self.callbacks.clear()
        self.timeout_tasks.clear()

        logger.info("ClarificationManager shutdown complete")


# Global instances
retry_manager = RetryManager()
clarification_manager = ClarificationManager()
