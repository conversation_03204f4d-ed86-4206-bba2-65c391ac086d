# Cursor Rules for AI Coding Agent

This directory contains Cursor rules that enforce best practices and specific workflows for the AI Coding Agent project.

## Rules Overview

### 🐳 `docker-priority.mdc`
**Enforces Docker-first workflow for all AI-created websites and services**

- Always generate Dockerfiles when creating or editing project components
- Default to container-based execution (never suggest running code directly on host)
- Use docker-compose for project orchestration
- Use SiteContainerManager for AI-managed websites
- Implement proper health checks, security, and resource limits

### 🌐 `website-containerization.mdc`
**Enforces website containerization using AI Coding Agent's container management system**

- Mandatory use of SiteContainerManager for all website operations
- Dynamic port allocation (8080-9000 range)
- File operations through mounted volumes or container APIs
- Integration with existing AI Coding Agent systems
- Compliance checklist for website operations

## How These Rules Work

### Always Apply Rules
Both rules are set to `alwaysApply: true`, meaning they will be automatically applied to every task in Cursor.

### File Patterns
Rules apply to all files (`globs: ["**/*"]`) to ensure comprehensive coverage.

### Integration with AI Coding Agent
These rules are specifically designed to work with the existing AI Coding Agent infrastructure:

- **SiteContainerManager**: `core/site_container_manager.py`
- **ExternalHostingManager**: `core/external_hosting_manager.py`
- **ContainerMonitor**: `core/container_monitor.py`
- **CLI Commands**: `cli/external_hosting_commands.py`
- **API Routes**: `api/external_hosting_routes.py`
- **Docker Templates**: `containers/Dockerfile.template`

## What This Achieves

### Consistent Docker-First Workflow
- Every website project is automatically containerized
- No host pollution or accidental global package installs
- Consistent local vs production parity

### Integration with AI Systems
- All operations go through the AI Coding Agent's management systems
- Automatic port allocation and health monitoring
- Seamless deployment to external hosting providers

### Security and Isolation
- Container isolation using Docker networks
- Resource limits and monitoring
- Security headers and rate limiting
- Non-root users in containers

### Developer Experience
- Hot reload for development environments
- Clear CLI and API interfaces
- Comprehensive debugging tools
- Local LLM compatibility

## Usage Examples

### Creating a New Website
```python
# ✅ CORRECT: Use SiteContainerManager
container_manager = SiteContainerManager()
result = await container_manager.create_site_container("my-website", config)
```

### Starting a Container
```bash
# ✅ CORRECT: Use container management
ai-cli start-site-container my-website
```

### Accessing the Website
```bash
# ✅ CORRECT: Use assigned port
curl http://localhost:8080/health
```

## Compliance

Before suggesting any website-related operations, verify:
- [ ] Uses SiteContainerManager for container operations
- [ ] Implements proper port allocation
- [ ] Uses volume mounting for file access
- [ ] Includes health check endpoints
- [ ] Integrates with security systems
- [ ] Provides CLI/API interfaces
- [ ] Supports external hosting deployment
- [ ] Follows established Docker patterns
- [ ] Uses the ai-coding-network
- [ ] Implements proper resource limits

## Why These Rules Exist

- **Consistency**: Ensures all websites follow the same containerization patterns
- **Security**: Prevents host pollution and provides proper isolation
- **Scalability**: Enables easy deployment and management of multiple sites
- **Integration**: Works seamlessly with the AI Coding Agent's existing systems
- **Local LLM Compatibility**: All features accessible via CLI and API for local Ollama models
