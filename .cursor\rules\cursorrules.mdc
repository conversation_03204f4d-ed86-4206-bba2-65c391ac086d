---
alwaysApply: true
---
# 🎯 CURSOR AI ASSISTANT RULES - CRITICAL TOP-LEVEL

**CRITICAL**: All AI assistants MUST read and follow these rules before making ANY code changes.

### 1. **100% Test Success Requirement**
- **ALL test runs MUST achieve 100% success rate**
- **NEVER accept, proceed with, or report test results below 100%**
- **Test failures indicate code quality issues that MUST be addressed immediately**

### 2. **100% TODO Completion**
- **ALL TODOs MUST be completed to 100% before ending any work session**
- **<PERSON><PERSON><PERSON> leave TODOs in 'pending' or 'in_progress' status**
- **TODO completion is MANDATORY - not optional**

### 3. **Docker-First Policy**
- **ALWAYS use Docker containers for all website projects**
- **ALWAYS use SiteContainerManager for container operations**
- **NEVER suggest running websites directly on the host system**
- **Follow Compose best practices**: externalize secrets/env via `.env` or `secrets`, avoid inline blobs, mount large data via volumes, split base/override files, and enable log rotation.

### 4. **Static Analysis Compliance**
- **ALL imports must resolve correctly** - no ModuleNotFoundError
- **ALL type annotations must match usage** - no reportArgumentType errors
- **ALL methods must be declared** - no reportAttributeAccessIssue errors
- **ALL dependencies must be in requirements files** - pin exact versions (==)

### 5. **Project Directory Cleanliness**
- **NEVER create duplicate files** - check existing files first
- **ALWAYS delete obsolete files** after consolidation/refactoring
- **ALWAYS remove legacy files** when replacing with new implementations
- **ALWAYS clean up temp files** after use
- **ALWAYS use `git rm`** to properly remove files from version control

### 6. **Virtual Environment Activation**
- **ALWAYS run ALL Python files in the virtual environment** - scripts, modules, tools, tests, etc.
- **NEVER run Python files directly without venv activation**
- **ALWAYS use the project's venv Python** - never use global Python
- **VENV PATH**: `F:\NasShare\AICodingAgent\.venv`

**Quick Commands:**
```bash
# ✅ CORRECT: Windows PowerShell
.\.venv\Scripts\Activate.ps1; python [script.py]

# ✅ CORRECT: Windows Command Prompt
.\.venv\Scripts\activate.bat && python [script.py]

# ✅ CORRECT: UNIX/macOS
source .venv/bin/activate && python [script.py]

# ❌ INCORRECT: Global Python
python [script.py]
```

### 7. **NEVER Ignore Syntax Errors**
- **NEVER ignore, work around, or skip syntax errors** - fix them immediately
- **NEVER proceed with development when syntax errors exist**
- **ALWAYS fix syntax errors before moving on to any other work**
- **ALWAYS test commands before running them** - ensure proper syntax
- **ALWAYS use proper quoting and escaping** in shell commands
- **ALWAYS validate Python code syntax** before execution
- **NEVER use "workarounds" or "simpler tests"** when syntax errors exist
- **Syntax errors are CRITICAL BLOCKERS** - stop everything until fixed

## 🔧 BEFORE ANY CODE CHANGE

✅ **Cursor rules monitoring system verified and running**
✅ **All tests passing 100%**
✅ **No open TODOs**
✅ **Python virtual environment activated** (for .py files)
✅ **Know which files you'll touch**
✅ **Check for existing similar files** (prevent duplicates)

## 🔄 AFTER ANY CODE CHANGE

✅ **Update TODO statuses**
✅ **Re-run tests (100% success required)**
✅ **Delete obsolete files** (use `git rm`)
✅ **Remove temp files** and cleanup artifacts
✅ **Commit with conventional message**
✅ **Push to remote repository**

---

## 📋 DETAILED GUIDELINES

### 🔍 Cursor Rules Monitoring System (CRITICAL)

#### **System Overview**
The Cursor Rules Monitoring System is a **real-time compliance enforcement solution** that ensures ALL cursor rules are followed continuously during development.

#### **Mandatory Verification Sequence**
1. **Activate virtual environment** first
2. **Verify monitoring system is running** using `python scripts/cursor_rules_monitor.py --status`
3. **Check monitoring status** (must show `"is_monitoring": true`)
4. **Check compliance score** (must be above 90%)
5. **If not running, start monitoring** using `python scripts/cursor_rules_monitor.py --strict --daemon`
6. **Only then proceed** with development work

#### **Monitoring System Commands**

**Verify Monitoring (Windows PowerShell):**
```powershell
# ✅ CORRECT: Activate venv and verify monitoring
.\.venv\Scripts\Activate.ps1; python scripts/cursor_rules_monitor.py --status

# ✅ CORRECT: Start monitoring if not running
.\.venv\Scripts\Activate.ps1; python scripts/cursor_rules_monitor.py --strict --daemon

# ✅ CORRECT: Start with custom interval
.\.venv\Scripts\Activate.ps1; python scripts/cursor_rules_monitor.py --interval 30 --strict --daemon
```

#### **Monitoring System Features**
- **Real-time compliance checking** every 30 seconds (configurable)
- **Automatic violation detection** and blocking
- **Compliance score tracking** (0-100%)
- **Historical violation logging** in `logs/cursor_rules_monitor.log`
- **Immediate alerts** for critical violations
- **Continuous enforcement** of all cursor rules
- **Web dashboard** for compliance tracking (optional)

#### **Compliance Score Requirements**
- **90%+**: Excellent compliance - proceed with development
- **80-89%**: Good compliance - address warnings
- **70-79%**: Fair compliance - fix issues before proceeding
- **<70%**: Poor compliance - STOP and fix critical violations

#### **Violation Handling**
- **Critical violations** → **BLOCK** all development work
- **Warnings** → **NOTIFY** but allow continuation
- **Compliance score <90%** → **REQUIRE** fixes before proceeding

#### **Monitoring System Integration**
- **Git hooks**: Automatic monitoring on commit/push
- **CI/CD**: Automated compliance checking
- **IDE integration**: Real-time feedback
- **Dashboard**: Web-based compliance tracking

### 🐳 Docker-First Policy
- **Multi-stage builds** with builder and runtime stages
- **Non-root users** in containers for security
- **HEALTHCHECK directives** for container readiness
- **Resource limits** in docker-compose.yml files
- **Automatic restart policies** (`restart: unless-stopped`)
- **Compose hygiene**: no inline blobs/secrets, use `env_file`/`secrets`, volumes for large data, per-env overrides, log rotation (`json-file` max-size/max-file)

### 🐍 Virtual Environment & Test Invocation (MUST)

#### **Virtual Environment Activation Rules**
- **ALWAYS run ALL Python files in the virtual environment** - scripts, modules, tools, tests, etc.
- **NEVER run Python files directly without venv activation**
- **ALWAYS use the project's venv Python** - never use global Python
- **VENV PATH**: `F:\NasShare\AICodingAgent\.venv`

#### **Platform-Specific Activation Commands**

**Windows PowerShell:**
```powershell
# ✅ CORRECT: Activate venv and run any Python file
.\.venv\Scripts\Activate.ps1; python [script.py]

# ✅ CORRECT: Run tests
.\.venv\Scripts\Activate.ps1; python -m pytest [args]

# ❌ INCORRECT: Global Python
python [script.py]
```

**Windows Command Prompt:**
```cmd
# ✅ CORRECT: Activate venv and run any Python file
.\.venv\Scripts\activate.bat && python [script.py]

# ✅ CORRECT: Run tests
.\.venv\Scripts\activate.bat && python -m pytest [args]

# ❌ INCORRECT: Global Python
python [script.py]
```

#### **Enforcement Rules**
- **NEVER run Python files directly** without venv activation
- **ALWAYS verify venv is activated** before any Python execution
- **ALWAYS use platform-specific** activation commands

### 🧪 Testing & QA
- **Pre-commit hooks**: flake8, mypy, pytest
- **CI enforces test coverage** and 100% success rate
- **Unit tests for all new components** and functions
- **Integration tests for API endpoints**
- **Maintain test coverage above 80%**
- **ALWAYS use venv Python** for all test commands

### 🔍 Static Analysis & Type Safety
- **Dependency Management**: Always update requirements.txt and requirements-dev.txt
- **Type Annotations**: Use Optional[...] or broaden unions if None is ever assigned
- **Method Declarations**: Declare .close(), .get(), .shutdown() methods
- **Enum Handling**: Accept Union[str, EnumType] for parameters
- **Config Arguments**: Consistent pattern (required OR optional, never mix)
- **Import Management**: Handle circular imports gracefully

### 🗂️ File Organization
- **core/**: Main agent logic, maintenance, context, site editing
- **security/**: Security management, SSL, authentication
- **models/**: Model routing, training, model manager
- **monitoring/**: Monitoring agents, performance tracking
- **content/**: Content management, CMS
- **database/**: DB management, migrations, CLI
- **templates/**: Template management and generation
- **utils/**: Utility functions and helpers
- **scripts/**: Executable scripts, server launchers
- **config/**: All configuration files (.json, .ini, .py, requirements)
- **data/**: Data files, datasets, model performance
- **logs/**: Log files
- **tests/**: All test files, test harnesses
- **docs/**: Documentation (.md files)
- **cli/**: CLI commands and interface

### 🗑️ Cleanup & Housekeeping

#### **File Duplication Prevention**
- **ALWAYS search for existing files** before creating new ones
- **Check similar names** - avoid `file_v2.py`, `file_new.py`, `file_updated.py`
- **Use descriptive names** - avoid generic names like `utils.py`, `helper.py`
- **Consolidate similar functionality** - merge duplicate functions/classes
- **Check imports** - ensure no duplicate imports or circular dependencies

#### **Obsolete File Management**
- **Delete source files** after consolidation into unified implementations
- **Remove backup files** - no `*.bak`, `*.backup`, `*_old.py` files
- **Clean up migration artifacts** - remove temporary migration files
- **Delete deprecated code** - remove commented-out or unused code
- **Remove experimental files** - clean up proof-of-concept code

#### **Legacy File Cleanup**
- **Remove old implementations** when replacing with new ones
- **Delete version-specific files** - no `v1_`, `v2_` prefixes
- **Clean up deprecated features** - remove unused legacy code
- **Remove compatibility layers** - delete temporary compatibility code
- **Archive historical files** - move to `docs/archive/` if needed for reference

#### **Temp File Management**
- **Clean up temp files** immediately after use
- **Remove test artifacts** - delete `*.tmp`, `*.temp`, `test_*` files
- **Clean build artifacts** - remove `__pycache__/`, `*.pyc`, `*.pyo` files
- **Delete log files** - clean up `*.log`, `*.out` files
- **Remove download artifacts** - delete downloaded files after processing


### 🌐 Version Control
- **Conventional commits**: `type(scope): description`
- **Feature branches**: `feature/phase-X.X`
- **Remote push after success**: Always push to GitHub
- **Pull requests** for code review

### 🤖 AI Model Requirements
- **Local Ollama models ONLY** - never use cloud models
- **Approved models**: deepseek-coder:1.3b, yi-coder:1.5b, qwen2.5-coder:3b, starcoder2:3b, mistral:7b-instruct-q4_0

### 🖥️ CLI & API Integration
- **ALWAYS create CLI commands** for every new feature
- **ALWAYS create API routes** for every new feature
- **Local Ollama LLM integration** - ensure features are LLM-accessible
- **Test CLI and API** - verify both interfaces work correctly

---

## 🚨 ENFORCEMENT PROTOCOL

### If Monitoring System Not Running:
1. **IMMEDIATELY STOP** all development work
2. **VERIFY monitoring status** using `python scripts/cursor_rules_monitor.py --status`
3. **If not running, START monitoring** using `python scripts/cursor_rules_monitor.py --strict --daemon`
4. **VERIFY monitoring is active** - check for `"is_monitoring": true`
5. **CHECK compliance score** - must be above 90% to proceed
6. **ONLY continue** after monitoring is confirmed active and compliant

### If Monitoring System Fails to Start:
1. **IMMEDIATELY STOP** all development work
2. **CHECK virtual environment** - ensure it's activated
3. **VERIFY dependencies** - ensure all required packages are installed
4. **CHECK file permissions** - ensure scripts are executable
5. **FIX the issue** before proceeding with any development work
6. **RESTART monitoring system** and verify it's working
7. **ONLY continue** after monitoring is successfully running

### If Compliance Score < 90%:
1. **IMMEDIATELY STOP** all development work
2. **REVIEW violations** - check monitoring system output
3. **FIX critical violations** first - these block all progress
4. **ADDRESS warnings** - improve compliance score
5. **RE-RUN monitoring** - verify score is above 90%
6. **ONLY proceed** after achieving 90%+ compliance score

### If Tests Fail (< 100% Success):
1. **STOP all development work**
2. **Analyze test failures** - identify root causes
3. **Fix ALL issues** before re-running tests
4. **Only proceed** after achieving 100% test success

### If TODOs Are Incomplete:
1. **IMMEDIATELY STOP** - do not proceed with any other work
2. **IDENTIFY BLOCKERS** - what is preventing completion?
3. **EXECUTE COMPLETION** - finish the TODO completely
4. **VERIFY COMPLETION** - test that it actually works

### If Static Analysis Errors:
1. **FIX all imports** - ensure packages are in requirements files
2. **UPDATE type annotations** - match actual usage
3. **DECLARE missing methods** - add .get(), .close(), etc.
4. **HANDLE enums properly** - use Union[str, EnumType]
5. **VERIFY config arguments** - consistent pattern throughout

### If Duplicate Files Found:
1. **IMMEDIATELY identify** which files are duplicates
2. **CONSOLIDATE functionality** - merge into single implementation
3. **UPDATE all references** - fix imports and dependencies
4. **DELETE duplicate files** - use `git rm` for proper removal
5. **TEST thoroughly** - ensure consolidation doesn't break anything

### If Obsolete Files Found:
1. **IDENTIFY obsolete files** - check for unused or deprecated code
2. **VERIFY obsolescence** - ensure files are truly not needed
3. **UPDATE dependencies** - fix any remaining references
4. **DELETE obsolete files** - use `git rm` for proper removal
5. **DOCUMENT deletion** - explain why files were removed

### If Virtual Environment Not Activated:
1. **IMMEDIATELY activate venv** - use platform-specific activation command
2. **VERIFY activation** - check that correct Python is being used
3. **RE-RUN commands** - ensure all Python commands use venv
4. **NEVER run Python files directly** without venv activation

---

## 📚 REFERENCE DOCUMENTS

- **Detailed Testing Guidelines**: `docs/TESTING_GUIDELINES.md`
- Docker Configuration Best Practices: `docs/docker_guidelines.md`
