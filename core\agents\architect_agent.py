#!/usr/bin/env python3
"""
ArchitectAgent - High-level task orchestrator for AI Coding Agent

Responsible for:
1. Parsing and categorizing high-level user instructions
2. Breaking down tasks into sub-tasks
3. Delegating to specialized agents
4. Tracking progress and status
5. Reporting back to user

This agent serves as the main entry point for natural language commands
and coordinates all other specialized agents in the system.
"""

import asyncio
import json
import logging
import time  # Added for time.time()
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from core.agents.agent_load_balancer import AgentLoadBalancer
from core.agents.agent_performance_tracker import AgentPerformanceTracker
from core.agents.backend_agent import BackendAgent

# Import collaboration system
from core.agents.collaboration_manager import CollaborationManager, CollaborationPattern
from core.agents.collaboration_patterns import CollaborationPatternLibrary
from core.agents.container_agent import ContainerAgent
from core.agents.context_analyzer import <PERSON>text<PERSON><PERSON><PERSON><PERSON>, TaskAnalysis

# Import enhanced base agent
from core.agents.enhanced_base_agent import EnhancedBaseAgent
from core.agents.enhanced_base_agent import TaskStatus as EnhancedTaskStatus
from core.agents.enhanced_base_agent import VerificationLevel

# Import specialized agents
from core.agents.frontend_agent import FrontendAgent
from core.agents.learning_agent import LearningAgent
from core.agents.shared_context import SharedContextManager
from core.agents.shell_ops_agent import ShellOpsAgent

# Import smart routing system
from core.agents.smart_task_router import RoutingDecision, SmartTaskRouter, TaskContext
from core.agents.task_distribution import TaskDistribution
from core.error_watcher_agent import ErrorWatcherAgent
from core.file_watcher_agent import FileWatcherAgent
from core.learning_integration import LearningIntegration, TaskOutcome
from core.retry_manager import ClarificationType, clarification_manager, retry_manager
from core.task_persistence import task_persistence
from core.task_queue import TaskPriority as QueueTaskPriority
from core.task_queue import task_queue_manager
from monitoring.monitoring_agent import MonitoringAgent
from security.llm_security_agent import LLMSecurityAgent as SecurityAgent
from utils.intent_classifier import IntentClassifier
from utils.progress_tracker import ProgressTracker

# Import utilities
from utils.task_parser import TaskParser

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task status enumeration"""

    PENDING = "pending"
    WAITING_DEPENDENCIES = "waiting_dependencies"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority enumeration"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SubTask:
    """Represents a sub-task within a larger project"""

    id: str
    name: str
    description: str
    agent_type: str
    priority: TaskPriority
    status: TaskStatus
    dependencies: List[str]
    estimated_duration: int  # minutes
    actual_duration: Optional[int] = None
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class ProjectTask:
    """Represents a complete project task"""

    id: str
    name: str
    description: str
    user_command: str
    status: TaskStatus
    priority: TaskPriority
    sub_tasks: List[SubTask]
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_estimated_duration: int = 0
    total_actual_duration: Optional[int] = None
    progress_percentage: float = 0.0
    result_summary: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class ArchitectAgent(EnhancedBaseAgent):
    """
    High-level task orchestrator that interprets user commands and delegates to specialized agents.

    This agent serves as the main entry point for natural language commands and coordinates
    all other specialized agents in the system.
    """

    def __init__(self, config_path: str = "config/smart_routing_config.json"):
        """Initialize the ArchitectAgent with configuration and specialized agents"""

        # Load default configuration for enhanced base agent
        default_config = {
            "memory_tracking": {
                "enabled": True,
                "max_attempts_per_task": 3,
                "cooldown_seconds": 300,
                "verify_tasks": True,
                "reset_on_success": True,
                "verification_level": "comprehensive",
                "persistence": True,
                "cleanup_interval": 86400,
            },
            "model_settings": {
                "model_name": "deepseek-coder:1.3b",
                "system_prompt": "You are an AI coding architect that coordinates multiple specialized agents.",
            },
            "task_routing": {
                "enabled": True,
                "smart_routing": True,
                "load_balancing": True,
                "performance_tracking": True,
            },
            "collaboration": {
                "enabled": True,
                "patterns": True,
                "shared_context": True,
            },
        }

        # Initialize enhanced base agent
        super().__init__(config_path, default_config)

        self.task_parser = TaskParser()
        self.intent_classifier = IntentClassifier()
        self.progress_tracker = ProgressTracker()

        # Initialize all agents
        self.frontend_agent = FrontendAgent()
        self.backend_agent = BackendAgent()
        self.container_agent = ContainerAgent()
        self.learning_agent = LearningAgent()
        self.shell_ops_agent = ShellOpsAgent()

        # Load security agent config from file
        security_config = self._load_security_config(
            "config/security_agent_config.json"
        )
        self.security_agent = SecurityAgent(security_config)

        self.monitoring_agent = MonitoringAgent()

        # Initialize new monitoring agents
        self.file_watcher_agent = FileWatcherAgent()
        self.error_watcher_agent = ErrorWatcherAgent()

        # Agent registry for easy access
        self.agents = {
            "frontend": self.frontend_agent,
            "backend": self.backend_agent,
            "container": self.container_agent,
            "learning": self.learning_agent,
            "shell_ops": self.shell_ops_agent,
            "security": self.security_agent,
            "monitoring": self.monitoring_agent,
            "file_watcher": self.file_watcher_agent,
            "error_watcher": self.error_watcher_agent,
        }

        # Task management
        self.active_tasks: Dict[str, ProjectTask] = {}
        self.completed_tasks: Dict[str, ProjectTask] = {}
        self.failed_tasks: Dict[str, ProjectTask] = {}

        # Initialize collaboration system
        self._initialize_collaboration_system()

        # Initialize smart routing system
        self._initialize_smart_routing_system()

        # Setup retry configurations
        self._setup_retry_configs()

        logger.info(
            "ArchitectAgent initialized successfully with enhanced memory tracking"
        )

    def _initialize_collaboration_system(self):
        """Initialize the collaboration system components."""
        try:
            # Load collaboration configuration
            collab_config_path = "config/collaboration_config.json"
            with open(collab_config_path, "r") as f:
                self.collaboration_config = json.load(f)
        except FileNotFoundError:
            logger.warning("Collaboration config not found, using defaults")
            self.collaboration_config = self._get_default_collaboration_config()
        except Exception as e:
            logger.error(f"Error loading collaboration config: {e}")
            self.collaboration_config = self._get_default_collaboration_config()

        # Initialize collaboration manager
        max_workers = self.collaboration_config.get("collaboration_manager", {}).get(
            "max_workers", 10
        )
        self.collaboration_manager = CollaborationManager(max_workers=max_workers)

        # Initialize shared context manager
        base_path = self.collaboration_config.get("shared_context", {}).get(
            "base_persistence_path", "data/shared_contexts"
        )
        self.shared_context_manager = SharedContextManager(
            base_persistence_path=base_path
        )

        # Initialize collaboration pattern library
        self.pattern_library = CollaborationPatternLibrary()

        # Register all agents with collaboration manager
        for agent_id, agent in self.agents.items():
            self.collaboration_manager.register_agent(agent_id, agent)

        logger.info("Collaboration system initialized successfully")

    def _initialize_smart_routing_system(self):
        """Initialize the smart routing system components."""
        try:
            # Initialize smart routing components
            self.smart_router = SmartTaskRouter()
            self.performance_tracker = AgentPerformanceTracker()
            self.context_analyzer = ContextAnalyzer()
            self.load_balancer = AgentLoadBalancer()
            self.task_distribution = TaskDistribution()

            # Register all agents with smart routing system (will be done asynchronously when needed)
            self._smart_routing_initialized = True
            logger.info("Smart routing system initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize smart routing system: {e}")
            # Continue without smart routing if initialization fails
            self.smart_router = None
            self.performance_tracker = None
            self.context_analyzer = None
            self.load_balancer = None
            self.task_distribution = None
            self._smart_routing_initialized = False

    async def _ensure_smart_routing_initialized(self):
        """Ensure smart routing system is fully initialized."""
        if (
            not hasattr(self, "_smart_routing_initialized")
            or not self._smart_routing_initialized
        ):
            return

        if not hasattr(self, "_agents_registered_with_smart_routing"):
            try:
                # Register all agents with smart routing system
                for agent_id, agent in self.agents.items():
                    # Get agent capabilities from config
                    agent_capabilities = self._get_agent_capabilities(agent_id)

                    # Register with smart router
                    if self.smart_router:
                        await self.smart_router.register_agent(
                            agent_id=agent_id,
                            agent_type=agent_id,
                            capabilities=agent_capabilities.get("capabilities", []),
                            specializations=agent_capabilities.get(
                                "specializations", []
                            ),
                        )

                    # Register with performance tracker
                    if self.performance_tracker:
                        await self.performance_tracker.register_agent(
                            agent_id, agent_id
                        )

                    # Register with load balancer
                    if self.load_balancer:
                        await self.load_balancer.register_agent(
                            agent_id=agent_id,
                            agent_type=agent_id,
                            max_capacity=agent_capabilities.get("max_capacity", 10),
                        )

                    # Register with task distribution
                    if self.task_distribution:
                        await self.task_distribution.register_agent(
                            agent_id=agent_id,
                            agent_type=agent_id,
                            capabilities=agent_capabilities.get("capabilities", []),
                            specializations=agent_capabilities.get(
                                "specializations", []
                            ),
                            max_capacity=agent_capabilities.get("max_capacity", 10),
                        )

                self._agents_registered_with_smart_routing = True
                logger.info("All agents registered with smart routing system")

            except Exception as e:
                logger.error(
                    f"Failed to register agents with smart routing system: {e}"
                )
                self._agents_registered_with_smart_routing = False

    def _get_agent_capabilities(self, agent_id: str) -> Dict[str, Any]:
        """Get agent capabilities from configuration."""
        try:
            # Load smart routing config
            config_path = "config/smart_routing_config.json"
            with open(config_path, "r") as f:
                config = json.load(f)

            agent_capabilities = config.get("agent_capabilities", {})
            return agent_capabilities.get(
                agent_id,
                {
                    "capabilities": [],
                    "specializations": [],
                    "max_capacity": 10,
                    "priority_weight": 1.0,
                },
            )

        except Exception as e:
            logger.error(f"Failed to load agent capabilities for {agent_id}: {e}")
            return {
                "capabilities": [],
                "specializations": [],
                "max_capacity": 10,
                "priority_weight": 1.0,
            }

    def _get_default_collaboration_config(self) -> Dict[str, Any]:
        """Get default collaboration configuration."""
        return {
            "collaboration_manager": {
                "max_workers": 10,
                "default_timeout_seconds": 300,
                "max_parallel_tasks": 5,
            },
            "shared_context": {
                "base_persistence_path": "data/shared_contexts",
                "default_lock_timeout": 30.0,
            },
            "patterns": {"auto_load_custom_patterns": True},
        }

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(
                f"Configuration file {config_path} not found, using defaults"
            )
            return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self._get_default_config()

    def _load_security_config(self, config_path: str) -> Dict[str, Any]:
        """Load security agent configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded security agent configuration from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(
                f"Security agent configuration file {config_path} not found, using default empty config"
            )
            return {}
        except Exception as e:
            logger.error(f"Error loading security agent configuration: {e}")
            return {}

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "max_concurrent_tasks": 5,
            "task_timeout_minutes": 60,
            "enable_parallel_execution": True,
            "enable_progress_tracking": True,
            "enable_error_recovery": True,
            "default_priority": "medium",
            "agents": {
                "frontend": {"enabled": True, "timeout": 30},
                "backend": {"enabled": True, "timeout": 45},
                "container": {"enabled": True, "timeout": 20},
                "learning": {"enabled": True, "timeout": 60},
                "shell_ops": {"enabled": True, "timeout": 15},
                "security": {"enabled": True, "timeout": 25},
                "monitoring": {"enabled": True, "timeout": 20},
            },
        }

    def _setup_retry_configs(self):
        """Set up retry configurations for different task types"""
        from core.retry_manager import RetryConfig, RetryStrategy

        # Frontend tasks - quick retries for UI issues
        retry_manager.set_retry_config(
            "frontend",
            RetryConfig(
                max_retries=2,
                strategy=RetryStrategy.LINEAR_BACKOFF,
                base_delay_seconds=10,
            ),
        )

        # Backend tasks - more retries for complex operations
        retry_manager.set_retry_config(
            "backend",
            RetryConfig(
                max_retries=3,
                strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
                base_delay_seconds=30,
            ),
        )

        # Container tasks - immediate retries for deployment issues
        retry_manager.set_retry_config(
            "container",
            RetryConfig(
                max_retries=2, strategy=RetryStrategy.IMMEDIATE, base_delay_seconds=5
            ),
        )

        # Security tasks - careful retries
        retry_manager.set_retry_config(
            "security",
            RetryConfig(
                max_retries=1, strategy=RetryStrategy.FIXED_DELAY, base_delay_seconds=60
            ),
        )

        # Monitoring tasks - standard retries
        retry_manager.set_retry_config(
            "monitoring",
            RetryConfig(
                max_retries=2,
                strategy=RetryStrategy.LINEAR_BACKOFF,
                base_delay_seconds=15,
            ),
        )

    async def process_user_command(
        self, user_command: str, priority: TaskPriority = None
    ) -> Dict[str, Any]:
        """
        Process a high-level user command and create a project task.

        Args:
            user_command: Natural language command from user
            priority: Task priority (optional)

        Returns:
            Dictionary with task information and status
        """
        try:
            # Validate user command
            if not user_command or not user_command.strip():
                return {
                    "success": False,
                    "error": "User command cannot be empty",
                    "message": "Please provide a valid command",
                }

            logger.info(f"Processing user command: {user_command}")

            # Generate task ID
            task_id = f"task_{self.task_counter:06d}"
            self.task_counter += 1

            # Parse and classify the command
            intent = self.intent_classifier.classify_intent(user_command)
            parsed_tasks = self.task_parser.parse_command(user_command)

            # Create project task
            project_task = ProjectTask(
                id=task_id,
                name=parsed_tasks.get("project_name", f"Project {task_id}"),
                description=parsed_tasks.get("description", user_command),
                user_command=user_command,
                status=TaskStatus.PENDING,
                priority=priority
                or TaskPriority(self.config.get("default_priority", "medium")),
                sub_tasks=[],
                created_at=datetime.now(),
            )

            # Create sub-tasks based on parsed requirements
            sub_tasks = await self._create_sub_tasks(parsed_tasks, task_id)
            project_task.sub_tasks = sub_tasks
            logger.info(f"Created {len(sub_tasks)} sub-tasks for project {task_id}")
            for sub_task in sub_tasks:
                logger.info(
                    f"  Sub-task: {sub_task.id} ({sub_task.agent_type}) - Dependencies: {sub_task.dependencies}"
                )

            # Calculate total estimated duration
            project_task.total_estimated_duration = sum(
                task.estimated_duration for task in sub_tasks
            )

            # Store the task
            self.active_tasks[task_id] = project_task

            # Persist task data
            task_data = {
                "id": project_task.id,
                "name": project_task.name,
                "status": project_task.status.value,
                "priority": project_task.priority.value,
                "created_at": project_task.created_at.isoformat(),
                "sub_tasks_count": len(sub_tasks),
                "estimated_duration": project_task.total_estimated_duration,
            }
            task_persistence.store_task(task_id, task_data)

            # Start execution if not at capacity
            if len(self.active_tasks) <= self.config.get("max_concurrent_tasks", 5):
                logger.info(f"Starting execution of task {task_id}")

                # Execute task asynchronously so it can be cancelled
                async def execute_with_error_handling():
                    try:
                        await self._execute_project_task(task_id)
                    except Exception as e:
                        logger.error(f"Error executing task {task_id}: {e}")
                        # Mark task as failed but keep it in active_tasks for debugging
                        if task_id in self.active_tasks:
                            self.active_tasks[task_id].status = TaskStatus.FAILED
                            self.active_tasks[task_id].result_summary = {
                                "error": str(e),
                                "message": "Task execution failed",
                            }

                # Create and store the task for proper cleanup
                execution_task = asyncio.create_task(execute_with_error_handling())
                project_task.metadata = project_task.metadata or {}
                project_task.metadata["execution_task"] = execution_task

            return {
                "success": True,
                "task_id": task_id,
                "project_name": project_task.name,
                "status": project_task.status.value,
                "priority": project_task.priority.value,
                "sub_tasks_count": len(sub_tasks),
                "estimated_duration": project_task.total_estimated_duration,
                "message": f"Project task '{project_task.name}' created and queued for execution",
            }

        except Exception as e:
            logger.error(f"Error processing user command: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to process user command",
            }

    async def _create_sub_tasks(
        self, parsed_tasks: Dict[str, Any], project_id: str
    ) -> List[SubTask]:
        """Create sub-tasks based on parsed requirements"""
        sub_tasks = []
        frontend_task_id = None
        backend_task_id = None
        container_task_id = None

        # Frontend tasks
        if parsed_tasks.get("frontend_requirements"):
            frontend_task_id = f"{project_id}_frontend_00"
            sub_tasks.append(
                SubTask(
                    id=frontend_task_id,
                    name="Frontend Development",
                    description=f"Create frontend components: {parsed_tasks['frontend_requirements']}",
                    agent_type="frontend",
                    priority=TaskPriority.HIGH,
                    status=TaskStatus.PENDING,
                    dependencies=[],
                    estimated_duration=parsed_tasks.get(
                        "frontend_estimated_duration", 30
                    ),
                )
            )

        # Backend tasks
        if parsed_tasks.get("backend_requirements"):
            backend_task_id = f"{project_id}_backend_01"
            sub_tasks.append(
                SubTask(
                    id=backend_task_id,
                    name="Backend Development",
                    description=f"Create backend services: {parsed_tasks['backend_requirements']}",
                    agent_type="backend",
                    priority=TaskPriority.HIGH,
                    status=TaskStatus.PENDING,
                    dependencies=[],
                    estimated_duration=parsed_tasks.get(
                        "backend_estimated_duration", 45
                    ),
                )
            )

        # Container tasks
        if parsed_tasks.get("containerization_required", True):
            container_task_id = f"{project_id}_container_02"
            container_dependencies = []
            if frontend_task_id:
                container_dependencies.append(frontend_task_id)
            if backend_task_id:
                container_dependencies.append(backend_task_id)

            sub_tasks.append(
                SubTask(
                    id=container_task_id,
                    name="Container Setup",
                    description="Create Docker containers and orchestration",
                    agent_type="container",
                    priority=TaskPriority.MEDIUM,
                    status=TaskStatus.PENDING,
                    dependencies=container_dependencies,
                    estimated_duration=20,
                )
            )

        # Security tasks
        if parsed_tasks.get("security_requirements"):
            security_dependencies = []
            if backend_task_id:
                security_dependencies.append(backend_task_id)

            sub_tasks.append(
                SubTask(
                    id=f"{project_id}_security_03",
                    name="Security Implementation",
                    description=f"Implement security measures: {parsed_tasks['security_requirements']}",
                    agent_type="security",
                    priority=TaskPriority.HIGH,
                    status=TaskStatus.PENDING,
                    dependencies=security_dependencies,
                    estimated_duration=25,
                )
            )

        # Monitoring tasks
        if parsed_tasks.get("monitoring_required", True):
            monitoring_dependencies = []
            if container_task_id:
                monitoring_dependencies.append(container_task_id)

            sub_tasks.append(
                SubTask(
                    id=f"{project_id}_monitoring_04",
                    name="Monitoring Setup",
                    description="Setup monitoring and logging",
                    agent_type="monitoring",
                    priority=TaskPriority.LOW,
                    status=TaskStatus.PENDING,
                    dependencies=monitoring_dependencies,
                    estimated_duration=20,
                )
            )

        # Learning tasks (if applicable)
        if parsed_tasks.get("learning_requirements"):
            sub_tasks.append(
                SubTask(
                    id=f"{project_id}_learning_05",
                    name="Learning Integration",
                    description=f"Integrate learning capabilities: {parsed_tasks['learning_requirements']}",
                    agent_type="learning",
                    priority=TaskPriority.MEDIUM,
                    status=TaskStatus.PENDING,
                    dependencies=[],
                    estimated_duration=60,
                )
            )

        return sub_tasks

    async def _execute_project_task(self, task_id: str):
        """Execute a complete project task"""
        logger.info(f"Executing project task {task_id}")
        project_task = self.active_tasks.get(task_id)
        if not project_task:
            logger.error(f"Project task {task_id} not found")
            return

        try:
            logger.info(f"Starting execution of project task: {project_task.name}")
            project_task.status = TaskStatus.IN_PROGRESS
            project_task.started_at = datetime.now()

            # Execute sub-tasks based on dependencies
            completed_tasks = []
            failed_tasks = []

            while len(completed_tasks) + len(failed_tasks) < len(
                project_task.sub_tasks
            ):
                # Find tasks that can be executed (dependencies satisfied)
                executable_tasks = [
                    task
                    for task in project_task.sub_tasks
                    if task.id not in [t.id for t in completed_tasks + failed_tasks]
                    and all(
                        dep in [t.id for t in completed_tasks]
                        for dep in task.dependencies
                    )
                ]

                if not executable_tasks:
                    # Check for circular dependencies or stuck tasks
                    remaining_tasks = [
                        task
                        for task in project_task.sub_tasks
                        if task.id not in [t.id for t in completed_tasks + failed_tasks]
                    ]
                    if remaining_tasks:
                        # Check if dependencies are actually missing or if tasks are truly stuck
                        missing_dependencies = []
                        for task in remaining_tasks:
                            missing_deps = [
                                dep
                                for dep in task.dependencies
                                if dep not in [t.id for t in completed_tasks]
                            ]
                            if missing_deps:
                                missing_dependencies.extend(missing_deps)

                        if missing_dependencies:
                            logger.warning(
                                f"Tasks stuck due to missing dependencies: {list(set(missing_dependencies))}"
                            )
                            logger.warning(
                                f"Stuck tasks: {[t.id for t in remaining_tasks]}"
                            )

                            # Check if missing dependencies are actually created tasks
                            all_task_ids = [t.id for t in project_task.sub_tasks]
                            truly_missing = [
                                dep
                                for dep in missing_dependencies
                                if dep not in all_task_ids
                            ]

                            if truly_missing:
                                logger.error(
                                    f"Critical: Dependencies {truly_missing} were never created"
                                )
                                # Mark remaining tasks as failed
                                for task in remaining_tasks:
                                    task.status = TaskStatus.FAILED
                                    task.error = f"Dependency resolution failed - missing: {truly_missing}"
                                    failed_tasks.append(task)
                            else:
                                # Dependencies exist but haven't completed yet - this is normal
                                logger.info(
                                    f"Tasks waiting for dependencies to complete: {[t.id for t in remaining_tasks]}"
                                )
                                # Mark tasks as waiting for dependencies
                                for task in remaining_tasks:
                                    if task.status == TaskStatus.PENDING:
                                        task.status = TaskStatus.WAITING_DEPENDENCIES
                                # Don't mark as failed, just wait
                                break
                        else:
                            # No missing dependencies but tasks still stuck - likely circular dependency
                            logger.error(
                                f"Circular dependency detected in tasks: {[t.id for t in remaining_tasks]}"
                            )
                            for task in remaining_tasks:
                                task.status = TaskStatus.FAILED
                                task.error = "Circular dependency detected"
                                failed_tasks.append(task)
                    break

                # Execute tasks in parallel if enabled
                if self.config.get("enable_parallel_execution", True):
                    execution_tasks = [
                        self._execute_sub_task(task) for task in executable_tasks
                    ]
                    results = await asyncio.gather(
                        *execution_tasks, return_exceptions=True
                    )

                    for task, result in zip(executable_tasks, results):
                        if isinstance(result, Exception):
                            task.status = TaskStatus.FAILED
                            task.error = str(result)
                            failed_tasks.append(task)
                        else:
                            task.status = TaskStatus.COMPLETED
                            task.result = result
                            task.completed_at = datetime.now()
                            completed_tasks.append(task)
                else:
                    # Execute tasks sequentially
                    for task in executable_tasks:
                        try:
                            result = await self._execute_sub_task(task)
                            task.status = TaskStatus.COMPLETED
                            task.result = result
                            task.completed_at = datetime.now()
                            completed_tasks.append(task)
                        except Exception as e:
                            task.status = TaskStatus.FAILED
                            task.error = str(e)
                            failed_tasks.append(task)

                # Update progress
                project_task.progress_percentage = (
                    len(completed_tasks) / len(project_task.sub_tasks) * 100
                )

            # Finalize project task
            waiting_tasks = [
                task
                for task in project_task.sub_tasks
                if task.status == TaskStatus.WAITING_DEPENDENCIES
            ]

            if failed_tasks:
                project_task.status = TaskStatus.FAILED
                project_task.result_summary = {
                    "completed": len(completed_tasks),
                    "failed": len(failed_tasks),
                    "waiting": len(waiting_tasks),
                    "failed_tasks": [task.id for task in failed_tasks],
                    "waiting_tasks": [task.id for task in waiting_tasks],
                    "errors": [task.error for task in failed_tasks if task.error],
                }
            elif waiting_tasks:
                project_task.status = TaskStatus.WAITING_DEPENDENCIES
                project_task.result_summary = {
                    "completed": len(completed_tasks),
                    "failed": 0,
                    "waiting": len(waiting_tasks),
                    "waiting_tasks": [task.id for task in waiting_tasks],
                    "message": "Project is waiting for dependencies to complete",
                }
            else:
                project_task.status = TaskStatus.COMPLETED
                project_task.result_summary = {
                    "completed": len(completed_tasks),
                    "failed": 0,
                    "waiting": 0,
                    "all_tasks_successful": True,
                }

            project_task.completed_at = datetime.now()
            project_task.total_actual_duration = (
                project_task.completed_at - project_task.started_at
            ).total_seconds() / 60

            # Move to completed tasks only if not cancelled
            if project_task.status != TaskStatus.CANCELLED:
                self.completed_tasks[task_id] = project_task
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]

            logger.info(
                f"Project task {task_id} completed with status: {project_task.status.value}"
            )

        except Exception as e:
            logger.error(f"Error executing project task {task_id}: {e}")
            project_task.status = TaskStatus.FAILED
            project_task.result_summary = {
                "error": str(e),
                "message": "Project task execution failed",
            }
            project_task.completed_at = datetime.now()

            # Move to completed tasks even if failed
            self.completed_tasks[task_id] = project_task
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

    async def _execute_sub_task(self, sub_task: SubTask) -> Dict[str, Any]:
        """Execute a single sub-task using the appropriate specialized agent with retry and clarification logic"""
        start_time = datetime.now()

        try:
            logger.info(f"Executing sub-task: {sub_task.name} ({sub_task.agent_type})")
            sub_task.status = TaskStatus.IN_PROGRESS
            sub_task.started_at = start_time

            # Get the appropriate agent
            agent = await self._get_agent_for_task(
                sub_task.agent_type, {"task_id": sub_task.id}
            )
            if not agent:
                raise ValueError(f"No agent available for type: {sub_task.agent_type}")

            # Execute the task with retry logic
            result = await self._execute_with_retry_and_clarification(sub_task, agent)

            # Record task outcome for learning
            await self._record_task_outcome(sub_task, result, start_time, True)

            return result

        except Exception as e:
            logger.error(f"Error executing sub-task {sub_task.id}: {e}")

            # Record task outcome for learning
            await self._record_task_outcome(
                sub_task,
                {"error": str(e)},
                start_time,
                False,
                error_type=type(e).__name__,
                error_message=str(e),
            )

            raise

    async def _execute_with_retry_and_clarification(
        self, sub_task: SubTask, agent
    ) -> Dict[str, Any]:
        """Execute task with retry logic and clarification requests"""
        timeout = (
            self.config.get("agents", {})
            .get(sub_task.agent_type, {})
            .get("timeout", 30)
        )

        while True:
            try:
                # Execute the task
                result = await asyncio.wait_for(
                    agent.execute_task(sub_task.description, sub_task.id),
                    timeout=timeout * 60,  # Convert to seconds
                )

                # Check if result indicates need for clarification
                if result.get("needs_clarification"):
                    clarification_result = await self._handle_clarification_request(
                        sub_task, result
                    )
                    if clarification_result:
                        # Retry with clarification
                        continue
                    else:
                        # User didn't provide clarification, fail the task
                        raise Exception("Task failed due to missing clarification")

                return result

            except asyncio.TimeoutError:
                error = Exception(f"Task execution timed out after {timeout} minutes")
            except Exception as e:
                error = e

            # Check if we should retry
            if await retry_manager.should_retry(
                sub_task.id, sub_task.agent_type, error
            ):
                await retry_manager.record_retry(
                    sub_task.id, sub_task.agent_type, error
                )

                # Calculate retry delay
                delay_seconds = await retry_manager.calculate_retry_delay(
                    sub_task.id, sub_task.agent_type
                )

                if delay_seconds > 0:
                    logger.info(
                        f"Retrying task {sub_task.id} in {delay_seconds} seconds"
                    )
                    await asyncio.sleep(delay_seconds)

                continue
            else:
                # No more retries, raise the error
                raise error

    async def _handle_clarification_request(
        self, sub_task: SubTask, result: Dict[str, Any]
    ) -> bool:
        """Handle clarification request from agent"""
        clarification_type = ClarificationType.MISSING_INFORMATION

        # Determine clarification type from result
        if "ambiguous" in result.get("message", "").lower():
            clarification_type = ClarificationType.AMBIGUOUS_REQUEST
        elif "conflict" in result.get("message", "").lower():
            clarification_type = ClarificationType.CONFLICTING_REQUIREMENTS
        elif "resource" in result.get("message", "").lower():
            clarification_type = ClarificationType.RESOURCE_CONSTRAINTS
        elif "permission" in result.get("message", "").lower():
            clarification_type = ClarificationType.PERMISSION_REQUIRED

        # Create clarification request
        request_id = await clarification_manager.request_clarification(
            task_id=sub_task.id,
            clarification_type=clarification_type,
            message=result.get("message", "Additional information needed"),
            options=result.get("options", []),
            required_fields=result.get("required_fields", []),
            timeout_minutes=30,
        )

        logger.info(
            f"Created clarification request {request_id} for task {sub_task.id}"
        )

        # For now, return False (no clarification provided)
        # In a real implementation, this would wait for user input
        return False

    async def _record_task_outcome(
        self,
        sub_task: SubTask,
        result: Dict[str, Any],
        start_time: datetime,
        success: bool,
        error_type: str = None,
        error_message: str = None,
    ):
        """Record task outcome for learning"""
        try:
            duration_minutes = (datetime.now() - start_time).total_seconds() / 60

            task_outcome = TaskOutcome(
                task_id=sub_task.id,
                task_type=sub_task.agent_type,
                success=success,
                duration_minutes=duration_minutes,
                error_type=error_type,
                error_message=error_message,
                agent_used=sub_task.agent_type,
                completion_time=datetime.now(),
            )

            await self.learning_integration.record_task_outcome(task_outcome)

        except Exception as e:
            logger.error(f"Error recording task outcome: {e}")

    async def _get_agent_for_task(
        self, agent_type: str, task_context: Dict[str, Any] = None
    ) -> Any:
        """Get the appropriate agent for a given task type using smart routing if available"""
        try:
            # Ensure smart routing is initialized
            await self._ensure_smart_routing_initialized()

            # Use smart routing if available
            if (
                self.smart_router
                and self._smart_routing_initialized
                and hasattr(self, "_agents_registered_with_smart_routing")
                and self._agents_registered_with_smart_routing
            ):

                # Create task context for smart routing
                task_context_obj = TaskContext(
                    task_id=task_context.get("task_id", f"task_{int(time.time())}"),
                    task_type=agent_type,
                    complexity=task_context.get("complexity", "moderate"),
                    requirements=task_context.get("requirements", []),
                    constraints=task_context.get("constraints", []),
                    priority=task_context.get("priority", "medium"),
                    estimated_duration=task_context.get("estimated_duration"),
                    dependencies=task_context.get("dependencies", []),
                    user_preferences=task_context.get("user_preferences", {}),
                )

                # Get routing decision
                routing_decision = await self.smart_router.route_task(task_context_obj)

                if routing_decision and routing_decision.selected_agent_id:
                    selected_agent_id = routing_decision.selected_agent_id
                    logger.info(
                        f"Smart routing selected agent {selected_agent_id} for task {task_context_obj.task_id}"
                    )

                    # Return the selected agent
                    return self.agents.get(selected_agent_id)

            # Fallback to original logic
            if agent_type == "frontend":
                return self.frontend_agent
            elif agent_type == "backend":
                return self.backend_agent
            elif agent_type == "container":
                return self.container_agent
            elif agent_type == "shell":
                return self.shell_ops_agent
            else:
                logger.warning(f"Unknown agent type: {agent_type}")
                return None

        except Exception as e:
            logger.error(f"Failed to get agent for task using smart routing: {e}")
            # Fallback to original logic
            if agent_type == "frontend":
                return self.frontend_agent
            elif agent_type == "backend":
                return self.backend_agent
            elif agent_type == "container":
                return self.container_agent
            elif agent_type == "shell":
                return self.shell_ops_agent
            else:
                logger.warning(f"Unknown agent type: {agent_type}")
                return None

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a specific task"""
        # First check in-memory tasks
        task = self.active_tasks.get(task_id) or self.completed_tasks.get(task_id)

        # If not found in memory, check persistence
        if not task:
            persisted_task = task_persistence.get_task(task_id)
            if persisted_task:
                return {
                    "success": True,
                    "task_id": task_id,
                    "project_name": persisted_task.get("name", "Unknown"),
                    "status": persisted_task.get("status", "unknown"),
                    "priority": persisted_task.get("priority", "medium"),
                    "created_at": persisted_task.get("created_at"),
                    "sub_tasks_count": persisted_task.get("sub_tasks_count", 0),
                    "estimated_duration": persisted_task.get("estimated_duration", 0),
                    "message": "Task found in persistence storage",
                }
            else:
                return {"success": False, "error": f"Task {task_id} not found"}

        return {
            "success": True,
            "task_id": task_id,
            "project_name": task.name,
            "status": task.status.value,
            "progress_percentage": task.progress_percentage,
            "sub_tasks": [
                {
                    "id": st.id,
                    "name": st.name,
                    "status": st.status.value,
                    "agent_type": st.agent_type,
                    "estimated_duration": st.estimated_duration,
                    "actual_duration": st.actual_duration,
                    "error": st.error,
                }
                for st in task.sub_tasks
            ],
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": (
                task.completed_at.isoformat() if task.completed_at else None
            ),
            "result_summary": task.result_summary,
        }

    async def get_all_tasks(self) -> Dict[str, Any]:
        """Get status of all tasks"""
        # Get in-memory tasks
        active_tasks = [
            {
                "id": task.id,
                "name": task.name,
                "status": task.status.value,
                "progress_percentage": task.progress_percentage,
                "created_at": task.created_at.isoformat(),
            }
            for task in self.active_tasks.values()
        ]

        completed_tasks = [
            {
                "id": task.id,
                "name": task.name,
                "status": task.status.value,
                "completed_at": (
                    task.completed_at.isoformat() if task.completed_at else None
                ),
            }
            for task in self.completed_tasks.values()
        ]

        # Get persisted tasks
        persisted_tasks = task_persistence.get_all_tasks()

        # Combine in-memory and persisted tasks
        all_task_ids = set()
        for task in active_tasks + completed_tasks:
            all_task_ids.add(task["id"])

        # Add persisted tasks that aren't in memory
        for task_id, task_data in persisted_tasks.items():
            if task_id not in all_task_ids:
                active_tasks.append(
                    {
                        "id": task_id,
                        "name": task_data.get("name", "Unknown"),
                        "status": task_data.get("status", "unknown"),
                        "progress_percentage": 0.0,
                        "created_at": task_data.get("created_at", ""),
                    }
                )

        return {
            "success": True,
            "active_tasks": active_tasks,
            "completed_tasks": completed_tasks,
            "total_active": len(active_tasks),
            "total_completed": len(completed_tasks),
        }

    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """Cancel an active task"""
        task = self.active_tasks.get(task_id)
        if not task:
            return {
                "success": False,
                "error": f"Task {task_id} not found or not active",
            }

        task.status = TaskStatus.CANCELLED
        # Cancel any running sub-tasks
        for sub_task in task.sub_tasks:
            if sub_task.status == TaskStatus.IN_PROGRESS:
                sub_task.status = TaskStatus.CANCELLED

        return {
            "success": True,
            "task_id": task_id,
            "message": f"Task {task_id} cancelled successfully",
        }

    async def _ensure_queue_started(self):
        """Ensure the task queue manager is started"""
        if not self._queue_started:
            await task_queue_manager.start()
            self._queue_started = True

    async def schedule_deferred_task(
        self,
        user_command: str,
        scheduled_at: datetime,
        priority: TaskPriority = TaskPriority.MEDIUM,
        max_retries: int = 3,
    ) -> Dict[str, Any]:
        """Schedule a task for deferred execution"""
        try:
            # Ensure queue is started
            await self._ensure_queue_started()

            # Create task ID
            task_id = f"deferred_task_{self.task_counter:06d}"
            self.task_counter += 1

            # Convert priority to queue priority
            priority_map = {
                TaskPriority.LOW: QueueTaskPriority.LOW,
                TaskPriority.MEDIUM: QueueTaskPriority.MEDIUM,
                TaskPriority.HIGH: QueueTaskPriority.HIGH,
                TaskPriority.CRITICAL: QueueTaskPriority.CRITICAL,
            }
            queue_priority = priority_map.get(priority, QueueTaskPriority.MEDIUM)

            # Add to task queue
            queue_task_id = await task_queue_manager.add_task(
                name=f"Deferred: {user_command[:50]}...",
                description=user_command,
                executor_func=self.process_user_command,
                priority=queue_priority,
                scheduled_at=scheduled_at,
                max_retries=max_retries,
                user_command=user_command,
                priority_param=priority,
            )

            return {
                "success": True,
                "task_id": task_id,
                "queue_task_id": queue_task_id,
                "scheduled_at": scheduled_at.isoformat(),
                "priority": priority.value,
                "message": f"Task scheduled for execution at {scheduled_at}",
            }

        except Exception as e:
            logger.error(f"Error scheduling deferred task: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to schedule deferred task",
            }

    async def get_queue_status(self) -> Dict[str, Any]:
        """Get status of the task queue"""
        await self._ensure_queue_started()
        return await task_queue_manager.get_queue_status()

    async def get_pending_clarifications(self) -> Dict[str, Any]:
        """Get pending clarification requests"""
        clarifications = await clarification_manager.get_pending_clarifications()
        return {
            "success": True,
            "clarifications": clarifications,
            "count": len(clarifications),
        }

    async def provide_clarification(
        self, request_id: str, response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Provide clarification for a request"""
        success = await clarification_manager.provide_clarification(
            request_id, response
        )
        return {
            "success": success,
            "request_id": request_id,
            "message": (
                "Clarification provided successfully"
                if success
                else "Failed to provide clarification"
            ),
        }

    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get learning insights and patterns"""
        return await self.learning_integration.get_learning_insights()

    async def get_pattern_recommendations(
        self, task_description: str
    ) -> Dict[str, Any]:
        """Get pattern recommendations for a task"""
        recommendations = await self.learning_integration.get_pattern_recommendations(
            task_description
        )
        return {
            "success": True,
            "recommendations": recommendations,
            "count": len(recommendations),
        }

    async def learn_from_feedback(
        self, task_id: str, feedback: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Learn from user feedback"""
        await self.learning_integration.learn_from_user_feedback(task_id, feedback)
        return {
            "success": True,
            "task_id": task_id,
            "message": "Feedback processed successfully",
        }

    # Collaboration System Methods

    async def create_collaboration(
        self,
        name: str,
        pattern_name: str,
        task_data: Dict[str, Any],
        agent_mapping: Dict[str, str] = None,
    ) -> Dict[str, Any]:
        """Create a new collaboration using a pattern."""
        try:
            # Get pattern from library
            pattern = self.pattern_library.get_pattern(pattern_name)
            if not pattern:
                return {
                    "success": False,
                    "error": f"Pattern '{pattern_name}' not found",
                    "available_patterns": self.pattern_library.list_patterns(),
                }

            # Create collaboration from pattern
            collaboration = self.pattern_library.create_collaboration_from_pattern(
                pattern_name, name, task_data, agent_mapping
            )

            if not collaboration:
                return {
                    "success": False,
                    "error": "Failed to create collaboration from pattern",
                }

            # Execute collaboration
            result = await self.collaboration_manager.execute_collaboration(
                collaboration.id
            )

            return {
                "success": True,
                "collaboration_id": collaboration.id,
                "pattern": pattern_name,
                "result": result,
                "message": f"Collaboration '{name}' completed successfully",
            }

        except Exception as e:
            logger.error(f"Error creating collaboration: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create collaboration",
            }

    async def execute_complex_task(
        self, task_description: str, available_agents: List[str] = None
    ) -> Dict[str, Any]:
        """Execute a complex task using intelligent collaboration patterns."""
        try:
            if available_agents is None:
                available_agents = list(self.agents.keys())

            # Get pattern recommendations
            recommendations = self.pattern_library.get_pattern_recommendations(
                task_description, available_agents
            )

            if not recommendations:
                return {
                    "success": False,
                    "error": "No suitable collaboration patterns found",
                    "message": "Try breaking down the task or check agent availability",
                }

            # Use the best recommendation
            best_pattern = recommendations[0]
            pattern_name = best_pattern["pattern_name"]

            logger.info(
                f"Selected pattern '{pattern_name}' for task: {task_description}"
            )

            # Create task data
            task_data = {
                "description": task_description,
                "requirements": self._parse_requirements(task_description),
                "timestamp": datetime.now().isoformat(),
            }

            # Create and execute collaboration
            return await self.create_collaboration(
                name=f"complex_task_{int(datetime.now().timestamp())}",
                pattern_name=pattern_name,
                task_data=task_data,
            )

        except Exception as e:
            logger.error(f"Error executing complex task: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to execute complex task",
            }

    def _parse_requirements(self, description: str) -> Dict[str, Any]:
        """Parse requirements from task description."""
        # Simple requirement extraction (could be enhanced with NLP)
        requirements = {
            "frontend": "frontend" in description.lower()
            or "ui" in description.lower()
            or "interface" in description.lower(),
            "backend": "backend" in description.lower()
            or "api" in description.lower()
            or "server" in description.lower(),
            "database": "database" in description.lower()
            or "db" in description.lower()
            or "data" in description.lower(),
            "container": "deploy" in description.lower()
            or "container" in description.lower()
            or "docker" in description.lower(),
            "security": "security" in description.lower()
            or "auth" in description.lower()
            or "secure" in description.lower(),
            "testing": "test" in description.lower()
            or "testing" in description.lower(),
        }
        return requirements

    async def get_collaboration_status(self, collaboration_id: str) -> Dict[str, Any]:
        """Get the status of a collaboration."""
        try:
            status = self.collaboration_manager.get_collaboration_status(
                collaboration_id
            )
            return {
                "success": True,
                "collaboration_id": collaboration_id,
                "status": status,
            }
        except Exception as e:
            logger.error(f"Error getting collaboration status: {e}")
            return {"success": False, "error": str(e)}

    async def list_active_collaborations(self) -> Dict[str, Any]:
        """List all active collaborations."""
        try:
            collaborations = self.collaboration_manager.get_active_collaborations()
            return {
                "success": True,
                "active_collaborations": collaborations,
                "count": len(collaborations),
            }
        except Exception as e:
            logger.error(f"Error listing collaborations: {e}")
            return {"success": False, "error": str(e)}

    async def get_collaboration_patterns(self) -> Dict[str, Any]:
        """Get available collaboration patterns."""
        try:
            patterns = self.pattern_library.list_patterns()
            available_agents = list(self.agents.keys())
            compatible_patterns = self.pattern_library.get_patterns_for_agents(
                available_agents
            )

            return {
                "success": True,
                "all_patterns": patterns,
                "compatible_patterns": compatible_patterns,
                "available_agents": available_agents,
            }
        except Exception as e:
            logger.error(f"Error getting patterns: {e}")
            return {"success": False, "error": str(e)}

    async def get_collaboration_metrics(self) -> Dict[str, Any]:
        """Get collaboration performance metrics."""
        try:
            metrics = self.collaboration_manager.get_performance_metrics()
            return {"success": True, "metrics": metrics}
        except Exception as e:
            logger.error(f"Error getting collaboration metrics: {e}")
            return {"success": False, "error": str(e)}

    async def shutdown(self):
        """Shutdown the ArchitectAgent and all specialized agents"""
        logger.info("Shutting down ArchitectAgent...")

        # Cancel all active tasks
        for task_id in list(self.active_tasks.keys()):
            try:
                # Cancel the execution task if it exists
                project_task = self.active_tasks[task_id]
                if project_task.metadata and "execution_task" in project_task.metadata:
                    execution_task = project_task.metadata["execution_task"]
                    if not execution_task.done():
                        execution_task.cancel()
                        try:
                            await execution_task
                        except asyncio.CancelledError:
                            pass

                await self.cancel_task(task_id)
            except Exception as e:
                logger.warning(f"Error cancelling task {task_id}: {e}")

        # Shutdown specialized agents
        try:
            await self.frontend_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down frontend agent: {e}")

        try:
            await self.backend_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down backend agent: {e}")

        try:
            await self.container_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down container agent: {e}")

        try:
            await self.learning_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down learning agent: {e}")

        try:
            await self.shell_ops_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down shell ops agent: {e}")

        try:
            await self.security_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down security agent: {e}")

        try:
            await self.monitoring_agent.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down monitoring agent: {e}")

        # Stop task queue manager
        try:
            if self._queue_started:
                await task_queue_manager.stop()
                self._queue_started = False
        except Exception as e:
            logger.warning(f"Error stopping task queue manager: {e}")

        # Stop clarification manager
        try:
            await clarification_manager.shutdown()
        except Exception as e:
            logger.warning(f"Error shutting down clarification manager: {e}")

        # Cleanup collaboration system
        try:
            self.collaboration_manager.cleanup()
            self.shared_context_manager.save_all_contexts()
        except Exception as e:
            logger.warning(f"Error cleaning up collaboration system: {e}")

        logger.info("ArchitectAgent shutdown complete")

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description for architect agent

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        try:
            # Use the task parser to parse requirements
            parsed_tasks = self.task_parser.parse_task(task_description)

            # Classify intent
            intent = self.intent_classifier.classify_intent(task_description)

            # Extract key information
            requirements = {
                "description": task_description,
                "parsed": True,
                "intent": intent,
                "parsed_tasks": parsed_tasks,
                "estimated_duration": self._estimate_duration(parsed_tasks),
                "priority": self._determine_priority(intent, task_description),
                "agent_requirements": self._identify_agent_requirements(parsed_tasks),
            }

            return requirements

        except Exception as e:
            logger.error(f"Error parsing task requirements: {e}")
            return {"description": task_description, "parsed": False, "error": str(e)}

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation for architect agent

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        try:
            if not requirements.get("parsed", False):
                return {
                    "success": False,
                    "error": "Failed to parse task requirements",
                    "task_id": task_id,
                }

            # Create project task
            project_task = ProjectTask(
                id=task_id,
                name=f"Project {task_id}",
                description=requirements["description"],
                user_command=requirements["description"],
                status=TaskStatus.IN_PROGRESS,
                priority=requirements.get("priority", TaskPriority.MEDIUM),
                sub_tasks=[],
                created_at=datetime.now(),
                started_at=datetime.now(),
                metadata=requirements,
            )

            # Store in active tasks
            self.active_tasks[task_id] = project_task

            # Create sub-tasks
            sub_tasks = await self._create_sub_tasks(
                requirements["parsed_tasks"], task_id
            )
            project_task.sub_tasks = sub_tasks

            # Execute sub-tasks
            results = []
            for sub_task in sub_tasks:
                try:
                    result = await self._execute_sub_task(sub_task)
                    results.append(result)

                    # Update sub-task with result
                    sub_task.result = result
                    sub_task.completed_at = datetime.now()
                    sub_task.actual_duration = (
                        sub_task.completed_at - sub_task.started_at
                    ).total_seconds() / 60

                except Exception as e:
                    logger.error(f"Error executing sub-task {sub_task.id}: {e}")
                    sub_task.error = str(e)
                    sub_task.status = TaskStatus.FAILED
                    results.append(
                        {"success": False, "error": str(e), "sub_task_id": sub_task.id}
                    )

            # Determine overall success
            all_successful = all(r.get("success", False) for r in results)

            # Update project task
            project_task.completed_at = datetime.now()
            project_task.status = (
                TaskStatus.COMPLETED if all_successful else TaskStatus.FAILED
            )
            project_task.total_actual_duration = (
                project_task.completed_at - project_task.started_at
            ).total_seconds() / 60
            project_task.result_summary = {
                "total_sub_tasks": len(sub_tasks),
                "successful_sub_tasks": len(
                    [r for r in results if r.get("success", False)]
                ),
                "failed_sub_tasks": len(
                    [r for r in results if not r.get("success", False)]
                ),
                "results": results,
            }

            # Move to appropriate collection
            if all_successful:
                self.completed_tasks[task_id] = project_task
                del self.active_tasks[task_id]
            else:
                self.failed_tasks[task_id] = project_task
                del self.active_tasks[task_id]

            return {
                "success": all_successful,
                "task_id": task_id,
                "project_task": asdict(project_task),
                "results": results,
                "total_duration": project_task.total_actual_duration,
            }

        except Exception as e:
            logger.error(f"Error executing architect task {task_id}: {e}")
            return {"success": False, "error": str(e), "task_id": task_id}

    async def _verify_task_specific(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Architect-specific task verification

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        try:
            # Check if all sub-tasks were successful
            if not result.get("success", False):
                return False

            # Check if project task was completed
            project_task = result.get("project_task", {})
            if project_task.get("status") != "completed":
                return False

            # Check if all sub-tasks have results
            results = result.get("results", [])
            if not results:
                return False

            # Verify that all sub-tasks were successful
            all_successful = all(r.get("success", False) for r in results)

            # Additional verification for architect-specific tasks
            if "architect" in task_description.lower():
                # Check if task was properly orchestrated
                if not project_task.get("result_summary"):
                    return False

                # Check if all required agents were involved
                agent_requirements = result.get("metadata", {}).get(
                    "agent_requirements", []
                )
                if agent_requirements and not all(
                    agent in self.agents for agent in agent_requirements
                ):
                    return False

            return all_successful

        except Exception as e:
            logger.error(f"Error during architect task verification: {e}")
            return False

    def _estimate_duration(self, parsed_tasks: Dict[str, Any]) -> int:
        """Estimate task duration based on parsed tasks"""
        total_duration = 0
        for task in parsed_tasks.get("tasks", []):
            total_duration += task.get("estimated_duration", 30)
        return total_duration

    def _determine_priority(self, intent: str, description: str) -> TaskPriority:
        """Determine task priority based on intent and description"""
        if "critical" in description.lower() or "urgent" in description.lower():
            return TaskPriority.CRITICAL
        elif "high" in description.lower() or "important" in description.lower():
            return TaskPriority.HIGH
        elif "low" in description.lower() or "minor" in description.lower():
            return TaskPriority.LOW
        else:
            return TaskPriority.MEDIUM

    def _identify_agent_requirements(self, parsed_tasks: Dict[str, Any]) -> List[str]:
        """Identify which agents are required for the task"""
        required_agents = set()
        for task in parsed_tasks.get("tasks", []):
            agent_type = task.get("agent_type", "").lower()
            if agent_type in self.agents:
                required_agents.add(agent_type)
        return list(required_agents)
