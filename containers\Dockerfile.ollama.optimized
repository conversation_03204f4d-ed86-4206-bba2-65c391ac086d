# AI Coding Agent - Optimized Ollama LLM Service Dockerfile
# Multi-stage build with GPU support and security hardening

# Stage 1: Base Ollama
FROM ollama/ollama:latest AS base

# Set environment variables
ENV OLLAMA_HOST=0.0.0.0
ENV OLLAMA_ORIGINS=*
ENV NVIDIA_VISIBLE_DEVICES=0
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility
ENV CUDA_VISIBLE_DEVICES=0

# Create non-root user for security
RUN groupadd --system --gid 1000 ollama \
    && useradd --system --uid 1000 --gid ollama --create-home --shell /bin/bash ollama

# Stage 2: Production Runtime
FROM base AS production

# Install additional dependencies for monitoring
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /home/<USER>

# Create necessary directories with proper ownership
RUN mkdir -p /home/<USER>/.ollama/models \
    && chown -R ollama:ollama /home/<USER>

# Switch to non-root user
USER ollama

# Expose Ollama port
EXPOSE 11434

# Health check for Ollama service
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:11434/api/tags || exit 1

# Default command
CMD ["ollama", "serve"]

# Stage 3: Development with GPU Support
FROM production AS development

# Install development tools
USER root
RUN apt-get update && apt-get install -y \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

USER ollama

# Override command for development
CMD ["ollama", "serve", "--verbose"]
