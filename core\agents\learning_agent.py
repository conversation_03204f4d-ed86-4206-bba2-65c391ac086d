#!/usr/bin/env python3
"""
LearningAgent - Specialized agent for learning and LLM training tasks

Handles:
- LLM fine-tuning and training
- Learning pattern analysis
- Model optimization
- Training data management
- Performance monitoring
- Learning feedback integration
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.agents.enhanced_base_agent import EnhancedBaseAgent
from core.agents.enhanced_base_agent import TaskStatus as EnhancedTaskStatus
from core.agents.enhanced_base_agent import Veri<PERSON><PERSON><PERSON>l
from learning.automated_learner import Auto<PERSON><PERSON><PERSON>ner
from learning.best_practices_learner import BestPractices<PERSON>earner
from models.ollama_manager import OllamaModelManager

logger = logging.getLogger(__name__)


class LearningAgent(EnhancedBaseAgent):
    """Specialized agent for learning and LLM training tasks"""

    def __init__(self, config_path: str = "config/learning_agent_config.json"):
        """Initialize the LearningAgent"""
        default_config = {
            "memory_tracking": {
                "enabled": True,
                "max_attempts_per_task": 3,
                "cooldown_seconds": 300,
                "verify_tasks": True,
                "reset_on_success": True,
                "verification_level": "comprehensive",
                "persistence": True,
                "cleanup_interval": 86400,
            },
            "model_settings": {
                "model_name": "deepseek-coder:1.3b",
                "system_prompt": "You are a learning AI assistant specialized in LLM training and optimization.",
            },
            "training_enabled": True,
            "fine_tuning_enabled": True,
            "pattern_learning": True,
            "performance_monitoring": True,
            "feedback_integration": True,
            "model_optimization": True,
            "data_management": True,
        }
        super().__init__(config_path, default_config)

        self.ollama_manager = OllamaModelManager()

        # Initialize learning components with fallbacks
        try:
            self.automated_learner = AutomatedLearner(self.config)
        except Exception as e:
            logger.warning(f"Could not initialize AutomatedLearner: {e}")
            self.automated_learner = None

        try:
            self.best_practices_learner = BestPracticesLearner(self.config)
        except Exception as e:
            logger.warning(f"Could not initialize BestPracticesLearner: {e}")
            self.best_practices_learner = None

        logger.info(
            "LearningAgent initialized successfully with enhanced memory tracking"
        )

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description for learning agent

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        try:
            # Use existing learning requirements parser
            requirements = await self._parse_learning_requirements(task_description)
            requirements["parsed"] = True
            return requirements
        except Exception as e:
            logger.error(f"Error parsing learning task requirements: {e}")
            return {"description": task_description, "parsed": False, "error": str(e)}

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation for learning agent

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        try:
            if not requirements.get("parsed", False):
                return {
                    "success": False,
                    "error": "Failed to parse task requirements",
                    "task_id": task_id,
                }

            logger.info(
                f"Executing learning task: {requirements.get('description', task_id)}"
            )

            # Execute learning tasks based on requirements
            results = {}

            if requirements.get("fine_tuning", False):
                results["fine_tuning"] = await self._execute_fine_tuning(
                    requirements, task_id
                )

            if requirements.get("pattern_learning", False):
                results["pattern_learning"] = await self._execute_pattern_learning(
                    requirements, task_id
                )

            if requirements.get("model_optimization", False):
                results["model_optimization"] = await self._execute_model_optimization(
                    requirements, task_id
                )

            if requirements.get("performance_monitoring", False):
                results["performance_monitoring"] = (
                    await self._execute_performance_monitoring(requirements, task_id)
                )

            if requirements.get("data_management", False):
                results["data_management"] = await self._execute_data_management(
                    requirements, task_id
                )

            # Determine overall success
            all_successful = all(
                result.get("success", False) for result in results.values()
            )

            return {
                "success": all_successful,
                "task_id": task_id,
                "results": results,
                "requirements": requirements,
            }

        except Exception as e:
            logger.error(f"Error executing learning task {task_id}: {e}")
            return {"success": False, "error": str(e), "task_id": task_id}

    async def _verify_task_specific(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Learning-specific task verification

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        try:
            # Check if task was successful
            if not result.get("success", False):
                return False

            # Check if all learning components were executed successfully
            results = result.get("results", {})
            if not results:
                return False

            # Verify each learning component
            for component, component_result in results.items():
                if not component_result.get("success", False):
                    logger.warning(
                        f"Learning component {component} failed verification"
                    )
                    return False

            # Additional verification for learning-specific tasks
            if (
                "learning" in task_description.lower()
                or "training" in task_description.lower()
            ):
                # Check if learning components are properly initialized
                if not self.automated_learner and not self.best_practices_learner:
                    logger.warning("Learning components not properly initialized")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error during learning task verification: {e}")
            return False
