"""
Task Persistence Manager
Handles storing and retrieving tasks between CLI commands
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class TaskPersistenceManager:
    """Manages task persistence between CLI commands"""

    def __init__(self, storage_file: str = "data/architect_tasks.json"):
        self.storage_file = Path(storage_file)
        self.storage_file.parent.mkdir(parents=True, exist_ok=True)
        self.tasks: Dict[str, Any] = {}
        self._load_tasks()

    def _load_tasks(self):
        """Load tasks from storage file"""
        try:
            if self.storage_file.exists():
                with open(self.storage_file, "r") as f:
                    data = json.load(f)
                    self.tasks = data.get("tasks", {})
                    logger.info(f"Loaded {len(self.tasks)} tasks from storage")
            else:
                self.tasks = {}
                logger.info("No existing task storage found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading tasks: {e}")
            self.tasks = {}

    def _save_tasks(self):
        """Save tasks to storage file"""
        try:
            data = {"tasks": self.tasks, "last_updated": datetime.now().isoformat()}
            with open(self.storage_file, "w") as f:
                json.dump(data, f, indent=2, default=str)
            logger.debug(f"Saved {len(self.tasks)} tasks to storage")
        except Exception as e:
            logger.error(f"Error saving tasks: {e}")

    def store_task(self, task_id: str, task_data: Dict[str, Any]):
        """Store a task"""
        self.tasks[task_id] = task_data
        self._save_tasks()

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get a task by ID"""
        return self.tasks.get(task_id)

    def get_all_tasks(self) -> Dict[str, Any]:
        """Get all tasks"""
        return self.tasks.copy()

    def update_task(self, task_id: str, task_data: Dict[str, Any]):
        """Update a task"""
        if task_id in self.tasks:
            self.tasks[task_id].update(task_data)
            self._save_tasks()

    def delete_task(self, task_id: str):
        """Delete a task"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            self._save_tasks()

    def clear_tasks(self):
        """Clear all tasks"""
        self.tasks = {}
        self._save_tasks()

    def get_active_tasks(self) -> Dict[str, Any]:
        """Get only active tasks"""
        return {
            task_id: task_data
            for task_id, task_data in self.tasks.items()
            if task_data.get("status")
            in ["pending", "in_progress", "waiting_dependencies"]
        }

    def get_completed_tasks(self) -> Dict[str, Any]:
        """Get only completed tasks"""
        return {
            task_id: task_data
            for task_id, task_data in self.tasks.items()
            if task_data.get("status") in ["completed", "failed", "cancelled"]
        }


# Global instance
task_persistence = TaskPersistenceManager()
