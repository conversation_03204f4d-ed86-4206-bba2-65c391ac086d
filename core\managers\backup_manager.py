#!/usr/bin/env python3
"""
Unified Backup Manager for AI Coding Agent
Handles automated backup operations, scheduling, and storage management.
Consolidates multiple backup implementations into a single comprehensive solution.
"""

import gzip
import hashlib
import json
import logging
import os
import shutil
import sys
import zipfile
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class BackupManager:
    """Manages automated backup operations and storage."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backup_config = config.get("backup", {})
        self.enabled = self.backup_config.get("enabled", False)

        # Backup storage paths
        self.local_backup_path = Path(
            self.backup_config.get("storage", {})
            .get("local", {})
            .get("path", "backups/local")
        )
        self.local_backup_path.mkdir(parents=True, exist_ok=True)

        # Backup metadata storage
        self.metadata_file = self.local_backup_path / "backup_metadata.json"
        self.backup_metadata = self._load_metadata()

        # Backup statistics
        loaded_stats = self.backup_metadata.get("statistics", {})
        self.backup_stats = {
            "total_backups": loaded_stats.get("total_backups", 0) or 0,
            "successful_backups": loaded_stats.get("successful_backups", 0) or 0,
            "failed_backups": loaded_stats.get("failed_backups", 0) or 0,
            "total_size_bytes": loaded_stats.get("total_size_bytes", 0) or 0,
            "last_backup_time": loaded_stats.get("last_backup_time", ""),
        }

        # Add missing attributes that tests expect
        self.backup_dir = str(self.local_backup_path)
        self.backup_enabled = self.enabled

        logger.info("Backup Manager initialized successfully")

    def _load_metadata(self) -> Dict[str, Any]:
        """Load backup metadata from file."""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, "r") as f:
                    metadata = json.load(f)
                logger.info(
                    f"Loaded backup metadata with {len(metadata.get('backups', {}))} backups"
                )
                return metadata
            else:
                return {"backups": {}, "statistics": {}, "last_updated": None}
        except Exception as e:
            logger.error(f"Error loading backup metadata: {e}")
            return {"backups": {}, "statistics": {}, "last_updated": None}

    def _save_metadata(self):
        """Save backup metadata to file."""
        try:
            self.backup_metadata["last_updated"] = datetime.now(
                timezone.utc
            ).isoformat()
            with open(self.metadata_file, "w") as f:
                json.dump(self.backup_metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving backup metadata: {e}")

    def create_backup(self, backup_type: str = "full") -> Dict[str, Any]:
        """Create a backup of specified type."""
        try:
            if not self.enabled:
                return {"error": "Backup system is disabled"}

            backup_id = (
                f"{backup_type}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
            )
            backup_path = self.local_backup_path / backup_id
            backup_path.mkdir(exist_ok=True)

            logger.info(f"Creating {backup_type} backup: {backup_id}")

            # Create backup based on type
            if backup_type == "full":
                result = self._create_full_backup(backup_path, backup_id)
            elif backup_type == "incremental":
                result = self._create_incremental_backup(backup_path, backup_id)
            elif backup_type == "database":
                result = self._create_database_backup(backup_path, backup_id)
            elif backup_type == "configuration":
                result = self._create_configuration_backup(backup_path, backup_id)
            elif backup_type == "code":
                result = self._create_code_backup(backup_path, backup_id)
            elif backup_type == "user_data":
                result = self._create_user_data_backup(backup_path, backup_id)
            else:
                return {"error": f"Unknown backup type: {backup_type}"}

            if "error" not in result:
                # Update metadata
                self._update_backup_metadata(backup_id, backup_type, result)

                # Update statistics
                self._update_backup_statistics(result)

                # Cleanup old backups
                self._cleanup_old_backups()

                logger.info(f"Backup {backup_id} created successfully")

            return result

        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return {"error": str(e)}

    def _create_full_backup(self, backup_path: Path, backup_id: str) -> Dict[str, Any]:
        """Create a full system backup."""
        try:
            start_time = datetime.now(timezone.utc)

            # Create backup components
            components = {}

            # Database backup
            db_result = self._backup_database(backup_path / "database")
            components["database"] = db_result

            # Configuration backup
            config_result = self._backup_configuration(backup_path / "configuration")
            components["configuration"] = config_result

            # Code backup
            code_result = self._backup_code(backup_path / "code")
            components["code"] = code_result

            # User data backup
            user_data_result = self._backup_user_data(backup_path / "user_data")
            components["user_data"] = user_data_result

            # System state backup
            system_result = self._backup_system_state(backup_path / "system")
            components["system"] = system_result

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Calculate total size
            total_size = sum(comp.get("size_bytes", 0) for comp in components.values())

            # Create backup manifest
            manifest = {
                "backup_id": backup_id,
                "backup_type": "full",
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "components": components,
                "total_size_bytes": total_size,
                "compression_ratio": self._calculate_compression_ratio(
                    total_size, backup_path
                ),
                "checksum": self._calculate_backup_checksum(backup_path),
            }

            # Save manifest
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)

            return {
                "success": True,
                "backup_id": backup_id,
                "backup_type": "full",
                "duration_seconds": duration,
                "size_bytes": total_size,
                "components": list(components.keys()),
                "manifest_path": str(backup_path / "manifest.json"),
            }

        except Exception as e:
            logger.error(f"Error creating full backup: {e}")
            return {"error": str(e)}

    def _create_incremental_backup(
        self, backup_path: Path, backup_id: str
    ) -> Dict[str, Any]:
        """Create an incremental backup."""
        try:
            start_time = datetime.now(timezone.utc)

            # Find last backup for comparison
            last_backup = self._get_last_backup()

            # Create incremental components
            components = {}

            # Incremental database backup
            if last_backup:
                db_result = self._backup_database_incremental(
                    backup_path / "database", last_backup
                )
            else:
                db_result = self._backup_database(backup_path / "database")
            components["database"] = db_result

            # Incremental configuration backup
            config_result = self._backup_configuration_incremental(
                backup_path / "configuration", last_backup
            )
            components["configuration"] = config_result

            # Incremental code backup
            code_result = self._backup_code_incremental(
                backup_path / "code", last_backup
            )
            components["code"] = code_result

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Calculate total size
            total_size = sum(comp.get("size_bytes", 0) for comp in components.values())

            # Create backup manifest
            manifest = {
                "backup_id": backup_id,
                "backup_type": "incremental",
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "base_backup": last_backup.get("backup_id") if last_backup else None,
                "components": components,
                "total_size_bytes": total_size,
                "compression_ratio": self._calculate_compression_ratio(
                    total_size, backup_path
                ),
                "checksum": self._calculate_backup_checksum(backup_path),
            }

            # Save manifest
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)

            return {
                "success": True,
                "backup_id": backup_id,
                "backup_type": "incremental",
                "duration_seconds": duration,
                "size_bytes": total_size,
                "components": list(components.keys()),
                "base_backup": last_backup.get("backup_id") if last_backup else None,
                "manifest_path": str(backup_path / "manifest.json"),
            }

        except Exception as e:
            logger.error(f"Error creating incremental backup: {e}")
            return {"error": str(e)}

    def _create_database_backup(
        self, backup_path: Path, backup_id: str
    ) -> Dict[str, Any]:
        """Create a database-specific backup."""
        try:
            start_time = datetime.now(timezone.utc)

            # Database backup
            db_result = self._backup_database(backup_path)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Create backup manifest
            manifest = {
                "backup_id": backup_id,
                "backup_type": "database",
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "database_info": db_result,
                "checksum": self._calculate_backup_checksum(backup_path),
            }

            # Save manifest
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)

            return {
                "success": True,
                "backup_id": backup_id,
                "backup_type": "database",
                "duration_seconds": duration,
                "size_bytes": db_result.get("size_bytes", 0),
                "database_tables": db_result.get("tables", []),
                "manifest_path": str(backup_path / "manifest.json"),
            }

        except Exception as e:
            logger.error(f"Error creating database backup: {e}")
            return {"error": str(e)}

    def _create_configuration_backup(
        self, backup_path: Path, backup_id: str
    ) -> Dict[str, Any]:
        """Create a configuration-specific backup."""
        try:
            start_time = datetime.now(timezone.utc)

            # Configuration backup
            config_result = self._backup_configuration(backup_path)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Create backup manifest
            manifest = {
                "backup_id": backup_id,
                "backup_type": "configuration",
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "configuration_files": config_result.get("files", []),
                "checksum": self._calculate_backup_checksum(backup_path),
            }

            # Save manifest
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)

            return {
                "success": True,
                "backup_id": backup_id,
                "backup_type": "configuration",
                "duration_seconds": duration,
                "size_bytes": config_result.get("size_bytes", 0),
                "config_files": config_result.get("files", []),
                "manifest_path": str(backup_path / "manifest.json"),
            }

        except Exception as e:
            logger.error(f"Error creating configuration backup: {e}")
            return {"error": str(e)}

    def _create_code_backup(self, backup_path: Path, backup_id: str) -> Dict[str, Any]:
        """Create a code-specific backup."""
        try:
            start_time = datetime.now(timezone.utc)

            # Code backup
            code_result = self._backup_code(backup_path)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Create backup manifest
            manifest = {
                "backup_id": backup_id,
                "backup_type": "code",
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "code_files": code_result.get("files", []),
                "checksum": self._calculate_backup_checksum(backup_path),
            }

            # Save manifest
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)

            return {
                "success": True,
                "backup_id": backup_id,
                "backup_type": "code",
                "duration_seconds": duration,
                "size_bytes": code_result.get("size_bytes", 0),
                "code_files": code_result.get("files", []),
                "manifest_path": str(backup_path / "manifest.json"),
            }

        except Exception as e:
            logger.error(f"Error creating code backup: {e}")
            return {"error": str(e)}

    def _create_user_data_backup(
        self, backup_path: Path, backup_id: str
    ) -> Dict[str, Any]:
        """Create a user data-specific backup."""
        try:
            start_time = datetime.now(timezone.utc)

            # User data backup
            user_data_result = self._backup_user_data(backup_path)

            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()

            # Create backup manifest
            manifest = {
                "backup_id": backup_id,
                "backup_type": "user_data",
                "timestamp": start_time.isoformat(),
                "duration_seconds": duration,
                "user_data_files": user_data_result.get("files", []),
                "checksum": self._calculate_backup_checksum(backup_path),
            }

            # Save manifest
            with open(backup_path / "manifest.json", "w") as f:
                json.dump(manifest, f, indent=2)

            return {
                "success": True,
                "backup_id": backup_id,
                "backup_type": "user_data",
                "duration_seconds": duration,
                "size_bytes": user_data_result.get("size_bytes", 0),
                "user_data_files": user_data_result.get("files", []),
                "manifest_path": str(backup_path / "manifest.json"),
            }

        except Exception as e:
            logger.error(f"Error creating user data backup: {e}")
            return {"error": str(e)}

    def _backup_database(self, backup_path: Path) -> Dict[str, Any]:
        """Backup database files."""
        try:
            backup_path.mkdir(parents=True, exist_ok=True)

            # Find database files
            db_files = []
            total_size = 0

            # Look for SQLite database files
            for db_file in Path(".").glob("*.db*"):
                if db_file.is_file():
                    db_files.append(str(db_file))
                    total_size += db_file.stat().st_size

                    # Copy database file
                    shutil.copy2(db_file, backup_path / db_file.name)

            return {
                "files": db_files,
                "size_bytes": total_size,
                "tables": self._get_database_tables(),
                "backup_path": str(backup_path),
            }

        except Exception as e:
            logger.error(f"Error backing up database: {e}")
            return {"error": str(e)}

    def _backup_database_incremental(
        self, backup_path: Path, last_backup: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create incremental database backup."""
        try:
            # For now, create full backup (incremental logic would compare with last backup)
            return self._backup_database(backup_path)
        except Exception as e:
            logger.error(f"Error creating incremental database backup: {e}")
            return {"error": str(e)}

    def _backup_configuration(self, backup_path: Path) -> Dict[str, Any]:
        """Backup configuration files."""
        try:
            backup_path.mkdir(parents=True, exist_ok=True)

            config_files = []
            total_size = 0

            # Backup config directory
            config_dir = Path("config")
            if config_dir.exists():
                for config_file in config_dir.rglob("*.json"):
                    if config_file.is_file():
                        config_files.append(str(config_file))
                        total_size += config_file.stat().st_size

                        # Copy config file
                        relative_path = config_file.relative_to(config_dir)
                        target_path = backup_path / relative_path
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(config_file, target_path)

            return {
                "files": config_files,
                "size_bytes": total_size,
                "backup_path": str(backup_path),
            }

        except Exception as e:
            logger.error(f"Error backing up configuration: {e}")
            return {"error": str(e)}

    def _backup_configuration_incremental(
        self, backup_path: Path, last_backup: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create incremental configuration backup."""
        try:
            # For now, create full backup (incremental logic would compare with last backup)
            return self._backup_configuration(backup_path)
        except Exception as e:
            logger.error(f"Error creating incremental configuration backup: {e}")
            return {"error": str(e)}

    def _backup_code(self, backup_path: Path) -> Dict[str, Any]:
        """Backup source code files."""
        try:
            backup_path.mkdir(parents=True, exist_ok=True)

            code_files = []
            total_size = 0

            # Backup src directory
            src_dir = Path("src")
            if src_dir.exists():
                for code_file in src_dir.rglob("*.py"):
                    if code_file.is_file():
                        code_files.append(str(code_file))
                        total_size += code_file.stat().st_size

                        # Copy code file
                        relative_path = code_file.relative_to(src_dir)
                        target_path = backup_path / relative_path
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(code_file, target_path)

            return {
                "files": code_files,
                "size_bytes": total_size,
                "backup_path": str(backup_path),
            }

        except Exception as e:
            logger.error(f"Error backing up code: {e}")
            return {"error": str(e)}

    def _backup_code_incremental(
        self, backup_path: Path, last_backup: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create incremental code backup."""
        try:
            # For now, create full backup (incremental logic would compare with last backup)
            return self._backup_code(backup_path)
        except Exception as e:
            logger.error(f"Error creating incremental code backup: {e}")
            return {"error": str(e)}

    def _backup_user_data(self, backup_path: Path) -> Dict[str, Any]:
        """Backup user data files."""
        try:
            backup_path.mkdir(parents=True, exist_ok=True)

            user_data_files = []
            total_size = 0

            # Backup user data directories
            user_data_dirs = ["content", "data", "logs", "test_reports"]

            for data_dir_name in user_data_dirs:
                data_dir = Path(data_dir_name)
                if data_dir.exists():
                    for data_file in data_dir.rglob("*"):
                        if data_file.is_file():
                            user_data_files.append(str(data_file))
                            total_size += data_file.stat().st_size

                            # Copy data file
                            relative_path = data_file.relative_to(data_dir)
                            target_path = backup_path / data_dir_name / relative_path
                            target_path.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(data_file, target_path)

            return {
                "files": user_data_files,
                "size_bytes": total_size,
                "backup_path": str(backup_path),
            }

        except Exception as e:
            logger.error(f"Error backing up user data: {e}")
            return {"error": str(e)}

    def _backup_system_state(self, backup_path: Path) -> Dict[str, Any]:
        """Backup system state information."""
        try:
            backup_path.mkdir(parents=True, exist_ok=True)

            # System information
            system_info = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "python_version": sys.version,
                "platform": os.name,
                "current_directory": str(Path.cwd()),
                "environment_variables": dict(os.environ),
            }

            # Save system info
            with open(backup_path / "system_info.json", "w") as f:
                json.dump(system_info, f, indent=2)

            return {
                "system_info": system_info,
                "size_bytes": len(json.dumps(system_info)),
                "backup_path": str(backup_path),
            }

        except Exception as e:
            logger.error(f"Error backing up system state: {e}")
            return {"error": str(e)}

    def _get_database_tables(self) -> List[str]:
        """Get list of database tables."""
        try:
            # Basic implementation - in production this would query the actual database
            # For now, implement basic table discovery logic

            # Common database tables that might exist
            common_tables = [
                "users",
                "sessions",
                "backups",
                "logs",
                "audit_logs",
                "mfa_configs",
                "oauth_sessions",
                "threat_data",
                "performance_metrics",
                "cache_data",
                "compliance_data",
                "disaster_recovery_logs",
                "site_data",
                "deployments",
                "api_keys",
                "webhooks",
                "notifications",
                "settings",
            ]

            # In production, you would:
            # 1. Connect to the actual database
            # 2. Query system tables for table names
            # 3. Filter out system tables if needed
            # 4. Return actual table names

            # Example for SQLite:
            # import sqlite3
            # conn = sqlite3.connect('database.db')
            # cursor = conn.cursor()
            # cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            # tables = [row[0] for row in cursor.fetchall()]
            # conn.close()
            # return tables

            # Example for PostgreSQL:
            # import psycopg2
            # conn = psycopg2.connect("dbname=test user=postgres password=secret")
            # cursor = conn.cursor()
            # cursor.execute("SELECT tablename FROM pg_tables WHERE schemaname='public';")
            # tables = [row[0] for row in cursor.fetchall()]
            # conn.close()
            # return tables

            # For now, return common tables that might exist
            return common_tables

        except Exception as e:
            logger.error(f"Error getting database tables: {e}")
            return []

    def _calculate_compression_ratio(
        self, original_size: int, backup_path: Path
    ) -> float:
        """Calculate compression ratio for backup."""
        try:
            if original_size == 0:
                return 1.0

            # Calculate actual backup size
            actual_size = sum(
                f.stat().st_size for f in backup_path.rglob("*") if f.is_file()
            )
            return actual_size / original_size if original_size > 0 else 1.0

        except Exception as e:
            logger.error(f"Error calculating compression ratio: {e}")
            return 1.0

    def _calculate_backup_checksum(self, backup_path: Path) -> str:
        """Calculate checksum for backup."""
        try:
            checksum = hashlib.sha256()

            # Calculate checksum of all files in backup
            for file_path in sorted(backup_path.rglob("*")):
                if file_path.is_file():
                    with open(file_path, "rb") as f:
                        checksum.update(f.read())

            return checksum.hexdigest()

        except Exception as e:
            logger.error(f"Error calculating backup checksum: {e}")
            return ""

    def _update_backup_metadata(
        self, backup_id: str, backup_type: str, result: Dict[str, Any]
    ):
        """Update backup metadata."""
        try:
            if "error" not in result:
                self.backup_metadata["backups"][backup_id] = {
                    "backup_type": backup_type,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "size_bytes": result.get("size_bytes", 0),
                    "duration_seconds": result.get("duration_seconds", 0),
                    "status": "success",
                    "manifest_path": result.get("manifest_path", ""),
                }
            else:
                self.backup_metadata["backups"][backup_id] = {
                    "backup_type": backup_type,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "status": "failed",
                    "error": result.get("error", "Unknown error"),
                }

            self._save_metadata()

        except Exception as e:
            logger.error(f"Error updating backup metadata: {e}")

    def _update_backup_statistics(self, result: Dict[str, Any]):
        """Update backup statistics."""
        try:
            self.backup_stats["total_backups"] += 1

            if "error" not in result:
                self.backup_stats["successful_backups"] += 1
                self.backup_stats["total_size_bytes"] += result.get("size_bytes", 0)
                self.backup_stats["last_backup_time"] = datetime.now(
                    timezone.utc
                ).isoformat()
            else:
                self.backup_stats["failed_backups"] += 1

            # Update metadata statistics
            self.backup_metadata["statistics"] = self.backup_stats
            self._save_metadata()

        except Exception as e:
            logger.error(f"Error updating backup statistics: {e}")

    def _get_last_backup(self) -> Optional[Dict[str, Any]]:
        """Get the last successful backup."""
        try:
            backups = self.backup_metadata.get("backups", {})
            successful_backups = [
                backup
                for backup in backups.values()
                if backup.get("status") == "success"
            ]

            if successful_backups:
                return max(successful_backups, key=lambda x: x.get("timestamp", ""))

            return None

        except Exception as e:
            logger.error(f"Error getting last backup: {e}")
            return None

    def _cleanup_old_backups(self):
        """Clean up old backups based on retention policy."""
        try:
            strategy_config = self.backup_config.get("strategy", {})

            for backup_type, config in strategy_config.items():
                if not config.get("enabled", False):
                    continue

                retention_days = config.get("retention_days", 30)
                cutoff_date = datetime.now(timezone.utc) - timedelta(
                    days=retention_days
                )

                # Find old backups of this type
                backups_to_remove = []
                for backup_id, backup_info in self.backup_metadata.get(
                    "backups", {}
                ).items():
                    if (
                        backup_info.get("backup_type") == backup_type
                        and backup_info.get("status") == "success"
                    ):

                        backup_date = datetime.fromisoformat(
                            backup_info.get("timestamp", "").replace("Z", "+00:00")
                        )
                        if backup_date < cutoff_date:
                            backups_to_remove.append(backup_id)

                # Remove old backups
                for backup_id in backups_to_remove:
                    self._remove_backup(backup_id)

        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")

    def _remove_backup(self, backup_id: str):
        """Remove a specific backup."""
        try:
            # Remove backup directory
            backup_dir = self.local_backup_path / backup_id
            if backup_dir.exists():
                shutil.rmtree(backup_dir)

            # Remove from metadata
            if backup_id in self.backup_metadata.get("backups", {}):
                del self.backup_metadata["backups"][backup_id]
                self._save_metadata()

            logger.info(f"Removed backup: {backup_id}")

        except Exception as e:
            logger.error(f"Error removing backup {backup_id}: {e}")

    def list_backups(self, backup_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """List available backups."""
        try:
            backups = []

            for backup_id, backup_info in self.backup_metadata.get(
                "backups", {}
            ).items():
                if backup_type is None or backup_info.get("backup_type") == backup_type:
                    backup_info["backup_id"] = backup_id
                    backups.append(backup_info)

            # Sort by timestamp (newest first)
            backups.sort(key=lambda x: x.get("timestamp", ""), reverse=True)

            return backups

        except Exception as e:
            logger.error(f"Error listing backups: {e}")
            return []

    def get_backup_info(self, backup_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific backup."""
        try:
            backup_info = self.backup_metadata.get("backups", {}).get(backup_id)
            if not backup_info:
                return {"error": f"Backup {backup_id} not found"}

            # Load manifest if available
            manifest_path = backup_info.get("manifest_path")
            if manifest_path and Path(manifest_path).exists():
                with open(manifest_path, "r") as f:
                    manifest = json.load(f)
                backup_info["manifest"] = manifest

            return backup_info

        except Exception as e:
            logger.error(f"Error getting backup info: {e}")
            return {"error": str(e)}

    def get_status(self) -> Dict[str, Any]:
        """Get backup system status."""
        try:
            total_backups = self.backup_stats.get("total_backups", 0) or 0
            successful_backups = self.backup_stats.get("successful_backups", 0) or 0
            failed_backups = self.backup_stats.get("failed_backups", 0) or 0
            total_size_bytes = self.backup_stats.get("total_size_bytes", 0) or 0
            last_backup_time = self.backup_stats.get("last_backup_time", "")

            return {
                "enabled": self.enabled,
                "backup_path": str(self.local_backup_path),
                "total_backups": total_backups,
                "successful_backups": successful_backups,
                "failed_backups": failed_backups,
                "success_rate": (successful_backups / max(total_backups, 1)) * 100,
                "total_size_bytes": total_size_bytes,
                "last_backup_time": last_backup_time,
                "available_backup_types": list(
                    self.backup_config.get("strategy", {}).keys()
                ),
            }

        except Exception as e:
            logger.error(f"Error getting backup status: {e}")
            return {"error": str(e)}

    def health_check(self) -> Dict[str, Any]:
        """Perform backup system health check."""
        try:
            health_result: Dict[str, Any] = {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "checks": {},
            }

            # Check if backup directory is accessible
            try:
                self.local_backup_path.mkdir(parents=True, exist_ok=True)
                health_result["checks"]["backup_directory"] = "passed"
            except Exception as e:
                health_result["checks"]["backup_directory"] = f"failed: {e}"
                health_result["status"] = "failed"

            # Check if metadata file is accessible
            try:
                self._save_metadata()
                health_result["checks"]["metadata_access"] = "passed"
            except Exception as e:
                health_result["checks"]["metadata_access"] = f"failed: {e}"
                health_result["status"] = "failed"

            # Check backup success rate
            total_backups = self.backup_stats.get("total_backups", 0) or 0
            successful_backups = self.backup_stats.get("successful_backups", 0) or 0
            success_rate = (successful_backups / max(total_backups, 1)) * 100
            if success_rate >= 90:
                health_result["checks"]["success_rate"] = "passed"
            else:
                health_result["checks"][
                    "success_rate"
                ] = f"warning: {success_rate:.1f}%"
                if health_result["status"] == "healthy":
                    health_result["status"] = "degraded"

            return health_result

        except Exception as e:
            logger.error(f"Error in backup health check: {e}")
            return {"status": "failed", "error": str(e)}

    def update_config(self, new_config: Dict[str, Any]):
        """Update backup configuration."""
        try:
            self.config = new_config
            self.backup_config = new_config.get("backup", {})
            self.enabled = self.backup_config.get("enabled", False)

            # Update backup path if changed
            new_backup_path = Path(
                self.backup_config.get("storage", {})
                .get("local", {})
                .get("path", "backups/local")
            )
            if new_backup_path != self.local_backup_path:
                self.local_backup_path = new_backup_path
                self.local_backup_path.mkdir(parents=True, exist_ok=True)
                self.metadata_file = self.local_backup_path / "backup_metadata.json"

            logger.info("Backup configuration updated successfully")

        except Exception as e:
            logger.error(f"Error updating backup configuration: {e}")

    # ========== COMPATIBILITY METHODS ==========
    # These methods provide compatibility with the original BackupManager interfaces

    def create_site_backup(
        self,
        site_path: Union[Path, str],
        site_name: str,
        backup_dir: Optional[str] = None,
    ) -> str:
        """
        Create a backup of a website - compatible with website_generator BackupManager.

        Args:
            site_path: Path to the site directory
            site_name: Name of the site
            backup_dir: Optional backup directory (defaults to configured backup path)

        Returns:
            str: Path to the created backup file
        """
        try:
            site_path = Path(site_path)
            if not site_path.exists():
                raise FileNotFoundError(f"Site path {site_path} not found")

            # Use configured backup directory or provided one
            if backup_dir:
                backup_base = Path(backup_dir)
                backup_base.mkdir(exist_ok=True)
            else:
                backup_base = self.local_backup_path

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{site_name}_{timestamp}.zip"
            backup_path = backup_base / backup_filename

            # Create zip backup
            with zipfile.ZipFile(backup_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for file_path in site_path.rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(site_path)
                        zipf.write(file_path, arcname)

            # Update metadata
            backup_info = {
                "backup_id": backup_filename,
                "site_name": site_name,
                "backup_type": "site_zip",
                "backup_time": datetime.now(timezone.utc).isoformat(),
                "backup_path": str(backup_path),
                "size_bytes": backup_path.stat().st_size,
                "source_path": str(site_path),
            }

            self.backup_metadata["backups"][backup_filename] = backup_info
            self._save_metadata()

            # Update statistics
            self.backup_stats["total_backups"] += 1
            self.backup_stats["successful_backups"] += 1
            self.backup_stats["total_size_bytes"] += backup_info["size_bytes"]
            self.backup_stats["last_backup_time"] = backup_info["backup_time"]

            logger.info(f"Site backup created: {backup_path}")
            return str(backup_path)

        except Exception as e:
            self.backup_stats["failed_backups"] += 1
            logger.error(f"Site backup creation failed: {e}")
            raise

    def restore_site_backup(
        self, backup_path: str, target_path: Union[Path, str]
    ) -> bool:
        """
        Restore a site from backup - compatible with website_generator BackupManager.

        Args:
            backup_path: Path to the backup file
            target_path: Path where to restore the site

        Returns:
            bool: True if restoration successful, False otherwise
        """
        try:
            backup_file = Path(backup_path)
            target_path = Path(target_path)

            if not backup_file.exists():
                raise FileNotFoundError(f"Backup file {backup_path} not found")

            # Remove existing target if it exists
            if target_path.exists():
                shutil.rmtree(target_path)

            target_path.mkdir(parents=True)

            # Extract backup
            with zipfile.ZipFile(backup_file, "r") as zipf:
                zipf.extractall(target_path)

            logger.info(f"Site backup restored to: {target_path}")
            return True

        except Exception as e:
            logger.error(f"Site backup restoration failed: {e}")
            return False

    def create_directory_backup(
        self, site_name: str, sites_dir: Optional[str] = None
    ) -> str:
        """
        Create backup of a site directory - compatible with home_server_hosting BackupManager.

        Args:
            site_name: Name of the site
            sites_dir: Optional sites directory (defaults to 'sites')

        Returns:
            str: Path to the created backup directory
        """
        try:
            if not self.enabled:
                logger.info("Backup disabled in config")
                return ""

            sites_path = Path(sites_dir) if sites_dir else Path("sites")
            site_path = sites_path / site_name

            if not site_path.exists():
                raise FileNotFoundError(f"Site {site_name} not found")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{site_name}_{timestamp}"
            backup_path = self.local_backup_path / backup_name

            # Create directory backup
            shutil.copytree(site_path, backup_path)

            # Create backup metadata
            size_bytes = self._get_directory_size(backup_path)
            metadata = {
                "site_name": site_name,
                "backup_time": datetime.now(timezone.utc).isoformat(),
                "backup_type": "directory_copy",
                "size_bytes": size_bytes,
                "source_path": str(site_path),
            }

            with open(backup_path / "backup_metadata.json", "w") as f:
                json.dump(metadata, f, indent=2)

            # Update main metadata
            self.backup_metadata["backups"][backup_name] = metadata
            self._save_metadata()

            # Update statistics
            self.backup_stats["total_backups"] += 1
            self.backup_stats["successful_backups"] += 1
            self.backup_stats["total_size_bytes"] += size_bytes
            self.backup_stats["last_backup_time"] = metadata["backup_time"]

            logger.info(f"Directory backup created: {backup_path}")
            return str(backup_path)

        except Exception as e:
            self.backup_stats["failed_backups"] += 1
            logger.error(f"Error creating directory backup for {site_name}: {e}")
            raise

    def restore_directory_backup(
        self, backup_path: str, site_name: str, sites_dir: Optional[str] = None
    ) -> bool:
        """
        Restore site from directory backup - compatible with home_server_hosting BackupManager.

        Args:
            backup_path: Path to the backup directory
            site_name: Name of the site to restore
            sites_dir: Optional sites directory (defaults to 'sites')

        Returns:
            bool: True if restoration successful, False otherwise
        """
        try:
            backup_dir = Path(backup_path)
            if not backup_dir.exists():
                raise FileNotFoundError(f"Backup {backup_path} not found")

            sites_path = Path(sites_dir) if sites_dir else Path("sites")
            target_path = sites_path / site_name

            # Remove existing site if it exists
            if target_path.exists():
                shutil.rmtree(target_path)

            # Copy backup to target
            shutil.copytree(backup_dir, target_path)

            # Remove backup metadata file from restored site
            metadata_file = target_path / "backup_metadata.json"
            if metadata_file.exists():
                metadata_file.unlink()

            logger.info(f"Directory backup restored: {site_name}")
            return True

        except Exception as e:
            logger.error(f"Error restoring directory backup: {e}")
            return False

    def _get_directory_size(self, directory: Path) -> int:
        """Calculate total size of directory in bytes."""
        try:
            total_size = 0
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            return total_size
        except Exception as e:
            logger.error(f"Error calculating directory size: {e}")
            return 0

    @classmethod
    def create_simple(cls, backup_dir: str = "backups") -> "BackupManager":
        """Create a simple BackupManager with minimal configuration."""
        config = {
            "backup": {"enabled": True, "storage": {"local": {"path": backup_dir}}}
        }
        return cls(config)

    def restore_backup(self, backup_id: str, target_path: Union[Path, str]) -> bool:
        """Restore a backup to the specified target path."""
        try:
            backup_info = self.get_backup_info(backup_id)
            if not backup_info or "error" in backup_info:
                logger.error(f"Backup {backup_id} not found")
                return False

            backup_path = Path(backup_info["backup_path"])
            if not backup_path.exists():
                logger.error(f"Backup file {backup_path} does not exist")
                return False

            target_path = Path(target_path)
            target_path.mkdir(parents=True, exist_ok=True)

            # Extract backup
            if backup_path.suffix == ".zip":
                import zipfile

                with zipfile.ZipFile(backup_path, "r") as zip_ref:
                    zip_ref.extractall(target_path)
            else:
                # Assume it's a directory
                import shutil

                shutil.copytree(backup_path, target_path, dirs_exist_ok=True)

            logger.info(f"Backup {backup_id} restored to {target_path}")
            return True

        except Exception as e:
            logger.error(f"Error restoring backup {backup_id}: {e}")
            return False

    def cleanup_old_backups(self, keep_days: int = 30) -> int:
        """Clean up old backups, keeping only those within the specified days."""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=keep_days)
            cleaned_count = 0

            for backup_id, backup_info in list(self.backup_metadata["backups"].items()):
                # Use backup_time field instead of created_at
                backup_time = backup_info.get(
                    "backup_time", backup_info.get("created_at")
                )
                if backup_time:
                    try:
                        # Handle timezone-aware and naive datetimes
                        if "Z" in backup_time:
                            backup_time = backup_time.replace("Z", "+00:00")
                        backup_date = datetime.fromisoformat(backup_time)
                        # Make timezone-aware if it's naive
                        if backup_date.tzinfo is None:
                            backup_date = backup_date.replace(tzinfo=timezone.utc)
                        if backup_date < cutoff_date:
                            self._remove_backup(backup_id)
                            cleaned_count += 1
                    except Exception as e:
                        logger.warning(
                            f"Could not parse backup time for {backup_id}: {e}"
                        )
                        continue

            logger.info(f"Cleaned up {cleaned_count} old backups")
            return cleaned_count

        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")
            return 0
