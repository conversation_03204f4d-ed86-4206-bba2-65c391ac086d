name: Cursor Rules Validation
true:
  pull_request:
    branches:
    - main
    - develop
  push:
    branches:
    - main
    - develop
  schedule:
  - cron: 0 2 * * *
jobs:
  validate-cursor-rules:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install dependencies
      run: 'python -m pip install --upgrade pip

        pip install -r config/requirements.txt

        pip install -r config/requirements-dev.txt

        '
    - name: Validate Cursor Rules
      run: "python - <<'PYCODE'\nimport sys\nfrom pathlib import Path\n\n# Add project\
        \ root to path\nproject_root = Path.cwd()\nsys.path.insert(0, str(project_root))\n\
        \ntry:\n    from core.utils.cursor_rules_validator import assert_rules_loaded\n\
        \    from core.agents.cursor_agent import CursorAgent\n\n    print(\"\U0001F50D\
        \ Validating cursor rules...\")\n\n    # Check rules loading\n    assert_rules_loaded()\n\
        \    print(\"\u2705 Cursor rules are properly loaded\")\n\n    # Check CursorAgent\
        \ integration\n    agent = CursorAgent()\n    rules_status = agent.get_rules_status()\n\
        \n    if rules_status[\"rules_loaded\"]:\n        print(\"\u2705 CursorAgent\
        \ integration successful\")\n        print(f\"\U0001F4CB Rules path: {rules_status['rules_path']}\"\
        )\n        print(f\"\U0001F916 Model: {rules_status['model_name']}\")\n  \
        \      print(f\"\U0001F527 Enforcement enabled: {rules_status['enforcement_enabled']}\"\
        )\n    else:\n        print(\"\u274C CursorAgent integration failed\")\n \
        \       sys.exit(1)\n\n    print(\"\U0001F389 Cursor rules validation completed\
        \ successfully!\")\n\nexcept Exception as e:\n    print(f\"\u274C Cursor rules\
        \ validation failed: {e}\")\n    sys.exit(1)\nPYCODE\n"
    - name: Run Cursor Rules Check Script
      run: 'python scripts/cursor_rules_check.py

        '
    - name: Validate Virtual Environment Usage
      run: 'python scripts/validate_venv_usage.py --verbose

        '
    - name: Test CursorAgent Functionality
      run: "python - <<'PYCODE'\nimport sys\nfrom pathlib import Path\n\n# Add project\
        \ root to path\nproject_root = Path.cwd()\nsys.path.insert(0, str(project_root))\n\
        \ntry:\n    from core.agents.cursor_agent import CursorAgent\n\n    print(\"\
        \U0001F9EA Testing CursorAgent functionality...\")\n\n    # Create agent instance\n\
        \    agent = CursorAgent()\n\n    # Test rules status\n    status = agent.get_rules_status()\n\
        \    print(f\"\U0001F4CA Rules status: {status}\")\n\n    # Test task validation\n\
        \    test_task = \"Create a new Python function for data processing\"\n  \
        \  validation = agent._validate_task_compliance(test_task)\n    print(f\"\U0001F50D\
        \ Task validation: {validation}\")\n\n    print(\"\u2705 CursorAgent functionality\
        \ test completed!\")\n\nexcept Exception as e:\n    print(f\"\u274C CursorAgent\
        \ functionality test failed: {e}\")\n    sys.exit(1)\nPYCODE\n"
    - name: Generate Compliance Report
      run: "python - <<'PYCODE'\nimport sys\nimport json\nfrom pathlib import Path\n\
        \n# Add project root to path\nproject_root = Path.cwd()\nsys.path.insert(0,\
        \ str(project_root))\n\ntry:\n    from core.utils.cursor_rules_validator import\
        \ generate_compliance_report\n\n    print(\"\U0001F4CA Generating compliance\
        \ report...\")\n\n    report = generate_compliance_report()\n\n    # Save\
        \ report to file\n    report_path = Path(\"cursor-compliance-report.json\"\
        )\n    with open(report_path, 'w') as f:\n        json.dump(report, f, indent=2)\n\
        \n    print(f\"\U0001F4C4 Compliance report saved to: {report_path}\")\n \
        \   print(f\"\U0001F3AF Overall compliance: {'\u2705 PASS' if report['overall_compliance']\
        \ else '\u274C FAIL'}\")\n\n    if not report['overall_compliance']:\n   \
        \     print(\"\u274C Compliance check failed!\")\n        sys.exit(1)\n\n\
        except Exception as e:\n    print(f\"\u274C Failed to generate compliance\
        \ report: {e}\")\n    sys.exit(1)\nPYCODE\n"
    - if: always()
      name: Upload Compliance Report
      uses: actions/upload-artifact@v3
      with:
        name: cursor-compliance-report
        path: cursor-compliance-report.json
    - if: github.event_name == 'pull_request'
      name: Comment on PR
      uses: actions/github-script@v6
      with:
        script: "const fs = require('fs');\n\ntry {\n  const report = JSON.parse(fs.readFileSync('cursor-compliance-report.json',\
          \ 'utf8'));\n\n  const comment = `## \U0001F3AF Cursor Rules Compliance\
          \ Check\n\n  **Status**: ${report.overall_compliance ? '\u2705 PASS' : '\u274C\
          \ FAIL'}\n\n  ### \U0001F4CA Compliance Details\n\n  - **Rules Loading**:\
          \ ${report.rules_loading.status}\n  - **Agent Integration**: ${report.agent_integration.status}\n\
          \  - **Cleanup Rules**: ${report.cleanup_rules.status}\n  - **File Structure**:\
          \ ${report.file_structure.status}\n\n  ### \U0001F4C5 Check Completed\n\
          \  ${report.timestamp}\n\n  ${report.overall_compliance ? '\U0001F389 All\
          \ cursor rules compliance checks passed!' : '\u26A0\uFE0F Some compliance\
          \ issues were found. Please review the details above.'}\n  `;\n\n  github.rest.issues.createComment({\n\
          \    issue_number: context.issue.number,\n    owner: context.repo.owner,\n\
          \    repo: context.repo.repo,\n    body: comment\n  });\n} catch (error)\
          \ {\n  console.error('Failed to create comment:', error);\n}\n"
'on':
- push
- pull_request
