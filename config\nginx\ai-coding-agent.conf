# AI Coding Agent Nginx Configuration
# This file should be placed in /etc/nginx/sites-available/ on Ubuntu/Debian systems
# or in /etc/nginx/conf.d/ on RHEL/CentOS systems

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    
    server_name aicodingagent.local your-domain.com;  # Replace with your domain
    
    # Redirect all HTTP requests to HTTPS with a 301 Moved Permanently response.
    return 301 https://$host$request_uri;
}

# HTTPS server configuration
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    server_name aicodingagent.local your-domain.com;  # Replace with your domain
    
    # SSL Configuration - Use Let's Encrypt certificates
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;  # Update path
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;  # Update path
    
    # Enable session resumption to improve performance
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    # Modern configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # HSTS (ngx_http_headers_module is required) (63072000 seconds = 2 years)
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # Security headers
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Logging
    access_log /var/log/nginx/ai-coding-agent-access.log;
    error_log /var/log/nginx/ai-coding-agent-error.log;
    
    # Proxy configuration
    location / {
        proxy_pass http://localhost:5000;  # Update port if your app runs on a different one
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Increase timeouts if needed
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # Static files (if serving any)
    location /static/ {
        alias /path/to/your/static/files/;  # Update path
        expires 30d;
        access_log off;
    }
    
    # Deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    location ~ /\.ht {
        deny all;
    }
    
    # Deny access to hidden files and directories
    location ~ /(\.|~$|#) {
        deny all;
    }
}

# Rate limiting configuration
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

# API endpoint rate limiting
location /api/ {
    limit_req zone=api_limit burst=20 nodelay;
    # Rest of your proxy configuration for /api/
    proxy_pass http://localhost:5000;
    # ... other proxy settings ...
}
