[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-coding-agent"
version = "0.1.0"
description = "AI-powered coding agent for automated website generation and deployment"
authors = [
    {name = "AI Coding Agent Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "Flask>=3.0.0",
    "Flask-SocketIO>=5.3.6",
    "python-dotenv>=1.0.0",
    "watchdog>=3.0.0",
    "gitpython>=3.1.41",
    "requests>=2.31.0",
    "python-jose>=3.3.0",
    "PyJWT>=2.8.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.12.1",
    "isort>=5.13.2",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pylint>=3.0.3",
    "bandit>=1.7.5",
    "safety>=2.3.5",
    "pytest>=7.4.4",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-asyncio>=0.23.2",
    "pytest-html>=4.1.1",
    "pytest-xdist>=3.5.0",
    "pre-commit>=3.6.0",
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=2.0.0",
    "myst-parser>=2.0.0",
    "memory-profiler>=0.61.0",
    "line-profiler>=4.1.2",
    "pydantic>=2.5.2",
    "pydantic-settings>=2.1.0",
    "ipython>=8.18.1",
    "jupyter>=1.0.0",
    "notebook>=7.0.6",
]

[project.scripts]
ai-coding-agent = "src.__main__:main"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["flask", "flask_socketio", "git", "requests", "jose", "jwt", "watchdog"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "docs"]
skips = ["B101", "B601"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
