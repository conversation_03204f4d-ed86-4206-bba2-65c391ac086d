#!/usr/bin/env python3
"""
Code Generation Module - AST-based advanced code generation system

This module provides comprehensive code generation capabilities using Abstract Syntax Trees
for precise, maintainable, and syntactically correct code generation across multiple languages.

Components:
- ASTCodeGenerator: Main code generation engine
- ASTManipulator: Language-specific AST operations
- CodeAnalyzer: Context extraction from existing code
- CodePatternMatcher: Pattern identification and matching
- Language Support: Python, TypeScript, JavaScript AST manipulation
"""

from core.code_generation.ast_code_generator import ASTCodeGenerator
from core.code_generation.ast_manipulator import ASTManipulator
from core.code_generation.code_analyzer import CodeAnalyzer
from core.code_generation.pattern_matcher import CodePatternMatcher

__version__ = "1.0.0"
__author__ = "AI Coding Agent"
__description__ = "AST-based advanced code generation system"

__all__ = ["ASTCodeGenerator", "ASTManipulator", "CodeAnalyzer", "CodePatternMatcher"]
