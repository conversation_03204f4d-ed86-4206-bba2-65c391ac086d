#!/usr/bin/env python3
"""
Advanced Learning Enhancements API Routes

Provides REST API endpoints for the Advanced Learning Enhancements system.
"""

import asyncio
import logging
from typing import Any, Dict, Optional, Union

from fastapi import APIRouter, Depends, HTTPException

# Import AIAgent type for type hints
try:
    from core.agent import AIAgent
except ImportError:
    # Create a type alias for when AIAgent is not available
    AIAgent = Any

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/advanced-learning", tags=["Advanced Learning Enhancements"])


def get_agent():
    """Dependency to get the AI agent instance"""
    # Get the global agent from the agent dependency module
    try:
        from api.agent_dependency import get_global_agent

        agent = get_global_agent()
        if agent is None:
            raise ValueError("Global agent not initialized")
        return agent
    except Exception as e:
        logger.error(f"Failed to get global agent: {e}")

        # Create mock agent for testing
        class MockAIAgent:
            async def run_enhancement_cycle(self, args):
                return {"success": True, "message": "Mock enhancement cycle"}

            async def get_enhancement_status(self, args):
                return {"success": True, "status": "mock"}

            async def enable_enhancement(self, args):
                return {"success": True, "message": "Mock enable"}

            async def disable_enhancement(self, args):
                return {"success": True, "message": "Mock disable"}

            async def get_meta_learning_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_pareto_optimization_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_workload_prediction_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_cascade_prediction_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_federated_learning_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_capability_discovery_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_adversarial_detection_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_degradation_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_causal_analysis_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_business_impact_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_quantum_ready_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_nas_integration_status(self, args):
                return {"success": True, "status": "mock"}

            async def get_learning_summary(self, args):
                return {"success": True, "summary": "Mock learning summary"}

            async def get_learning_recommendations(self, args):
                return {"success": True, "recommendations": []}

            async def record_learning_event(self, args):
                return {"success": True, "message": "Mock event recorded"}

            async def learn_code_pattern(self, args):
                return {"success": True, "message": "Mock pattern learned"}

            async def learn_user_preference(self, args):
                return {"success": True, "message": "Mock preference learned"}

            async def learn_performance_insight(self, args):
                return {"success": True, "message": "Mock insight learned"}

        return MockAIAgent()


@router.post("/run-cycle")
async def run_enhancement_cycle(agent: AIAgent = Depends(get_agent)):
    """Run a complete enhancement cycle"""
    try:
        result = await agent.run_enhancement_cycle({})
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error running enhancement cycle: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status")
async def get_enhancement_status(agent: AIAgent = Depends(get_agent)):
    """Get status of all enhancement components"""
    try:
        result = await agent.get_enhancement_status({})
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error getting enhancement status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/enable/{enhancement_name}")
async def enable_enhancement(
    enhancement_name: str, agent: AIAgent = Depends(get_agent)
):
    """Enable a specific enhancement"""
    try:
        result = await agent.enable_enhancement({"enhancement_name": enhancement_name})
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error enabling enhancement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/disable/{enhancement_name}")
async def disable_enhancement(
    enhancement_name: str, agent: AIAgent = Depends(get_agent)
):
    """Disable a specific enhancement"""
    try:
        result = await agent.disable_enhancement({"enhancement_name": enhancement_name})
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error disabling enhancement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Automated Learning API Endpoints
@router.get("/learning/summary")
async def get_learning_summary(agent: AIAgent = Depends(get_agent)):
    """Get automated learning summary"""
    try:
        result = await agent.get_learning_summary({})
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error getting learning summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/learning/recommendations")
async def get_learning_recommendations(agent: AIAgent = Depends(get_agent)):
    """Get learning recommendations"""
    try:
        result = await agent.get_learning_recommendations({})
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error getting learning recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/learning/event")
async def record_learning_event(
    event_type: str,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    data: Optional[Dict[str, Any]] = None,
    outcome: str = "unknown",
    feedback_score: Optional[float] = None,
    agent: AIAgent = Depends(get_agent),
):
    """Record a learning event"""
    try:
        args = {
            "event_type": event_type,
            "user_id": user_id,
            "session_id": session_id,
            "context": context or {},
            "data": data or {},
            "outcome": outcome,
            "feedback_score": feedback_score,
        }
        result = await agent.record_learning_event(args)
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error recording learning event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/learning/code-pattern")
async def learn_code_pattern(
    code: str,
    language: str,
    context: str = "",
    outcome: str = "success",
    feedback_score: Optional[float] = None,
    agent: AIAgent = Depends(get_agent),
):
    """Learn from code generation"""
    try:
        args = {
            "code": code,
            "language": language,
            "context": context,
            "outcome": outcome,
            "feedback_score": feedback_score,
        }
        result = await agent.learn_code_pattern(args)
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error learning code pattern: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/learning/user-preference")
async def learn_user_preference(
    user_id: str,
    preference_type: str,
    preference_key: str,
    preference_value: Any,
    confidence: float = 1.0,
    agent: AIAgent = Depends(get_agent),
):
    """Learn user preference"""
    try:
        args = {
            "user_id": user_id,
            "preference_type": preference_type,
            "preference_key": preference_key,
            "preference_value": preference_value,
            "confidence": confidence,
        }
        result = await agent.learn_user_preference(args)
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error learning user preference: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/learning/performance-insight")
async def learn_performance_insight(
    component: str,
    metric: str,
    value: float,
    threshold: float,
    trend: str,
    agent: AIAgent = Depends(get_agent),
):
    """Learn performance insight"""
    try:
        args = {
            "component": component,
            "metric": metric,
            "value": value,
            "threshold": threshold,
            "trend": trend,
        }
        result = await agent.learn_performance_insight(args)
        if result.get("success"):
            return result
        else:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Unknown error")
            )
    except Exception as e:
        logger.error(f"Error learning performance insight: {e}")
        raise HTTPException(status_code=500, detail=str(e))
