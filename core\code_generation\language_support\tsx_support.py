#!/usr/bin/env python3
"""
TSX AST Support for Code Generation

This module provides TSX-specific AST manipulation and code generation support.
"""

import ast
import logging
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

from core.code_generation.language_support.base_language_support import (
    ASTNodeInfo,
    LanguageType,
)
from core.code_generation.language_support.typescript_support import (
    TypeScriptASTSupport,
    TypeScriptClassInfo,
    TypeScriptFunctionInfo,
)

logger = logging.getLogger(__name__)


@dataclass
class TSXComponentInfo:
    """Information about a TSX component"""

    name: str
    props_interface: str = None
    props: List[str] = None
    children: List[str] = None
    is_functional: bool = True
    is_export: bool = False
    hooks: List[str] = None
    styled_components: List[str] = None
    generics: List[str] = None


class TSXASTSupport(TypeScriptASTSupport):
    """
    TSX-specific AST support implementation.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.language = LanguageType.TSX

    async def create_base_ast(self) -> ast.AST:
        """Create a base TSX AST"""
        # Create a module with React and TypeScript imports
        module = ast.Module(
            body=[
                ast.ImportFrom(
                    module="react",
                    names=[ast.alias(name="React", asname=None)],
                    level=0,
                ),
                ast.ImportFrom(
                    module="react", names=[ast.alias(name="FC", asname=None)], level=0
                ),
            ],
            type_ignores=[],
        )
        return module

    async def add_react_component(
        self,
        ast_tree: ast.AST,
        component_name: str,
        props_interface: str = None,
        props: List[str] = None,
        children: List[str] = None,
    ) -> ast.AST:
        """Add React component to TSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Add props interface if provided
        if props_interface:
            await self.add_interface(
                ast_tree,
                props_interface,
                [{"name": prop, "type": "string"} for prop in (props or [])],
            )

        # Create component function with TypeScript typing
        props_param = f"{props_interface}" if props_interface else "any"
        component_body = [
            ast.Return(
                value=ast.Call(
                    func=ast.Name(id="React.createElement"),
                    args=[
                        ast.Constant(value=component_name),
                        ast.Name(id="props"),
                        *[ast.Constant(value=child) for child in (children or [])],
                    ],
                    keywords=[],
                )
            )
        ]

        # Create function definition with TypeScript typing
        component_def = ast.FunctionDef(
            name=component_name,
            args=ast.arguments(
                posonlyargs=[],
                args=[ast.arg(arg="props", annotation=ast.Name(id=props_param))],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=component_body,
            decorator_list=[],
            returns=ast.Name(id="JSX.Element"),
        )

        ast_tree.body.append(component_def)
        return ast_tree

    async def add_functional_component(
        self, ast_tree: ast.AST, component_name: str, props_interface: str = None
    ) -> ast.AST:
        """Add functional React component to TSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create FC type
        props_type = f"{props_interface}" if props_interface else "{}"
        fc_type = f"FC<{props_type}>"

        # Create component
        component_def = ast.Assign(
            targets=[ast.Name(id=component_name)],
            value=ast.Call(
                func=ast.Name(id="FC"),
                args=[],
                keywords=[ast.keyword(arg="props", value=ast.Name(id="props"))],
            ),
        )

        ast_tree.body.append(component_def)
        return ast_tree

    async def add_hook(
        self,
        ast_tree: ast.AST,
        hook_name: str,
        hook_type: str,
        parameters: List[str] = None,
        return_type: str = None,
    ) -> ast.AST:
        """Add React hook to TSX AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Import hook if needed
        await self.add_import(ast_tree, f"import {{ {hook_name} }} from 'react'")

        # Create hook usage with typing
        hook_call = ast.Call(
            func=ast.Name(id=hook_name),
            args=[ast.Constant(value=param) for param in (parameters or [])],
            keywords=[],
        )

        # Create variable assignment with type annotation
        hook_var = ast.AnnAssign(
            target=ast.Name(id=f"{hook_name.lower()}_result"),
            annotation=ast.Name(id=return_type or "any"),
            value=hook_call,
            simple=1,
        )

        ast_tree.body.append(hook_var)
        return ast_tree

    async def add_styled_component(
        self,
        ast_tree: ast.AST,
        component_name: str,
        styles: str,
        props_interface: str = None,
    ) -> ast.AST:
        """Add styled component to TSX AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Import styled-components if needed
        await self.add_import(ast_tree, "import styled from 'styled-components'")

        # Create styled component with typing
        styled_component = ast.AnnAssign(
            target=ast.Name(id=component_name),
            annotation=ast.Name(
                id=f'StyledComponent<"div", {props_interface or "any"}>'
            ),
            value=ast.Call(
                func=ast.Attribute(value=ast.Name(id="styled"), attr="div"),
                args=[],
                keywords=[ast.keyword(arg="css", value=ast.Constant(value=styles))],
            ),
            simple=1,
        )

        ast_tree.body.append(styled_component)
        return ast_tree

    async def add_tsx_element(
        self,
        ast_tree: ast.AST,
        element_name: str,
        attributes: Dict[str, str] = None,
        children: List[str] = None,
        element_type: str = None,
    ) -> ast.AST:
        """Add TSX element to AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create TSX element with typing
        element = ast.Call(
            func=ast.Name(id=element_name),
            args=[ast.Constant(value=child) for child in (children or [])],
            keywords=[
                ast.keyword(arg=key, value=ast.Constant(value=value))
                for key, value in (attributes or {}).items()
            ],
        )

        if element_type:
            element = ast.AnnAssign(
                target=ast.Name(id=f"{element_name}_element"),
                annotation=ast.Name(id=element_type),
                value=element,
                simple=1,
            )

        ast_tree.body.append(element)
        return ast_tree

    async def add_event_handler(
        self,
        ast_tree: ast.AST,
        event_name: str,
        handler_name: str,
        handler_body: str,
        event_type: str = None,
    ) -> ast.AST:
        """Add event handler to TSX AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create handler function with typing
        event_param_type = event_type or "React.MouseEvent<HTMLElement>"
        handler_def = ast.FunctionDef(
            name=handler_name,
            args=ast.arguments(
                posonlyargs=[],
                args=[ast.arg(arg="event", annotation=ast.Name(id=event_param_type))],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=[ast.Expr(value=ast.parse(handler_body, mode="eval"))],
            decorator_list=[],
            returns=ast.Name(id="void"),
        )

        ast_tree.body.append(handler_def)
        return ast_tree

    async def add_props_interface(
        self, ast_tree: ast.AST, interface_name: str, properties: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add TypeScript interface for props"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create interface body
        interface_body = []
        for prop in properties:
            prop_name = prop.get("name", "")
            prop_type = prop.get("type", "any")
            prop_optional = prop.get("optional", False)

            # Create property signature
            prop_sig = ast.AnnAssign(
                target=ast.Name(id=prop_name),
                annotation=ast.Name(id=prop_type),
                value=None,
                simple=1,
            )
            interface_body.append(prop_sig)

        # Create interface definition
        interface_def = ast.ClassDef(
            name=interface_name,
            bases=[],
            keywords=[],
            body=interface_body,
            decorator_list=[],
        )

        ast_tree.body.append(interface_def)
        return ast_tree

    async def add_state_management(
        self,
        ast_tree: ast.AST,
        state_name: str,
        initial_value: str,
        state_type: str = None,
    ) -> ast.AST:
        """Add React state management to TSX AST with TypeScript typing"""
        return await self.add_hook(
            ast_tree,
            "useState",
            "state",
            [initial_value],
            f'[{state_type or "any"}, React.Dispatch<React.SetStateAction<{state_type or "any"}>>]',
        )

    async def add_effect(
        self,
        ast_tree: ast.AST,
        effect_name: str,
        effect_body: str,
        dependencies: List[str] = None,
    ) -> ast.AST:
        """Add React useEffect to TSX AST"""
        return await self.add_hook(
            ast_tree, "useEffect", "effect", dependencies or [], "void"
        )

    async def add_context(
        self,
        ast_tree: ast.AST,
        context_name: str,
        default_value: str,
        context_type: str = None,
    ) -> ast.AST:
        """Add React context to TSX AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Import createContext
        await self.add_import(ast_tree, "import { createContext } from 'react'")

        # Create context with typing
        context_def = ast.AnnAssign(
            target=ast.Name(id=context_name),
            annotation=ast.Name(id=f'React.Context<{context_type or "any"}>'),
            value=ast.Call(
                func=ast.Name(id="createContext"),
                args=[ast.Constant(value=default_value)],
                keywords=[],
            ),
            simple=1,
        )

        ast_tree.body.append(context_def)
        return ast_tree

    async def add_provider(
        self,
        ast_tree: ast.AST,
        provider_name: str,
        context_name: str,
        value: str,
        provider_type: str = None,
    ) -> ast.AST:
        """Add React context provider to TSX AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create provider component with typing
        provider_body = [
            ast.Return(
                value=ast.Call(
                    func=ast.Attribute(
                        value=ast.Name(id=context_name), attr="Provider"
                    ),
                    args=[],
                    keywords=[ast.keyword(arg="value", value=ast.Name(id=value))],
                )
            )
        ]

        provider_def = ast.FunctionDef(
            name=provider_name,
            args=ast.arguments(
                posonlyargs=[],
                args=[
                    ast.arg(arg="children", annotation=ast.Name(id="React.ReactNode"))
                ],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=provider_body,
            decorator_list=[],
            returns=ast.Name(id="JSX.Element"),
        )

        ast_tree.body.append(provider_def)
        return ast_tree

    async def add_custom_hook(
        self,
        ast_tree: ast.AST,
        hook_name: str,
        parameters: List[str],
        body: str,
        return_type: str = None,
    ) -> ast.AST:
        """Add custom React hook to TSX AST with TypeScript typing"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse hook body
        try:
            body_ast = ast.parse(body)
            if isinstance(body_ast, ast.Module):
                hook_body = body_ast.body
            else:
                hook_body = [body_ast]
        except SyntaxError:
            hook_body = [ast.Pass()]

        # Create hook arguments with typing
        args = []
        for param in parameters:
            if ":" in param:
                name, param_type = param.split(":", 1)
                args.append(
                    ast.arg(
                        arg=name.strip(), annotation=ast.Name(id=param_type.strip())
                    )
                )
            else:
                args.append(ast.arg(arg=param.strip()))

        # Create hook definition with typing
        hook_def = ast.FunctionDef(
            name=hook_name,
            args=ast.arguments(
                posonlyargs=[], args=args, kwonlyargs=[], defaults=[], kw_defaults=[]
            ),
            body=hook_body,
            decorator_list=[],
            returns=ast.Name(id=return_type or "any"),
        )

        ast_tree.body.append(hook_def)
        return ast_tree

    async def add_generic_component(
        self,
        ast_tree: ast.AST,
        component_name: str,
        generic_type: str,
        props_interface: str = None,
    ) -> ast.AST:
        """Add generic React component to TSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create generic component
        props_type = (
            f"{props_interface}<{generic_type}>"
            if props_interface
            else f"{{ data: {generic_type} }}"
        )

        component_def = ast.FunctionDef(
            name=f"{component_name}<{generic_type}>",
            args=ast.arguments(
                posonlyargs=[],
                args=[ast.arg(arg="props", annotation=ast.Name(id=props_type))],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=[ast.Pass()],
            decorator_list=[],
            returns=ast.Name(id="JSX.Element"),
        )

        ast_tree.body.append(component_def)
        return ast_tree

    def _convert_to_tsx(self, typescript_code: str) -> str:
        """Convert TypeScript code to TSX"""
        # This is a simplified conversion
        # In practice, you'd need a more sophisticated converter

        tsx_code = typescript_code

        # Convert React.createElement to JSX syntax
        tsx_code = re.sub(
            r"React\.createElement<(\w+)>\((\w+), (\w+), (.*)\)",
            r"<\1 {...\3}>\4</\1>",
            tsx_code,
        )

        # Convert function components to TSX
        tsx_code = re.sub(
            r"function (\w+)<(\w+)>\(props: (\w+)\): JSX\.Element {",
            r"function \1<\2>(props: \3): JSX.Element {",
            tsx_code,
        )

        return tsx_code

    async def ast_to_code(self, ast_tree: ast.AST) -> str:
        """Convert TSX AST back to code string"""
        try:
            # Use parent method first
            code = await super().ast_to_code(ast_tree)
            # Convert to TSX syntax
            return self._convert_to_tsx(code)
        except Exception as e:
            logger.error(f"Failed to convert AST to TSX code: {e}")
            raise
