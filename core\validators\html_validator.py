"""
HTMLValidator - Unified HTML validation and compliance checking.
Consolidates multiple HTMLValidator implementations into a single comprehensive solution.
"""

import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional


class HTMLValidator:
    """HTML validation and compliance checking"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.rules = self._default_rules()

    def _default_rules(self) -> Dict[str, Any]:
        """Default HTML validation rules"""
        return {
            "html_structure": {
                "required_tags": ["html", "head", "body"],
                "max_depth": 10,
                "allow_iframes": False,
            },
            "accessibility": {
                "aria_required": True,
                "alt_text_required": True,
                "skip_links": True,
            },
            "performance": {
                "max_css_size": 100,
                "max_js_size": 200,
                "min_meta_tags": 5,
            },
            "security": {
                "no_inline_scripts": True,
                "no_external_scripts": False,
                "sanitize_attributes": True,
            },
        }

    def validate_html_file(self, file_path: str) -> Dict[str, Any]:
        """Validate HTML file against rules"""
        try:
            html_content = Path(file_path).read_text(encoding="utf-8")
            validation_results = self.validate_html_content(html_content)

            return {
                "test_name": f"HTML_Validation_{Path(file_path).stem}",
                "test_type": "html_validation",
                "status": "passed" if validation_results["valid"] else "failed",
                "duration": validation_results["duration"],
                "timestamp": datetime.now(),
                "details": validation_results,
                "error_message": validation_results.get("error"),
                "file_path": file_path,
            }
        except Exception as e:
            return {
                "test_name": f"HTML_Validation_{Path(file_path).stem}",
                "test_type": "html_validation",
                "status": "error",
                "duration": 0,
                "timestamp": datetime.now(),
                "details": {"file_path": file_path},
                "error_message": str(e),
                "file_path": file_path,
            }

    def validate_html_content(self, html_content: str) -> Dict[str, Any]:
        """Validate HTML content"""
        start_time = datetime.now()
        issues = []

        try:
            # Basic structure validation
            structure_issues = self._validate_structure(html_content)
            issues.extend(structure_issues)

            # Accessibility validation
            accessibility_issues = self._validate_accessibility(html_content)
            issues.extend(accessibility_issues)

            # Security validation
            security_issues = self._validate_security(html_content)
            issues.extend(security_issues)

            # Performance validation
            performance_issues = self._validate_performance(html_content)
            issues.extend(performance_issues)

            duration = (datetime.now() - start_time).total_seconds()

            return {
                "valid": len(issues) == 0,
                "duration": duration,
                "issues": issues,
                "error": None,
                "stats": {
                    "total_issues": len(issues),
                    "structure_issues": len(structure_issues),
                    "accessibility_issues": len(accessibility_issues),
                    "security_issues": len(security_issues),
                    "performance_issues": len(performance_issues),
                },
            }

        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            return {
                "valid": False,
                "duration": duration,
                "issues": issues,
                "error": str(e),
            }

    def _validate_structure(self, html_content: str) -> List[Dict[str, Any]]:
        """Validate HTML structure"""
        issues = []

        # Check for required tags
        required_tags = self.rules["html_structure"]["required_tags"]
        for tag in required_tags:
            if f"<{tag}" not in html_content.lower():
                issues.append(
                    {
                        "type": "structure",
                        "severity": "error",
                        "message": f"Missing required tag: <{tag}>",
                        "rule": "required_tags",
                    }
                )

        # Check for proper DOCTYPE
        if not html_content.strip().lower().startswith("<!doctype"):
            issues.append(
                {
                    "type": "structure",
                    "severity": "warning",
                    "message": "Missing DOCTYPE declaration",
                    "rule": "doctype",
                }
            )

        # Check for nested depth (simplified)
        max_depth = self.rules["html_structure"]["max_depth"]
        nesting_level = html_content.count("<div") + html_content.count("<span")
        if nesting_level > max_depth:
            issues.append(
                {
                    "type": "structure",
                    "severity": "warning",
                    "message": f"Deep nesting detected: {nesting_level} levels",
                    "rule": "max_depth",
                }
            )

        return issues

    def _validate_accessibility(self, html_content: str) -> List[Dict[str, Any]]:
        """Validate accessibility features"""
        issues = []

        # Check for alt text on images
        if self.rules["accessibility"]["alt_text_required"]:
            img_tags = re.findall(r"<img[^>]*>", html_content, re.IGNORECASE)
            for img_tag in img_tags:
                if "alt=" not in img_tag:
                    issues.append(
                        {
                            "type": "accessibility",
                            "severity": "error",
                            "message": "Image missing alt attribute",
                            "rule": "alt_text_required",
                        }
                    )

        # Check for proper heading hierarchy
        headings = re.findall(r"<h([1-6])[^>]*>", html_content, re.IGNORECASE)
        if headings:
            heading_levels = [int(h) for h in headings]
            if heading_levels and heading_levels[0] != 1:
                issues.append(
                    {
                        "type": "accessibility",
                        "severity": "warning",
                        "message": "Page should start with h1",
                        "rule": "heading_hierarchy",
                    }
                )

        return issues

    def _validate_security(self, html_content: str) -> List[Dict[str, Any]]:
        """Validate security aspects"""
        issues = []

        # Check for inline scripts
        if self.rules["security"]["no_inline_scripts"]:
            if re.search(
                r"<script[^>]*>[^<]*</script>", html_content, re.IGNORECASE | re.DOTALL
            ):
                issues.append(
                    {
                        "type": "security",
                        "severity": "warning",
                        "message": "Inline scripts detected",
                        "rule": "no_inline_scripts",
                    }
                )

        # Check for dangerous attributes
        dangerous_attrs = ["onclick", "onload", "onerror", "javascript:"]
        for attr in dangerous_attrs:
            if attr in html_content.lower():
                issues.append(
                    {
                        "type": "security",
                        "severity": "error",
                        "message": f"Potentially dangerous attribute: {attr}",
                        "rule": "sanitize_attributes",
                    }
                )

        return issues

    def _validate_performance(self, html_content: str) -> List[Dict[str, Any]]:
        """Validate performance aspects"""
        issues = []

        # Check for meta tags
        meta_tags = re.findall(r"<meta[^>]*>", html_content, re.IGNORECASE)
        min_meta_tags = self.rules["performance"]["min_meta_tags"]
        if len(meta_tags) < min_meta_tags:
            issues.append(
                {
                    "type": "performance",
                    "severity": "info",
                    "message": f"Only {len(meta_tags)} meta tags found, recommended: {min_meta_tags}",
                    "rule": "min_meta_tags",
                }
            )

        # Check for large inline CSS/JS
        style_tags = re.findall(
            r"<style[^>]*>.*?</style>", html_content, re.IGNORECASE | re.DOTALL
        )
        for style in style_tags:
            if len(style) > self.rules["performance"]["max_css_size"] * 1024:
                issues.append(
                    {
                        "type": "performance",
                        "severity": "warning",
                        "message": f"Large inline CSS detected: {len(style)} characters",
                        "rule": "max_css_size",
                    }
                )

        return issues

    def validate_multiple_files(self, file_paths: List[str]) -> Dict[str, Any]:
        """Validate multiple HTML files"""
        results = []
        total_issues = 0

        for file_path in file_paths:
            result = self.validate_html_file(file_path)
            results.append(result)
            if result.get("details", {}).get("issues"):
                total_issues += len(result["details"]["issues"])

        return {
            "total_files": len(file_paths),
            "total_issues": total_issues,
            "results": results,
            "summary": {
                "passed": len([r for r in results if r["status"] == "passed"]),
                "failed": len([r for r in results if r["status"] == "failed"]),
                "errors": len([r for r in results if r["status"] == "error"]),
            },
        }
