networks:
  ai-coding-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: ai-coding-bridge
    ipam:
      config:
      - subnet: **********/16
services:
  api:
    build:
      context: ..
      dockerfile: containers/Dockerfile.api
      target: production
    depends_on:
      db:
        condition: service_healthy
      ollama:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
    env_file: ../.env
    environment:
      API_URL: ${API_URL:-http://localhost:8000}
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
      ENVIRONMENT: production
      JWT_SECRET: ${JWT_SECRET}
      LOG_LEVEL: INFO
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_AUTH_COOKIE_SECURE: ${SUPABASE_AUTH_COOKIE_SECURE:-true}
      SUPABASE_AUTH_ENABLED: ${SUPABASE_AUTH_ENABLED:-true}
      SUPABASE_AUTH_REDIRECT_URL: ${SUPABASE_AUTH_REDIRECT_URL:-http://localhost:3000/auth/callback}
      SUPABASE_DB_URL: ${SUPABASE_DB_URL}
      SUPABASE_REALTIME_CHANNELS: ${SUPABASE_REALTIME_CHANNELS:-chat,notifications,file-updates}
      SUPABASE_REALTIME_ENABLED: ${SUPABASE_REALTIME_ENABLED:-true}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      SUPABASE_STORAGE_BUCKET: ${SUPABASE_STORAGE_BUCKET:-ai-coding-agent-files}
      SUPABASE_STORAGE_ENABLED: ${SUPABASE_STORAGE_ENABLED:-true}
      SUPABASE_URL: ${SUPABASE_URL}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health
      timeout: 30s
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - ./data:/app/data:rw
    - ./logs:/app/logs:rw
    - ./backups:/app/backups:rw
    - ./sites:/app/sites:rw
    - ./uploads:/app/uploads:rw
    - ./ssl:/app/ssl:ro
    - ./database:/app/database:rw
    - ./config:/app/config:ro
    - ./test_reports:/app/test_reports:rw
    - containers\extracted\docker-compose.prod_api_ports.json:/app/config/ports.json:ro
  backup:
    build:
      context: ..
      dockerfile: containers/Dockerfile.backup
    container_name: ai-coding-backup-prod
    depends_on:
    - db
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    environment:
      BACKUP_ENABLED: 'true'
      BACKUP_SCHEDULE: 0 2 * * *
      LOG_LEVEL: INFO
    networks:
    - ai-coding-network
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - ./backups:/app/backups:rw
    - ./data:/app/data:ro
    - ./database:/app/database:ro
    - ./logs:/app/logs:ro
  db:
    container_name: ai-coding-db-prod
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    environment:
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: '0.9'
      POSTGRES_DB: ${DB_NAME:-ai_coding_agent}
      POSTGRES_DEFAULT_STATISTICS_TARGET: '100'
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
      POSTGRES_EFFECTIVE_IO_CONCURRENCY: '200'
      POSTGRES_INITDB_ARGS: --encoding=UTF-8
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
      POSTGRES_PASSWORD: ${DB_PASSWORD:-ai_coding_password}
      POSTGRES_RANDOM_PAGE_COST: '1.1'
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_USER: ${DB_USER:-ai_coding_user}
      POSTGRES_WAL_BUFFERS: 16MB
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD-SHELL
      - pg_isready -U ${DB_USER:-ai_coding_user} -d ${DB_NAME:-ai_coding_agent}
      timeout: 10s
    image: postgres:15-alpine
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - pgdata:/var/lib/postgresql/data
    - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    - ./database/migrations:/docker-entrypoint-initdb.d/migrations
    - ./database/backups:/var/lib/postgresql/backups
    - containers\extracted\docker-compose.prod_db_ports.json:/app/config/ports.json:ro
  frontend:
    build:
      context: ..
      dockerfile: containers/Dockerfile.frontend
      target: production
    depends_on:
      api:
        condition: service_healthy
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    env_file: ../.env
    environment:
      NEXT_PUBLIC_API_URL: ${API_URL:-http://localhost:8000}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
      NODE_ENV: production
      PORT: '3000'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:3000/api/health
      timeout: 30s
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    read_only: true
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - ./public:/app/public:ro
    - containers\extracted\docker-compose.prod_frontend_ports.json:/app/config/ports.json:ro
  monitoring:
    build:
      context: ..
      dockerfile: containers/Dockerfile.monitoring
    container_name: ai-coding-monitoring-prod
    depends_on:
    - api
    - db
    - redis
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    environment:
      LOG_LEVEL: INFO
      MONITORING_ENABLED: 'true'
    networks:
    - ai-coding-network
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - ./logs:/app/logs:rw
    - ./data:/app/data:rw
  nginx:
    container_name: ai-coding-nginx-prod
    depends_on:
    - api
    - frontend
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 30s
      test:
      - CMD
      - curl
      - -f
      - http://localhost/health
      timeout: 10s
    image: nginx:alpine
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    read_only: true
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    - ./nginx/conf.d:/etc/nginx/conf.d:ro
    - ./ssl:/etc/nginx/ssl:ro
    - ./logs/nginx:/var/log/nginx:rw
    - containers\extracted\docker-compose.prod_nginx_ports.json:/app/config/ports.json:ro
  ollama:
    build:
      context: ..
      dockerfile: containers/Dockerfile.ollama.optimized
      target: production
    container_name: ai-coding-ollama-prod
    deploy:
      resources:
        limits:
          cpus: '6.0'
          memory: 12G
        reservations:
          cpus: '3.0'
          devices:
          - capabilities:
            - gpu
            count: 1
            driver: nvidia
            options:
              memory: 6GB
          memory: 6G
    environment:
      CUDA_VISIBLE_DEVICES: '0'
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      NVIDIA_VISIBLE_DEVICES: '0'
      OLLAMA_HOST: 0.0.0.0
      OLLAMA_ORIGINS: '*'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:11434/api/tags
      timeout: 30s
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - ./models:/root/.ollama/models
    - ollama_data:/root/.ollama
    - containers\extracted\docker-compose.prod_ollama_ports.json:/app/config/ports.json:ro
  redis:
    command:
    - redis-server
    - --appendonly
    - 'yes'
    - --save
    - '900'
    - '1'
    - --save
    - '300'
    - '10'
    - --save
    - '60'
    - '10000'
    - --maxmemory
    - 1gb
    - --maxmemory-policy
    - allkeys-lru
    - --tcp-keepalive
    - '300'
    - --timeout
    - '0'
    container_name: ai-coding-redis-prod
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 30s
      test:
      - CMD
      - redis-cli
      - ping
      timeout: 10s
    image: redis:7-alpine
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - redis_data:/data
    - containers\extracted\docker-compose.prod_redis_ports.json:/app/config/ports.json:ro
  security:
    build:
      context: ..
      dockerfile: containers/Dockerfile.security
    container_name: ai-coding-security-prod
    depends_on:
    - api
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    environment:
      LOG_LEVEL: INFO
      SECURITY_ENABLED: 'true'
    networks:
    - ai-coding-network
    read_only: false
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    volumes:
    - ./logs:/app/logs:rw
    - ./data:/app/data:rw
    - ./ssl:/app/ssl:ro
version: '3.8'
volumes:
  ollama_data:
    driver: local
    driver_opts:
      device: ./data/ollama_data
      o: bind
      type: none
  pgdata:
    driver: local
    driver_opts:
      device: ./database/postgres_data
      o: bind
      type: none
  redis_data:
    driver: local
    driver_opts:
      device: ./data/redis_data
      o: bind
      type: none
