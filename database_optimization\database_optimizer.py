"""
Database Optimization System

Handles database performance optimization, query analysis, indexing,
and maintenance for the AI Coding Agent's database systems.
"""

import asyncio
import json
import logging
import sqlite3
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class QueryPerformanceMetrics:
    """Performance metrics for database queries"""

    query_id: str
    query_text: str
    execution_time_ms: float
    rows_returned: int
    rows_affected: int
    timestamp: datetime = field(default_factory=datetime.now)
    table_name: Optional[str] = None
    index_used: Optional[str] = None
    cache_hit: bool = False
    error_message: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "query_id": self.query_id,
            "query_text": self.query_text,
            "execution_time_ms": self.execution_time_ms,
            "rows_returned": self.rows_returned,
            "rows_affected": self.rows_affected,
            "timestamp": self.timestamp.isoformat(),
            "table_name": self.table_name,
            "index_used": self.index_used,
            "cache_hit": self.cache_hit,
            "error_message": self.error_message,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "QueryPerformanceMetrics":
        """Create from dictionary"""
        return cls(
            query_id=data["query_id"],
            query_text=data["query_text"],
            execution_time_ms=data["execution_time_ms"],
            rows_returned=data["rows_returned"],
            rows_affected=data["rows_affected"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            table_name=data.get("table_name"),
            index_used=data.get("index_used"),
            cache_hit=data.get("cache_hit", False),
            error_message=data.get("error_message"),
        )


@dataclass
class DatabaseMetrics:
    """Database performance metrics"""

    timestamp: datetime = field(default_factory=datetime.now)
    total_queries: int = 0
    slow_queries: int = 0
    avg_query_time_ms: float = 0.0
    max_query_time_ms: float = 0.0
    cache_hit_rate: float = 0.0
    active_connections: int = 0
    database_size_mb: float = 0.0
    index_count: int = 0
    table_count: int = 0
    fragmentation_percent: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "total_queries": self.total_queries,
            "slow_queries": self.slow_queries,
            "avg_query_time_ms": self.avg_query_time_ms,
            "max_query_time_ms": self.max_query_time_ms,
            "cache_hit_rate": self.cache_hit_rate,
            "active_connections": self.active_connections,
            "database_size_mb": self.database_size_mb,
            "index_count": self.index_count,
            "table_count": self.table_count,
            "fragmentation_percent": self.fragmentation_percent,
        }


@dataclass
class OptimizationRecommendation:
    """Database optimization recommendation"""

    recommendation_id: str
    type: str  # index, query, maintenance, schema
    priority: str  # low, medium, high, critical
    description: str
    impact: str  # low, medium, high
    estimated_improvement: float  # percentage
    implementation_cost: str  # low, medium, high
    sql_statement: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    implemented: bool = False
    implemented_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "recommendation_id": self.recommendation_id,
            "type": self.type,
            "priority": self.priority,
            "description": self.description,
            "impact": self.impact,
            "estimated_improvement": self.estimated_improvement,
            "implementation_cost": self.implementation_cost,
            "sql_statement": self.sql_statement,
            "created_at": self.created_at.isoformat(),
            "implemented": self.implemented,
            "implemented_at": (
                self.implemented_at.isoformat() if self.implemented_at else None
            ),
        }


class DatabaseOptimizer:
    """
    Database optimization system for AI Coding Agent.

    Features:
    - Query performance monitoring
    - Index optimization
    - Query analysis and optimization
    - Database maintenance
    - Performance recommendations
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the database optimizer"""
        self.config = config
        self.db_path = config.get("database_path", "database/ai_coding_agent.db")
        self.query_metrics: List[QueryPerformanceMetrics] = []
        self.database_metrics: List[DatabaseMetrics] = []
        self.recommendations: List[OptimizationRecommendation] = []
        self.optimization_history: List[Dict[str, Any]] = []

        # Performance thresholds
        self.thresholds = {
            "slow_query_ms": config.get("slow_query_threshold", 1000.0),
            "critical_query_ms": config.get("critical_query_threshold", 5000.0),
            "max_cache_size": config.get("max_cache_size", 1000),
            "maintenance_interval_hours": config.get("maintenance_interval", 24),
        }

        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread = None
        self.optimization_lock = threading.Lock()

        # Query cache
        self.query_cache: Dict[str, Any] = {}
        self.cache_hits = 0
        self.cache_misses = 0

        logger.info("Database Optimizer initialized")

    def start_monitoring(self) -> None:
        """Start database performance monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop, daemon=True
            )
            self.monitor_thread.start()
            logger.info("Database performance monitoring started")

    def stop_monitoring(self) -> None:
        """Stop database performance monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join()
            self.monitor_thread = None
        logger.info("Database performance monitoring stopped")

    def record_query_performance(
        self,
        query_text: str,
        execution_time_ms: float,
        rows_returned: int = 0,
        rows_affected: int = 0,
        table_name: Optional[str] = None,
        index_used: Optional[str] = None,
        error_message: Optional[str] = None,
    ) -> None:
        """Record query performance metrics"""
        query_id = f"query_{int(time.time() * 1000)}"

        metrics = QueryPerformanceMetrics(
            query_id=query_id,
            query_text=query_text,
            execution_time_ms=execution_time_ms,
            rows_returned=rows_returned,
            rows_affected=rows_affected,
            table_name=table_name,
            index_used=index_used,
            cache_hit=False,  # Will be updated if cache hit
            error_message=error_message,
        )

        with self.optimization_lock:
            self.query_metrics.append(metrics)

            # Check for slow queries
            if execution_time_ms > self.thresholds["slow_query_ms"]:
                self._analyze_slow_query(metrics)

            # Clean up old metrics
            cutoff_date = datetime.now() - timedelta(days=7)
            self.query_metrics = [
                m for m in self.query_metrics if m.timestamp > cutoff_date
            ]

    def get_query_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get query performance summary"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_queries = [q for q in self.query_metrics if q.timestamp > cutoff_time]

        if not recent_queries:
            return {}

        total_queries = len(recent_queries)
        slow_queries = len(
            [
                q
                for q in recent_queries
                if q.execution_time_ms > self.thresholds["slow_query_ms"]
            ]
        )
        avg_time = sum(q.execution_time_ms for q in recent_queries) / total_queries
        max_time = max(q.execution_time_ms for q in recent_queries)

        # Calculate cache hit rate
        cache_hits = sum(1 for q in recent_queries if q.cache_hit)
        cache_hit_rate = cache_hits / total_queries if total_queries > 0 else 0.0

        return {
            "total_queries": total_queries,
            "slow_queries": slow_queries,
            "avg_execution_time_ms": avg_time,
            "max_execution_time_ms": max_time,
            "cache_hit_rate": cache_hit_rate,
            "time_range_hours": hours,
        }

    def analyze_query_patterns(self) -> List[Dict[str, Any]]:
        """Analyze query patterns for optimization opportunities"""
        patterns = {}

        for query in self.query_metrics[-1000:]:  # Last 1000 queries
            # Normalize query for pattern matching
            normalized = self._normalize_query(query.query_text)

            if normalized not in patterns:
                patterns[normalized] = {
                    "query_pattern": normalized,
                    "count": 0,
                    "total_time": 0.0,
                    "avg_time": 0.0,
                    "max_time": 0.0,
                    "tables": set(),
                    "indexes_used": set(),
                }

            pattern = patterns[normalized]
            pattern["count"] += 1
            pattern["total_time"] += query.execution_time_ms
            pattern["max_time"] = max(pattern["max_time"], query.execution_time_ms)

            if query.table_name:
                pattern["tables"].add(query.table_name)
            if query.index_used:
                pattern["indexes_used"].add(query.index_used)

        # Calculate averages and convert sets to lists
        for pattern in patterns.values():
            pattern["avg_time"] = pattern["total_time"] / pattern["count"]
            pattern["tables"] = list(pattern["tables"])
            pattern["indexes_used"] = list(pattern["indexes_used"])

        # Sort by total time (most expensive patterns first)
        sorted_patterns = sorted(
            patterns.values(), key=lambda x: x["total_time"], reverse=True
        )
        return sorted_patterns[:20]  # Top 20 patterns

    def generate_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Generate optimization recommendations based on analysis"""
        recommendations = []

        # Analyze query patterns
        patterns = self.analyze_query_patterns()

        for pattern in patterns:
            if pattern["avg_time"] > self.thresholds["slow_query_ms"]:
                # Slow query pattern
                recommendation = OptimizationRecommendation(
                    recommendation_id=f"slow_query_{int(time.time())}",
                    type="query",
                    priority=(
                        "high"
                        if pattern["avg_time"] > self.thresholds["critical_query_ms"]
                        else "medium"
                    ),
                    description=f"Optimize slow query pattern: {pattern['query_pattern'][:100]}...",
                    impact="high" if pattern["count"] > 10 else "medium",
                    estimated_improvement=50.0,  # Estimate 50% improvement
                    implementation_cost="medium",
                    sql_statement=self._generate_optimization_sql(pattern),
                )
                recommendations.append(recommendation)

        # Check for missing indexes
        missing_indexes = self._identify_missing_indexes()
        for index_rec in missing_indexes:
            recommendations.append(index_rec)

        # Database maintenance recommendations
        maintenance_recs = self._generate_maintenance_recommendations()
        recommendations.extend(maintenance_recs)

        return recommendations

    def optimize_database(self) -> Dict[str, Any]:
        """Perform database optimization"""
        with self.optimization_lock:
            logger.info("Starting database optimization")

            # Generate recommendations
            recommendations = self.generate_optimization_recommendations()

            # Apply high-priority recommendations
            applied_optimizations = []
            for rec in recommendations:
                if rec.priority in ["high", "critical"]:
                    try:
                        if self._apply_optimization(rec):
                            applied_optimizations.append(rec)
                    except Exception as e:
                        logger.error(
                            f"Failed to apply optimization {rec.recommendation_id}: {e}"
                        )

            # Perform maintenance
            maintenance_results = self._perform_maintenance()

            # Record optimization
            optimization_record = {
                "timestamp": datetime.now().isoformat(),
                "recommendations_generated": len(recommendations),
                "optimizations_applied": len(applied_optimizations),
                "maintenance_performed": maintenance_results,
                "performance_improvement": self._calculate_performance_improvement(),
            }

            self.optimization_history.append(optimization_record)

            logger.info(
                f"Database optimization completed: {len(applied_optimizations)} optimizations applied"
            )

            return {
                "status": "completed",
                "recommendations_generated": len(recommendations),
                "optimizations_applied": len(applied_optimizations),
                "maintenance_performed": maintenance_results,
                "performance_improvement": optimization_record[
                    "performance_improvement"
                ],
            }

    def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect database metrics
                metrics = self._collect_database_metrics()
                self.database_metrics.append(metrics)

                # Clean up old metrics
                cutoff_date = datetime.now() - timedelta(days=7)
                self.database_metrics = [
                    m for m in self.database_metrics if m.timestamp > cutoff_date
                ]

                # Check for optimization triggers
                if self._should_optimize():
                    self.optimize_database()

                # Sleep for monitoring interval
                time.sleep(300)  # Check every 5 minutes

            except Exception as e:
                logger.error(f"Error in database monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute on error

    def _collect_database_metrics(self) -> DatabaseMetrics:
        """Collect current database metrics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get database size
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            db_size_mb = (page_count * page_size) / (1024 * 1024)

            # Get table count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]

            # Get index count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index'")
            index_count = cursor.fetchone()[0]

            # Get fragmentation info
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            fragmentation = 0.0 if integrity_result == "ok" else 10.0

            conn.close()

            # Calculate query metrics
            recent_queries = self.query_metrics[-100:] if self.query_metrics else []
            total_queries = len(recent_queries)
            slow_queries = len(
                [
                    q
                    for q in recent_queries
                    if q.execution_time_ms > self.thresholds["slow_query_ms"]
                ]
            )
            avg_time = (
                sum(q.execution_time_ms for q in recent_queries) / total_queries
                if total_queries > 0
                else 0.0
            )
            max_time = (
                max(q.execution_time_ms for q in recent_queries)
                if recent_queries
                else 0.0
            )

            # Calculate cache hit rate
            cache_hits = sum(1 for q in recent_queries if q.cache_hit)
            cache_hit_rate = cache_hits / total_queries if total_queries > 0 else 0.0

            return DatabaseMetrics(
                total_queries=total_queries,
                slow_queries=slow_queries,
                avg_query_time_ms=avg_time,
                max_query_time_ms=max_time,
                cache_hit_rate=cache_hit_rate,
                active_connections=1,  # SQLite has limited connection info
                database_size_mb=db_size_mb,
                index_count=index_count,
                table_count=table_count,
                fragmentation_percent=fragmentation,
            )

        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")
            return DatabaseMetrics()

    def _normalize_query(self, query: str) -> str:
        """Normalize query for pattern matching"""
        # Remove extra whitespace and convert to lowercase
        normalized = " ".join(query.lower().split())

        # Replace specific values with placeholders
        import re

        normalized = re.sub(r"\d+", "N", normalized)
        normalized = re.sub(r"'[^']*'", "'STRING'", normalized)
        normalized = re.sub(r'"[^"]*"', '"STRING"', normalized)

        return normalized

    def _analyze_slow_query(self, metrics: QueryPerformanceMetrics) -> None:
        """Analyze a slow query for optimization opportunities"""
        logger.warning(
            f"Slow query detected: {metrics.execution_time_ms:.1f}ms - {metrics.query_text[:100]}..."
        )

        # Generate recommendation for slow query
        recommendation = OptimizationRecommendation(
            recommendation_id=f"slow_query_{int(time.time())}",
            type="query",
            priority=(
                "high"
                if metrics.execution_time_ms > self.thresholds["critical_query_ms"]
                else "medium"
            ),
            description=f"Optimize slow query: {metrics.query_text[:100]}...",
            impact="high",
            estimated_improvement=60.0,
            implementation_cost="medium",
        )

        self.recommendations.append(recommendation)

    def _identify_missing_indexes(self) -> List[OptimizationRecommendation]:
        """Identify missing indexes based on query patterns"""
        recommendations = []

        # Analyze query patterns for potential missing indexes
        patterns = self.analyze_query_patterns()

        for pattern in patterns:
            if "WHERE" in pattern["query_pattern"] and pattern["avg_time"] > 100:
                # Look for WHERE clauses that might benefit from indexes
                tables = pattern["tables"]
                for table in tables:
                    # Check if table has indexes
                    if not self._table_has_adequate_indexes(table):
                        recommendation = OptimizationRecommendation(
                            recommendation_id=f"missing_index_{int(time.time())}",
                            type="index",
                            priority="medium",
                            description=f"Add index for table {table}",
                            impact="medium",
                            estimated_improvement=40.0,
                            implementation_cost="low",
                            sql_statement=f"CREATE INDEX idx_{table}_optimization ON {table}(column_name)",
                        )
                        recommendations.append(recommendation)

        return recommendations

    def _table_has_adequate_indexes(self, table_name: str) -> bool:
        """Check if a table has adequate indexes"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND tbl_name=?",
                (table_name,),
            )
            index_count = cursor.fetchone()[0]

            conn.close()
            return index_count > 0

        except Exception as e:
            logger.error(f"Error checking indexes for table {table_name}: {e}")
            return False

    def _generate_maintenance_recommendations(self) -> List[OptimizationRecommendation]:
        """Generate database maintenance recommendations"""
        recommendations = []

        # Check database size and fragmentation
        if self.database_metrics:
            latest_metrics = self.database_metrics[-1]

            if latest_metrics.fragmentation_percent > 5.0:
                recommendations.append(
                    OptimizationRecommendation(
                        recommendation_id=f"maintenance_{int(time.time())}",
                        type="maintenance",
                        priority="medium",
                        description="Perform database VACUUM to reduce fragmentation",
                        impact="medium",
                        estimated_improvement=20.0,
                        implementation_cost="low",
                        sql_statement="VACUUM",
                    )
                )

            if latest_metrics.database_size_mb > 100:  # Large database
                recommendations.append(
                    OptimizationRecommendation(
                        recommendation_id=f"analyze_{int(time.time())}",
                        type="maintenance",
                        priority="low",
                        description="Update table statistics with ANALYZE",
                        impact="low",
                        estimated_improvement=10.0,
                        implementation_cost="low",
                        sql_statement="ANALYZE",
                    )
                )

        return recommendations

    def _should_optimize(self) -> bool:
        """Check if optimization should be performed"""
        if not self.database_metrics:
            return False

        latest_metrics = self.database_metrics[-1]

        # Optimize if there are many slow queries
        if latest_metrics.slow_queries > 10:
            return True

        # Optimize if average query time is high
        if latest_metrics.avg_query_time_ms > self.thresholds["slow_query_ms"]:
            return True

        # Optimize if fragmentation is high
        if latest_metrics.fragmentation_percent > 10.0:
            return True

        return False

    def _apply_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply a specific optimization recommendation"""
        try:
            if recommendation.sql_statement:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute(recommendation.sql_statement)
                conn.commit()
                conn.close()

                recommendation.implemented = True
                recommendation.implemented_at = datetime.now()

                logger.info(f"Applied optimization: {recommendation.description}")
                return True

        except Exception as e:
            logger.error(
                f"Failed to apply optimization {recommendation.recommendation_id}: {e}"
            )
            return False

    def _perform_maintenance(self) -> Dict[str, Any]:
        """Perform database maintenance tasks"""
        maintenance_results = {}

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # VACUUM to reduce fragmentation
            cursor.execute("VACUUM")
            maintenance_results["vacuum"] = "completed"

            # ANALYZE to update statistics
            cursor.execute("ANALYZE")
            maintenance_results["analyze"] = "completed"

            # REINDEX to rebuild indexes
            cursor.execute("REINDEX")
            maintenance_results["reindex"] = "completed"

            conn.close()

            logger.info("Database maintenance completed")

        except Exception as e:
            logger.error(f"Database maintenance failed: {e}")
            maintenance_results["error"] = str(e)

        return maintenance_results

    def _calculate_performance_improvement(self) -> float:
        """Calculate estimated performance improvement"""
        if len(self.database_metrics) < 2:
            return 0.0

        # Compare recent metrics with earlier metrics
        recent_metrics = self.database_metrics[-10:]  # Last 10 measurements
        # 10 measurements before that
        earlier_metrics = self.database_metrics[-20:-10]

        if not earlier_metrics:
            return 0.0

        recent_avg = sum(m.avg_query_time_ms for m in recent_metrics) / len(
            recent_metrics
        )
        earlier_avg = sum(m.avg_query_time_ms for m in earlier_metrics) / len(
            earlier_metrics
        )

        if earlier_avg == 0:
            return 0.0

        improvement = ((earlier_avg - recent_avg) / earlier_avg) * 100
        return max(0.0, improvement)

    def _generate_optimization_sql(self, pattern: Dict[str, Any]) -> Optional[str]:
        """Generate SQL for query optimization"""
        # This is a simplified version - in practice, you'd have more sophisticated
        # query analysis and optimization generation
        if "SELECT" in pattern["query_pattern"] and pattern["tables"]:
            table = pattern["tables"][0]
            return f"CREATE INDEX idx_{table}_optimization ON {table}(id)"

        return None

    def save_optimization_data(self, file_path: str) -> None:
        """Save optimization data to file"""
        data = {
            "query_metrics": [m.to_dict() for m in self.query_metrics],
            "database_metrics": [m.to_dict() for m in self.database_metrics],
            "recommendations": [r.to_dict() for r in self.recommendations],
            "optimization_history": self.optimization_history,
            "thresholds": self.thresholds,
        }

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"Saved optimization data to {file_path}")

    def load_optimization_data(self, file_path: str) -> None:
        """Load optimization data from file"""
        if not Path(file_path).exists():
            return

        with open(file_path, "r") as f:
            data = json.load(f)

        self.query_metrics = [
            QueryPerformanceMetrics.from_dict(m) for m in data.get("query_metrics", [])
        ]
        self.database_metrics = [
            DatabaseMetrics(**m) for m in data.get("database_metrics", [])
        ]
        self.recommendations = [
            OptimizationRecommendation(**r) for r in data.get("recommendations", [])
        ]
        self.optimization_history = data.get("optimization_history", [])

        logger.info(f"Loaded optimization data from {file_path}")

    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get optimization summary"""
        return {
            "total_queries_monitored": len(self.query_metrics),
            "total_recommendations": len(self.recommendations),
            "implemented_recommendations": len(
                [r for r in self.recommendations if r.implemented]
            ),
            "optimization_history_count": len(self.optimization_history),
            "monitoring_active": self.monitoring_active,
            "cache_hit_rate": (
                self.cache_hits / (self.cache_hits + self.cache_misses)
                if (self.cache_hits + self.cache_misses) > 0
                else 0.0
            ),
        }
