// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Import cypress-file-upload for file upload testing
import 'cypress-file-upload'

// Custom command to wait for toast notifications
Cypress.Commands.add('waitForToast', (expectedText, timeout = 5000) => {
  cy.get('.react-hot-toast', { timeout }).should('contain.text', expectedText)
})

// Custom command to check for error toast
Cypress.Commands.add('expectErrorToast', (expectedText) => {
  cy.waitForToast(expectedText)
  cy.get('.react-hot-toast').should('have.class', 'error-toast')
})

// Custom command to check for success toast
Cypress.Commands.add('expectSuccessToast', (expectedText) => {
  cy.waitForToast(expectedText)
  cy.get('.react-hot-toast').should('have.class', 'success-toast')
})

// Custom command to simulate site validation
Cypress.Commands.add('validateSite', (siteName) => {
  cy.get(`[data-testid="site-${siteName}"]`).within(() => {
    cy.get('[data-testid="validate-button"]').click()
  })
})

// Custom command to simulate file upload
Cypress.Commands.add('uploadFile', (fileName, fileType = 'text/html') => {
  cy.fixture(fileName).then((fileContent) => {
    cy.get('[data-testid="file-upload"]').attachFile({
      fileContent: fileContent,
      fileName: fileName,
      mimeType: fileType
    })
  })
})

// Custom command to send chat message
Cypress.Commands.add('sendChatMessage', (message) => {
  cy.get('[data-testid="chat-input"]').type(message)
  cy.get('[data-testid="chat-send"]').click()
})

// Custom command to enhance prompt
Cypress.Commands.add('enhancePrompt', (prompt) => {
  cy.get('[data-testid="chat-input"]').type(prompt)
  cy.get('[data-testid="enhance-button"]').click()
})

// Custom command to run error detection scan
Cypress.Commands.add('runErrorScan', () => {
  cy.get('[data-testid="error-detection-scan"]').click()
})

// Custom command to test model health
Cypress.Commands.add('testModelHealth', (modelName) => {
  cy.get(`[data-testid="model-${modelName}-test"]`).click()
})

// Custom command to wait for AI response
Cypress.Commands.add('waitForAIResponse', (timeout = 15000) => {
  cy.get('[data-testid="chat-messages"]', { timeout }).should('contain', '[AI]')
})

// Custom command to check for loading states
Cypress.Commands.add('waitForLoading', (selector, timeout = 10000) => {
  cy.get(selector, { timeout }).should('not.have.class', 'loading')
})

// Custom command to simulate network error
Cypress.Commands.add('simulateNetworkError', () => {
  cy.intercept('POST', '/api/v1/chat', { forceNetworkError: true }).as('chatError')
})

// Custom command to simulate server error
Cypress.Commands.add('simulateServerError', (endpoint, statusCode = 500) => {
  cy.intercept('POST', endpoint, { statusCode }).as('serverError')
})

// Custom command to check for accessibility issues
Cypress.Commands.add('checkAccessibility', () => {
  cy.injectAxe()
  cy.checkA11y()
})

// Custom command to wait for file operations
Cypress.Commands.add('waitForFileOperation', (operation) => {
  cy.get(`[data-testid="${operation}-status"]`).should('contain', 'complete')
})

// Custom command to simulate slow network
Cypress.Commands.add('simulateSlowNetwork', (delay = 3000) => {
  cy.intercept('**', (req) => {
    req.reply((res) => {
      res.delay = delay
    })
  })
})

// Override visit command to handle authentication if needed
Cypress.Commands.overwrite('visit', (originalFn, url, options) => {
  // Add any pre-visit logic here (e.g., authentication)
  return originalFn(url, options)
})

// Custom command to check toast visibility and content
Cypress.Commands.add('checkToast', (expectedText, type = 'info') => {
  cy.get('.react-hot-toast').should('be.visible')
  cy.get('.react-hot-toast').should('contain.text', expectedText)

  if (type === 'error') {
    cy.get('.react-hot-toast').should('have.class', 'error-toast')
  } else if (type === 'success') {
    cy.get('.react-hot-toast').should('have.class', 'success-toast')
  }
})

// Custom command to clear all toasts
Cypress.Commands.add('clearToasts', () => {
  cy.get('.react-hot-toast').each(($toast) => {
    cy.wrap($toast).find('[data-testid="toast-close"]').click()
  })
})

// Custom command to wait for component to be ready
Cypress.Commands.add('waitForComponent', (componentName) => {
  cy.get(`[data-testid="${componentName}"]`).should('be.visible')
  cy.get(`[data-testid="${componentName}"]`).should('not.have.class', 'loading')
})

// Custom command to simulate user interaction sequence
Cypress.Commands.add('simulateUserFlow', (flow) => {
  flow.forEach((step) => {
    switch (step.action) {
      case 'click':
        cy.get(`[data-testid="${step.target}"]`).click()
        break
      case 'type':
        cy.get(`[data-testid="${step.target}"]`).type(step.value)
        break
      case 'wait':
        cy.wait(step.value)
        break
      case 'expect':
        cy.checkToast(step.value, step.type)
        break
      default:
        cy.log(`Unknown action: ${step.action}`)
    }
  })
})
