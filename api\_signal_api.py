"""This is the 'bare' scipy.signal API.

This --- private! --- module only collects implementations of public  API
for _support_alternative_backends.
The latter --- also private! --- module adds delegation to CuPy etc and
re-exports decorated names to __init__.py
"""

from api._czt import *  # noqa: F403
from api._filter_design import *  # noqa: F403
from api._fir_filter_design import *  # noqa: F403
from api._lti_conversion import *  # noqa: F403
from api._ltisys import *  # noqa: F403
from api._max_len_seq import max_len_seq  # noqa: F401
from api._peak_finding import *  # noqa: F403
from api._savitzky_golay import savgol_coeffs, savgol_filter  # noqa: F401
from api._short_time_fft import *  # noqa: F403
from api._signaltools import *  # noqa: F403
from api._spectral_py import *  # noqa: F403
from api._spline import sepfir2d  # noqa: F401
from api._spline_filters import *  # noqa: F403
from api._upfirdn import upfirdn  # noqa: F401
from api._waveforms import *  # noqa: F403
from api.windows import get_window  # keep this one in signal namespace  # noqa: F401

from . import _signaltools, windows  # noqa: F401

__all__ = [s for s in dir() if not s.startswith("_")]
