{"timestamp": "2025-07-19T15:13:31.277119", "python_version": "3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]", "tools": {"black": {"success": true, "stdout": "", "stderr": "All done! ✨ 🍰 ✨\n31 files would be left unchanged.\n"}, "isort": {"success": true, "stdout": "", "stderr": ""}, "flake8": {"success": false, "stdout": "", "stderr": "Traceback (most recent call last):\n  File \"<frozen runpy>\", line 198, in _run_module_as_main\n  File \"<frozen runpy>\", line 88, in _run_code\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\__main__.py\", line 7, in <module>\n    raise SystemExit(main())\n                     ~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\main\\cli.py\", line 23, in main\n    app.run(argv)\n    ~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\main\\application.py\", line 198, in run\n    self._run(argv)\n    ~~~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\main\\application.py\", line 186, in _run\n    self.initialize(argv)\n    ~~~~~~~~~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\main\\application.py\", line 165, in initialize\n    self.plugins, self.options = parse_args(argv)\n                                 ~~~~~~~~~~^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\options\\parse_args.py\", line 53, in parse_args\n    opts = aggregator.aggregate_options(option_manager, cfg, cfg_dir, rest)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\options\\aggregator.py\", line 30, in aggregate_options\n    parsed_config = config.parse_config(manager, cfg, cfg_dir)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flake8\\options\\config.py\", line 131, in parse_config\n    raise ValueError(\n    ...<3 lines>...\n    )\nValueError: Error code '#' supplied to 'extend-ignore' option does not match '^[A-Z]{1,3}[0-9]{0,3}$'\n"}, "mypy": {"success": false, "stdout": "src\\__main__.py:5: error: Cannot find implementation or library stub for module named \".cli\"  [import-not-found]\nsrc\\ssl_manager.py:29: error: Returning Any from function declared to return \"dict[str, Any]\"  [no-any-return]\nsrc\\deployment.py:29: error: Returning Any from function declared to return \"dict[str, Any]\"  [no-any-return]\nsrc\\cli\\error_handler.py:35: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\cli\\error_handler.py:54: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\cli\\error_handler.py:100: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\template_manager.py:21: error: Returning Any from function declared to return \"dict[str, Any]\"  [no-any-return]\nsrc\\template_manager.py:53: error: Returning Any from function declared to return \"dict[str, Any]\"  [no-any-return]\nsrc\\security_manager.py:45: error: Returning Any from function declared to return \"dict[str, Any]\"  [no-any-return]\nsrc\\cli\\ssl_commands.py:20: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\ssl_commands.py:20: note: Use \"-> None\" if function does not return a value\nsrc\\cli\\ssl_commands.py:36: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\ssl_commands.py:67: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\ssl_commands.py:92: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\ssl_commands.py:113: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\ssl_commands.py:152: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:14: error: Cannot find implementation or library stub for module named \".config\"  [import-not-found]\nsrc\\cli\\__init__.py:14: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports\nsrc\\cli\\__init__.py:15: error: Cannot find implementation or library stub for module named \".db\"  [import-not-found]\nsrc\\cli\\__init__.py:16: error: Cannot find implementation or library stub for module named \".deployment\"  [import-not-found]\nsrc\\cli\\__init__.py:17: error: Cannot find implementation or library stub for module named \".models\"  [import-not-found]\nsrc\\cli\\__init__.py:18: error: Cannot find implementation or library stub for module named \".server_launcher\"  [import-not-found]\nsrc\\cli\\__init__.py:32: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:32: note: Use \"-> None\" if function does not return a value\nsrc\\cli\\__init__.py:40: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:40: note: Use \"-> None\" if function does not return a value\nsrc\\cli\\__init__.py:47: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:54: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:61: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:68: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:71: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:71: note: Use \"-> None\" if function does not return a value\nsrc\\cli\\__init__.py:91: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:133: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:133: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\cli\\__init__.py:171: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:171: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\cli\\__init__.py:213: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:213: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\cli\\__init__.py:256: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:256: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\cli\\__init__.py:294: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:312: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:337: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\__init__.py:337: note: Use \"-> None\" if function does not return a value\nsrc\\cli\\security_commands.py:20: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:20: note: Use \"-> None\" if function does not return a value\nsrc\\cli\\security_commands.py:32: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:64: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:92: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:116: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:153: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:173: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:203: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\cli\\security_commands.py:236: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\db\\__init__.py:14: error: Cannot find implementation or library stub for module named \".config\"  [import-not-found]\nsrc\\db\\__init__.py:58: error: Incompatible types in \"yield\" (actual type \"Session\", expected type \"scoped_session[Any]\")  [misc]\nsrc\\db\\models.py:28: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:28: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:28: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\models.py:54: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:54: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:54: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\models.py:80: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:80: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:80: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\models.py:117: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:117: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:117: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\models.py:145: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:145: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:145: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\models.py:166: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:166: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:166: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\models.py:190: error: Variable \"db.Base\" is not valid as a type  [valid-type]\nsrc\\db\\models.py:190: note: See https://mypy.readthedocs.io/en/stable/common_issues.html#variables-vs-type-aliases\nsrc\\db\\models.py:190: error: Invalid base class \"Base\"  [misc]\nsrc\\db\\database_manager.py:81: error: Returning Any from function declared to return \"ModelType\"  [no-any-return]\nsrc\\db\\database_manager.py:129: error: Returning Any from function declared to return \"ModelType\"  [no-any-return]\nsrc\\db\\database_manager.py:140: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\db\\database_manager.py:140: note: Use \"-> None\" if function does not return a value\nsrc\\db\\database_manager.py:155: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\db\\database_manager.py:155: note: Use \"-> None\" if function does not return a value\nsrc\\db\\database_manager.py:174: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\db\\database_manager.py:174: note: Use \"-> None\" if function does not return a value\nsrc\\db\\database_manager.py:193: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\db\\database_manager.py:193: note: Use \"-> None\" if function does not return a value\nsrc\\server_launcher.py:32: error: Returning Any from function declared to return \"dict[str, Any]\"  [no-any-return]\nsrc\\server_launcher.py:41: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:45: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:53: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:65: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:65: note: Use \"-> None\" if function does not return a value\nsrc\\server_launcher.py:69: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:69: note: Use \"-> None\" if function does not return a value\nsrc\\server_launcher.py:73: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:73: note: Use \"-> None\" if function does not return a value\nsrc\\server_launcher.py:76: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:76: note: Use \"-> None\" if function does not return a value\nsrc\\server_launcher.py:80: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\server_launcher.py:83: error: Function is missing a type annotation  [no-untyped-def]\nsrc\\server_launcher.py:93: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:93: note: Use \"-> None\" if function does not return a value\nsrc\\server_launcher.py:110: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\server_launcher.py:110: note: Use \"-> None\" if function does not return a value\nsrc\\models\\__init__.py:13: error: Cannot find implementation or library stub for module named \".config\"  [import-not-found]\nsrc\\models\\__init__.py:30: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:36: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:41: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:60: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:72: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:78: error: Returning Any from function declared to return \"str\"  [no-any-return]\nsrc\\models\\__init__.py:83: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:89: error: Returning Any from function declared to return \"str\"  [no-any-return]\nsrc\\models\\__init__.py:98: error: Returning Any from function declared to return \"list[float]\"  [no-any-return]\nsrc\\models\\__init__.py:125: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\models\\__init__.py:128: error: Cannot determine type of \"_initialized\"  [has-type]\nsrc\\models\\__init__.py:131: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\models\\__init__.py:131: note: Use \"-> None\" if function does not return a value\nsrc\\models\\__init__.py:132: error: Cannot determine type of \"_initialized\"  [has-type]\nsrc\\models\\__init__.py:151: error: Incompatible types in assignment (expression has type \"type[ModelProvider]\", target has type \"ModelProvider\")  [assignment]\nsrc\\models\\__init__.py:153: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:157: error: Returning Any from function declared to return \"ModelProvider\"  [no-any-return]\nsrc\\models\\__init__.py:157: error: \"ModelProvider\" not callable  [operator]\nsrc\\models\\__init__.py:159: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:171: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:183: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]\nsrc\\models\\__init__.py:223: error: Function is missing a return type annotation  [no-untyped-def]\nsrc\\models\\__init__.py:223: note: Use \"-> None\" if function does not return a value\nFound 104 errors in 14 files (checked 19 source files)\n", "stderr": ""}, "bandit": {"success": false, "stdout": "{\n  \"errors\": [],\n  \"generated_at\": \"2025-07-19T21:13:39Z\",\n  \"metrics\": {\n    \"_totals\": {\n      \"CONFIDENCE.HIGH\": 20,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 4,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 20,\n      \"SEVERITY.MEDIUM\": 4,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 2718,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\__main__.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 6,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\cli\\\\__init__.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 3,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 3,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 268,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\cli\\\\error_handler.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 79,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\cli\\\\security_commands.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 205,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\cli\\\\ssl_commands.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 131,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\cli\\\\state_tracker.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 100,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\config\\\\__init__.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 1,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 1,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 72,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\db\\\\__init__.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 52,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\db\\\\database_manager.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 170,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\db\\\\models.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 164,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\deployment.py\": {\n      \"CONFIDENCE.HIGH\": 5,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 5,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 135,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\model_router.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 54,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\models\\\\__init__.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 192,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\requirement_parser.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 85,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\security_manager.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 434,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\server_launcher.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 101,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\ssl_manager.py\": {\n      \"CONFIDENCE.HIGH\": 15,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 15,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 286,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\template_manager.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 82,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    },\n    \"src\\\\website_generator.py\": {\n      \"CONFIDENCE.HIGH\": 0,\n      \"CONFIDENCE.LOW\": 0,\n      \"CONFIDENCE.MEDIUM\": 0,\n      \"CONFIDENCE.UNDEFINED\": 0,\n      \"SEVERITY.HIGH\": 0,\n      \"SEVERITY.LOW\": 0,\n      \"SEVERITY.MEDIUM\": 0,\n      \"SEVERITY.UNDEFINED\": 0,\n      \"loc\": 102,\n      \"nosec\": 0,\n      \"skipped_tests\": 0\n    }\n  },\n  \"results\": [\n    {\n      \"code\": \"166 @click.option(\\\"--port\\\", type=int, default=5000, help=\\\"Port to run the server on\\\")\\n167 @click.option(\\\"--host\\\", default=\\\"0.0.0.0\\\", help=\\\"Host to bind to\\\")\\n168 @click.option(\\\"--debug/--no-debug\\\", default=False, help=\\\"Enable debug mode\\\")\\n\",\n      \"col_offset\": 32,\n      \"end_col_offset\": 41,\n      \"filename\": \"src\\\\cli\\\\__init__.py\",\n      \"issue_confidence\": \"MEDIUM\",\n      \"issue_cwe\": {\n        \"id\": 605,\n        \"link\": \"https://cwe.mitre.org/data/definitions/605.html\"\n      },\n      \"issue_severity\": \"MEDIUM\",\n      \"issue_text\": \"Possible binding to all interfaces.\",\n      \"line_number\": 167,\n      \"line_range\": [\n        167\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b104_hardcoded_bind_all_interfaces.html\",\n      \"test_id\": \"B104\",\n      \"test_name\": \"hardcoded_bind_all_interfaces\"\n    },\n    {\n      \"code\": \"278     # Open browser if not disabled\\n279     if not no_open and host in [\\\"0.0.0.0\\\", \\\"127.0.0.1\\\"]:\\n280         import webbrowser\\n\",\n      \"col_offset\": 32,\n      \"end_col_offset\": 41,\n      \"filename\": \"src\\\\cli\\\\__init__.py\",\n      \"issue_confidence\": \"MEDIUM\",\n      \"issue_cwe\": {\n        \"id\": 605,\n        \"link\": \"https://cwe.mitre.org/data/definitions/605.html\"\n      },\n      \"issue_severity\": \"MEDIUM\",\n      \"issue_text\": \"Possible binding to all interfaces.\",\n      \"line_number\": 279,\n      \"line_range\": [\n        279\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b104_hardcoded_bind_all_interfaces.html\",\n      \"test_id\": \"B104\",\n      \"test_name\": \"hardcoded_bind_all_interfaces\"\n    },\n    {\n      \"code\": \"251 @click.option(\\\"--port\\\", type=int, default=5000, help=\\\"Port to run the server on\\\")\\n252 @click.option(\\\"--host\\\", default=\\\"0.0.0.0\\\", help=\\\"Host to bind to\\\")\\n253 @click.option(\\\"--no-open\\\", is_flag=True, help=\\\"Do not open browser automatically\\\")\\n\",\n      \"col_offset\": 32,\n      \"end_col_offset\": 41,\n      \"filename\": \"src\\\\cli\\\\__init__.py\",\n      \"issue_confidence\": \"MEDIUM\",\n      \"issue_cwe\": {\n        \"id\": 605,\n        \"link\": \"https://cwe.mitre.org/data/definitions/605.html\"\n      },\n      \"issue_severity\": \"MEDIUM\",\n      \"issue_text\": \"Possible binding to all interfaces.\",\n      \"line_number\": 252,\n      \"line_range\": [\n        252\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b104_hardcoded_bind_all_interfaces.html\",\n      \"test_id\": \"B104\",\n      \"test_name\": \"hardcoded_bind_all_interfaces\"\n    },\n    {\n      \"code\": \"40 \\n41     host: str = \\\"0.0.0.0\\\"\\n42     port: int = 5000\\n\",\n      \"col_offset\": 16,\n      \"end_col_offset\": 25,\n      \"filename\": \"src\\\\config\\\\__init__.py\",\n      \"issue_confidence\": \"MEDIUM\",\n      \"issue_cwe\": {\n        \"id\": 605,\n        \"link\": \"https://cwe.mitre.org/data/definitions/605.html\"\n      },\n      \"issue_severity\": \"MEDIUM\",\n      \"issue_text\": \"Possible binding to all interfaces.\",\n      \"line_number\": 41,\n      \"line_range\": [\n        41\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b104_hardcoded_bind_all_interfaces.html\",\n      \"test_id\": \"B104\",\n      \"test_name\": \"hardcoded_bind_all_interfaces\"\n    },\n    {\n      \"code\": \"4 import shutil\\n5 import subprocess\\n6 from datetime import datetime\\n\",\n      \"col_offset\": 0,\n      \"end_col_offset\": 17,\n      \"filename\": \"src\\\\deployment.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Consider possible security implications associated with the subprocess module.\",\n      \"line_number\": 5,\n      \"line_range\": [\n        5\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess\",\n      \"test_id\": \"B404\",\n      \"test_name\": \"blacklist\"\n    },\n    {\n      \"code\": \"117                     # Test and reload nginx\\n118                     subprocess.run([\\\"nginx\\\", \\\"-t\\\"], check=True)\\n119                     subprocess.run([\\\"systemctl\\\", \\\"reload\\\", \\\"nginx\\\"], check=True)\\n\",\n      \"col_offset\": 20,\n      \"end_col_offset\": 63,\n      \"filename\": \"src\\\\deployment.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 118,\n      \"line_range\": [\n        118\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"117                     # Test and reload nginx\\n118                     subprocess.run([\\\"nginx\\\", \\\"-t\\\"], check=True)\\n119                     subprocess.run([\\\"systemctl\\\", \\\"reload\\\", \\\"nginx\\\"], check=True)\\n\",\n      \"col_offset\": 20,\n      \"end_col_offset\": 63,\n      \"filename\": \"src\\\\deployment.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 118,\n      \"line_range\": [\n        118\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"118                     subprocess.run([\\\"nginx\\\", \\\"-t\\\"], check=True)\\n119                     subprocess.run([\\\"systemctl\\\", \\\"reload\\\", \\\"nginx\\\"], check=True)\\n120                 except Exception as nginx_error:\\n\",\n      \"col_offset\": 20,\n      \"end_col_offset\": 80,\n      \"filename\": \"src\\\\deployment.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 119,\n      \"line_range\": [\n        119\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"118                     subprocess.run([\\\"nginx\\\", \\\"-t\\\"], check=True)\\n119                     subprocess.run([\\\"systemctl\\\", \\\"reload\\\", \\\"nginx\\\"], check=True)\\n120                 except Exception as nginx_error:\\n\",\n      \"col_offset\": 20,\n      \"end_col_offset\": 80,\n      \"filename\": \"src\\\\deployment.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 119,\n      \"line_range\": [\n        119\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"9 import shutil\\n10 import subprocess\\n11 import tempfile\\n\",\n      \"col_offset\": 0,\n      \"end_col_offset\": 17,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Consider possible security implications associated with the subprocess module.\",\n      \"line_number\": 10,\n      \"line_range\": [\n        10\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/blacklists/blacklist_imports.html#b404-import-subprocess\",\n      \"test_id\": \"B404\",\n      \"test_name\": \"blacklist\"\n    },\n    {\n      \"code\": \"87             # Run certbot\\n88             result = subprocess.run(cmd, capture_output=True, text=True)\\n89 \\n\",\n      \"col_offset\": 21,\n      \"end_col_offset\": 72,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 88,\n      \"line_range\": [\n        88\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"137             key_path = domain_dir / \\\"privkey.pem\\\"\\n138             subprocess.run(\\n139                 [\\\"openssl\\\", \\\"genrsa\\\", \\\"-out\\\", str(key_path), \\\"2048\\\"], check=True\\n140             )\\n141 \\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 138,\n      \"line_range\": [\n        138,\n        139,\n        140\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"137             key_path = domain_dir / \\\"privkey.pem\\\"\\n138             subprocess.run(\\n139                 [\\\"openssl\\\", \\\"genrsa\\\", \\\"-out\\\", str(key_path), \\\"2048\\\"], check=True\\n140             )\\n141 \\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 138,\n      \"line_range\": [\n        138,\n        139,\n        140\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"143             csr_path = domain_dir / \\\"cert.csr\\\"\\n144             subprocess.run(\\n145                 [\\n146                     \\\"openssl\\\",\\n147                     \\\"req\\\",\\n148                     \\\"-new\\\",\\n149                     \\\"-key\\\",\\n150                     str(key_path),\\n151                     \\\"-out\\\",\\n152                     str(csr_path),\\n153                     \\\"-config\\\",\\n154                     str(config_path),\\n155                 ],\\n156                 check=True,\\n157             )\\n158 \\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 144,\n      \"line_range\": [\n        144,\n        145,\n        146,\n        147,\n        148,\n        149,\n        150,\n        151,\n        152,\n        153,\n        154,\n        155,\n        156,\n        157\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"143             csr_path = domain_dir / \\\"cert.csr\\\"\\n144             subprocess.run(\\n145                 [\\n146                     \\\"openssl\\\",\\n147                     \\\"req\\\",\\n148                     \\\"-new\\\",\\n149                     \\\"-key\\\",\\n150                     str(key_path),\\n151                     \\\"-out\\\",\\n152                     str(csr_path),\\n153                     \\\"-config\\\",\\n154                     str(config_path),\\n155                 ],\\n156                 check=True,\\n157             )\\n158 \\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 144,\n      \"line_range\": [\n        144,\n        145,\n        146,\n        147,\n        148,\n        149,\n        150,\n        151,\n        152,\n        153,\n        154,\n        155,\n        156,\n        157\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"160             cert_path = domain_dir / \\\"fullchain.pem\\\"\\n161             subprocess.run(\\n162                 [\\n163                     \\\"openssl\\\",\\n164                     \\\"x509\\\",\\n165                     \\\"-req\\\",\\n166                     \\\"-days\\\",\\n167                     \\\"365\\\",\\n168                     \\\"-in\\\",\\n169                     str(csr_path),\\n170                     \\\"-signkey\\\",\\n171                     str(key_path),\\n172                     \\\"-out\\\",\\n173                     str(cert_path),\\n174                     \\\"-extensions\\\",\\n175                     \\\"v3_req\\\",\\n176                     \\\"-extfile\\\",\\n177                     str(config_path),\\n178                 ],\\n179                 check=True,\\n180             )\\n181 \\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 161,\n      \"line_range\": [\n        161,\n        162,\n        163,\n        164,\n        165,\n        166,\n        167,\n        168,\n        169,\n        170,\n        171,\n        172,\n        173,\n        174,\n        175,\n        176,\n        177,\n        178,\n        179,\n        180\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"160             cert_path = domain_dir / \\\"fullchain.pem\\\"\\n161             subprocess.run(\\n162                 [\\n163                     \\\"openssl\\\",\\n164                     \\\"x509\\\",\\n165                     \\\"-req\\\",\\n166                     \\\"-days\\\",\\n167                     \\\"365\\\",\\n168                     \\\"-in\\\",\\n169                     str(csr_path),\\n170                     \\\"-signkey\\\",\\n171                     str(key_path),\\n172                     \\\"-out\\\",\\n173                     str(cert_path),\\n174                     \\\"-extensions\\\",\\n175                     \\\"v3_req\\\",\\n176                     \\\"-extfile\\\",\\n177                     str(config_path),\\n178                 ],\\n179                 check=True,\\n180             )\\n181 \\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 161,\n      \"line_range\": [\n        161,\n        162,\n        163,\n        164,\n        165,\n        166,\n        167,\n        168,\n        169,\n        170,\n        171,\n        172,\n        173,\n        174,\n        175,\n        176,\n        177,\n        178,\n        179,\n        180\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"224         try:\\n225             subprocess.run([\\\"openssl\\\", \\\"version\\\"], capture_output=True, check=True)\\n226             return True\\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 83,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 225,\n      \"line_range\": [\n        225\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"224         try:\\n225             subprocess.run([\\\"openssl\\\", \\\"version\\\"], capture_output=True, check=True)\\n226             return True\\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 83,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 225,\n      \"line_range\": [\n        225\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"232         try:\\n233             subprocess.run([\\\"certbot\\\", \\\"--version\\\"], capture_output=True, check=True)\\n234             return True\\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 85,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 233,\n      \"line_range\": [\n        233\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"232         try:\\n233             subprocess.run([\\\"certbot\\\", \\\"--version\\\"], capture_output=True, check=True)\\n234             return True\\n\",\n      \"col_offset\": 12,\n      \"end_col_offset\": 85,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 233,\n      \"line_range\": [\n        233\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"254             # Get certificate expiry date\\n255             result = subprocess.run(\\n256                 [\\\"openssl\\\", \\\"x509\\\", \\\"-in\\\", str(cert_path), \\\"-noout\\\", \\\"-dates\\\"],\\n257                 capture_output=True,\\n258                 text=True,\\n259                 check=True,\\n260             )\\n261 \\n\",\n      \"col_offset\": 21,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"Starting a process with a partial executable path\",\n      \"line_number\": 255,\n      \"line_range\": [\n        255,\n        256,\n        257,\n        258,\n        259,\n        260\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b607_start_process_with_partial_path.html\",\n      \"test_id\": \"B607\",\n      \"test_name\": \"start_process_with_partial_path\"\n    },\n    {\n      \"code\": \"254             # Get certificate expiry date\\n255             result = subprocess.run(\\n256                 [\\\"openssl\\\", \\\"x509\\\", \\\"-in\\\", str(cert_path), \\\"-noout\\\", \\\"-dates\\\"],\\n257                 capture_output=True,\\n258                 text=True,\\n259                 check=True,\\n260             )\\n261 \\n\",\n      \"col_offset\": 21,\n      \"end_col_offset\": 13,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 255,\n      \"line_range\": [\n        255,\n        256,\n        257,\n        258,\n        259,\n        260\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    },\n    {\n      \"code\": \"293 \\n294             result = subprocess.run(cmd, capture_output=True, text=True)\\n295 \\n\",\n      \"col_offset\": 21,\n      \"end_col_offset\": 72,\n      \"filename\": \"src\\\\ssl_manager.py\",\n      \"issue_confidence\": \"HIGH\",\n      \"issue_cwe\": {\n        \"id\": 78,\n        \"link\": \"https://cwe.mitre.org/data/definitions/78.html\"\n      },\n      \"issue_severity\": \"LOW\",\n      \"issue_text\": \"subprocess call - check for execution of untrusted input.\",\n      \"line_number\": 294,\n      \"line_range\": [\n        294\n      ],\n      \"more_info\": \"https://bandit.readthedocs.io/en/1.8.6/plugins/b603_subprocess_without_shell_equals_true.html\",\n      \"test_id\": \"B603\",\n      \"test_name\": \"subprocess_without_shell_equals_true\"\n    }\n  ]\n}", "stderr": "[main]\tINFO\tprofile include tests: None\n[main]\tINFO\tprofile exclude tests: None\n[main]\tINFO\tcli include tests: None\n[main]\tINFO\tcli exclude tests: None\n"}, "safety": {"success": true, "stdout": "", "stderr": "", "message": "Dependency check skipped - not needed for development"}, "pytest": {"success": false, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.13.5, pytest-8.4.1, pluggy-1.6.0\nrootdir: F:\\NasShare\\AICodingAgent\nconfigfile: pyproject.toml\nplugins: anyio-4.9.0, asyncio-1.0.0, cov-6.2.1, mock-3.14.1\nasyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollected 46 items\n\ntests\\test_database_manager.py ......                                    [ 13%]\ntests\\test_deployment.py .....                                           [ 23%]\ntests\\test_model_manager.py ....F..                                      [ 39%]\ntests\\test_requirement_parser.py ....                                    [ 47%]\ntests\\test_simple.py .                                                   [ 50%]\ntests\\test_ssl_security.py ...F......E........                           [ 89%]\ntests\\test_website_generator.py F....                                    [100%]\n\n=================================== ERRORS ====================================\n______ ERROR at teardown of TestSecurityManager.test_rate_limit_exceeded ______\ntests\\test_ssl_security.py:159: in temp_dir\n    shutil.rmtree(temp_dir)\nC:\\Python313\\Lib\\shutil.py:790: in rmtree\n    return _rmtree_unsafe(path, onexc)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\nC:\\Python313\\Lib\\shutil.py:629: in _rmtree_unsafe\n    onexc(os.unlink, fullname, err)\nC:\\Python313\\Lib\\shutil.py:625: in _rmtree_unsafe\n    os.unlink(fullname)\nE   PermissionError: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp83exia76\\\\security.db'\n------------------------------ Captured log call ------------------------------\nERROR    security_manager:security_manager.py:377 Failed to log security event: database is locked\nWARNING  security_manager:security_manager.py:381 Security event: rate_limit_exceeded from 192.168.1.100 - {'request_count': 10, 'limit': 10}\n================================== FAILURES ===================================\n_____________________ TestModelManager.test_get_provider ______________________\ntests\\test_model_manager.py:35: in test_get_provider\n    provider = self.manager.get_provider(\"test\", self.test_model)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsrc\\models\\__init__.py:157: in get_provider\n    return self._providers[name](model_name, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nE   TypeError: Can't instantiate abstract class TestProvider without an implementation for abstract methods 'chat', 'embeddings', 'generate'\n______________ TestSSLManager.test_setup_self_signed_certificate ______________\ntests\\test_ssl_security.py:99: in test_setup_self_signed_certificate\n    assert result[\"status\"] == \"success\"\nE   AssertionError: assert 'error' == 'success'\nE     \nE     - success\nE     + error\n------------------------------ Captured log call ------------------------------\nERROR    ssl_manager:ssl_manager.py:194 Self-signed certificate creation failed: [WinError 2] The system cannot find the file specified: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp_q5kw2g0\\\\ssl\\\\test.example.com\\\\cert.csr'\n__________________ TestWebsiteGenerator.test_create_website ___________________\ntests\\test_website_generator.py:20: in test_create_website\n    template_dir = Path(\"templates/modern\")\n                   ^^^^\nE   NameError: name 'Path' is not defined\n============================== warnings summary ===============================\ntests/test_model_manager.py::TestModelManager::test_chat\n  C:\\Python313\\Lib\\unittest\\case.py:606: RuntimeWarning: coroutine 'TestModelManager.test_chat' was never awaited\n    if method() is not None:\n  Enable tracemalloc to get traceback where the object was allocated.\n  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.\n\ntests/test_model_manager.py::TestModelManager::test_chat\n  C:\\Python313\\Lib\\unittest\\case.py:707: DeprecationWarning: It is deprecated to return a value that is not None from a test case (<bound method TestModelManager.test_chat of <tests.test_model_manager.TestModelManager testMethod=test_chat>>)\n    return self.run(*args, **kwds)\n\ntests/test_model_manager.py::TestModelManager::test_embeddings\n  C:\\Python313\\Lib\\unittest\\case.py:606: RuntimeWarning: coroutine 'TestModelManager.test_embeddings' was never awaited\n    if method() is not None:\n  Enable tracemalloc to get traceback where the object was allocated.\n  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.\n\ntests/test_model_manager.py::TestModelManager::test_embeddings\n  C:\\Python313\\Lib\\unittest\\case.py:707: DeprecationWarning: It is deprecated to return a value that is not None from a test case (<bound method TestModelManager.test_embeddings of <tests.test_model_manager.TestModelManager testMethod=test_embeddings>>)\n    return self.run(*args, **kwds)\n\ntests/test_model_manager.py::TestModelManager::test_generate\n  C:\\Python313\\Lib\\unittest\\case.py:606: RuntimeWarning: coroutine 'TestModelManager.test_generate' was never awaited\n    if method() is not None:\n  Enable tracemalloc to get traceback where the object was allocated.\n  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.\n\ntests/test_model_manager.py::TestModelManager::test_generate\n  C:\\Python313\\Lib\\unittest\\case.py:707: DeprecationWarning: It is deprecated to return a value that is not None from a test case (<bound method TestModelManager.test_generate of <tests.test_model_manager.TestModelManager testMethod=test_generate>>)\n    return self.run(*args, **kwds)\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n=========================== short test summary info ===========================\nERROR tests/test_ssl_security.py::TestSecurityManager::test_rate_limit_exceeded\nFAILED tests/test_model_manager.py::TestModelManager::test_get_provider - Typ...\nFAILED tests/test_ssl_security.py::TestSSLManager::test_setup_self_signed_certificate\nFAILED tests/test_website_generator.py::TestWebsiteGenerator::test_create_website\n============= 3 failed, 43 passed, 6 warnings, 1 error in 23.27s ==============\n", "stderr": ""}}, "summary": {"passed": 3, "failed": 3, "warnings": 1}}