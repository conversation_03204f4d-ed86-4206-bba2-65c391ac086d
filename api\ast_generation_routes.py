#!/usr/bin/env python3
"""
API Routes for AST-Based Code Generation

This module provides REST API endpoints for the AST-based code generation system.
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException
from pydantic import BaseModel, Field

from core.code_generation.ast_code_generator import (
    ASTCodeGenerator,
    CodeLanguage,
    GenerationContext,
    GenerationStrategy,
)
from core.code_generation.ast_manipulator import ASTManipulator
from core.code_generation.code_analyzer import CodeAnalyzer
from core.code_generation.pattern_matcher import CodePatternMatcher

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ast-generation", tags=["AST Code Generation"])


# Pydantic models for request/response
class CodeGenerationRequest(BaseModel):
    language: str = Field(
        ...,
        description="Programming language (python, typescript, javascript, jsx, tsx)",
    )
    target_file: Optional[str] = Field(None, description="Target file path")
    requirements: List[str] = Field(..., description="Code requirements")
    constraints: List[str] = Field(default=[], description="Code constraints")
    conventions: Dict[str, Any] = Field(default={}, description="Code conventions")
    imports: List[str] = Field(default=[], description="Import statements")
    dependencies: List[str] = Field(default=[], description="Dependencies")
    strategy: str = Field(default="hybrid", description="Generation strategy")
    metadata: Dict[str, Any] = Field(default={}, description="Additional metadata")


class CodeGenerationResponse(BaseModel):
    success: bool
    code: Optional[str] = None
    language: Optional[str] = None
    complexity_score: Optional[float] = None
    quality_score: Optional[float] = None
    generation_time: Optional[float] = None
    strategy_used: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class CodeAnalysisRequest(BaseModel):
    code: str = Field(..., description="Code to analyze")
    language: str = Field(default="python", description="Programming language")


class CodeAnalysisResponse(BaseModel):
    success: bool
    analysis: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class CodeRefactoringRequest(BaseModel):
    code: str = Field(..., description="Code to refactor")
    language: str = Field(default="python", description="Programming language")
    refactoring_rules: List[Dict[str, Any]] = Field(
        ..., description="Refactoring rules"
    )


class CodeRefactoringResponse(BaseModel):
    success: bool
    original_code: Optional[str] = None
    refactored_code: Optional[str] = None
    refactoring_rules: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None


class CodeMergeRequest(BaseModel):
    existing_code: str = Field(..., description="Existing code")
    new_code: str = Field(..., description="New code to merge")
    language: str = Field(default="python", description="Programming language")


class CodeMergeResponse(BaseModel):
    success: bool
    existing_code: Optional[str] = None
    new_code: Optional[str] = None
    merged_code: Optional[str] = None
    error: Optional[str] = None


class PatternRequest(BaseModel):
    requirements: List[str] = Field(
        ..., description="Requirements for pattern matching"
    )
    language: str = Field(default="python", description="Programming language")


class PatternResponse(BaseModel):
    success: bool
    patterns: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None


class CodeValidationRequest(BaseModel):
    code: str = Field(..., description="Code to validate")
    language: str = Field(default="python", description="Programming language")


class CodeValidationResponse(BaseModel):
    success: bool
    is_valid: Optional[bool] = None
    language: Optional[str] = None
    ast_node_count: Optional[int] = None
    error: Optional[str] = None


class PatternExportRequest(BaseModel):
    file_path: str = Field(..., description="File path for export")


class PatternExportResponse(BaseModel):
    success: bool
    file_path: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None


class PatternImportRequest(BaseModel):
    file_path: str = Field(..., description="File path for import")


class PatternImportResponse(BaseModel):
    success: bool
    imported_count: Optional[int] = None
    file_path: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None


class MetricsResponse(BaseModel):
    success: bool
    generation_metrics: Optional[Dict[str, Any]] = None
    analysis_metrics: Optional[Dict[str, Any]] = None
    manipulation_metrics: Optional[Dict[str, Any]] = None
    pattern_metrics: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class CleanupResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    error: Optional[str] = None


# Global instances
ast_generator = ASTCodeGenerator()
ast_manipulator = ASTManipulator()
code_analyzer = CodeAnalyzer()
pattern_matcher = CodePatternMatcher()


@router.post("/generate", response_model=CodeGenerationResponse)
async def generate_code(request: CodeGenerationRequest):
    """Generate code using AST-based generation"""
    try:
        language = CodeLanguage(request.language)
        strategy = GenerationStrategy(request.strategy)

        context = GenerationContext(
            language=language,
            target_file=request.target_file or f"generated.{request.language}",
            requirements=request.requirements,
            constraints=request.constraints,
            conventions=request.conventions,
            imports=request.imports,
            dependencies=request.dependencies,
            metadata=request.metadata,
        )

        result = await ast_generator.generate_code(context, strategy)

        return CodeGenerationResponse(
            success=True,
            code=result.code,
            language=result.language.value,
            complexity_score=result.complexity_score,
            quality_score=result.quality_score,
            generation_time=result.generation_time,
            strategy_used=result.strategy_used.value,
            metadata=result.metadata,
        )

    except Exception as e:
        logger.error(f"Code generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze", response_model=CodeAnalysisResponse)
async def analyze_code(request: CodeAnalysisRequest):
    """Analyze existing code using AST analysis"""
    try:
        if not request.code:
            raise HTTPException(status_code=400, detail="No code provided for analysis")

        analysis = await code_analyzer.analyze_code(request.code, request.language)

        return CodeAnalysisResponse(
            success=True,
            analysis={
                "structure": {
                    "functions": analysis.structure.functions,
                    "classes": analysis.structure.classes,
                    "imports": analysis.structure.imports,
                    "variables": analysis.structure.variables,
                    "total_lines": analysis.structure.total_lines,
                },
                "complexity": {
                    "cyclomatic_complexity": analysis.complexity.cyclomatic_complexity,
                    "cognitive_complexity": analysis.complexity.cognitive_complexity,
                    "nesting_depth": analysis.complexity.nesting_depth,
                    "complexity_level": analysis.complexity.complexity_level.value,
                },
                "quality": {
                    "maintainability_index": analysis.quality.maintainability_index,
                    "code_smells": analysis.quality.code_smells,
                    "violations": analysis.quality.violations,
                    "overall_quality_score": analysis.quality.overall_quality_score,
                },
                "dependencies": analysis.dependencies,
                "context": analysis.context,
            },
        )

    except Exception as e:
        logger.error(f"Code analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refactor", response_model=CodeRefactoringResponse)
async def refactor_code(request: CodeRefactoringRequest):
    """Refactor code using AST transformations"""
    try:
        if not request.code:
            raise HTTPException(
                status_code=400, detail="No code provided for refactoring"
            )

        if not request.refactoring_rules:
            raise HTTPException(status_code=400, detail="No refactoring rules provided")

        language_enum = CodeLanguage(request.language)
        refactored_code = await ast_generator.refactor_code(
            request.code, request.refactoring_rules, language_enum
        )

        return CodeRefactoringResponse(
            success=True,
            original_code=request.code,
            refactored_code=refactored_code,
            refactoring_rules=request.refactoring_rules,
        )

    except Exception as e:
        logger.error(f"Code refactoring failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/merge", response_model=CodeMergeResponse)
async def merge_code(request: CodeMergeRequest):
    """Merge two code files using AST merging"""
    try:
        if not request.existing_code or not request.new_code:
            raise HTTPException(
                status_code=400,
                detail="Both existing_code and new_code must be provided",
            )

        language_enum = CodeLanguage(request.language)
        merged_code = await ast_generator.merge_code(
            request.existing_code, request.new_code, language_enum
        )

        return CodeMergeResponse(
            success=True,
            existing_code=request.existing_code,
            new_code=request.new_code,
            merged_code=merged_code,
        )

    except Exception as e:
        logger.error(f"Code merging failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/patterns", response_model=PatternResponse)
async def find_patterns(request: PatternRequest):
    """Find applicable code patterns"""
    try:
        if not request.requirements:
            raise HTTPException(
                status_code=400, detail="No requirements provided for pattern matching"
            )

        patterns = await pattern_matcher.find_applicable_patterns(
            request.requirements, request.language
        )

        return PatternResponse(
            success=True,
            patterns=[
                {
                    "name": pattern.name,
                    "pattern_type": pattern.pattern_type.value,
                    "category": pattern.category.value,
                    "language": pattern.language,
                    "description": pattern.description,
                    "usage_count": pattern.usage_count,
                    "success_rate": pattern.success_rate,
                    "complexity_score": pattern.complexity_score,
                }
                for pattern in patterns
            ],
        )

    except Exception as e:
        logger.error(f"Pattern finding failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics", response_model=MetricsResponse)
async def get_metrics():
    """Get generation and analysis metrics"""
    try:
        generation_metrics = await ast_generator.get_generation_metrics()
        analysis_metrics = await code_analyzer.get_analysis_metrics()
        manipulation_metrics = await ast_manipulator.get_manipulation_metrics()
        pattern_metrics = await pattern_matcher.get_matching_metrics()

        return MetricsResponse(
            success=True,
            generation_metrics=generation_metrics,
            analysis_metrics=analysis_metrics,
            manipulation_metrics=manipulation_metrics,
            pattern_metrics=pattern_metrics,
        )

    except Exception as e:
        logger.error(f"Metrics retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate", response_model=CodeValidationResponse)
async def validate_code(request: CodeValidationRequest):
    """Validate generated code"""
    try:
        if not request.code:
            raise HTTPException(
                status_code=400, detail="No code provided for validation"
            )

        language_enum = CodeLanguage(request.language)
        ast_tree = await ast_generator._parse_code(request.code, language_enum)
        is_valid = await ast_manipulator.validate_ast(ast_tree)

        import ast

        return CodeValidationResponse(
            success=True,
            is_valid=is_valid,
            language=request.language,
            ast_node_count=len(list(ast.walk(ast_tree))) if ast_tree else 0,
        )

    except Exception as e:
        logger.error(f"Code validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export-patterns", response_model=PatternExportResponse)
async def export_patterns(request: PatternExportRequest):
    """Export code patterns to file"""
    try:
        success = await pattern_matcher.export_patterns(request.file_path)

        return PatternExportResponse(
            success=success,
            file_path=request.file_path,
            message=(
                "Patterns exported successfully"
                if success
                else "Failed to export patterns"
            ),
        )

    except Exception as e:
        logger.error(f"Pattern export failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import-patterns", response_model=PatternImportResponse)
async def import_patterns(request: PatternImportRequest):
    """Import code patterns from file"""
    try:
        from pathlib import Path

        if not Path(request.file_path).exists():
            raise HTTPException(
                status_code=404, detail=f"Pattern file not found: {request.file_path}"
            )

        imported_count = await pattern_matcher.import_patterns(request.file_path)

        return PatternImportResponse(
            success=True,
            imported_count=imported_count,
            file_path=request.file_path,
            message=f"Successfully imported {imported_count} patterns",
        )

    except Exception as e:
        logger.error(f"Pattern import failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup", response_model=CleanupResponse)
async def cleanup():
    """Cleanup resources"""
    try:
        await ast_generator.cleanup()
        await ast_manipulator.cleanup()
        await code_analyzer.cleanup()
        await pattern_matcher.cleanup()

        return CleanupResponse(success=True, message="Cleanup completed successfully")

    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "AST Code Generation API",
        "version": "1.0.0",
    }


@router.get("/languages")
async def get_supported_languages():
    """Get supported programming languages"""
    return {
        "languages": [{"value": lang.value, "name": lang.name} for lang in CodeLanguage]
    }


@router.get("/strategies")
async def get_generation_strategies():
    """Get available generation strategies"""
    return {
        "strategies": [
            {"value": strategy.value, "name": strategy.name}
            for strategy in GenerationStrategy
        ]
    }
