import asyncio
import os
from typing import Any, Dict, Optional

import httpx

FINE_TUNER_HOST = os.getenv("FINE_TUNER_SERVICE_HOST", "http://localhost")
FINE_TUNER_PORT = os.getenv("FINE_TUNER_PORT", "8002")
BASE_URL = f"{FINE_TUNER_HOST}:{FINE_TUNER_PORT}"


class FineTunerCommands:
    """
    CLI command set for managing the Fine Tuner service.
    Designed to be imported into the existing CLI entrypoint and invoked by local LLMs.
    """

    def __init__(self) -> None:
        pass

    async def health(self) -> Dict[str, Any]:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/health")
            r.raise_for_status()
            return r.json()

    async def status(self) -> Dict[str, Any]:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/status")
            r.raise_for_status()
            return r.json()

    async def start(
        self,
        dataset_path: Optional[str] = None,
        model_name: Optional[str] = None,
        epochs: Optional[int] = 1,
        learning_rate: Optional[float] = 1e-4,
        output_dir: Optional[str] = "outputs",
    ) -> Dict[str, Any]:
        payload = {
            "dataset_path": dataset_path,
            "model_name": model_name,
            "epochs": epochs,
            "learning_rate": learning_rate,
            "output_dir": output_dir,
        }
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return r.json()

    async def stop(self) -> Dict[str, Any]:
        async with httpx.AsyncClient(timeout=15) as client:
            r = await client.post(f"{BASE_URL}/stop")
            r.raise_for_status()
            return r.json()

    async def progress(self) -> Dict[str, Any]:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/progress")
            r.raise_for_status()
            return r.json()

    async def evaluate(
        self, model_artifact: str, eval_dataset_path: str
    ) -> Dict[str, Any]:
        payload = {
            "model_artifact": model_artifact,
            "eval_dataset_path": eval_dataset_path,
        }
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/evaluate", json=payload)
            r.raise_for_status()
            return r.json()


# Optional: quick manual test
if __name__ == "__main__":

    async def _demo() -> None:
        cli = FineTunerCommands()
        print(await cli.health())
        print(await cli.status())

    asyncio.run(_demo())
