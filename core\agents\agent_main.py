#!/usr/bin/env python3
"""
Main Agent Module - Unified AI Agent Implementation
Imports from modular components for clean organization
"""

import argparse
import asyncio
import json
import logging
import os
import signal
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from cli.advanced_learning_commands import AdvancedLearningCommands
from cli.cms_commands import CMSCommands
from cli.maintenance_commands import MaintenanceEngine
from cli.ssl_commands import SSLManager
from cli.testing_commands import TestingHarness
from config import get_config
from core.agents.base_agent import Agent<PERSON><PERSON>r, Agent<PERSON>ogger, ErrorHandler
from core.agents.feedback_manager import FeedbackManager
from core.agents.plugin_manager import PluginInterface, PluginManager
from core.agents.task_manager import CommandRouter, TaskManager
from core.managers.deployment_manager import DeploymentManager
from core.website_generator import WebsiteGenerator
from db.database_manager import DatabaseManager
from learning.advanced_learning_enhancements_part2 import AdvancedLearningEnhancements
from models.model_router import ModelRouter
from security.security_manager import SecurityManager

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import modular components

# Import external dependencies

# Import automated learning system
try:
    from learning.adaptive_learning_system import AdaptiveLearningSystem
    from learning.advanced_learning_enhancements import (
        AdversarialDetector,
        CapabilityDiscovery,
        CascadePredictor,
        DegradationManager,
        FederatedLearningManager,
        MetaLearningOptimizer,
        ParetoOptimizer,
        WorkloadPredictor,
    )
    from learning.automated_learner import AutomatedLearner
    from learning.best_practices_learner import BestPracticesLearner
    from monitoring.framework_monitor import FrameworkMonitor

    ADVANCED_LEARNING_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Advanced learning system not available: {e}")
    MetaLearningOptimizer = None
    ParetoOptimizer = None
    WorkloadPredictor = None
    CascadePredictor = None
    FederatedLearningManager = None
    CapabilityDiscovery = None
    AdversarialDetector = None
    DegradationManager = None
    AdaptiveLearningSystem = None
    AutomatedLearner = None
    BestPracticesLearner = None
    FrameworkMonitor = None
    ADVANCED_LEARNING_AVAILABLE = False


class AIAgent:
    """Main AI Coding Agent - Unified Entrypoint"""

    def __init__(
        self,
        config: str,
        automated_learner: bool = True,
        best_practices_learner: bool = True,
        framework_monitor: bool = True,
    ):
        """
        Initialize the AI Agent with a required configuration file.

        :param config: Path to the JSON configuration file for all sub-agents.
        :param automated_learner: Whether to enable automated learning capabilities.
        :param best_practices_learner: Whether to enable best practices learning.
        :param framework_monitor: Whether to enable framework monitoring.
        """
        assert config, "A config file path is required"

        # Initialize core components
        self.logger = AgentLogger()
        self.error_handler = ErrorHandler(self.logger)
        self.feedback_manager = FeedbackManager(self.logger)
        self.task_manager = TaskManager(self)
        self.plugin_manager = PluginManager(self)

        # Load configuration
        self.config = get_config(config)

        # Initialize components
        self._initialize_components()

        # Initialize learning systems
        self._initialize_learning_systems(
            automated_learner, best_practices_learner, framework_monitor
        )

        # Create integration systems
        self._create_notification_system()
        self._create_ide_integration()
        self._create_security_enhancement()

        self.logger.info("AI Coding Agent initialized successfully")

    def _initialize_components(self):
        """Initialize all agent components"""
        try:
            # Initialize CLI components with correct parameters
            self.cms_commands = CMSCommands("config/cms_config.json")
            self.maintenance_engine = MaintenanceEngine(
                "config/maintenance_config.json"
            )

            # Initialize TestingHarness with TestConfig
            from tests.testing_harness import TestConfig

            test_config = TestConfig()
            self.testing_harness = TestingHarness(test_config)

            self.ssl_manager = SSLManager("config/ssl_config.json")

            # Initialize core managers with correct parameters
            self.security_manager = SecurityManager("config/security_config.json")
            self.deployment_manager = DeploymentManager()

            # Initialize WebsiteGenerator with required dependencies
            from templates.template_manager import TemplateManager

            template_manager = TemplateManager()
            self.website_generator = WebsiteGenerator(template_manager)

            self.model_router = ModelRouter()

            # Initialize DatabaseManager with a default model
            from db.models import Project

            self.database_manager = DatabaseManager(Project)

            # Initialize learning components
            self.advanced_learning = AdvancedLearningEnhancements()
            self.advanced_learning_commands = AdvancedLearningCommands(self)

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}", error=e)
            raise

    def _initialize_learning_systems(
        self,
        automated_learner: bool,
        best_practices_learner: bool,
        framework_monitor: bool,
    ):
        """Initialize learning systems if available"""
        if not ADVANCED_LEARNING_AVAILABLE:
            self.logger.warning("Advanced learning systems not available")
            return

        try:
            if automated_learner and AutomatedLearner is not None:
                self.automated_learner = AutomatedLearner()
            if best_practices_learner and BestPracticesLearner is not None:
                self.best_practices_learner = BestPracticesLearner()
            if framework_monitor and FrameworkMonitor is not None:
                self.framework_monitor = FrameworkMonitor()

            self.logger.info("Learning systems initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize learning systems: {e}", error=e)

    def _create_notification_system(self):
        """Create notification system"""
        try:

            class NotificationSystem:
                def __init__(self):
                    self.subscribers = []
                    self.notifications = []

                def subscribe(self, callback):
                    self.subscribers.append(callback)

                def notify(self, message, level="info"):
                    notification = {
                        "message": message,
                        "level": level,
                        "timestamp": datetime.now().isoformat(),
                    }
                    self.notifications.append(notification)

                    # Notify subscribers
                    for callback in self.subscribers:
                        try:
                            callback(notification)
                        except Exception as e:
                            # Use print instead of logger since this is a nested class
                            print(f"Error in notification callback: {e}")

                def get_notifications(self, limit=50):
                    return self.notifications[-limit:]

            self.notification_system = NotificationSystem()
            self.logger.info("Notification system created")

        except Exception as e:
            self.logger.error(f"Failed to create notification system: {e}", error=e)

    def _create_ide_integration(self):
        """Create IDE integration system"""
        try:

            class IDEIntegration:
                def __init__(self):
                    self.clients = {}
                    self.file_watchers = {}

                def connect_client(self, client_id, callback):
                    self.clients[client_id] = callback
                    print(f"Client {client_id} connected")

                def disconnect_client(self, client_id):
                    if client_id in self.clients:
                        del self.clients[client_id]
                        print(f"Client {client_id} disconnected")

                def broadcast_update(self, update_type, data):
                    for client_id, callback in self.clients.items():
                        try:
                            callback(update_type, data)
                        except Exception as e:
                            print(f"Error broadcasting to client {client_id}: {e}")

                def watch_file(self, file_path, callback):
                    self.file_watchers[file_path] = callback

                def unwatch_file(self, file_path):
                    if file_path in self.file_watchers:
                        del self.file_watchers[file_path]

            self.ide_integration = IDEIntegration()
            self.logger.info("IDE integration created")

        except Exception as e:
            self.logger.error(f"Failed to create IDE integration: {e}", error=e)

    def _create_security_enhancement(self):
        """Create security enhancement system"""
        try:

            class SecurityEnhancement:
                def __init__(self):
                    self.security_checks = []
                    self.threat_detectors = []
                    self.vulnerability_scanners = []

                def add_security_check(self, check_function):
                    self.security_checks.append(check_function)

                def add_threat_detector(self, detector):
                    self.threat_detectors.append(detector)

                def add_vulnerability_scanner(self, scanner):
                    self.vulnerability_scanners.append(scanner)

                async def run_security_audit(self):
                    results = {
                        "security_checks": [],
                        "threats_detected": [],
                        "vulnerabilities": [],
                    }

                    # Run security checks
                    for check in self.security_checks:
                        try:
                            result = await check()
                            results["security_checks"].append(result)
                        except Exception as e:
                            print(f"Security check failed: {e}")

                    # Run threat detection
                    for detector in self.threat_detectors:
                        try:
                            threats = await detector()
                            results["threats_detected"].extend(threats)
                        except Exception as e:
                            print(f"Threat detection failed: {e}")

                    # Run vulnerability scanning
                    for scanner in self.vulnerability_scanners:
                        try:
                            vulnerabilities = await scanner()
                            results["vulnerabilities"].extend(vulnerabilities)
                        except Exception as e:
                            print(f"Vulnerability scanning failed: {e}")

                    return results

            self.security_enhancement = SecurityEnhancement()
            self.logger.info("Security enhancement created")

        except Exception as e:
            self.logger.error(f"Failed to create security enhancement: {e}", error=e)

    async def execute_command(
        self, command: str, args: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a command using the task manager"""
        return await self.task_manager.execute_task(command, args or {})

    # Command handlers - these will be implemented in the main agent file
    # For now, we'll create placeholder methods that can be overridden

    async def create_content(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Create content - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def list_content(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """List content - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def update_content(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Update content - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def delete_content(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Delete content - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def generate_content(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Generate content - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def check_links(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Check links - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def update_dependencies(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Update dependencies - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def approve_updates(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Approve updates - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def maintenance_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Maintenance status - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def run_maintenance(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Run maintenance - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def test_html(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Test HTML - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def test_css(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Test CSS - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def test_playwright(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Test Playwright - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def test_deployment(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Test deployment - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def test_suite(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Test suite - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def test_results(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Test results - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def deploy(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def rollback(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Rollback - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def deployment_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Deployment status - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def list_deployments(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """List deployments - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def generate_site(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Generate site - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def list_templates(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """List templates - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def customize_theme(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Customize theme - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def ssl_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """SSL status - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def ssl_renew(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """SSL renew - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def ssl_install(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """SSL install - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def security_scan(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Security scan - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def security_audit(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Security audit - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def security_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Security status - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def system_health(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """System health - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def system_config(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """System config - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def system_logs(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """System logs - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def run_enhancement_cycle(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Run enhancement cycle - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def get_enhancement_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Get enhancement status - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def enable_enhancement(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Enable enhancement - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def disable_enhancement(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Disable enhancement - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def optimize_model(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize model - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def get_optimization_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Get optimization status - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def record_model_performance(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Record model performance - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def get_optimization_recommendations(
        self, args: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get optimization recommendations - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def get_learning_summary(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Get learning summary - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def get_learning_recommendations(
        self, args: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get learning recommendations - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def record_learning_event(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Record learning event - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def learn_code_pattern(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Learn code pattern - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def learn_user_preference(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Learn user preference - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def learn_performance_insight(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Learn performance insight - placeholder"""
        return {"success": False, "error": "Method not implemented"}

    async def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            "success": True,
            "status": "running",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "logger": "active",
                "error_handler": "active",
                "feedback_manager": "active",
                "task_manager": "active",
                "plugin_manager": "active",
            },
        }

    async def shutdown(self):
        """Shutdown the agent gracefully"""
        try:
            self.logger.info("Shutting down AI Coding Agent...")

            # Cleanup plugins
            for plugin_name in list(self.plugin_manager.plugins.keys()):
                self.plugin_manager.unregister_plugin(plugin_name)

            # Close database connections if database_manager exists and has a close method
            if hasattr(self, "database_manager") and self.database_manager is not None:
                if hasattr(self.database_manager, "close"):
                    try:
                        await self.database_manager.close()
                    except Exception as e:
                        self.logger.warning(f"Error closing database manager: {e}")
                elif hasattr(self.database_manager, "close") and callable(
                    getattr(self.database_manager, "close")
                ):
                    try:
                        self.database_manager.close()
                    except Exception as e:
                        self.logger.warning(f"Error closing database manager: {e}")

            self.logger.info("AI Coding Agent shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}", error=e)


# Global agent instance
_agent_instance = None


def get_agent():
    """Get or create the global agent instance"""
    global _agent_instance
    if _agent_instance is None:
        try:
            _agent_instance = AIAgent("config/smart_routing_config.json")
        except Exception as e:
            print(f"Failed to create agent instance: {e}")

            # Create a mock agent for fallback
            class MockAIAgent:
                async def execute_command(self, command, args=None):
                    return {"success": False, "error": "Agent not available"}

                async def get_status(self):
                    return {"success": False, "status": "error"}

                async def shutdown(self):
                    pass

            _agent_instance = MockAIAgent()
    return _agent_instance


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\nShutting down...")
    if _agent_instance:
        asyncio.create_task(_agent_instance.shutdown())
    sys.exit(0)


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
