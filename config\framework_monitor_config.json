{"framework_monitor": {"enabled": true, "monitoring_interval": 1800, "frameworks": [{"name": "react", "version_check_url": "https://registry.npmjs.org/react/latest", "current_version": "18.2.0", "update_check_enabled": true}, {"name": "next.js", "version_check_url": "https://registry.npmjs.org/next/latest", "current_version": "14.0.0", "update_check_enabled": true}, {"name": "python", "version_check_url": "https://www.python.org/downloads/", "current_version": "3.11.0", "update_check_enabled": true}, {"name": "<PERSON><PERSON><PERSON>", "version_check_url": "https://pypi.org/pypi/fastapi/json", "current_version": "0.104.0", "update_check_enabled": true}], "security_monitoring": {"enabled": true, "vulnerability_check_interval": 3600, "sources": ["https://nvd.nist.gov/vuln/data-feeds", "https://github.com/advisories"]}, "performance_monitoring": {"enabled": true, "metrics": ["response_time", "memory_usage", "cpu_usage", "error_rate"], "alert_thresholds": {"response_time_ms": 1000, "memory_usage_percent": 80, "cpu_usage_percent": 70, "error_rate_percent": 5}}, "notifications": {"enabled": true, "channels": ["console", "log", "api", "email"], "alert_levels": ["info", "warning", "critical"]}, "storage": {"data_path": "data/framework_monitoring/", "retention_days": 30, "backup_enabled": true}}}