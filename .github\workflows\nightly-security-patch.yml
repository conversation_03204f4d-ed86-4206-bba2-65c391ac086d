name: Nightly Security Patch
true:
  schedule:
  - cron: 0 2 * * *
  workflow_dispatch:
    inputs:
      dry_run:
        default: 'false'
        description: Run in dry-run mode (scan only, no patches)
        required: false
        type: boolean
      verbose:
        default: 'false'
        description: Enable verbose logging
        required: false
        type: boolean
env:
  PIP_DISABLE_PIP_VERSION_CHECK: 1
  PYTHONUNBUFFERED: 1
jobs:
  security-patch:
    permissions:
      contents: write
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        cache: pip
        python-version: '3.11'
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        cache: npm
        node-version: '18'
    - name: Create virtual environment
      run: 'python -m venv .venv

        source .venv/bin/activate

        echo "VIRTUAL_ENV=$VIRTUAL_ENV" >> $GITHUB_ENV

        echo "$VIRTUAL_ENV/bin" >> $GITHUB_PATH

        '
    - name: Install Python dependencies
      run: 'pip install --upgrade pip

        pip install -r requirements.txt

        pip install pip-audit requests

        '
    - name: Install Node.js dependencies
      run: 'npm ci

        '
    - name: Configure Git
      run: 'git config --global user.name "GitHub Actions Security Bot"

        git config --global user.email "<EMAIL>"

        '
    - env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      id: security_patch
      name: Run security patch script
      run: "# Determine if this is a dry run\nDRY_RUN_FLAG=\"\"\nif [ \"${{ github.event.inputs.dry_run\
        \ }}\" = \"true\" ]; then\n  DRY_RUN_FLAG=\"--dry-run\"\nfi\n\n# Determine\
        \ if verbose logging is enabled\nVERBOSE_FLAG=\"\"\nif [ \"${{ github.event.inputs.verbose\
        \ }}\" = \"true\" ]; then\n  VERBOSE_FLAG=\"--verbose\"\nfi\n\n# Run the security\
        \ patch script\npython scripts/auto_security_patch.py $DRY_RUN_FLAG $VERBOSE_FLAG\
        \ --project-root .\n\n# Capture exit code for later use\necho \"patch_exit_code=$?\"\
        \ >> $GITHUB_OUTPUT\n"
    - id: git_check
      if: steps.security_patch.outputs.patch_exit_code == 0
      name: Check for changes
      run: "if git diff --quiet && git diff --cached --quiet; then\n  echo \"changes=false\"\
        \ >> $GITHUB_OUTPUT\n  echo \"No changes detected\"\nelse\n  echo \"changes=true\"\
        \ >> $GITHUB_OUTPUT\n  echo \"Changes detected\"\n  git status --porcelain\n\
        fi\n"
    - if: steps.git_check.outputs.changes == 'true' && github.event.inputs.dry_run
        != 'true'
      name: Commit and push changes
      run: '# Add all changes

        git add .


        # Create commit with security patch details

        COMMIT_MSG="chore: automated security patches $(date ''+%Y-%m-%d %H:%M:%S
        UTC'')


        - Applied critical security patches automatically

        - Generated by: scripts/auto_security_patch.py

        - Workflow: ${{ github.workflow }}

        - Run ID: ${{ github.run_id }}"


        git commit -m "$COMMIT_MSG"


        # Push changes

        git push origin main

        '
    - if: steps.security_patch.outputs.patch_exit_code == 2
      name: Create issue for manual intervention
      uses: actions/github-script@v7
      with:
        script: "const fs = require('fs');\nconst path = require('path');\n\n// Find\
          \ the latest security patch report\nconst logsDir = 'logs';\nif (fs.existsSync(logsDir))\
          \ {\n  const files = fs.readdirSync(logsDir)\n    .filter(file => file.startsWith('security_patch_report_')\
          \ && file.endsWith('.json'))\n    .sort()\n    .reverse();\n\n  if (files.length\
          \ > 0) {\n    const reportPath = path.join(logsDir, files[0]);\n    const\
          \ report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));\n\n    const\
          \ summary = report.summary || {};\n    const sessionInfo = report.session_info\
          \ || {};\n\n    const issueBody = `\n# \U0001F6A8 Security Vulnerabilities\
          \ Require Manual Intervention\n\nThe automated security patch process found\
          \ critical vulnerabilities that require manual intervention.\n\n## Summary\n\
          - **Session ID**: ${sessionInfo.session_id || 'N/A'}\n- **Scan Time**: ${sessionInfo.start_time\
          \ || 'N/A'}\n- **Critical Vulnerabilities Found**: ${summary.total_critical_vulnerabilities_found\
          \ || 0}\n- **Automatically Patched**: ${summary.total_vulnerabilities_patched\
          \ || 0}\n- **Require Manual Intervention**: ${summary.total_vulnerabilities_failed\
          \ || 0}\n\n## Next Steps\n1. Review the full security patch report: \\`${reportPath}\\\
          `\n2. Manually investigate and fix the remaining vulnerabilities\n3. Run\
          \ the security patch script again to verify fixes\n4. Consider updating\
          \ the automated patching logic if needed\n\n## Workflow Details\n- **Workflow**:\
          \ ${context.workflow}\n- **Run ID**: ${context.runId}\n- **Triggered by**:\
          \ ${context.eventName}\n\nThis issue was automatically created by the nightly\
          \ security patch workflow.\n    `;\n\n    await github.rest.issues.create({\n\
          \      owner: context.repo.owner,\n      repo: context.repo.repo,\n    \
          \  title: `\U0001F6A8 Critical Security Vulnerabilities Require Manual Intervention\
          \ - ${new Date().toISOString().split('T')[0]}`,\n      body: issueBody,\n\
          \      labels: ['security', 'critical', 'manual-intervention-required']\n\
          \    });\n  }\n}\n"
    - if: always()
      name: Upload security patch reports
      uses: actions/upload-artifact@v4
      with:
        name: security-patch-reports-${{ github.run_id }}
        path: logs/security_patch_report_*.json
        retention-days: 30
    - if: always()
      name: Upload logs
      uses: actions/upload-artifact@v4
      with:
        name: security-patch-logs-${{ github.run_id }}
        path: logs/auto_security_patch.log
        retention-days: 7
    - env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: failure()
      name: Notify on failure
      uses: actions/github-script@v7
      with:
        script: "const { SLACK_WEBHOOK_URL } = process.env;\n\nif (SLACK_WEBHOOK_URL)\
          \ {\n  const payload = {\n    text: \"\u274C Nightly Security Patch Failed\"\
          ,\n    blocks: [\n      {\n        type: \"header\",\n        text: {\n\
          \          type: \"plain_text\",\n          text: \"\U0001F6A8 Security\
          \ Patch Workflow Failed\"\n        }\n      },\n      {\n        type: \"\
          section\",\n        text: {\n          type: \"mrkdwn\",\n          text:\
          \ `*Workflow*: ${context.workflow}\\n*Run ID*: ${context.runId}\\n*Repository*:\
          \ ${context.repo.owner}/${context.repo.repo}`\n        }\n      },\n   \
          \   {\n        type: \"section\",\n        text: {\n          type: \"mrkdwn\"\
          ,\n          text: `[View Workflow Run](https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})`\n\
          \        }\n      }\n    ]\n  };\n\n  await fetch(SLACK_WEBHOOK_URL, {\n\
          \    method: 'POST',\n    headers: { 'Content-Type': 'application/json'\
          \ },\n    body: JSON.stringify(payload)\n  });\n}\n"
    timeout-minutes: 30
'on':
- push
- pull_request
