#!/usr/bin/env python3
"""
Advanced Recovery System for AI Coding Agent
Provides automatic detection, diagnosis, and recovery from various system failures.
"""

import asyncio
import json
import logging
import os
import shutil
import subprocess
import sys
import time
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

logger = logging.getLogger(__name__)


class FailureType(Enum):
    """Types of failures the system can detect and recover from"""

    FILE_CORRUPTION = "file_corruption"
    DATABASE_CORRUPTION = "database_corruption"
    CONFIGURATION_ERROR = "configuration_error"
    DEPENDENCY_MISSING = "dependency_missing"
    PERMISSION_ERROR = "permission_error"
    DISK_SPACE_LOW = "disk_space_low"
    MEMORY_LOW = "memory_low"
    NETWORK_ERROR = "network_error"
    PROCESS_HANG = "process_hang"
    IMPORT_ERROR = "import_error"
    VALIDATION_ERROR = "validation_error"
    BACKUP_FAILURE = "backup_failure"
    DEPLOYMENT_FAILURE = "deployment_failure"


@dataclass
class FailureEvent:
    """Represents a detected failure event"""

    failure_type: FailureType
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    timestamp: datetime
    context: Dict[str, Any]
    auto_recoverable: bool = True
    recovery_attempts: int = 0
    max_recovery_attempts: int = 3


class RecoveryAction:
    """Base class for recovery actions"""

    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.execution_time: Optional[float] = None
        self.success: Optional[bool] = None
        self.error_message: Optional[str] = None

    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the recovery action"""
        start_time = time.time()
        try:
            result = await self._execute_impl(context)
            self.success = result
            self.execution_time = time.time() - start_time
            return result
        except Exception as e:
            self.success = False
            self.error_message = str(e)
            self.execution_time = time.time() - start_time
            logger.error(f"Recovery action {self.name} failed: {e}")
            return False

    async def _execute_impl(self, context: Dict[str, Any]) -> bool:
        """Implementation of the recovery action"""
        raise NotImplementedError


class FileCorruptionRecovery(RecoveryAction):
    """Recovers from file corruption by restoring from backup"""

    def __init__(self):
        super().__init__(
            "file_corruption_recovery", "Restore corrupted files from backup"
        )

    async def _execute_impl(self, context: Dict[str, Any]) -> bool:
        file_path = context.get("file_path")
        if not file_path:
            return False

        try:
            # Find latest backup of the file
            backup_path = await self._find_file_backup(file_path)
            if backup_path and backup_path.exists():
                # Restore the file
                shutil.copy2(backup_path, file_path)
                logger.info(f"Restored corrupted file {file_path} from backup")
                return True
            else:
                logger.error(f"No backup found for corrupted file {file_path}")
                return False
        except Exception as e:
            logger.error(f"Failed to restore file {file_path}: {e}")
            return False

    async def _find_file_backup(self, file_path: str) -> Optional[Path]:
        """Find the latest backup of a file"""
        backup_dir = Path("backups")
        if not backup_dir.exists():
            return None

        # Look for backups containing this file
        for backup_folder in sorted(backup_dir.iterdir(), reverse=True):
            if backup_folder.is_dir():
                potential_backup = backup_folder / file_path
                if potential_backup.exists():
                    return potential_backup
        return None


class DatabaseRecovery(RecoveryAction):
    """Recovers from database corruption"""

    def __init__(self):
        super().__init__("database_recovery", "Restore database from backup")

    async def _execute_impl(self, context: Dict[str, Any]) -> bool:
        try:
            # Stop any running database processes
            await self._stop_database_processes()

            # Restore from latest backup
            backup_path = await self._find_latest_db_backup()
            if backup_path:
                await self._restore_database(backup_path)
                logger.info("Database restored from backup successfully")
                return True
            else:
                logger.error("No database backup found")
                return False
        except Exception as e:
            logger.error(f"Database recovery failed: {e}")
            return False

    async def _stop_database_processes(self):
        """Stop any running database processes"""
        # Implementation depends on the database being used
        pass

    async def _find_latest_db_backup(self) -> Optional[Path]:
        """Find the latest database backup"""
        backup_dir = Path("backups")
        db_backups = list(backup_dir.glob("*/database/*.sql"))
        if db_backups:
            return max(db_backups, key=lambda p: p.stat().st_mtime)
        return None

    async def _restore_database(self, backup_path: Path):
        """Restore database from backup file"""
        # Implementation depends on the database being used
        pass


class ConfigurationRecovery(RecoveryAction):
    """Recovers from configuration errors"""

    def __init__(self):
        super().__init__("configuration_recovery", "Restore configuration from backup")

    async def _execute_impl(self, context: Dict[str, Any]) -> bool:
        try:
            config_file = context.get("config_file")
            if not config_file:
                return False

            # Find backup of configuration
            backup_path = await self._find_config_backup(config_file)
            if backup_path:
                shutil.copy2(backup_path, config_file)
                logger.info(f"Restored configuration {config_file} from backup")
                return True
            else:
                # Try to regenerate configuration from defaults
                return await self._regenerate_config(config_file)
        except Exception as e:
            logger.error(f"Configuration recovery failed: {e}")
            return False

    async def _find_config_backup(self, config_file: str) -> Optional[Path]:
        """Find backup of configuration file"""
        backup_dir = Path("backups")
        config_backups = list(backup_dir.glob(f"*/config/{config_file}"))
        if config_backups:
            return max(config_backups, key=lambda p: p.stat().st_mtime)
        return None

    async def _regenerate_config(self, config_file: str) -> bool:
        """Regenerate configuration from defaults"""
        try:
            # Implementation depends on the configuration system
            logger.info(f"Regenerated configuration {config_file} from defaults")
            return True
        except Exception as e:
            logger.error(f"Failed to regenerate configuration: {e}")
            return False


class DependencyRecovery(RecoveryAction):
    """Recovers from missing dependencies"""

    def __init__(self):
        super().__init__("dependency_recovery", "Reinstall missing dependencies")

    async def _execute_impl(self, context: Dict[str, Any]) -> bool:
        try:
            missing_deps = context.get("missing_dependencies", [])
            if not missing_deps:
                return False

            # Reinstall dependencies
            for dep in missing_deps:
                await self._install_dependency(dep)

            logger.info(f"Reinstalled {len(missing_deps)} missing dependencies")
            return True
        except Exception as e:
            logger.error(f"Dependency recovery failed: {e}")
            return False

    async def _install_dependency(self, dependency: str):
        """Install a single dependency"""
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dependency],
                capture_output=True,
                text=True,
                timeout=300,
            )
            if result.returncode != 0:
                raise Exception(f"Failed to install {dependency}: {result.stderr}")
        except subprocess.TimeoutExpired:
            raise Exception(f"Timeout installing {dependency}")


class RecoverySystem:
    """Main recovery system that coordinates detection and recovery"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.failure_history: List[FailureEvent] = []
        self.recovery_actions: Dict[FailureType, List[RecoveryAction]] = (
            self._setup_recovery_actions()
        )
        self.monitoring_active = False
        self.health_check_interval = self.config.get("health_check_interval", 60)

        # Setup logging
        self.logger = logging.getLogger(__name__)

    def _setup_recovery_actions(self) -> Dict[FailureType, List[RecoveryAction]]:
        """Setup recovery actions for each failure type"""
        return {
            FailureType.FILE_CORRUPTION: [FileCorruptionRecovery()],
            FailureType.DATABASE_CORRUPTION: [DatabaseRecovery()],
            FailureType.CONFIGURATION_ERROR: [ConfigurationRecovery()],
            FailureType.DEPENDENCY_MISSING: [DependencyRecovery()],
            # Add more recovery actions as needed
        }

    async def start_monitoring(self):
        """Start continuous health monitoring"""
        self.monitoring_active = True
        self.logger.info("Recovery system monitoring started")

        while self.monitoring_active:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                await asyncio.sleep(10)  # Shorter delay on error

    async def stop_monitoring(self):
        """Stop continuous health monitoring"""
        self.monitoring_active = False
        self.logger.info("Recovery system monitoring stopped")

    async def _perform_health_check(self):
        """Perform comprehensive health check"""
        failures = []

        # Check file integrity
        file_failures = await self._check_file_integrity()
        failures.extend(file_failures)

        # Check database health
        db_failures = await self._check_database_health()
        failures.extend(db_failures)

        # Check configuration
        config_failures = await self._check_configuration()
        failures.extend(config_failures)

        # Check dependencies
        dep_failures = await self._check_dependencies()
        failures.extend(dep_failures)

        # Check system resources
        resource_failures = await self._check_system_resources()
        failures.extend(resource_failures)

        # Process detected failures
        for failure in failures:
            await self._handle_failure(failure)

    async def _check_file_integrity(self) -> List[FailureEvent]:
        """Check integrity of critical files"""
        failures = []
        critical_files = ["config/main_config.json", "core/agent.py", "api/main.py"]

        for file_path in critical_files:
            if not os.path.exists(file_path):
                failures.append(
                    FailureEvent(
                        failure_type=FailureType.FILE_CORRUPTION,
                        severity="high",
                        description=f"Critical file missing: {file_path}",
                        timestamp=datetime.now(),
                        context={"file_path": file_path},
                    )
                )
            elif await self._is_file_corrupted(file_path):
                failures.append(
                    FailureEvent(
                        failure_type=FailureType.FILE_CORRUPTION,
                        severity="medium",
                        description=f"File appears corrupted: {file_path}",
                        timestamp=datetime.now(),
                        context={"file_path": file_path},
                    )
                )

        return failures

    async def _is_file_corrupted(self, file_path: str) -> bool:
        """Check if a file appears to be corrupted"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                # Basic corruption checks
                if len(content) == 0:
                    return True
                if file_path.endswith(".json"):
                    json.loads(content)  # Test JSON validity
                return False
        except Exception:
            return True

    async def _check_database_health(self) -> List[FailureEvent]:
        """Check database health"""
        failures = []
        # Implementation depends on the database being used
        return failures

    async def _check_configuration(self) -> List[FailureEvent]:
        """Check configuration validity"""
        failures = []
        try:
            # Test loading main configuration
            with open("config/main_config.json", "r") as f:
                config = json.load(f)
            # Add configuration validation logic
        except Exception as e:
            failures.append(
                FailureEvent(
                    failure_type=FailureType.CONFIGURATION_ERROR,
                    severity="high",
                    description=f"Configuration error: {e}",
                    timestamp=datetime.now(),
                    context={"config_file": "config/main_config.json"},
                )
            )
        return failures

    async def _check_dependencies(self) -> List[FailureEvent]:
        """Check if all required dependencies are available"""
        failures = []
        required_modules = ["fastapi", "uvicorn", "sqlalchemy", "pydantic"]

        missing_deps = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_deps.append(module)

        if missing_deps:
            failures.append(
                FailureEvent(
                    failure_type=FailureType.DEPENDENCY_MISSING,
                    severity="high",
                    description=f"Missing dependencies: {missing_deps}",
                    timestamp=datetime.now(),
                    context={"missing_dependencies": missing_deps},
                )
            )

        return failures

    async def _check_system_resources(self) -> List[FailureEvent]:
        """Check system resource availability"""
        failures = []

        # Check disk space
        disk_usage = shutil.disk_usage(".")
        free_space_gb = disk_usage.free / (1024**3)
        if free_space_gb < 1.0:  # Less than 1GB free
            failures.append(
                FailureEvent(
                    failure_type=FailureType.DISK_SPACE_LOW,
                    severity="medium",
                    description=f"Low disk space: {free_space_gb:.2f}GB free",
                    timestamp=datetime.now(),
                    context={"free_space_gb": free_space_gb},
                )
            )

        return failures

    async def _handle_failure(self, failure: FailureEvent):
        """Handle a detected failure"""
        self.failure_history.append(failure)
        self.logger.warning(f"Failure detected: {failure.description}")

        if not failure.auto_recoverable:
            self.logger.error(f"Failure not auto-recoverable: {failure.description}")
            return

        if failure.recovery_attempts >= failure.max_recovery_attempts:
            self.logger.error(
                f"Max recovery attempts reached for: {failure.description}"
            )
            return

        # Attempt recovery
        await self._attempt_recovery(failure)

    async def _attempt_recovery(self, failure: FailureEvent):
        """Attempt to recover from a failure"""
        recovery_actions = self.recovery_actions.get(failure.failure_type, [])

        for action in recovery_actions:
            self.logger.info(f"Attempting recovery: {action.name}")

            try:
                success = await action.execute(failure.context)
                if success:
                    self.logger.info(f"Recovery successful: {action.name}")
                    return
                else:
                    self.logger.warning(f"Recovery failed: {action.name}")
            except Exception as e:
                self.logger.error(f"Recovery action error: {action.name} - {e}")

        failure.recovery_attempts += 1
        self.logger.error(f"All recovery attempts failed for: {failure.description}")

    async def manual_recovery(
        self, failure_type: FailureType, context: Dict[str, Any]
    ) -> bool:
        """Manually trigger recovery for a specific failure type"""
        failure = FailureEvent(
            failure_type=failure_type,
            severity="manual",
            description="Manual recovery triggered",
            timestamp=datetime.now(),
            context=context,
            auto_recoverable=True,
        )

        await self._attempt_recovery(failure)
        return failure.recovery_attempts < failure.max_recovery_attempts

    def get_failure_history(self, hours: int = 24) -> List[FailureEvent]:
        """Get failure history for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [f for f in self.failure_history if f.timestamp > cutoff_time]

    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        recent_failures = self.get_failure_history(1)  # Last hour

        return {
            "status": "healthy" if not recent_failures else "degraded",
            "monitoring_active": self.monitoring_active,
            "recent_failures": len(recent_failures),
            "total_failures": len(self.failure_history),
            "last_failure": (
                self.failure_history[-1].timestamp if self.failure_history else None
            ),
        }


# Global recovery system instance
recovery_system = RecoverySystem()


async def start_recovery_monitoring():
    """Start the recovery system monitoring"""
    await recovery_system.start_monitoring()


def get_recovery_system() -> RecoverySystem:
    """Get the global recovery system instance"""
    return recovery_system
