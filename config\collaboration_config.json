{"collaboration_manager": {"max_workers": 10, "default_timeout_seconds": 300, "max_parallel_tasks": 5, "enable_persistence": true, "persistence_path": "data/collaborations", "cleanup_completed_after_hours": 24, "max_collaboration_history": 1000}, "shared_context": {"enable_persistence": true, "base_persistence_path": "data/shared_contexts", "default_lock_timeout": 30.0, "max_access_log_size": 10000, "auto_save_interval_minutes": 5, "enable_event_logging": true}, "patterns": {"library_path": "data/collaboration_patterns.json", "auto_load_custom_patterns": true, "enable_pattern_learning": false, "pattern_recommendation_threshold": 10}, "agents": {"registration_timeout": 60, "health_check_interval": 30, "auto_retry_failed_tasks": true, "max_task_retries": 3, "task_retry_delay_seconds": 5}, "monitoring": {"enable_performance_tracking": true, "enable_metrics_collection": true, "metrics_retention_days": 30, "alert_on_failures": true, "alert_thresholds": {"success_rate_minimum": 0.8, "average_duration_maximum": 600, "failure_rate_maximum": 0.2}}, "security": {"enable_context_validation": true, "max_context_size_mb": 100, "enable_agent_authentication": false, "sanitize_task_data": true, "blocked_context_keys": ["password", "secret", "token", "api_key"]}, "optimization": {"enable_task_batching": true, "batch_size": 5, "enable_smart_scheduling": true, "load_balancing_strategy": "round_robin", "cache_task_results": true, "cache_ttl_minutes": 60}, "logging": {"log_level": "INFO", "log_collaboration_events": true, "log_context_changes": false, "log_performance_metrics": true, "log_to_file": true, "log_file_path": "logs/collaboration.log", "log_rotation": {"enabled": true, "max_size_mb": 100, "backup_count": 5}}, "debugging": {"enable_detailed_tracing": false, "capture_stack_traces": false, "enable_profiling": false, "profile_output_path": "profiles/collaboration"}, "experimental": {"enable_ai_task_routing": false, "enable_predictive_scheduling": false, "enable_auto_pattern_generation": false, "enable_cross_collaboration_learning": false}}