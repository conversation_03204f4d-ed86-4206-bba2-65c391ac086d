repos:
  # Cursor Rules Enforcement - MANDATORY
  - repo: local
    hooks:
      - id: cursor-rules-enforcement
        name: Cursor Rules Enforcement
        entry: python scripts/cursor_rules_check.py
        language: system
        stages: [commit, push]
        always_run: true
        description: "Enforce cursor rules compliance before commit"

      - id: cursorrules-check
        name: "Ensure Cursor rules loaded"
        entry: python -c "import core.utils.cursor_rules_validator as u; u.assert_rules_loaded()"
        language: python
        files: cursorrules\.md$
        description: "Validate cursor rules are properly loaded"

      - id: check-test-commands
        name: "Ensure pytest runs via venv"
        entry: bash -c 'grep -R "pytest\|python -m pytest" -n . | grep -vE ".venv|venv|virtualenv" | grep -vE "\.git|node_modules|__pycache__" | grep -q "pytest\|python -m pytest" && echo "❌ Use .venv python for tests" && exit 1 || exit 0'
        language: system
        files: \.(py|md|txt|sh|ps1|bat)$
        exclude: \.venv|venv|virtualenv|\.git|node_modules|__pycache__
        description: "Ensure test commands use virtual environment"

  # Pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: requirements-txt-fixer

  # Python formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3.11

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ["--profile", "black"]

  # Python linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503,W504]

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-PyYAML]

  # Security scanning
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src/, -f, json, -o, bandit-report.json]
        exclude: ^tests/

  # Dependency security
  - repo: https://github.com/PyCQA/safety
    rev: 2.3.5
    hooks:
      - id: safety
        args: [--full-report]

  # Frontend formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [javascript, jsx, ts, tsx, json, css, scss, html, yaml, yml, markdown, md]

  # Frontend linting
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.56.0
    hooks:
      - id: eslint
        files: \.(js|jsx|ts|tsx)$
        types: [file]
        additional_dependencies:
          - eslint@8.56.0
          - '@typescript-eslint/eslint-plugin@6.19.0'
          - '@typescript-eslint/parser@6.19.0'
          - eslint-plugin-react@7.33.2
          - eslint-plugin-react-hooks@4.6.0

  # Test execution
  - repo: local
    hooks:
      - id: run-tests
        name: Run Tests
        entry: python -m pytest --tb=short
        language: system
        stages: [push]
        always_run: true
        description: "Run tests to ensure 100% success rate"

  # TODO completion check
  - repo: local
    hooks:
      - id: check-todos
        name: Check TODO Completion
        entry: python -c "from core.cursor_rules_enforcer import CursorRulesEnforcer; e = CursorRulesEnforcer(); exit(0 if e.check_todo_completion() else 1)"
        language: system
        stages: [commit]
        always_run: true
        description: "Ensure all TODOs are completed"

  # File organization check
  - repo: local
    hooks:
      - id: check-file-organization
        name: Check File Organization
        entry: python -c "from core.cursor_rules_enforcer import CursorRulesEnforcer; e = CursorRulesEnforcer(); result = e._check_file_organization(); exit(0 if result['status'] == 'passed' else 1)"
        language: system
        stages: [commit]
        always_run: true
        description: "Ensure files are in correct directories"

  # Dependency management check
  - repo: local
    hooks:
      - id: check-dependencies
        name: Check Dependency Management
        entry: python -c "from core.cursor_rules_enforcer import CursorRulesEnforcer; e = CursorRulesEnforcer(); result = e._check_dependency_management(); exit(0 if result['status'] == 'passed' else 1)"
        language: system
        stages: [commit]
        always_run: true
        description: "Ensure dependencies are properly managed"

  # AI model compliance check
  - repo: local
    hooks:
      - id: check-ai-models
        name: Check AI Model Compliance
        entry: python -c "from core.cursor_rules_enforcer import CursorRulesEnforcer; e = CursorRulesEnforcer(); result = e._check_ai_model_compliance(); exit(0 if result['status'] == 'passed' else 1)"
        language: system
        stages: [commit]
        always_run: true
        description: "Ensure only local Ollama models are used"
