import logging
import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()


class ErrorHandler:
    """Centralized error handling for the API"""

    @staticmethod
    def create_error_response(
        error: Exception,
        status_code: int = 500,
        error_code: str = "internal_error",
        context: Optional[Dict[str, Any]] = None,
    ) -> JSONResponse:
        """Create a standardized error response"""

        error_info = {
            "success": False,
            "error": {
                "code": error_code,
                "message": str(error),
                "type": type(error).__name__,
                "timestamp": datetime.now().isoformat(),
            },
        }

        if context:
            error_info["error"]["context"] = context

        # Add stack trace in development
        import os

        if os.getenv("DEBUG", "").lower() in ("true", "1", "yes"):
            error_info["error"]["stack_trace"] = traceback.format_exc()

        return JSONResponse(error_info, status_code=status_code)

    @staticmethod
    def log_error(error: Exception, context: Optional[Dict[str, Any]] = None):
        """Log error with context"""
        logger.error(
            f"API Error: {type(error).__name__}: {str(error)}",
            extra={
                "error_type": type(error).__name__,
                "context": context or {},
                "timestamp": datetime.now().isoformat(),
            },
            exc_info=True,
        )


# Global error handlers
@router.post("/errors/report")
async def report_error(request: Request):
    """Endpoint for clients to report errors"""
    try:
        data = await request.json()

        # Validate required fields
        required_fields = ["message", "type"]
        for field in required_fields:
            if field not in data:
                raise HTTPException(
                    status_code=400, detail=f"Missing required field: {field}"
                )

        # Log the reported error
        logger.error(
            f"Client Error Report: {data['type']}: {data['message']}",
            extra={
                "client_error": data,
                "user_agent": request.headers.get("user-agent"),
                "ip": request.client.host if request.client else None,
                "timestamp": datetime.now().isoformat(),
            },
        )

        return JSONResponse({"success": True, "message": "Error reported successfully"})

    except Exception as e:
        return ErrorHandler.create_error_response(
            e, status_code=500, error_code="error_report_failed"
        )


@router.get("/errors/health")
async def error_service_health():
    """Health check for error handling service"""
    return {
        "success": True,
        "service": "error_handling",
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
    }


# Exception handlers for common error types
async def validation_exception_handler(request: Request, exc: Exception):
    """Handle validation errors"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=422,
        error_code="validation_error",
        context={"request_path": str(request.url.path)},
    )


async def not_found_exception_handler(request: Request, exc: Exception):
    """Handle 404 errors"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=404,
        error_code="not_found",
        context={"request_path": str(request.url.path)},
    )


async def authentication_exception_handler(request: Request, exc: Exception):
    """Handle authentication errors"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=401,
        error_code="authentication_error",
        context={"request_path": str(request.url.path)},
    )


async def authorization_exception_handler(request: Request, exc: Exception):
    """Handle authorization errors"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=403,
        error_code="authorization_error",
        context={"request_path": str(request.url.path)},
    )


async def rate_limit_exception_handler(request: Request, exc: Exception):
    """Handle rate limit errors"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=429,
        error_code="rate_limit_exceeded",
        context={"request_path": str(request.url.path)},
    )


async def service_unavailable_exception_handler(request: Request, exc: Exception):
    """Handle service unavailable errors"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=503,
        error_code="service_unavailable",
        context={"request_path": str(request.url.path)},
    )


async def generic_exception_handler(request: Request, exc: Exception):
    """Handle all other unhandled exceptions"""
    return ErrorHandler.create_error_response(
        exc,
        status_code=500,
        error_code="internal_server_error",
        context={"request_path": str(request.url.path)},
    )
