"""
Complex Task Manager

Main orchestrator for complex code tasks using starcoder2 for AI-powered
architecture design, system integration, performance optimization, and
complex problem solving.
"""

import asyncio
import json
import os
import sys
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from complex_tasks.architecture_designer import ArchitectureDesigner
from complex_tasks.models import (
    ComplexTask,
    ProgressReport,
    QualityMetrics,
    ResourceAllocation,
    TaskComplexity,
    TaskStatus,
    TaskType,
)
from complex_tasks.performance_optimizer import PerformanceOptimizer
from complex_tasks.problem_solver import ComplexProblemSolver
from complex_tasks.progress_tracker import ProgressTracker
from complex_tasks.quality_assurance import QualityAssurance
from complex_tasks.resource_monitor import ResourceMonitor
from complex_tasks.system_integrator import SystemIntegrator
from models import ModelManager
from utils.logger import get_logger

sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

logger = get_logger(__name__)


class ComplexTaskManager:
    """
    Main manager for complex code tasks using starcoder2.

    Handles task creation, execution, monitoring, and completion with
    AI-powered assistance for complex software engineering tasks.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the complex task manager"""
        self.config = config
        self.tasks: Dict[str, Any] = {}
        self.executor = ThreadPoolExecutor(
            max_workers=config.get("max_concurrent_tasks", 4)
        )

        # Initialize AI model manager with starcoder2
        self.model_manager = ModelManager()
        self.model_manager.register_provider(
            "starcoder2", self._create_starcoder2_provider()
        )

        # Initialize specialized components
        self.architecture_designer = ArchitectureDesigner(config, self.model_manager)
        self.system_integrator = SystemIntegrator(config, self.model_manager)
        self.performance_optimizer = PerformanceOptimizer(config, self.model_manager)
        self.problem_solver = ComplexProblemSolver(config, self.model_manager)
        self.resource_monitor = ResourceMonitor(config)
        self.quality_assurance = QualityAssurance(config)
        self.progress_tracker = ProgressTracker(config)

        # Task execution state
        self.running_tasks: Dict[str, Any] = {}
        self.task_queue: List[str] = []
        self.completed_tasks: List[Dict[str, Any]] = []

        logger.info("Complex Task Manager initialized with starcoder2 integration")

    def _create_starcoder2_provider(self):
        """Create starcoder2 model provider"""
        from complex_tasks.models import ModelProvider

        class StarCoder2Provider(ModelProvider):
            def __init__(self, model_name: str, **kwargs):
                super().__init__(model_name, **kwargs)
                self.model_name = "starcoder2:3b"
                self.temperature = 0.1
                self.max_tokens = 4096

            async def generate(self, prompt: str, **kwargs) -> str:
                """Generate code using starcoder2"""
                # This would integrate with actual starcoder2 model
                # For now, return a placeholder response
                return f"# Generated by {self.model_name}\n# {prompt[:100]}..."

            async def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
                """Chat with starcoder2"""
                # Convert messages to prompt
                prompt = "\n".join(
                    [f"{msg['role']}: {msg['content']}" for msg in messages]
                )
                return await self.generate(prompt, **kwargs)

        return StarCoder2Provider

    async def create_task(self, task_data: Dict[str, Any]) -> ComplexTask:
        """Create a new complex task"""
        try:
            task_id = str(uuid.uuid4())

            # Create resource allocation
            resource_data = task_data.get("resource_allocation", {})
            resource_allocation = ResourceAllocation(**resource_data)

            # Create task
            task = ComplexTask(
                task_id=task_id,
                title=task_data["title"],
                description=task_data["description"],
                task_type=TaskType(task_data["task_type"]),
                complexity=TaskComplexity(task_data["complexity"]),
                resource_allocation=resource_allocation,
                requirements=task_data.get("requirements", []),
                constraints=task_data.get("constraints", []),
                dependencies=task_data.get("dependencies", []),
                deliverables=task_data.get("deliverables", []),
                acceptance_criteria=task_data.get("acceptance_criteria", []),
                deadline=(
                    datetime.fromisoformat(task_data["deadline"])
                    if task_data.get("deadline")
                    else None
                ),
                assigned_to=task_data.get("assigned_to"),
                priority=task_data.get("priority", 5),
                tags=task_data.get("tags", []),
                metadata=task_data.get("metadata", {}),
            )

            self.tasks[task_id] = task

            # Add to queue if ready to execute
            if not task.dependencies:
                self.task_queue.append(task_id)

            logger.info(f"Created complex task: {task_id} - {task.title}")
            return task

        except Exception as e:
            logger.error(f"Error creating task: {e}")
            raise

    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """Execute a complex task using appropriate AI component"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")

        task = self.tasks[task_id]

        try:
            # Update task status
            task.status = TaskStatus.IN_PROGRESS
            await self._add_progress_report(task_id, "Task execution started", 0.0)

            # Check resource availability
            if not await self.resource_monitor.check_availability(
                task.resource_allocation
            ):
                raise RuntimeError("Insufficient resources for task execution")

            # Execute based on task type
            result = None
            if task.task_type == TaskType.ARCHITECTURE_DESIGN:
                result = await self.architecture_designer.design_architecture(task)
            elif task.task_type == TaskType.SYSTEM_INTEGRATION:
                result = await self.system_integrator.integrate_system(task)
            elif task.task_type == TaskType.PERFORMANCE_OPTIMIZATION:
                result = await self.performance_optimizer.optimize_performance(task)
            elif task.task_type == TaskType.COMPLEX_PROBLEM_SOLVING:
                result = await self.problem_solver.solve_problem(task)
            else:
                raise ValueError(f"Unsupported task type: {task.task_type}")

            # Update task with results
            task.status = TaskStatus.COMPLETED
            task.quality_metrics = result.get("quality_metrics")
            await self._add_progress_report(
                task_id, "Task completed successfully", 100.0, result
            )

            logger.info(f"Task {task_id} completed successfully")
            return result

        except Exception as e:
            task.status = TaskStatus.FAILED
            await self._add_progress_report(task_id, f"Task failed: {str(e)}", 0.0)
            logger.error(f"Task {task_id} failed: {e}")
            raise

    async def execute_tasks_batch(self, task_ids: List[str]) -> Dict[str, Any]:
        """Execute multiple tasks in batch"""
        results: Dict[str, Any] = {}

        # Group tasks by dependencies
        dependency_groups = self._group_tasks_by_dependencies(task_ids)

        for group in dependency_groups:
            # Execute tasks in parallel within each group
            tasks = [self.execute_task(task_id) for task_id in group]
            group_results = await asyncio.gather(*tasks, return_exceptions=True)

            for task_id, result in zip(group, group_results):
                if isinstance(result, Exception):
                    results[task_id] = {"error": str(result)}
                else:
                    results[task_id] = result

        return results

    def _group_tasks_by_dependencies(self, task_ids: List[str]) -> List[List[str]]:
        """Group tasks by dependencies for proper execution order"""
        groups = []
        remaining = set(task_ids)

        while remaining:
            # Find tasks with no dependencies in remaining set
            ready = []
            for task_id in remaining:
                task = self.tasks[task_id]
                if not any(dep in remaining for dep in task.dependencies):
                    ready.append(task_id)

            if not ready:
                # Circular dependency detected
                logger.warning("Circular dependency detected in tasks")
                ready = list(remaining)

            groups.append(ready)
            remaining -= set(ready)

        return groups

    async def _add_progress_report(
        self,
        task_id: str,
        phase: str,
        percentage: float,
        result: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Add a progress report to a task"""
        task = self.tasks[task_id]

        report = ProgressReport(
            task_id=task_id,
            status=task.status,
            progress_percentage=percentage,
            current_phase=phase,
            estimated_completion=datetime.now()
            + timedelta(hours=task.resource_allocation.estimated_duration_hours),
            resource_usage=await self.resource_monitor.get_current_usage(),
            quality_metrics=result.get("quality_metrics") if result else None,
        )

        task.add_progress_report(report)
        await self.progress_tracker.track_progress(report)

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get detailed status of a task"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")

        task = self.tasks[task_id]
        latest_progress = task.get_latest_progress()

        return {
            "task": task.to_dict(),
            "latest_progress": latest_progress.to_dict() if latest_progress else None,
            "resource_usage": await self.resource_monitor.get_current_usage(),
            "quality_metrics": (
                task.quality_metrics.to_dict() if task.quality_metrics else None
            ),
        }

    async def list_tasks(
        self, status: Optional[TaskStatus] = None, task_type: Optional[TaskType] = None
    ) -> List[Dict[str, Any]]:
        """List tasks with optional filtering"""
        tasks = []

        for task in self.tasks.values():
            if status and task.status != status:
                continue
            if task_type and task.task_type != task_type:
                continue

            tasks.append(task.to_dict())

        return sorted(tasks, key=lambda x: x["priority"], reverse=True)

    async def update_task(self, task_id: str, updates: Dict[str, Any]) -> ComplexTask:
        """Update task properties"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")

        task = self.tasks[task_id]

        # Update allowed fields
        allowed_fields = [
            "title",
            "description",
            "priority",
            "deadline",
            "assigned_to",
            "tags",
        ]
        for field, value in updates.items():
            if field in allowed_fields:
                setattr(task, field, value)

        task.updated_at = datetime.now()

        logger.info(f"Updated task {task_id}")
        return task

    async def cancel_task(self, task_id: str) -> None:
        """Cancel a running task"""
        if task_id not in self.tasks:
            raise ValueError(f"Task {task_id} not found")

        task = self.tasks[task_id]
        task.status = TaskStatus.CANCELLED

        # Cancel running task if exists
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            del self.running_tasks[task_id]

        await self._add_progress_report(
            task_id, "Task cancelled", task.calculate_completion_percentage()
        )
        logger.info(f"Cancelled task {task_id}")

    async def get_task_analytics(self) -> Dict[str, Any]:
        """Get analytics for all tasks"""
        total_tasks = len(self.tasks)
        completed_tasks = len(
            [t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]
        )
        failed_tasks = len(
            [t for t in self.tasks.values() if t.status == TaskStatus.FAILED]
        )
        running_tasks = len(
            [t for t in self.tasks.values() if t.status == TaskStatus.IN_PROGRESS]
        )

        # Calculate average quality metrics
        quality_scores = []
        for task in self.tasks.values():
            if task.quality_metrics:
                quality_scores.append(task.quality_metrics.calculate_overall_score())

        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0

        return {
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "running_tasks": running_tasks,
            "success_rate": (
                (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            ),
            "average_quality_score": avg_quality,
            "tasks_by_type": self._count_tasks_by_type(),
            "tasks_by_complexity": self._count_tasks_by_complexity(),
            "overdue_tasks": len([t for t in self.tasks.values() if t.is_overdue()]),
        }

    def _count_tasks_by_type(self) -> Dict[str, int]:
        """Count tasks by type"""
        counts: Dict[str, int] = {}
        for task in self.tasks.values():
            task_type = task.task_type.value
            counts[task_type] = counts.get(task_type, 0) + 1 or 0
        return counts

    def _count_tasks_by_complexity(self) -> Dict[str, int]:
        """Count tasks by complexity"""
        counts: Dict[str, int] = {}
        for task in self.tasks.values():
            complexity = task.complexity.value
            counts[complexity] = counts.get(complexity, 0) + 1 or 0
        return counts

    async def save_tasks(self, file_path: str) -> None:
        """Save all tasks to file"""
        data = {
            "tasks": [task.to_dict() for task in self.tasks.values()],
            "metadata": {
                "saved_at": datetime.now().isoformat(),
                "total_tasks": len(self.tasks),
            },
        }

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"Saved {len(self.tasks)} tasks to {file_path}")

    async def load_tasks(self, file_path: str) -> None:
        """Load tasks from file"""
        with open(file_path, "r") as f:
            data = json.load(f)

        self.tasks.clear()
        for task_data in data["tasks"]:
            task = ComplexTask.from_dict(task_data)
            self.tasks[task.task_id] = task

        logger.info(f"Loaded {len(self.tasks)} tasks from {file_path}")

    async def cleanup_completed_tasks(self, days_old: int = 30) -> int:
        """Clean up completed tasks older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        tasks_to_remove = []

        for task_id, task in self.tasks.items():
            if task.status == TaskStatus.COMPLETED and task.updated_at < cutoff_date:
                tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.tasks[task_id]

        logger.info(f"Cleaned up {len(tasks_to_remove)} completed tasks")
        return len(tasks_to_remove)
