"""
State tracking for the CLI.
"""

import json
import logging
import time
import uuid
from dataclasses import asdict, dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class StateEvent:
    """Represents a state change event"""

    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: float = field(default_factory=time.time)
    event_type: str = "state_change"
    component: str = ""
    message: str = ""
    data: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result["timestamp"] = datetime.fromtimestamp(self.timestamp).isoformat()
        return result


class StateTracker:
    """Tracks application state changes"""

    def __init__(self, state_file: Optional[str] = None):
        """
        Initialize the state tracker.

        Args:
            state_file: Path to the state file. If None, uses default location.
        """
        self.state_file = state_file or "state/state.json"
        self._state: Dict[str, Any] = {}
        self._events: List[StateEvent] = []
        self._load_state()

    def _ensure_state_dir(self) -> None:
        """Ensure the state directory exists"""
        state_path = Path(self.state_file).parent
        state_path.mkdir(parents=True, exist_ok=True)

    def _load_state(self) -> None:
        """Load state from file"""
        try:
            if Path(self.state_file).exists():
                with open(self.state_file, "r") as f:
                    self._state = json.load(f)
            logger.debug(f"Loaded state from {self.state_file}")
        except Exception as e:
            logger.warning(f"Failed to load state: {e}")
            self._state = {}

    def _save_state(self) -> None:
        """Save state to file"""
        try:
            self._ensure_state_dir()
            with open(self.state_file, "w") as f:
                json.dump(self._state, f, indent=2)
            logger.debug(f"Saved state to {self.state_file}")
        except Exception as e:
            logger.error(f"Failed to save state: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get a state value"""
        return self._state.get(key, default)

    def set(self, key: str, value: Any, save: bool = True) -> None:
        """Set a state value"""
        self._state[key] = value
        if save:
            self._save_state()

    def update(self, updates: Dict[str, Any], save: bool = True) -> None:
        """Update multiple state values"""
        self._state.update(updates)
        if save:
            self._save_state()

    def log_event(self, event: StateEvent) -> None:
        """Log a state change event"""
        self._events.append(event)
        logger.debug(f"State event: {event.event_type} - {event.message}")

    def get_events(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent state events"""
        return [e.to_dict() for e in self._events[-limit:]]

    def clear_events(self) -> None:
        """Clear all events"""
        self._events.clear()

    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            "state_file": str(Path(self.state_file).absolute()),
            "event_count": len(self._events),
            "last_event": self._events[-1].to_dict() if self._events else None,
            "state_keys": list(self._state.keys()),
        }


# Global state tracker instance
state_tracker = StateTracker()


def track_event(
    component: str, message: str, data: Optional[Dict[str, Any]] = None
) -> StateEvent:
    """Helper function to track an event"""
    event = StateEvent(component=component, message=message, data=data or {})
    state_tracker.log_event(event)
    return event
