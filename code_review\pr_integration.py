"""
Pull Request Integration

Integration with version control systems for automated pull request
review, comment generation, and approval recommendations.

Phase 20 Implementation - Advanced Code Review
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class PRComment:
    """Represents a pull request comment."""

    id: str
    type: str  # "suggestion", "warning", "error", "info"
    severity: str  # "critical", "high", "medium", "low"
    title: str
    message: str
    file_path: str
    line_number: int
    suggestion: Optional[str] = None
    confidence: float = 0.8


@dataclass
class PRReviewResult:
    """Result of pull request review."""

    pr_id: str
    overall_score: float
    quality_score: float
    security_score: float
    performance_score: float
    comments: List[PRComment]
    approval_recommendation: str
    summary: str
    confidence: float


class PRIntegration:
    """
    Pull request integration for automated code review.

    This class handles integration with version control systems
    to provide automated pull request reviews and recommendations.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the PR integration.

        Args:
            config: Configuration dictionary for PR integration
        """
        self.config = config or {}
        self.supported_platforms = ["github", "gitlab", "bitbucket"]

        logger.info("PR Integration initialized successfully")

    async def review_pull_request(self, pr_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Review a pull request and provide automated feedback.

        Args:
            pr_data: Pull request data including files, changes, and context

        Returns:
            Dictionary with PR review results
        """
        try:
            logger.info(
                f"Starting PR review for PR #{pr_data.get('number', 'unknown')}"
            )

            # Extract PR information
            pr_id = pr_data.get("id", "unknown")
            files = pr_data.get("files", [])
            title = pr_data.get("title", "")
            description = pr_data.get("description", "")

            # Review each file
            all_comments = []
            quality_scores = []
            security_scores = []
            performance_scores = []

            for file_data in files:
                file_comments = await self._review_file(file_data)
                all_comments.extend(file_comments)

                # Extract scores from file review
                if "quality_score" in file_data:
                    quality_scores.append(file_data["quality_score"])
                if "security_score" in file_data:
                    security_scores.append(file_data["security_score"])
                if "performance_score" in file_data:
                    performance_scores.append(file_data["performance_score"])

            # Calculate overall scores
            overall_score = self._calculate_overall_score(
                quality_scores, security_scores, performance_scores
            )
            quality_score = (
                sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            )
            security_score = (
                sum(security_scores) / len(security_scores) if security_scores else 0.0
            )
            performance_score = (
                sum(performance_scores) / len(performance_scores)
                if performance_scores
                else 0.0
            )

            # Generate approval recommendation
            approval_recommendation = self._generate_approval_recommendation(
                overall_score, all_comments
            )

            # Generate review summary
            summary = await self._generate_review_summary(
                pr_id, title, overall_score, all_comments
            )

            # Calculate confidence
            confidence = self._calculate_confidence(all_comments)

            return {
                "pr_id": pr_id,
                "overall_score": overall_score,
                "quality_score": quality_score,
                "security_score": security_score,
                "performance_score": performance_score,
                "comments": [
                    self._comment_to_dict(comment) for comment in all_comments
                ],
                "approval_recommendation": approval_recommendation,
                "summary": summary,
                "confidence": confidence,
                "total_comments": len(all_comments),
                "critical_count": len(
                    [c for c in all_comments if c.severity == "critical"]
                ),
                "high_count": len([c for c in all_comments if c.severity == "high"]),
                "medium_count": len(
                    [c for c in all_comments if c.severity == "medium"]
                ),
                "low_count": len([c for c in all_comments if c.severity == "low"]),
            }

        except Exception as e:
            logger.error(f"Error reviewing pull request: {e}")
            return {
                "pr_id": pr_data.get("id", "unknown"),
                "overall_score": 0.0,
                "quality_score": 0.0,
                "security_score": 0.0,
                "performance_score": 0.0,
                "comments": [],
                "approval_recommendation": "Unable to review",
                "summary": "Error occurred during review",
                "confidence": 0.0,
                "total_comments": 0,
                "critical_count": 0,
                "high_count": 0,
                "medium_count": 0,
                "low_count": 0,
            }

    async def _review_file(self, file_data: Dict[str, Any]) -> List[PRComment]:
        """Review a single file in the pull request."""
        comments = []

        try:
            file_path = file_data.get("path", "")
            language = self._detect_language(file_path)
            content = file_data.get("content", "")
            changes = file_data.get("changes", [])

            # Review code quality
            quality_comments = await self._review_code_quality(
                content, language, file_path
            )
            comments.extend(quality_comments)

            # Review security
            security_comments = await self._review_security(
                content, language, file_path
            )
            comments.extend(security_comments)

            # Review performance
            performance_comments = await self._review_performance(
                content, language, file_path
            )
            comments.extend(performance_comments)

            # Review changes
            change_comments = await self._review_changes(changes, file_path)
            comments.extend(change_comments)

        except Exception as e:
            logger.error(
                f"Error reviewing file {file_data.get('path', 'unknown')}: {e}"
            )

        return comments

    async def _review_code_quality(
        self, content: str, language: str, file_path: str
    ) -> List[PRComment]:
        """Review code quality aspects."""
        comments = []

        # Check for long functions
        if self._has_long_functions(content, language):
            comment = PRComment(
                id="long_function",
                type="warning",
                severity="medium",
                title="Long function detected",
                message="This function is quite long and may be hard to maintain. Consider breaking it down into smaller, focused functions.",
                file_path=file_path,
                line_number=self._find_function_line(content),
                suggestion="Break down the function into smaller functions with single responsibilities",
                confidence=0.8,
            )
            comments.append(comment)

        # Check for missing documentation
        if self._has_missing_documentation(content, language):
            comment = PRComment(
                id="missing_documentation",
                type="suggestion",
                severity="low",
                title="Missing documentation",
                message="This code could benefit from additional documentation.",
                file_path=file_path,
                line_number=1,
                suggestion="Add docstrings, comments, or README updates to improve code understanding",
                confidence=0.7,
            )
            comments.append(comment)

        # Check for code style issues
        style_comments = await self._check_style_issues(content, language, file_path)
        comments.extend(style_comments)

        return comments

    async def _review_security(
        self, content: str, language: str, file_path: str
    ) -> List[PRComment]:
        """Review security aspects."""
        comments = []

        # Check for hardcoded secrets
        if self._has_hardcoded_secrets(content):
            comment = PRComment(
                id="hardcoded_secret",
                type="error",
                severity="critical",
                title="Hardcoded secret detected",
                message="This code contains hardcoded secrets which is a security risk.",
                file_path=file_path,
                line_number=self._find_secret_line(content),
                suggestion="Use environment variables or secure configuration management",
                confidence=0.9,
            )
            comments.append(comment)

        # Check for SQL injection vulnerabilities
        if self._has_sql_injection_risk(content, language):
            comment = PRComment(
                id="sql_injection_risk",
                type="error",
                severity="high",
                title="Potential SQL injection risk",
                message="This code may be vulnerable to SQL injection attacks.",
                file_path=file_path,
                line_number=self._find_sql_line(content),
                suggestion="Use parameterized queries or prepared statements",
                confidence=0.8,
            )
            comments.append(comment)

        # Check for XSS vulnerabilities
        if self._has_xss_risk(content, language):
            comment = PRComment(
                id="xss_risk",
                type="error",
                severity="high",
                title="Potential XSS risk",
                message="This code may be vulnerable to cross-site scripting attacks.",
                file_path=file_path,
                line_number=self._find_xss_line(content),
                suggestion="Sanitize user input and use safe output methods",
                confidence=0.8,
            )
            comments.append(comment)

        return comments

    async def _review_performance(
        self, content: str, language: str, file_path: str
    ) -> List[PRComment]:
        """Review performance aspects."""
        comments = []

        # Check for inefficient algorithms
        if self._has_inefficient_algorithm(content):
            comment = PRComment(
                id="inefficient_algorithm",
                type="warning",
                severity="medium",
                title="Inefficient algorithm detected",
                message="This code may have performance issues with large datasets.",
                file_path=file_path,
                line_number=self._find_algorithm_line(content),
                suggestion="Consider using more efficient algorithms or data structures",
                confidence=0.7,
            )
            comments.append(comment)

        # Check for memory leaks
        if self._has_memory_leak_risk(content, language):
            comment = PRComment(
                id="memory_leak_risk",
                type="warning",
                severity="medium",
                title="Potential memory leak risk",
                message="This code may have memory management issues.",
                file_path=file_path,
                line_number=self._find_memory_line(content),
                suggestion="Ensure proper resource cleanup and memory management",
                confidence=0.7,
            )
            comments.append(comment)

        return comments

    async def _review_changes(
        self, changes: List[Dict[str, Any]], file_path: str
    ) -> List[PRComment]:
        """Review the changes made in the pull request."""
        comments = []

        for change in changes:
            # Check for large changes
            if change.get("lines_added", 0) + change.get("lines_deleted", 0) > 100:
                comment = PRComment(
                    id="large_change",
                    type="info",
                    severity="low",
                    title="Large change detected",
                    message="This is a large change that may need careful review.",
                    file_path=file_path,
                    line_number=change.get("line_number", 1),
                    suggestion="Consider breaking this into smaller, more focused changes",
                    confidence=0.6,
                )
                comments.append(comment)

            # Check for deleted code
            if change.get("lines_deleted", 0) > 50:
                comment = PRComment(
                    id="large_deletion",
                    type="warning",
                    severity="medium",
                    title="Large deletion detected",
                    message="A large amount of code was deleted. Ensure this is intentional.",
                    file_path=file_path,
                    line_number=change.get("line_number", 1),
                    suggestion="Verify that the deletion is necessary and doesn't break functionality",
                    confidence=0.7,
                )
                comments.append(comment)

        return comments

    async def _check_style_issues(
        self, content: str, language: str, file_path: str
    ) -> List[PRComment]:
        """Check for style and formatting issues."""
        comments = []

        # Check for inconsistent indentation
        if self._has_inconsistent_indentation(content, language):
            comment = PRComment(
                id="inconsistent_indentation",
                type="suggestion",
                severity="low",
                title="Inconsistent indentation",
                message="The code has inconsistent indentation.",
                file_path=file_path,
                line_number=1,
                suggestion="Use consistent indentation throughout the file",
                confidence=0.8,
            )
            comments.append(comment)

        # Check for long lines
        if self._has_long_lines(content):
            comment = PRComment(
                id="long_lines",
                type="suggestion",
                severity="low",
                title="Long lines detected",
                message="Some lines are longer than recommended.",
                file_path=file_path,
                line_number=self._find_long_line(content),
                suggestion="Break long lines to improve readability",
                confidence=0.7,
            )
            comments.append(comment)

        return comments

    def _calculate_overall_score(
        self,
        quality_scores: List[float],
        security_scores: List[float],
        performance_scores: List[float],
    ) -> float:
        """Calculate overall score for the pull request."""
        if not quality_scores and not security_scores and not performance_scores:
            return 0.0

        all_scores = quality_scores + security_scores + performance_scores
        return sum(all_scores) / len(all_scores)

    def _generate_approval_recommendation(
        self, overall_score: float, comments: List[PRComment]
    ) -> str:
        """Generate approval recommendation based on score and comments."""
        critical_issues = len([c for c in comments if c.severity == "critical"])
        high_issues = len([c for c in comments if c.severity == "high"])

        if critical_issues > 0:
            return "❌ DO NOT APPROVE - Critical issues found"
        elif high_issues > 2:
            return "⚠️ REVIEW REQUIRED - Multiple high-priority issues"
        elif overall_score >= 0.8:
            return "✅ APPROVE - Good code quality"
        elif overall_score >= 0.6:
            return "⚠️ REVIEW REQUIRED - Some issues to address"
        else:
            return "❌ DO NOT APPROVE - Poor code quality"

    async def _generate_review_summary(
        self, pr_id: str, title: str, overall_score: float, comments: List[PRComment]
    ) -> str:
        """Generate a summary of the pull request review."""
        total_comments = len(comments)
        critical_count = len([c for c in comments if c.severity == "critical"])
        high_count = len([c for c in comments if c.severity == "high"])

        summary = f"## Pull Request Review Summary\n\n"
        summary += f"**PR #{pr_id}**: {title}\n\n"
        summary += f"**Overall Score**: {overall_score:.2f}/1.0\n\n"
        summary += f"**Issues Found**:\n"
        summary += f"- Total comments: {total_comments}\n"
        summary += f"- Critical issues: {critical_count}\n"
        summary += f"- High priority issues: {high_count}\n\n"

        if critical_count > 0:
            summary += "🚨 **Critical issues must be addressed before approval.**\n\n"
        elif high_count > 0:
            summary += "⚠️ **High priority issues should be addressed.**\n\n"
        elif overall_score >= 0.8:
            summary += "✅ **Code quality is good.**\n\n"
        else:
            summary += "📝 **Code quality needs improvement.**\n\n"

        summary += "Please review the detailed comments below and address any issues before merging."

        return summary

    def _calculate_confidence(self, comments: List[PRComment]) -> float:
        """Calculate confidence in the review."""
        if not comments:
            return 0.9  # High confidence when no issues found

        # Calculate average confidence from comments
        avg_confidence = sum(comment.confidence for comment in comments) / len(comments)
        return min(0.95, avg_confidence)

    def _comment_to_dict(self, comment: PRComment) -> Dict[str, Any]:
        """Convert PRComment to dictionary."""
        return {
            "id": comment.id,
            "type": comment.type,
            "severity": comment.severity,
            "title": comment.title,
            "message": comment.message,
            "file_path": comment.file_path,
            "line_number": comment.line_number,
            "suggestion": comment.suggestion,
            "confidence": comment.confidence,
        }

    # Helper methods for detecting various issues
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file path."""
        if file_path.endswith(".py"):
            return "python"
        elif file_path.endswith((".js", ".ts", ".jsx", ".tsx")):
            return "javascript"
        elif file_path.endswith(".java"):
            return "java"
        elif file_path.endswith((".cpp", ".cc", ".cxx")):
            return "cpp"
        else:
            return "unknown"

    def _has_long_functions(self, content: str, language: str) -> bool:
        """Check if the code has long functions."""
        lines = content.split("\n")
        return len(lines) > 50  # Simplified check

    def _has_missing_documentation(self, content: str, language: str) -> bool:
        """Check if the code is missing documentation."""
        # Simplified check - look for docstrings or comments
        return "def " in content and '"""' not in content and "'''" not in content

    def _has_hardcoded_secrets(self, content: str) -> bool:
        """Check if the code has hardcoded secrets."""
        secret_patterns = [
            r'api_key\s*=\s*["\'][^"\']+["\']',
            r'password\s*=\s*["\'][^"\']+["\']',
            r'token\s*=\s*["\'][^"\']+["\']',
        ]

        for pattern in secret_patterns:
            if pattern in content:
                return True
        return False

    def _has_sql_injection_risk(self, content: str, language: str) -> bool:
        """Check if the code has SQL injection risk."""
        sql_patterns = [
            r"executeQuery\s*\(.*\+",
            r"execute\s*\(.*\+",
            r"query\s*=\s*.*\+",
        ]

        for pattern in sql_patterns:
            if pattern in content:
                return True
        return False

    def _has_xss_risk(self, content: str, language: str) -> bool:
        """Check if the code has XSS risk."""
        xss_patterns = [r"innerHTML\s*=", r"document\.write\s*\(", r"eval\s*\("]

        for pattern in xss_patterns:
            if pattern in content:
                return True
        return False

    def _has_inefficient_algorithm(self, content: str) -> bool:
        """Check if the code has inefficient algorithms."""
        # Simplified check for nested loops
        return content.count("for ") > 2 or content.count("while ") > 2

    def _has_memory_leak_risk(self, content: str, language: str) -> bool:
        """Check if the code has memory leak risk."""
        if language == "cpp":
            return "new " in content and "delete " not in content
        return False

    def _has_inconsistent_indentation(self, content: str, language: str) -> bool:
        """Check if the code has inconsistent indentation."""
        # Simplified check
        return False

    def _has_long_lines(self, content: str) -> bool:
        """Check if the code has long lines."""
        lines = content.split("\n")
        return any(len(line) > 80 for line in lines)

    # Helper methods for finding line numbers
    def _find_function_line(self, content: str) -> int:
        """Find the line number of a function."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "def " in line or "function " in line:
                return i + 1
        return 1

    def _find_secret_line(self, content: str) -> int:
        """Find the line number of a hardcoded secret."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "api_key" in line or "password" in line or "token" in line:
                return i + 1
        return 1

    def _find_sql_line(self, content: str) -> int:
        """Find the line number of SQL injection risk."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "executeQuery" in line or "execute" in line:
                return i + 1
        return 1

    def _find_xss_line(self, content: str) -> int:
        """Find the line number of XSS risk."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "innerHTML" in line or "document.write" in line:
                return i + 1
        return 1

    def _find_algorithm_line(self, content: str) -> int:
        """Find the line number of inefficient algorithm."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "for " in line or "while " in line:
                return i + 1
        return 1

    def _find_memory_line(self, content: str) -> int:
        """Find the line number of memory leak risk."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if "new " in line:
                return i + 1
        return 1

    def _find_long_line(self, content: str) -> int:
        """Find the line number of a long line."""
        lines = content.split("\n")
        for i, line in enumerate(lines):
            if len(line) > 80:
                return i + 1
        return 1

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the PR integration."""
        return {
            "status": "healthy",
            "supported_platforms": self.supported_platforms,
            "features": [
                "automated_reviews",
                "comment_generation",
                "approval_recommendations",
                "security_analysis",
                "performance_analysis",
                "quality_assessment",
            ],
        }
