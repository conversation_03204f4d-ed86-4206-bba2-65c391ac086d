#!/usr/bin/env python3
"""
Docker Migration API Routes
Provides REST API endpoints for Docker file migration operations.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/docker-migration", tags=["Docker Migration"])


class MigrationRequest(BaseModel):
    """Migration request model"""

    force: bool = False
    backup: bool = True


class MigrationResponse(BaseModel):
    """Migration response model"""

    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class StatusResponse(BaseModel):
    """Status response model"""

    success: bool
    status_results: Dict[str, Any]
    error: Optional[str] = None


class CleanupResponse(BaseModel):
    """Cleanup response model"""

    success: bool
    deleted_files: int
    backup_dir: str
    error: Optional[str] = None


class DockerMigrationService:
    """Service for Docker file migration operations"""

    def __init__(self):
        self.project_root = Path(".")
        self.containers_dir = self.project_root / "containers"
        self.backup_dir = self.containers_dir / "backup" / "original"

    async def migrate_docker_files(
        self, force: bool = False, backup: bool = True
    ) -> Dict[str, Any]:
        """Migrate Docker files from optimized to standard names"""
        try:
            logger.info("Starting Docker file migration")

            # Check if optimized files exist
            optimized_files = [
                "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.development.yml",
                "containers/docker-compose.production.yml",
                ".dockerignore.optimized",
            ]

            missing_files = []
            for file_path in optimized_files:
                if not (self.project_root / file_path).exists():
                    missing_files.append(file_path)

            if missing_files:
                return {
                    "success": False,
                    "error": f"Missing optimized files: {', '.join(missing_files)}",
                }

            # Create backup if requested
            if backup:
                backup_result = await self._create_backup()
                if not backup_result["success"]:
                    return backup_result

            # Migrate files
            migration_result = await self._migrate_files()
            if not migration_result["success"]:
                return migration_result

            # Verify migration
            verification_result = await self._verify_migration()

            return {
                "success": True,
                "message": "Docker files migrated successfully",
                "backup_result": backup_result if backup else None,
                "migration_result": migration_result,
                "verification_result": verification_result,
            }
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return {"success": False, "error": str(e)}

    async def rollback_migration(self) -> Dict[str, Any]:
        """Rollback Docker file migration"""
        try:
            logger.info("Rolling back Docker file migration")

            if not self.backup_dir.exists():
                return {"success": False, "error": "No backup directory found"}

            # Restore files from backup
            restored_files = []
            failed_restores = []

            for backup_file in self.backup_dir.glob("*"):
                original_path = self._get_original_path(backup_file.name)
                if original_path:
                    try:
                        # Remove current file if it exists
                        if original_path.exists():
                            original_path.unlink()

                        # Restore from backup
                        import shutil

                        shutil.copy2(backup_file, original_path)
                        restored_files.append(str(original_path))
                        logger.info(f"Restored: {original_path.name}")
                    except Exception as e:
                        failed_restores.append(f"{backup_file.name}: {e}")
                        logger.error(f"Failed to restore {backup_file.name}: {e}")

            return {
                "success": len(failed_restores) == 0,
                "message": f"Rollback completed. {len(restored_files)} files restored.",
                "restored_files": restored_files,
                "failed_restores": failed_restores,
            }
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return {"success": False, "error": str(e)}

    async def migration_status(self) -> Dict[str, Any]:
        """Check migration status"""
        try:
            logger.info("Checking migration status")

            status_results = {}

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path
                backup_file = self.backup_dir / Path(original_path).name

                if original_file.exists():
                    size = original_file.stat().st_size
                    if backup_file.exists():
                        backup_size = backup_file.stat().st_size
                        if size != backup_size:
                            status = "migrated"
                        else:
                            status = "same_size"
                        backup_available = True
                    else:
                        status = "no_backup"
                        backup_available = False
                else:
                    status = "not_found"
                    backup_available = False
                    size = 0

                status_results[original_path] = {
                    "status": status,
                    "size": size,
                    "backup_available": backup_available,
                }

            return {"success": True, "status_results": status_results}
        except Exception as e:
            logger.error(f"Status check failed: {e}")
            return {"success": False, "error": str(e)}

    async def cleanup_backups(self) -> Dict[str, Any]:
        """Clean up backup files"""
        try:
            logger.info("Cleaning up backup files")

            if not self.backup_dir.exists():
                return {
                    "success": True,
                    "message": "No backup directory found",
                    "deleted_files": 0,
                    "backup_dir": str(self.backup_dir),
                }

            # Count files to be deleted
            backup_files = list(self.backup_dir.glob("*"))

            if not backup_files:
                return {
                    "success": True,
                    "message": "No backup files found",
                    "deleted_files": 0,
                    "backup_dir": str(self.backup_dir),
                }

            # Delete backup directory
            import shutil

            shutil.rmtree(self.backup_dir)

            return {
                "success": True,
                "message": f"Deleted {len(backup_files)} backup files",
                "deleted_files": len(backup_files),
                "backup_dir": str(self.backup_dir),
            }
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
            return {"success": False, "error": str(e)}

    async def _create_backup(self) -> Dict[str, Any]:
        """Create backup of original files"""
        try:
            logger.info("Creating backup of original files")

            # Create backup directory
            self.backup_dir.mkdir(parents=True, exist_ok=True)

            backed_up_files = []

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path

                if original_file.exists():
                    try:
                        # Copy to backup
                        backup_file = self.backup_dir / original_file.name
                        import shutil

                        shutil.copy2(original_file, backup_file)
                        backed_up_files.append(str(backup_file))
                        logger.info(f"Backed up: {original_file.name}")
                    except Exception as e:
                        logger.error(f"Failed to backup {original_file.name}: {e}")
                        return {
                            "success": False,
                            "error": f"Backup failed for {original_file.name}: {e}",
                        }

            return {"success": True, "backed_up_files": backed_up_files}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _migrate_files(self) -> Dict[str, Any]:
        """Migrate files from optimized to standard names"""
        try:
            logger.info("Migrating Docker files")

            migrated_files = []

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path
                optimized_file = self.project_root / optimized_path

                if optimized_file.exists():
                    try:
                        # Remove original if it exists
                        if original_file.exists():
                            original_file.unlink()
                            logger.info(f"Removed original: {original_file.name}")

                        # Rename optimized to standard name
                        optimized_file.rename(original_file)
                        migrated_files.append(str(original_file))
                        logger.info(
                            f"Migrated: {optimized_file.name} -> {original_file.name}"
                        )
                    except Exception as e:
                        logger.error(f"Failed to migrate {optimized_file.name}: {e}")
                        return {
                            "success": False,
                            "error": f"Migration failed for {optimized_file.name}: {e}",
                        }

            return {"success": True, "migrated_files": migrated_files}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _verify_migration(self) -> Dict[str, Any]:
        """Verify migration was successful"""
        try:
            logger.info("Verifying migration")

            verification_results = {}

            file_mappings = {
                "containers/Dockerfile.api": "containers/Dockerfile.api.optimized",
                "containers/Dockerfile.frontend": "containers/Dockerfile.frontend.optimized",
                "containers/docker-compose.dev.yml": "containers/docker-compose.development.yml",
                "containers/docker-compose.prod.yml": "containers/docker-compose.production.yml",
                ".dockerignore": ".dockerignore.optimized",
            }

            for original_path, optimized_path in file_mappings.items():
                original_file = self.project_root / original_path
                backup_file = self.backup_dir / Path(original_path).name

                if original_file.exists():
                    if backup_file.exists():
                        original_size = original_file.stat().st_size
                        backup_size = backup_file.stat().st_size

                        if original_size != backup_size:
                            verification_results[original_path] = "migrated"
                        else:
                            verification_results[original_path] = "same_size"
                    else:
                        verification_results[original_path] = "no_backup"
                else:
                    verification_results[original_path] = "not_found"

            return {"success": True, "verification_results": verification_results}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _get_original_path(self, backup_filename: str) -> Optional[Path]:
        """Get original file path from backup filename"""
        file_mappings = {
            "Dockerfile.api": "containers/Dockerfile.api",
            "Dockerfile.frontend": "containers/Dockerfile.frontend",
            "docker-compose.dev.yml": "containers/docker-compose.dev.yml",
            "docker-compose.prod.yml": "containers/docker-compose.prod.yml",
            ".dockerignore": ".dockerignore",
        }

        if backup_filename in file_mappings:
            return self.project_root / file_mappings[backup_filename]

        return None


# Dependency injection
def get_migration_service() -> DockerMigrationService:
    return DockerMigrationService()


@router.post("/migrate", response_model=MigrationResponse)
async def migrate_docker_files(
    request: MigrationRequest,
    service: DockerMigrationService = Depends(get_migration_service),
):
    """Migrate Docker files from optimized to standard names"""
    try:
        result = await service.migrate_docker_files(
            force=request.force, backup=request.backup
        )

        if result["success"]:
            return MigrationResponse(
                success=True, message=result["message"], details=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Migration endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/rollback", response_model=MigrationResponse)
async def rollback_migration(
    service: DockerMigrationService = Depends(get_migration_service),
):
    """Rollback Docker file migration"""
    try:
        result = await service.rollback_migration()

        if result["success"]:
            return MigrationResponse(
                success=True, message=result["message"], details=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Rollback endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=StatusResponse)
async def migration_status(
    service: DockerMigrationService = Depends(get_migration_service),
):
    """Check migration status"""
    try:
        result = await service.migration_status()

        if result["success"]:
            return StatusResponse(success=True, status_results=result["status_results"])
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Status endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cleanup", response_model=CleanupResponse)
async def cleanup_backups(
    service: DockerMigrationService = Depends(get_migration_service),
):
    """Clean up backup files"""
    try:
        result = await service.cleanup_backups()

        if result["success"]:
            return CleanupResponse(
                success=True,
                deleted_files=result["deleted_files"],
                backup_dir=result["backup_dir"],
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Cleanup endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "docker-migration"}
