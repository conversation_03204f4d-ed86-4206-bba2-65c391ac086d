{"server": {"domain": "localhost", "port": 5000, "host": "0.0.0.0", "debug": false, "threaded": true}, "ssl": {"enabled": true, "cert_path": "ssl/cert.pem", "key_path": "ssl/key.pem", "lets_encrypt": {"enabled": true, "email": "<EMAIL>", "staging": false}}, "sites": {"sites_dir": "sites", "max_sites": 100, "default_template": "modern", "auto_deploy": true}, "monitoring": {"enabled": true, "interval_seconds": 30, "max_history_size": 1000, "alerts": {"cpu_threshold": 80, "memory_threshold": 85, "disk_threshold": 90, "network_threshold": 1000000}, "log_retention_days": 30, "metrics_retention_days": 7}, "deployment": {"auto_backup": true, "validation_required": true, "rollback_on_failure": true, "max_concurrent_deployments": 3, "deployment_timeout_seconds": 300, "health_check_after_deploy": true, "health_check_timeout_seconds": 30}, "backup": {"enabled": true, "backup_dir": "backups", "schedule": "daily", "retention_days": 30, "compression": true, "encryption": false, "auto_cleanup": true, "backup_before_deploy": true}, "hot_reload": {"enabled": true, "watch_recursive": true, "debounce_seconds": 1.0, "file_patterns": ["*.html", "*.css", "*.js", "*.json", "*.md"], "exclude_patterns": ["*.tmp", "*.log", ".git/*", "node_modules/*"], "auto_deploy_on_change": false}, "network": {"firewall": {"enabled": true, "allowed_ports": [22, 80, 443, 5000, 8080], "blocked_ips": []}, "nginx": {"enabled": true, "config_path": "/etc/nginx/sites-available", "sites_enabled_path": "/etc/nginx/sites-enabled", "auto_config": true, "ssl_redirect": true}, "health_checks": {"enabled": true, "interval_seconds": 60, "timeout_seconds": 10, "retry_count": 3}}, "security": {"rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_size": 20}, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["Content-Type", "Authorization"]}, "authentication": {"enabled": false, "method": "basic", "users": {}}, "file_validation": {"enabled": true, "allowed_extensions": [".html", ".css", ".js", ".json", ".md", ".png", ".jpg", ".jpeg", ".gif", ".svg"], "max_file_size_mb": 10, "scan_for_malware": false}}, "performance": {"caching": {"enabled": true, "cache_dir": "cache", "max_cache_size_mb": 100, "cache_ttl_seconds": 3600}, "compression": {"enabled": true, "gzip_level": 6, "brotli_enabled": true}, "optimization": {"minify_html": true, "minify_css": true, "minify_js": true, "optimize_images": true, "inline_critical_css": false}}, "logging": {"level": "INFO", "file": "logs/home_server.log", "max_file_size_mb": 10, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "access_log": "logs/access.log", "error_log": "logs/error.log"}, "notifications": {"enabled": false, "email": {"smtp_server": "", "smtp_port": 587, "username": "", "password": "", "from_email": "", "to_emails": []}, "webhook": {"url": "", "events": ["deployment", "backup", "alert"]}}, "maintenance": {"auto_updates": {"enabled": false, "check_interval_hours": 24, "auto_install": false}, "cleanup": {"enabled": true, "schedule": "weekly", "cleanup_logs": true, "cleanup_cache": true, "cleanup_temp_files": true}}}