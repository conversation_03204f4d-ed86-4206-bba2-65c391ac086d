{"learning_service": {"enabled": true, "port": 8084, "host": "0.0.0.0", "log_level": "INFO", "environment": "production"}, "container": {"name": "ai-coding-learning", "restart_policy": "unless-stopped", "health_check": {"interval": "30s", "timeout": "10s", "start_period": "40s", "retries": 3, "endpoint": "/health"}, "resources": {"limits": {"cpus": "2.0", "memory": "4G"}, "reservations": {"cpus": "1.0", "memory": "2G"}}, "volumes": {"data": "/app/data", "logs": "/app/logs", "backups": "/app/backups", "config": "/app/config", "models": "/app/models", "learning": "/app/learning"}, "environment": {"PYTHONPATH": "/app", "LEARNING_ENABLED": "true", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "PORT": "8084"}}, "dependencies": {"required_services": ["api", "db", "redis", "ollama"], "health_checks": {"api": "http://api:8000/health", "db": "******************************************************/ai_coding_agent", "redis": "redis://redis:6379", "ollama": "http://ollama:11434/api/tags"}}, "learning_components": {"automated_learner": {"enabled": true, "config_file": "config/automated_learner_config.json", "data_directory": "/app/data/learning/automated", "log_file": "/app/logs/learning/automated_learner.log"}, "best_practices_learner": {"enabled": true, "config_file": "config/best_practices_config.json", "data_directory": "/app/data/learning/best_practices", "log_file": "/app/logs/learning/best_practices_learner.log"}, "framework_monitor": {"enabled": true, "config_file": "config/framework_monitor_config.json", "data_directory": "/app/data/learning/framework_monitor", "log_file": "/app/logs/learning/framework_monitor.log"}}, "monitoring": {"enabled": true, "metrics_endpoint": "/metrics", "prometheus_integration": true, "log_aggregation": true, "performance_tracking": true}, "security": {"non_root_user": true, "user": "learning", "group": "learning", "file_permissions": "644", "directory_permissions": "755"}, "networking": {"network": "ai-coding-network", "internal_port": 8084, "external_port": 8084, "service_discovery": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "handlers": [{"type": "file", "path": "/app/logs/learning/learning_service.log", "max_size": "10MB", "backup_count": 5}, {"type": "console", "enabled": true}]}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 30, "backup_directory": "/app/backups/learning", "include_data": true, "include_logs": false}, "performance": {"max_concurrent_requests": 100, "request_timeout": 30, "memory_limit": "4G", "cpu_limit": "2.0", "connection_pool_size": 20}, "api_endpoints": {"health": "/health", "status": "/status", "summary": "/learning/summary", "recommendations": "/learning/recommendations", "learn_pattern": "/learn/pattern", "learn_preference": "/learn/preference"}}