"""
AI Model Performance Monitor

Real-time monitoring of AI model performance, resource usage, and health metrics.
Provides alerts and notifications for performance issues.
"""

import asyncio
import json
import logging
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import psutil

logger = logging.getLogger(__name__)


@dataclass
class PerformanceAlert:
    """Performance alert for AI models"""

    alert_id: str
    model_name: str
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "alert_id": self.alert_id,
            "model_name": self.model_name,
            "alert_type": self.alert_type,
            "severity": self.severity,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PerformanceAlert":
        """Create from dictionary"""
        return cls(
            alert_id=data["alert_id"],
            model_name=data["model_name"],
            alert_type=data["alert_type"],
            severity=data["severity"],
            message=data["message"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            resolved=data.get("resolved", False),
            resolved_at=(
                datetime.fromisoformat(data["resolved_at"])
                if data.get("resolved_at")
                else None
            ),
            metadata=data.get("metadata", {}),
        )


@dataclass
class ResourceUsage:
    """System resource usage metrics"""

    timestamp: datetime = field(default_factory=datetime.now)
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    memory_available_mb: float = 0.0
    disk_usage_percent: float = 0.0
    network_sent_mb: float = 0.0
    network_recv_mb: float = 0.0
    gpu_usage_percent: Optional[float] = None
    gpu_memory_mb: Optional[float] = None
    gpu_temperature: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_used_mb": self.memory_used_mb,
            "memory_available_mb": self.memory_available_mb,
            "disk_usage_percent": self.disk_usage_percent,
            "network_sent_mb": self.network_sent_mb,
            "network_recv_mb": self.network_recv_mb,
            "gpu_usage_percent": self.gpu_usage_percent,
            "gpu_memory_mb": self.gpu_memory_mb,
            "gpu_temperature": self.gpu_temperature,
        }


class PerformanceMonitor:
    """
    Real-time performance monitoring for AI models.

    Features:
    - Resource usage tracking
    - Performance alerts
    - Health checks
    - Trend analysis
    - Alert notifications
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the performance monitor"""
        self.config = config
        self.alerts: List[PerformanceAlert] = []
        self.resource_history: deque = deque(maxlen=1000)  # Keep last 1000 measurements
        self.model_performance: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=100)
        )
        self.alert_handlers: List[Callable] = []
        self.monitoring_active = False
        self.monitor_thread = None

        # Alert thresholds
        self.alert_thresholds = {
            "response_time_warning": 3000.0,
            "response_time_critical": 5000.0,
            "memory_warning": 80.0,
            "memory_critical": 90.0,
            "cpu_warning": 70.0,
            "cpu_critical": 85.0,
            "gpu_warning": 80.0,
            "gpu_critical": 95.0,
            "disk_warning": 85.0,
            "disk_critical": 95.0,
            "error_rate_warning": 0.05,
            "error_rate_critical": 0.10,
        }

        # Monitoring intervals
        self.resource_monitor_interval = config.get(
            "resource_monitor_interval", 30
        )  # seconds
        self.alert_check_interval = config.get("alert_check_interval", 60)  # seconds
        self.health_check_interval = config.get("health_check_interval", 300)  # seconds

        logger.info("Performance Monitor initialized")

    def start_monitoring(self) -> None:
        """Start the performance monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop, daemon=True
            )
            self.monitor_thread.start()
            logger.info("Performance monitoring started")

    def stop_monitoring(self) -> None:
        """Stop the performance monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join()
            self.monitor_thread = None
        logger.info("Performance monitoring stopped")

    def add_alert_handler(self, handler: Callable[[PerformanceAlert], None]) -> None:
        """Add an alert handler function"""
        self.alert_handlers.append(handler)
        logger.info(f"Added alert handler: {handler.__name__}")

    def record_model_performance(
        self,
        model_name: str,
        response_time_ms: float,
        success: bool = True,
        memory_usage_mb: Optional[float] = None,
        cpu_usage_percent: Optional[float] = None,
        gpu_usage_percent: Optional[float] = None,
        gpu_memory_mb: Optional[float] = None,
    ) -> None:
        """Record performance metrics for a model"""
        performance_data = {
            "timestamp": datetime.now(),
            "response_time_ms": response_time_ms,
            "success": success,
            "memory_usage_mb": memory_usage_mb,
            "cpu_usage_percent": cpu_usage_percent,
            "gpu_usage_percent": gpu_usage_percent,
            "gpu_memory_mb": gpu_memory_mb,
        }

        self.model_performance[model_name].append(performance_data)

        # Check for performance issues
        self._check_model_performance(model_name, performance_data)

    def get_model_performance_summary(
        self, model_name: str, hours: int = 24
    ) -> Optional[Dict[str, Any]]:
        """Get performance summary for a model"""
        if model_name not in self.model_performance:
            return None

        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_data = [
            data
            for data in self.model_performance[model_name]
            if data["timestamp"] > cutoff_time
        ]

        if not recent_data:
            return None

        total_requests = len(recent_data)
        successful_requests = sum(1 for data in recent_data if data["success"])
        avg_response_time = (
            sum(data["response_time_ms"] for data in recent_data) / total_requests
        )
        success_rate = successful_requests / total_requests
        error_rate = 1 - success_rate

        # Calculate percentiles
        response_times = [data["response_time_ms"] for data in recent_data]
        response_times.sort()
        p50 = response_times[len(response_times) // 2]
        p95 = response_times[int(len(response_times) * 0.95)]
        p99 = response_times[int(len(response_times) * 0.99)]

        return {
            "model_name": model_name,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "success_rate": success_rate,
            "error_rate": error_rate,
            "avg_response_time_ms": avg_response_time,
            "p50_response_time_ms": p50,
            "p95_response_time_ms": p95,
            "p99_response_time_ms": p99,
            "time_range_hours": hours,
        }

    def get_active_alerts(
        self, severity: Optional[str] = None
    ) -> List[PerformanceAlert]:
        """Get active alerts, optionally filtered by severity"""
        alerts = [alert for alert in self.alerts if not alert.resolved]

        if severity:
            alerts = [alert for alert in alerts if alert.severity == severity]

        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)

    def resolve_alert(self, alert_id: str) -> bool:
        """Mark an alert as resolved"""
        for alert in self.alerts:
            if alert.alert_id == alert_id and not alert.resolved:
                alert.resolved = True
                alert.resolved_at = datetime.now()
                logger.info(f"Alert {alert_id} marked as resolved")
                return True
        return False

    def get_resource_usage(self) -> ResourceUsage:
        """Get current system resource usage"""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory usage
        memory = psutil.virtual_memory()

        # Disk usage
        disk = psutil.disk_usage("/")

        # Network usage
        network = psutil.net_io_counters()

        # GPU usage (if available)
        gpu_usage_percent: Optional[float] = None
        gpu_memory_mb: Optional[float] = None
        gpu_temperature: Optional[float] = None

        try:
            # Try to get GPU info using nvidia-ml-py if available
            import pynvml

            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            gpu_util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_usage_percent = float(gpu_util.gpu)

            gpu_memory = pynvml.nvmlDeviceGetMemoryInfo(handle)
            gpu_memory_mb = float(gpu_memory.used) / 1024.0 / 1024.0

            try:
                gpu_temp = pynvml.nvmlDeviceGetTemperature(
                    handle, pynvml.NVML_TEMPERATURE_GPU
                )
                gpu_temperature = float(gpu_temp)
            except:
                pass

        except ImportError:
            # pynvml not available, skip GPU monitoring
            pass
        except Exception as e:
            logger.debug(f"GPU monitoring not available: {e}")

        resource_usage = ResourceUsage(
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            memory_available_mb=memory.available / 1024 / 1024,
            disk_usage_percent=disk.percent,
            network_sent_mb=network.bytes_sent / 1024 / 1024,
            network_recv_mb=network.bytes_recv / 1024 / 1024,
            gpu_usage_percent=gpu_usage_percent,
            gpu_memory_mb=gpu_memory_mb,
            gpu_temperature=gpu_temperature,
        )

        self.resource_history.append(resource_usage)
        return resource_usage

    def get_resource_trends(self, minutes: int = 60) -> Dict[str, Any]:
        """Get resource usage trends"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_data = [
            data for data in self.resource_history if data.timestamp > cutoff_time
        ]

        if not recent_data:
            return {}

        # Calculate trends
        cpu_trend = (recent_data[-1].cpu_percent - recent_data[0].cpu_percent) / len(
            recent_data
        )
        memory_trend = (
            recent_data[-1].memory_percent - recent_data[0].memory_percent
        ) / len(recent_data)

        # Calculate averages
        avg_cpu = sum(data.cpu_percent for data in recent_data) / len(recent_data)
        avg_memory = sum(data.memory_percent for data in recent_data) / len(recent_data)
        avg_disk = sum(data.disk_usage_percent for data in recent_data) / len(
            recent_data
        )

        return {
            "cpu_trend": cpu_trend,
            "memory_trend": memory_trend,
            "avg_cpu_percent": avg_cpu,
            "avg_memory_percent": avg_memory,
            "avg_disk_percent": avg_disk,
            "data_points": len(recent_data),
            "time_range_minutes": minutes,
        }

    def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        last_resource_check = 0
        last_alert_check = 0
        last_health_check = 0

        while self.monitoring_active:
            current_time = time.time()

            try:
                # Resource monitoring
                if current_time - last_resource_check >= self.resource_monitor_interval:
                    self._monitor_resources()
                    last_resource_check = current_time

                # Alert checking
                if current_time - last_alert_check >= self.alert_check_interval:
                    self._check_alerts()
                    last_alert_check = current_time

                # Health checks
                if current_time - last_health_check >= self.health_check_interval:
                    self._perform_health_checks()
                    last_health_check = current_time

                # Sleep for a short interval
                time.sleep(10)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(30)  # Wait longer on error

    def _monitor_resources(self) -> None:
        """Monitor system resources"""
        resource_usage = self.get_resource_usage()

        # Check for resource alerts
        if resource_usage.cpu_percent > self.alert_thresholds["cpu_critical"]:
            self._create_alert(
                "system",
                "critical_cpu",
                "critical",
                f"Critical CPU usage: {resource_usage.cpu_percent:.1f}%",
            )
        elif resource_usage.cpu_percent > self.alert_thresholds["cpu_warning"]:
            self._create_alert(
                "system",
                "high_cpu",
                "high",
                f"High CPU usage: {resource_usage.cpu_percent:.1f}%",
            )

        if resource_usage.memory_percent > self.alert_thresholds["memory_critical"]:
            self._create_alert(
                "system",
                "critical_memory",
                "critical",
                f"Critical memory usage: {resource_usage.memory_percent:.1f}%",
            )
        elif resource_usage.memory_percent > self.alert_thresholds["memory_warning"]:
            self._create_alert(
                "system",
                "high_memory",
                "high",
                f"High memory usage: {resource_usage.memory_percent:.1f}%",
            )

        if resource_usage.disk_usage_percent > self.alert_thresholds["disk_critical"]:
            self._create_alert(
                "system",
                "critical_disk",
                "critical",
                f"Critical disk usage: {resource_usage.disk_usage_percent:.1f}%",
            )
        elif resource_usage.disk_usage_percent > self.alert_thresholds["disk_warning"]:
            self._create_alert(
                "system",
                "high_disk",
                "high",
                f"High disk usage: {resource_usage.disk_usage_percent:.1f}%",
            )

        if resource_usage.gpu_usage_percent:
            if resource_usage.gpu_usage_percent > self.alert_thresholds["gpu_critical"]:
                self._create_alert(
                    "system",
                    "critical_gpu",
                    "critical",
                    f"Critical GPU usage: {resource_usage.gpu_usage_percent:.1f}%",
                )
            elif (
                resource_usage.gpu_usage_percent > self.alert_thresholds["gpu_warning"]
            ):
                self._create_alert(
                    "system",
                    "high_gpu",
                    "high",
                    f"High GPU usage: {resource_usage.gpu_usage_percent:.1f}%",
                )

    def _check_model_performance(
        self, model_name: str, performance_data: Dict[str, Any]
    ) -> None:
        """Check model performance for issues"""
        # Check response time
        if (
            performance_data["response_time_ms"]
            > self.alert_thresholds["response_time_critical"]
        ):
            self._create_alert(
                model_name,
                "critical_response_time",
                "critical",
                f"Critical response time: {performance_data['response_time_ms']:.1f}ms",
            )
        elif (
            performance_data["response_time_ms"]
            > self.alert_thresholds["response_time_warning"]
        ):
            self._create_alert(
                model_name,
                "high_response_time",
                "high",
                f"High response time: {performance_data['response_time_ms']:.1f}ms",
            )

        # Check for failures
        if not performance_data["success"]:
            self._create_alert(
                model_name, "model_failure", "high", "Model request failed"
            )

    def _check_alerts(self) -> None:
        """Check for alert conditions across all models"""
        for model_name in self.model_performance:
            summary = self.get_model_performance_summary(model_name, hours=1)
            if not summary:
                continue

            # Check error rate
            if summary["error_rate"] > self.alert_thresholds["error_rate_critical"]:
                self._create_alert(
                    model_name,
                    "critical_error_rate",
                    "critical",
                    f"Critical error rate: {summary['error_rate']:.2%}",
                )
            elif summary["error_rate"] > self.alert_thresholds["error_rate_warning"]:
                self._create_alert(
                    model_name,
                    "high_error_rate",
                    "high",
                    f"High error rate: {summary['error_rate']:.2%}",
                )

    def _perform_health_checks(self) -> None:
        """Perform system health checks"""
        # Check if models are responding
        for model_name in self.model_performance:
            recent_data = list(self.model_performance[model_name])[
                -10:
            ]  # Last 10 requests
            if not recent_data:
                continue

            # Check if model has been inactive
            last_request = max(data["timestamp"] for data in recent_data)
            if datetime.now() - last_request > timedelta(minutes=30):
                self._create_alert(
                    model_name,
                    "model_inactive",
                    "medium",
                    "Model has been inactive for 30+ minutes",
                )

    def _create_alert(
        self, model_name: str, alert_type: str, severity: str, message: str
    ) -> None:
        """Create a new performance alert"""
        # Check if similar alert already exists
        for alert in self.alerts:
            if (
                alert.model_name == model_name
                and alert.alert_type == alert_type
                and not alert.resolved
                and datetime.now() - alert.timestamp < timedelta(minutes=5)
            ):
                return  # Don't create duplicate alerts

        alert_id = f"{model_name}_{alert_type}_{int(time.time())}"
        alert = PerformanceAlert(
            alert_id=alert_id,
            model_name=model_name,
            alert_type=alert_type,
            severity=severity,
            message=message,
            metadata={"timestamp": datetime.now().isoformat()},
        )

        self.alerts.append(alert)

        # Notify alert handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error(f"Error in alert handler {handler.__name__}: {e}")

        logger.warning(f"Performance alert created: {alert_id} - {message}")

    def save_monitoring_data(self, file_path: str) -> None:
        """Save monitoring data to file"""
        data = {
            "alerts": [alert.to_dict() for alert in self.alerts],
            "resource_history": [usage.to_dict() for usage in self.resource_history],
            "model_performance": {
                model: list(perf_data)
                for model, perf_data in self.model_performance.items()
            },
            "config": self.config,
            "alert_thresholds": self.alert_thresholds,
        }

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"Saved monitoring data to {file_path}")

    def load_monitoring_data(self, file_path: str) -> None:
        """Load monitoring data from file"""
        if not Path(file_path).exists():
            return

        with open(file_path, "r") as f:
            data = json.load(f)

        self.alerts = [
            PerformanceAlert.from_dict(alert) for alert in data.get("alerts", [])
        ]

        # Load resource history
        self.resource_history.clear()
        for usage_data in data.get("resource_history", []):
            usage = ResourceUsage(
                timestamp=datetime.fromisoformat(usage_data["timestamp"]),
                cpu_percent=usage_data["cpu_percent"],
                memory_percent=usage_data["memory_percent"],
                memory_used_mb=usage_data["memory_used_mb"],
                memory_available_mb=usage_data["memory_available_mb"],
                disk_usage_percent=usage_data["disk_usage_percent"],
                network_sent_mb=usage_data["network_sent_mb"],
                network_recv_mb=usage_data["network_recv_mb"],
                gpu_usage_percent=usage_data.get("gpu_usage_percent"),
                gpu_memory_mb=usage_data.get("gpu_memory_mb"),
                gpu_temperature=usage_data.get("gpu_temperature"),
            )
            self.resource_history.append(usage)

        # Load model performance
        self.model_performance.clear()
        for model_name, perf_data in data.get("model_performance", {}).items():
            self.model_performance[model_name] = deque(perf_data, maxlen=100)

        logger.info(f"Loaded monitoring data from {file_path}")

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get summary of monitoring activities"""
        active_alerts = self.get_active_alerts()

        return {
            "total_alerts": len(self.alerts),
            "active_alerts": len(active_alerts),
            "alerts_by_severity": {
                "critical": len([a for a in active_alerts if a.severity == "critical"]),
                "high": len([a for a in active_alerts if a.severity == "high"]),
                "medium": len([a for a in active_alerts if a.severity == "medium"]),
                "low": len([a for a in active_alerts if a.severity == "low"]),
            },
            "models_monitored": len(self.model_performance),
            "resource_data_points": len(self.resource_history),
            "alert_handlers": len(self.alert_handlers),
            "monitoring_active": self.monitoring_active,
        }
