{"content_directory": "content", "media_directory": "content/media", "versions_directory": "content/versions", "cache_directory": "content/content_cache", "max_file_size_mb": 50, "allowed_file_types": [".html", ".md", ".txt", ".json", ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".tiff", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"], "backup": {"enabled": true, "auto_backup": true, "backup_schedule": "daily", "retention_days": 30, "include_media": true, "include_versions": true, "compression": true}, "ai_models": {"content_generation": {"model": "deepseek-coder:1.3b", "endpoint": "http://localhost:11434/api/generate", "temperature": 0.7, "max_tokens": 2000, "timeout": 30}, "blog_generation": {"model": "qwen2.5-coder:3b", "endpoint": "http://localhost:11434/api/generate", "temperature": 0.8, "max_tokens": 2500, "timeout": 30}, "meta_generation": {"model": "yi-coder:1.5b", "endpoint": "http://localhost:11434/api/generate", "temperature": 0.5, "max_tokens": 200, "timeout": 15}, "alt_text_generation": {"model": "starcoder2:3b", "endpoint": "http://localhost:11434/api/generate", "temperature": 0.6, "max_tokens": 100, "timeout": 10}}, "image_optimization": {"enabled": true, "quality": 85, "max_dimensions": [1920, 1080], "formats": {"webp": {"enabled": true, "quality": 80}, "jpeg": {"enabled": true, "quality": 85}, "png": {"enabled": true, "optimize": true}}, "thumbnails": {"enabled": true, "sizes": [[300, 200], [600, 400], [1200, 800]]}, "optimization_levels": {"low": {"quality": 70, "max_dimensions": [800, 600]}, "medium": {"quality": 85, "max_dimensions": [1200, 800]}, "high": {"quality": 95, "max_dimensions": [1920, 1080]}}}, "content_types": {"article": {"template": "article_template.html", "required_fields": ["title", "content", "author"], "optional_fields": ["tags", "meta_description", "featured_image"], "validation_rules": {"title_min_length": 10, "title_max_length": 200, "content_min_length": 100, "content_max_length": 10000}}, "blog": {"template": "blog_template.html", "required_fields": ["title", "content", "author"], "optional_fields": ["tags", "meta_description", "featured_image", "excerpt"], "validation_rules": {"title_min_length": 10, "title_max_length": 200, "content_min_length": 200, "content_max_length": 15000}}, "page": {"template": "page_template.html", "required_fields": ["title", "content"], "optional_fields": ["meta_description", "template"], "validation_rules": {"title_min_length": 5, "title_max_length": 200, "content_min_length": 50, "content_max_length": 50000}}, "image": {"template": "image_template.html", "required_fields": ["title", "file_path"], "optional_fields": ["alt_text", "caption", "tags", "dimensions"], "validation_rules": {"title_min_length": 3, "title_max_length": 100, "max_file_size_mb": 10}}, "video": {"template": "video_template.html", "required_fields": ["title", "file_path"], "optional_fields": ["description", "duration", "tags", "thumbnail"], "validation_rules": {"title_min_length": 3, "title_max_length": 100, "max_file_size_mb": 100}}, "document": {"template": "document_template.html", "required_fields": ["title", "file_path"], "optional_fields": ["description", "tags", "file_size"], "validation_rules": {"title_min_length": 3, "title_max_length": 100, "max_file_size_mb": 50}}}, "version_control": {"enabled": true, "max_versions_per_item": 10, "auto_version_on_update": true, "version_metadata": {"include_content_hash": true, "include_file_size": true, "include_author": true, "include_timestamp": true}}, "search": {"enabled": true, "index_content": true, "index_metadata": true, "search_fields": ["title", "content", "tags", "author"], "fuzzy_search": true, "max_results": 50}, "seo": {"auto_generate_meta": true, "meta_description_length": 160, "auto_generate_alt_text": true, "structured_data": {"enabled": true, "include_article": true, "include_image": true, "include_video": true}}, "media_processing": {"auto_optimize_images": true, "generate_thumbnails": true, "create_webp_versions": true, "watermark": {"enabled": false, "image_path": "", "position": "bottom-right", "opacity": 0.3}, "metadata_extraction": {"enabled": true, "extract_exif": true, "extract_creation_date": true, "extract_dimensions": true}}, "workflow": {"draft_review": {"enabled": false, "require_approval": false, "approvers": []}, "publishing": {"auto_publish": false, "schedule_publishing": true, "publish_notifications": false}, "archiving": {"auto_archive_old": false, "archive_after_days": 365, "archive_notifications": false}}, "performance": {"cache_enabled": true, "cache_ttl_seconds": 3600, "lazy_loading": true, "image_lazy_loading": true, "content_preview": {"enabled": true, "preview_length": 200}}, "security": {"file_validation": {"enabled": true, "scan_for_malware": false, "validate_file_types": true, "max_file_size_mb": 50}, "access_control": {"enabled": false, "require_authentication": false, "allowed_users": []}, "content_validation": {"enabled": true, "sanitize_html": true, "block_script_tags": true, "allowed_html_tags": ["p", "br", "strong", "em", "u", "h1", "h2", "h3", "h4", "h5", "h6", "ul", "ol", "li", "a", "img", "blockquote", "code", "pre"]}}, "notifications": {"enabled": false, "email": {"smtp_server": "", "smtp_port": 587, "username": "", "password": "", "from_email": "", "to_emails": []}, "webhook": {"url": "", "events": ["content_created", "content_updated", "content_published"]}}, "logging": {"level": "INFO", "file": "logs/cms_content.log", "max_file_size_mb": 10, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "log_events": ["content_created", "content_updated", "content_deleted", "content_published", "media_uploaded", "ai_generation"]}, "templates": {"default_templates": {"article": "templates/article.html", "blog": "templates/blog.html", "page": "templates/page.html", "image": "templates/image.html", "video": "templates/video.html", "document": "templates/document.html"}, "custom_templates_dir": "templates/custom", "template_variables": {"site_name": "AI Coding Agent CMS", "site_description": "Content Management System", "site_url": "http://localhost:5000", "author": "AI Coding Agent"}}, "api": {"enabled": true, "rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_size": 20}, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["Content-Type", "Authorization"]}, "authentication": {"enabled": false, "method": "token", "token_expiry_hours": 24}}}