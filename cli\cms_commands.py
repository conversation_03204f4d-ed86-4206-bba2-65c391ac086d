"""
CLI Commands for Phase 2.3: CMS & Content
Provides commands for AI-powered content creation, image optimization, and content management.
"""

import json
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.table import Table

from content.cms_content_manager import ContentItem, ContentManager, ImageMetadata


class CMSCommands:
    """CLI commands for CMS and content management"""

    def __init__(self, config_path: str = "config/cms_config.json"):
        self.config_path = config_path
        self.cms = None
        self._initialize_cms()

    def _initialize_cms(self):
        """Initialize the CMS system"""
        try:
            self.cms = ContentManager(self.config_path)
            print("✅ CMS Content Manager initialized")
        except Exception as e:
            print(f"❌ Error initializing CMS: {e}")

    def create_content(
        self,
        title: str,
        content: str,
        content_type: str = "article",
        author: str = "AI Coding Agent",
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Create new content"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            content_item = self.cms.create_content(
                title=title,
                content=content,
                content_type=content_type,
                author=author,
                tags=tags or [],
                metadata=metadata or {},
            )

            print(f"✅ Content created successfully")
            print(f"📝 ID: {content_item.id}")
            print(f"📝 Title: {content_item.title}")
            print(f"📝 Type: {content_item.content_type}")
            print(f"📝 Status: {content_item.status}")
            print(f"📝 Author: {content_item.author}")
            print(f"📝 Created: {content_item.created_at}")

            return True

        except Exception as e:
            print(f"❌ Error creating content: {e}")
            return False

    def generate_ai_content(
        self,
        topic: str,
        content_type: str = "article",
        length: str = "medium",
        style: str = "informative",
        author: str = "AI Coding Agent",
        tags: Optional[List[str]] = None,
    ):
        """Generate content using AI"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            print(f"🤖 Generating AI content for topic: {topic}")
            print(f"📝 Type: {content_type}")
            print(f"📏 Length: {length}")
            print(f"🎨 Style: {style}")

            content_item = self.cms.generate_ai_content(
                topic=topic,
                content_type=content_type,
                length=length,
                style=style,
                author=author,
                tags=tags or [topic.lower()],
            )

            print(f"✅ AI content generated successfully")
            print(f"📝 ID: {content_item.id}")
            print(f"📝 Title: {content_item.title}")
            print(f"📝 Status: {content_item.status}")
            print(
                f"📝 AI Generated: {content_item.metadata.get('ai_generated', False)}"
            )

            # Show content preview
            preview = (
                content_item.content[:200] + "..."
                if len(content_item.content) > 200
                else content_item.content
            )
            print(f"📄 Preview: {preview}")

            return True

        except Exception as e:
            print(f"❌ Error generating AI content: {e}")
            return False

    def update_content(
        self,
        content_id: str,
        title: Optional[str] = None,
        content: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        author: Optional[str] = None,
    ):
        """Update existing content"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            content_item = self.cms.update_content(
                content_id=content_id,
                title=title,
                content=content,
                tags=tags,
                metadata=metadata,
                author=author,
            )

            print(f"✅ Content updated successfully")
            print(f"📝 ID: {content_item.id}")
            print(f"📝 Title: {content_item.title}")
            print(f"📝 Version: {content_item.version}")
            print(f"📝 Updated: {content_item.updated_at}")

            return True

        except Exception as e:
            print(f"❌ Error updating content: {e}")
            return False

    def publish_content(self, content_id: str):
        """Publish content"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            content_item = self.cms.publish_content(content_id)

            print(f"✅ Content published successfully")
            print(f"📝 ID: {content_item.id}")
            print(f"📝 Title: {content_item.title}")
            print(f"📝 Status: {content_item.status}")

            return True

        except Exception as e:
            print(f"❌ Error publishing content: {e}")
            return False

    def archive_content(self, content_id: str):
        """Archive content"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            content_item = self.cms.archive_content(content_id)

            print(f"✅ Content archived successfully")
            print(f"📝 ID: {content_item.id}")
            print(f"📝 Title: {content_item.title}")
            print(f"📝 Status: {content_item.status}")

            return True

        except Exception as e:
            print(f"❌ Error archiving content: {e}")
            return False

    def delete_content(self, content_id: str):
        """Delete content"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            success = self.cms.delete_content(content_id)

            if success:
                print(f"✅ Content deleted successfully")
                print(f"📝 ID: {content_id}")
            else:
                print(f"❌ Failed to delete content")

            return success

        except Exception as e:
            print(f"❌ Error deleting content: {e}")
            return False

    def get_content(self, content_id: str):
        """Get content by ID"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            content_item = self.cms.get_content(content_id)

            if content_item:
                print(f"📝 Content Details:")
                print(f"   ID: {content_item.id}")
                print(f"   Title: {content_item.title}")
                print(f"   Type: {content_item.content_type}")
                print(f"   Author: {content_item.author}")
                print(f"   Status: {content_item.status}")
                print(f"   Version: {content_item.version}")
                print(f"   Created: {content_item.created_at}")
                print(f"   Updated: {content_item.updated_at}")
                print(f"   Tags: {', '.join(content_item.tags)}")

                if content_item.file_size:
                    print(f"   File Size: {content_item.file_size / 1024:.1f} KB")

                # Show content preview
                preview = (
                    content_item.content[:300] + "..."
                    if len(content_item.content) > 300
                    else content_item.content
                )
                print(f"   Content Preview: {preview}")

            else:
                print(f"❌ Content not found: {content_id}")

            return content_item is not None

        except Exception as e:
            print(f"❌ Error getting content: {e}")
            return False

    def list_content(
        self,
        content_type: Optional[str] = None,
        status: Optional[str] = None,
        author: Optional[str] = None,
        tags: Optional[List[str]] = None,
    ):
        """List content with filters"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            content_items = self.cms.list_content(
                content_type=content_type, status=status, author=author, tags=tags
            )

            if not content_items:
                print("📝 No content found")
                return True

            print(f"📝 Found {len(content_items)} content items:")
            print("-" * 80)

            for item in content_items:
                status_emoji = {"published": "✅", "draft": "📝", "archived": "📦"}.get(
                    item.status, "❓"
                )

                print(f"{status_emoji} {item.id}")
                print(f"   Title: {item.title}")
                print(f"   Type: {item.content_type}")
                print(f"   Author: {item.author}")
                print(f"   Status: {item.status}")
                print(f"   Updated: {item.updated_at}")
                print(f"   Tags: {', '.join(item.tags)}")
                print()

            return True

        except Exception as e:
            print(f"❌ Error listing content: {e}")
            return False

    def search_content(self, query: str):
        """Search content"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            results = self.cms.search_content(query)

            if not results:
                print(f"🔍 No results found for: {query}")
                return True

            print(f"🔍 Found {len(results)} results for: {query}")
            print("-" * 80)

            for item in results:
                print(f"📝 {item.id}")
                print(f"   Title: {item.title}")
                print(f"   Type: {item.content_type}")
                print(f"   Author: {item.author}")
                print(f"   Status: {item.status}")
                print(f"   Updated: {item.updated_at}")
                print()

            return True

        except Exception as e:
            print(f"❌ Error searching content: {e}")
            return False

    def upload_media(
        self,
        file_path: str,
        title: Optional[str] = None,
        tags: Optional[List[str]] = None,
        optimize: bool = True,
    ):
        """Upload and process media file"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            if not Path(file_path).exists():
                print(f"❌ File not found: {file_path}")
                return False

            print(f"📁 Uploading media file: {file_path}")
            print(f"📝 Title: {title or Path(file_path).stem}")
            print(f"🏷️  Tags: {', '.join(tags or [])}")
            print(f"⚡ Optimization: {'Enabled' if optimize else 'Disabled'}")

            content_item = self.cms.upload_media(
                file_path=file_path, title=title, tags=tags, optimize=optimize
            )

            print(f"✅ Media uploaded successfully")
            print(f"📝 ID: {content_item.id}")
            print(f"📝 Title: {content_item.title}")
            print(f"📝 Type: {content_item.content_type}")
            print(f"📝 Status: {content_item.status}")

            # Show optimization info if available
            if "original_size" in content_item.metadata:
                original_size = content_item.metadata["original_size"]
                optimized_size = content_item.metadata.get(
                    "optimized_size", original_size
                )
                savings = ((original_size - optimized_size) / original_size) * 100

                print(
                    f"📊 File Size: {original_size / 1024:.1f} KB → {optimized_size / 1024:.1f} KB"
                )
                print(f"📊 Savings: {savings:.1f}%")

            if "dimensions" in content_item.metadata:
                width, height = content_item.metadata["dimensions"]
                print(f"📐 Dimensions: {width} x {height}")

            return True

        except Exception as e:
            print(f"❌ Error uploading media: {e}")
            return False

    def get_statistics(self):
        """Get content statistics"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            stats = self.cms.get_content_statistics()

            print("📊 Content Statistics:")
            print("=" * 50)
            print(f"📝 Total Items: {stats['total_items']}")
            print(f"✅ Published: {stats['published_items']}")
            print(f"📝 Drafts: {stats['draft_items']}")
            print(f"📦 Archived: {stats['archived_items']}")
            print(f"💾 Total Size: {stats['total_size_mb']:.2f} MB")

            print(f"\n📊 Content Types:")
            for content_type, count in stats["type_counts"].items():
                print(f"   {content_type}: {count}")

            print(f"\n📊 Authors: <AUTHORS>
            for author, count in stats["author_counts"].items():
                print(f"   {author}: {count}")

            return True

        except Exception as e:
            print(f"❌ Error getting statistics: {e}")
            return False

    def create_sample_content(self):
        """Create sample content for testing"""
        if not self.cms:
            print("❌ CMS not initialized")
            return False

        try:
            print("📝 Creating sample content...")

            # Create sample article
            article_content = """<article>
<h1>Welcome to AI Coding Agent CMS</h1>
<p>This is a sample article created to demonstrate the CMS capabilities.</p>
<h2>Features</h2>
<ul>
<li>AI-powered content generation</li>
<li>Image optimization and processing</li>
<li>Content version control</li>
<li>Media management</li>
<li>Search functionality</li>
</ul>
<p>The CMS provides a comprehensive solution for managing digital content with advanced features.</p>
</article>"""

            article = self.cms.create_content(
                title="Welcome to AI Coding Agent CMS",
                content=article_content,
                content_type="article",
                author="AI Coding Agent",
                tags=["welcome", "cms", "features"],
            )

            # Create sample blog post
            blog_content = """<article>
<h1>Getting Started with Content Management</h1>
<p>Learn how to effectively manage your content using our advanced CMS system.</p>
<h2>Key Benefits</h2>
<ul>
<li>Easy content creation and editing</li>
<li>Automatic image optimization</li>
<li>SEO-friendly content structure</li>
<li>Version control and backup</li>
</ul>
<p>Start creating amazing content today!</p>
</article>"""

            blog = self.cms.create_content(
                title="Getting Started with Content Management",
                content=blog_content,
                content_type="blog",
                author="AI Coding Agent",
                tags=["tutorial", "cms", "getting-started"],
            )

            # Generate AI content
            ai_article = self.cms.generate_ai_content(
                topic="The Future of Content Management Systems",
                content_type="article",
                length="medium",
                style="informative",
                tags=["ai", "future", "cms"],
            )

            print("✅ Sample content created successfully")
            print(f"📝 Article: {article.id}")
            print(f"📝 Blog: {blog.id}")
            print(f"🤖 AI Article: {ai_article.id}")

            return True

        except Exception as e:
            print(f"❌ Error creating sample content: {e}")
            return False


def main():
    """Main CLI entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="CMS Content Management CLI")
    parser.add_argument(
        "command",
        choices=[
            "create",
            "generate",
            "update",
            "publish",
            "archive",
            "delete",
            "get",
            "list",
            "search",
            "upload",
            "stats",
            "sample",
        ],
    )
    parser.add_argument("--title", help="Content title")
    parser.add_argument("--content", help="Content text")
    parser.add_argument("--content-id", help="Content ID")
    parser.add_argument("--content-type", default="article", help="Content type")
    parser.add_argument("--author", default="AI Coding Agent", help="Author name")
    parser.add_argument("--tags", nargs="+", help="Content tags")
    parser.add_argument("--topic", help="Topic for AI generation")
    parser.add_argument(
        "--length",
        default="medium",
        choices=["short", "medium", "long"],
        help="Content length",
    )
    parser.add_argument(
        "--style",
        default="informative",
        choices=["informative", "casual", "professional", "creative"],
        help="Content style",
    )
    parser.add_argument("--file-path", help="File path for media upload")
    parser.add_argument(
        "--optimize", action="store_true", default=True, help="Optimize images"
    )
    parser.add_argument("--query", help="Search query")
    parser.add_argument("--status", help="Content status filter")
    parser.add_argument(
        "--config", default="config/cms_config.json", help="Config file path"
    )

    args = parser.parse_args()

    commands = CMSCommands(args.config)

    if args.command == "create":
        if not args.title or not args.content:
            print("❌ Title and content required for creation")
            return
        commands.create_content(
            args.title, args.content, args.content_type, args.author, args.tags
        )
    elif args.command == "generate":
        if not args.topic:
            print("❌ Topic required for AI generation")
            return
        commands.generate_ai_content(
            args.topic,
            args.content_type,
            args.length,
            args.style,
            args.author,
            args.tags,
        )
    elif args.command == "update":
        if not args.content_id:
            print("❌ Content ID required for update")
            return
        commands.update_content(
            args.content_id, args.title, args.content, args.tags, args.author
        )
    elif args.command == "publish":
        if not args.content_id:
            print("❌ Content ID required for publishing")
            return
        commands.publish_content(args.content_id)
    elif args.command == "archive":
        if not args.content_id:
            print("❌ Content ID required for archiving")
            return
        commands.archive_content(args.content_id)
    elif args.command == "delete":
        if not args.content_id:
            print("❌ Content ID required for deletion")
            return
        commands.delete_content(args.content_id)
    elif args.command == "get":
        if not args.content_id:
            print("❌ Content ID required")
            return
        commands.get_content(args.content_id)
    elif args.command == "list":
        commands.list_content(args.content_type, args.status, args.author, args.tags)
    elif args.command == "search":
        if not args.query:
            print("❌ Search query required")
            return
        commands.search_content(args.query)
    elif args.command == "upload":
        if not args.file_path:
            print("❌ File path required for upload")
            return
        commands.upload_media(args.file_path, args.title, args.tags, args.optimize)
    elif args.command == "stats":
        commands.get_statistics()
    elif args.command == "sample":
        commands.create_sample_content()


if __name__ == "__main__":
    main()
