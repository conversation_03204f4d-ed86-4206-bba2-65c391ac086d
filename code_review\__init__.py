"""
Advanced Code Review Module

This module provides comprehensive code review capabilities using yi-coder
and other AI models for intelligent code analysis, security review, performance
analysis, and quality assessment.

Phase 20 Implementation - Advanced Code Review
"""

from code_review.advanced_code_reviewer import AdvancedCodeReviewer
from code_review.performance_analyzer import PerformanceAnalyzer
from code_review.pr_integration import PRIntegration
from code_review.quality_metrics import QualityMetrics
from code_review.review_dashboard import ReviewDashboard
from code_review.security_analyzer import SecurityAnalyzer
from code_review.yi_coder_integration import YiCoderIntegration

__version__ = "1.0.0"
__author__ = "AI Coding Agent Team"

__all__ = [
    "AdvancedCodeReviewer",
    "YiCoderIntegration",
    "SecurityAnalyzer",
    "PerformanceAnalyzer",
    "QualityMetrics",
    "PRIntegration",
    "ReviewDashboard",
]
