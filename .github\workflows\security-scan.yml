name: Security Scanning
true:
  pull_request:
    branches:
    - main
    - develop
  push:
    branches:
    - main
    - develop
  schedule:
  - cron: 0 2 * * *
jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        cache: npm
        node-version: '20'
    - name: Install Python dependencies
      run: 'python -m pip install --upgrade pip

        pip install -r requirements.txt

        pip install pip-audit safety bandit

        '
    - name: Install Node.js dependencies
      run: npm ci
    - name: Run Python security audit
      run: 'echo "Running Python security audit..."

        pip-audit --format json > python-security-audit.json || true

        '
    - name: Run Python code security analysis
      run: 'echo "Running Python code security analysis..."

        bandit -r src/ -f json -o bandit-report.json || true

        '
    - name: Run Node.js security audit
      run: 'echo "Running Node.js security audit..."

        npm audit --audit-level moderate --json > nodejs-security-audit.json || true

        '
    - continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      name: Run Snyk security scan
      uses: snyk/actions/node@master
      with:
        args: --severity-threshold=high
    - continue-on-error: true
      name: Check for secrets in code
      uses: trufflesecurity/trufflehog@main
      with:
        base: ${{ github.event.before }}
        head: ${{ github.event.after }}
        path: .
    - name: Run dependency vulnerability check
      run: 'echo "Checking for critical vulnerabilities..."

        python scripts/check_critical_vulns.py

        '
    - name: Generate security report
      run: 'python scripts/generate_security_report.py

        '
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: 'python-security-audit.json

          nodejs-security-audit.json

          bandit-report.json

          security-report.md

          '
        retention-days: 90
    - if: github.event_name == 'pull_request'
      name: Comment on PR with security findings
      uses: actions/github-script@v7
      with:
        script: "const fs = require('fs');\n\nlet securityReport = '';\ntry {\n  securityReport\
          \ = fs.readFileSync('security-report.md', 'utf8');\n} catch (error) {\n\
          \  securityReport = 'No security issues found.';\n}\n\ngithub.rest.issues.createComment({\n\
          \  issue_number: context.issue.number,\n  owner: context.repo.owner,\n \
          \ repo: context.repo.repo,\n  body: `## \xF0\u0178\u201D\u2019 Security\
          \ Scan Results\\n\\n${securityReport}\\n\\n---\\n*This comment was automatically\
          \ generated by the security scanning workflow.*`\n});\n"
    - name: Fail on critical vulnerabilities
      run: "if [ -f python-security-audit.json ]; then\n  python scripts/check_critical_vulns.py\n\
        fi\n"
'on':
- push
- pull_request
