#!/usr/bin/env python3
"""
Learning Service API Routes
Provides REST API endpoints for automated learning system operations

Endpoints:
- GET /api/learning/status - Get learning system status
- GET /api/learning/summary - Get learning summary
- GET /api/learning/recommendations - Get learning recommendations
- POST /api/learning/pattern - Learn code pattern
- POST /api/learning/preference - Learn user preference
- GET /api/learning/components - Test learning components
- POST /api/learning/optimize - Optimize learning system
- POST /api/learning/export - Export learning data
- POST /api/learning/import - Import learning data
"""

import asyncio
import json

# Add project root to path for imports
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiohttp
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from pydantic import BaseModel, Field

from api.agent_dependency import get_agent

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


router = APIRouter(prefix="/api/learning", tags=["Learning System"])


# Pydantic models for request/response
class CodePatternRequest(BaseModel):
    pattern_type: str = Field(..., description="Type of code pattern")
    pattern_data: Dict[str, Any] = Field(..., description="Pattern data")
    context: Optional[str] = Field(None, description="Context information")
    language: Optional[str] = Field(None, description="Programming language")


class UserPreferenceRequest(BaseModel):
    preference_type: str = Field(..., description="Type of user preference")
    preference_data: Dict[str, Any] = Field(..., description="Preference data")
    user_id: Optional[str] = Field(None, description="User identifier")
    context: Optional[str] = Field(None, description="Context information")


class OptimizationRequest(BaseModel):
    optimization_type: str = Field(..., description="Type of optimization")
    parameters: Optional[Dict[str, Any]] = Field(
        None, description="Optimization parameters"
    )


class ExportRequest(BaseModel):
    export_format: str = Field("json", description="Export format")
    include_metadata: bool = Field(True, description="Include metadata in export")


class ImportRequest(BaseModel):
    import_format: str = Field("json", description="Import format")
    overwrite_existing: bool = Field(False, description="Overwrite existing data")


class StatusResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any]


class LearningSummaryResponse(BaseModel):
    success: bool
    message: str
    summary: Dict[str, Any]


class LearningRecommendationsResponse(BaseModel):
    success: bool
    message: str
    recommendations: List[Dict[str, Any]]


class ComponentTestResponse(BaseModel):
    success: bool
    message: str
    components: Dict[str, Any]


class OptimizationResponse(BaseModel):
    success: bool
    message: str
    optimization_result: Dict[str, Any]


class ExportResponse(BaseModel):
    success: bool
    message: str
    export_info: Dict[str, Any]


class ImportResponse(BaseModel):
    success: bool
    message: str
    import_info: Dict[str, Any]


@router.get("/status", response_model=StatusResponse)
async def get_learning_status(agent=Depends(get_agent)):
    """Get comprehensive learning system status"""
    try:
        if agent:
            result = await agent.get_status()
            return StatusResponse(
                success=True,
                message="Learning status retrieved successfully",
                data=result,
            )
        else:
            # Try to connect to learning service
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get("http://localhost:8084/status") as response:
                        if response.status == 200:
                            data = await response.json()
                            return StatusResponse(
                                success=True,
                                message="Learning status retrieved from service",
                                data=data,
                            )
                        else:
                            raise HTTPException(
                                status_code=503, detail="Learning service unavailable"
                            )
            except Exception as e:
                raise HTTPException(
                    status_code=503, detail=f"Learning service error: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get learning status: {str(e)}"
        )


@router.get("/summary", response_model=LearningSummaryResponse)
async def get_learning_summary(agent=Depends(get_agent)):
    """Get learning summary"""
    try:
        if agent:
            result = await agent.get_learning_summary({})
            return LearningSummaryResponse(
                success=True,
                message="Learning summary retrieved successfully",
                summary=result,
            )
        else:
            # Try to connect to learning service
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        "http://localhost:8084/learning/summary"
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return LearningSummaryResponse(
                                success=True,
                                message="Learning summary retrieved from service",
                                summary=data,
                            )
                        else:
                            raise HTTPException(
                                status_code=503, detail="Learning service unavailable"
                            )
            except Exception as e:
                raise HTTPException(
                    status_code=503, detail=f"Learning service error: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get learning summary: {str(e)}"
        )


@router.get("/recommendations", response_model=LearningRecommendationsResponse)
async def get_learning_recommendations(agent=Depends(get_agent)):
    """Get learning recommendations"""
    try:
        if agent:
            result = await agent.get_learning_recommendations({})
            recommendations = (
                result.get("recommendations", []) if isinstance(result, dict) else []
            )
            return LearningRecommendationsResponse(
                success=True,
                message="Learning recommendations retrieved successfully",
                recommendations=recommendations,
            )
        else:
            # Try to connect to learning service
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        "http://localhost:8084/learning/recommendations"
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            recommendations = (
                                data.get("recommendations", [])
                                if isinstance(data, dict)
                                else []
                            )
                            return LearningRecommendationsResponse(
                                success=True,
                                message="Learning recommendations retrieved from service",
                                recommendations=recommendations,
                            )
                        else:
                            raise HTTPException(
                                status_code=503, detail="Learning service unavailable"
                            )
            except Exception as e:
                raise HTTPException(
                    status_code=503, detail=f"Learning service error: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get learning recommendations: {str(e)}"
        )


@router.post("/pattern", response_model=StatusResponse)
async def learn_code_pattern(request: CodePatternRequest, agent=Depends(get_agent)):
    """Learn a new code pattern"""
    try:
        pattern_data = {
            "pattern_type": request.pattern_type,
            "pattern_data": request.pattern_data,
            "context": request.context,
            "language": request.language,
        }

        if agent:
            result = await agent.learn_code_pattern(pattern_data)
            return StatusResponse(
                success=True, message="Code pattern learned successfully", data=result
            )
        else:
            # Try to connect to learning service
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "http://localhost:8084/learn/pattern", json=pattern_data
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return StatusResponse(
                                success=True,
                                message="Code pattern learned by service",
                                data=data,
                            )
                        else:
                            raise HTTPException(
                                status_code=503, detail="Learning service unavailable"
                            )
            except Exception as e:
                raise HTTPException(
                    status_code=503, detail=f"Learning service error: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to learn code pattern: {str(e)}"
        )


@router.post("/preference", response_model=StatusResponse)
async def learn_user_preference(
    request: UserPreferenceRequest, agent=Depends(get_agent)
):
    """Learn user preference"""
    try:
        preference_data = {
            "preference_type": request.preference_type,
            "preference_data": request.preference_data,
            "user_id": request.user_id,
            "context": request.context,
        }

        if agent:
            result = await agent.learn_user_preference(preference_data)
            return StatusResponse(
                success=True,
                message="User preference learned successfully",
                data=result,
            )
        else:
            # Try to connect to learning service
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "http://localhost:8084/learn/preference", json=preference_data
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return StatusResponse(
                                success=True,
                                message="User preference learned by service",
                                data=data,
                            )
                        else:
                            raise HTTPException(
                                status_code=503, detail="Learning service unavailable"
                            )
            except Exception as e:
                raise HTTPException(
                    status_code=503, detail=f"Learning service error: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to learn user preference: {str(e)}"
        )


@router.get("/components", response_model=ComponentTestResponse)
async def test_learning_components(agent=Depends(get_agent)):
    """Test learning components functionality"""
    try:
        if agent:
            # Test local components
            components = {}

            # Test automated learner
            if hasattr(agent, "automated_learner") and agent.automated_learner:
                components["automated_learner"] = {
                    "status": "available",
                    "type": type(agent.automated_learner).__name__,
                }
            else:
                components["automated_learner"] = {"status": "not_available"}

            # Test best practices learner
            if (
                hasattr(agent, "best_practices_learner")
                and agent.best_practices_learner
            ):
                components["best_practices_learner"] = {
                    "status": "available",
                    "type": type(agent.best_practices_learner).__name__,
                }
            else:
                components["best_practices_learner"] = {"status": "not_available"}

            # Test framework monitor
            if hasattr(agent, "framework_monitor") and agent.framework_monitor:
                components["framework_monitor"] = {
                    "status": "available",
                    "type": type(agent.framework_monitor).__name__,
                }
            else:
                components["framework_monitor"] = {"status": "not_available"}

            return ComponentTestResponse(
                success=True,
                message="Learning components test completed",
                components=components,
            )
        else:
            # Try to connect to learning service
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get("http://localhost:8084/health") as response:
                        if response.status == 200:
                            data = await response.json()
                            return ComponentTestResponse(
                                success=True,
                                message="Learning components test from service",
                                components=data.get("components", {}),
                            )
                        else:
                            raise HTTPException(
                                status_code=503, detail="Learning service unavailable"
                            )
            except Exception as e:
                raise HTTPException(
                    status_code=503, detail=f"Learning service error: {str(e)}"
                )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to test learning components: {str(e)}"
        )


@router.post("/optimize", response_model=OptimizationResponse)
async def optimize_learning_system(
    request: OptimizationRequest, agent=Depends(get_agent)
):
    """Optimize learning system performance"""
    try:
        if agent and hasattr(agent, "optimize_learning_system"):
            result = await agent.optimize_learning_system(
                {
                    "optimization_type": request.optimization_type,
                    "parameters": request.parameters or {},
                }
            )
            return OptimizationResponse(
                success=True,
                message="Learning system optimization completed",
                optimization_result=result,
            )
        else:
            return OptimizationResponse(
                success=False,
                message="Learning system optimization not supported",
                optimization_result={"error": "Optimization method not available"},
            )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to optimize learning system: {str(e)}"
        )


@router.post("/export", response_model=ExportResponse)
async def export_learning_data(request: ExportRequest, agent=Depends(get_agent)):
    """Export learning data"""
    try:
        if agent:
            # Get learning data from agent components
            learning_data = {
                "export_timestamp": asyncio.get_event_loop().time(),
                "export_format": request.export_format,
                "include_metadata": request.include_metadata,
            }

            # Add component data if available
            if hasattr(agent, "automated_learner") and agent.automated_learner:
                try:
                    learning_data["automated_learner"] = (
                        agent.automated_learner.get_learning_data()
                    )
                except Exception as e:
                    learning_data["automated_learner"] = {"error": str(e)}

            if (
                hasattr(agent, "best_practices_learner")
                and agent.best_practices_learner
            ):
                try:
                    learning_data["best_practices_learner"] = (
                        agent.best_practices_learner.get_learning_data()
                    )
                except Exception as e:
                    learning_data["best_practices_learner"] = {"error": str(e)}

            if hasattr(agent, "framework_monitor") and agent.framework_monitor:
                try:
                    learning_data["framework_monitor"] = (
                        agent.framework_monitor.get_learning_data()
                    )
                except Exception as e:
                    learning_data["framework_monitor"] = {"error": str(e)}

            return ExportResponse(
                success=True,
                message="Learning data exported successfully",
                export_info={
                    "format": request.export_format,
                    "data_size": len(json.dumps(learning_data)),
                    "components": list(learning_data.keys()),
                },
            )
        else:
            return ExportResponse(
                success=False,
                message="Cannot export learning data without local agent",
                export_info={"error": "No local agent available"},
            )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to export learning data: {str(e)}"
        )


@router.post("/import", response_model=ImportResponse)
async def import_learning_data(
    request: ImportRequest, file: UploadFile = File(...), agent=Depends(get_agent)
):
    """Import learning data"""
    try:
        if not agent:
            return ImportResponse(
                success=False,
                message="Cannot import learning data without local agent",
                import_info={"error": "No local agent available"},
            )

        # Read uploaded file
        content = await file.read()
        try:
            learning_data = json.loads(content.decode("utf-8"))
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON format")

        # Import data to components
        imported_components = []

        if (
            "automated_learner" in learning_data
            and hasattr(agent, "automated_learner")
            and agent.automated_learner
        ):
            try:
                agent.automated_learner.import_learning_data(
                    learning_data["automated_learner"]
                )
                imported_components.append("automated_learner")
            except Exception as e:
                pass

        if (
            "best_practices_learner" in learning_data
            and hasattr(agent, "best_practices_learner")
            and agent.best_practices_learner
        ):
            try:
                agent.best_practices_learner.import_learning_data(
                    learning_data["best_practices_learner"]
                )
                imported_components.append("best_practices_learner")
            except Exception as e:
                pass

        if (
            "framework_monitor" in learning_data
            and hasattr(agent, "framework_monitor")
            and agent.framework_monitor
        ):
            try:
                agent.framework_monitor.import_learning_data(
                    learning_data["framework_monitor"]
                )
                imported_components.append("framework_monitor")
            except Exception as e:
                pass

        return ImportResponse(
            success=True,
            message=f"Learning data imported successfully",
            import_info={
                "format": request.import_format,
                "imported_components": imported_components,
                "overwrite_existing": request.overwrite_existing,
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to import learning data: {str(e)}"
        )


# Health check endpoint
@router.get("/health")
async def learning_health_check():
    """Health check for learning API"""
    try:
        return {
            "status": "healthy",
            "service": "learning_api",
            "timestamp": asyncio.get_event_loop().time(),
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": asyncio.get_event_loop().time(),
        }
