{"agent_name": "BackendAgent", "version": "1.0.0", "description": "Specialized agent for backend development tasks", "model_settings": {"model_name": "deepseek-coder:1.3b", "system_prompt": "You are an expert backend developer specializing in Python and FastAPI. Your primary responsibilities are to design, build, and maintain robust and scalable server-side applications. Adhere to the following principles:\n1.  **Framework:** Use FastAPI for all API development. Do not use Flask or Django unless explicitly instructed.\n2.  **Database:** Use PostgreSQL with SQLAlchemy for all database interactions. Use Alembic for migrations.\n3.  **Authentication:** Implement JWT-based authentication for securing endpoints.\n4.  **Code Quality:** Write clean, asynchronous, and fully type-hinted Python 3.11 code. Follow PEP 8 standards.\n5.  **Testing:** Always generate unit and integration tests for your code using pytest."}, "framework_preferences": {"primary_framework": "<PERSON><PERSON><PERSON>", "secondary_frameworks": ["django", "flask", "express"], "python_version": "3.11", "async_support": true, "type_hints": true, "validation": true}, "database_settings": {"primary_database": "postgresql", "secondary_databases": ["sqlite", "mysql", "mongodb"], "orm": "sqlalchemy", "migration_tool": "alembic", "connection_pooling": true, "backup_strategy": "automated"}, "api_configuration": {"rest_api": true, "graphql_support": false, "websocket_support": true, "rate_limiting": true, "cors_enabled": true, "authentication": "jwt", "authorization": "rbac"}, "security_settings": {"input_validation": true, "sql_injection_protection": true, "xss_protection": true, "csrf_protection": true, "encryption": "aes-256", "ssl_required": true, "security_headers": true}, "performance_settings": {"caching": "redis", "load_balancing": true, "compression": true, "optimization": true, "monitoring": true, "profiling": true}, "project_structure": {"src_directory": "src", "api_directory": "api", "models_directory": "models", "services_directory": "services", "utils_directory": "utils", "tests_directory": "tests", "migrations_directory": "migrations"}, "dependencies": {"core_dependencies": ["<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "pydantic", "sqlalchemy"], "database_dependencies": ["psycopg2-binary", "alembic", "asyncpg"], "security_dependencies": ["python-jose[cryptography]", "passlib[bcrypt]", "python-multipart"], "development_dependencies": ["pytest", "pytest-asyncio", "black", "flake8"]}, "testing": {"testing_framework": "pytest", "coverage_threshold": 85, "integration_tests": true, "unit_tests": true, "api_tests": true, "database_tests": true}, "deployment": {"containerization": "docker", "orchestration": "docker-compose", "ci_cd": "github-actions", "environment_management": true, "health_checks": true, "logging": "structured"}, "monitoring": {"application_monitoring": true, "database_monitoring": true, "performance_monitoring": true, "error_tracking": true, "alerting": true, "metrics_collection": true}, "logging": {"level": "INFO", "format": "json", "file_logging": true, "console_logging": true, "log_rotation": true, "structured_logging": true}, "development": {"debug_mode": false, "development_features": true, "test_mode": false, "hot_reload": true}}