import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Migration } from '@/types';

interface DatabaseMigration {
  id: string;
  project_id: string;
  name: string;
  description: string;
  sql_content: string;
  status: 'pending' | 'applied' | 'failed' | 'rolled_back';
  applied_at?: string;
  created_at: string;
  updated_at: string;
  version: string;
  checksum: string;
}

interface MigrationEditorProps {
  projectId: string;
  migrationId?: string;
  onSave?: (migration: DatabaseMigration) => void;
  onCancel?: () => void;
  onError?: (error: string) => void;
}

const MIGRATION_TEMPLATES = {
  'create_table': `-- Create a new table
CREATE TABLE table_name (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_table_name_created_at ON table_name(created_at);

-- Add RLS (Row Level Security)
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own data" ON table_name
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own data" ON table_name
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own data" ON table_name
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own data" ON table_name
    FOR DELETE USING (auth.uid() = user_id);`,

  'add_column': `-- Add a new column to an existing table
ALTER TABLE table_name
ADD COLUMN new_column_name TEXT;

-- Add a column with a default value
ALTER TABLE table_name
ADD COLUMN status TEXT DEFAULT 'active';

-- Add a column with a NOT NULL constraint
ALTER TABLE table_name
ADD COLUMN required_field TEXT NOT NULL DEFAULT 'default_value';`,

  'create_index': `-- Create a single column index
CREATE INDEX idx_table_name_column_name ON table_name(column_name);

-- Create a composite index
CREATE INDEX idx_table_name_col1_col2 ON table_name(column1, column2);

-- Create a unique index
CREATE UNIQUE INDEX idx_table_name_unique_field ON table_name(unique_field);

-- Create a partial index
CREATE INDEX idx_table_name_active ON table_name(column_name)
WHERE status = 'active';`,

  'add_foreign_key': `-- Add a foreign key constraint
ALTER TABLE child_table
ADD CONSTRAINT fk_child_parent
FOREIGN KEY (parent_id) REFERENCES parent_table(id);

-- Add a foreign key with cascade delete
ALTER TABLE child_table
ADD CONSTRAINT fk_child_parent_cascade
FOREIGN KEY (parent_id) REFERENCES parent_table(id)
ON DELETE CASCADE;`,

  'custom': `-- Write your custom SQL migration here
-- This template is for custom migrations that don't fit the standard patterns

-- Example: Complex data transformation
UPDATE users
SET email_verified = true
WHERE email_verified IS NULL;

-- Example: Data migration
INSERT INTO new_table (id, name, created_at)
SELECT id, name, created_at FROM old_table
WHERE status = 'active';`
};

export const MigrationEditor: React.FC<MigrationEditorProps> = ({
  projectId,
  migrationId,
  onSave,
  onCancel,
  onError
}) => {
  const [migration, setMigration] = useState<Partial<DatabaseMigration>>({
    project_id: projectId,
    name: '',
    description: '',
    sql_content: '',
    version: '1.0.0'
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isNewMigration, setIsNewMigration] = useState(!migrationId);

  useEffect(() => {
    if (migrationId) {
      loadMigration();
    }
  }, [migrationId]);

  const loadMigration = async () => {
    if (!migrationId) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/migrations/${migrationId}`);
      if (response.ok) {
        const data = await response.json();
        setMigration(data);
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to load migration');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load migration';
      onError?.(errorMessage);
      console.error('Error loading migration:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof DatabaseMigration, value: string) => {
    setMigration(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const applyTemplate = (templateKey: string) => {
    const template = MIGRATION_TEMPLATES[templateKey as keyof typeof MIGRATION_TEMPLATES];
    if (template) {
      setMigration(prev => ({
        ...prev,
        sql_content: template
      }));
      setSelectedTemplate(templateKey);
    }
  };

  const validateMigration = (): string[] => {
    const errors: string[] = [];

    if (!migration.name?.trim()) {
      errors.push('Migration name is required');
    }

    if (!migration.description?.trim()) {
      errors.push('Migration description is required');
    }

    if (!migration.sql_content?.trim()) {
      errors.push('SQL content is required');
    }

    if (!migration.version?.trim()) {
      errors.push('Version is required');
    }

    return errors;
  };

  const saveMigration = async () => {
    const errors = validateMigration();
    if (errors.length > 0) {
      onError?.(errors.join(', '));
      alert(`Validation errors: ${errors.join(', ')}`);
      return;
    }

    setSaving(true);
    try {
      const method = isNewMigration ? 'POST' : 'PUT';
      const url = isNewMigration
        ? `/api/v1/projects/${projectId}/migrations`
        : `/api/v1/projects/${projectId}/migrations/${migrationId}`;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(migration),
      });

      if (response.ok) {
        const savedMigration = await response.json();
        setMigration(savedMigration);
        setIsNewMigration(false);
        onSave?.(savedMigration);
        alert(`Migration "${savedMigration.name}" saved successfully!`);
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to save migration');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save migration';
      onError?.(errorMessage);
      alert(`Error saving migration: ${errorMessage}`);
    } finally {
      setSaving(false);
    }
  };

  const downloadMigration = () => {
    const content = `-- Migration: ${migration.name}
-- Description: ${migration.description}
-- Version: ${migration.version}
-- Created: ${new Date().toISOString()}

${migration.sql_content}`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${migration.name?.replace(/\s+/g, '_').toLowerCase()}.sql`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setMigration(prev => ({
          ...prev,
          sql_content: content
        }));
      };
      reader.readAsText(file);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">
          {isNewMigration ? 'Create New Migration' : 'Edit Migration'}
        </h3>
        {!isNewMigration && (
          <div className="text-sm text-gray-500">
            ID: {migrationId}
          </div>
        )}
      </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Migration Name *
            </label>
            <Input
              type="text"
              value={migration.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="create_users_table"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Version *
            </label>
            <Input
              type="text"
              value={migration.version || ''}
              onChange={(e) => handleInputChange('version', e.target.value)}
              placeholder="1.0.0"
              className="w-full"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description *
          </label>
          <textarea
            value={migration.description || ''}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe what this migration does..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
        </div>

        {/* Template Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            SQL Templates
          </label>
          <div className="flex flex-wrap gap-2">
            {Object.keys(MIGRATION_TEMPLATES).map(templateKey => (
              <Button
                key={templateKey}
                onClick={() => applyTemplate(templateKey)}
                variant={selectedTemplate === templateKey ? 'primary' : 'outline'}
                size="sm"
              >
                {templateKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Button>
            ))}
          </div>
        </div>

        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload SQL File
          </label>
          <input
            type="file"
            accept=".sql"
            onChange={handleFileUpload}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {/* SQL Editor */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium text-gray-700">
              SQL Content *
            </label>
            <Button
              onClick={downloadMigration}
              variant="outline"
              size="sm"
              disabled={!migration.sql_content}
            >
              Download SQL
            </Button>
          </div>
          <textarea
            value={migration.sql_content || ''}
            onChange={(e) => handleInputChange('sql_content', e.target.value)}
            placeholder="-- Write your SQL migration here..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            rows={15}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <Button
            onClick={saveMigration}
            disabled={saving}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {saving ? 'Saving...' : isNewMigration ? 'Create Migration' : 'Update Migration'}
          </Button>

          {onCancel && (
            <Button
              onClick={onCancel}
              variant="outline"
              disabled={saving}
            >
              Cancel
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
