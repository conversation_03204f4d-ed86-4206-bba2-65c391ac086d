import React, { useState } from 'react';
import { <PERSON>rk<PERSON>, Loader2, Check, X } from 'lucide-react';
import toast from 'react-hot-toast';
import { promptEnhancer } from '@/services/PromptEnhancer';

interface EnhanceButtonProps {
  onEnhance: (enhancedPrompt: string) => void;
  disabled?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showFeedback?: boolean;
}

export const EnhanceButton: React.FC<EnhanceButtonProps> = ({
  onEnhance,
  disabled = false,
  className = '',
  size = 'md',
  showFeedback = true
}) => {
  const [loading, setLoading] = useState(false);
  const [enhanced, setEnhanced] = useState(false);
  const [error, setError] = useState(false);

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const handleEnhance = async () => {
    if (loading || disabled) return;

    setLoading(true);
    setError(false);
    setEnhanced(false);

    try {
      // Get current prompt from a global state or context
      // For now, we'll use a placeholder - in real implementation, this would come from the chat input
      const currentPrompt = getCurrentPrompt();

      if (!currentPrompt.trim()) {
        toast.error('No prompt to enhance');
        return;
      }

      // Auto-detect enhancement mode
      const mode = promptEnhancer.detectEnhancementMode(currentPrompt);

      // Enhance the prompt
      const result = await promptEnhancer.enhancePrompt(currentPrompt, mode);

      if (result.confidence > 0.3) {
        onEnhance(result.enhanced);
        setEnhanced(true);

        if (showFeedback) {
          toast.success('Prompt enhanced successfully!', {
            duration: 3000,
            icon: '✨'
          });
        }

        // Reset enhanced state after a delay
        setTimeout(() => setEnhanced(false), 2000);
      } else {
        toast.error('Enhancement confidence too low, using original prompt');
        onEnhance(currentPrompt);
      }
    } catch (error) {
      toast.error('Enhancement error.');
      setError(true);

      if (showFeedback) {
        toast.error('Couldn\'t enhance prompt. Try again.', {
          duration: 4000
        });
      }

      // Reset error state after a delay
      setTimeout(() => setError(false), 2000);
    } finally {
      setLoading(false);
    }
  };

  // Placeholder function - in real implementation, this would get the current prompt from context
  const getCurrentPrompt = (): string => {
    // This would typically come from a global state or context
    // For now, return a placeholder
    return '';
  };

  const getButtonContent = () => {
    if (loading) {
      return (
        <>
          <Loader2 className={`${iconSizes[size]} animate-spin`} />
          <span>Enhancing...</span>
        </>
      );
    }

    if (enhanced) {
      return (
        <>
          <Check className={`${iconSizes[size]} text-green-500`} />
          <span>Enhanced</span>
        </>
      );
    }

    if (error) {
      return (
        <>
          <X className={`${iconSizes[size]} text-red-500`} />
          <span>Failed</span>
        </>
      );
    }

    return (
      <>
        <Sparkles className={iconSizes[size]} />
        <span>Enhance</span>
      </>
    );
  };

  const getButtonClasses = () => {
    let baseClasses = `flex items-center gap-2 rounded-md font-medium transition-all duration-200 ${sizeClasses[size]} ${className}`;

    if (loading) {
      baseClasses += ' bg-blue-500 text-white cursor-not-allowed opacity-75';
    } else if (enhanced) {
      baseClasses += ' bg-green-500 text-white cursor-not-allowed';
    } else if (error) {
      baseClasses += ' bg-red-500 text-white cursor-not-allowed';
    } else if (disabled) {
      baseClasses += ' bg-gray-300 text-gray-500 cursor-not-allowed';
    } else {
      baseClasses += ' bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2';
    }

    return baseClasses;
  };

  return (
    <button
      onClick={handleEnhance}
      disabled={disabled || loading}
      className={getButtonClasses()}
      title="Enhance prompt with AI assistance"
    >
      {getButtonContent()}
    </button>
  );
};

// Enhanced version with prompt input
interface EnhanceButtonWithPromptProps extends Omit<EnhanceButtonProps, 'onEnhance'> {
  prompt: string;
  onEnhance: (enhancedPrompt: string) => void;
}

export const EnhanceButtonWithPrompt: React.FC<EnhanceButtonWithPromptProps> = ({
  prompt,
  onEnhance,
  ...props
}) => {
  const [loading, setLoading] = useState(false);
  const [enhanced, setEnhanced] = useState(false);
  const [error, setError] = useState(false);

  const handleEnhance = async () => {
    if (loading || props.disabled || !prompt.trim()) return;

    setLoading(true);
    setError(false);
    setEnhanced(false);

    try {
      // Auto-detect enhancement mode
      const mode = promptEnhancer.detectEnhancementMode(prompt);

      // Enhance the prompt
      const result = await promptEnhancer.enhancePrompt(prompt, mode);

      if (result.confidence > 0.3) {
        onEnhance(result.enhanced);
        setEnhanced(true);

        if (props.showFeedback !== false) {
          toast.success('Prompt enhanced successfully!', {
            duration: 3000,
            icon: '✨'
          });
        }

        // Reset enhanced state after a delay
        setTimeout(() => setEnhanced(false), 2000);
      } else {
        toast.error('Enhancement confidence too low, using original prompt');
        onEnhance(prompt);
      }
    } catch (error) {
      toast.error('Enhancement error.');
      setError(true);

      if (props.showFeedback !== false) {
        toast.error('Couldn\'t enhance prompt. Try again.', {
          duration: 4000
        });
      }

      // Reset error state after a delay
      setTimeout(() => setError(false), 2000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <EnhanceButton
      {...props}
      onEnhance={handleEnhance}
      disabled={props.disabled || loading || !prompt.trim()}
    />
  );
};
