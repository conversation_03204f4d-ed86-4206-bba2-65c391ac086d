#!/usr/bin/env python3
"""
JSX AST Support for Code Generation

This module provides JSX-specific AST manipulation and code generation support.
"""

import ast
import logging
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

from core.code_generation.language_support.base_language_support import (
    ASTNodeInfo,
    LanguageType,
)
from core.code_generation.language_support.javascript_support import (
    JavaScriptASTSupport,
    JavaScriptClassInfo,
    JavaScriptFunctionInfo,
)

logger = logging.getLogger(__name__)


@dataclass
class JSXComponentInfo:
    """Information about a JSX component"""

    name: str
    props: List[str] = None
    children: List[str] = None
    is_functional: bool = True
    is_export: bool = False
    hooks: List[str] = None
    styled_components: List[str] = None


class JSXASTSupport(JavaScriptASTSupport):
    """
    JSX-specific AST support implementation.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.language = LanguageType.JSX

    async def create_base_ast(self) -> ast.AST:
        """Create a base JSX AST"""
        # Create a module with React import
        module = ast.Module(
            body=[
                ast.ImportFrom(
                    module="react",
                    names=[ast.alias(name="React", asname=None)],
                    level=0,
                )
            ],
            type_ignores=[],
        )
        return module

    async def add_react_component(
        self,
        ast_tree: ast.AST,
        component_name: str,
        props: List[str] = None,
        children: List[str] = None,
    ) -> ast.AST:
        """Add React component to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create component function
        props_param = "props" if props else "_props"
        component_body = [
            ast.Return(
                value=ast.Call(
                    func=ast.Name(id="React.createElement"),
                    args=[
                        ast.Constant(value=component_name),
                        ast.Name(id=props_param),
                        *[ast.Constant(value=child) for child in (children or [])],
                    ],
                    keywords=[],
                )
            )
        ]

        # Create function definition
        component_def = ast.FunctionDef(
            name=component_name,
            args=ast.arguments(
                posonlyargs=[],
                args=[ast.arg(arg=props_param)],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=component_body,
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(component_def)
        return ast_tree

    async def add_hook(
        self,
        ast_tree: ast.AST,
        hook_name: str,
        hook_type: str,
        parameters: List[str] = None,
    ) -> ast.AST:
        """Add React hook to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Import hook if needed - convert to Python syntax
        await self.add_import(ast_tree, f"from react import {hook_name}")

        # Create hook usage
        hook_call = ast.Call(
            func=ast.Name(id=hook_name),
            args=[ast.Constant(value=param) for param in (parameters or [])],
            keywords=[],
        )

        # Create variable assignment
        hook_var = ast.Assign(
            targets=[ast.Name(id=f"{hook_name.lower()}_result")], value=hook_call
        )

        ast_tree.body.append(hook_var)
        return ast_tree

    async def add_styled_component(
        self, ast_tree: ast.AST, component_name: str, styles: str
    ) -> ast.AST:
        """Add styled component to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Import styled-components if needed - convert to Python syntax
        await self.add_import(ast_tree, "from styled_components import styled")

        # Create styled component
        styled_component = ast.Assign(
            targets=[ast.Name(id=component_name)],
            value=ast.Call(
                func=ast.Attribute(value=ast.Name(id="styled"), attr="div"),
                args=[],
                keywords=[ast.keyword(arg="css", value=ast.Constant(value=styles))],
            ),
        )

        ast_tree.body.append(styled_component)
        return ast_tree

    async def add_jsx_element(
        self,
        ast_tree: ast.AST,
        element_name: str,
        attributes: Dict[str, str] = None,
        children: List[str] = None,
    ) -> ast.AST:
        """Add JSX element to AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create JSX element
        element = ast.Call(
            func=ast.Name(id=element_name),
            args=[ast.Constant(value=child) for child in (children or [])],
            keywords=[
                ast.keyword(arg=key, value=ast.Constant(value=value))
                for key, value in (attributes or {}).items()
            ],
        )

        ast_tree.body.append(element)
        return ast_tree

    async def add_event_handler(
        self, ast_tree: ast.AST, event_name: str, handler_name: str, handler_body: str
    ) -> ast.AST:
        """Add event handler to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create handler function
        handler_def = ast.FunctionDef(
            name=handler_name,
            args=ast.arguments(
                posonlyargs=[],
                args=[ast.arg(arg="event")],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=[ast.Expr(value=ast.parse(handler_body, mode="eval"))],
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(handler_def)
        return ast_tree

    async def add_props_interface(
        self, ast_tree: ast.AST, interface_name: str, properties: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add TypeScript interface for props (for TSX compatibility)"""
        # This is a simplified implementation
        # In practice, you'd need proper TypeScript AST support
        return ast_tree

    async def add_state_management(
        self, ast_tree: ast.AST, state_name: str, initial_value: str
    ) -> ast.AST:
        """Add React state management to JSX AST"""
        return await self.add_hook(ast_tree, "useState", "state", [initial_value])

    async def add_effect(
        self,
        ast_tree: ast.AST,
        effect_name: str,
        effect_body: str,
        dependencies: List[str] = None,
    ) -> ast.AST:
        """Add React useEffect to JSX AST"""
        return await self.add_hook(ast_tree, "useEffect", "effect", dependencies or [])

    async def add_context(
        self, ast_tree: ast.AST, context_name: str, default_value: str
    ) -> ast.AST:
        """Add React context to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Import createContext
        await self.add_import(ast_tree, "import { createContext } from 'react'")

        # Create context
        context_def = ast.Assign(
            targets=[ast.Name(id=context_name)],
            value=ast.Call(
                func=ast.Name(id="createContext"),
                args=[ast.Constant(value=default_value)],
                keywords=[],
            ),
        )

        ast_tree.body.append(context_def)
        return ast_tree

    async def add_provider(
        self, ast_tree: ast.AST, provider_name: str, context_name: str, value: str
    ) -> ast.AST:
        """Add React context provider to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create provider component
        provider_body = [
            ast.Return(
                value=ast.Call(
                    func=ast.Attribute(
                        value=ast.Name(id=context_name), attr="Provider"
                    ),
                    args=[],
                    keywords=[ast.keyword(arg="value", value=ast.Name(id=value))],
                )
            )
        ]

        provider_def = ast.FunctionDef(
            name=provider_name,
            args=ast.arguments(
                posonlyargs=[],
                args=[ast.arg(arg="children")],
                kwonlyargs=[],
                defaults=[],
                kw_defaults=[],
            ),
            body=provider_body,
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(provider_def)
        return ast_tree

    async def add_custom_hook(
        self, ast_tree: ast.AST, hook_name: str, parameters: List[str], body: str
    ) -> ast.AST:
        """Add custom React hook to JSX AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse hook body
        try:
            body_ast = ast.parse(body)
            if isinstance(body_ast, ast.Module):
                hook_body = body_ast.body
            else:
                hook_body = [body_ast]
        except SyntaxError:
            hook_body = [ast.Pass()]

        # Create hook arguments
        args = []
        for param in parameters:
            args.append(ast.arg(arg=param.strip()))

        # Create hook definition
        hook_def = ast.FunctionDef(
            name=hook_name,
            args=ast.arguments(
                posonlyargs=[], args=args, kwonlyargs=[], defaults=[], kw_defaults=[]
            ),
            body=hook_body,
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(hook_def)
        return ast_tree

    def _convert_to_jsx(self, javascript_code: str) -> str:
        """Convert JavaScript code to JSX"""
        # This is a simplified conversion
        # In practice, you'd need a more sophisticated converter

        jsx_code = javascript_code

        # Convert React.createElement to JSX syntax
        jsx_code = re.sub(
            r"React\.createElement\((\w+), (\w+), (.*)\)",
            r"<\1 {...\2}>\3</\1>",
            jsx_code,
        )

        # Convert function components to JSX
        jsx_code = re.sub(
            r"function (\w+)\(props\) {", r"function \1(props) {", jsx_code
        )

        return jsx_code

    async def ast_to_code(self, ast_tree: ast.AST) -> str:
        """Convert JSX AST back to code string"""
        try:
            # Use parent method first
            code = await super().ast_to_code(ast_tree)
            # Convert to JSX syntax
            return self._convert_to_jsx(code)
        except Exception as e:
            logger.error(f"Failed to convert AST to JSX code: {e}")
            raise
