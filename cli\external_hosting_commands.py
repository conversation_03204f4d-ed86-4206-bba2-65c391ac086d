"""
External Hosting CLI Commands
Provides command-line interface for external hosting exports and deployments.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from core.external_hosting_manager import (
    ExportConfig,
    ExternalHostingManager,
    HostingProvider,
)

logger = logging.getLogger(__name__)


class ExternalHostingCommands:
    """CLI commands for external hosting management"""

    def __init__(self, agent):
        self.agent = agent
        self.hosting_manager = ExternalHostingManager()

    async def export_site(
        self, site_name: str, provider: str, **kwargs
    ) -> Dict[str, Any]:
        """Export a site for external hosting"""
        try:
            # Validate provider
            try:
                hosting_provider = HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Create export configuration
            config = ExportConfig(
                site_name=site_name,
                provider=hosting_provider,
                optimize=kwargs.get("optimize", True),
                minify=kwargs.get("minify", True),
                compress=kwargs.get("compress", True),
                custom_domain=kwargs.get("custom_domain"),
                environment=kwargs.get("environment", "production"),
            )

            # Export the site
            result = await self.hosting_manager.export_site(
                site_name, hosting_provider, config
            )

            if result.success:
                logger.info(f"✅ Site {site_name} exported to {provider}")
                return {
                    "success": True,
                    "message": f"Site {site_name} exported to {provider}",
                    "export_path": result.export_path,
                    "deployment_url": result.deployment_url,
                    "provider": provider,
                    "build_artifacts": result.build_artifacts,
                    "export_time": (
                        result.export_time.isoformat() if result.export_time else None
                    ),
                }
            else:
                logger.error(f"❌ Failed to export site: {result.error_message}")
                return {"success": False, "error": result.error_message}

        except Exception as e:
            logger.error(f"Error exporting site: {e}")
            return {"success": False, "error": str(e)}

    async def export_to_netlify(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to Netlify"""
        return await self.export_site(site_name, "netlify", **kwargs)

    async def export_to_github_pages(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to GitHub Pages"""
        return await self.export_site(site_name, "github_pages", **kwargs)

    async def export_to_vercel(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site to Vercel"""
        return await self.export_site(site_name, "vercel", **kwargs)

    async def export_static(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Export site as static files"""
        return await self.export_site(site_name, "static_export", **kwargs)

    async def list_exports(
        self, site_name: Optional[str] = None, **kwargs
    ) -> Dict[str, Any]:
        """List available exports"""
        try:
            exports = await self.hosting_manager.list_exports(site_name)

            if exports:
                logger.info(f"✅ Found {len(exports)} exports")
                return {"success": True, "exports": exports, "count": len(exports)}
            else:
                logger.info("ℹ️ No exports found")
                return {
                    "success": True,
                    "exports": [],
                    "count": 0,
                    "message": "No exports found",
                }

        except Exception as e:
            logger.error(f"Error listing exports: {e}")
            return {"success": False, "error": str(e)}

    async def delete_export(self, export_name: str, **kwargs) -> Dict[str, Any]:
        """Delete an export"""
        try:
            success = await self.hosting_manager.delete_export(export_name)

            if success:
                logger.info(f"✅ Deleted export: {export_name}")
                return {
                    "success": True,
                    "message": f"Export {export_name} deleted successfully",
                }
            else:
                logger.error(f"❌ Failed to delete export: {export_name}")
                return {
                    "success": False,
                    "error": f"Failed to delete export: {export_name}",
                }

        except Exception as e:
            logger.error(f"Error deleting export: {e}")
            return {"success": False, "error": str(e)}

    async def configure_provider(self, provider: str, **kwargs) -> Dict[str, Any]:
        """Configure a hosting provider"""
        try:
            # Validate provider
            try:
                hosting_provider = HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Load current configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)
            else:
                config = {}

            # Update provider configuration
            provider_key = provider.lower()
            if provider_key not in config:
                config[provider_key] = {}

            # Update with provided parameters
            for key, value in kwargs.items():
                if value is not None:  # Only update non-None values
                    config[provider_key][key] = value

            # Enable the provider
            config[provider_key]["enabled"] = True

            # Save configuration
            with open(config_file, "w") as f:
                json.dump(config, f, indent=2)

            logger.info(f"✅ Configured {provider} hosting provider")
            return {
                "success": True,
                "message": f"Provider {provider} configured successfully",
                "provider": provider,
                "enabled": True,
            }

        except Exception as e:
            logger.error(f"Error configuring provider: {e}")
            return {"success": False, "error": str(e)}

    async def get_provider_status(self, provider: str, **kwargs) -> Dict[str, Any]:
        """Get status of a hosting provider"""
        try:
            # Validate provider
            try:
                hosting_provider = HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                provider_config = config.get(provider.lower(), {})

                return {
                    "success": True,
                    "provider": provider,
                    "enabled": provider_config.get("enabled", False),
                    "configured": bool(provider_config),
                    "configuration": provider_config,
                }
            else:
                return {
                    "success": True,
                    "provider": provider,
                    "enabled": False,
                    "configured": False,
                    "configuration": {},
                }

        except Exception as e:
            logger.error(f"Error getting provider status: {e}")
            return {"success": False, "error": str(e)}

    async def list_providers(self, **kwargs) -> Dict[str, Any]:
        """List all available hosting providers"""
        try:
            providers = []
            for provider in HostingProvider:
                status = await self.get_provider_status(provider.value)
                if status["success"]:
                    providers.append(
                        {
                            "name": provider.value,
                            "enabled": status["enabled"],
                            "configured": status["configured"],
                        }
                    )

            return {"success": True, "providers": providers, "count": len(providers)}

        except Exception as e:
            logger.error(f"Error listing providers: {e}")
            return {"success": False, "error": str(e)}

    async def test_provider_connection(self, provider: str, **kwargs) -> Dict[str, Any]:
        """Test connection to a hosting provider"""
        try:
            # Validate provider
            try:
                hosting_provider = HostingProvider(provider.lower())
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid provider: {provider}. Supported providers: {[p.value for p in HostingProvider]}",
                }

            # Get provider status
            status = await self.get_provider_status(provider)
            if not status["success"]:
                return status

            if not status["enabled"]:
                return {
                    "success": False,
                    "error": f"Provider {provider} is not enabled",
                }

            # Test connection based on provider
            if provider.lower() == "netlify":
                return await self._test_netlify_connection()
            elif provider.lower() == "github_pages":
                return await self._test_github_connection()
            elif provider.lower() == "vercel":
                return await self._test_vercel_connection()
            else:
                return {
                    "success": True,
                    "message": f"Provider {provider} connection test not implemented",
                    "provider": provider,
                }

        except Exception as e:
            logger.error(f"Error testing provider connection: {e}")
            return {"success": False, "error": str(e)}

    async def _test_netlify_connection(self) -> Dict[str, Any]:
        """Test Netlify connection"""
        try:
            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                netlify_config = config.get("netlify", {})
                api_token = netlify_config.get("api_token")

                if not api_token:
                    return {
                        "success": False,
                        "error": "Netlify API token not configured",
                    }

                # Test API connection
                import requests

                headers = {"Authorization": f"Bearer {api_token}"}
                response = requests.get(
                    "https://api.netlify.com/api/v1/user", headers=headers
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "Netlify connection successful",
                        "provider": "netlify",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Netlify API error: {response.status_code}",
                    }
            else:
                return {"success": False, "error": "Configuration file not found"}

        except Exception as e:
            return {
                "success": False,
                "error": f"Netlify connection test failed: {str(e)}",
            }

    async def _test_github_connection(self) -> Dict[str, Any]:
        """Test GitHub connection"""
        try:
            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                github_config = config.get("github_pages", {})
                token = github_config.get("token")
                repository = github_config.get("repository")

                if not token or not repository:
                    return {
                        "success": False,
                        "error": "GitHub token or repository not configured",
                    }

                # Test API connection
                import requests

                headers = {"Authorization": f"token {token}"}
                response = requests.get(
                    f"https://api.github.com/repos/{repository}", headers=headers
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "GitHub connection successful",
                        "provider": "github_pages",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"GitHub API error: {response.status_code}",
                    }
            else:
                return {"success": False, "error": "Configuration file not found"}

        except Exception as e:
            return {
                "success": False,
                "error": f"GitHub connection test failed: {str(e)}",
            }

    async def _test_vercel_connection(self) -> Dict[str, Any]:
        """Test Vercel connection"""
        try:
            # Load configuration
            config_file = Path("config/external_hosting_config.json")
            if config_file.exists():
                import json

                with open(config_file, "r") as f:
                    config = json.load(f)

                vercel_config = config.get("vercel", {})
                api_token = vercel_config.get("api_token")

                if not api_token:
                    return {
                        "success": False,
                        "error": "Vercel API token not configured",
                    }

                # Test API connection
                import requests

                headers = {"Authorization": f"Bearer {api_token}"}
                response = requests.get(
                    "https://api.vercel.com/v1/user", headers=headers
                )

                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "Vercel connection successful",
                        "provider": "vercel",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Vercel API error: {response.status_code}",
                    }
            else:
                return {"success": False, "error": "Configuration file not found"}

        except Exception as e:
            return {
                "success": False,
                "error": f"Vercel connection test failed: {str(e)}",
            }
