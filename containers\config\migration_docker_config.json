{"migration_runner": {"service": {"name": "migration-runner", "container_name": "ai-coding-migration-runner", "port": 8086, "restart_policy": "unless-stopped", "health_check": {"interval": "30s", "timeout": "30s", "retries": 3, "start_period": "40s", "test": ["CMD", "curl", "-f", "http://localhost:8086/health"]}}, "resources": {"limits": {"cpus": "0.5", "memory": "1G"}, "reservations": {"cpus": "0.25", "memory": "512M"}}, "volumes": {"database": "./database:/app/database", "migrations": "./migrations:/app/migrations", "logs": "./logs:/app/logs", "backups": "./backups:/app/backups", "config": "./config:/app/config"}, "environment": {"PYTHONPATH": "/app", "MIGRATION_ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "DATABASE_URL": "******************************************************/ai_coding_agent", "MIGRATION_BACKUP_ENABLED": "true", "MIGRATION_ROLLBACK_ENABLED": "true", "MIGRATION_VALIDATION_ENABLED": "true", "MIGRATION_TIMEOUT": "300", "MIGRATION_RETRY_ATTEMPTS": "3", "BACKUP_RETENTION_DAYS": "30", "BACKUP_COMPRESSION": "true"}, "networks": {"primary": "ai-coding-network", "dependencies": ["db", "api"]}, "security": {"non_root_user": "migrationrunner", "read_only_root_filesystem": false, "capabilities_drop": ["ALL"], "security_opt": ["no-new-privileges"]}, "monitoring": {"prometheus_metrics": true, "log_aggregation": true, "performance_tracking": true, "error_reporting": true, "metrics_endpoint": "/metrics", "health_endpoint": "/health"}, "backup": {"enabled": true, "automatic_backup": true, "backup_interval": "24h", "backup_retention": "30d", "backup_compression": true, "backup_encryption": false, "backup_validation": true, "pre_migration_backup": true, "post_migration_backup": false}, "rollback": {"enabled": true, "automatic_rollback": false, "rollback_validation": true, "rollback_timeout": "300", "rollback_retry_attempts": "3", "rollback_safety_checks": true}, "validation": {"enabled": true, "syntax_validation": true, "dependency_validation": true, "conflict_detection": true, "pre_migration_validation": true, "post_migration_validation": true}, "performance": {"connection_pool_size": 5, "query_timeout": 60, "migration_timeout": 300, "batch_size": 1000, "parallel_migrations": false, "optimization_enabled": true}, "logging": {"level": "INFO", "format": "json", "file_rotation": true, "max_file_size": "10MB", "backup_count": 5, "structured_logging": true, "log_to_stdout": true, "log_to_file": true}, "api": {"endpoints": {"status": "/api/migration/status", "summary": "/api/migration/summary", "list": "/api/migration/list", "run": "/api/migration/run", "rollback": "/api/migration/rollback", "validate": "/api/migration/validate", "backup_create": "/api/migration/backup/create", "backup_restore": "/api/migration/backup/restore", "backup_list": "/api/migration/backup/list", "metrics": "/api/migration/metrics", "export": "/api/migration/export", "health": "/api/migration/health"}, "rate_limiting": {"enabled": true, "requests_per_minute": 60, "burst_limit": 10}, "authentication": {"enabled": false, "method": "none"}, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["*"]}}, "cli": {"commands": {"status": "migration_status", "summary": "migration_summary", "list": "list_migrations", "run": "run_migration", "rollback": "rollback_migration", "validate": "validate_migration", "backup_create": "create_backup", "backup_restore": "restore_backup", "backup_list": "list_backups", "metrics": "migration_metrics", "export": "export_migration_report"}, "output_format": "json", "verbose_mode": false, "interactive_mode": false}, "deployment": {"strategy": "rolling", "update_config": {"parallelism": 1, "delay": "10s", "failure_action": "rollback", "monitor": "30s", "max_failure_ratio": 0.1}, "rollback_config": {"parallelism": 1, "delay": "10s", "failure_action": "pause", "monitor": "30s", "max_failure_ratio": 0.1}, "restart_policy": {"condition": "on-failure", "delay": "5s", "max_attempts": 3, "window": "120s"}}, "maintenance": {"scheduled_maintenance": true, "maintenance_window": "02:00-04:00", "maintenance_timezone": "UTC", "backup_before_maintenance": true, "validation_after_maintenance": true}}, "database": {"connection": {"host": "db", "port": 5432, "database": "ai_coding_agent", "username": "ai_coding_user", "password": "ai_coding_password", "ssl_mode": "prefer", "connection_timeout": 30, "command_timeout": 60}, "pool": {"min_size": 1, "max_size": 10, "max_queries": 50000, "max_inactive_connection_lifetime": 300}, "migrations": {"table_name": "migration_history", "lock_timeout": 30, "transaction_timeout": 300, "batch_size": 1000, "parallel_execution": false}}, "monitoring": {"prometheus": {"enabled": true, "port": 8086, "metrics_path": "/metrics", "scrape_interval": "15s"}, "grafana": {"enabled": true, "dashboard_uid": "migration-runner", "panel_refresh": "30s"}, "alerts": {"migration_failure": {"enabled": true, "severity": "critical", "notification_channels": ["email", "slack"]}, "backup_failure": {"enabled": true, "severity": "warning", "notification_channels": ["email"]}, "rollback_triggered": {"enabled": true, "severity": "critical", "notification_channels": ["email", "slack", "<PERSON><PERSON><PERSON><PERSON>"]}, "validation_failure": {"enabled": true, "severity": "warning", "notification_channels": ["email"]}}}, "security": {"file_permissions": {"migrations": "644", "backups": "600", "logs": "644", "config": "600"}, "network": {"internal_only": true, "allowed_hosts": ["db", "api"], "firewall_rules": []}, "encryption": {"backup_encryption": false, "log_encryption": false, "config_encryption": false}}}