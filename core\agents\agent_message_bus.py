import asyncio
import json
import logging
import threading
import time
import uuid
import weakref
from collections import defaultdict, deque
from dataclasses import asdict, dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Union

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of messages in the agent communication system."""

    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    ASSISTANCE_REQUEST = "assistance_request"
    ASSISTANCE_RESPONSE = "assistance_response"
    CONTEXT_UPDATE = "context_update"
    STATUS_UPDATE = "status_update"
    ERROR_NOTIFICATION = "error_notification"
    BROADCAST = "broadcast"
    DIRECT_MESSAGE = "direct_message"
    SYSTEM_NOTIFICATION = "system_notification"


class MessagePriority(Enum):
    """Message priority levels."""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class MessageStatus(Enum):
    """Message delivery status."""

    PENDING = "pending"
    DELIVERED = "delivered"
    ACKNOWLEDGED = "acknowledged"
    FAILED = "failed"
    EXPIRED = "expired"


@dataclass
class AgentMessage:
    """Represents a message between agents."""

    id: str
    message_type: MessageType
    sender_id: str
    recipient_id: Optional[str]  # None for broadcast messages
    subject: str
    content: Dict[str, Any]
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    expires_at: Optional[float] = None
    requires_acknowledgment: bool = False
    correlation_id: Optional[str] = None  # For request-response patterns
    metadata: Dict[str, Any] = field(default_factory=dict)
    status: MessageStatus = MessageStatus.PENDING
    delivery_attempts: int = 0
    max_delivery_attempts: int = 3

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return {
            "id": self.id,
            "message_type": self.message_type.value,
            "sender_id": self.sender_id,
            "recipient_id": self.recipient_id,
            "subject": self.subject,
            "content": self.content,
            "priority": self.priority.value,
            "timestamp": self.timestamp,
            "expires_at": self.expires_at,
            "requires_acknowledgment": self.requires_acknowledgment,
            "correlation_id": self.correlation_id,
            "metadata": self.metadata,
            "status": self.status.value,
            "delivery_attempts": self.delivery_attempts,
            "max_delivery_attempts": self.max_delivery_attempts,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentMessage":
        """Create message from dictionary."""
        return cls(
            id=data["id"],
            message_type=MessageType(data["message_type"]),
            sender_id=data["sender_id"],
            recipient_id=data.get("recipient_id"),
            subject=data["subject"],
            content=data["content"],
            priority=MessagePriority(
                data.get("priority", MessagePriority.NORMAL.value)
            ),
            timestamp=data.get("timestamp", time.time()),
            expires_at=data.get("expires_at"),
            requires_acknowledgment=data.get("requires_acknowledgment", False),
            correlation_id=data.get("correlation_id"),
            metadata=data.get("metadata", {}),
            status=MessageStatus(data.get("status", MessageStatus.PENDING.value)),
            delivery_attempts=data.get("delivery_attempts", 0),
            max_delivery_attempts=data.get("max_delivery_attempts", 3),
        )

    def is_expired(self) -> bool:
        """Check if message has expired."""
        return self.expires_at is not None and time.time() > self.expires_at

    def should_retry(self) -> bool:
        """Check if message should be retried."""
        return (
            self.status == MessageStatus.FAILED
            and self.delivery_attempts < self.max_delivery_attempts
            and not self.is_expired()
        )


@dataclass
class MessageFilter:
    """Filter for message subscription."""

    message_types: Optional[List[MessageType]] = None
    sender_ids: Optional[List[str]] = None
    subjects: Optional[List[str]] = None
    priorities: Optional[List[MessagePriority]] = None

    def matches(self, message: AgentMessage) -> bool:
        """Check if message matches this filter."""
        if self.message_types and message.message_type not in self.message_types:
            return False
        if self.sender_ids and message.sender_id not in self.sender_ids:
            return False
        if self.subjects and message.subject not in self.subjects:
            return False
        if self.priorities and message.priority not in self.priorities:
            return False
        return True


class MessageSubscription:
    """Represents a message subscription."""

    def __init__(
        self,
        subscription_id: str,
        agent_id: str,
        callback: Callable[[AgentMessage], None],
        message_filter: Optional[MessageFilter] = None,
        is_async: bool = False,
    ):
        self.subscription_id = subscription_id
        self.agent_id = agent_id
        self.callback = callback
        self.message_filter = message_filter or MessageFilter()
        self.is_async = is_async
        self.created_at = time.time()
        self.message_count = 0
        self.last_message_at: Optional[float] = None

    def matches(self, message: AgentMessage) -> bool:
        """Check if message matches this subscription."""
        return self.message_filter.matches(message)

    async def deliver(self, message: AgentMessage) -> bool:
        """Deliver message to subscriber."""
        try:
            if self.is_async:
                if asyncio.iscoroutinefunction(self.callback):
                    await self.callback(message)
                else:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.callback, message
                    )
            else:
                self.callback(message)

            self.message_count += 1
            self.last_message_at = time.time()
            return True
        except Exception as e:
            logger.error(
                f"Error delivering message to subscription {self.subscription_id}: {e}"
            )
            return False


class AgentMessageBus:
    """Real-time message bus for agent communication."""

    def __init__(self, max_message_history: int = 10000, cleanup_interval: int = 300):
        self.max_message_history = max_message_history
        self.cleanup_interval = cleanup_interval

        # Message storage
        self.message_history: deque = deque(maxlen=max_message_history)
        self.pending_messages: Dict[str, AgentMessage] = {}
        self.pending_acknowledgments: Dict[str, AgentMessage] = {}

        # Subscriptions
        self.subscriptions: Dict[str, MessageSubscription] = {}
        self.agent_subscriptions: Dict[str, List[str]] = defaultdict(list)

        # Message routing
        self.message_queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.delivery_stats: Dict[str, Dict[str, Any]] = defaultdict(
            lambda: {"sent": 0, "delivered": 0, "failed": 0, "acknowledged": 0}
        )

        # Threading and async support
        self._lock = threading.RLock()
        self._running = False
        self._cleanup_task: Optional[asyncio.Task] = None
        self._delivery_tasks: Set[asyncio.Task] = set()

        # WebSocket integration
        self.websocket_clients: Dict[str, Any] = {}
        self.websocket_callbacks: List[Callable[[AgentMessage], None]] = []

        logger.info("AgentMessageBus initialized")

    def start(self) -> None:
        """Start the message bus."""
        with self._lock:
            if self._running:
                return

            self._running = True

            # Start cleanup task
            if asyncio.get_event_loop().is_running():
                self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        logger.info("AgentMessageBus started")

    def stop(self) -> None:
        """Stop the message bus."""
        with self._lock:
            if not self._running:
                return

            self._running = False

            # Cancel cleanup task
            if self._cleanup_task and not self._cleanup_task.done():
                self._cleanup_task.cancel()

            # Cancel delivery tasks
            for task in list(self._delivery_tasks):
                if not task.done():
                    task.cancel()
            self._delivery_tasks.clear()

        logger.info("AgentMessageBus stopped")

    def subscribe(
        self,
        agent_id: str,
        callback: Callable[[AgentMessage], None],
        message_filter: Optional[MessageFilter] = None,
        is_async: bool = False,
    ) -> str:
        """Subscribe to messages."""
        subscription_id = str(uuid.uuid4())

        subscription = MessageSubscription(
            subscription_id=subscription_id,
            agent_id=agent_id,
            callback=callback,
            message_filter=message_filter,
            is_async=is_async,
        )

        with self._lock:
            self.subscriptions[subscription_id] = subscription
            self.agent_subscriptions[agent_id].append(subscription_id)

        logger.info(f"Agent {agent_id} subscribed with ID {subscription_id}")
        return subscription_id

    def unsubscribe(self, subscription_id: str) -> bool:
        """Unsubscribe from messages."""
        with self._lock:
            if subscription_id not in self.subscriptions:
                return False

            subscription = self.subscriptions[subscription_id]
            agent_id = subscription.agent_id

            del self.subscriptions[subscription_id]
            if subscription_id in self.agent_subscriptions[agent_id]:
                self.agent_subscriptions[agent_id].remove(subscription_id)

            if not self.agent_subscriptions[agent_id]:
                del self.agent_subscriptions[agent_id]

        logger.info(f"Unsubscribed {subscription_id}")
        return True

    def unsubscribe_agent(self, agent_id: str) -> int:
        """Unsubscribe all subscriptions for an agent."""
        with self._lock:
            if agent_id not in self.agent_subscriptions:
                return 0

            subscription_ids = list(self.agent_subscriptions[agent_id])
            count = 0

            for subscription_id in subscription_ids:
                if self.unsubscribe(subscription_id):
                    count += 1

        logger.info(f"Unsubscribed {count} subscriptions for agent {agent_id}")
        return count

    async def publish(self, message: AgentMessage) -> bool:
        """Publish a message to the bus."""
        try:
            # Validate message
            if message.is_expired():
                logger.warning(f"Message {message.id} is expired, not publishing")
                return False

            # Store in history
            with self._lock:
                self.message_history.append(message)
                self.delivery_stats[message.sender_id]["sent"] += 1

            # Route message
            if message.recipient_id:
                # Direct message
                await self._deliver_direct_message(message)
            else:
                # Broadcast message
                await self._deliver_broadcast_message(message)

            # WebSocket notification
            await self._notify_websocket_clients(message)

            logger.debug(f"Published message {message.id} from {message.sender_id}")
            return True

        except Exception as e:
            logger.error(f"Error publishing message: {e}")
            return False

    async def send_message(
        self,
        sender_id: str,
        recipient_id: Optional[str],
        message_type: MessageType,
        subject: str,
        content: Dict[str, Any],
        priority: MessagePriority = MessagePriority.NORMAL,
        expires_in_seconds: Optional[int] = None,
        requires_acknowledgment: bool = False,
        correlation_id: Optional[str] = None,
    ) -> str:
        """Send a message (convenience method)."""
        message_id = str(uuid.uuid4())
        expires_at = None
        if expires_in_seconds:
            expires_at = time.time() + expires_in_seconds

        message = AgentMessage(
            id=message_id,
            message_type=message_type,
            sender_id=sender_id,
            recipient_id=recipient_id,
            subject=subject,
            content=content,
            priority=priority,
            expires_at=expires_at,
            requires_acknowledgment=requires_acknowledgment,
            correlation_id=correlation_id,
        )

        success = await self.publish(message)
        return message_id if success else None

    async def send_request(
        self,
        sender_id: str,
        recipient_id: str,
        subject: str,
        content: Dict[str, Any],
        timeout_seconds: int = 30,
        priority: MessagePriority = MessagePriority.NORMAL,
    ) -> Optional[AgentMessage]:
        """Send a request and wait for response."""
        correlation_id = str(uuid.uuid4())

        # Create response future
        response_future = asyncio.Future()

        # Subscribe to response
        def response_handler(message: AgentMessage):
            if (
                message.correlation_id == correlation_id
                and message.message_type == MessageType.TASK_RESPONSE
            ):
                if not response_future.done():
                    response_future.set_result(message)

        subscription_id = self.subscribe(
            agent_id=sender_id,
            callback=response_handler,
            message_filter=MessageFilter(
                message_types=[MessageType.TASK_RESPONSE], sender_ids=[recipient_id]
            ),
        )

        try:
            # Send request
            await self.send_message(
                sender_id=sender_id,
                recipient_id=recipient_id,
                message_type=MessageType.TASK_REQUEST,
                subject=subject,
                content=content,
                priority=priority,
                correlation_id=correlation_id,
                requires_acknowledgment=True,
            )

            # Wait for response
            response = await asyncio.wait_for(response_future, timeout=timeout_seconds)
            return response

        except asyncio.TimeoutError:
            logger.warning(f"Request {correlation_id} timed out")
            return None
        except Exception as e:
            logger.error(f"Error in send_request: {e}")
            return None
        finally:
            self.unsubscribe(subscription_id)

    async def acknowledge_message(self, message_id: str, agent_id: str) -> bool:
        """Acknowledge receipt of a message."""
        with self._lock:
            if message_id in self.pending_acknowledgments:
                message = self.pending_acknowledgments[message_id]
                message.status = MessageStatus.ACKNOWLEDGED
                self.delivery_stats[message.sender_id]["acknowledged"] += 1
                del self.pending_acknowledgments[message_id]
                logger.debug(f"Message {message_id} acknowledged by {agent_id}")
                return True

        return False

    async def _deliver_direct_message(self, message: AgentMessage) -> None:
        """Deliver message to specific recipient."""
        recipient_id = message.recipient_id

        # Find subscriptions for recipient
        matching_subscriptions = []
        with self._lock:
            if recipient_id in self.agent_subscriptions:
                for subscription_id in self.agent_subscriptions[recipient_id]:
                    subscription = self.subscriptions[subscription_id]
                    if subscription.matches(message):
                        matching_subscriptions.append(subscription)

        # Deliver to matching subscriptions
        delivery_success = False
        for subscription in matching_subscriptions:
            try:
                success = await subscription.deliver(message)
                if success:
                    delivery_success = True
                    message.status = MessageStatus.DELIVERED
                    self.delivery_stats[message.sender_id]["delivered"] += 1
            except Exception as e:
                logger.error(
                    f"Error delivering to subscription {subscription.subscription_id}: {e}"
                )

        # Handle acknowledgment requirement
        if message.requires_acknowledgment and delivery_success:
            with self._lock:
                self.pending_acknowledgments[message.id] = message

        # Queue for offline delivery if no subscriptions
        if not matching_subscriptions:
            with self._lock:
                self.message_queues[recipient_id].append(message)
                logger.debug(
                    f"Queued message {message.id} for offline agent {recipient_id}"
                )

    async def _deliver_broadcast_message(self, message: AgentMessage) -> None:
        """Deliver broadcast message to all matching subscribers."""
        matching_subscriptions = []
        with self._lock:
            for subscription in self.subscriptions.values():
                if subscription.matches(message):
                    matching_subscriptions.append(subscription)

        # Deliver to all matching subscriptions
        delivery_count = 0
        for subscription in matching_subscriptions:
            try:
                success = await subscription.deliver(message)
                if success:
                    delivery_count += 1
            except Exception as e:
                logger.error(
                    f"Error delivering broadcast to {subscription.subscription_id}: {e}"
                )

        if delivery_count > 0:
            message.status = MessageStatus.DELIVERED
            self.delivery_stats[message.sender_id]["delivered"] += 1
            logger.debug(
                f"Broadcast message {message.id} delivered to {delivery_count} subscribers"
            )

    def get_queued_messages(self, agent_id: str) -> List[AgentMessage]:
        """Get queued messages for an agent."""
        with self._lock:
            if agent_id not in self.message_queues:
                return []

            messages = list(self.message_queues[agent_id])
            self.message_queues[agent_id].clear()
            return messages

    def get_message_history(
        self,
        limit: Optional[int] = None,
        agent_id: Optional[str] = None,
        message_type: Optional[MessageType] = None,
    ) -> List[AgentMessage]:
        """Get message history with optional filtering."""
        with self._lock:
            messages = list(self.message_history)

        # Apply filters
        if agent_id:
            messages = [
                m
                for m in messages
                if m.sender_id == agent_id or m.recipient_id == agent_id
            ]

        if message_type:
            messages = [m for m in messages if m.message_type == message_type]

        # Apply limit
        if limit:
            messages = messages[-limit:]

        return messages

    def get_delivery_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get message delivery statistics."""
        with self._lock:
            return dict(self.delivery_stats)

    def get_subscription_stats(self) -> Dict[str, Any]:
        """Get subscription statistics."""
        with self._lock:
            total_subscriptions = len(self.subscriptions)
            agents_with_subscriptions = len(self.agent_subscriptions)

            subscription_details = {}
            for agent_id, subscription_ids in self.agent_subscriptions.items():
                subscription_details[agent_id] = {
                    "subscription_count": len(subscription_ids),
                    "subscriptions": [
                        {
                            "id": sub_id,
                            "message_count": self.subscriptions[sub_id].message_count,
                            "last_message_at": self.subscriptions[
                                sub_id
                            ].last_message_at,
                        }
                        for sub_id in subscription_ids
                    ],
                }

            return {
                "total_subscriptions": total_subscriptions,
                "agents_with_subscriptions": agents_with_subscriptions,
                "subscription_details": subscription_details,
            }

    def get_system_stats(self) -> Dict[str, Any]:
        """Get overall system statistics."""
        with self._lock:
            return {
                "message_history_size": len(self.message_history),
                "pending_messages": len(self.pending_messages),
                "pending_acknowledgments": len(self.pending_acknowledgments),
                "total_subscriptions": len(self.subscriptions),
                "active_agents": len(self.agent_subscriptions),
                "websocket_clients": len(self.websocket_clients),
                "is_running": self._running,
            }

    # WebSocket Integration

    def add_websocket_client(self, client_id: str, websocket) -> None:
        """Add WebSocket client for real-time updates."""
        with self._lock:
            self.websocket_clients[client_id] = websocket
        logger.info(f"Added WebSocket client {client_id}")

    def remove_websocket_client(self, client_id: str) -> None:
        """Remove WebSocket client."""
        with self._lock:
            if client_id in self.websocket_clients:
                del self.websocket_clients[client_id]
        logger.info(f"Removed WebSocket client {client_id}")

    def add_websocket_callback(self, callback: Callable[[AgentMessage], None]) -> None:
        """Add callback for WebSocket message notifications."""
        self.websocket_callbacks.append(callback)

    async def _notify_websocket_clients(self, message: AgentMessage) -> None:
        """Notify WebSocket clients of new message."""
        if not self.websocket_clients and not self.websocket_callbacks:
            return

        message_data = message.to_dict()

        # Send to WebSocket clients
        with self._lock:
            clients_to_remove = []
            for client_id, websocket in self.websocket_clients.items():
                try:
                    await websocket.send_text(
                        json.dumps({"type": "agent_message", "data": message_data})
                    )
                except Exception as e:
                    logger.warning(
                        f"Error sending to WebSocket client {client_id}: {e}"
                    )
                    clients_to_remove.append(client_id)

            # Clean up disconnected clients
            for client_id in clients_to_remove:
                del self.websocket_clients[client_id]

        # Call registered callbacks
        for callback in self.websocket_callbacks:
            try:
                callback(message)
            except Exception as e:
                logger.error(f"Error in WebSocket callback: {e}")

    async def _cleanup_loop(self) -> None:
        """Periodic cleanup of expired messages and acknowledgments."""
        while self._running:
            try:
                current_time = time.time()

                # Clean up expired pending acknowledgments
                with self._lock:
                    expired_acks = []
                    for message_id, message in self.pending_acknowledgments.items():
                        if message.is_expired():
                            expired_acks.append(message_id)

                    for message_id in expired_acks:
                        del self.pending_acknowledgments[message_id]
                        logger.debug(f"Cleaned up expired acknowledgment {message_id}")

                # Sleep until next cleanup
                await asyncio.sleep(self.cleanup_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(10)  # Wait before retrying

    def export_messages(self, file_path: str, format: str = "json") -> None:
        """Export message history to file."""
        try:
            with self._lock:
                messages = [m.to_dict() for m in self.message_history]

            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            if format.lower() == "json":
                with open(file_path, "w") as f:
                    json.dump(messages, f, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported export format: {format}")

            logger.info(f"Exported {len(messages)} messages to {file_path}")
        except Exception as e:
            logger.error(f"Error exporting messages: {e}")

    def import_messages(self, file_path: str) -> int:
        """Import messages from file."""
        try:
            if not Path(file_path).exists():
                logger.error(f"Import file not found: {file_path}")
                return 0

            with open(file_path, "r") as f:
                message_data = json.load(f)

            imported_count = 0
            with self._lock:
                for data in message_data:
                    try:
                        message = AgentMessage.from_dict(data)
                        self.message_history.append(message)
                        imported_count += 1
                    except Exception as e:
                        logger.warning(f"Error importing message: {e}")

            logger.info(f"Imported {imported_count} messages from {file_path}")
            return imported_count
        except Exception as e:
            logger.error(f"Error importing messages: {e}")
            return 0
