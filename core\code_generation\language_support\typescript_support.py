#!/usr/bin/env python3
"""
TypeScript AST Support for Code Generation

This module provides TypeScript-specific AST manipulation and code generation support.
"""

import ast
import logging
import re
import warnings
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

import astor

from core.code_generation.language_support.base_language_support import (
    ASTNodeInfo,
    BaseLanguageSupport,
    LanguageType,
)

# Suppress astor deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="astor")


logger = logging.getLogger(__name__)


@dataclass
class TypeScriptFunctionInfo:
    """Information about a TypeScript function"""

    name: str
    parameters: List[str]
    return_type: Optional[str] = None
    is_async: bool = False
    is_export: bool = False
    decorators: List[str] = None


@dataclass
class TypeScriptClassInfo:
    """Information about a TypeScript class"""

    name: str
    extends: Optional[str] = None
    implements: List[str] = None
    methods: List[TypeScriptFunctionInfo] = None
    properties: List[Dict[str, Any]] = None
    is_export: bool = False
    decorators: List[str] = None


class TypeScriptASTSupport(BaseLanguageSupport):
    """
    TypeScript-specific AST support implementation.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(LanguageType.TYPESCRIPT, config)

    async def create_base_ast(self) -> ast.AST:
        """Create a base TypeScript AST"""
        # For TypeScript, we'll use a simplified AST structure
        # In practice, you'd use a proper TypeScript parser
        return ast.Module(body=[], type_ignores=[])

    async def parse_code(self, code: str) -> ast.AST:
        """Parse TypeScript code string into AST"""
        try:
            # For now, we'll use Python's AST parser as a fallback
            # In practice, you'd use a TypeScript parser like ast-typescript
            return ast.parse(code)
        except SyntaxError as e:
            logger.error(f"Failed to parse TypeScript code: {e}")
            raise

    async def ast_to_code(self, ast_tree: ast.AST) -> str:
        """Convert TypeScript AST back to code string"""
        try:
            # For now, we'll use astor as a fallback
            # In practice, you'd use a TypeScript code generator
            code = astor.to_source(ast_tree)
            # Convert Python-style code to TypeScript
            return self._convert_to_typescript(code)
        except Exception as e:
            logger.error(f"Failed to convert AST to TypeScript code: {e}")
            raise

    async def add_import(self, ast_tree: ast.AST, import_statement: str) -> ast.AST:
        """Add import statement to TypeScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse the import statement
        try:
            import_ast = ast.parse(import_statement)
            if isinstance(import_ast, ast.Module) and import_ast.body:
                import_node = import_ast.body[0]

                # Check if import already exists
                for existing_node in ast_tree.body:
                    if isinstance(existing_node, type(import_node)):
                        if self._imports_are_equivalent(existing_node, import_node):
                            return ast_tree

                # Add import at the beginning
                ast_tree.body.insert(0, import_node)

        except SyntaxError as e:
            logger.error(f"Invalid import statement: {import_statement}, error: {e}")
            raise

        return ast_tree

    async def add_function(
        self, ast_tree: ast.AST, function_name: str, parameters: List[str], body: str
    ) -> ast.AST:
        """Add function to TypeScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse function body
        try:
            body_ast = ast.parse(body)
            if isinstance(body_ast, ast.Module):
                function_body = body_ast.body
            else:
                function_body = [body_ast]
        except SyntaxError:
            # If body parsing fails, create a simple pass statement
            function_body = [ast.Pass()]

        # Create function arguments
        args = []
        for param in parameters:
            if ":" in param:
                name, param_type = param.split(":", 1)
                args.append(
                    ast.arg(
                        arg=name.strip(), annotation=ast.Name(id=param_type.strip())
                    )
                )
            else:
                args.append(ast.arg(arg=param.strip()))

        # Create function definition
        function_def = ast.FunctionDef(
            name=function_name,
            args=ast.arguments(
                posonlyargs=[], args=args, kwonlyargs=[], defaults=[], kw_defaults=[]
            ),
            body=function_body,
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(function_def)
        return ast_tree

    async def add_class(
        self, ast_tree: ast.AST, class_name: str, methods: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add class to TypeScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create class body
        class_body = []

        # Add methods
        for method_info in methods:
            method_name = method_info.get("name", "")
            parameters = method_info.get("parameters", [])
            body = method_info.get("body", "pass")

            # Parse method body
            try:
                body_ast = ast.parse(body)
                if isinstance(body_ast, ast.Module):
                    method_body = body_ast.body
                else:
                    method_body = [body_ast]
            except SyntaxError:
                method_body = [ast.Pass()]

            # Create method arguments
            # TypeScript methods have 'this' context
            args = [ast.arg(arg="this")]
            for param in parameters:
                if ":" in param:
                    name, param_type = param.split(":", 1)
                    args.append(
                        ast.arg(
                            arg=name.strip(), annotation=ast.Name(id=param_type.strip())
                        )
                    )
                else:
                    args.append(ast.arg(arg=param.strip()))

            # Create method definition
            method_def = ast.FunctionDef(
                name=method_name,
                args=ast.arguments(
                    posonlyargs=[],
                    args=args,
                    kwonlyargs=[],
                    defaults=[],
                    kw_defaults=[],
                ),
                body=method_body,
                decorator_list=[],
                returns=None,
            )

            class_body.append(method_def)

        # Create class definition
        class_def = ast.ClassDef(
            name=class_name, bases=[], keywords=[], body=class_body, decorator_list=[]
        )

        ast_tree.body.append(class_def)
        return ast_tree

    async def add_variable(
        self,
        ast_tree: ast.AST,
        variable_name: str,
        value: str,
        var_type: Optional[str] = None,
    ) -> ast.AST:
        """Add variable declaration to TypeScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse the value
        try:
            value_ast = ast.parse(value, mode="eval")
        except SyntaxError:
            # If parsing fails, use the string as-is
            value_ast = ast.Constant(value=value)

        # Create assignment
        if var_type:
            # With type annotation
            annotation = ast.Name(id=var_type)
            assign = ast.AnnAssign(
                target=ast.Name(id=variable_name),
                annotation=annotation,
                value=value_ast,
                simple=1,
            )
        else:
            # Without type annotation
            assign = ast.Assign(targets=[ast.Name(id=variable_name)], value=value_ast)

        ast_tree.body.append(assign)
        return ast_tree

    async def merge_asts(self, existing_ast: ast.AST, new_ast: ast.AST) -> ast.AST:
        """Merge two TypeScript ASTs"""
        if not isinstance(existing_ast, ast.Module) or not isinstance(
            new_ast, ast.Module
        ):
            raise ValueError("Both ASTs must be Modules")

        # Merge imports first
        existing_imports = []
        existing_others = []

        for node in existing_ast.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                existing_imports.append(node)
            else:
                existing_others.append(node)

        new_imports = []
        new_others = []

        for node in new_ast.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                new_imports.append(node)
            else:
                new_others.append(node)

        # Merge imports, avoiding duplicates
        merged_imports = existing_imports.copy()
        for new_import in new_imports:
            if not any(
                self._imports_are_equivalent(existing, new_import)
                for existing in merged_imports
            ):
                merged_imports.append(new_import)

        # Create merged AST
        merged_ast = ast.Module(
            body=merged_imports + existing_others + new_others, type_ignores=[]
        )

        return merged_ast

    async def validate_ast(self, ast_tree: ast.AST) -> bool:
        """Validate TypeScript AST structure"""
        try:
            # Try to convert back to code to validate
            await self.ast_to_code(ast_tree)
            return True
        except Exception as e:
            logger.error(f"AST validation failed: {e}")
            return False

    async def extract_node_info(self, ast_tree: ast.AST) -> List[ASTNodeInfo]:
        """Extract information about TypeScript AST nodes"""
        node_info_list = []

        for node in ast.walk(ast_tree):
            info = ASTNodeInfo(
                node_type=type(node).__name__,
                line_number=getattr(node, "lineno", None),
                column=getattr(node, "col_offset", None),
            )

            # Extract name for named nodes
            if hasattr(node, "name"):
                info.name = node.name
            elif hasattr(node, "id"):
                info.name = node.id
            elif hasattr(node, "attr"):
                info.name = node.attr

            # Add specific metadata
            if isinstance(node, ast.FunctionDef):
                info.metadata = {
                    "is_async": isinstance(node, ast.AsyncFunctionDef),
                    "decorator_count": len(node.decorator_list),
                    "parameter_count": len(node.args.args),
                }
            elif isinstance(node, ast.ClassDef):
                info.metadata = {
                    "base_count": len(node.bases),
                    "decorator_count": len(node.decorator_list),
                    "method_count": len(
                        [n for n in node.body if isinstance(n, ast.FunctionDef)]
                    ),
                }
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                info.metadata = {
                    "import_type": (
                        "import" if isinstance(node, ast.Import) else "from_import"
                    )
                }

            node_info_list.append(info)

        return node_info_list

    def _imports_are_equivalent(self, import1: ast.AST, import2: ast.AST) -> bool:
        """Check if two import statements are equivalent"""
        if type(import1) != type(import2):
            return False

        if isinstance(import1, ast.Import):
            return import1.names == import2.names
        elif isinstance(import1, ast.ImportFrom):
            return (
                import1.module == import2.module
                and import1.names == import2.names
                and import1.level == import2.level
            )

        return False

    async def _apply_naming_conventions(
        self, ast_tree: ast.AST, naming_style: str
    ) -> ast.AST:
        """Apply TypeScript naming conventions to AST"""
        if naming_style == "camelCase":
            # Convert function and variable names to camelCase
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef) and not self._is_camel_case(
                    node.name
                ):
                    node.name = self._to_camel_case(node.name)
                elif isinstance(node, ast.Name) and not self._is_camel_case(node.id):
                    node.id = self._to_camel_case(node.id)

        return ast_tree

    async def _apply_line_length_conventions(
        self, ast_tree: ast.AST, max_length: int
    ) -> ast.AST:
        """Apply line length conventions to TypeScript AST"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated line breaking logic
        return ast_tree

    def _is_camel_case(self, name: str) -> bool:
        """Check if a name follows camelCase convention"""
        return bool(re.match(r"^[a-z][a-zA-Z0-9]*$", name))

    def _to_camel_case(self, name: str) -> str:
        """Convert a name to camelCase"""
        # Convert snake_case or kebab-case to camelCase
        name = re.sub(r"[-_]", " ", name)
        words = name.split()
        if not words:
            return name
        return words[0].lower() + "".join(word.capitalize() for word in words[1:])

    def _convert_to_typescript(self, python_code: str) -> str:
        """Convert Python-style code to TypeScript"""
        # This is a simplified conversion
        # In practice, you'd need a more sophisticated converter

        # Replace Python-specific syntax
        typescript_code = python_code

        # Convert function definitions
        typescript_code = re.sub(r"def (\w+)\(", r"function \1(", typescript_code)

        # Convert class definitions
        typescript_code = re.sub(r"class (\w+):", r"class \1 {", typescript_code)

        # Convert imports
        typescript_code = re.sub(r"from (\w+) import", r"import {", typescript_code)
        typescript_code = re.sub(r"import (\w+)", r"import \1", typescript_code)

        # Convert variable declarations
        typescript_code = re.sub(r"(\w+): (\w+) =", r"const \1: \2 =", typescript_code)

        return typescript_code

    async def add_interface(
        self, ast_tree: ast.AST, interface_name: str, properties: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add TypeScript interface to AST"""
        # This is a simplified implementation
        # In practice, you'd need proper TypeScript AST support
        return ast_tree

    async def add_type_alias(
        self, ast_tree: ast.AST, type_name: str, type_definition: str
    ) -> ast.AST:
        """Add TypeScript type alias to AST"""
        # This is a simplified implementation
        # In practice, you'd need proper TypeScript AST support
        return ast_tree

    async def add_decorator(
        self,
        ast_tree: ast.AST,
        decorator_name: str,
        target: str,
        parameters: List[str] = None,
    ) -> ast.AST:
        """Add TypeScript decorator to AST"""
        # This is a simplified implementation
        # In practice, you'd need proper TypeScript AST support
        return ast_tree
