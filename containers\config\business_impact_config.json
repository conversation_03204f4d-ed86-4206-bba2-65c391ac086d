{"correlation_window": 86400, "min_correlation_strength": 0.3, "confidence_interval": 0.95, "kpi_mappings": {"response_time": ["user_satisfaction", "conversion_rate"], "success_rate": ["user_retention", "customer_satisfaction"], "throughput": ["revenue", "user_engagement"], "error_rate": ["customer_support_tickets", "user_churn"]}, "impact_direction_thresholds": {"positive": 0.3, "negative": -0.3, "neutral": 0.0}, "correlation_analysis": {"min_data_points": 10, "normalization": {"response_time_max": 10.0, "business_metric_max": 100.0}}, "relationship_types": {"response_time": {"relationship": "inverse", "description": "Lower response time correlates with higher business metrics"}, "success_rate": {"relationship": "direct", "description": "Higher success rate correlates with higher business metrics"}, "error_rate": {"relationship": "inverse", "description": "Lower error rate correlates with higher business metrics"}, "throughput": {"relationship": "direct", "description": "Higher throughput correlates with higher business metrics"}}, "insight_generation": {"high_correlation_threshold": 0.5, "actionable_insight_threshold": 0.3, "recommendation_templates": {"response_time": ["Optimize response time to improve user satisfaction", "Consider caching strategies to reduce latency", "Monitor response time impact on conversion rates"], "success_rate": ["Maintain high success rates to improve user retention", "Implement error handling to prevent user churn", "Track success rate correlation with customer satisfaction"], "error_rate": ["Reduce error rates to decrease support tickets", "Implement robust error handling and recovery", "Monitor error rate impact on user churn"], "throughput": ["Optimize throughput to increase revenue", "Scale resources to handle higher user engagement", "Monitor throughput correlation with business metrics"]}}}