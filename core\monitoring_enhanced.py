#!/usr/bin/env python3
"""
Enhanced Monitoring System for AI Coding Agent
Provides comprehensive monitoring of system performance, resources, and operations.
"""

import asyncio
import json
import logging
import os
import threading
import time
from collections import defaultdict, deque
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import psutil

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """System performance metrics"""

    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    process_count: int


@dataclass
class ApplicationMetrics:
    """Application-specific metrics"""

    timestamp: datetime
    active_requests: int
    request_rate: float
    error_rate: float
    response_time_avg: float
    database_connections: int
    cache_hit_rate: float
    backup_status: str
    validation_status: str


@dataclass
class Alert:
    """System alert"""

    timestamp: datetime
    level: str  # 'info', 'warning', 'error', 'critical'
    category: str
    message: str
    details: Optional[Dict[str, Any]] = None
    acknowledged: bool = False


class MetricsCollector:
    """Collects system and application metrics"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.collection_interval = self.config.get("collection_interval", 30)
        self.retention_hours = self.config.get("retention_hours", 24)
        self.metrics_file = Path("data/monitoring/metrics.json")
        self.metrics_file.parent.mkdir(parents=True, exist_ok=True)

        # Metrics storage
        self.system_metrics: deque = deque(maxlen=3600)  # 1 hour at 1-second intervals
        self.application_metrics: deque = deque(maxlen=3600)
        self.alerts: List[Alert] = []

        # Performance tracking
        self.request_times: deque = deque(maxlen=1000)
        self.error_counts: defaultdict = defaultdict(int)
        self.last_network_stats = psutil.net_io_counters()
        self.last_network_check = time.time()

        # Monitoring state
        self.monitoring_active = False
        self.collection_thread: Optional[threading.Thread] = None

        # Setup logging
        self.logger = logging.getLogger(__name__)

    def start_monitoring(self):
        """Start continuous metrics collection"""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        self.collection_thread = threading.Thread(
            target=self._collection_loop, daemon=True
        )
        self.collection_thread.start()
        self.logger.info("Metrics collection started")

    def stop_monitoring(self):
        """Stop continuous metrics collection"""
        self.monitoring_active = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        self.logger.info("Metrics collection stopped")

    def _collection_loop(self):
        """Main collection loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                system_metrics = self._collect_system_metrics()
                self.system_metrics.append(system_metrics)

                # Collect application metrics
                app_metrics = self._collect_application_metrics()
                self.application_metrics.append(app_metrics)

                # Check for alerts
                self._check_alerts(system_metrics, app_metrics)

                # Save metrics periodically
                if len(self.system_metrics) % 60 == 0:  # Every 60 collections
                    self._save_metrics()

                time.sleep(self.collection_interval)

            except Exception as e:
                self.logger.error(f"Error in metrics collection: {e}")
                time.sleep(5)  # Shorter delay on error

    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Disk usage
            disk = psutil.disk_usage(".")
            disk_usage_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024**3)

            # Network usage
            current_time = time.time()
            current_network = psutil.net_io_counters()

            if self.last_network_check > 0:
                time_diff = current_time - self.last_network_check
                network_bytes_sent = (
                    current_network.bytes_sent - self.last_network_stats.bytes_sent
                ) / time_diff
                network_bytes_recv = (
                    current_network.bytes_recv - self.last_network_stats.bytes_recv
                ) / time_diff
            else:
                network_bytes_sent = 0
                network_bytes_recv = 0

            self.last_network_stats = current_network
            self.last_network_check = current_time

            # Active connections
            active_connections = len(psutil.net_connections())

            # Process count
            process_count = len(psutil.pids())

            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_usage_percent=disk_usage_percent,
                disk_free_gb=disk_free_gb,
                network_bytes_sent=int(network_bytes_sent),
                network_bytes_recv=int(network_bytes_recv),
                active_connections=active_connections,
                process_count=process_count,
            )

        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            # Return default metrics on error
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_usage_percent=0.0,
                disk_free_gb=0.0,
                network_bytes_sent=0,
                network_bytes_recv=0,
                active_connections=0,
                process_count=0,
            )

    def _collect_application_metrics(self) -> ApplicationMetrics:
        """Collect application-specific metrics"""
        try:
            # Calculate request rate
            current_time = time.time()
            recent_requests = [
                rt for rt in self.request_times if current_time - rt < 60
            ]  # Last minute
            request_rate = len(recent_requests) / 60.0

            # Calculate error rate
            total_errors = sum(self.error_counts.values())
            total_requests = len(self.request_times)
            error_rate = (
                (total_errors / total_requests * 100) if total_requests > 0 else 0.0
            )

            # Calculate average response time
            if self.request_times:
                response_time_avg = sum(self.request_times) / len(self.request_times)
            else:
                response_time_avg = 0.0

            # Get backup status
            backup_status = self._get_backup_status()

            # Get validation status
            validation_status = self._get_validation_status()

            return ApplicationMetrics(
                timestamp=datetime.now(),
                active_requests=len(recent_requests),
                request_rate=request_rate,
                error_rate=error_rate,
                response_time_avg=response_time_avg,
                database_connections=0,  # Would need database-specific implementation
                cache_hit_rate=0.0,  # Would need cache-specific implementation
                backup_status=backup_status,
                validation_status=validation_status,
            )

        except Exception as e:
            self.logger.error(f"Error collecting application metrics: {e}")
            return ApplicationMetrics(
                timestamp=datetime.now(),
                active_requests=0,
                request_rate=0.0,
                error_rate=0.0,
                response_time_avg=0.0,
                database_connections=0,
                cache_hit_rate=0.0,
                backup_status="unknown",
                validation_status="unknown",
            )

    def _get_backup_status(self) -> str:
        """Get current backup system status"""
        try:
            backup_dir = Path("backups")
            if not backup_dir.exists():
                return "no_backups"

            # Check for recent backups
            recent_backups = list(backup_dir.glob("*"))
            if not recent_backups:
                return "no_backups"

            # Check if any backup is recent (within last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_backup = any(
                datetime.fromtimestamp(b.stat().st_mtime) > cutoff_time
                for b in recent_backups
                if b.is_dir()
            )

            return "recent" if recent_backup else "stale"

        except Exception:
            return "unknown"

    def _get_validation_status(self) -> str:
        """Get current validation system status"""
        try:
            # Check if validation system is available
            from core.validation import get_validation_system

            validation_system = get_validation_system()
            health = validation_system.get_system_health()
            return health.get("status", "unknown")
        except Exception:
            return "unknown"

    def _check_alerts(
        self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics
    ):
        """Check for conditions that require alerts"""
        alerts = []

        # CPU usage alert
        if system_metrics.cpu_percent > 90:
            alerts.append(
                Alert(
                    timestamp=datetime.now(),
                    level="warning",
                    category="performance",
                    message=f"High CPU usage: {system_metrics.cpu_percent:.1f}%",
                    details={"cpu_percent": system_metrics.cpu_percent},
                )
            )

        # Memory usage alert
        if system_metrics.memory_percent > 85:
            alerts.append(
                Alert(
                    timestamp=datetime.now(),
                    level="warning",
                    category="performance",
                    message=f"High memory usage: {system_metrics.memory_percent:.1f}%",
                    details={"memory_percent": system_metrics.memory_percent},
                )
            )

        # Disk space alert
        if system_metrics.disk_free_gb < 1.0:
            alerts.append(
                Alert(
                    timestamp=datetime.now(),
                    level="critical",
                    category="storage",
                    message=f"Low disk space: {system_metrics.disk_free_gb:.2f}GB free",
                    details={"disk_free_gb": system_metrics.disk_free_gb},
                )
            )

        # Error rate alert
        if app_metrics.error_rate > 5.0:
            alerts.append(
                Alert(
                    timestamp=datetime.now(),
                    level="error",
                    category="application",
                    message=f"High error rate: {app_metrics.error_rate:.1f}%",
                    details={"error_rate": app_metrics.error_rate},
                )
            )

        # Backup status alert
        if app_metrics.backup_status == "stale":
            alerts.append(
                Alert(
                    timestamp=datetime.now(),
                    level="warning",
                    category="backup",
                    message="No recent backups found",
                    details={"backup_status": app_metrics.backup_status},
                )
            )

        # Add new alerts
        self.alerts.extend(alerts)

        # Clean up old alerts (older than 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.alerts = [alert for alert in self.alerts if alert.timestamp > cutoff_time]

    def record_request(
        self,
        response_time: float,
        success: bool = True,
        error_type: Optional[str] = None,
    ):
        """Record a request for metrics calculation"""
        self.request_times.append(response_time)

        if not success and error_type:
            self.error_counts[error_type] += 1

    def _save_metrics(self):
        """Save metrics to file"""
        try:
            metrics_data = {
                "system_metrics": [
                    asdict(m) for m in list(self.system_metrics)[-100:]
                ],  # Last 100
                "application_metrics": [
                    asdict(m) for m in list(self.application_metrics)[-100:]
                ],
                "alerts": [asdict(a) for a in self.alerts],
                "last_updated": datetime.now().isoformat(),
            }

            with open(self.metrics_file, "w") as f:
                json.dump(metrics_data, f, indent=2)

        except Exception as e:
            self.logger.error(f"Error saving metrics: {e}")

    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current system and application metrics"""
        if not self.system_metrics:
            return {"error": "No metrics available"}

        latest_system = self.system_metrics[-1]
        latest_app = self.application_metrics[-1] if self.application_metrics else None

        return {
            "system": asdict(latest_system),
            "application": asdict(latest_app) if latest_app else None,
            "alerts": [asdict(a) for a in self.alerts[-10:]],  # Last 10 alerts
            "monitoring_active": self.monitoring_active,
        }

    def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get metrics summary for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        # Filter metrics by time
        recent_system = [m for m in self.system_metrics if m.timestamp > cutoff_time]
        recent_app = [m for m in self.application_metrics if m.timestamp > cutoff_time]

        if not recent_system:
            return {"error": "No metrics available for specified time period"}

        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_system) / len(recent_system)
        avg_memory = sum(m.memory_percent for m in recent_system) / len(recent_system)
        avg_disk = sum(m.disk_usage_percent for m in recent_system) / len(recent_system)

        if recent_app:
            avg_request_rate = sum(m.request_rate for m in recent_app) / len(recent_app)
            avg_error_rate = sum(m.error_rate for m in recent_app) / len(recent_app)
            avg_response_time = sum(m.response_time_avg for m in recent_app) / len(
                recent_app
            )
        else:
            avg_request_rate = 0.0
            avg_error_rate = 0.0
            avg_response_time = 0.0

        return {
            "period_hours": hours,
            "metrics_count": len(recent_system),
            "averages": {
                "cpu_percent": avg_cpu,
                "memory_percent": avg_memory,
                "disk_usage_percent": avg_disk,
                "request_rate": avg_request_rate,
                "error_rate": avg_error_rate,
                "response_time_avg": avg_response_time,
            },
            "current": {
                "cpu_percent": recent_system[-1].cpu_percent,
                "memory_percent": recent_system[-1].memory_percent,
                "disk_free_gb": recent_system[-1].disk_free_gb,
            },
        }


class MonitoringDashboard:
    """Provides monitoring dashboard functionality"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.logger = logging.getLogger(__name__)

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        try:
            current_metrics = self.metrics_collector.get_current_metrics()
            summary_24h = self.metrics_collector.get_metrics_summary(24)
            summary_1h = self.metrics_collector.get_metrics_summary(1)

            # Get system health
            try:
                from core.validation import get_validation_system

                validation_system = get_validation_system()
                system_health = validation_system.get_system_health()
            except Exception:
                system_health = {
                    "status": "unknown",
                    "message": "Validation system unavailable",
                }

            # Get recovery system health
            try:
                from core.recovery_system import get_recovery_system

                recovery_system = get_recovery_system()
                recovery_health = recovery_system.get_system_health()
            except Exception:
                recovery_health = {
                    "status": "unknown",
                    "message": "Recovery system unavailable",
                }

            return {
                "current_metrics": current_metrics,
                "summary_24h": summary_24h,
                "summary_1h": summary_1h,
                "system_health": system_health,
                "recovery_health": recovery_health,
                "dashboard_time": datetime.now().isoformat(),
            }

        except Exception as e:
            self.logger.error(f"Error getting dashboard data: {e}")
            return {"error": str(e)}

    def get_alerts_summary(self) -> Dict[str, Any]:
        """Get summary of current alerts"""
        alerts = self.metrics_collector.alerts

        # Count alerts by level
        alert_counts = defaultdict(int)
        for alert in alerts:
            alert_counts[alert.level] += 1

        # Get recent critical alerts
        recent_critical = [
            asdict(a)
            for a in alerts
            if a.level == "critical"
            and a.timestamp > datetime.now() - timedelta(hours=1)
        ]

        return {
            "total_alerts": len(alerts),
            "alert_counts": dict(alert_counts),
            "recent_critical": recent_critical,
            "unacknowledged": len([a for a in alerts if not a.acknowledged]),
        }


# Global monitoring instances
metrics_collector = MetricsCollector()
monitoring_dashboard = MonitoringDashboard(metrics_collector)


def start_monitoring():
    """Start the monitoring system"""
    metrics_collector.start_monitoring()


def stop_monitoring():
    """Stop the monitoring system"""
    metrics_collector.stop_monitoring()


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance"""
    return metrics_collector


def get_monitoring_dashboard() -> MonitoringDashboard:
    """Get the global monitoring dashboard instance"""
    return monitoring_dashboard
