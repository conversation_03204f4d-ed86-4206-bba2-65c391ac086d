"""
Pydantic models for API request/response validation
Provides consistent data structures and validation for all API endpoints.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator


class ChatRequest(BaseModel):
    """Request model for chat endpoint"""

    prompt: str = Field(
        ..., min_length=1, max_length=10000, description="User's message or question"
    )
    context: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional context for the request"
    )
    intent: Optional[str] = Field(default=None, description="User's intent or goal")
    history: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Conversation history"
    )
    model: Optional[str] = Field(
        default="deepseek-coder:1.3b", description="AI model to use"
    )
    complexity_hint: Optional[str] = Field(
        default=None, description="Task complexity hint"
    )

    @field_validator("prompt")
    @classmethod
    def validate_prompt(cls, v: str) -> str:
        if not v.strip():
            raise ValueError("Prompt cannot be empty")
        return v.strip()


class ChatResponse(BaseModel):
    """Response model for chat endpoint"""

    success: bool = Field(..., description="Whether the request was successful")
    response: Dict[str, Any] = Field(..., description="AI response content")
    metadata: Dict[str, Any] = Field(..., description="Request metadata and timing")


class ModelInfo(BaseModel):
    """Model information for available models endpoint"""

    name: str = Field(..., description="Model name")
    type: str = Field(..., description="Model type (local/cloud)")
    status: str = Field(..., description="Model status (available/unavailable)")
    description: Optional[str] = Field(default=None, description="Model description")
    capabilities: List[str] = Field(
        default_factory=list, description="Model capabilities"
    )


class UploadRequest(BaseModel):
    """Request model for file upload endpoint"""

    target_name: Optional[str] = Field(default=None, description="Target site name")
    review_first: bool = Field(
        default=True, description="Whether to review before processing"
    )
    cleanup_after: bool = Field(
        default=False, description="Whether to cleanup after processing"
    )


class UploadResponse(BaseModel):
    """Response model for upload endpoint"""

    success: bool = Field(..., description="Whether upload was successful")
    upload_id: str = Field(..., description="Unique upload identifier")
    message: str = Field(..., description="Upload status message")
    files_processed: int = Field(..., description="Number of files processed")
    upload_path: Optional[str] = Field(
        default=None, description="Path where files were uploaded"
    )


class SiteInfo(BaseModel):
    """Site information model"""

    name: str = Field(..., description="Site name")
    path: str = Field(..., description="Site path")
    created_at: datetime = Field(..., description="Creation timestamp")
    last_modified: datetime = Field(..., description="Last modification timestamp")
    file_count: int = Field(..., description="Number of files")
    size_mb: float = Field(..., description="Total size in MB")
    framework: Optional[str] = Field(default=None, description="Detected framework")
    status: str = Field(..., description="Site status")


class ErrorReport(BaseModel):
    """Error report model"""

    message: str = Field(..., min_length=1, description="Error message")
    type: str = Field(..., description="Error type")
    severity: str = Field(default="medium", description="Error severity")
    context: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional context"
    )
    user_agent: Optional[str] = Field(default=None, description="User agent string")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Error timestamp"
    )


class ErrorResponse(BaseModel):
    """Standard error response model"""

    success: bool = Field(default=False, description="Always false for errors")
    error: Dict[str, Any] = Field(..., description="Error details")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Error timestamp"
    )


class HealthStatus(BaseModel):
    """Health check response model"""

    status: str = Field(..., description="Overall health status")
    version: str = Field(..., description="API version")
    timestamp: datetime = Field(
        default_factory=datetime.now, description="Health check timestamp"
    )
    services: Dict[str, str] = Field(..., description="Individual service statuses")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")


class FileContent(BaseModel):
    """File content model for file operations"""

    path: str = Field(..., description="File path")
    content: str = Field(..., description="File content")
    encoding: str = Field(default="utf-8", description="File encoding")
    modified_at: Optional[datetime] = Field(
        default=None, description="Last modification time"
    )


class CommandRequest(BaseModel):
    """Command execution request model"""

    command: str = Field(..., min_length=1, description="Command to execute")
    working_directory: Optional[str] = Field(
        default=None, description="Working directory"
    )
    timeout: int = Field(
        default=300, ge=1, le=3600, description="Command timeout in seconds"
    )
    environment: Optional[Dict[str, str]] = Field(
        default=None, description="Environment variables"
    )


class CommandResponse(BaseModel):
    """Command execution response model"""

    success: bool = Field(..., description="Whether command executed successfully")
    command_id: str = Field(..., description="Unique command identifier")
    output: str = Field(..., description="Command output")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    exit_code: int = Field(..., description="Command exit code")
    execution_time: float = Field(..., description="Execution time in seconds")


class GitStatus(BaseModel):
    """Git status model"""

    branch: str = Field(..., description="Current branch")
    status: str = Field(..., description="Repository status")
    staged_files: List[str] = Field(default_factory=list, description="Staged files")
    unstaged_files: List[str] = Field(
        default_factory=list, description="Unstaged files"
    )
    untracked_files: List[str] = Field(
        default_factory=list, description="Untracked files"
    )
    last_commit: Optional[Dict[str, Any]] = Field(
        default=None, description="Last commit info"
    )


class CommitRequest(BaseModel):
    """Git commit request model"""

    message: str = Field(..., min_length=1, description="Commit message")
    files: List[str] = Field(default_factory=list, description="Files to commit")
    author_name: Optional[str] = Field(default=None, description="Author name")
    author_email: Optional[str] = Field(default=None, description="Author email")


class PreviewServerStatus(BaseModel):
    """Preview server status model"""

    running: bool = Field(..., description="Whether server is running")
    port: Optional[int] = Field(default=None, description="Server port")
    url: Optional[str] = Field(default=None, description="Server URL")
    start_time: Optional[datetime] = Field(
        default=None, description="Server start time"
    )
    process_id: Optional[int] = Field(default=None, description="Process ID")


# Enums for better type safety
class ErrorSeverity(str, Enum):
    """Error severity levels"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ModelType(str, Enum):
    """Model types"""

    LOCAL = "local"
    CLOUD = "cloud"


class SiteStatus(str, Enum):
    """Site status values"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PROCESSING = "processing"
