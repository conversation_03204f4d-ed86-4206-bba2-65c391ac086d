"""
Advanced Code Reviewer

Main orchestrator for comprehensive code review capabilities including
quality analysis, security review, performance analysis, and PR integration.

Phase 20 Implementation - Advanced Code Review
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional

from code_review.performance_analyzer import PerformanceAnalyzer
from code_review.pr_integration import PRIntegration
from code_review.quality_metrics import QualityMetrics
from code_review.review_dashboard import ReviewDashboard
from code_review.security_analyzer import SecurityAnalyzer
from code_review.yi_coder_integration import YiCoderIntegration

logger = logging.getLogger(__name__)


@dataclass
class CodeReviewRequest:
    """Request structure for code review operations."""

    code: str
    language: str
    file_path: Optional[str] = None
    project_context: Optional[Dict[str, Any]] = None
    review_type: str = (
        "comprehensive"  # "quality", "security", "performance", "comprehensive"
    )
    pr_context: Optional[Dict[str, Any]] = None


@dataclass
class CodeReviewResponse:
    """Response structure for code review operations."""

    quality_score: float
    security_score: float
    performance_score: float
    overall_score: float
    issues: List[Dict[str, Any]]
    recommendations: List[str]
    review_summary: str
    execution_time: float


class AdvancedCodeReviewer:
    """
    Advanced code reviewer that provides comprehensive code analysis.

    This class orchestrates multiple analysis engines to provide
    thorough code review capabilities including quality assessment,
    security analysis, performance evaluation, and PR integration.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the advanced code reviewer.

        Args:
            config: Configuration dictionary for the reviewer
        """
        self.config = config or self._load_default_config()

        # Initialize components
        self.yi_coder_integration = YiCoderIntegration(self.config.get("model", {}))
        self.security_analyzer = SecurityAnalyzer(self.config.get("security", {}))
        self.performance_analyzer = PerformanceAnalyzer(
            self.config.get("performance", {})
        )
        self.quality_metrics = QualityMetrics(self.config.get("quality", {}))
        self.pr_integration = PRIntegration(self.config.get("pr_integration", {}))
        self.review_dashboard = ReviewDashboard(self.config.get("dashboard", {}))

        logger.info("Advanced Code Reviewer initialized successfully")

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration for the code reviewer."""
        return {
            "enabled": True,
            "version": "1.0.0",
            "model": {
                "name": "yi-coder:1.5b",
                "temperature": 0.2,
                "max_tokens": 2048,
                "top_p": 0.95,
            },
            "features": {
                "code_quality_analysis": {
                    "enabled": True,
                    "metrics": ["maintainability", "complexity", "readability"],
                    "thresholds": {
                        "maintainability": 0.7,
                        "complexity": 0.8,
                        "readability": 0.8,
                    },
                },
                "security_review": {
                    "enabled": True,
                    "vulnerability_scanning": True,
                    "compliance_checking": True,
                    "severity_levels": ["critical", "high", "medium", "low"],
                },
                "performance_analysis": {
                    "enabled": True,
                    "bottleneck_detection": True,
                    "optimization_suggestions": True,
                    "resource_analysis": True,
                },
                "pr_integration": {
                    "enabled": True,
                    "automated_reviews": True,
                    "comment_generation": True,
                    "approval_recommendations": True,
                },
            },
            "languages": {
                "python": {"enabled": True, "priority": "high"},
                "javascript": {"enabled": True, "priority": "high"},
                "typescript": {"enabled": True, "priority": "high"},
                "java": {"enabled": True, "priority": "medium"},
                "cpp": {"enabled": True, "priority": "medium"},
            },
            "performance": {
                "max_response_time": 5.0,
                "cache_enabled": True,
                "cache_ttl": 600,
                "batch_size": 5,
            },
            "logging": {"level": "INFO", "file_enabled": True, "console_enabled": True},
        }

    async def review_code(self, request: CodeReviewRequest) -> CodeReviewResponse:
        """
        Perform comprehensive code review.

        Args:
            request: Code review request containing code and context

        Returns:
            CodeReviewResponse with analysis results
        """
        start_time = asyncio.get_event_loop().time()

        try:
            logger.info(
                f"Starting code review for {request.language} file: {request.file_path}"
            )

            # Perform quality analysis
            quality_results = await self._analyze_quality(request)

            # Perform security analysis
            security_results = await self._analyze_security(request)

            # Perform performance analysis
            performance_results = await self._analyze_performance(request)

            # Generate AI-powered insights
            ai_insights = await self._generate_ai_insights(
                request, quality_results, security_results, performance_results
            )

            # Calculate overall scores
            quality_score = quality_results.get("overall_score", 0.0)
            security_score = security_results.get("security_score", 0.0)
            performance_score = performance_results.get("performance_score", 0.0)
            overall_score = (quality_score + security_score + performance_score) / 3

            # Compile issues and recommendations
            issues = self._compile_issues(
                quality_results, security_results, performance_results
            )
            recommendations = self._compile_recommendations(
                quality_results, security_results, performance_results, ai_insights
            )

            # Generate review summary
            review_summary = await self._generate_review_summary(
                request, quality_score, security_score, performance_score, overall_score
            )

            execution_time = asyncio.get_event_loop().time() - start_time

            return CodeReviewResponse(
                quality_score=quality_score,
                security_score=security_score,
                performance_score=performance_score,
                overall_score=overall_score,
                issues=issues,
                recommendations=recommendations,
                review_summary=review_summary,
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Error in code review: {e}")
            raise

    async def _analyze_quality(self, request: CodeReviewRequest) -> Dict[str, Any]:
        """Analyze code quality using quality metrics."""
        try:
            return await self.quality_metrics.analyze_code_quality(
                code=request.code,
                language=request.language,
                file_path=request.file_path,
            )
        except Exception as e:
            logger.error(f"Error in quality analysis: {e}")
            return {"overall_score": 0.0, "issues": [], "recommendations": []}

    async def _analyze_security(self, request: CodeReviewRequest) -> Dict[str, Any]:
        """Analyze code security using security analyzer."""
        try:
            return await self.security_analyzer.analyze_security(
                code=request.code,
                language=request.language,
                file_path=request.file_path,
            )
        except Exception as e:
            logger.error(f"Error in security analysis: {e}")
            return {"security_score": 0.0, "vulnerabilities": [], "recommendations": []}

    async def _analyze_performance(self, request: CodeReviewRequest) -> Dict[str, Any]:
        """Analyze code performance using performance analyzer."""
        try:
            return await self.performance_analyzer.analyze_performance(
                code=request.code,
                language=request.language,
                file_path=request.file_path,
            )
        except Exception as e:
            logger.error(f"Error in performance analysis: {e}")
            return {"performance_score": 0.0, "bottlenecks": [], "recommendations": []}

    async def _generate_ai_insights(
        self,
        request: CodeReviewRequest,
        quality_results: Dict[str, Any],
        security_results: Dict[str, Any],
        performance_results: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Generate AI-powered insights using yi-coder."""
        try:
            return await self.yi_coder_integration.generate_review_insights(
                code=request.code,
                language=request.language,
                quality_results=quality_results,
                security_results=security_results,
                performance_results=performance_results,
            )
        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
            return {"insights": [], "recommendations": []}

    def _compile_issues(
        self,
        quality_results: Dict[str, Any],
        security_results: Dict[str, Any],
        performance_results: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Compile all issues from different analysis results."""
        issues = []

        # Add quality issues
        if "issues" in quality_results:
            issues.extend(quality_results["issues"])

        # Add security vulnerabilities
        if "vulnerabilities" in security_results:
            issues.extend(security_results["vulnerabilities"])

        # Add performance bottlenecks
        if "bottlenecks" in performance_results:
            issues.extend(performance_results["bottlenecks"])

        return issues

    def _compile_recommendations(
        self,
        quality_results: Dict[str, Any],
        security_results: Dict[str, Any],
        performance_results: Dict[str, Any],
        ai_insights: Dict[str, Any],
    ) -> List[str]:
        """Compile all recommendations from different analysis results."""
        recommendations = []

        # Add quality recommendations
        if "recommendations" in quality_results:
            recommendations.extend(quality_results["recommendations"])

        # Add security recommendations
        if "recommendations" in security_results:
            recommendations.extend(security_results["recommendations"])

        # Add performance recommendations
        if "recommendations" in performance_results:
            recommendations.extend(performance_results["recommendations"])

        # Add AI insights
        if "recommendations" in ai_insights:
            recommendations.extend(ai_insights["recommendations"])

        return list(set(recommendations))  # Remove duplicates

    async def _generate_review_summary(
        self,
        request: CodeReviewRequest,
        quality_score: float,
        security_score: float,
        performance_score: float,
        overall_score: float,
    ) -> str:
        """Generate a comprehensive review summary."""
        try:
            return await self.yi_coder_integration.generate_review_summary(
                code=request.code,
                language=request.language,
                quality_score=quality_score,
                security_score=security_score,
                performance_score=performance_score,
                overall_score=overall_score,
            )
        except Exception as e:
            logger.error(f"Error generating review summary: {e}")
            return f"Code review completed with overall score: {overall_score:.2f}"

    async def review_pull_request(self, pr_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Review a pull request and provide automated feedback.

        Args:
            pr_data: Pull request data including files, changes, and context

        Returns:
            Dictionary with PR review results
        """
        try:
            return await self.pr_integration.review_pull_request(pr_data)
        except Exception as e:
            logger.error(f"Error reviewing pull request: {e}")
            raise

    async def get_review_dashboard(self, project_id: str) -> Dict[str, Any]:
        """
        Get review dashboard data for a project.

        Args:
            project_id: Project identifier

        Returns:
            Dictionary with dashboard data
        """
        try:
            return await self.review_dashboard.get_dashboard_data(project_id)
        except Exception as e:
            logger.error(f"Error getting review dashboard: {e}")
            raise

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all components."""
        return {
            "status": "healthy",
            "version": "1.0.0",
            "components": {
                "yi_coder_integration": self.yi_coder_integration.get_health_status(),
                "security_analyzer": self.security_analyzer.get_health_status(),
                "performance_analyzer": self.performance_analyzer.get_health_status(),
                "quality_metrics": self.quality_metrics.get_health_status(),
                "pr_integration": self.pr_integration.get_health_status(),
                "review_dashboard": self.review_dashboard.get_health_status(),
            },
            "features_enabled": {
                "code_quality_analysis": self.config.get("features", {})
                .get("code_quality_analysis", {})
                .get("enabled", False),
                "security_review": self.config.get("features", {})
                .get("security_review", {})
                .get("enabled", False),
                "performance_analysis": self.config.get("features", {})
                .get("performance_analysis", {})
                .get("enabled", False),
                "pr_integration": self.config.get("features", {})
                .get("pr_integration", {})
                .get("enabled", False),
            },
        }
