#!/usr/bin/env python3
"""
AI Coding Agent - Unified Entrypoint (Legacy)
Phase 3.1: CLI/Web Workflow Integration

This file is being refactored into modular components.
For new implementations, use core.agents.AIAgent instead.
"""

# Import the new modular agent
from core.agents import <PERSON><PERSON><PERSON><PERSON>, Agent<PERSON>ogger, AIAgent, Error<PERSON><PERSON>ler, get_agent

# Re-export for backward compatibility
__all__ = ["AIAgent", "get_agent", "AgentError", "AgentLogger", "ErrorHandler"]
