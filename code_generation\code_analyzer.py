"""
Code Analyzer Module

Performs static code analysis, quality assessment, and provides insights
about code structure, complexity, and best practices.

Phase 19 Implementation - Enhanced Code Generation
"""

import ast
import logging
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class CodeMetrics:
    """Code quality metrics and analysis results."""

    lines_of_code: int
    cyclomatic_complexity: int
    function_count: int
    class_count: int
    comment_ratio: float
    maintainability_index: float
    code_smells: List[str]
    quality_score: float


@dataclass
class AnalysisResult:
    """Result of code analysis."""

    metrics: CodeMetrics
    issues: List[Dict[str, Any]]
    suggestions: List[str]
    language: str
    analysis_time: float


class CodeAnalyzer:
    """
    Static code analyzer for multiple programming languages.

    Provides:
    - Code quality metrics
    - Complexity analysis
    - Best practices validation
    - Code smell detection
    - Performance analysis
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the code analyzer.

        Args:
            config: Configuration dictionary for analysis settings
        """
        self.config = config or {}
        self.supported_languages = ["python", "javascript", "typescript", "java", "cpp"]
        self.health_status = "healthy"

        logger.info("Code Analyzer initialized successfully")

    async def analyze_code(
        self, code: str, language: str, file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive code analysis.

        Args:
            code: Source code to analyze
            language: Programming language
            file_path: Optional file path for context

        Returns:
            Dictionary with analysis results
        """
        try:
            if language not in self.supported_languages:
                raise ValueError(f"Unsupported language: {language}")

            # Perform language-specific analysis
            if language == "python":
                analysis_result = await self._analyze_python_code(code, file_path)
            elif language in ["javascript", "typescript"]:
                analysis_result = await self._analyze_javascript_code(
                    code, language, file_path
                )
            elif language == "java":
                analysis_result = await self._analyze_java_code(code, file_path)
            elif language == "cpp":
                analysis_result = await self._analyze_cpp_code(code, file_path)
            else:
                analysis_result = await self._analyze_generic_code(
                    code, language, file_path
                )

            return {
                "language": language,
                "metrics": analysis_result.metrics.__dict__,
                "issues": analysis_result.issues,
                "suggestions": analysis_result.suggestions,
                "analysis_time": analysis_result.analysis_time,
                "file_path": file_path,
            }

        except Exception as e:
            logger.error(f"Error analyzing code: {e}")
            raise

    async def _analyze_python_code(
        self, code: str, file_path: Optional[str]
    ) -> AnalysisResult:
        """Analyze Python code using AST."""
        import time

        start_time = time.time()

        try:
            # Parse the code
            tree = ast.parse(code)

            # Calculate metrics
            metrics = self._calculate_python_metrics(tree, code)

            # Detect issues
            issues = self._detect_python_issues(tree, code)

            # Generate suggestions
            suggestions = self._generate_python_suggestions(metrics, issues)

            analysis_time = time.time() - start_time

            return AnalysisResult(
                metrics=metrics,
                issues=issues,
                suggestions=suggestions,
                language="python",
                analysis_time=analysis_time,
            )

        except SyntaxError as e:
            logger.warning(f"Syntax error in Python code: {e}")
            return self._create_error_result("python", str(e))

    async def _analyze_javascript_code(
        self, code: str, language: str, file_path: Optional[str]
    ) -> AnalysisResult:
        """Analyze JavaScript/TypeScript code."""
        import time

        start_time = time.time()

        try:
            # Basic JavaScript analysis (in real implementation, would use a proper JS parser)
            metrics = self._calculate_javascript_metrics(code)
            issues = self._detect_javascript_issues(code)
            suggestions = self._generate_javascript_suggestions(metrics, issues)

            analysis_time = time.time() - start_time

            return AnalysisResult(
                metrics=metrics,
                issues=issues,
                suggestions=suggestions,
                language=language,
                analysis_time=analysis_time,
            )

        except Exception as e:
            logger.warning(f"Error analyzing JavaScript code: {e}")
            return self._create_error_result(language, str(e))

    async def _analyze_java_code(
        self, code: str, file_path: Optional[str]
    ) -> AnalysisResult:
        """Analyze Java code."""
        import time

        start_time = time.time()

        try:
            # Basic Java analysis (in real implementation, would use a proper Java parser)
            metrics = self._calculate_java_metrics(code)
            issues = self._detect_java_issues(code)
            suggestions = self._generate_java_suggestions(metrics, issues)

            analysis_time = time.time() - start_time

            return AnalysisResult(
                metrics=metrics,
                issues=issues,
                suggestions=suggestions,
                language="java",
                analysis_time=analysis_time,
            )

        except Exception as e:
            logger.warning(f"Error analyzing Java code: {e}")
            return self._create_error_result("java", str(e))

    async def _analyze_cpp_code(
        self, code: str, file_path: Optional[str]
    ) -> AnalysisResult:
        """Analyze C++ code."""
        import time

        start_time = time.time()

        try:
            # Basic C++ analysis (in real implementation, would use a proper C++ parser)
            metrics = self._calculate_cpp_metrics(code)
            issues = self._detect_cpp_issues(code)
            suggestions = self._generate_cpp_suggestions(metrics, issues)

            analysis_time = time.time() - start_time

            return AnalysisResult(
                metrics=metrics,
                issues=issues,
                suggestions=suggestions,
                language="cpp",
                analysis_time=analysis_time,
            )

        except Exception as e:
            logger.warning(f"Error analyzing C++ code: {e}")
            return self._create_error_result("cpp", str(e))

    async def _analyze_generic_code(
        self, code: str, language: str, file_path: Optional[str]
    ) -> AnalysisResult:
        """Analyze code in any supported language using generic methods."""
        import time

        start_time = time.time()

        try:
            # Generic analysis for any language
            metrics = self._calculate_generic_metrics(code)
            issues = self._detect_generic_issues(code)
            suggestions = self._generate_generic_suggestions(metrics, issues)

            analysis_time = time.time() - start_time

            return AnalysisResult(
                metrics=metrics,
                issues=issues,
                suggestions=suggestions,
                language=language,
                analysis_time=analysis_time,
            )

        except Exception as e:
            logger.warning(f"Error analyzing {language} code: {e}")
            return self._create_error_result(language, str(e))

    def _calculate_python_metrics(self, tree: ast.AST, code: str) -> CodeMetrics:
        """Calculate Python-specific metrics using AST."""
        lines_of_code = len(code.split("\n"))
        function_count = len(
            [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
        )
        class_count = len(
            [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
        )

        # Calculate cyclomatic complexity
        cyclomatic_complexity = self._calculate_cyclomatic_complexity(tree)

        # Calculate comment ratio
        comment_ratio = self._calculate_comment_ratio(code)

        # Calculate maintainability index
        maintainability_index = self._calculate_maintainability_index(
            lines_of_code, cyclomatic_complexity, comment_ratio
        )

        # Detect code smells
        code_smells = self._detect_python_code_smells(tree, code)

        # Calculate quality score
        quality_score = self._calculate_quality_score(
            maintainability_index, len(code_smells), comment_ratio
        )

        return CodeMetrics(
            lines_of_code=lines_of_code,
            cyclomatic_complexity=cyclomatic_complexity,
            function_count=function_count,
            class_count=class_count,
            comment_ratio=comment_ratio,
            maintainability_index=maintainability_index,
            code_smells=code_smells,
            quality_score=quality_score,
        )

    def _calculate_javascript_metrics(self, code: str) -> CodeMetrics:
        """Calculate JavaScript-specific metrics."""
        lines_of_code = len(code.split("\n"))
        function_count = len(
            re.findall(r"function\s+\w+|=>\s*{|const\s+\w+\s*=\s*\([^)]*\)\s*=>", code)
        )
        class_count = len(re.findall(r"class\s+\w+", code))

        # Simplified metrics for JavaScript
        cyclomatic_complexity = self._estimate_cyclomatic_complexity(code)
        comment_ratio = self._calculate_comment_ratio(code)
        maintainability_index = self._calculate_maintainability_index(
            lines_of_code, cyclomatic_complexity, comment_ratio
        )
        code_smells = self._detect_javascript_code_smells(code)
        quality_score = self._calculate_quality_score(
            maintainability_index, len(code_smells), comment_ratio
        )

        return CodeMetrics(
            lines_of_code=lines_of_code,
            cyclomatic_complexity=cyclomatic_complexity,
            function_count=function_count,
            class_count=class_count,
            comment_ratio=comment_ratio,
            maintainability_index=maintainability_index,
            code_smells=code_smells,
            quality_score=quality_score,
        )

    def _calculate_java_metrics(self, code: str) -> CodeMetrics:
        """Calculate Java-specific metrics."""
        lines_of_code = len(code.split("\n"))
        function_count = len(
            re.findall(
                r"(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\([^)]*\)\s*\{",
                code,
            )
        )
        class_count = len(re.findall(r"class\s+\w+", code))

        cyclomatic_complexity = self._estimate_cyclomatic_complexity(code)
        comment_ratio = self._calculate_comment_ratio(code)
        maintainability_index = self._calculate_maintainability_index(
            lines_of_code, cyclomatic_complexity, comment_ratio
        )
        code_smells = self._detect_java_code_smells(code)
        quality_score = self._calculate_quality_score(
            maintainability_index, len(code_smells), comment_ratio
        )

        return CodeMetrics(
            lines_of_code=lines_of_code,
            cyclomatic_complexity=cyclomatic_complexity,
            function_count=function_count,
            class_count=class_count,
            comment_ratio=comment_ratio,
            maintainability_index=maintainability_index,
            code_smells=code_smells,
            quality_score=quality_score,
        )

    def _calculate_cpp_metrics(self, code: str) -> CodeMetrics:
        """Calculate C++-specific metrics."""
        lines_of_code = len(code.split("\n"))
        function_count = len(re.findall(r"\w+\s+\w+\s*\([^)]*\)\s*\{", code))
        class_count = len(re.findall(r"class\s+\w+", code))

        cyclomatic_complexity = self._estimate_cyclomatic_complexity(code)
        comment_ratio = self._calculate_comment_ratio(code)
        maintainability_index = self._calculate_maintainability_index(
            lines_of_code, cyclomatic_complexity, comment_ratio
        )
        code_smells = self._detect_cpp_code_smells(code)
        quality_score = self._calculate_quality_score(
            maintainability_index, len(code_smells), comment_ratio
        )

        return CodeMetrics(
            lines_of_code=lines_of_code,
            cyclomatic_complexity=cyclomatic_complexity,
            function_count=function_count,
            class_count=class_count,
            comment_ratio=comment_ratio,
            maintainability_index=maintainability_index,
            code_smells=code_smells,
            quality_score=quality_score,
        )

    def _calculate_generic_metrics(self, code: str) -> CodeMetrics:
        """Calculate generic metrics for any language."""
        lines_of_code = len(code.split("\n"))
        cyclomatic_complexity = self._estimate_cyclomatic_complexity(code)
        comment_ratio = self._calculate_comment_ratio(code)
        maintainability_index = self._calculate_maintainability_index(
            lines_of_code, cyclomatic_complexity, comment_ratio
        )
        code_smells: List[str] = []
        quality_score = self._calculate_quality_score(
            maintainability_index, len(code_smells), comment_ratio
        )

        return CodeMetrics(
            lines_of_code=lines_of_code,
            cyclomatic_complexity=cyclomatic_complexity,
            function_count=0,
            class_count=0,
            comment_ratio=comment_ratio,
            maintainability_index=maintainability_index,
            code_smells=code_smells,
            quality_score=quality_score,
        )

    def _calculate_cyclomatic_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity from Python AST."""
        complexity = 1  # Base complexity

        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1

        return complexity

    def _estimate_cyclomatic_complexity(self, code: str) -> int:
        """Estimate cyclomatic complexity for non-Python languages."""
        complexity = 1  # Base complexity

        # Count control flow statements
        complexity += len(
            re.findall(r"\bif\b|\bwhile\b|\bfor\b|\bswitch\b|\bcase\b|\bcatch\b", code)
        )

        # Count logical operators
        complexity += len(re.findall(r"\b&&\b|\b\|\|\b|\band\b|\bor\b", code))

        return complexity

    def _calculate_comment_ratio(self, code: str) -> float:
        """Calculate the ratio of comment lines to total lines."""
        lines = code.split("\n")
        comment_lines = 0

        for line in lines:
            stripped = line.strip()
            if (
                stripped.startswith("//")
                or stripped.startswith("#")
                or stripped.startswith("/*")
            ):
                comment_lines += 1

        return comment_lines / len(lines) if lines else 0.0

    def _calculate_maintainability_index(
        self, loc: int, complexity: int, comment_ratio: float
    ) -> float:
        """Calculate maintainability index."""
        # Simplified maintainability index calculation
        base_score = 171 - 5.2 * complexity - 0.23 * loc - 16.2 * comment_ratio
        return max(0, min(100, base_score))

    def _calculate_quality_score(
        self, maintainability: float, code_smells: int, comment_ratio: float
    ) -> float:
        """Calculate overall code quality score."""
        # Weighted combination of various factors
        maintainability_weight = 0.4
        code_smells_weight = 0.4
        comment_weight = 0.2

        # Normalize code smells (fewer is better)
        normalized_smells = max(0, 1 - (code_smells / 10))

        # Normalize comment ratio (optimal around 0.2)
        normalized_comments = 1 - abs(comment_ratio - 0.2) / 0.2

        score = (
            maintainability_weight * (maintainability / 100)
            + code_smells_weight * normalized_smells
            + comment_weight * normalized_comments
        )

        return max(0, min(1, score))

    def _detect_python_issues(self, tree: ast.AST, code: str) -> List[Dict[str, Any]]:
        """Detect issues in Python code."""
        issues = []

        # Check for long functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if len(node.body) > 20:
                    issues.append(
                        {
                            "type": "long_function",
                            "severity": "warning",
                            "message": f"Function '{node.name}' is too long ({len(node.body)} statements)",
                            "line": node.lineno,
                        }
                    )

        # Check for unused imports (simplified)
        if "import" in code and "unused_import" in self.config.get("checks", []):
            issues.append(
                {
                    "type": "unused_import",
                    "severity": "info",
                    "message": "Potential unused imports detected",
                    "line": 1,
                }
            )

        return issues

    def _detect_javascript_issues(self, code: str) -> List[Dict[str, Any]]:
        """Detect issues in JavaScript code."""
        issues = []

        # Check for var usage (suggest const/let)
        if "var " in code:
            issues.append(
                {
                    "type": "var_usage",
                    "severity": "warning",
                    "message": "Consider using 'const' or 'let' instead of 'var'",
                    "line": 1,
                }
            )

        return issues

    def _detect_java_issues(self, code: str) -> List[Dict[str, Any]]:
        """Detect issues in Java code."""
        issues = []

        # Check for public fields
        if re.search(r"public\s+\w+\s+\w+\s*;", code):
            issues.append(
                {
                    "type": "public_field",
                    "severity": "warning",
                    "message": "Consider making fields private and providing getters/setters",
                    "line": 1,
                }
            )

        return issues

    def _detect_cpp_issues(self, code: str) -> List[Dict[str, Any]]:
        """Detect issues in C++ code."""
        issues = []

        # Check for raw pointers
        if "* " in code:
            issues.append(
                {
                    "type": "raw_pointer",
                    "severity": "warning",
                    "message": "Consider using smart pointers instead of raw pointers",
                    "line": 1,
                }
            )

        return issues

    def _detect_generic_issues(self, code: str) -> List[Dict[str, Any]]:
        """Detect generic issues in any language."""
        issues = []

        # Check for long lines
        lines = code.split("\n")
        for i, line in enumerate(lines, 1):
            if len(line) > 120:
                issues.append(
                    {
                        "type": "long_line",
                        "severity": "info",
                        "message": f"Line {i} is too long ({len(line)} characters)",
                        "line": i,
                    }
                )

        return issues

    def _detect_python_code_smells(self, tree: ast.AST, code: str) -> List[str]:
        """Detect code smells in Python code."""
        smells = []

        # Check for magic numbers
        if re.search(r"\b\d{3,}\b", code):
            smells.append("Magic numbers detected")

        # Check for long parameter lists
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and len(node.args.args) > 5:
                smells.append(f"Long parameter list in function '{node.name}'")

        return smells

    def _detect_javascript_code_smells(self, code: str) -> List[str]:
        """Detect code smells in JavaScript code."""
        smells = []

        # Check for callback hell
        if code.count("})") > 3:
            smells.append("Potential callback hell detected")

        return smells

    def _detect_java_code_smells(self, code: str) -> List[str]:
        """Detect code smells in Java code."""
        smells = []

        # Check for god classes
        if code.count("class") > 3:
            smells.append("Multiple classes in single file detected")

        return smells

    def _detect_cpp_code_smells(self, code: str) -> List[str]:
        """Detect code smells in C++ code."""
        smells = []

        # Check for memory leaks
        if "new " in code and "delete " not in code:
            smells.append("Potential memory leak detected")

        return smells

    def _generate_python_suggestions(
        self, metrics: CodeMetrics, issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate suggestions for Python code."""
        suggestions = []

        if metrics.cyclomatic_complexity > 10:
            suggestions.append(
                "Consider breaking down complex functions into smaller ones"
            )

        if metrics.comment_ratio < 0.1:
            suggestions.append("Add more comments to improve code documentation")

        if metrics.quality_score < 0.7:
            suggestions.append("Consider refactoring to improve code quality")

        return suggestions

    def _generate_javascript_suggestions(
        self, metrics: CodeMetrics, issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate suggestions for JavaScript code."""
        suggestions = []

        if metrics.cyclomatic_complexity > 10:
            suggestions.append("Consider using early returns to reduce complexity")

        return suggestions

    def _generate_java_suggestions(
        self, metrics: CodeMetrics, issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate suggestions for Java code."""
        suggestions = []

        if metrics.function_count > 20:
            suggestions.append("Consider splitting the class into smaller classes")

        return suggestions

    def _generate_cpp_suggestions(
        self, metrics: CodeMetrics, issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate suggestions for C++ code."""
        suggestions = []

        if metrics.cyclomatic_complexity > 15:
            suggestions.append("Consider using design patterns to reduce complexity")

        return suggestions

    def _generate_generic_suggestions(
        self, metrics: CodeMetrics, issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate generic suggestions for any language."""
        suggestions = []

        if metrics.lines_of_code > 500:
            suggestions.append("Consider splitting the file into smaller modules")

        return suggestions

    def _create_error_result(self, language: str, error_message: str) -> AnalysisResult:
        """Create an error result when analysis fails."""
        return AnalysisResult(
            metrics=CodeMetrics(
                lines_of_code=0,
                cyclomatic_complexity=0,
                function_count=0,
                class_count=0,
                comment_ratio=0.0,
                maintainability_index=0.0,
                code_smells=[],
                quality_score=0.0,
            ),
            issues=[
                {
                    "type": "analysis_error",
                    "severity": "error",
                    "message": error_message,
                    "line": 1,
                }
            ],
            suggestions=["Fix syntax errors before analysis"],
            language=language,
            analysis_time=0.0,
        )

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get the health status of the code analyzer.

        Returns:
            Dictionary with health status information
        """
        return {
            "status": self.health_status,
            "supported_languages": self.supported_languages,
            "config": self.config,
        }
