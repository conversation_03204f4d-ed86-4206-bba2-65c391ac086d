{"threat_detection_service": {"enabled": true, "port": 8085, "host": "0.0.0.0", "log_level": "INFO", "environment": "production"}, "container": {"name": "ai-coding-threat-detection", "image": "ai-coding-threat-detection:latest", "restart_policy": "unless-stopped", "resources": {"limits": {"cpus": "1.0", "memory": "2G"}, "reservations": {"cpus": "0.5", "memory": "1G"}}, "volumes": ["./data:/app/data", "./logs:/app/logs", "./backups:/app/backups", "./config:/app/config", "./models:/app/models", "./security:/app/security"], "environment_variables": {"PYTHONPATH": "/app", "THREAT_DETECTION_ENABLED": "true", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "DATABASE_URL": "******************************************************/ai_coding_agent", "OLLAMA_URL": "http://ollama:11434", "REDIS_URL": "redis://redis:6379"}}, "dependencies": {"required_services": ["api", "db", "redis", "ollama"], "health_check_dependencies": true}, "threat_detection_components": {"behavioral_analysis": {"enabled": true, "model": "behavioral_analysis_v1", "confidence_threshold": 0.8, "update_interval": 300}, "pattern_detection": {"enabled": true, "model": "pattern_detection_v2", "confidence_threshold": 0.75, "update_interval": 180}, "real_time_monitoring": {"enabled": true, "scan_interval": 30, "alert_threshold": 0.9, "max_alerts_per_minute": 10}, "alert_system": {"enabled": true, "notification_channels": ["log", "api"], "alert_retention_days": 30, "escalation_threshold": 5}}, "monitoring": {"health_check": {"endpoint": "/health", "interval": 30, "timeout": 10, "retries": 3, "start_period": 40}, "metrics": {"enabled": true, "endpoint": "/metrics", "collection_interval": 60}, "logging": {"level": "INFO", "format": "json", "output": "stdout"}}, "security": {"non_root_user": true, "user": "threatdetection", "group": "threatdetection", "capabilities": [], "read_only_root": false, "security_opt": ["no-new-privileges"]}, "networking": {"network": "ai-coding-network", "port_mapping": "8085:8085", "internal_port": 8085, "external_port": 8085}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 7, "backup_path": "/app/backups/threat-detection"}, "performance": {"max_concurrent_requests": 100, "request_timeout": 30, "connection_pool_size": 20, "cache_enabled": true, "cache_ttl": 300}, "api_endpoints": {"health": {"method": "GET", "path": "/health", "description": "Health check endpoint"}, "status": {"method": "GET", "path": "/status", "description": "Get threat detection system status"}, "threats_summary": {"method": "GET", "path": "/threats/summary", "description": "Get threats summary"}, "threat_alerts": {"method": "GET", "path": "/threats/alerts", "description": "Get threat alerts"}, "threat_components": {"method": "GET", "path": "/threats/components", "description": "Get threat detection components status"}, "optimize": {"method": "POST", "path": "/optimize", "description": "Optimize threat detection system"}, "export": {"method": "GET", "path": "/export", "description": "Export threat detection data"}, "import": {"method": "POST", "path": "/import", "description": "Import threat detection data"}, "approved_models": {"method": "GET", "path": "/approved-models", "description": "Get approved threat detection models"}, "validate_model": {"method": "POST", "path": "/validate-model", "description": "Validate threat detection model"}}}