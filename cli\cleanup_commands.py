#!/usr/bin/env python3
"""
Cleanup CLI Commands
Provides command-line interface for cleanup operations
"""

import sys
from pathlib import Path

import click

from scripts.cleanup_deployments import cleanup_old_deployments, remove_duplicate_files
from scripts.find_duplicates import find_duplicates

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@click.group()
def cleanup():
    """Cleanup commands for the AI Coding Agent project"""
    pass


@cleanup.command()
@click.option("--sites-dir", default="sites", help="Sites directory to clean")
@click.option("--days", default=7, help="Number of days to keep deployments")
@click.option("--force", is_flag=True, help="Force cleanup without confirmation")
def deployments(sites_dir, days, force):
    """Clean up old deployment directories"""
    click.echo(f"🧹 Cleaning up deployments older than {days} days...")

    if force:
        # For automated cleanup, we'll modify the function to not ask for confirmation
        click.echo("⚠️  Force mode enabled - no confirmation required")

    cleanup_old_deployments(sites_dir, days)


@cleanup.command()
@click.option("--force", is_flag=True, help="Force removal without confirmation")
def duplicates(force):
    """Remove duplicate files"""
    click.echo("🔍 Finding and removing duplicate files...")

    if force:
        click.echo("⚠️  Force mode enabled - no confirmation required")

    remove_duplicate_files()


@cleanup.command()
@click.option("--sites-dir", default="sites", help="Sites directory to clean")
@click.option("--days", default=7, help="Number of days to keep deployments")
@click.option("--force", is_flag=True, help="Force cleanup without confirmation")
def all(sites_dir, days, force):
    """Run all cleanup operations"""
    click.echo("🧹 Running complete cleanup...")

    # Clean deployments
    click.echo("\n1️⃣  Cleaning up old deployments...")
    cleanup_old_deployments(sites_dir, days)

    # Remove duplicates
    click.echo("\n2️⃣  Removing duplicate files...")
    remove_duplicate_files()

    click.echo("\n✨ Complete cleanup finished!")


@cleanup.command()
def check():
    """Check for cleanup opportunities"""
    click.echo("🔍 Checking for cleanup opportunities...")

    # Check for duplicates
    duplicates = find_duplicates()
    if duplicates:
        click.echo(f"❌ Found {len(duplicates)} sets of duplicate files")
        for i, dup_set in enumerate(duplicates, 1):
            click.echo(f"  Set {i}: {len(dup_set['files'])} files")
    else:
        click.echo("✅ No duplicate files found")

    # Check for old deployments
    sites_path = Path("sites")
    if sites_path.exists():
        import re
        from datetime import datetime, timedelta

        deployment_pattern = re.compile(r".*_\d{8}_\d{6}$")
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=7)

        old_deployments = []
        for item in sites_path.iterdir():
            if item.is_dir() and deployment_pattern.match(item.name):
                timestamp_str = (
                    item.name.split("_")[-2] + "_" + item.name.split("_")[-1]
                )
                try:
                    timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    if timestamp < cutoff_time:
                        old_deployments.append(item)
                except ValueError:
                    continue

        if old_deployments:
            click.echo(f"🗑️  Found {len(old_deployments)} old deployment directories")
        else:
            click.echo("✅ No old deployments found")

    click.echo("\n💡 Use 'cleanup all' to perform cleanup operations")


if __name__ == "__main__":
    cleanup()
