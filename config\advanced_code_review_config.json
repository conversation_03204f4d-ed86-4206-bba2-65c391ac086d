{"enabled": true, "version": "1.0.0", "model": {"name": "yi-coder:1.5b", "temperature": 0.2, "max_tokens": 2048, "top_p": 0.95}, "features": {"code_quality_analysis": {"enabled": true, "metrics": ["maintainability", "complexity", "readability"], "thresholds": {"maintainability": 0.7, "complexity": 0.8, "readability": 0.8}}, "security_review": {"enabled": true, "vulnerability_scanning": true, "compliance_checking": true, "severity_levels": ["critical", "high", "medium", "low"]}, "performance_analysis": {"enabled": true, "bottleneck_detection": true, "optimization_suggestions": true, "resource_analysis": true}, "pr_integration": {"enabled": true, "automated_reviews": true, "comment_generation": true, "approval_recommendations": true}}, "languages": {"python": {"enabled": true, "priority": "high"}, "javascript": {"enabled": true, "priority": "high"}, "typescript": {"enabled": true, "priority": "high"}, "java": {"enabled": true, "priority": "medium"}, "cpp": {"enabled": true, "priority": "medium"}}, "integration": {"github": {"enabled": true, "webhook_support": true, "api_integration": true}, "gitlab": {"enabled": true, "webhook_support": true, "api_integration": true}, "ci_cd": {"enabled": true, "pipeline_integration": true, "quality_gates": true}}, "performance": {"max_response_time": 5.0, "cache_enabled": true, "cache_ttl": 600, "batch_size": 5}, "logging": {"level": "INFO", "file_enabled": true, "console_enabled": true}}