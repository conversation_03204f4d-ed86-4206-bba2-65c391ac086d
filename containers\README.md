# AI Coding Agent - Docker Compose Setup

This directory contains the improved Docker Compose configuration for the AI Coding Agent system.

## 🚀 Key Improvements

### 1. **Environment Variables Management**
- ✅ **Docker Secrets**: Sensitive data (passwords, keys) are managed via Docker secrets
- ✅ **x-templates**: Common environment variables are defined once and reused
- ✅ **Centralized Configuration**: All environment variables are documented in `env.example`

### 2. **Security Enhancements**
- ✅ **Read-only Mounts**: Configuration files are mounted as read-only
- ✅ **Docker Secrets**: Passwords and keys are stored securely
- ✅ **Resource Limits**: CPU and memory limits prevent resource exhaustion
- ✅ **Network Isolation**: Custom network with specific subnet

### 3. **Volume Management**
- ✅ **Named Volumes**: All data is stored in named volumes for better portability
- ✅ **Persistent Storage**: Data persists across container restarts
- ✅ **Backup Strategy**: Dedicated backup service with retention policies

### 4. **Health Checks**
- ✅ **Service Dependencies**: Services wait for dependencies to be healthy
- ✅ **Automatic Recovery**: Failed services are automatically restarted
- ✅ **Monitoring Integration**: Health checks integrate with Prometheus/Grafana

### 5. **Logging**
- ✅ **Centralized Logging**: JSON-formatted logs with rotation
- ✅ **Log Retention**: Automatic log rotation (10MB max, 3 files)
- ✅ **Structured Logging**: Consistent log format across all services

## 📁 Directory Structure

```
containers/
├── docker-compose.yml          # Main Docker Compose configuration
├── docker-compose.dev.yml      # Development overrides
├── docker-compose.prod.yml     # Production overrides
├── secrets/                    # Docker secrets (sensitive data)
│   ├── db_password.txt
│   ├── secret_key.txt
│   ├── jwt_secret.txt
│   ├── grafana_password.txt
│   └── redis_password.txt
├── env.example                 # Environment variables template
└── README.md                   # This file
```

## 🔧 Setup Instructions

### 1. **Initial Setup**

```bash
# Clone the repository
git clone <repository-url>
cd AICodingAgent

# Create secrets directory
mkdir -p containers/secrets

# Generate secure passwords and keys
openssl rand -base64 32 > containers/secrets/db_password.txt
openssl rand -base64 32 > containers/secrets/secret_key.txt
openssl rand -base64 32 > containers/secrets/jwt_secret.txt
openssl rand -base64 32 > containers/secrets/grafana_password.txt
openssl rand -base64 32 > containers/secrets/redis_password.txt

# Copy environment template
cp containers/env.example containers/.env

# Edit environment variables
nano containers/.env
```

### 2. **Environment Configuration**

Edit `containers/.env` with your specific values:

```bash
# Database Configuration
DB_NAME=ai_coding_agent
DB_USER=ai_coding_user

# External Services
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Service Ports
FINE_TUNER_PORT=8002
VALIDATION_PORT=8004
```

### 3. **Starting Services**

```bash
# Start all services
docker-compose -f containers/docker-compose.yml up -d

# Start specific services
docker-compose -f containers/docker-compose.yml up -d api frontend db redis

# View logs
docker-compose -f containers/docker-compose.yml logs -f api

# Check service status
docker-compose -f containers/docker-compose.yml ps
```

### 4. **Development Mode**

```bash
# Start with development overrides
docker-compose -f containers/docker-compose.yml -f containers/docker-compose.dev.yml up -d
```

### 5. **Production Mode**

```bash
# Start with production overrides
docker-compose -f containers/docker-compose.yml -f containers/docker-compose.prod.yml up -d
```

## 🔍 Service Overview

### Core Services
- **api**: Main API service (port 8000)
- **frontend**: Next.js frontend (port 3000)
- **db**: PostgreSQL database (port 5432)
- **redis**: Redis cache (port 6379)
- **ollama**: AI model service (port 11434)

### Monitoring Services
- **monitoring**: Application monitoring (port 9090)
- **prometheus**: Metrics collection (port 9091)
- **grafana**: Dashboard (port 3001)
- **dashboard**: Custom dashboard (port 8080)

### Specialized Services
- **learning**: Learning system (port 8084)
- **fine-tuner**: Model fine-tuning (port 8002)
- **validation**: Code validation (port 8004)
- **security**: Security monitoring
- **threat-detection**: Threat detection
- **disaster-recovery**: Backup and recovery

### Infrastructure Services
- **nginx**: Reverse proxy (ports 80, 443)
- **scheduler**: Task scheduling
- **backup**: Automated backups
- **container_agent**: Container management
- **file_watcher**: File system monitoring
- **error_watcher**: Error monitoring
- **cursor_monitor**: Cursor rules monitoring

## 🔐 Security Features

### Docker Secrets
- Database passwords
- JWT secrets
- API keys
- Grafana passwords
- Redis passwords

### Network Security
- Isolated network (`ai-coding-network`)
- Custom subnet (`172.28.0.0/16`)
- Service-to-service communication only

### Resource Limits
- CPU limits per service
- Memory limits per service
- GPU allocation for AI services

## 📊 Monitoring

### Health Checks
- All services have health checks
- Automatic restart on failure
- Integration with monitoring stack

### Metrics
- Prometheus metrics collection
- Grafana dashboards
- Custom application metrics

### Logging
- Structured JSON logging
- Log rotation (10MB max, 3 files)
- Centralized log management

## 🚨 Troubleshooting

### Common Issues

1. **Service Won't Start**
   ```bash
   # Check logs
   docker-compose -f containers/docker-compose.yml logs <service-name>

   # Check health status
   docker-compose -f containers/docker-compose.yml ps
   ```

2. **Database Connection Issues**
   ```bash
   # Check database health
   docker-compose -f containers/docker-compose.yml exec db pg_isready

   # Check database logs
   docker-compose -f containers/docker-compose.yml logs db
   ```

3. **Volume Issues**
   ```bash
   # List volumes
   docker volume ls

   # Inspect volume
   docker volume inspect <volume-name>
   ```

4. **Network Issues**
   ```bash
   # Check network
   docker network ls
   docker network inspect ai-coding-network
   ```

### Debug Mode

```bash
# Start with debug logging
LOG_LEVEL=DEBUG docker-compose -f containers/docker-compose.yml up -d

# View all logs
docker-compose -f containers/docker-compose.yml logs -f
```

## 🔄 Maintenance

### Backup
```bash
# Manual backup
docker-compose -f containers/docker-compose.yml exec backup python backup.py

# Check backup status
docker-compose -f containers/docker-compose.yml logs backup
```

### Updates
```bash
# Pull latest images
docker-compose -f containers/docker-compose.yml pull

# Rebuild services
docker-compose -f containers/docker-compose.yml build --no-cache

# Restart services
docker-compose -f containers/docker-compose.yml restart
```

### Cleanup
```bash
# Remove stopped containers
docker-compose -f containers/docker-compose.yml down

# Remove volumes (WARNING: This will delete data)
docker-compose -f containers/docker-compose.yml down -v

# Remove images
docker-compose -f containers/docker-compose.yml down --rmi all
```

## 📈 Performance Tuning

### Resource Optimization
- Adjust CPU/memory limits in `docker-compose.yml`
- Monitor resource usage with `docker stats`
- Scale services as needed

### Database Optimization
- Configure PostgreSQL settings
- Monitor query performance
- Optimize indexes

### Cache Optimization
- Configure Redis settings
- Monitor cache hit rates
- Adjust TTL values

## 🤝 Contributing

When contributing to the Docker setup:

1. **Test Changes**: Always test in development environment
2. **Document Changes**: Update this README
3. **Security Review**: Ensure secrets are properly managed
4. **Performance Impact**: Consider resource usage changes

## 📞 Support

For issues and questions:

1. Check the troubleshooting section
2. Review service logs
3. Check health status
4. Consult the main project documentation

## 📄 License

This Docker setup is part of the AI Coding Agent project and follows the same license terms.
