/**
 * Learning Analytics Component
 * Displays learning analytics with charts and visualizations
 */

import React from 'react';
import { useLearningStore } from '../../store/learningStore';

export const LearningAnalytics: React.FC = () => {
  const { getAnalytics } = useLearningStore();
  const analytics = getAnalytics();

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Learning Analytics
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Performance Metrics */}
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-3">Performance Metrics</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Accuracy</span>
                <span className="text-sm font-medium text-gray-900">
                  {(analytics.accuracy * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${analytics.accuracy * 100}%` }}
                ></div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Response Time</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.responseTime.toFixed(2)}s
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${Math.min(analytics.responseTime * 10, 100)}%` }}
                ></div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">User Satisfaction</span>
                <span className="text-sm font-medium text-gray-900">
                  {(analytics.userSatisfaction * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full"
                  style={{ width: `${analytics.userSatisfaction * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Learning Statistics */}
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-3">Learning Statistics</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Patterns</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.totalPatterns.toLocaleString()}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">User Preferences</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.userPreferences.toLocaleString()}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Performance Insights</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.performanceInsights.toLocaleString()}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Recommendations Generated</span>
                <span className="text-sm font-medium text-gray-900">
                  {analytics.recommendationsGenerated.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Recommendations Priority Distribution */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-500 mb-3">Recommendations by Priority</h4>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {analytics.highPriorityRecommendations}
              </div>
              <div className="text-sm text-gray-500">High Priority</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {analytics.mediumPriorityRecommendations}
              </div>
              <div className="text-sm text-gray-500">Medium Priority</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {analytics.lowPriorityRecommendations}
              </div>
              <div className="text-sm text-gray-500">Low Priority</div>
            </div>
          </div>
        </div>

        {/* System Health */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-500 mb-3">System Health</h4>
          <div className="flex items-center">
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              analytics.systemHealth === 'healthy'
                ? 'bg-green-100 text-green-800'
                : analytics.systemHealth === 'warning'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {analytics.systemHealth}
            </div>
            <span className="ml-2 text-sm text-gray-600">
              {analytics.activeModels} active models
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
