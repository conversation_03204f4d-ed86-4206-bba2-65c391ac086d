absl-py==2.3.1
accelerate==1.9.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.15
aiosignal==1.4.0
alabaster==0.7.16
annotated-types==0.7.0
anyio==4.9.0
APScheduler==3.10.4
astor==0.8.1
attrs==25.3.0
Authlib==1.6.1
babel==2.17.0
bandit==1.7.5
bcrypt==4.3.0
beautifulsoup4==4.12.3
bidict==0.23.1
bitsandbytes==0.46.1
black==24.3.0
blinker==1.9.0
boolean.py==5.0
bs4==0.0.2
CacheControl==0.14.3
certifi==2025.7.14
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
configparser==6.0.1
coverage==7.9.2
cryptography==45.0.5
cyclonedx-python-lib==9.1.0
datasets==4.0.0
defusedxml==0.7.1
deprecation==2.1.0
dill==0.3.8
distlib==0.4.0
docker==7.1.0
docutils==0.20.1
dparse==0.6.4
ecdsa==0.19.1
fastapi==0.116.1
feedparser==6.0.11
filelock==3.16.1
flake8==7.0.0
Flask==3.1.1
flask-cors==6.0.1
Flask-SocketIO==5.5.1
frozenlist==1.7.0
fsspec==2025.3.0
gitdb==4.0.12
GitPython==3.1.44
gotrue==2.12.3
GPUtil==1.4.0
greenlet==3.2.3
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.27.0
huggingface-hub==0.34.3
hyperframe==6.1.0
identify==2.6.12
idna==3.10
imagesize==1.4.1
iniconfig==2.1.0
isort==5.13.2
itsdangerous==2.2.0
Jinja2==3.1.6
joblib==1.5.1
jsonlines==4.0.0
license-expression==30.4.4
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==4.0.0
mccabe==0.7.0
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.1
multidict==6.6.3
multiprocess==0.70.16
mypy==1.8.0
mypy_extensions==1.1.0
networkx==3.5
nltk==3.9.1
nodeenv==1.9.1
numpy==2.2.6
opencv-python==*********
packageurl-python==0.17.1
packaging==25.0
pandas==2.3.1
passlib==1.7.4
pathspec==0.12.1
pbr==6.1.1
peft==0.16.0
pillow==11.3.0
pip-api==0.0.34
pip-requirements-parser==32.0.1
pip_audit==2.9.0
platformdirs==4.3.8
playwright==1.54.0
pluggy==1.6.0
postgrest==1.1.1
pre-commit==3.6.0
propcache==0.3.2
protobuf==6.31.1
psutil==6.1.1
psycopg2-binary==2.9.10
py-serializable==2.1.0
pyarrow==21.0.0
pyasn1==0.4.8
pycodestyle==2.11.1
pycparser==2.22
pydantic>=2.11.7,<3.0.0
pydantic-settings>=2.8.0
pydantic_core>=2.33.2
pyee==13.0.0
pyflakes==3.2.0
Pygments==2.19.2
PyJWT==2.10.1
pynvml==11.5.0
pyparsing==3.2.3
pypng==0.20220715.0
pytesseract==0.3.13
pytest==8.0.0
pytest-asyncio==0.23.5
pytest-cov==4.1.0
pytest-mock==3.12.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
python-engineio==4.12.2
python-jose==3.4.0
python-multipart==0.0.18
python-socketio==5.13.0
pytz==2025.2
PyYAML==6.0.2
qrcode==7.4.2
realtime>=2.6.0
regex==2024.11.6
requests==2.32.4
rich==14.0.0
rouge_score==0.1.2
rsa==4.9.1
ruamel.yaml==0.18.14
ruamel.yaml.clib==0.2.12
safetensors==0.5.3
# safety and safety-schemas moved to requirements-dev.txt to avoid Pydantic conflicts
schedule==1.2.2
scikit-learn==1.7.1
scipy==1.16.1
sentry-sdk==2.34.0
setuptools==80.9.0
sgmllib3k==1.0.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
snowballstemmer==3.0.1
sortedcontainers==2.4.0
soupsieve==2.7
Sphinx==7.2.6
sphinx-rtd-theme==2.0.0
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.41
starlette==0.47.2
stevedore==5.4.1
storage3==0.12.0
StrEnum==0.4.15
supabase==2.17.0
supafunc==0.10.1
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tokenizers==0.21.4
toml==0.10.2
tomlkit==0.13.3
torch==2.7.1
tqdm==4.67.1
transformers==4.54.1
typer==0.16.0
types-passlib==1.7.7.20250602
types-psutil==7.0.0.20250601
types-pyasn1==0.6.0.20250516
types-python-jose==3.5.0.20250531
types-requests==2.32.4.20250611
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.5.0
uvicorn==0.35.0
virtualenv==20.31.2
wandb==0.21.0
watchdog==3.0.0
watchfiles==1.1.0
websockets==15.0
Werkzeug==3.1.3
wsproto==1.2.0
xxhash==3.5.0
yarl==1.20.1
