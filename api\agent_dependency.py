#!/usr/bin/env python3
"""
Global Agent Dependency Management

Provides a centralized way to manage the global AI agent instance
for use across all API routes.
"""

import logging
from typing import Any, Optional

logger = logging.getLogger(__name__)

# Global agent instance
_global_agent = None


def set_global_agent(agent):
    """Set the global agent instance"""
    global _global_agent
    _global_agent = agent
    logger.info("Global agent instance set successfully")


def get_global_agent():
    """Get the global agent instance"""
    return _global_agent


def get_agent():
    """Get the global agent instance (alias for compatibility)"""
    return _global_agent


def clear_global_agent():
    """Clear the global agent instance"""
    global _global_agent
    _global_agent = None
    logger.info("Global agent instance cleared")
