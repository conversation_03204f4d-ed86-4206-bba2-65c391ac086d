"""
Mock array_api_strict module for compatibility.

This module provides a mock implementation of array_api_strict for cases
where the actual module is not available.
"""

from typing import Any, Optional


class Device:
    """Mock Device class for array_api_strict compatibility."""

    def __init__(self, device_name: str):
        self.device_name = device_name

    def __str__(self) -> str:
        return self.device_name

    def __repr__(self) -> str:
        return f"Device('{self.device_name}')"

    def __eq__(self, other: Any) -> bool:
        if isinstance(other, Device):
            return self.device_name == other.device_name
        return False

    def __hash__(self) -> int:
        return hash(self.device_name)


# Create default devices
CPU_DEVICE = Device("CPU_DEVICE")
DEVICE1 = Device("device1")
DEVICE2 = Device("device2")


def get_device(device_name: str) -> Device:
    """Get a device by name."""
    return Device(device_name)


# Export the main classes and constants
__all__ = ["Device", "CPU_DEVICE", "DEVICE1", "DEVICE2", "get_device"]
