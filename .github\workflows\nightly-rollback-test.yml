name: Nightly Rollback Test
true:
  schedule:
  - cron: 0 2 * * *
  workflow_dispatch: null
jobs:
  rollback-test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        cache: npm
        node-version: '18'
    - name: Install Python dependencies
      run: 'python -m pip install --upgrade pip

        pip install -r requirements.txt

        '
    - name: Install Node.js dependencies
      run: 'npm ci

        '
    - name: Create test deployment
      run: '# Create a test deployment to ensure we have something to test

        mkdir -p deployments/test-site_$(date +%Y%m%d_%H%M%S)

        echo ''{"site": "test-site", "timestamp": "''$(date +%Y%m%d_%H%M%S)''", "source":
        "sites/test-site", "destination": "deployments/test-site_''$(date +%Y%m%d_%H%M%S)''"}''
        > deployments/test-site_$(date +%Y%m%d_%H%M%S)/deployment.json

        '
    - name: Create test site
      run: 'mkdir -p sites/test-site

        echo ''<!DOCTYPE html><html><head><title>Test Site</title></head><body><h1>Test
        Site</h1><p>This is a test site for rollback testing.</p></body></html>''
        > sites/test-site/index.html

        '
    - name: Run nightly rollback test
      run: 'python scripts/nightly_rollback_test.py

        '
    - if: always()
      name: Upload test results
      uses: actions/upload-artifact@v4
      with:
        name: rollback-test-results-${{ github.run_number }}
        path: 'logs/rollback_tests/

          logs/nightly_rollback_test_*.log

          '
        retention-days: 30
    - if: failure()
      name: Create issue on test failure
      uses: actions/github-script@v7
      with:
        script: "const fs = require('fs');\nconst path = require('path');\n\n// Find\
          \ the most recent test log\nconst logDir = 'logs';\nconst logFiles = fs.readdirSync(logDir)\n\
          \  .filter(file => file.startsWith('nightly_rollback_test_') && file.endsWith('.log'))\n\
          \  .sort()\n  .reverse();\n\nlet logContent = 'No log file found';\nif (logFiles.length\
          \ > 0) {\n  logContent = fs.readFileSync(path.join(logDir, logFiles[0]),\
          \ 'utf8');\n}\n\n// Create issue\nawait github.rest.issues.create({\n  owner:\
          \ context.repo.owner,\n  repo: context.repo.repo,\n  title: '\U0001F6A8\
          \ Nightly Rollback Test Failed',\n  body: `## Nightly Rollback Test Failure\n\
          \n  The nightly rollback test failed on ${new Date().toISOString()}.\n\n\
          \  **Run Details:**\n  - Workflow: ${context.workflow}\n  - Run ID: ${context.runId}\n\
          \  - Commit: ${context.sha}\n\n  **Log Output:**\n  \\`\\`\\`\n  ${logContent}\n\
          \  \\`\\`\\`\n\n  **Next Steps:**\n  1. Review the test results in the artifacts\n\
          \  2. Check the deployment snapshots\n  3. Investigate any server startup\
          \ issues\n  4. Verify the test endpoints are accessible\n\n  **Manual Test\
          \ Command:**\n  \\`\\`\\`bash\n  python scripts/nightly_rollback_test.py\n\
          \  \\`\\`\\`\n\n  **Pipeline Test Command:**\n  \\`\\`\\`bash\n  python\
          \ src/home_server_pipeline.py rollback-test\n  \\`\\`\\`\n  `,\n  labels:\
          \ ['bug', 'rollback-test', 'nightly']\n});\n"
    - if: github.event_name == 'pull_request'
      name: Comment on PR if triggered by PR
      uses: actions/github-script@v7
      with:
        script: "await github.rest.issues.createComment({\n  owner: context.repo.owner,\n\
          \  repo: context.repo.repo,\n  issue_number: context.issue.number,\n  body:\
          \ `## \U0001F504 Rollback Test Results\n\n  The rollback test has been executed\
          \ for this PR.\n\n  **Status:** ${job.status === 'success' ? '\u2705 PASSED'\
          \ : '\u274C FAILED'}\n\n  Check the workflow run for detailed results and\
          \ artifacts.\n  `\n});\n"
'on':
- push
- pull_request
