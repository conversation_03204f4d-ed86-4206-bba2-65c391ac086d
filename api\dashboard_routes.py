#!/usr/bin/env python3
"""
Dashboard API Routes
Provides REST API endpoints for dashboard backend operations
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional

import psutil
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["Dashboard"])


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove dead connections
                self.active_connections.remove(connection)


manager = ConnectionManager()


# Pydantic models
class DashboardSummary(BaseModel):
    total_services: int
    active_services: int
    system_health: str
    uptime: str
    last_update: datetime


class DashboardMetrics(BaseModel):
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    active_connections: int


class Notification(BaseModel):
    id: str
    type: str
    message: str
    timestamp: datetime
    priority: str
    read: bool = False


class RealTimeUpdate(BaseModel):
    type: str
    data: Dict[str, Any]
    timestamp: datetime


# Health check endpoint
@router.get("/health")
async def health_check():
    """Dashboard health check endpoint"""
    try:
        # Basic health checks
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")

        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "dashboard-backend",
            "version": "1.0.0",
            "metrics": {
                "cpu_usage": cpu_usage,
                "memory_usage": memory.percent,
                "disk_usage": disk.percent,
                "active_connections": len(manager.active_connections),
            },
        }

        return JSONResponse(content=health_status, status_code=200)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# Dashboard summary endpoint
@router.get("/summary")
async def get_dashboard_summary():
    """Get dashboard summary"""
    try:
        # Get system information
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        # Count services (simplified - in real implementation, check actual services)
        total_services = 10  # Example: API, DB, Redis, etc.
        active_services = 8  # Example: running services

        summary = DashboardSummary(
            total_services=total_services,
            active_services=active_services,
            system_health="healthy",
            uptime=str(uptime),
            last_update=datetime.now(),
        )

        return summary
    except Exception as e:
        logger.error(f"Failed to get dashboard summary: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {str(e)}")


# Dashboard metrics endpoint
@router.get("/metrics")
async def get_dashboard_metrics():
    """Get dashboard metrics"""
    try:
        # Get system metrics
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")
        network = psutil.net_io_counters()

        metrics = DashboardMetrics(
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=disk.percent,
            network_io={
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv,
            },
            active_connections=len(manager.active_connections),
        )

        return metrics
    except Exception as e:
        logger.error(f"Failed to get dashboard metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


# Notifications endpoint
@router.get("/notifications")
async def get_notifications():
    """Get dashboard notifications"""
    try:
        # Example notifications (in real implementation, fetch from database)
        notifications = [
            Notification(
                id="1",
                type="info",
                message="System backup completed successfully",
                timestamp=datetime.now(),
                priority="low",
            ),
            Notification(
                id="2",
                type="warning",
                message="High CPU usage detected",
                timestamp=datetime.now(),
                priority="medium",
            ),
        ]

        return {"notifications": notifications}
    except Exception as e:
        logger.error(f"Failed to get notifications: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get notifications: {str(e)}"
        )


# Real-time status endpoint
@router.get("/real-time-status")
async def get_real_time_status():
    """Get real-time update status"""
    try:
        status = {
            "websocket_enabled": True,
            "active_connections": len(manager.active_connections),
            "last_update": datetime.now().isoformat(),
            "update_frequency": "1s",
            "supported_events": ["metrics", "notifications", "alerts", "system_status"],
        }

        return status
    except Exception as e:
        logger.error(f"Failed to get real-time status: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get real-time status: {str(e)}"
        )


# Configuration endpoint
@router.get("/config")
async def get_dashboard_config():
    """Get dashboard configuration"""
    try:
        config = {
            "websocket_enabled": os.getenv("WEBSOCKET_ENABLED", "true").lower()
            == "true",
            "real_time_updates": os.getenv("REAL_TIME_UPDATES", "true").lower()
            == "true",
            "notifications_enabled": os.getenv("NOTIFICATIONS_ENABLED", "true").lower()
            == "true",
            "metrics_collection": os.getenv("METRICS_COLLECTION", "true").lower()
            == "true",
            "update_interval": "1s",
            "max_connections": 100,
            "port": int(os.getenv("DASHBOARD_PORT", "8088")),
        }

        return config
    except Exception as e:
        logger.error(f"Failed to get dashboard config: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get config: {str(e)}")


# Export endpoint
@router.get("/export")
async def export_dashboard_data(format: str = "json"):
    """Export dashboard data"""
    try:
        # Get all dashboard data
        summary = await get_dashboard_summary()
        metrics = await get_dashboard_metrics()
        notifications = await get_notifications()

        export_data = {
            "summary": summary.dict(),
            "metrics": metrics.dict(),
            "notifications": notifications,
            "export_timestamp": datetime.now().isoformat(),
            "format": format,
        }

        if format.lower() == "json":
            return export_data
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {format}")

    except Exception as e:
        logger.error(f"Failed to export dashboard data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export data: {str(e)}")


# WebSocket endpoint for real-time updates
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time dashboard updates"""
    await manager.connect(websocket)
    try:
        while True:
            # Send periodic updates
            await asyncio.sleep(1)

            # Get current metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            update = RealTimeUpdate(
                type="metrics_update",
                data={
                    "cpu_usage": cpu_usage,
                    "memory_usage": memory.percent,
                    "active_connections": len(manager.active_connections),
                },
                timestamp=datetime.now(),
            )

            await manager.send_personal_message(update.json(), websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)


# Root endpoint
@router.get("/")
async def dashboard_root():
    """Dashboard root endpoint"""
    return {
        "service": "dashboard-backend",
        "version": "1.0.0",
        "status": "running",
        "endpoints": [
            "/health",
            "/summary",
            "/metrics",
            "/notifications",
            "/real-time-status",
            "/config",
            "/export",
            "/ws",
        ],
        "documentation": "/docs",
    }
