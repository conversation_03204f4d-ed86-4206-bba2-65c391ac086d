import React, { useState, useRef, useEffect, memo, useCallback } from 'react';
import { Send, Bot, User, Sparkles, Loader2, Settings, ChevronDown, LogIn } from 'lucide-react';
import { EnhanceButtonWithPrompt } from './EnhanceButton';
import { aiService } from '@/services/AIService';
import { intentRecognition } from '@/services/IntentRecognition';
import { conversationManager } from '@/services/ConversationManager';
import { promptEnhancer } from '@/services/PromptEnhancer';
import { getFileManager, EditorFile } from '@/services/FileManager';
import { useAuth } from '@/contexts/AuthContext';
import { LoginModal } from '@/components/auth/LoginModal';
import toast from 'react-hot-toast';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
  intent?: any;
  suggestions?: string[];
}

interface ChatPanelProps {
  className?: string;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({ className = '' }) => {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI coding assistant. I can help you create websites, modify code, and answer questions. What would you like to build today?',
      timestamp: new Date(),
      suggestions: [
        'Create a photography portfolio website',
        'Build a business landing page',
        'Add a contact form to my website',
        'Deploy my website online'
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [autoEnhance, setAutoEnhance] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Initialize conversation session
  useEffect(() => {
    conversationManager.startSession('Untitled Project');
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    // Check authentication
    if (!isAuthenticated) {
      setShowLoginModal(true);
      return;
    }

    let finalPrompt = inputValue;
    let enhancementMode = 'code_generation';
    let codeContext = '';

    // Auto-enhance if enabled
    if (autoEnhance) {
      try {
        enhancementMode = promptEnhancer.detectEnhancementMode(inputValue);
        const result = await promptEnhancer.enhancePrompt(inputValue, enhancementMode);
        if (result.confidence > 0.3) {
          finalPrompt = result.enhanced;
        }
      } catch (error) {
        console.error('ChatPanel: Auto-enhancement failed:', {
          error: error instanceof Error ? error.message : String(error),
          inputValue,
          enhancementMode,
          timestamp: new Date().toISOString()
        });
        toast.error('Auto-enhancement failed.');
      }
    }

    // Extract code context if intent-specific
    if (["code_modification", "debugging", "code_generation"].includes(enhancementMode)) {
      // Use FileManager to get the active file
      const fileManager = getFileManager();
      const files = fileManager.getAllFiles();
      const activeFile = files.find((f: EditorFile) => f.isActive);
      if (activeFile && activeFile.content) {
        codeContext = activeFile.content;
      }
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setErrorMessage(null);

    let timeoutId: NodeJS.Timeout | null = setTimeout(() => {
      setErrorMessage('AI is unavailable. Please try again later.');
      setIsLoading(false);
    }, 10000);

    try {
      // Parse the command
      const parsedCommand = intentRecognition.parseCommand(finalPrompt);

      // Update AI context
      aiService.updateContext(conversationManager.getContextForAI());

      // Generate AI response
      const useCase = parsedCommand.intent.type === 'create' ? 'code' :
                     parsedCommand.intent.type === 'modify' ? 'code' :
                     parsedCommand.intent.type === 'deploy' ? 'intent' :
                     parsedCommand.intent.type === 'maintain' ? 'review' : 'intent';

      const aiResponse = await aiService.generateResponse(
        finalPrompt,
        useCase,
        undefined,
        codeContext,
        parsedCommand.intent,
        conversationManager.getContextForAI().conversationHistory
      );

      if (timeoutId) clearTimeout(timeoutId);

      // Add turn to conversation manager
      await conversationManager.addTurn(inputValue, parsedCommand, aiResponse);

      // Get next action suggestions
      const suggestions = conversationManager.suggestNextActions();

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date(),
        intent: parsedCommand.intent,
        suggestions
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error: any) {
      if (timeoutId) clearTimeout(timeoutId);

      // Log error for debugging
      console.error('ChatPanel: Error during AI response generation:', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        userInput: inputValue,
        finalPrompt
      });

      // Handle authentication errors
      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        setErrorMessage('Please log in to use the AI chat feature.');
        setShowLoginModal(true);
      } else {
        setErrorMessage('AI is unavailable. Please try again later.');
        toast.error('AI response error.');
      }

      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = useCallback((date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  const MessageItem = memo(({ message }: { message: ChatMessage }) => (
    <div
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
      role="listitem"
    >
      <div
        className={`flex max-w-[80%] ${
          message.type === 'user'
            ? 'bg-blue-500 text-white'
            : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
        } rounded-lg px-3 py-2`}
        role="article"
        aria-label={`${message.type === 'user' ? 'User' : 'AI'} message`}
      >
        <div className="flex items-start space-x-2">
          {message.type === 'ai' && (
            <Bot className="w-4 h-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
          )}
          <div className="flex-1">
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
            <p className="text-xs opacity-70 mt-1" aria-label="Message timestamp">
              {formatTime(message.timestamp)}
            </p>

            {/* Show suggestions for AI messages */}
            {message.type === 'ai' && message.suggestions && message.suggestions.length > 0 && (
              <div className="mt-3 space-y-1" role="region" aria-label="Suggested actions">
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  Suggested next actions:
                </p>
                <div className="flex flex-wrap gap-1" role="group" aria-label="Action suggestions">
                  {message.suggestions.slice(0, 3).map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                      aria-label={`Use suggestion: ${suggestion}`}
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
          {message.type === 'user' && (
            <User className="w-4 h-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
          )}
        </div>
      </div>
    </div>
  ));

  const handleEnhance = (enhancedPrompt: string) => {
    setInputValue(enhancedPrompt);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  return (
    <div className={`flex flex-col h-full ${className}`} role="region" aria-label="AI Chat Assistant">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100" id="chat-header">
          AI Assistant
        </h3>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
            title="Settings"
            aria-label="Toggle chat settings"
            aria-expanded={showSettings}
            aria-controls="chat-settings-panel"
          >
            <Settings className="w-4 h-4" aria-hidden="true" />
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div
          id="chat-settings-panel"
          className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800"
          role="region"
          aria-labelledby="chat-header"
        >
          <div className="flex items-center justify-between text-sm">
            <label className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
              <input
                type="checkbox"
                checked={autoEnhance}
                onChange={(e) => setAutoEnhance(e.target.checked)}
                className="w-4 h-4 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                aria-describedby="auto-enhance-description"
              />
              <Sparkles className="w-4 h-4" aria-hidden="true" />
              <span id="auto-enhance-description">Auto-enhance prompts</span>
            </label>
          </div>
        </div>
      )}

      {/* Messages */}
      <div
        className="flex-1 overflow-y-auto p-3 space-y-4"
        role="log"
        aria-label="Chat messages"
        aria-live="polite"
        aria-atomic="false"
      >
        {messages.map((message) => (
          <MessageItem key={message.id} message={message} />
        ))}

        {isLoading && (
          <div className="flex justify-start" role="status" aria-live="polite">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2">
              <div className="flex items-center space-x-2">
                <Bot className="w-4 h-4" aria-hidden="true" />
                <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" />
                <span className="text-sm">AI is thinking...</span>
              </div>
            </div>
          </div>
        )}

        {errorMessage && (
          <div className="text-red-500 text-center my-2" role="alert" aria-live="assertive">
            {errorMessage}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

            {/* Input */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3" role="region" aria-label="Chat input">
        {!isAuthenticated ? (
          <div className="flex flex-col items-center justify-center p-6 space-y-4">
            <div className="text-center">
              <LogIn className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Login Required
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Please log in to use the AI chat feature and access your projects.
              </p>
              <button
                onClick={() => setShowLoginModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                Login
              </button>
            </div>
          </div>
        ) : (
          <div className="flex space-x-2">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Describe what you want to create or modify..."
              className="flex-1 resize-none border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
              rows={3}
              disabled={isLoading}
              aria-label="Chat message input"
              aria-describedby="input-instructions"
            />
            <div className="flex flex-col space-y-2" role="group" aria-label="Chat actions">
              <EnhanceButtonWithPrompt
                prompt={inputValue}
                onEnhance={handleEnhance}
                disabled={!inputValue.trim() || isLoading}
                size="sm"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                aria-label="Send message"
                aria-describedby={!inputValue.trim() || isLoading ? 'send-button-error' : undefined}
              >
                <Send className="w-4 h-4" aria-hidden="true" />
              </button>
              {(!inputValue.trim() || isLoading) && (
                <span id="send-button-error" className="sr-only">
                  Please enter a message to send
                </span>
              )}
            </div>
          </div>
        )}

        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400" id="input-instructions">
          Press Enter to send, Shift+Enter for new line
          {autoEnhance && (
            <span className="ml-2 text-blue-500" role="status" aria-live="polite">
              ✨ Auto-enhance enabled
            </span>
          )}
        </div>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onSuccess={() => {
          setErrorMessage(null);
          toast.success('Login successful! You can now use the AI chat.');
        }}
      />
    </div>
  );
};
