import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict

import docker
import uvicorn
from fastapi import FastAP<PERSON>
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

from core.site_container_manager import SiteContainerManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app for health check
app = FastAPI(title="File Watcher Agent")


@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "file_watcher_agent"}


@app.get("/status")
def get_status():
    """Get watcher status"""
    return {
        "watching": (
            len(file_watcher.watched_sites) if "file_watcher" in globals() else 0
        ),
        "sites": (
            list(file_watcher.watched_sites.keys())
            if "file_watcher" in globals()
            else []
        ),
    }


class SiteChangeHandler(FileSystemEventHandler):
    """Handler for file system events in site directories"""

    def __init__(self, site_name: str, site_container_manager: SiteContainerManager):
        self.site_name = site_name
        self.site_container_manager = site_container_manager
        self.last_rebuild = 0
        self.rebuild_cooldown = 5  # 5 seconds cooldown between rebuilds

    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return

        # Skip certain file types
        ignored_extensions = {".log", ".tmp", ".swp", ".pyc", "__pycache__"}
        file_path = Path(event.src_path)

        if any(part.startswith(".") for part in file_path.parts):
            return  # Skip hidden files/directories

        if file_path.suffix in ignored_extensions:
            return

        current_time = time.time()
        if current_time - self.last_rebuild < self.rebuild_cooldown:
            return  # Still in cooldown

        logger.info(f"File changed in {self.site_name}: {event.src_path}")
        self._trigger_rebuild()

    def on_created(self, event):
        """Handle file creation events"""
        self.on_modified(event)

    def _trigger_rebuild(self):
        """Trigger a site container rebuild"""
        try:
            self.last_rebuild = time.time()
            logger.info(f"Triggering rebuild for site: {self.site_name}")

            # Call the site container manager to rebuild
            result = self.site_container_manager.rebuild_site_container(self.site_name)

            if result.get("success"):
                logger.info(f"Successfully triggered rebuild for {self.site_name}")
            else:
                logger.error(
                    f"Failed to rebuild {self.site_name}: {result.get('error', 'Unknown error')}"
                )

        except Exception as e:
            logger.error(f"Error triggering rebuild for {self.site_name}: {str(e)}")


class FileWatcherAgent:
    """Agent that monitors site directories for changes and triggers rebuilds"""

    def __init__(self, sites_directory: str = "sites"):
        self.sites_directory = Path(sites_directory)
        self.observer = Observer()
        self.watched_sites: Dict[str, Any] = {}
        self.site_container_manager = SiteContainerManager()

        # Ensure sites directory exists
        self.sites_directory.mkdir(exist_ok=True)

        logger.info(f"FileWatcherAgent initialized, monitoring: {self.sites_directory}")

    def start_watching(self):
        """Start watching all existing sites and begin monitoring"""
        self._discover_existing_sites()
        self.observer.start()
        logger.info("FileWatcherAgent started watching for changes")

    def stop_watching(self):
        """Stop watching for changes"""
        self.observer.stop()
        self.observer.join()
        logger.info("FileWatcherAgent stopped watching")

    def _discover_existing_sites(self):
        """Discover and start watching existing site directories"""
        if not self.sites_directory.exists():
            logger.warning(f"Sites directory {self.sites_directory} does not exist")
            return

        for site_dir in self.sites_directory.iterdir():
            if site_dir.is_dir() and not site_dir.name.startswith("."):
                self.add_site_watch(site_dir.name)

    def add_site_watch(self, site_name: str):
        """Add a new site to be watched"""
        site_path = self.sites_directory / site_name

        if not site_path.exists():
            logger.warning(f"Site directory {site_path} does not exist")
            return

        if site_name in self.watched_sites:
            logger.info(f"Site {site_name} is already being watched")
            return

        try:
            handler = SiteChangeHandler(site_name, self.site_container_manager)
            watch = self.observer.schedule(handler, str(site_path), recursive=True)

            self.watched_sites[site_name] = {
                "handler": handler,
                "watch": watch,
                "path": str(site_path),
            }

            logger.info(f"Started watching site: {site_name} at {site_path}")

        except Exception as e:
            logger.error(f"Failed to start watching {site_name}: {str(e)}")

    def remove_site_watch(self, site_name: str):
        """Remove a site from being watched"""
        if site_name not in self.watched_sites:
            logger.warning(f"Site {site_name} is not being watched")
            return

        try:
            watch_info = self.watched_sites[site_name]
            self.observer.unschedule(watch_info["watch"])
            del self.watched_sites[site_name]

            logger.info(f"Stopped watching site: {site_name}")

        except Exception as e:
            logger.error(f"Failed to stop watching {site_name}: {str(e)}")


# Global file watcher instance
file_watcher = None


def run_file_watcher():
    """Run the file watcher agent"""
    global file_watcher

    try:
        file_watcher = FileWatcherAgent()
        file_watcher.start_watching()

        # Keep the watcher running
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"FileWatcherAgent error: {str(e)}")
    finally:
        if file_watcher:
            file_watcher.stop_watching()


if __name__ == "__main__":
    import threading

    # Start the file watcher in a separate thread
    watcher_thread = threading.Thread(target=run_file_watcher, daemon=True)
    watcher_thread.start()

    # Run the FastAPI app
    uvicorn.run(app, host="0.0.0.0", port=8090)
