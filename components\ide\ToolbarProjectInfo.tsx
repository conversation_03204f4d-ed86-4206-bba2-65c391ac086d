import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, GitBranch, Sun, <PERSON>, Monitor, Menu } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';

interface ToolbarProjectInfoProps {
  currentProject: string;
  onProjectChange: (project: string) => void;
  onMobileMenuToggle?: () => void;
}

export const ToolbarProjectInfo: React.FC<ToolbarProjectInfoProps> = ({
  currentProject,
  onProjectChange,
  onMobileMenuToggle
}) => {
  const { theme, toggleTheme, isDark } = useTheme();

  const getThemeIcon = () => {
    if (theme === 'system') {
      return <Monitor className="w-4 h-4" aria-hidden="true" />;
    }
    return isDark ? <Sun className="w-4 h-4" aria-hidden="true" /> : <Moon className="w-4 h-4" aria-hidden="true" />;
  };

  const getThemeLabel = () => {
    if (theme === 'system') return 'System theme';
    return isDark ? 'Switch to light theme' : 'Switch to dark theme';
  };

    return (
    <div className="flex items-center space-x-4" role="group" aria-label="Project information">
      {/* Mobile menu button */}
      {onMobileMenuToggle && (
        <button
          onClick={onMobileMenuToggle}
          className="md:hidden p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          title="Toggle sidebar menu"
          aria-label="Toggle sidebar menu"
        >
          <Menu className="w-4 h-4" aria-hidden="true" />
        </button>
      )}

      <div className="flex items-center space-x-2">
        <FolderOpen className="w-4 h-4 text-gray-500" aria-hidden="true" />
        <label htmlFor="project-name" className="sr-only">Project name</label>
        <input
          id="project-name"
          type="text"
          value={currentProject}
          onChange={(e) => onProjectChange(e.target.value)}
          className="text-sm font-medium bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 text-gray-900 dark:text-gray-100"
          aria-label="Project name"
        />
      </div>
    <div className="flex items-center space-x-1" aria-label="Current branch">
      <GitBranch className="w-3 h-3 text-gray-400" aria-hidden="true" />
      <span className="text-xs text-gray-500">main</span>
    </div>
    <button
      onClick={toggleTheme}
      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
      title={getThemeLabel()}
      aria-label={getThemeLabel()}
    >
      {getThemeIcon()}
    </button>
  </div>
  );
};
