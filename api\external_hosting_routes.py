"""
External Hosting API Routes
Provides REST API endpoints for external hosting exports and deployments.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from pydantic import BaseModel, Field

from core.external_hosting_manager import (
    ExportConfig,
    ExternalHostingManager,
    HostingProvider,
)
from dashboard.auth import get_current_active_user
from db.models import User

router = APIRouter(prefix="/api/external-hosting", tags=["External Hosting"])


class HostingProviderEnum(str, Enum):
    """API enum for hosting providers"""

    netlify = "netlify"
    github_pages = "github_pages"
    vercel = "vercel"
    static_export = "static_export"


class ExportRequest(BaseModel):
    """Request model for site export"""

    site_name: str = Field(..., description="Name of the site to export")
    provider: HostingProviderEnum = Field(..., description="Hosting provider")
    optimize: bool = Field(True, description="Optimize assets")
    minify: bool = Field(True, description="Minify code")
    compress: bool = Field(True, description="Compress files")
    custom_domain: Optional[str] = Field(None, description="Custom domain")
    environment: str = Field("production", description="Environment")


class ProviderConfigRequest(BaseModel):
    """Request model for provider configuration"""

    provider: HostingProviderEnum = Field(..., description="Hosting provider")
    api_token: Optional[str] = Field(None, description="API token")
    team_id: Optional[str] = Field(None, description="Team ID")
    site_id: Optional[str] = Field(None, description="Site ID")
    repository: Optional[str] = Field(None, description="Repository name")
    branch: Optional[str] = Field("gh-pages", description="Branch name")
    token: Optional[str] = Field(None, description="GitHub token")
    username: Optional[str] = Field(None, description="Username")
    email: Optional[str] = Field(None, description="Email")
    custom_domain: Optional[str] = Field(None, description="Custom domain")
    project_id: Optional[str] = Field(None, description="Project ID")


class ExportResponse(BaseModel):
    """Response model for export operations"""

    success: bool
    message: str
    export_path: Optional[str] = None
    deployment_url: Optional[str] = None
    provider: Optional[str] = None
    build_artifacts: Optional[List[str]] = None
    export_time: Optional[str] = None
    error: Optional[str] = None


class ExportInfo(BaseModel):
    """Model for export information"""

    name: str
    path: str
    size: int
    created: datetime
    type: str


class ExportsListResponse(BaseModel):
    """Response model for exports list"""

    success: bool
    exports: List[ExportInfo]
    count: int
    message: Optional[str] = None


class ProviderInfo(BaseModel):
    """Model for provider information"""

    name: str
    enabled: bool
    configured: bool


class ProvidersListResponse(BaseModel):
    """Response model for providers list"""

    success: bool
    providers: List[ProviderInfo]
    count: int


class ProviderStatusResponse(BaseModel):
    """Response model for provider status"""

    success: bool
    provider: str
    enabled: bool
    configured: bool
    configuration: Dict[str, Any]


class ConnectionTestResponse(BaseModel):
    """Response model for connection test"""

    success: bool
    message: str
    provider: str
    error: Optional[str] = None


@router.post("/export", response_model=ExportResponse)
async def export_site(
    request: ExportRequest, current_user: User = Depends(get_current_active_user)
):
    """Export a site for external hosting"""
    try:
        hosting_manager = ExternalHostingManager()

        # Convert API enum to internal enum
        provider = HostingProvider(request.provider.value)

        # Create export configuration
        config = ExportConfig(
            site_name=request.site_name,
            provider=provider,
            optimize=request.optimize,
            minify=request.minify,
            compress=request.compress,
            custom_domain=request.custom_domain,
            environment=request.environment,
        )

        # Export the site
        result = await hosting_manager.export_site(request.site_name, provider, config)

        if result.success:
            return ExportResponse(
                success=True,
                message=f"Site {request.site_name} exported to {request.provider.value}",
                export_path=result.export_path,
                deployment_url=result.deployment_url,
                provider=request.provider.value,
                build_artifacts=result.build_artifacts,
                export_time=(
                    result.export_time.isoformat() if result.export_time else None
                ),
            )
        else:
            return ExportResponse(
                success=False, message="Export failed", error=result.error_message
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export/{provider}", response_model=ExportResponse)
async def export_site_to_provider(
    provider: HostingProviderEnum,
    site_name: str,
    optimize: bool = True,
    minify: bool = True,
    compress: bool = True,
    custom_domain: Optional[str] = None,
    environment: str = "production",
    current_user: User = Depends(get_current_active_user),
):
    """Export a site to a specific provider"""
    try:
        hosting_manager = ExternalHostingManager()

        # Convert API enum to internal enum
        hosting_provider = HostingProvider(provider.value)

        # Create export configuration
        config = ExportConfig(
            site_name=site_name,
            provider=hosting_provider,
            optimize=optimize,
            minify=minify,
            compress=compress,
            custom_domain=custom_domain,
            environment=environment,
        )

        # Export the site
        result = await hosting_manager.export_site(site_name, hosting_provider, config)

        if result.success:
            return ExportResponse(
                success=True,
                message=f"Site {site_name} exported to {provider.value}",
                export_path=result.export_path,
                deployment_url=result.deployment_url,
                provider=provider.value,
                build_artifacts=result.build_artifacts,
                export_time=(
                    result.export_time.isoformat() if result.export_time else None
                ),
            )
        else:
            return ExportResponse(
                success=False, message="Export failed", error=result.error_message
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/exports", response_model=ExportsListResponse)
async def list_exports(
    site_name: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
):
    """List available exports"""
    try:
        hosting_manager = ExternalHostingManager()
        exports = await hosting_manager.list_exports(site_name)

        export_infos = [
            ExportInfo(
                name=export["name"],
                path=export["path"],
                size=export["size"],
                created=export["created"],
                type=export["type"],
            )
            for export in exports
        ]

        return ExportsListResponse(
            success=True,
            exports=export_infos,
            count=len(export_infos),
            message=(
                f"Found {len(export_infos)} exports"
                if export_infos
                else "No exports found"
            ),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/exports/{export_name}")
async def delete_export(
    export_name: str, current_user: User = Depends(get_current_active_user)
):
    """Delete an export"""
    try:
        hosting_manager = ExternalHostingManager()
        success = await hosting_manager.delete_export(export_name)

        if success:
            return {
                "success": True,
                "message": f"Export {export_name} deleted successfully",
            }
        else:
            raise HTTPException(
                status_code=404, detail=f"Export {export_name} not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/providers/configure")
async def configure_provider(
    request: ProviderConfigRequest,
    current_user: User = Depends(get_current_active_user),
):
    """Configure a hosting provider"""
    try:
        # Load current configuration
        import json
        from pathlib import Path

        config_file = Path("config/external_hosting_config.json")
        if config_file.exists():
            with open(config_file, "r") as f:
                config = json.load(f)
        else:
            config = {}

        # Update provider configuration
        provider_key = request.provider.value
        if provider_key not in config:
            config[provider_key] = {}

        # Update with provided parameters
        provider_config = config[provider_key]
        for field, value in request.dict().items():
            if field != "provider" and value is not None:
                provider_config[field] = value

        # Enable the provider
        provider_config["enabled"] = True

        # Save configuration
        with open(config_file, "w") as f:
            json.dump(config, f, indent=2)

        return {
            "success": True,
            "message": f"Provider {request.provider.value} configured successfully",
            "provider": request.provider.value,
            "enabled": True,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers", response_model=ProvidersListResponse)
async def list_providers(current_user: User = Depends(get_current_active_user)):
    """List all available hosting providers"""
    try:
        providers = []
        for provider in HostingProvider:
            # Check if provider is configured
            import json
            from pathlib import Path

            config_file = Path("config/external_hosting_config.json")
            enabled = False
            configured = False

            if config_file.exists():
                with open(config_file, "r") as f:
                    config = json.load(f)

                provider_config = config.get(provider.value, {})
                enabled = provider_config.get("enabled", False)
                configured = bool(provider_config)

            providers.append(
                ProviderInfo(
                    name=provider.value, enabled=enabled, configured=configured
                )
            )

        return ProvidersListResponse(
            success=True, providers=providers, count=len(providers)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers/{provider}", response_model=ProviderStatusResponse)
async def get_provider_status(
    provider: HostingProviderEnum, current_user: User = Depends(get_current_active_user)
):
    """Get status of a hosting provider"""
    try:
        # Validate provider
        try:
            hosting_provider = HostingProvider(provider.value)
        except ValueError:
            raise HTTPException(
                status_code=400, detail=f"Invalid provider: {provider.value}"
            )

        # Load configuration
        import json
        from pathlib import Path

        config_file = Path("config/external_hosting_config.json")
        if config_file.exists():
            with open(config_file, "r") as f:
                config = json.load(f)

            provider_config = config.get(provider.value, {})

            return ProviderStatusResponse(
                success=True,
                provider=provider.value,
                enabled=provider_config.get("enabled", False),
                configured=bool(provider_config),
                configuration=provider_config,
            )
        else:
            return ProviderStatusResponse(
                success=True,
                provider=provider.value,
                enabled=False,
                configured=False,
                configuration={},
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/providers/{provider}/test", response_model=ConnectionTestResponse)
async def test_provider_connection(
    provider: HostingProviderEnum, current_user: User = Depends(get_current_active_user)
):
    """Test connection to a hosting provider"""
    try:
        # Validate provider
        try:
            hosting_provider = HostingProvider(provider.value)
        except ValueError:
            raise HTTPException(
                status_code=400, detail=f"Invalid provider: {provider.value}"
            )

        # Get provider status
        status_response = await get_provider_status(provider, current_user)
        if not status_response.enabled:
            return ConnectionTestResponse(
                success=False,
                message=f"Provider {provider.value} is not enabled",
                provider=provider.value,
                error="Provider not enabled",
            )

        # Test connection based on provider
        if provider.value == "netlify":
            result = await _test_netlify_connection()
        elif provider.value == "github_pages":
            result = await _test_github_connection()
        elif provider.value == "vercel":
            result = await _test_vercel_connection()
        else:
            result = {
                "success": True,
                "message": f"Provider {provider.value} connection test not implemented",
                "provider": provider.value,
            }

        return ConnectionTestResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def _test_netlify_connection() -> Dict[str, Any]:
    """Test Netlify connection"""
    try:
        # Load configuration
        import json
        from pathlib import Path

        config_file = Path("config/external_hosting_config.json")
        if config_file.exists():
            with open(config_file, "r") as f:
                config = json.load(f)

            netlify_config = config.get("netlify", {})
            api_token = netlify_config.get("api_token")

            if not api_token:
                return {
                    "success": False,
                    "message": "Netlify API token not configured",
                    "provider": "netlify",
                    "error": "API token not configured",
                }

            # Test API connection
            import requests

            headers = {"Authorization": f"Bearer {api_token}"}
            response = requests.get(
                "https://api.netlify.com/api/v1/user", headers=headers
            )

            if response.status_code == 200:
                return {
                    "success": True,
                    "message": "Netlify connection successful",
                    "provider": "netlify",
                }
            else:
                return {
                    "success": False,
                    "message": f"Netlify API error: {response.status_code}",
                    "provider": "netlify",
                    "error": f"API error: {response.status_code}",
                }
        else:
            return {
                "success": False,
                "message": "Configuration file not found",
                "provider": "netlify",
                "error": "Configuration file not found",
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"Netlify connection test failed: {str(e)}",
            "provider": "netlify",
            "error": str(e),
        }


async def _test_github_connection() -> Dict[str, Any]:
    """Test GitHub connection"""
    try:
        # Load configuration
        import json
        from pathlib import Path

        config_file = Path("config/external_hosting_config.json")
        if config_file.exists():
            with open(config_file, "r") as f:
                config = json.load(f)

            github_config = config.get("github_pages", {})
            token = github_config.get("token")
            repository = github_config.get("repository")

            if not token or not repository:
                return {
                    "success": False,
                    "message": "GitHub token or repository not configured",
                    "provider": "github_pages",
                    "error": "Token or repository not configured",
                }

            # Test API connection
            import requests

            headers = {"Authorization": f"token {token}"}
            response = requests.get(
                f"https://api.github.com/repos/{repository}", headers=headers
            )

            if response.status_code == 200:
                return {
                    "success": True,
                    "message": "GitHub connection successful",
                    "provider": "github_pages",
                }
            else:
                return {
                    "success": False,
                    "message": f"GitHub API error: {response.status_code}",
                    "provider": "github_pages",
                    "error": f"API error: {response.status_code}",
                }
        else:
            return {
                "success": False,
                "message": "Configuration file not found",
                "provider": "github_pages",
                "error": "Configuration file not found",
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"GitHub connection test failed: {str(e)}",
            "provider": "github_pages",
            "error": str(e),
        }


async def _test_vercel_connection() -> Dict[str, Any]:
    """Test Vercel connection"""
    try:
        # Load configuration
        import json
        from pathlib import Path

        config_file = Path("config/external_hosting_config.json")
        if config_file.exists():
            with open(config_file, "r") as f:
                config = json.load(f)

            vercel_config = config.get("vercel", {})
            api_token = vercel_config.get("api_token")

            if not api_token:
                return {
                    "success": False,
                    "message": "Vercel API token not configured",
                    "provider": "vercel",
                    "error": "API token not configured",
                }

            # Test API connection
            import requests

            headers = {"Authorization": f"Bearer {api_token}"}
            response = requests.get("https://api.vercel.com/v1/user", headers=headers)

            if response.status_code == 200:
                return {
                    "success": True,
                    "message": "Vercel connection successful",
                    "provider": "vercel",
                }
            else:
                return {
                    "success": False,
                    "message": f"Vercel API error: {response.status_code}",
                    "provider": "vercel",
                    "error": f"API error: {response.status_code}",
                }
        else:
            return {
                "success": False,
                "message": "Configuration file not found",
                "provider": "vercel",
                "error": "Configuration file not found",
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"Vercel connection test failed: {str(e)}",
            "provider": "vercel",
            "error": str(e),
        }
