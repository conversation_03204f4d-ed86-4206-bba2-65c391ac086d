import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { useForm, SubmitHandler } from 'react-hook-form';

interface SupabaseConfig {
  id?: string;
  project_id: string;
  supabase_url: string;
  supabase_anon_key: string;
  supabase_service_role_key: string;
  project_ref: string;
  organization_id: string;
  linked: boolean;
  created_at?: string;
  updated_at?: string;
}

interface SupabaseConfigFormProps {
  projectId: string;
  onConfigSaved?: (config: SupabaseConfig) => void;
  onError?: (error: string) => void;
}

export const SupabaseConfigForm: React.FC<SupabaseConfigFormProps> = ({
  projectId,
  onConfigSaved,
  onError
}) => {
  const [config, setConfig] = useState<SupabaseConfig>({
    project_id: projectId,
    supabase_url: '',
    supabase_anon_key: '',
    supabase_service_role_key: '',
    project_ref: '',
    organization_id: '',
    linked: false
  });
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [existingConfig, setExistingConfig] = useState<SupabaseConfig | null>(null);

  useEffect(() => {
    loadExistingConfig();
  }, [projectId]);

  const loadExistingConfig = async () => {
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/supabase/config`);
      if (response.ok) {
        const data = await response.json();
        setExistingConfig(data);
        setConfig(data);
      }
    } catch (error) {
      console.error('Error loading existing config:', error);
    }
  };

  const handleInputChange = (field: keyof SupabaseConfig, value: string) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateConfig = (): string[] => {
    const errors: string[] = [];

    if (!config.supabase_url) errors.push('Supabase URL is required');
    if (!config.supabase_anon_key) errors.push('Anonymous key is required');
    if (!config.supabase_service_role_key) errors.push('Service role key is required');
    if (!config.project_ref) errors.push('Project reference is required');
    if (!config.organization_id) errors.push('Organization ID is required');

    // Basic URL validation
    if (config.supabase_url && !config.supabase_url.startsWith('https://')) {
      errors.push('Supabase URL must start with https://');
    }

    return errors;
  };

  const testConnection = async () => {
    setTesting(true);
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/supabase/test-connection`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Connection test successful! Project: ${result.project_name}`);
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Connection test failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection test failed';
      onError?.(errorMessage);
      alert(`Connection test failed: ${errorMessage}`);
    } finally {
      setTesting(false);
    }
  };

  const saveConfig = async () => {
    const errors = validateConfig();
    if (errors.length > 0) {
      onError?.(errors.join(', '));
      alert(`Validation errors: ${errors.join(', ')}`);
      return;
    }

    setLoading(true);
    try {
      const method = existingConfig ? 'PUT' : 'POST';
      const response = await fetch(`/api/v1/projects/${projectId}/supabase/config`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        const savedConfig = await response.json();
        setConfig(savedConfig);
        setExistingConfig(savedConfig);
        onConfigSaved?.(savedConfig);
        alert('Supabase configuration saved successfully!');
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to save configuration');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save configuration';
      onError?.(errorMessage);
      alert(`Error saving configuration: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const deleteConfig = async () => {
    if (!existingConfig) return;

    if (!confirm('Are you sure you want to delete this Supabase configuration? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/supabase/config`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setConfig({
          project_id: projectId,
          supabase_url: '',
          supabase_anon_key: '',
          supabase_service_role_key: '',
          project_ref: '',
          organization_id: '',
          linked: false
        });
        setExistingConfig(null);
        alert('Supabase configuration deleted successfully!');
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to delete configuration');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete configuration';
      onError?.(errorMessage);
      alert(`Error deleting configuration: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-4">Supabase Configuration</h3>

      {existingConfig && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded">
          <p className="text-sm text-green-700">
            ✓ Configuration found for project: {existingConfig.project_ref}
          </p>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Supabase URL
          </label>
          <Input
            type="url"
            value={config.supabase_url}
            onChange={(e) => handleInputChange('supabase_url', e.target.value)}
            placeholder="https://your-project.supabase.co"
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Anonymous Key
          </label>
          <Input
            type="password"
            value={config.supabase_anon_key}
            onChange={(e) => handleInputChange('supabase_anon_key', e.target.value)}
            placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Service Role Key
          </label>
          <Input
            type="password"
            value={config.supabase_service_role_key}
            onChange={(e) => handleInputChange('supabase_service_role_key', e.target.value)}
            placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Project Reference
          </label>
          <Input
            type="text"
            value={config.project_ref}
            onChange={(e) => handleInputChange('project_ref', e.target.value)}
            placeholder="your-project-ref"
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Organization ID
          </label>
          <Input
            type="text"
            value={config.organization_id}
            onChange={(e) => handleInputChange('organization_id', e.target.value)}
            placeholder="your-org-id"
            className="w-full"
          />
        </div>
      </div>

      <div className="flex gap-3 mt-6">
        <Button
          onClick={saveConfig}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {loading ? 'Saving...' : existingConfig ? 'Update Configuration' : 'Save Configuration'}
        </Button>

        <Button
          onClick={testConnection}
          disabled={testing || !config.supabase_url}
          variant="outline"
        >
          {testing ? 'Testing...' : 'Test Connection'}
        </Button>

        {existingConfig && (
          <Button
            onClick={deleteConfig}
            disabled={loading}
            variant="outline"
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            Delete Configuration
          </Button>
        )}
      </div>
    </div>
  );
};
