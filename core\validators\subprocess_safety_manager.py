import asyncio
import logging
import os
import signal
import subprocess
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import psutil

from core.validators.safety_validator import SafetyValidator

logger = logging.getLogger(__name__)


class SubprocessSafetyManager:
    """Comprehensive subprocess safety manager with enhanced security features"""

    def __init__(self):
        self.active_processes: Dict[int, asyncio.subprocess.Process] = {}
        self.process_history: List[Dict[str, Any]] = []
        self.max_concurrent_processes = 5
        self.max_process_history = 100

    async def run_safe_command(
        self,
        command: str,
        cwd: str,
        timeout: int = 300,
        env: Dict[str, str] = None,
        capture_output: bool = True,
    ) -> Dict[str, Any]:
        """Run a command with comprehensive safety checks"""

        # Pre-execution safety checks
        self._pre_execution_checks(command, cwd)

        # Check concurrent process limit
        if len(self.active_processes) >= self.max_concurrent_processes:
            raise RuntimeError(
                f"🚫 Too many concurrent processes: {len(self.active_processes)} >= {self.max_concurrent_processes}"
            )

        # Prepare safe environment
        safe_env = self._prepare_safe_environment(env)

        # Create process with safety measures
        process = await self._create_safe_process(
            command, cwd, safe_env, capture_output
        )

        try:
            # Execute with timeout
            result = await self._execute_with_timeout(process, timeout)

            # Post-execution validation
            self._post_execution_validation(result)

            return result

        except Exception as e:
            logger.error(f"❌ Subprocess execution failed: {command} - {e}")
            return self._create_error_result(command, cwd, str(e))
        finally:
            # Cleanup
            self._cleanup_process(process)

    def _pre_execution_checks(self, command: str, cwd: str) -> None:
        """Perform all pre-execution safety checks"""

        # Validate command
        SafetyValidator.validate_command(command)

        # Validate working directory
        SafetyValidator.validate_subprocess_cwd(cwd)

        # Enforce directory confinement
        SafetyValidator.enforce_directory_confinement(cwd)

        # Check for shell injection attempts
        self._check_shell_injection(command)

        # Validate command length
        if len(command) > 1000:
            raise ValueError(f"🚫 Command too long: {len(command)} > 1000 characters")

    def _check_shell_injection(self, command: str) -> None:
        """Check for shell injection patterns"""
        shell_injection_patterns = [
            r"\$\(.*\)",  # Command substitution
            r"`.*`",  # Backtick command substitution
            r"&&.*",  # Command chaining
            r"\|\|.*",  # Command chaining
            r";.*",  # Command separator
            r"\|.*",  # Pipe
            r">.*",  # Output redirection
            r"<.*",  # Input redirection
            r"&.*",  # Background execution
            r"#.*",  # Comments
            r"\$[A-Z_]+",  # Variable expansion (e.g., $PATH, $HOME)
            r"\{.*\}",  # Brace expansion
            r"eval\s*\(",  # eval function
            r"exec\s*\(",  # exec function
        ]

        import re

        for pattern in shell_injection_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                raise ValueError(f"🚫 Shell injection pattern detected: {pattern}")

    def _prepare_safe_environment(self, env: Dict[str, str] = None) -> Dict[str, str]:
        """Prepare a safe environment for subprocess execution"""
        safe_env = os.environ.copy()

        if env:
            safe_env.update(env)

        # Remove dangerous environment variables
        dangerous_env_vars = [
            "PATH",
            "LD_LIBRARY_PATH",
            "PYTHONPATH",
            "NODE_PATH",
            "LD_PRELOAD",
            "LD_LIBRARY_PATH",
            "DYLD_LIBRARY_PATH",
            "DYLD_INSERT_LIBRARIES",
            "DYLD_FORCE_FLAT_NAMESPACE",
        ]

        for var in dangerous_env_vars:
            if var in safe_env:
                del safe_env[var]

        # Set safe defaults
        safe_env.update(
            {
                "PYTHONPATH": "",
                "NODE_PATH": "",
                "PATH": "/usr/local/bin:/usr/bin:/bin",
                "HOME": str(Path.home()),
                "USER": os.getenv("USER", "unknown"),
                "SHELL": "/bin/bash",
            }
        )

        return safe_env

    async def _create_safe_process(
        self, command: str, cwd: str, env: Dict[str, str], capture_output: bool = True
    ) -> asyncio.subprocess.Process:
        """Create a subprocess with safety measures"""

        # Split command into parts
        cmd_parts = command.split()

        # Create process
        process = await asyncio.create_subprocess_exec(
            *cmd_parts,
            cwd=cwd,
            env=env,
            stdout=asyncio.subprocess.PIPE if capture_output else None,
            stderr=asyncio.subprocess.PIPE if capture_output else None,
            stdin=asyncio.subprocess.DEVNULL,
            preexec_fn=self._set_process_limits if os.name != "nt" else None,
        )

        # Track active process
        self.active_processes[process.pid] = process

        logger.info(f"🔒 Created safe subprocess PID {process.pid}: {command}")
        return process

    def _set_process_limits(self):
        """Set process resource limits (Unix only)"""
        try:
            import resource

            # Set CPU time limit (5 minutes)
            resource.setrlimit(resource.RLIMIT_CPU, (300, 300))
            # Set memory limit (1GB)
            resource.setrlimit(
                resource.RLIMIT_AS, (1024 * 1024 * 1024, 1024 * 1024 * 1024)
            )
            # Set file descriptor limit
            resource.setrlimit(resource.RLIMIT_NOFILE, (100, 100))
            # Set core file size to 0 (no core dumps)
            resource.setrlimit(resource.RLIMIT_CORE, (0, 0))
        except ImportError:
            pass  # Windows doesn't have resource module

    async def _execute_with_timeout(
        self, process: asyncio.subprocess.Process, timeout: int
    ) -> Dict[str, Any]:
        """Execute process with timeout and monitoring"""

        start_time = datetime.now(timezone.utc)

        try:
            # Wait for completion with timeout
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            end_time = datetime.now(timezone.utc)
            execution_time = (end_time - start_time).total_seconds()

            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": (
                    stdout.decode("utf-8", errors="ignore").strip() if stdout else ""
                ),
                "stderr": (
                    stderr.decode("utf-8", errors="ignore").strip() if stderr else ""
                ),
                "pid": process.pid,
                "execution_time": execution_time,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "timeout": timeout,
            }

        except asyncio.TimeoutExpired:
            logger.warning(
                f"⏰ Process {process.pid} timed out after {timeout} seconds"
            )
            return await self._handle_timeout(process, start_time, timeout)

    async def _handle_timeout(
        self, process: asyncio.subprocess.Process, start_time: datetime, timeout: int
    ) -> Dict[str, Any]:
        """Handle process timeout gracefully"""

        # Try to terminate gracefully
        try:
            process.terminate()
            await asyncio.wait_for(process.wait(), timeout=5)
        except asyncio.TimeoutExpired:
            # Force kill if graceful termination fails
            try:
                process.kill()
                await process.wait()
            except:
                pass

        end_time = datetime.now(timezone.utc)
        execution_time = (end_time - start_time).total_seconds()

        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": f"Process timed out after {timeout} seconds",
            "pid": process.pid,
            "execution_time": execution_time,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "timeout": timeout,
            "terminated": True,
        }

    def _post_execution_validation(self, result: Dict[str, Any]) -> None:
        """Validate subprocess execution results"""

        # Check for suspicious output
        suspicious_patterns = [
            r"error.*permission",
            r"access.*denied",
            r"operation.*not.*permitted",
            r"segmentation.*fault",
            r"bus.*error",
            r"stack.*overflow",
        ]

        import re

        output = f"{result.get('stdout', '')} {result.get('stderr', '')}".lower()

        for pattern in suspicious_patterns:
            if re.search(pattern, output):
                logger.warning(f"⚠️ Suspicious output pattern detected: {pattern}")

    def _create_error_result(
        self, command: str, cwd: str, error: str
    ) -> Dict[str, Any]:
        """Create error result for failed subprocess"""
        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": error,
            "command": command,
            "cwd": cwd,
            "execution_time": 0,
            "start_time": datetime.now(timezone.utc).isoformat(),
            "end_time": datetime.now(timezone.utc).isoformat(),
            "error": True,
        }

    def _cleanup_process(self, process: asyncio.subprocess.Process) -> None:
        """Clean up process tracking"""
        if process.pid in self.active_processes:
            del self.active_processes[process.pid]

        # Add to history
        self.process_history.append(
            {
                "pid": process.pid,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "returncode": getattr(process, "returncode", None),
            }
        )

        # Trim history if too long
        if len(self.process_history) > self.max_process_history:
            self.process_history = self.process_history[-self.max_process_history :]

    async def cancel_process(self, pid: int) -> bool:
        """Cancel a running process"""
        if pid not in self.active_processes:
            return False

        process = self.active_processes[pid]

        try:
            process.terminate()
            await asyncio.wait_for(process.wait(), timeout=5)
            return True
        except asyncio.TimeoutExpired:
            try:
                process.kill()
                await process.wait()
                return True
            except:
                return False

    async def cancel_all_processes(self) -> int:
        """Cancel all running processes"""
        cancelled_count = 0

        for pid in list(self.active_processes.keys()):
            if await self.cancel_process(pid):
                cancelled_count += 1

        return cancelled_count

    def get_process_status(self) -> Dict[str, Any]:
        """Get current process status"""
        return {
            "active_processes": len(self.active_processes),
            "max_concurrent_processes": self.max_concurrent_processes,
            "process_history_count": len(self.process_history),
            "active_pids": list(self.active_processes.keys()),
        }

    def get_process_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent process history"""
        return self.process_history[-limit:] if limit else self.process_history.copy()

    def validate_path_safety(self, path: str) -> bool:
        """Validate path for subprocess operations"""
        try:
            SafetyValidator.validate_file_path(path)
            SafetyValidator.enforce_directory_confinement(path)
            return True
        except ValueError as e:
            logger.error(f"🚫 Path safety validation failed: {e}")
            return False

    def validate_command_safety(self, command: str) -> bool:
        """Validate command for subprocess operations"""
        try:
            SafetyValidator.validate_command(command)
            self._check_shell_injection(command)
            return True
        except ValueError as e:
            logger.error(f"🚫 Command safety validation failed: {e}")
            return False
