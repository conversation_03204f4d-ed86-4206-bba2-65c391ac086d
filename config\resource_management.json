{"resource_limits": {"memory": {"max_mb": 512, "reservation_mb": 256, "warning_threshold_percent": 80, "critical_threshold_percent": 90}, "cpu": {"max_percent": 50, "reservation_percent": 25, "warning_threshold_percent": 70, "critical_threshold_percent": 85}, "disk": {"warning_threshold_percent": 80, "critical_threshold_percent": 90}}, "optimization": {"enabled": true, "auto_optimize": true, "optimization_interval_seconds": 300, "garbage_collection": {"enabled": true, "frequency_seconds": 600}, "cache_management": {"enabled": true, "max_cache_size_mb": 50, "cleanup_threshold_percent": 75}}, "monitoring": {"resource_monitoring": {"enabled": true, "check_interval_seconds": 30, "log_interval_seconds": 300}, "alerts": {"enabled": true, "memory_alert_threshold_percent": 85, "cpu_alert_threshold_percent": 80, "disk_alert_threshold_percent": 85}}, "container_specific": {"docker": {"memory_limit_mb": 512, "cpu_limit": 0.5, "ulimits": {"nofile": {"soft": 65536, "hard": 65536}}}, "kubernetes": {"memory_limit_mb": 512, "cpu_limit": "500m", "memory_request_mb": 256, "cpu_request": "250m"}}, "performance": {"monitoring_interval": {"normal": 30, "high_load": 60, "critical": 120}, "batch_processing": {"enabled": true, "batch_size": 100, "max_batch_duration_seconds": 30}}}