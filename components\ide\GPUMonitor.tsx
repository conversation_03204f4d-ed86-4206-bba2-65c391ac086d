import React, { useState, useEffect, memo, useCallback } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/Badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Activity,
  Thermometer,
  HardDrive,
  Cpu,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import toast from 'react-hot-toast';

interface GPUStatus {
  name: string;
  total_memory_mb: number;
  free_memory_mb: number;
  used_memory_mb: number;
  temperature_c: number;
  utilization_percent: number;
  memory_usage_percent: number;
  status: 'healthy' | 'warning' | 'critical' | 'error';
}

interface ModelPerformance {
  model_name: string;
  avg_response_time_ms: number;
  avg_gpu_utilization_percent: number;
  success_rate_percent: number;
  gpu_layers: number;
  gpu_memory_mb: number;
}

interface GPUMonitorProps {
  refreshInterval?: number;
  showDetails?: boolean;
}

const GPUMonitor: React.FC<GPUMonitorProps> = ({
  refreshInterval = 5000,
  showDetails = true
}) => {
  const [gpuStatus, setGpuStatus] = useState<GPUStatus | null>(null);
  const [modelPerformance, setModelPerformance] = useState<ModelPerformance[]>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchGPUStatus = async () => {
    try {
      const response = await fetch('/api/v1/gpu/status');
      if (response.ok) {
        const data = await response.json();
        setGpuStatus(data);
        setError(null);
      } else {
        setError('Failed to fetch GPU status');
      }
    } catch (err) {
      setError('Error connecting to GPU monitoring service');
    }
  };

  const fetchModelPerformance = async () => {
    try {
      const response = await fetch('/api/v1/models/performance');
      if (response.ok) {
        const data = await response.json();
        setModelPerformance(data.models || []);
      }
    } catch (err) {
      toast.error('Failed to fetch model performance.');
    }
  };

  const updateData = async () => {
    setIsLoading(true);
    await Promise.all([fetchGPUStatus(), fetchModelPerformance()]);
    setLastUpdate(new Date());
    setIsLoading(false);
  };

  useEffect(() => {
    updateData();
    const interval = setInterval(updateData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 60) return 'text-green-600';
    if (temp < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getUtilizationColor = (util: number) => {
    if (util < 50) return 'text-green-600';
    if (util < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const ErrorDisplay = memo(() => (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          GPU Monitor
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Alert variant="error">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  ));

  const ModelPerformanceList = memo(({ models }: { models: ModelPerformance[] }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cpu className="h-5 w-5" />
          Model Performance
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {models.map((model, index) => (
            <div key={index} className="border rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-sm">{model.model_name}</span>
                <Badge variant="secondary">
                  {model.gpu_layers} layers
                </Badge>
              </div>

              <div className="grid grid-cols-3 gap-4 text-xs">
                <div>
                  <span className="text-gray-500">Response Time:</span>
                  <div className="font-medium">{model.avg_response_time_ms}ms</div>
                </div>
                <div>
                  <span className="text-gray-500">GPU Util:</span>
                  <div className="font-medium">{model.avg_gpu_utilization_percent}%</div>
                </div>
                <div>
                  <span className="text-gray-500">Success Rate:</span>
                  <div className="font-medium">{model.success_rate_percent}%</div>
                </div>
              </div>

              <div className="mt-2 text-xs text-gray-500">
                GPU Memory: {model.gpu_memory_mb}MB
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  ));

  if (error && !gpuStatus) {
    return <ErrorDisplay />;
  }

  return (
    <div className="space-y-4">
      {/* GPU Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              GPU Monitor - NVIDIA Quadro P1000
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              {lastUpdate.toLocaleTimeString()}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {gpuStatus ? (
            <>
              {/* GPU Status Overview */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(gpuStatus.status)}
                  <span className={`font-medium ${getStatusColor(gpuStatus.status)}`}>
                    {gpuStatus.name}
                  </span>
                </div>
                <Badge variant={gpuStatus.status === 'healthy' ? 'success' : 'error'}>
                  {gpuStatus.status.toUpperCase()}
                </Badge>
              </div>

              {/* GPU Utilization */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <Cpu className="h-4 w-4" />
                    GPU Utilization
                  </span>
                  <span className={getUtilizationColor(gpuStatus.utilization_percent)}>
                    {gpuStatus.utilization_percent}%
                  </span>
                </div>
                <Progress
                  value={gpuStatus.utilization_percent}
                  className="h-2"
                  variant={gpuStatus.utilization_percent > 80 ? 'error' : gpuStatus.utilization_percent > 50 ? 'warning' : 'success'}
                />
              </div>

              {/* Memory Usage */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <HardDrive className="h-4 w-4" />
                    VRAM Usage
                  </span>
                  <span>
                    {gpuStatus.used_memory_mb}MB / {gpuStatus.total_memory_mb}MB
                  </span>
                </div>
                <Progress
                  value={gpuStatus.memory_usage_percent}
                  className="h-2"
                  variant={gpuStatus.memory_usage_percent > 90 ? 'error' : gpuStatus.memory_usage_percent > 70 ? 'warning' : 'success'}
                />
              </div>

              {/* Temperature */}
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center gap-1">
                  <Thermometer className="h-4 w-4" />
                  Temperature
                </span>
                <span className={getTemperatureColor(gpuStatus.temperature_c)}>
                  {gpuStatus.temperature_c}°C
                </span>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Model Performance */}
      {showDetails && modelPerformance.length > 0 && (
        <ModelPerformanceList models={modelPerformance} />
      )}

      {/* Performance Alerts */}
      {gpuStatus && (
        <div className="space-y-2">
          {gpuStatus.temperature_c > 80 && (
            <Alert variant="error">
              <Thermometer className="h-4 w-4" />
              <AlertDescription>
                GPU temperature is high ({gpuStatus.temperature_c}°C). Consider improving cooling.
              </AlertDescription>
            </Alert>
          )}

          {gpuStatus.memory_usage_percent > 90 && (
            <Alert variant="error">
              <HardDrive className="h-4 w-4" />
              <AlertDescription>
                GPU memory usage is critical ({gpuStatus.memory_usage_percent}%). Consider unloading unused models.
              </AlertDescription>
            </Alert>
          )}

          {gpuStatus.utilization_percent > 95 && (
            <Alert variant="error">
              <Activity className="h-4 w-4" />
              <AlertDescription>
                GPU utilization is very high ({gpuStatus.utilization_percent}%). Monitor for thermal throttling.
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
};

export default GPUMonitor;
