{"model_optimizer_service": {"enabled": true, "port": 8087, "host": "0.0.0.0", "log_level": "INFO", "environment": "production"}, "container": {"name": "ai-coding-model-optimizer", "image": "ai-coding-model-optimizer:latest", "restart_policy": "unless-stopped", "resources": {"limits": {"cpus": "2.0", "memory": "4G"}, "reservations": {"cpus": "1.0", "memory": "2G"}}, "volumes": ["./data:/app/data", "./logs:/app/logs", "./models:/app/models", "./config:/app/config", "./ai_optimization:/app/ai_optimization"], "environment_variables": {"PYTHONPATH": "/app", "MODEL_OPTIMIZER_ENABLED": "true", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "DATABASE_URL": "******************************************************/ai_coding_agent", "OLLAMA_URL": "http://ollama:11434", "REDIS_URL": "redis://redis:6379", "NVIDIA_VISIBLE_DEVICES": "0", "NVIDIA_DRIVER_CAPABILITIES": "compute,utility", "CUDA_VISIBLE_DEVICES": "0"}}, "dependencies": {"required_services": ["api", "db", "redis", "ollama"], "health_check_dependencies": true}, "model_optimizer_components": {"gpu_optimizer": {"enabled": true, "gpu_memory_gb": 4, "optimization_types": ["compression", "quantization", "pruning", "distillation"], "batch_size": 1, "mixed_precision": true}, "model_compressor": {"enabled": true, "compression_methods": ["pruning", "quantization", "knowledge_distillation"], "compression_ratios": [0.25, 0.5, 0.75], "accuracy_threshold": 0.95}, "performance_analyzer": {"enabled": true, "analysis_types": ["inference_time", "memory_usage", "throughput", "accuracy"], "benchmark_datasets": ["test_data", "validation_data"], "metrics_collection": true}, "resource_monitor": {"enabled": true, "monitoring_interval": 30, "gpu_monitoring": true, "memory_monitoring": true, "alert_thresholds": {"gpu_utilization": 90, "memory_usage": 95, "temperature": 85}}}, "gpu_configuration": {"hardware": {"model": "NVIDIA Quadro P1000", "memory": "4GB GDDR5", "cuda_cores": 512, "memory_bandwidth": "32 GB/s", "compute_capability": "6.1"}, "allocation": {"primary_service": "model_optimizer", "memory_allocation": "4GB", "priority": 1}, "optimization_settings": {"cuda_visible_devices": "0", "gpu_memory_fraction": 0.95, "allow_growth": true, "per_process_gpu_memory_fraction": 0.9, "mixed_precision": true}}, "monitoring": {"health_check": {"endpoint": "/health", "interval": 30, "timeout": 10, "retries": 3, "start_period": 40}, "metrics": {"enabled": true, "endpoint": "/metrics", "collection_interval": 60}, "logging": {"level": "INFO", "format": "json", "output": "stdout"}}, "security": {"non_root_user": true, "user": "modeloptimizer", "group": "modeloptimizer", "capabilities": [], "read_only_root": false, "security_opt": ["no-new-privileges"]}, "networking": {"network": "ai-coding-network", "port_mapping": "8087:8087", "internal_port": 8087, "external_port": 8087}, "performance": {"max_concurrent_optimizations": 2, "optimization_timeout": 3600, "connection_pool_size": 10, "cache_enabled": true, "cache_ttl": 300}, "api_endpoints": {"health": {"method": "GET", "path": "/health", "description": "Health check endpoint"}, "status": {"method": "GET", "path": "/status", "description": "Get model optimizer system status"}, "optimization_summary": {"method": "GET", "path": "/optimization/summary", "description": "Get optimization summary"}, "optimization_jobs": {"method": "GET", "path": "/optimization/jobs", "description": "Get list of optimization jobs"}, "optimization_components": {"method": "GET", "path": "/optimization/components", "description": "Get model optimizer components status"}, "gpu_status": {"method": "GET", "path": "/gpu/status", "description": "Get GPU status and capabilities"}, "optimize_model": {"method": "POST", "path": "/optimization/optimize", "description": "Optimize a model"}, "analyze_performance": {"method": "POST", "path": "/optimization/analyze-performance", "description": "Analyze model performance"}, "compress_model": {"method": "POST", "path": "/optimization/compress", "description": "Compress a model"}, "metrics": {"method": "GET", "path": "/metrics", "description": "Get model optimizer metrics"}, "export": {"method": "GET", "path": "/export", "description": "Export model optimization data"}}}