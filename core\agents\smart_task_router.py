#!/usr/bin/env python3
"""
SmartTaskRouter - AI-powered intelligent task distribution system

This module implements intelligent task routing based on:
1. Agent capabilities and specializations
2. Historical performance metrics
3. Current agent load and availability
4. Task complexity analysis
5. Real-time agent capability assessment
6. Learning from task outcomes

The router uses Ollama LLMs to make intelligent routing decisions and
continuously learns from task outcomes to improve future routing.
"""

import asyncio
import json
import logging
import statistics
import time
import uuid
from collections import defaultdict, deque
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from core.agents.agent_load_balancer import AgentLoadBalancer
from core.agents.agent_performance_tracker import AgentPerformanceTracker
from core.agents.context_analyzer import ContextAnalyzer
from core.agents.task_distribution import TaskDistribution
from models.ollama_manager import OllamaModelManager

logger = logging.getLogger(__name__)


class RoutingStrategy(Enum):
    """Available routing strategies"""

    PERFORMANCE_BASED = "performance_based"
    LOAD_BALANCED = "load_balanced"
    CAPABILITY_MATCHED = "capability_matched"
    HYBRID = "hybrid"
    AI_POWERED = "ai_powered"


class TaskComplexity(Enum):
    """Task complexity levels"""

    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"


@dataclass
class RoutingDecision:
    """Represents a routing decision made by the smart router"""

    task_id: str
    selected_agent_id: str
    strategy_used: RoutingStrategy
    confidence_score: float
    reasoning: str
    alternative_agents: List[str] = field(default_factory=list)
    estimated_duration: Optional[int] = None
    complexity_score: Optional[float] = None
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class AgentCapability:
    """Represents an agent's capabilities"""

    agent_id: str
    agent_type: str
    capabilities: List[str] = field(default_factory=list)
    specializations: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    current_load: int = 0
    max_capacity: int = 10
    availability_score: float = 1.0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class TaskContext:
    """Context information for task routing"""

    task_id: str
    task_type: str
    complexity: TaskComplexity
    requirements: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    priority: str = "medium"
    estimated_duration: Optional[int] = None
    dependencies: List[str] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)


class SmartTaskRouter:
    """
    AI-powered intelligent task router that distributes tasks based on
    agent capabilities, performance history, and current load.
    """

    def __init__(self, config_path: str = "config/smart_routing_config.json"):
        self.config = self._load_config(config_path)
        self.performance_tracker = AgentPerformanceTracker()
        self.context_analyzer = ContextAnalyzer()
        self.load_balancer = AgentLoadBalancer()
        self.task_distribution = TaskDistribution()

        # Initialize Ollama manager for AI-powered decisions
        self.ollama_manager = OllamaModelManager()

        # Routing state
        self.agent_capabilities: Dict[str, AgentCapability] = {}
        self.routing_history: deque = deque(maxlen=1000)
        self.learning_enabled = self.config.get("learning", {}).get("enabled", True)
        self.ai_routing_enabled = self.config.get("ai_routing", {}).get("enabled", True)

        # Performance tracking
        self.routing_metrics = {
            "total_decisions": 0,
            "successful_routes": 0,
            "failed_routes": 0,
            "average_confidence": 0.0,
            "strategy_usage": defaultdict(int),
        }

        logger.info("SmartTaskRouter initialized with AI-powered routing")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded smart routing config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "routing": {
                "default_strategy": "hybrid",
                "confidence_threshold": 0.7,
                "max_alternatives": 3,
                "fallback_strategy": "load_balanced",
            },
            "ai_routing": {
                "enabled": True,
                "model_name": "qwen2.5-coder:3b",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            "learning": {
                "enabled": True,
                "learning_rate": 0.1,
                "update_interval_minutes": 30,
            },
            "performance": {
                "tracking_enabled": True,
                "history_size": 1000,
                "cleanup_interval_hours": 24,
            },
            "load_balancing": {
                "enabled": True,
                "max_load_threshold": 0.8,
                "load_check_interval_seconds": 60,
            },
        }

    async def register_agent(
        self,
        agent_id: str,
        agent_type: str,
        capabilities: List[str] = None,
        specializations: List[str] = None,
    ) -> bool:
        """Register an agent with the router"""
        try:
            agent_capability = AgentCapability(
                agent_id=agent_id,
                agent_type=agent_type,
                capabilities=capabilities or [],
                specializations=specializations or [],
            )

            self.agent_capabilities[agent_id] = agent_capability
            await self.performance_tracker.register_agent(agent_id, agent_type)
            await self.load_balancer.register_agent(agent_id, agent_type)

            logger.info(
                f"Registered agent {agent_id} ({agent_type}) with capabilities: {capabilities}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to register agent {agent_id}: {e}")
            return False

    async def route_task(
        self, task_context: TaskContext, available_agents: List[str] = None
    ) -> RoutingDecision:
        """
        Route a task to the most suitable agent using AI-powered decision making
        """
        try:
            self.routing_metrics["total_decisions"] += 1

            # Analyze task context
            complexity_score = await self.context_analyzer.analyze_complexity(
                task_context
            )
            task_context.complexity = self._map_complexity_score(complexity_score)

            # Get available agents
            if available_agents is None:
                available_agents = list(self.agent_capabilities.keys())

            # Filter agents by availability and load
            available_agents = await self._filter_available_agents(available_agents)

            if not available_agents:
                raise ValueError("No available agents for task routing")

            # Make routing decision
            if self.ai_routing_enabled:
                decision = await self._ai_powered_routing(
                    task_context, available_agents
                )
            else:
                decision = await self._rule_based_routing(
                    task_context, available_agents
                )

            # Update metrics
            self.routing_metrics["strategy_usage"][decision.strategy_used.value] += 1
            self.routing_history.append(decision)

            logger.info(
                f"Routed task {task_context.task_id} to agent {decision.selected_agent_id} "
                f"using strategy {decision.strategy_used.value} (confidence: {decision.confidence_score:.2f})"
            )

            return decision

        except Exception as e:
            logger.error(f"Failed to route task {task_context.task_id}: {e}")
            # Fallback to simple routing
            return await self._fallback_routing(task_context, available_agents or [])

    async def _ai_powered_routing(
        self, task_context: TaskContext, available_agents: List[str]
    ) -> RoutingDecision:
        """Use AI to make routing decisions"""
        try:
            # Prepare context for AI decision
            agent_info = []
            for agent_id in available_agents:
                capability = self.agent_capabilities.get(agent_id)
                if capability:
                    performance = await self.performance_tracker.get_agent_performance(
                        agent_id
                    )
                    load_info = await self.load_balancer.get_agent_load(agent_id)

                    agent_info.append(
                        {
                            "agent_id": agent_id,
                            "agent_type": capability.agent_type,
                            "capabilities": capability.capabilities,
                            "specializations": capability.specializations,
                            "performance_metrics": performance,
                            "current_load": load_info.get("current_load", 0),
                            "availability_score": load_info.get(
                                "availability_score", 1.0
                            ),
                        }
                    )

            # Create AI prompt
            prompt = self._create_routing_prompt(task_context, agent_info)

            # Get AI decision
            response = await self.ollama_manager.generate_response(
                model_name=self.config["ai_routing"]["model_name"],
                prompt=prompt,
                max_tokens=self.config["ai_routing"]["max_tokens"],
                temperature=self.config["ai_routing"]["temperature"],
            )

            # Parse AI response
            decision = self._parse_ai_response(response, task_context, available_agents)

            return decision

        except Exception as e:
            logger.error(f"AI-powered routing failed: {e}")
            return await self._rule_based_routing(task_context, available_agents)

    def _create_routing_prompt(
        self, task_context: TaskContext, agent_info: List[Dict[str, Any]]
    ) -> str:
        """Create a prompt for AI routing decision"""
        prompt = f"""
You are an intelligent task router for an AI coding agent system. Your job is to route tasks to the most suitable agent based on capabilities, performance, and current load.

TASK CONTEXT:
- Task ID: {task_context.task_id}
- Task Type: {task_context.task_type}
- Complexity: {task_context.complexity.value}
- Requirements: {', '.join(task_context.requirements)}
- Constraints: {', '.join(task_context.constraints)}
- Priority: {task_context.priority}
- Estimated Duration: {task_context.estimated_duration or 'Unknown'} minutes

AVAILABLE AGENTS:
"""

        for agent in agent_info:
            prompt += f"""
Agent ID: {agent['agent_id']}
Type: {agent['agent_type']}
Capabilities: {', '.join(agent['capabilities'])}
Specializations: {', '.join(agent['specializations'])}
Performance Metrics: {json.dumps(agent['performance_metrics'])}
Current Load: {agent['current_load']}
Availability Score: {agent['availability_score']:.2f}
"""

        prompt += """
INSTRUCTIONS:
1. Analyze the task requirements and constraints
2. Consider each agent's capabilities, specializations, and performance history
3. Account for current load and availability
4. Select the best agent for this task
5. Provide a confidence score (0.0 to 1.0)
6. Explain your reasoning

RESPONSE FORMAT (JSON):
{
    "selected_agent_id": "agent_id",
    "confidence_score": 0.85,
    "reasoning": "Detailed explanation of why this agent was chosen",
    "alternative_agents": ["agent_id1", "agent_id2"],
    "estimated_duration": 30
}

Respond with only the JSON object:
"""

        return prompt

    def _parse_ai_response(
        self, response: str, task_context: TaskContext, available_agents: List[str]
    ) -> RoutingDecision:
        """Parse AI response into a routing decision"""
        try:
            # Extract JSON from response
            json_start = response.find("{")
            json_end = response.rfind("}") + 1
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in AI response")

            json_str = response[json_start:json_end]
            ai_decision = json.loads(json_str)

            # Validate AI decision
            selected_agent = ai_decision.get("selected_agent_id")
            if selected_agent not in available_agents:
                raise ValueError(f"AI selected unavailable agent: {selected_agent}")

            return RoutingDecision(
                task_id=task_context.task_id,
                selected_agent_id=selected_agent,
                strategy_used=RoutingStrategy.AI_POWERED,
                confidence_score=ai_decision.get("confidence_score", 0.5),
                reasoning=ai_decision.get("reasoning", "AI-powered decision"),
                alternative_agents=ai_decision.get("alternative_agents", []),
                estimated_duration=ai_decision.get("estimated_duration"),
                complexity_score=task_context.complexity.value,
            )

        except Exception as e:
            logger.error(f"Failed to parse AI response: {e}")
            # Fallback to rule-based routing
            return asyncio.create_task(
                self._rule_based_routing(task_context, available_agents)
            )

    async def _rule_based_routing(
        self, task_context: TaskContext, available_agents: List[str]
    ) -> RoutingDecision:
        """Use rule-based routing as fallback"""
        try:
            # Score each agent
            agent_scores = []
            for agent_id in available_agents:
                capability = self.agent_capabilities.get(agent_id)
                if not capability:
                    continue

                # Calculate score based on multiple factors
                capability_score = self._calculate_capability_score(
                    task_context, capability
                )
                performance_score = await self.performance_tracker.get_agent_score(
                    agent_id
                )
                load_score = await self.load_balancer.get_agent_score(agent_id)

                total_score = (
                    capability_score * 0.4 + performance_score * 0.4 + load_score * 0.2
                )

                agent_scores.append((agent_id, total_score))

            # Sort by score and select best
            agent_scores.sort(key=lambda x: x[1], reverse=True)

            if not agent_scores:
                raise ValueError("No suitable agents found")

            selected_agent_id, confidence_score = agent_scores[0]
            alternative_agents = [agent_id for agent_id, _ in agent_scores[1:3]]

            return RoutingDecision(
                task_id=task_context.task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=RoutingStrategy.HYBRID,
                confidence_score=confidence_score,
                reasoning=f"Rule-based selection based on capabilities, performance, and load",
                alternative_agents=alternative_agents,
                complexity_score=task_context.complexity.value,
            )

        except Exception as e:
            logger.error(f"Rule-based routing failed: {e}")
            return await self._fallback_routing(task_context, available_agents)

    async def _fallback_routing(
        self, task_context: TaskContext, available_agents: List[str]
    ) -> RoutingDecision:
        """Simple fallback routing"""
        if not available_agents:
            raise ValueError("No agents available for routing")

        # Simple round-robin or first available
        selected_agent_id = available_agents[0]

        return RoutingDecision(
            task_id=task_context.task_id,
            selected_agent_id=selected_agent_id,
            strategy_used=RoutingStrategy.LOAD_BALANCED,
            confidence_score=0.3,
            reasoning="Fallback routing - first available agent",
            alternative_agents=available_agents[1:],
            complexity_score=task_context.complexity.value,
        )

    def _calculate_capability_score(
        self, task_context: TaskContext, capability: AgentCapability
    ) -> float:
        """Calculate how well an agent's capabilities match the task"""
        score = 0.0

        # Check if agent has required capabilities
        for requirement in task_context.requirements:
            if requirement.lower() in [cap.lower() for cap in capability.capabilities]:
                score += 0.3
            elif requirement.lower() in [
                spec.lower() for spec in capability.specializations
            ]:
                score += 0.5

        # Check agent type match
        if task_context.task_type.lower() in capability.agent_type.lower():
            score += 0.2

        return min(score, 1.0)

    def _map_complexity_score(self, complexity_score: float) -> TaskComplexity:
        """Map complexity score to complexity enum"""
        if complexity_score < 0.25:
            return TaskComplexity.SIMPLE
        elif complexity_score < 0.5:
            return TaskComplexity.MODERATE
        elif complexity_score < 0.75:
            return TaskComplexity.COMPLEX
        else:
            return TaskComplexity.VERY_COMPLEX

    async def _filter_available_agents(self, agent_ids: List[str]) -> List[str]:
        """Filter agents based on availability and load"""
        available_agents = []

        for agent_id in agent_ids:
            capability = self.agent_capabilities.get(agent_id)
            if not capability:
                continue

            # Check load
            load_info = await self.load_balancer.get_agent_load(agent_id)
            if load_info.get("current_load", 0) >= capability.max_capacity:
                continue

            # Check availability
            if load_info.get("availability_score", 1.0) < 0.3:
                continue

            available_agents.append(agent_id)

        return available_agents

    async def record_task_outcome(
        self,
        task_id: str,
        agent_id: str,
        success: bool,
        duration: int = None,
        error: str = None,
    ) -> None:
        """Record the outcome of a routed task for learning"""
        try:
            # Update performance tracker
            await self.performance_tracker.record_task_outcome(
                agent_id, task_id, success, duration, error
            )

            # Update load balancer
            await self.load_balancer.record_task_completion(agent_id, duration)

            # Update routing metrics
            if success:
                self.routing_metrics["successful_routes"] += 1
            else:
                self.routing_metrics["failed_routes"] += 1

            # Find the routing decision for this task
            for decision in self.routing_history:
                if (
                    decision.task_id == task_id
                    and decision.selected_agent_id == agent_id
                ):
                    # Update decision with outcome
                    decision.success = success
                    decision.actual_duration = duration
                    decision.error = error
                    break

            logger.info(
                f"Recorded task outcome for {task_id}: success={success}, duration={duration}"
            )

        except Exception as e:
            logger.error(f"Failed to record task outcome for {task_id}: {e}")

    async def get_routing_metrics(self) -> Dict[str, Any]:
        """Get routing performance metrics"""
        return {
            "total_decisions": self.routing_metrics["total_decisions"],
            "successful_routes": self.routing_metrics["successful_routes"],
            "failed_routes": self.routing_metrics["failed_routes"],
            "success_rate": (
                self.routing_metrics["successful_routes"]
                / max(self.routing_metrics["total_decisions"], 1)
            )
            * 100,
            "average_confidence": self.routing_metrics["average_confidence"],
            "strategy_usage": dict(self.routing_metrics["strategy_usage"]),
            "agent_capabilities_count": len(self.agent_capabilities),
            "routing_history_size": len(self.routing_history),
        }

    async def get_agent_recommendations(
        self, task_context: TaskContext
    ) -> List[Dict[str, Any]]:
        """Get agent recommendations for a task"""
        try:
            available_agents = list(self.agent_capabilities.keys())
            recommendations = []

            for agent_id in available_agents:
                capability = self.agent_capabilities.get(agent_id)
                if not capability:
                    continue

                capability_score = self._calculate_capability_score(
                    task_context, capability
                )
                performance_score = await self.performance_tracker.get_agent_score(
                    agent_id
                )
                load_score = await self.load_balancer.get_agent_score(agent_id)

                total_score = (
                    capability_score * 0.4 + performance_score * 0.4 + load_score * 0.2
                )

                recommendations.append(
                    {
                        "agent_id": agent_id,
                        "agent_type": capability.agent_type,
                        "capability_score": capability_score,
                        "performance_score": performance_score,
                        "load_score": load_score,
                        "total_score": total_score,
                        "capabilities": capability.capabilities,
                        "specializations": capability.specializations,
                    }
                )

            # Sort by total score
            recommendations.sort(key=lambda x: x["total_score"], reverse=True)
            return recommendations[:5]  # Return top 5 recommendations

        except Exception as e:
            logger.error(f"Failed to get agent recommendations: {e}")
            return []

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            await self.performance_tracker.cleanup()
            await self.load_balancer.cleanup()
            logger.info("SmartTaskRouter cleanup completed")
        except Exception as e:
            logger.error(f"Failed to cleanup SmartTaskRouter: {e}")
