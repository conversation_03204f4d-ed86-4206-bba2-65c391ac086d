{"monitor": {"check_interval": 30, "strict_mode": true, "daemon_mode": true, "log_level": "INFO", "max_log_size_mb": 100, "max_metrics_history": 1000, "max_violation_history": 100}, "health_check": {"enabled": true, "interval_seconds": 30, "timeout_seconds": 10, "retries": 3, "start_period_seconds": 40}, "thresholds": {"critical": {"cpu_percent": 80.0, "memory_percent": 80.0, "disk_percent": 90.0, "log_size_mb": 100.0, "compliance_score": 70.0}, "warning": {"cpu_percent": 60.0, "memory_percent": 60.0, "disk_percent": 75.0, "log_size_mb": 50.0, "compliance_score": 80.0}}, "logging": {"structured": true, "file_rotation": {"max_size_mb": 10, "max_files": 3}, "console_output": true}, "metrics": {"enabled": true, "export_prometheus": true, "retention_days": 30}, "alerts": {"enabled": true, "channels": {"log": true, "email": false, "slack": false, "webhook": false}}, "dependencies": {"services": [{"name": "database", "url": "http://db:5432", "timeout": 5, "required": true}, {"name": "redis", "url": "http://redis:6379", "timeout": 5, "required": true}, {"name": "api", "url": "http://api:8000/health", "timeout": 10, "required": true}, {"name": "ollama", "url": "http://ollama:11434/api/tags", "timeout": 10, "required": false}]}, "security": {"non_root_user": true, "read_only_filesystem": true, "capabilities_dropped": ["ALL"], "no_new_privileges": true}}