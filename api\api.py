"""
FastAPI Dashboard API
Main application entry point for the dashboard backend.
"""

import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import (
    Depends,
    FastAPI,
    HTTPException,
    Request,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates

import error_detection
from dashboard.database import (
    create_tables,
    get_database_stats,
    get_db_context,
    health_check,
    init_db,
)
from dashboard.routes import router
from dashboard.websocket_manager import WebSocketManager

try:
    from dashboard.models import schemas
except ImportError:
    # Create a placeholder if schemas module is not available
    schemas = None

from api.chat_routes import router as chat_router

try:
    from monitoring.monitoring_agent import router as monitoring_router
    from monitoring.monitoring_agent import (
        start_monitoring_agent,
        stop_monitoring_agent,
    )
except ImportError:
    # Create placeholder if monitoring_agent is not available
    monitoring_router = None

    async def start_monitoring_agent():
        pass

    async def stop_monitoring_agent():
        pass


from config import get_config

try:
    from core.website_generator import WebsiteGenerator
except ImportError:
    # Create placeholder if website_generator is not available
    class WebsiteGenerator:
        def __init__(self):
            pass

        def create_website(self, *args, **kwargs):
            return {"success": False, "error": "WebsiteGenerator not available"}


try:
    from templates.template_manager import TemplateManager
except ImportError:
    # Create placeholder if template_manager is not available
    class TemplateManager:
        def __init__(self):
            pass

        def get_template(self, *args, **kwargs):
            return {"success": False, "error": "TemplateManager not available"}


# Configure logging
logger = logging.getLogger(__name__)

# WebSocket manager instance
websocket_manager = WebSocketManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting dashboard API...")
    try:
        create_tables()
        init_db()
        logger.info("Database initialized successfully")

        # Start monitoring agent
        await start_monitoring_agent()
        logger.info("Monitoring agent started successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")

    yield

    # Shutdown
    logger.info("Shutting down dashboard API...")
    await websocket_manager.disconnect_all()
    await stop_monitoring_agent()


# Create FastAPI app
app = FastAPI(
    title="AI Coding Agent Dashboard",
    description="Dashboard backend for AI Coding Agent project",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1")
app.include_router(chat_router, prefix="/api/v1")
if monitoring_router:
    app.include_router(monitoring_router)


# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            await websocket_manager.handle_message(websocket, data)
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        websocket_manager.disconnect(websocket)


# Health check endpoint
@app.get("/health")
async def health_check_endpoint():
    """Health check endpoint"""
    db_healthy = health_check()
    return {
        "status": "healthy" if db_healthy else "unhealthy",
        "database": "connected" if db_healthy else "disconnected",
        "websocket_connections": len(websocket_manager.active_connections),
    }


# Stats endpoint
@app.get("/stats")
async def stats_endpoint():
    """Get system statistics"""
    try:
        stats = get_database_stats()
        return {
            "database_stats": stats,
            "websocket_connections": len(websocket_manager.active_connections),
            "active_connections": list(websocket_manager.active_connections.keys()),
        }
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get statistics")


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Coding Agent Dashboard API",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "stats": "/stats",
            "websocket": "/ws",
            "api_docs": "/docs",
            "redoc": "/redoc",
        },
    }


# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500, content={"error": "Internal server error", "detail": str(exc)}
    )


@app.post("/api/v1/error-detection/scan")
async def scan_project(request: Request):
    """Scan project for web errors."""
    try:
        data = await request.json()
        project_path = data.get("project_path", ".")

        detector = error_detection.WebErrorDetector(project_path)
        scan_report = detector.run_comprehensive_scan()

        return JSONResponse(content=scan_report)
    except Exception as e:
        logger.error(f"Error scanning project: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Scan failed: {str(e)}"}
        )


@app.post("/api/v1/error-detection/auto-fix")
async def auto_fix_errors(request: Request):
    """Automatically fix detected errors."""
    try:
        data = await request.json()
        project_path = data.get("project_path", ".")
        risk_level = data.get("risk_level", "moderate")

        manager = error_detection.AutoFixManager(project_path)
        fix_report = manager.run_safe_auto_fix(risk_level)

        return JSONResponse(content=fix_report)
    except Exception as e:
        logger.error(f"Error auto-fixing: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Auto-fix failed: {str(e)}"}
        )


@app.get("/api/v1/error-detection/history")
async def get_fix_history(project_path: str = ".", limit: int = 10):
    """Get fix history."""
    try:
        manager = error_detection.AutoFixManager(project_path)
        history = manager.get_fix_history(limit)

        return JSONResponse(content=history)
    except Exception as e:
        logger.error(f"Error getting history: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to get history: {str(e)}"}
        )


@app.get("/api/v1/error-detection/session/{session_id}")
async def get_session_details(session_id: str):
    """Get detailed session information."""
    try:
        # For now, we'll use a default project path
        # In a real implementation, you'd store session info in a database
        manager = error_detection.AutoFixManager(".")
        session_details = manager.get_session_details(session_id)

        if session_details is None:
            return JSONResponse(status_code=404, content={"error": "Session not found"})

        return JSONResponse(content=session_details)
    except Exception as e:
        logger.error(f"Error getting session details: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to get session details: {str(e)}"},
        )


@app.get("/api/v1/error-detection/status")
async def get_error_status(project_path: str = "."):
    """Get current error status."""
    try:
        detector = error_detection.WebErrorDetector(project_path)
        summary = detector.get_scan_summary()

        status = {
            "total_errors": summary["total_errors"],
            "auto_fixable_count": summary["auto_fixable_count"],
            "errors_by_category": summary["errors_by_category"],
            "errors_by_severity": summary["errors_by_severity"],
        }

        return JSONResponse(content=status)
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to get status: {str(e)}"}
        )


@app.get("/api/v1/error-detection/report")
async def generate_report(
    project_path: str = ".",
    category: Optional[str] = None,
    severity: Optional[str] = None,
):
    """Generate detailed error report."""
    try:
        detector = error_detection.WebErrorDetector(project_path)
        scan_report = detector.run_comprehensive_scan()

        # Apply filters
        filtered_errors = scan_report["errors"]
        if category:
            filtered_errors = [e for e in filtered_errors if e["category"] == category]
        if severity:
            filtered_errors = [e for e in filtered_errors if e["severity"] == severity]

        # Update scan report with filtered errors
        scan_report["errors"] = filtered_errors
        scan_report["total_errors"] = len(filtered_errors)

        return JSONResponse(content=scan_report)
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to generate report: {str(e)}"}
        )


@app.post("/api/v1/error-detection/export")
async def export_results(request: Request):
    """Export scan results in various formats."""
    try:
        data = await request.json()
        scan_report = data.get("scan_report", {})
        format_type = data.get("format", "json")

        if format_type == "json":
            return JSONResponse(content=scan_report)
        elif format_type == "html":
            # Generate HTML report
            html_content = generate_html_report(scan_report)
            return Response(content=html_content, media_type="text/html")
        elif format_type == "text":
            # Generate text report
            text_content = generate_text_report(scan_report)
            return Response(content=text_content, media_type="text/plain")
        else:
            return JSONResponse(
                status_code=400, content={"error": f"Unsupported format: {format_type}"}
            )
    except Exception as e:
        logger.error(f"Error exporting results: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Export failed: {str(e)}"}
        )


@app.get("/api/v1/error-detection/progress/{session_id}")
async def get_scan_progress(session_id: str):
    """Get real-time scan progress."""
    try:
        # For now, return mock progress
        # In a real implementation, you'd track progress in a database
        progress = {
            "progress": 75,
            "current_step": "Analyzing JavaScript files",
            "estimated_remaining": 30,
        }

        return JSONResponse(content=progress)
    except Exception as e:
        logger.error(f"Error getting progress: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to get progress: {str(e)}"}
        )


@app.post("/api/v1/error-detection/cancel/{session_id}")
async def cancel_operation(session_id: str):
    """Cancel ongoing scan or fix operation."""
    try:
        # For now, return success
        # In a real implementation, you'd cancel the operation
        return JSONResponse(content={"success": True})
    except Exception as e:
        logger.error(f"Error canceling operation: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to cancel operation: {str(e)}"}
        )


@app.get("/api/v1/error-detection/file-errors")
async def get_file_errors(file_path: str):
    """Get errors for a specific file."""
    try:
        # For now, return empty list
        # In a real implementation, you'd scan the specific file
        return JSONResponse(content=[])
    except Exception as e:
        logger.error(f"Error getting file errors: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to get file errors: {str(e)}"}
        )


@app.post("/api/v1/error-detection/fix/{error_id}")
async def apply_single_fix(error_id: str):
    """Apply fix for a specific error."""
    try:
        # For now, return mock result
        # In a real implementation, you'd apply the specific fix
        result = {
            "error_id": error_id,
            "success": True,
            "fix_applied": True,
            "backup_created": True,
            "backup_path": f"/backups/{error_id}.backup",
            "changes_made": ["Fixed accessibility issue"],
            "error_message": None,
            "fixed_at": datetime.now().isoformat(),
        }

        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"Error applying single fix: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Failed to apply fix: {str(e)}"}
        )


@app.post("/api/v1/error-detection/recommendations")
async def get_fix_recommendations(request: Request):
    """Get fix recommendations for an error."""
    try:
        data = await request.json()
        error = data.get("error", {})

        # Generate recommendations based on error type
        recommendations = []
        estimated_effort = "Low"
        risk_level = "Safe"

        if error.get("category") == "accessibility":
            recommendations = [
                "Add alt attributes to images",
                "Include ARIA labels for interactive elements",
                "Ensure proper heading hierarchy",
            ]
        elif error.get("category") == "seo":
            recommendations = [
                "Add meta description",
                "Include proper title tags",
                "Add structured data markup",
            ]
        elif error.get("category") == "performance":
            recommendations = [
                "Optimize image sizes",
                "Minify CSS and JavaScript",
                "Enable compression",
            ]
        elif error.get("category") == "security":
            recommendations = [
                "Remove hardcoded secrets",
                "Add Content Security Policy",
                "Validate user inputs",
            ]
        elif error.get("category") == "compatibility":
            recommendations = [
                "Add CSS fallbacks",
                "Use polyfills for modern features",
                "Test across different browsers",
            ]

        result = {
            "recommendations": recommendations,
            "estimated_effort": estimated_effort,
            "risk_level": risk_level,
        }

        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"Error getting recommendations: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to get recommendations: {str(e)}"},
        )


@app.get("/api/v1/error-detection/updates")
async def subscribe_to_updates(project_path: str = "."):
    """Subscribe to real-time updates."""
    try:
        # For now, return a simple response
        # In a real implementation, you'd use Server-Sent Events
        return JSONResponse(content={"message": "Updates endpoint"})
    except Exception as e:
        logger.error(f"Error with updates: {e}")
        return JSONResponse(
            status_code=500, content={"error": f"Updates failed: {str(e)}"}
        )


def generate_html_report(scan_report: Dict[str, Any]) -> str:
    """Generate HTML report."""
    html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Web Error Detection Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .error {{ border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }}
        .critical {{ border-left: 5px solid #ff4444; }}
        .high {{ border-left: 5px solid #ff8800; }}
        .medium {{ border-left: 5px solid #ffaa00; }}
        .low {{ border-left: 5px solid #00aa00; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat {{ background: #f8f8f8; padding: 15px; border-radius: 5px; text-align: center; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Web Error Detection Report</h1>
        <p>Generated: {scan_report.get('scan_timestamp', 'Unknown')}</p>
        <p>Duration: {scan_report.get('scan_duration_seconds', 0):.2f} seconds</p>
    </div>

    <div class="summary">
        <h2>Summary</h2>
        <div class="stats">
            <div class="stat">
                <h3>{scan_report.get('total_errors', 0)}</h3>
                <p>Total Errors</p>
            </div>
            <div class="stat">
                <h3>{scan_report.get('auto_fixable_errors', 0)}</h3>
                <p>Auto-Fixable</p>
            </div>
        </div>
    </div>
"""

    # Add errors by category
    if scan_report.get("errors_by_category"):
        html += "<h2>Errors by Category</h2><ul>"
        for category, count in scan_report["errors_by_category"].items():
            html += f"<li><strong>{category.title()}:</strong> {count}</li>"
        html += "</ul>"

    # Add detailed errors
    if scan_report.get("errors"):
        html += "<h2>Detailed Errors</h2>"
        for error in scan_report["errors"]:
            severity_class = error.get("severity", "low")
            html += f"""
            <div class="error {severity_class}">
                <h3>{error['title']}</h3>
                <p><strong>Category:</strong> {error['category']}</p>
                <p><strong>Severity:</strong> {error['severity']}</p>
                <p><strong>File:</strong> {error.get('file_path', 'N/A')}</p>
                <p><strong>Auto-fixable:</strong> {error.get('auto_fixable', False)}</p>
                <p><strong>Description:</strong> {error['description']}</p>
            </div>
            """

    html += """
</body>
</html>
"""
    return html


def generate_text_report(scan_report: Dict[str, Any]) -> str:
    """Generate text report."""
    report = []
    report.append("=" * 60)
    report.append("WEB ERROR DETECTION REPORT")
    report.append("=" * 60)
    report.append(f"Scan Time: {scan_report.get('scan_timestamp', 'Unknown')}")
    report.append(
        f"Duration: {scan_report.get('scan_duration_seconds', 0):.2f} seconds"
    )
    report.append(f"Total Errors: {scan_report.get('total_errors', 0)}")
    report.append("")

    # Errors by category
    if scan_report.get("errors_by_category"):
        report.append("ERRORS BY CATEGORY:")
        for category, count in scan_report["errors_by_category"].items():
            report.append(f"  {category.upper()}: {count}")
        report.append("")

    # Errors by severity
    if scan_report.get("errors_by_severity"):
        report.append("ERRORS BY SEVERITY:")
        for severity, count in scan_report["errors_by_severity"].items():
            report.append(f"  {severity.upper()}: {count}")
        report.append("")

    # Detailed errors
    if scan_report.get("errors"):
        report.append("DETAILED ERRORS:")
        for i, error in enumerate(scan_report["errors"], 1):
            report.append(f"{i}. {error['title']}")
            report.append(f"   Category: {error['category']}")
            report.append(f"   Severity: {error['severity']}")
            report.append(f"   File: {error.get('file_path', 'N/A')}")
            report.append(f"   Auto-fixable: {error.get('auto_fixable', False)}")
            report.append(f"   Description: {error['description']}")
            report.append("")

    return "\n".join(report)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="127.0.0.1", port=8000)
