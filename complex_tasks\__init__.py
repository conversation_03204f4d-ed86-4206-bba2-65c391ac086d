"""
Complex Code Tasks Module

This module provides comprehensive complex code task management using starcoder2
for architecture design, system integration, performance optimization, and
complex problem solving.
"""

from complex_tasks.architecture_designer import ArchitectureDesigner
from complex_tasks.models import (
    ComplexTask,
    ProgressReport,
    QualityMetrics,
    ResourceAllocation,
    TaskComplexity,
    TaskStatus,
    TaskType,
)
from complex_tasks.performance_optimizer import PerformanceOptimizer
from complex_tasks.problem_solver import ComplexProblemSolver
from complex_tasks.progress_tracker import ProgressTracker
from complex_tasks.quality_assurance import QualityAssurance
from complex_tasks.resource_monitor import ResourceMonitor
from complex_tasks.system_integrator import SystemIntegrator
from complex_tasks.task_manager import ComplexTaskManager

__all__ = [
    "ComplexTaskManager",
    "ArchitectureDesigner",
    "SystemIntegrator",
    "PerformanceOptimizer",
    "ComplexProblemSolver",
    "ResourceMonitor",
    "QualityAssurance",
    "ProgressTracker",
    "ComplexTask",
    "TaskStatus",
    "TaskComplexity",
    "TaskType",
    "ResourceAllocation",
    "QualityMetrics",
    "ProgressReport",
]
