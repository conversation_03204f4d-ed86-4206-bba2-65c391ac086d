"""
Database models for AI Coding Agent.
"""

# type: ignore[misc,valid-type]

from datetime import datetime, timezone
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import declarative_base, relationship

# Create the base class for all models
Base = declarative_base()  # type: ignore[valid-type,misc]

# Type alias for Base to fix mypy issues

if TYPE_CHECKING:
    from sqlalchemy.orm import DeclarativeBase

    BaseType = DeclarativeBase
else:
    BaseType = type(Base)


def utc_now() -> datetime:
    """Return timezone-aware UTC datetime"""
    return datetime.now(timezone.utc)


class User(Base):  # type: ignore[valid-type,misc]
    """User model for authentication and authorization"""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)

    # Session management fields
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    mfa_secret = Column(String(100), nullable=True)
    mfa_enabled = Column(Boolean, default=False)

    # Relationships
    projects = relationship("Project", back_populates="owner")
    sessions = relationship(
        "UserSession", back_populates="user", cascade="all, delete-orphan"
    )
    login_attempts_rel = relationship(
        "LoginAttempt", back_populates="user", cascade="all, delete-orphan"
    )
    auth_logs = relationship(
        "AuthLog", back_populates="user", cascade="all, delete-orphan"
    )

    def verify_password(self, password: str) -> bool:
        """Verify password against stored hash"""
        from passlib.context import CryptContext

        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        return pwd_context.verify(password, self.hashed_password)


class UserSession(Base):  # type: ignore[valid-type,misc]
    """User session tracking for security and management"""

    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String(100), unique=True, index=True, nullable=False)
    refresh_token = Column(String(100), unique=True, index=True, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    device_info = Column(JSON, nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_active = Column(Boolean, default=True)
    last_activity = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    created_at = Column(DateTime(timezone=True), default=utc_now)

    # Relationships
    user = relationship("User", back_populates="sessions")


class LoginAttempt(Base):  # type: ignore[valid-type,misc]
    """Track login attempts for security monitoring"""

    __tablename__ = "login_attempts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    username = Column(String(50), nullable=False)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(String(500), nullable=True)
    success = Column(Boolean, default=False)
    failure_reason = Column(String(200), nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)

    # Relationships
    user = relationship("User", back_populates="login_attempts_rel")


class AuthLog(Base):  # type: ignore[valid-type,misc]
    """Authentication and authorization audit log"""

    __tablename__ = "auth_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    event_type = Column(
        String(50), nullable=False
    )  # login, logout, password_change, etc.
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    details = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)

    # Relationships
    user = relationship("User", back_populates="auth_logs")


class MagicLink(Base):  # type: ignore[valid-type,misc]
    """Magic link for passwordless authentication"""

    __tablename__ = "magic_links"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), nullable=False)
    token = Column(String(100), unique=True, index=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    used = Column(Boolean, default=False)
    ip_address = Column(String(45), nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)


class Project(Base):  # type: ignore[valid-type,misc]
    """Project model for organizing code generation tasks"""

    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)

    # Relationships
    owner = relationship("User", back_populates="projects")
    files = relationship("CodeFile", back_populates="project")
    deployments = relationship("Deployment", back_populates="project")
    supabase_config = relationship(
        "SupabaseConfig", back_populates="project", uselist=False
    )
    migrations = relationship("DatabaseMigration", back_populates="project")
    supabase_tables = relationship("SupabaseTable", back_populates="project")

    # New fields for Supabase support
    backend_type = Column(String(50), nullable=True)  # supabase, custom, firebase, etc.
    project_type = Column(String(50), default="general")  # general, supabase, api, etc.


class CodeFile(Base):  # type: ignore[valid-type,misc]
    """Code file model for storing generated code"""

    __tablename__ = "code_files"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    content = Column(Text, nullable=True)
    path = Column(String(500), nullable=True)
    language = Column(String(50), nullable=True)
    is_directory = Column(Boolean, default=False)
    parent_id = Column(Integer, ForeignKey("code_files.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)

    # Relationships
    project = relationship("Project", back_populates="files")
    children = relationship(
        "CodeFile",
        back_populates="parent",
        # Prevent cascade delete to avoid accidental deletion
        cascade="all, delete-orphan",
    )
    parent = relationship("CodeFile", remote_side=[id], back_populates="children")


class Deployment(Base):  # type: ignore[valid-type,misc]
    """Deployment model for tracking deployments"""

    __tablename__ = "deployments"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    status = Column(
        String(50), default="pending"
    )  # pending, success, failed, in_progress
    environment = Column(String(50), default="development")
    url = Column(String(500), nullable=True)
    commit_hash = Column(String(100), nullable=True)
    logs = Column(Text, nullable=True)
    metadata_ = Column("metadata", JSON, default=dict)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    project = relationship("Project", back_populates="deployments")


class ModelRun(Base):  # type: ignore[valid-type,misc]
    """Model run tracking for analytics"""

    __tablename__ = "model_runs"

    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String(100), nullable=False)
    prompt = Column(Text, nullable=True)
    response = Column(Text, nullable=True)
    parameters = Column(JSON, default=dict)
    duration = Column(Integer, nullable=True)  # in milliseconds
    tokens_used = Column(Integer, nullable=True)
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)


class APIToken(Base):  # type: ignore[valid-type,misc]
    """API token for authentication"""

    __tablename__ = "api_tokens"

    id = Column(Integer, primary_key=True, index=True)
    token = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    last_used_at = Column(DateTime, nullable=True)
    scopes = Column(JSON, default=list)


class Settings(Base):  # type: ignore[valid-type,misc]
    """Application settings"""

    __tablename__ = "settings"

    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, index=True, nullable=False)
    value = Column(JSON, nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)


class SupabaseConfig(Base):  # type: ignore[valid-type,misc]
    """Supabase configuration for user projects"""

    __tablename__ = "supabase_configs"
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    supabase_url = Column(String(500), nullable=False)
    supabase_anon_key = Column(String(500), nullable=False)
    supabase_service_role_key = Column(String(500), nullable=True)
    database_url = Column(String(500), nullable=True)
    region = Column(String(50), nullable=True)
    organization_id = Column(String(100), nullable=True)
    project_ref = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    # Relationships
    project = relationship("Project", back_populates="supabase_config")


class DatabaseMigration(Base):  # type: ignore[valid-type,misc]
    """Database migration tracking for Supabase projects"""

    __tablename__ = "database_migrations"
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    migration_name = Column(String(200), nullable=False)
    migration_file = Column(String(500), nullable=False)
    sql_content = Column(Text, nullable=False)
    status = Column(String(50), default="pending")
    applied_at = Column(DateTime(timezone=True), nullable=True)
    rollback_sql = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    # Relationships
    project = relationship("Project", back_populates="migrations")


class SupabaseTable(Base):  # type: ignore[valid-type,misc]
    """Supabase table schema tracking"""

    __tablename__ = "supabase_tables"
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    table_name = Column(String(100), nullable=False)
    schema_name = Column(String(50), default="public")
    table_definition = Column(JSON, nullable=False)
    columns = Column(JSON, nullable=False)
    indexes = Column(JSON, default=list)
    foreign_keys = Column(JSON, default=list)
    rls_enabled = Column(Boolean, default=False)
    rls_policies = Column(JSON, default=list)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    # Relationships
    project = relationship("Project", back_populates="supabase_tables")
