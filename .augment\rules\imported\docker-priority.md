---
type: "agent_requested"
description: "Example description"
---
# Docker First Policy

- ✅ Always generate a Dockerfile when creating or editing any project component (API, site, plugin).
- ✅ Default to container-based execution: **do not suggest running app code directly on the host system**.
- ✅ Use `docker-compose` for project orchestration — both for development (`docker-compose.override.yml`) and production.
- ✅ For AI-managed websites:
  - Use the `SiteContainerManager` to build/start/stop containers.
  - Assign dynamic ports via the built-in port allocator.
  - Edit files only through mounted volumes ​or via agent APIs interacting with container filesystem.
- ✅ Generated Dockerfiles must:
  - Use multi-stage builds (builder + runtime).
  - Drop to a non-root user.
  - Include a `.dockerignore`.
  - Use HEALTHCHECK for container readiness.
  - Set resource limits in `docker-compose.yml`.
- ✅ All local hosting instructions (CLI or UI) should reference `localhost:<mapped_port>` with Nginx reverse proxy when applicable.
- ✅ Agent-generated debugging instructions must run inside containers (e.g. `docker exec`) unless explicitly told otherwise.
- ✅ Always update Compose services in `docker-compose.yml` when a new site container is created or removed.

# Why this rule exists
- Ensures consistent local vs production parity.
- Prevents host pollution or accidental global package installs.
- Enables lifecycle management, security isolation, and reproducible deployments.

# Implementation Guidelines
- When creating new websites, always start with `SiteContainerManager.create_site_container()`
- Use the existing container templates in `containers/Dockerfile.template`
- Leverage the built-in port allocation system (8080-9000 range)
- Integrate with the existing `ai-coding-network` Docker network
- Follow the established volume mounting patterns for persistent data
- Use the health check endpoints (`/health`) for container monitoring
- Implement proper logging to the container logs directory

# Integration with AI Coding Agent
- All website operations should go through the `SiteContainerManager` API
- Use the existing CLI commands in `cli/external_hosting_commands.py`
- Leverage the external hosting system for deployment to Netlify, GitHub Pages, Vercel
- Follow the established security patterns with rate limiting and HTTPS support
- Use the container monitoring system for health checks and automatic restarts
