#!/usr/bin/env python3
"""
GPU Monitoring Module for NVIDIA Quadro P1000
Provides real-time GPU status and performance metrics.
"""

import asyncio
import json
import logging
import subprocess
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class GPUStatus:
    """GPU status information."""

    name: str
    total_memory_mb: int
    free_memory_mb: int
    used_memory_mb: int
    temperature_c: int
    utilization_percent: int
    memory_usage_percent: float
    status: str
    timestamp: datetime


@dataclass
class ModelPerformance:
    """Model performance metrics."""

    model_name: str
    avg_response_time_ms: float
    avg_gpu_utilization_percent: float
    success_rate_percent: float
    gpu_layers: int
    gpu_memory_mb: int
    last_used: datetime


class GPUMonitor:
    """GPU monitoring for NVIDIA Quadro P1000."""

    def __init__(self):
        self.gpu_name = "NVIDIA Quadro P1000"
        self.total_vram_mb = 4096
        self.status_history: List[GPUStatus] = []
        self.model_performance: Dict[str, ModelPerformance] = {}
        self.max_history_size = 100

    async def get_gpu_status(self) -> Dict[str, Any]:
        """Get current GPU status."""
        try:
            # Get GPU information using nvidia-smi
            result = subprocess.run(
                [
                    "nvidia-smi",
                    "--query-gpu=name,memory.total,memory.free,memory.used,temperature.gpu,utilization.gpu",
                    "--format=csv,noheader,nounits",
                ],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if result.returncode == 0:
                gpu_info = result.stdout.strip().split(",")

                # Parse GPU information
                name = gpu_info[0].strip()
                total_memory = int(gpu_info[1])
                free_memory = int(gpu_info[2])
                used_memory = int(gpu_info[3])
                temperature = int(gpu_info[4])
                utilization = int(gpu_info[5])

                # Calculate memory usage percentage
                memory_usage_percent = (
                    (used_memory / total_memory) * 100 if total_memory > 0 else 0
                )

                # Determine status
                status = self._determine_status(
                    temperature, utilization, memory_usage_percent
                )

                # Create GPU status object
                gpu_status = GPUStatus(
                    name=name,
                    total_memory_mb=total_memory,
                    free_memory_mb=free_memory,
                    used_memory_mb=used_memory,
                    temperature_c=temperature,
                    utilization_percent=utilization,
                    memory_usage_percent=round(memory_usage_percent, 2),
                    status=status,
                    timestamp=datetime.now(),
                )

                # Add to history
                self._add_to_history(gpu_status)

                return asdict(gpu_status)
            else:
                logger.error(f"nvidia-smi failed with return code: {result.returncode}")
                return self._get_error_status("Failed to get GPU information")

        except subprocess.TimeoutExpired:
            logger.error("nvidia-smi command timed out")
            return self._get_error_status("GPU monitoring command timed out")
        except Exception as e:
            logger.error(f"Error getting GPU status: {e}")
            return self._get_error_status(f"Error: {str(e)}")

    def _determine_status(
        self, temperature: int, utilization: int, memory_usage: float
    ) -> str:
        """Determine GPU status based on metrics."""
        # Check for critical conditions
        if temperature > 85 or utilization > 95 or memory_usage > 95:
            return "critical"

        # Check for warning conditions
        if temperature > 75 or utilization > 85 or memory_usage > 85:
            return "warning"

        # Check for healthy conditions
        if temperature < 70 and utilization < 80 and memory_usage < 80:
            return "healthy"

        return "warning"

    def _get_error_status(self, error_message: str) -> Dict[str, Any]:
        """Get error status response."""
        return {
            "name": self.gpu_name,
            "total_memory_mb": self.total_vram_mb,
            "free_memory_mb": 0,
            "used_memory_mb": 0,
            "temperature_c": 0,
            "utilization_percent": 0,
            "memory_usage_percent": 0,
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": error_message,
        }

    def _add_to_history(self, gpu_status: GPUStatus):
        """Add GPU status to history."""
        self.status_history.append(gpu_status)

        # Keep only the last max_history_size entries
        if len(self.status_history) > self.max_history_size:
            self.status_history = self.status_history[-self.max_history_size :]

    async def get_gpu_history(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get GPU status history for the last N minutes."""
        cutoff_time = datetime.now().timestamp() - (minutes * 60)

        history = []
        for status in self.status_history:
            if status.timestamp.timestamp() >= cutoff_time:
                history.append(asdict(status))

        return history

    async def get_gpu_metrics(self) -> Dict[str, Any]:
        """Get comprehensive GPU metrics."""
        current_status = await self.get_gpu_status()

        # Calculate averages from history
        if self.status_history:
            recent_history = self.status_history[-10:]  # Last 10 entries

            avg_temperature = sum(s.temperature_c for s in recent_history) / len(
                recent_history
            )
            avg_utilization = sum(s.utilization_percent for s in recent_history) / len(
                recent_history
            )
            avg_memory_usage = sum(
                s.memory_usage_percent for s in recent_history
            ) / len(recent_history)

            # Calculate trends
            if len(recent_history) >= 2:
                temp_trend = (
                    recent_history[-1].temperature_c - recent_history[0].temperature_c
                )
                util_trend = (
                    recent_history[-1].utilization_percent
                    - recent_history[0].utilization_percent
                )
                memory_trend = (
                    recent_history[-1].memory_usage_percent
                    - recent_history[0].memory_usage_percent
                )
            else:
                temp_trend = util_trend = memory_trend = 0
        else:
            avg_temperature = avg_utilization = avg_memory_usage = 0
            temp_trend = util_trend = memory_trend = 0

        return {
            "current": current_status,
            "averages": {
                "temperature_c": round(avg_temperature, 1),
                "utilization_percent": round(avg_utilization, 1),
                "memory_usage_percent": round(avg_memory_usage, 1),
            },
            "trends": {
                "temperature_trend": round(temp_trend, 1),
                "utilization_trend": round(util_trend, 1),
                "memory_trend": round(memory_trend, 1),
            },
            "alerts": self._generate_alerts(current_status),
        }

    def _generate_alerts(self, gpu_status: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate alerts based on GPU status."""
        alerts = []

        if gpu_status.get("status") == "error":
            alerts.append(
                {
                    "type": "error",
                    "message": gpu_status.get("error", "Unknown GPU error"),
                    "severity": "critical",
                }
            )
            return alerts

        temperature = gpu_status.get("temperature_c", 0)
        utilization = gpu_status.get("utilization_percent", 0)
        memory_usage = gpu_status.get("memory_usage_percent", 0)

        # Temperature alerts
        if temperature > 85:
            alerts.append(
                {
                    "type": "temperature",
                    "message": f"GPU temperature is critical: {temperature}°C",
                    "severity": "critical",
                }
            )
        elif temperature > 75:
            alerts.append(
                {
                    "type": "temperature",
                    "message": f"GPU temperature is high: {temperature}°C",
                    "severity": "warning",
                }
            )

        # Utilization alerts
        if utilization > 95:
            alerts.append(
                {
                    "type": "utilization",
                    "message": f"GPU utilization is critical: {utilization}%",
                    "severity": "critical",
                }
            )
        elif utilization > 85:
            alerts.append(
                {
                    "type": "utilization",
                    "message": f"GPU utilization is high: {utilization}%",
                    "severity": "warning",
                }
            )

        # Memory usage alerts
        if memory_usage > 95:
            alerts.append(
                {
                    "type": "memory",
                    "message": f"GPU memory usage is critical: {memory_usage}%",
                    "severity": "critical",
                }
            )
        elif memory_usage > 85:
            alerts.append(
                {
                    "type": "memory",
                    "message": f"GPU memory usage is high: {memory_usage}%",
                    "severity": "warning",
                }
            )

        return alerts

    async def update_model_performance(
        self, model_name: str, performance_data: Dict[str, Any]
    ):
        """Update model performance metrics."""
        try:
            model_perf = ModelPerformance(
                model_name=model_name,
                avg_response_time_ms=performance_data.get("avg_response_time_ms", 0),
                avg_gpu_utilization_percent=performance_data.get(
                    "avg_gpu_utilization_percent", 0
                ),
                success_rate_percent=performance_data.get("success_rate_percent", 0),
                gpu_layers=performance_data.get("gpu_layers", 24),
                gpu_memory_mb=performance_data.get("gpu_memory_mb", 1536),
                last_used=datetime.now(),
            )

            self.model_performance[model_name] = model_perf

        except Exception as e:
            logger.error(f"Failed to update model performance for {model_name}: {e}")

    async def get_model_performance(self) -> Dict[str, Any]:
        """Get model performance metrics."""
        models = []
        for model_name, perf in self.model_performance.items():
            models.append(asdict(perf))

        return {
            "models": models,
            "total_models": len(models),
            "timestamp": datetime.now().isoformat(),
        }

    async def get_gpu_recommendations(self) -> List[str]:
        """Get GPU optimization recommendations."""
        current_status = await self.get_gpu_status()
        recommendations = []

        if current_status.get("status") == "error":
            recommendations.append("Check GPU drivers and ensure nvidia-smi is working")
            return recommendations

        temperature = current_status.get("temperature_c", 0)
        utilization = current_status.get("utilization_percent", 0)
        memory_usage = current_status.get("memory_usage_percent", 0)

        # Temperature recommendations
        if temperature > 80:
            recommendations.append(
                "Consider improving GPU cooling - temperature is very high"
            )
        elif temperature > 70:
            recommendations.append(
                "Monitor GPU temperature - consider improving airflow"
            )

        # Utilization recommendations
        if utilization < 30:
            recommendations.append(
                "GPU utilization is low - consider increasing GPU layers for better performance"
            )
        elif utilization > 90:
            recommendations.append(
                "GPU utilization is very high - monitor for thermal throttling"
            )

        # Memory recommendations
        if memory_usage > 90:
            recommendations.append(
                "GPU memory usage is critical - consider unloading unused models"
            )
        elif memory_usage < 50:
            recommendations.append(
                "GPU memory usage is low - consider loading more models or increasing GPU layers"
            )

        # General recommendations
        if len(self.model_performance) == 0:
            recommendations.append(
                "No models loaded - consider loading models to utilize GPU"
            )

        return recommendations

    async def get_gpu_summary(self) -> Dict[str, Any]:
        """Get GPU summary information."""
        current_status = await self.get_gpu_status()
        metrics = await self.get_gpu_metrics()
        recommendations = await self.get_gpu_recommendations()
        model_performance = await self.get_model_performance()

        return {
            "gpu_info": {
                "name": self.gpu_name,
                "total_vram_mb": self.total_vram_mb,
                "driver_version": await self._get_driver_version(),
                "cuda_version": await self._get_cuda_version(),
            },
            "current_status": current_status,
            "metrics": metrics,
            "model_performance": model_performance,
            "recommendations": recommendations,
            "timestamp": datetime.now().isoformat(),
        }

    async def _get_driver_version(self) -> str:
        """Get NVIDIA driver version."""
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=driver_version", "--format=csv,noheader"],
                capture_output=True,
                text=True,
                timeout=5,
            )

            if result.returncode == 0:
                return result.stdout.strip()
        except Exception as e:
            logger.error(f"Failed to get driver version: {e}")

        return "Unknown"

    async def _get_cuda_version(self) -> str:
        """Get CUDA version."""
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=cuda_version", "--format=csv,noheader"],
                capture_output=True,
                text=True,
                timeout=5,
            )

            if result.returncode == 0:
                return result.stdout.strip()
        except Exception as e:
            logger.error(f"Failed to get CUDA version: {e}")

        return "Unknown"


# Global GPU monitor instance
gpu_monitor = GPUMonitor()


async def get_gpu_status() -> Dict[str, Any]:
    """Get current GPU status."""
    return await gpu_monitor.get_gpu_status()


async def get_gpu_metrics() -> Dict[str, Any]:
    """Get comprehensive GPU metrics."""
    return await gpu_monitor.get_gpu_metrics()


async def get_gpu_summary() -> Dict[str, Any]:
    """Get GPU summary information."""
    return await gpu_monitor.get_gpu_summary()


async def get_model_performance() -> Dict[str, Any]:
    """Get model performance metrics."""
    return await gpu_monitor.get_model_performance()


async def update_model_performance(model_name: str, performance_data: Dict[str, Any]):
    """Update model performance metrics."""
    await gpu_monitor.update_model_performance(model_name, performance_data)


if __name__ == "__main__":

    async def test_gpu_monitor():
        """Test GPU monitoring functionality."""
        print("🚀 Testing GPU Monitor for NVIDIA Quadro P1000")
        print("=" * 50)

        # Test GPU status
        print("\n📊 GPU Status:")
        status = await get_gpu_status()
        print(json.dumps(status, indent=2, default=str))

        # Test GPU metrics
        print("\n📈 GPU Metrics:")
        metrics = await get_gpu_metrics()
        print(json.dumps(metrics, indent=2, default=str))

        # Test GPU summary
        print("\n📋 GPU Summary:")
        summary = await get_gpu_summary()
        print(json.dumps(summary, indent=2, default=str))

    asyncio.run(test_gpu_monitor())
