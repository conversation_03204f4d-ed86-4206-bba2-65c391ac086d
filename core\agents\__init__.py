#!/usr/bin/env python3
"""
Core Agents Package - Modular Agent Components
"""

# Import main agent class
from core.agents.agent_main import AIAgent, get_agent

# Import all agent classes
from core.agents.architect_agent import ArchitectAgent, TaskPriority
from core.agents.backend_agent import BackendAgent

# Import base classes and utilities
from core.agents.base_agent import Agent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from core.agents.container_agent import ContainerAgent

# Import manager classes
from core.agents.feedback_manager import FeedbackManager
from core.agents.frontend_agent import FrontendAgent
from core.agents.learning_agent import LearningAgent
from core.agents.plugin_manager import PluginInterface, PluginManager
from core.agents.shell_ops_agent import ShellOpsAgent
from core.agents.task_manager import CommandRouter, TaskManager
from core.agents.update_agent import UpdateAgent

# Export all public classes
__all__ = [
    # Base classes
    "AgentError",
    "AgentLogger",
    "<PERSON><PERSON>r<PERSON><PERSON><PERSON>",
    # Manager classes
    "FeedbackManager",
    "<PERSON><PERSON>anager",
    "CommandRouter",
    "PluginManager",
    "PluginInterface",
    # Main agent
    "AIAgent",
    "get_agent",
    # Agent classes
    "ArchitectAgent",
    "BackendAgent",
    "ContainerAgent",
    "FrontendAgent",
    "LearningAgent",
    "ShellOpsAgent",
    "UpdateAgent",
    # Enums
    "TaskPriority",
]
