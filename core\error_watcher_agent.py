import asyncio
import json
import logging
import threading
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import docker
import requests
import uvicorn
from fastapi import FastAPI

from models.ollama_manager import OllamaModelManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app for health check
app = FastAPI(title="Error Watcher Agent")


@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "error_watcher_agent"}


@app.get("/status")
def get_status():
    """Get error watcher status"""
    return {
        "monitoring_containers": (
            len(error_watcher.monitored_containers)
            if "error_watcher" in globals()
            else 0
        ),
        "errors_detected": (
            error_watcher.errors_detected if "error_watcher" in globals() else 0
        ),
        "last_check": (
            error_watcher.last_check if "error_watcher" in globals() else None
        ),
    }


class ErrorWatcherAgent:
    """Agent that monitors container logs for errors and reports them"""

    def __init__(self, config_path: str = "config/error_watcher_agent_config.json"):
        self.config_path = config_path
        self.config = self._load_config()

        # Initialize Docker client
        try:
            self.docker_client = docker.from_env()
        except Exception as e:
            logger.error(f"Failed to connect to Docker: {str(e)}")
            self.docker_client = None

        # Initialize Ollama manager for error classification
        self.ollama_manager = OllamaModelManager()

        # Tracking variables
        self.monitored_containers: Dict[str, Any] = {}
        self.errors_detected = 0
        self.last_check = None
        self.is_running = False

        logger.info("ErrorWatcherAgent initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {self.config_path}")
            return config
        except FileNotFoundError:
            logger.warning(
                f"Configuration file {self.config_path} not found, using defaults"
            )
            return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "model_name": "mistral:7b-instruct-q4_0",
            "system_prompt": "You are an error classification system. Analyze log entries and classify them as 'error', 'warning', or 'info'. Only respond with the classification.",
            "check_interval": 30,
            "error_keywords": ["error", "exception", "failed", "crash", "critical"],
            "warning_keywords": ["warning", "warn", "deprecated"],
            "dashboard_api_url": "http://api:8000/api/v1/dashboard/errors",
            "container_filters": {
                "include_patterns": ["ai-coding-*"],
                "exclude_patterns": ["ai-coding-db", "ai-coding-redis"],
            },
        }

    def start_monitoring(self):
        """Start monitoring container logs"""
        if not self.docker_client:
            logger.error("Cannot start monitoring: Docker client not available")
            return

        self.is_running = True
        logger.info("Starting error monitoring")

        while self.is_running:
            try:
                self._update_monitored_containers()
                self._check_container_logs()
                self.last_check = time.time()

                # Wait for next check
                time.sleep(self.config.get("check_interval", 30))

            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(5)  # Short delay before retrying

    def stop_monitoring(self):
        """Stop monitoring container logs"""
        self.is_running = False
        logger.info("Stopping error monitoring")

    def _update_monitored_containers(self):
        """Update the list of containers to monitor"""
        try:
            containers = self.docker_client.containers.list(all=False)
            current_containers = {}

            for container in containers:
                container_name = container.name

                # Apply filters
                if self._should_monitor_container(container_name):
                    current_containers[container.id] = {
                        "name": container_name,
                        "container": container,
                        "last_log_time": self.monitored_containers.get(
                            container.id, {}
                        ).get("last_log_time", 0),
                    }

            self.monitored_containers = current_containers

        except Exception as e:
            logger.error(f"Error updating monitored containers: {str(e)}")

    def _should_monitor_container(self, container_name: str) -> bool:
        """Check if a container should be monitored based on filters"""
        filters = self.config.get("container_filters", {})

        # Check include patterns
        include_patterns = filters.get("include_patterns", [])
        if include_patterns:
            if not any(pattern in container_name for pattern in include_patterns):
                return False

        # Check exclude patterns
        exclude_patterns = filters.get("exclude_patterns", [])
        if exclude_patterns:
            if any(pattern in container_name for pattern in exclude_patterns):
                return False

        return True

    def _check_container_logs(self):
        """Check logs for all monitored containers"""
        for container_id, container_info in self.monitored_containers.items():
            try:
                self._check_single_container_logs(container_info)
            except Exception as e:
                logger.error(
                    f"Error checking logs for {container_info['name']}: {str(e)}"
                )

    def _check_single_container_logs(self, container_info: Dict[str, Any]):
        """Check logs for a single container"""
        container = container_info["container"]
        last_log_time = container_info["last_log_time"]

        try:
            # Get recent logs (since last check)
            logs = container.logs(
                since=(
                    int(last_log_time) if last_log_time else int(time.time() - 300)
                ),  # Last 5 minutes if no previous time
                tail=100,
                timestamps=True,
            ).decode("utf-8")

            if logs.strip():
                self._analyze_logs(logs, container_info["name"])
                container_info["last_log_time"] = time.time()

        except Exception as e:
            logger.error(f"Error getting logs for {container_info['name']}: {str(e)}")

    def _analyze_logs(self, logs: str, container_name: str):
        """Analyze logs for errors using LLM classification"""
        log_lines = logs.strip().split("\n")

        for line in log_lines:
            if not line.strip():
                continue

            # Quick keyword check first
            severity = self._quick_classify_log_line(line)

            if severity in ["error", "warning"]:
                # Use LLM for more detailed classification
                llm_classification = self._llm_classify_log_line(line)

                if llm_classification == "error":
                    self._report_error(line, container_name, severity)

    def _quick_classify_log_line(self, log_line: str) -> str:
        """Quick classification using keyword matching"""
        log_lower = log_line.lower()

        error_keywords = self.config.get("error_keywords", [])
        warning_keywords = self.config.get("warning_keywords", [])

        if any(keyword in log_lower for keyword in error_keywords):
            return "error"
        elif any(keyword in log_lower for keyword in warning_keywords):
            return "warning"
        else:
            return "info"

    def _llm_classify_log_line(self, log_line: str) -> str:
        """Classify log line using LLM"""
        try:
            prompt = f"""
            {self.config.get('system_prompt', '')}

            Log entry: {log_line}

            Classification:"""

            response = self.ollama_manager.generate_response(
                prompt,
                model_name=self.config.get("model_name", "mistral:7b-instruct-q4_0"),
            )

            classification = response.get("response", "info").strip().lower()

            # Ensure valid classification
            if classification in ["error", "warning", "info"]:
                return classification
            else:
                return "info"

        except Exception as e:
            logger.error(f"Error in LLM classification: {str(e)}")
            return "info"

    def _report_error(self, log_line: str, container_name: str, severity: str):
        """Report error to the dashboard API"""
        try:
            self.errors_detected += 1

            error_data = {
                "timestamp": time.time(),
                "container_name": container_name,
                "severity": severity,
                "message": log_line,
                "source": "error_watcher_agent",
            }

            dashboard_url = self.config.get(
                "dashboard_api_url", "http://api:8000/api/v1/dashboard/errors"
            )

            response = requests.post(dashboard_url, json=error_data, timeout=10)

            if response.status_code == 200:
                logger.info(f"Reported {severity} from {container_name} to dashboard")
            else:
                logger.warning(
                    f"Failed to report error to dashboard: {response.status_code}"
                )

        except Exception as e:
            logger.error(f"Error reporting to dashboard: {str(e)}")


# Global error watcher instance
error_watcher = None


def run_error_watcher():
    """Run the error watcher agent"""
    global error_watcher

    try:
        error_watcher = ErrorWatcherAgent()
        error_watcher.start_monitoring()

    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"ErrorWatcherAgent error: {str(e)}")
    finally:
        if error_watcher:
            error_watcher.stop_monitoring()


if __name__ == "__main__":
    import threading

    # Start the error watcher in a separate thread
    watcher_thread = threading.Thread(target=run_error_watcher, daemon=True)
    watcher_thread.start()

    # Run the FastAPI app
    uvicorn.run(app, host="0.0.0.0", port=8091)
