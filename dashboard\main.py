#!/usr/bin/env python3
"""
Dashboard Main Application
FastAPI application for the dashboard backend
"""

import logging
import os

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AI Coding Agent Dashboard",
    description="Dashboard backend with WebSocket support for real-time user interface",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import dashboard routes with error handling
try:
    from api.dashboard_routes import router as dashboard_router

    app.include_router(dashboard_router)
    logger.info("Dashboard routes included successfully")
except ImportError as e:
    logger.warning(f"Could not import dashboard routes: {e}")
    # Create a placeholder router if dashboard routes are not available
    from fastapi import APIRouter

    dashboard_router = APIRouter()

    @dashboard_router.get("/placeholder")
    async def placeholder():
        return {"message": "Dashboard routes not available"}

    app.include_router(dashboard_router)


# Root endpoint
@app.get("/")
async def root():
    """Dashboard root endpoint"""
    return {
        "service": "dashboard-backend",
        "version": "1.0.0",
        "status": "running",
        "port": int(os.getenv("DASHBOARD_PORT", "8080")),
        "endpoints": [
            "/health",
            "/api/dashboard/summary",
            "/api/dashboard/metrics",
            "/api/dashboard/notifications",
            "/api/dashboard/real-time-status",
            "/api/dashboard/config",
            "/api/dashboard/export",
            "/api/dashboard/ws",
        ],
        "documentation": "/docs",
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    """Dashboard health check endpoint"""
    return {
        "status": "healthy",
        "service": "dashboard-backend",
        "version": "1.0.0",
        "port": int(os.getenv("DASHBOARD_PORT", "8080")),
    }


if __name__ == "__main__":
    import uvicorn

    # Get port from environment or default to 8080
    port = int(os.getenv("DASHBOARD_PORT", "8080"))
    host = os.getenv("DASHBOARD_HOST", "0.0.0.0")

    logger.info(f"Starting Dashboard Backend on {host}:{port}")

    uvicorn.run(
        "dashboard.main:app", host=host, port=port, reload=False, log_level="info"
    )
