"""
Web Search and Update Monitoring API Routes

Provides REST API endpoints for web search capabilities and update monitoring
for local Ollama LLM integration.
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel

from core.agent import get_agent
from trend_monitoring.update_monitor import (
    BestPracticeChange,
    SecurityVulnerability,
    UpdateInfo,
    UpdateMonitor,
)
from trend_monitoring.web_search import SearchResult, WebSearchEngine

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/web-search", tags=["Web Search & Update Monitoring"])


# Pydantic models for request/response
class SearchRequest(BaseModel):
    query: str
    language: Optional[str] = None
    framework: Optional[str] = None
    max_results: Optional[int] = 20


class SearchResponse(BaseModel):
    success: bool
    results: List[Dict[str, Any]]
    total_results: int
    query: str
    search_time: float


class UpdateSummaryResponse(BaseModel):
    success: bool
    summary: Dict[str, Any]
    last_check: Optional[str]


class UpdateRecommendationResponse(BaseModel):
    success: bool
    recommendations: List[Dict[str, Any]]
    total_recommendations: int


class VulnerabilityResponse(BaseModel):
    success: bool
    vulnerabilities: List[Dict[str, Any]]
    total_vulnerabilities: int
    critical_count: int


class BestPracticeResponse(BaseModel):
    success: bool
    changes: List[Dict[str, Any]]
    total_changes: int


class MonitoringStatusResponse(BaseModel):
    success: bool
    is_monitoring: bool
    last_check: Optional[str]
    check_errors: int
    metrics: Dict[str, Any]


# Global instances
web_search_engine = None
update_monitor = None


async def get_web_search_engine():
    """Get or create web search engine instance"""
    global web_search_engine
    if web_search_engine is None:
        config = {
            "trusted_domains": [
                "developer.mozilla.org",
                "docs.python.org",
                "owasp.org",
                "stackoverflow.com",
                "github.com",
                "dev.to",
            ],
            "blocked_domains": [],
            "user_agent": "AICodingAgent/1.0",
            "rate_limit_delay": 1.0,
            "max_retries": 3,
            "timeout": 30,
            "cache_ttl": 3600,
            "max_workers": 5,
            "rss_feeds": {
                "mdn": {
                    "name": "MDN Web Docs",
                    "url": "https://developer.mozilla.org/en-US/sitemap.xml",
                    "category": "documentation",
                    "check_interval": 3600,
                    "priority": "high",
                },
                "python_docs": {
                    "name": "Python Documentation",
                    "url": "https://docs.python.org/3/feeds/news.rss",
                    "category": "documentation",
                    "check_interval": 7200,
                    "priority": "high",
                },
                "owasp": {
                    "name": "OWASP Security",
                    "url": "https://owasp.org/feeds/",
                    "category": "security",
                    "check_interval": 1800,
                    "priority": "critical",
                },
            },
        }
        web_search_engine = WebSearchEngine(config)
        await web_search_engine.start()
    return web_search_engine


async def get_update_monitor():
    """Get or create update monitor instance"""
    global update_monitor
    if update_monitor is None:
        config = {
            "check_intervals": {
                "framework_updates": 3600,
                "security_vulnerabilities": 1800,
                "best_practices": 7200,
                "dependency_updates": 3600,
            },
            "api_endpoints": {
                "github": "https://api.github.com",
                "pypi": "https://pypi.org/pypi",
                "npm": "https://registry.npmjs.org",
                "nvd": "https://services.nvd.nist.gov/rest/json/cves/2.0",
            },
            "max_workers": 5,
            "frameworks": {
                "python": {
                    "django": {"current_version": "4.2.0"},
                    "flask": {"current_version": "2.3.0"},
                    "fastapi": {"current_version": "0.100.0"},
                },
                "javascript": {
                    "react": {"current_version": "18.2.0"},
                    "vue": {"current_version": "3.3.0"},
                    "angular": {"current_version": "16.0.0"},
                },
            },
            "requirements_file": "requirements.txt",
        }
        update_monitor = UpdateMonitor(config)
        await update_monitor.start_monitoring()
    return update_monitor


@router.post("/search", response_model=SearchResponse)
async def search_best_practices(request: SearchRequest, agent=Depends(get_agent)):
    """Search for coding best practices"""
    try:
        start_time = datetime.now()

        web_engine = await get_web_search_engine()
        results = await web_engine.search_best_practices(
            query=request.query, language=request.language, framework=request.framework
        )

        # Limit results
        limited_results = results[: request.max_results]

        # Convert to dictionaries
        result_dicts = [result.to_dict() for result in limited_results]

        search_time = (datetime.now() - start_time).total_seconds()

        return SearchResponse(
            success=True,
            results=result_dicts,
            total_results=len(results),
            query=request.query,
            search_time=search_time,
        )

    except Exception as e:
        logger.error(f"Error in web search: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/search", response_model=SearchResponse)
async def search_best_practices_get(
    query: str = Query(..., description="Search query"),
    language: Optional[str] = Query(None, description="Programming language filter"),
    framework: Optional[str] = Query(None, description="Framework filter"),
    max_results: int = Query(20, description="Maximum number of results"),
):
    """Search for coding best practices (GET endpoint)"""
    try:
        start_time = datetime.now()

        web_engine = await get_web_search_engine()
        results = await web_engine.search_best_practices(
            query=query, language=language, framework=framework
        )

        # Limit results
        limited_results = results[:max_results]

        # Convert to dictionaries
        result_dicts = [result.to_dict() for result in limited_results]

        search_time = (datetime.now() - start_time).total_seconds()

        return SearchResponse(
            success=True,
            results=result_dicts,
            total_results=len(results),
            query=query,
            search_time=search_time,
        )

    except Exception as e:
        logger.error(f"Error in web search: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/update-summary", response_model=UpdateSummaryResponse)
async def get_update_summary():
    """Get summary of all updates and monitoring status"""
    try:
        update_mon = await get_update_monitor()
        summary = await update_mon.get_update_summary()

        return UpdateSummaryResponse(
            success=True, summary=summary, last_check=summary.get("last_check")
        )

    except Exception as e:
        logger.error(f"Error getting update summary: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get update summary: {str(e)}"
        )


@router.get("/recommendations", response_model=UpdateRecommendationResponse)
async def get_update_recommendations():
    """Get recommended updates based on priority"""
    try:
        update_mon = await get_update_monitor()
        recommendations = await update_mon.get_recommended_updates()

        # Convert to dictionaries
        rec_dicts = [rec.to_dict() for rec in recommendations]

        return UpdateRecommendationResponse(
            success=True,
            recommendations=rec_dicts,
            total_recommendations=len(recommendations),
        )

    except Exception as e:
        logger.error(f"Error getting update recommendations: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get recommendations: {str(e)}"
        )


@router.get("/vulnerabilities", response_model=VulnerabilityResponse)
async def get_critical_vulnerabilities():
    """Get critical security vulnerabilities"""
    try:
        update_mon = await get_update_monitor()
        vulnerabilities = await update_mon.get_critical_vulnerabilities()

        # Convert to dictionaries
        vuln_dicts = [vuln.to_dict() for vuln in vulnerabilities]

        critical_count = len(
            [v for v in vulnerabilities if v.severity in ["high", "critical"]]
        )

        return VulnerabilityResponse(
            success=True,
            vulnerabilities=vuln_dicts,
            total_vulnerabilities=len(vulnerabilities),
            critical_count=critical_count,
        )

    except Exception as e:
        logger.error(f"Error getting vulnerabilities: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get vulnerabilities: {str(e)}"
        )


@router.get("/best-practices", response_model=BestPracticeResponse)
async def get_recent_best_practice_changes():
    """Get recent best practice changes"""
    try:
        update_mon = await get_update_monitor()
        changes = await update_mon.get_recent_best_practice_changes()

        # Convert to dictionaries
        change_dicts = [change.to_dict() for change in changes]

        return BestPracticeResponse(
            success=True, changes=change_dicts, total_changes=len(changes)
        )

    except Exception as e:
        logger.error(f"Error getting best practice changes: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get best practice changes: {str(e)}"
        )


@router.get("/status", response_model=MonitoringStatusResponse)
async def get_monitoring_status():
    """Get monitoring system status"""
    try:
        update_mon = await get_update_monitor()
        web_engine = await get_web_search_engine()

        # Get update summary for metrics
        summary = await update_mon.get_update_summary()

        return MonitoringStatusResponse(
            success=True,
            is_monitoring=update_mon.is_monitoring,
            last_check=summary.get("last_check"),
            check_errors=summary.get("check_errors", 0),
            metrics=summary,
        )

    except Exception as e:
        logger.error(f"Error getting monitoring status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.post("/start-monitoring")
async def start_monitoring():
    """Start the monitoring system"""
    try:
        update_mon = await get_update_monitor()
        if not update_mon.is_monitoring:
            await update_mon.start_monitoring()

        return {"success": True, "message": "Monitoring started successfully"}

    except Exception as e:
        logger.error(f"Error starting monitoring: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to start monitoring: {str(e)}"
        )


@router.post("/stop-monitoring")
async def stop_monitoring():
    """Stop the monitoring system"""
    try:
        update_mon = await get_update_monitor()
        if update_mon.is_monitoring:
            await update_mon.stop_monitoring()

        return {"success": True, "message": "Monitoring stopped successfully"}

    except Exception as e:
        logger.error(f"Error stopping monitoring: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to stop monitoring: {str(e)}"
        )


@router.post("/check-updates")
async def manual_check_updates():
    """Manually trigger update checks"""
    try:
        update_mon = await get_update_monitor()

        # Run all update checks
        await update_mon._check_framework_updates()
        await update_mon._check_security_vulnerabilities()
        await update_mon._check_best_practices()
        await update_mon._check_dependency_updates()

        return {"success": True, "message": "Update checks completed successfully"}

    except Exception as e:
        logger.error(f"Error in manual update check: {e}")
        raise HTTPException(status_code=500, detail=f"Update check failed: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check if both systems are available
        web_engine = await get_web_search_engine()
        update_mon = await get_update_monitor()

        return {
            "success": True,
            "status": "healthy",
            "web_search_engine": "available",
            "update_monitor": "available",
            "monitoring_active": update_mon.is_monitoring,
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/sources")
async def get_available_sources():
    """Get list of available search and monitoring sources"""
    try:
        web_engine = await get_web_search_engine()
        update_mon = await get_update_monitor()

        sources = {
            "web_search_sources": {
                "duckduckgo": "DuckDuckGo Instant Answer API",
                "rss_feeds": [feed.name for feed in web_engine.rss_feeds],
                "trusted_domains": list(web_engine.trusted_domains),
            },
            "update_monitoring_sources": {
                "frameworks": {
                    "python": list(
                        update_mon.config.get("frameworks", {}).get("python", {}).keys()
                    ),
                    "javascript": list(
                        update_mon.config.get("frameworks", {})
                        .get("javascript", {})
                        .keys()
                    ),
                },
                "security_sources": [
                    "NVD (National Vulnerability Database)",
                    "GitHub Security Advisories",
                ],
                "documentation_sources": [
                    "MDN Web Docs",
                    "Python Documentation",
                    "OWASP Security Guidelines",
                ],
            },
        }

        return {"success": True, "sources": sources}

    except Exception as e:
        logger.error(f"Error getting sources: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get sources: {str(e)}")
