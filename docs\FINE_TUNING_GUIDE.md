# AI Coding Agent Fine-tuning Guide

This guide provides comprehensive instructions for fine-tuning the AI Coding Agent using the provided pipeline.

## Overview

The fine-tuning system consists of three main components:
1. **Data Preprocessing** - Prepares and cleans training data
2. **Model Training** - Fine-tunes the base model with LoRA
3. **Model Evaluation** - Evaluates the fine-tuned model's performance

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements-fine-tuning.txt
```

### 2. Prepare Your Dataset

Create a JSONL file with the following format:
```jsonl
{"messages": [{"role": "user", "content": "How do I create a Python function?"}, {"role": "assistant", "content": "Here's how to create a Python function..."}]}
{"messages": [{"role": "user", "content": "Fix this code: ..."}, {"role": "assistant", "content": "Here's the fixed code..."}]}
```

### 3. Run the Complete Pipeline

```bash
python src/fine_tuning/pipeline.py run --config config/fine_tuning_config.json
```

## Detailed Usage

### Data Preprocessing

Preprocess your raw dataset:

```bash
python src/fine_tuning/pipeline.py preprocess \
    --input data/fine_tuning/raw_dataset.jsonl \
    --output data/fine_tuning/processed
```

This will create:
- `train.jsonl` - Training data
- `val.jsonl` - Validation data
- `test.jsonl` - Test data

### Model Training

Train the model with custom data:

```bash
python src/fine_tuning/pipeline.py train \
    --train-file data/fine_tuning/processed/train.jsonl \
    --val-file data/fine_tuning/processed/val.jsonl \
    --output-dir models/fine_tuned/my_model \
    --config config/fine_tuning_config.json
```

### Model Evaluation

Evaluate the trained model:

```bash
python src/fine_tuning/pipeline.py evaluate \
    --model-path models/fine_tuned/my_model \
    --test-file data/fine_tuning/processed/test.jsonl \
    --output-dir evaluation_results
```

## Configuration

### Fine-tuning Parameters

The configuration file (`config/fine_tuning_config.json`) contains:

#### Training Parameters
- **model_name**: Base model to fine-tune (default: "microsoft/DialoGPT-medium")
- **max_length**: Maximum sequence length (default: 512)
- **num_epochs**: Number of training epochs (default: 3)
- **batch_size**: Training batch size (default: 4)
- **learning_rate**: Learning rate (default: 2e-4)

#### LoRA Parameters
- **use_lora**: Enable LoRA fine-tuning (default: true)
- **lora_r**: LoRA rank (default: 16)
- **lora_alpha**: LoRA alpha (default: 32)
- **lora_dropout**: LoRA dropout rate (default: 0.1)

#### Evaluation Parameters
- **metrics**: List of evaluation metrics ["bleu", "rouge", "perplexity", "code_quality"]
- **max_samples**: Maximum samples to evaluate (default: 1000)

## Dataset Format

### Conversation Format
```jsonl
{"messages": [{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]}
```

### Code Examples Format
```jsonl
{"messages": [{"role": "user", "content": "Write a Python function to sort a list"}, {"role": "assistant", "content": "def sort_list(items):\n    return sorted(items)"}]}
```

## Monitoring Training

### Weights & Biases Integration
Enable W&B logging by setting:
```json
"use_wandb": true,
"wandb_project": "your-project-name"
```

### Local Logging
Training logs are saved to `fine_tuning.log`

## Memory Optimization

For limited GPU memory:
1. Reduce `batch_size` (try 1-2)
2. Enable gradient checkpointing
3. Use 8-bit training with bitsandbytes
4. Reduce `max_length`

## Troubleshooting

### Common Issues

**Out of Memory**
- Reduce batch size
- Use gradient accumulation
- Enable CPU offloading

**Slow Training**
- Use mixed precision training
- Increase batch size if memory allows
- Use multiple GPUs with accelerate

**Poor Results**
- Increase training epochs
- Adjust learning rate
- Check data quality
- Increase LoRA rank

## Advanced Usage

### Custom Training Script

```python
from fine_tuning.trainer import FineTuningTrainer, FineTuningConfig

config = FineTuningConfig(
    model_name="microsoft/DialoGPT-medium",
    train_file="data/train.jsonl",
    val_file="data/val.jsonl",
    output_dir="models/custom_model",
    num_epochs=5,
    learning_rate=1e-4,
    use_lora=True,
    lora_r=32
)

trainer = FineTuningTrainer(config)
metrics = trainer.train()
```

### Custom Evaluation

```python
from fine_tuning.evaluator import ModelEvaluator, EvaluationConfig

config = EvaluationConfig(
    model_path="models/my_model",
    test_file="data/test.jsonl",
    metrics=["bleu", "rouge", "custom_metric"],
    output_dir="custom_eval"
)

evaluator = ModelEvaluator(config)
results = evaluator.run_evaluation()
```

## Performance Benchmarks

### Expected Training Times
- **Small dataset (1K examples)**: 30-60 minutes
- **Medium dataset (10K examples)**: 2-4 hours
- **Large dataset (100K examples)**: 8-12 hours

### Memory Requirements
- **Minimum**: 8GB GPU RAM
- **Recommended**: 16GB+ GPU RAM
- **Large models**: 24GB+ GPU RAM

## Model Deployment

After fine-tuning, deploy your model:

```python
from model_router import ModelRouter

router = ModelRouter()
router.load_fine_tuned_model("models/fine_tuned/my_model")
```

## Support

For issues or questions:
1. Check the logs in `fine_tuning.log`
2. Review the configuration settings
3. Ensure dataset format is correct
4. Verify GPU availability and memory
