{"port_assignments": {"site1": 8080, "site2": 8081, "site3": 8082, "another-site": 8084, "test-site": 8080}, "containers": [{"site_name": "test-site", "container_name": "site-test-site", "port": 8080, "status": "ContainerStatus.STOPPED", "image_name": "ai-coding-site-test-site", "created_at": "2025-08-06 17:02:22.511438", "environment": "EnvironmentType.DEVELOPMENT", "last_started": null, "last_health_check": null, "health_status": "<MagicMock name='from_env().containers.get().attrs.get().get().get()' id='1941273936288'>", "resource_usage": {"cpu_percent": "<MagicMock name='from_env().containers.get().stats().get().get().get()' id='1941273939312'>", "memory_usage": "<MagicMock name='from_env().containers.get().stats().get().get()' id='1941273938640'>", "memory_limit": "<MagicMock name='from_env().containers.get().stats().get().get()' id='1941273938640'>"}, "logs": [], "ssl_enabled": false, "ssl_cert_path": null, "backup_enabled": true, "last_backup": null, "hot_reload_enabled": false, "monitoring_enabled": true}]}