"""
Mock hf_xet module for compatibility.

This module provides mock implementations of hf_xet functions for cases
where the actual module is not available.
"""

from typing import Any, Dict, List, Optional, Union


def upload_bytes(
    data: bytes,
    path: str,
    repo_id: str,
    *,
    token: Optional[str] = None,
    endpoint: Optional[str] = None,
    **kwargs: Any
) -> Dict[str, Any]:
    """
    Upload bytes to a repository.

    Args:
        data: The bytes to upload
        path: The path in the repository
        repo_id: The repository ID
        token: Optional authentication token
        endpoint: Optional endpoint URL
        **kwargs: Additional arguments

    Returns:
        Dict containing upload information
    """
    # This is a mock implementation
    return {
        "path": path,
        "size": len(data),
        "status": "uploaded",
        "repo_id": repo_id
    }


def upload_files(
    files: List[Dict[str, Any]],
    repo_id: str,
    *,
    token: Optional[str] = None,
    endpoint: Optional[str] = None,
    **kwargs: Any
) -> List[Dict[str, Any]]:
    """
    Upload multiple files to a repository.

    Args:
        files: List of file dictionaries
        repo_id: The repository ID
        token: Optional authentication token
        endpoint: Optional endpoint URL
        **kwargs: Additional arguments

    Returns:
        List of upload results
    """
    # This is a mock implementation
    results = []
    for file_info in files:
        results.append({
            "path": file_info.get("path", ""),
            "size": file_info.get("size", 0),
            "status": "uploaded",
            "repo_id": repo_id
        })
    return results


# Export the main functions
__all__ = ["upload_bytes", "upload_files"]
