#!/usr/bin/env python3
"""
CodePatternMatcher - Pattern identification and matching for code generation

This module provides comprehensive pattern matching capabilities including:
1. Code pattern identification
2. Pattern matching algorithms
3. Pattern customization
4. Pattern recommendation
5. Multi-language pattern support
6. Pattern learning and evolution
"""

import ast
import difflib
import json
import logging
import re
import time
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import astor

# Import CodeLanguage from ast_code_generator
try:
    from core.code_generation.ast_code_generator import CodeLanguage
except ImportError:
    # Fallback if circular import
    class CodeLanguage(Enum):
        """Supported programming languages"""

        PYTHON = "python"
        TYPESCRIPT = "typescript"
        JAVASCRIPT = "javascript"
        JSX = "jsx"
        TSX = "tsx"


logger = logging.getLogger(__name__)


class PatternType(Enum):
    """Types of code patterns"""

    STRUCTURAL = "structural"
    BEHAVIORAL = "behavioral"
    CREATIONAL = "creational"
    FUNCTIONAL = "functional"
    UTILITY = "utility"
    CUSTOM = "custom"


class PatternCategory(Enum):
    """Pattern categories"""

    API = "api"
    DATABASE = "database"
    WEB = "web"
    TESTING = "testing"
    SECURITY = "security"
    PERFORMANCE = "performance"
    GENERAL = "general"


@dataclass
class CodePattern:
    """Represents a code pattern"""

    name: str
    pattern_type: PatternType
    category: PatternCategory
    language: str
    description: str
    code_template: str
    ast_template: Optional[ast.AST] = None
    parameters: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    examples: List[str] = field(default_factory=list)
    usage_count: int = 0
    success_rate: float = 0.0
    complexity_score: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    last_used: Optional[datetime] = None

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value with default fallback"""
        return getattr(self, key, default)


@dataclass
class PatternMatch:
    """Represents a pattern match result"""

    pattern: CodePattern
    confidence_score: float
    match_reason: str
    parameter_mapping: Dict[str, Any] = field(default_factory=dict)
    customization_suggestions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PatternRecommendation:
    """Represents a pattern recommendation"""

    pattern: CodePattern
    relevance_score: float
    recommendation_reason: str
    alternatives: List[CodePattern] = field(default_factory=list)
    estimated_effort: str = "medium"
    metadata: Dict[str, Any] = field(default_factory=dict)


class CodePatternMatcher:
    """
    Provides comprehensive pattern matching capabilities for intelligent code generation.
    """

    def __init__(self, config_path: str = "config/pattern_matcher_config.json"):
        self.config = self._load_config(config_path)

        # Pattern storage
        self.patterns: Dict[str, CodePattern] = {}
        self.pattern_cache: Dict[str, List[PatternMatch]] = {}

        # Performance tracking
        self.matching_metrics = {
            "total_matches": 0,
            "successful_matches": 0,
            "failed_matches": 0,
            "average_confidence": 0.0,
            "pattern_usage": defaultdict(int),
        }

        # Load default patterns
        self._load_default_patterns()

        logger.info("CodePatternMatcher initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded pattern matcher config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "pattern_matching": {
                "enable_caching": True,
                "cache_size": 1000,
                "confidence_threshold": 0.7,
                "max_patterns_per_match": 5,
                "enable_learning": True,
            },
            "patterns": {
                "auto_load_patterns": True,
                "pattern_directory": "data/code_patterns",
                "pattern_cache_size": 500,
                "enable_pattern_evolution": True,
            },
            "matching": {
                "algorithm": "semantic",
                "enable_fuzzy_matching": True,
                "fuzzy_threshold": 0.8,
                "enable_context_matching": True,
                "context_weight": 0.3,
            },
            "customization": {
                "enable_parameter_substitution": True,
                "enable_constraint_validation": True,
                "enable_example_generation": True,
                "max_customization_depth": 3,
            },
            "recommendations": {
                "enable_recommendations": True,
                "max_recommendations": 10,
                "relevance_threshold": 0.5,
                "enable_alternative_suggestions": True,
            },
        }

    def _load_default_patterns(self):
        """Load default code patterns"""
        try:
            # Python patterns
            self._load_python_patterns()

            # JavaScript/TypeScript patterns
            self._load_js_patterns()

            logger.info(f"Loaded {len(self.patterns)} default patterns")

        except Exception as e:
            logger.error(f"Failed to load default patterns: {e}")

    def _load_python_patterns(self):
        """Load Python-specific patterns"""
        try:
            # API patterns
            api_pattern = CodePattern(
                name="fastapi_endpoint",
                pattern_type=PatternType.STRUCTURAL,
                category=PatternCategory.API,
                language="python",
                description="FastAPI endpoint pattern with request/response models",
                code_template="""
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional

app = FastAPI()

class {request_model}(BaseModel):
    {request_fields}

class {response_model}(BaseModel):
    {response_fields}

@app.{http_method}("/{endpoint_path}")
async def {function_name}(request: {request_model}) -> {response_model}:
    try:
        # Implementation here
        return {response_model}({response_data})
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
""",
                parameters=[
                    "request_model",
                    "response_model",
                    "request_fields",
                    "response_fields",
                    "http_method",
                    "endpoint_path",
                    "function_name",
                    "response_data",
                ],
                constraints=["fastapi", "pydantic"],
                examples=["user_creation", "data_retrieval", "file_upload"],
            )
            self.patterns["fastapi_endpoint"] = api_pattern

            # Database patterns
            db_pattern = CodePattern(
                name="sqlalchemy_model",
                pattern_type=PatternType.STRUCTURAL,
                category=PatternCategory.DATABASE,
                language="python",
                description="SQLAlchemy model pattern with relationships",
                code_template="""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class {model_name}(Base):
    __tablename__ = '{table_name}'

    id = Column(Integer, primary_key=True, index=True)
    {fields}
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    {relationships}
""",
                parameters=["model_name", "table_name", "fields", "relationships"],
                constraints=["sqlalchemy"],
                examples=["user_model", "product_model", "order_model"],
            )
            self.patterns["sqlalchemy_model"] = db_pattern

            # Testing patterns
            test_pattern = CodePattern(
                name="pytest_test_case",
                pattern_type=PatternType.FUNCTIONAL,
                category=PatternCategory.TESTING,
                language="python",
                description="Pytest test case pattern with fixtures",
                code_template="""
import pytest
from {module_name} import {class_name}

class Test{class_name}:
    @pytest.fixture
    def {fixture_name}(self):
        return {class_name}()

    def test_{test_method_name}(self, {fixture_name}):
        # Arrange
        {arrange_code}

        # Act
        result = {fixture_name}.{method_name}({method_args})

        # Assert
        assert result == {expected_result}
""",
                parameters=[
                    "module_name",
                    "class_name",
                    "fixture_name",
                    "test_method_name",
                    "arrange_code",
                    "method_name",
                    "method_args",
                    "expected_result",
                ],
                constraints=["pytest"],
                examples=["unit_test", "integration_test", "api_test"],
            )
            self.patterns["pytest_test_case"] = test_pattern

        except Exception as e:
            logger.error(f"Failed to load Python patterns: {e}")

    def _load_js_patterns(self):
        """Load JavaScript/TypeScript patterns"""
        try:
            # React component pattern
            react_pattern = CodePattern(
                name="react_component",
                pattern_type=PatternType.STRUCTURAL,
                category=PatternCategory.WEB,
                language="typescript",
                description="React functional component with TypeScript",
                code_template="""
import React, { useState, useEffect } from 'react';

interface {props_interface} {
    {props_fields}
}

const {component_name}: React.FC<{props_interface}> = ({ {props_destructuring} }) => {
    const [state, setState] = useState<{state_type}>({initial_state});

    useEffect(() => {
        {effect_code}
    }, [{dependencies}]);

    const handle{event_name} = () => {
        {event_handler_code}
    };

    return (
        <div className="{css_class}">
            {jsx_content}
        </div>
    );
};

export default {component_name};
""",
                parameters=[
                    "props_interface",
                    "props_fields",
                    "component_name",
                    "props_destructuring",
                    "state_type",
                    "initial_state",
                    "effect_code",
                    "dependencies",
                    "event_name",
                    "event_handler_code",
                    "css_class",
                    "jsx_content",
                ],
                constraints=["react", "typescript"],
                examples=["button_component", "form_component", "list_component"],
            )
            self.patterns["react_component"] = react_pattern

            # Express route pattern
            express_pattern = CodePattern(
                name="express_route",
                pattern_type=PatternType.STRUCTURAL,
                category=PatternCategory.API,
                language="javascript",
                description="Express.js route handler pattern",
                code_template="""
const express = require('express');
const router = express.Router();

router.{http_method}('/{route_path}', async (req, res) => {
    try {
        const { {request_params} } = req.{param_source};

        // Validation
        if (!{validation_condition}) {
            return res.status(400).json({ error: '{error_message}' });
        }

        // Business logic
        const result = await {service_call};

        res.status(200).json({ data: result });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

module.exports = router;
""",
                parameters=[
                    "http_method",
                    "route_path",
                    "request_params",
                    "param_source",
                    "validation_condition",
                    "error_message",
                    "service_call",
                ],
                constraints=["express"],
                examples=["user_route", "product_route", "auth_route"],
            )
            self.patterns["express_route"] = express_pattern

        except Exception as e:
            logger.error(f"Failed to load JS patterns: {e}")

    async def find_applicable_patterns(
        self, requirements: List[str], language: Union[str, CodeLanguage]
    ) -> List[CodePattern]:
        """
        Find applicable patterns for given requirements and language.

        :param requirements: List of requirements to match against
        :param language: Programming language (str or CodeLanguage enum)
        :return: List of applicable patterns
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        logger.info(
            f"Finding applicable patterns for {language} with {len(requirements)} requirements"
        )

        applicable_patterns = []

        for pattern in self.patterns.values():
            if pattern.language.lower() == language.lower():
                relevance_score = await self._calculate_pattern_relevance(
                    pattern, requirements
                )
                if (
                    relevance_score
                    > self.config["pattern_matching"]["confidence_threshold"]
                ):
                    pattern.usage_count += 1
                    applicable_patterns.append(pattern)

        # Sort by relevance score (descending)
        applicable_patterns.sort(
            key=lambda p: p.success_rate * p.usage_count, reverse=True
        )

        # Limit results
        max_patterns = self.config["pattern_matching"]["max_patterns_per_match"]
        applicable_patterns = applicable_patterns[:max_patterns]

        logger.info(
            f"Found {len(applicable_patterns)} applicable patterns for {language}"
        )
        return applicable_patterns

    async def _calculate_pattern_relevance(
        self, pattern: CodePattern, requirements: List[str]
    ) -> float:
        """Calculate pattern relevance score for requirements"""
        try:
            score = 0.0

            # Check pattern name relevance
            pattern_name_lower = pattern.name.lower()
            for requirement in requirements:
                requirement_lower = requirement.lower()
                if any(
                    word in pattern_name_lower for word in requirement_lower.split()
                ):
                    score += 0.3
                elif (
                    difflib.SequenceMatcher(
                        None, pattern_name_lower, requirement_lower
                    ).ratio()
                    > 0.6
                ):
                    score += 0.2

            # Check description relevance
            description_lower = pattern.description.lower()
            for requirement in requirements:
                requirement_lower = requirement.lower()
                if any(word in description_lower for word in requirement_lower.split()):
                    score += 0.2

            # Check category relevance
            category_lower = pattern.category.value.lower()
            for requirement in requirements:
                requirement_lower = requirement.lower()
                if any(word in category_lower for word in requirement_lower.split()):
                    score += 0.15

            # Check examples relevance
            for example in pattern.examples:
                example_lower = example.lower()
                for requirement in requirements:
                    requirement_lower = requirement.lower()
                    if any(word in example_lower for word in requirement_lower.split()):
                        score += 0.1

            # Consider usage history
            if pattern.usage_count > 0:
                score += min(0.1, pattern.usage_count * 0.01)

            # Consider success rate
            score += pattern.success_rate * 0.1

            return min(1.0, score)

        except Exception as e:
            logger.error(f"Failed to calculate pattern relevance: {e}")
            return 0.0

    async def customize_pattern(
        self, pattern: CodePattern, requirements: List[str], constraints: List[str]
    ) -> CodePattern:
        """Customize a pattern for specific requirements and constraints"""
        try:
            # Create a copy of the pattern for customization
            customized_pattern = copy.deepcopy(pattern)

            # Apply requirement-based customizations
            customized_pattern = await self._apply_requirement_customizations(
                customized_pattern, requirements
            )

            # Apply constraint-based customizations
            customized_pattern = await self._apply_constraint_customizations(
                customized_pattern, constraints
            )

            # Update pattern metadata
            customized_pattern.name = f"{pattern.name}_customized"
            customized_pattern.created_at = datetime.now()

            return customized_pattern

        except Exception as e:
            logger.error(f"Failed to customize pattern: {e}")
            return pattern

    async def _apply_requirement_customizations(
        self, pattern: CodePattern, requirements: List[str]
    ) -> CodePattern:
        """Apply requirement-based customizations to pattern"""
        try:
            # This is a simplified implementation
            # In a full implementation, this would use more sophisticated customization logic

            # Update description with requirements
            if requirements:
                pattern.description += f" Customized for: {', '.join(requirements)}"

            # Add requirements as constraints
            pattern.constraints.extend(requirements)

            return pattern

        except Exception as e:
            logger.error(f"Failed to apply requirement customizations: {e}")
            return pattern

    async def _apply_constraint_customizations(
        self, pattern: CodePattern, constraints: List[str]
    ) -> CodePattern:
        """Apply constraint-based customizations to pattern"""
        try:
            # This is a simplified implementation
            # In a full implementation, this would modify the code template based on constraints

            # Add constraints to pattern
            pattern.constraints.extend(constraints)

            # Modify template based on constraints
            for constraint in constraints:
                constraint_lower = constraint.lower()
                if "async" in constraint_lower:
                    # Add async/await to template
                    pattern.code_template = pattern.code_template.replace(
                        "def ", "async def "
                    )
                    pattern.code_template = pattern.code_template.replace(
                        "return ", "return await "
                    )
                elif "type" in constraint_lower:
                    # Add type hints to template
                    pattern.code_template = pattern.code_template.replace(
                        "def ", "def -> Any: "
                    )
                elif "error" in constraint_lower:
                    # Add error handling to template
                    pattern.code_template = pattern.code_template.replace(
                        "# Implementation here",
                        "# Implementation here\ntry:\n    pass\nexcept Exception as e:\n    raise e",
                    )

            return pattern

        except Exception as e:
            logger.error(f"Failed to apply constraint customizations: {e}")
            return pattern

    async def pattern_to_ast(
        self, pattern: CodePattern, language: Union[str, CodeLanguage]
    ) -> ast.AST:
        """
        Convert a pattern to AST representation.

        :param pattern: Code pattern to convert
        :param language: Programming language (str or CodeLanguage enum)
        :return: AST representation of the pattern
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        if language.lower() == "python":
            return await self._pattern_to_python_ast(pattern)
        elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
            return await self._pattern_to_js_ast(pattern, language)
        else:
            raise ValueError(f"Unsupported language: {language}")

    async def _pattern_to_python_ast(self, pattern: CodePattern) -> ast.AST:
        """Convert pattern to Python AST"""
        try:
            # For now, create a basic AST structure
            # In a full implementation, this would parse the template and create proper AST

            # Create a simple module with a comment
            comment = ast.Expr(value=ast.Constant(value=f"# Pattern: {pattern.name}"))
            module = ast.Module(body=[comment], type_ignores=[])

            return module

        except Exception as e:
            logger.error(f"Failed to convert pattern to Python AST: {e}")
            raise

    async def _pattern_to_js_ast(
        self, pattern: CodePattern, language: Union[str, CodeLanguage]
    ) -> ast.AST:
        """
        Convert pattern to JavaScript/TypeScript AST.

        :param pattern: Code pattern to convert
        :param language: Programming language (str or CodeLanguage enum)
        :return: AST representation of the pattern
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        # For now, return a simple AST structure
        # In a real implementation, this would parse the JS/TS code
        return ast.Module(body=[], type_ignores=[])

    async def get_pattern_recommendations(
        self, requirements: List[str], language: Union[str, CodeLanguage]
    ) -> List[PatternRecommendation]:
        """
        Get pattern recommendations for given requirements and language.

        :param requirements: List of requirements to match against
        :param language: Programming language (str or CodeLanguage enum)
        :return: List of pattern recommendations
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        logger.info(
            f"Getting pattern recommendations for {language} with {len(requirements)} requirements"
        )

        recommendations = []
        applicable_patterns = await self.find_applicable_patterns(
            requirements, language
        )

        for pattern in applicable_patterns:
            relevance_score = await self._calculate_pattern_relevance(
                pattern, requirements
            )
            alternatives = await self._find_alternative_patterns(
                pattern, requirements, language
            )
            estimated_effort = self._estimate_pattern_effort(pattern)

            recommendation = PatternRecommendation(
                pattern=pattern,
                relevance_score=relevance_score,
                recommendation_reason=f"Matches {len(requirements)} requirements with {relevance_score:.2f} relevance",
                alternatives=alternatives,
                estimated_effort=estimated_effort,
            )
            recommendations.append(recommendation)

        # Sort by relevance score (descending)
        recommendations.sort(key=lambda r: r.relevance_score, reverse=True)

        logger.info(
            f"Generated {len(recommendations)} pattern recommendations for {language}"
        )
        return recommendations

    async def _find_alternative_patterns(
        self,
        pattern: CodePattern,
        requirements: List[str],
        language: Union[str, CodeLanguage],
    ) -> List[CodePattern]:
        """
        Find alternative patterns for a given pattern.

        :param pattern: Original pattern
        :param requirements: List of requirements
        :param language: Programming language (str or CodeLanguage enum)
        :return: List of alternative patterns
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        alternatives = []

        for alt_pattern in self.patterns.values():
            if (
                alt_pattern.name != pattern.name
                and alt_pattern.language.lower() == language.lower()
                and alt_pattern.category == pattern.category
            ):

                relevance_score = await self._calculate_pattern_relevance(
                    alt_pattern, requirements
                )
                if relevance_score > 0.5:  # Lower threshold for alternatives
                    alternatives.append(alt_pattern)

        # Sort by relevance score (descending)
        alternatives.sort(key=lambda p: p.success_rate * p.usage_count, reverse=True)

        # Limit alternatives
        return alternatives[:3]

    def _estimate_pattern_effort(self, pattern: CodePattern) -> str:
        """Estimate implementation effort for a pattern"""
        try:
            # Simple effort estimation based on pattern complexity
            if pattern.complexity_score < 0.3:
                return "low"
            elif pattern.complexity_score < 0.7:
                return "medium"
            else:
                return "high"

        except Exception as e:
            logger.error(f"Failed to estimate pattern effort: {e}")
            return "medium"

    async def learn_from_usage(
        self, pattern_name: str, success: bool, feedback: str = None
    ):
        """Learn from pattern usage"""
        try:
            if pattern_name not in self.patterns:
                return

            pattern = self.patterns[pattern_name]

            # Update success rate
            if hasattr(pattern, "total_usage"):
                pattern.total_usage += 1
            else:
                pattern.total_usage = 1

            if success:
                if hasattr(pattern, "successful_usage"):
                    pattern.successful_usage += 1
                else:
                    pattern.successful_usage = 1

            # Calculate new success rate
            if pattern.total_usage > 0:
                pattern.success_rate = pattern.successful_usage / pattern.total_usage

            # Store feedback if provided
            if feedback:
                if not hasattr(pattern, "feedback_history"):
                    pattern.feedback_history = []
                pattern.feedback_history.append(
                    {
                        "feedback": feedback,
                        "success": success,
                        "timestamp": datetime.now(),
                    }
                )

            logger.info(f"Updated pattern {pattern_name} with usage data")

        except Exception as e:
            logger.error(f"Failed to learn from usage: {e}")

    async def add_custom_pattern(self, pattern: CodePattern) -> bool:
        """Add a custom pattern"""
        try:
            # Validate pattern
            if not self._validate_pattern(pattern):
                return False

            # Add pattern
            self.patterns[pattern.name] = pattern

            logger.info(f"Added custom pattern: {pattern.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to add custom pattern: {e}")
            return False

    def _validate_pattern(self, pattern: CodePattern) -> bool:
        """Validate a pattern"""
        try:
            # Check required fields
            if not pattern.name or not pattern.description or not pattern.code_template:
                return False

            # Check for duplicate names
            if pattern.name in self.patterns:
                return False

            # Validate pattern type and category
            if not isinstance(pattern.pattern_type, PatternType):
                return False

            if not isinstance(pattern.category, PatternCategory):
                return False

            return True

        except Exception as e:
            logger.error(f"Failed to validate pattern: {e}")
            return False

    async def export_patterns(self, file_path: str) -> bool:
        """Export patterns to file"""
        try:
            # Convert patterns to serializable format
            patterns_data = {}
            for name, pattern in self.patterns.items():
                patterns_data[name] = asdict(pattern)

            # Write to file
            with open(file_path, "w") as f:
                json.dump(patterns_data, f, indent=2, default=str)

            logger.info(f"Exported {len(self.patterns)} patterns to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to export patterns: {e}")
            return False

    async def import_patterns(self, file_path: str) -> int:
        """Import patterns from file"""
        try:
            with open(file_path, "r") as f:
                patterns_data = json.load(f)

            imported_count = 0
            for name, pattern_data in patterns_data.items():
                try:
                    # Convert back to CodePattern object
                    pattern = CodePattern(**pattern_data)
                    if self._validate_pattern(pattern):
                        self.patterns[name] = pattern
                        imported_count += 1
                except Exception as e:
                    logger.warning(f"Failed to import pattern {name}: {e}")

            logger.info(f"Imported {imported_count} patterns from {file_path}")
            return imported_count

        except Exception as e:
            logger.error(f"Failed to import patterns: {e}")
            return 0

    async def get_pattern_statistics(self) -> Dict[str, Any]:
        """Get pattern statistics"""
        try:
            stats = {
                "total_patterns": len(self.patterns),
                "patterns_by_language": defaultdict(int),
                "patterns_by_category": defaultdict(int),
                "patterns_by_type": defaultdict(int),
                "most_used_patterns": [],
                "highest_success_rate": [],
                "recent_patterns": [],
            }

            # Calculate statistics
            for pattern in self.patterns.values():
                stats["patterns_by_language"][pattern.language] += 1
                stats["patterns_by_category"][pattern.category.value] += 1
                stats["patterns_by_type"][pattern.pattern_type.value] += 1

            # Get most used patterns
            most_used = sorted(
                self.patterns.values(), key=lambda p: p.usage_count, reverse=True
            )[:5]
            stats["most_used_patterns"] = [
                {"name": p.name, "usage_count": p.usage_count} for p in most_used
            ]

            # Get highest success rate patterns
            high_success = [p for p in self.patterns.values() if p.success_rate > 0.8]
            stats["highest_success_rate"] = [
                {"name": p.name, "success_rate": p.success_rate}
                for p in high_success[:5]
            ]

            # Get recent patterns
            recent = sorted(
                self.patterns.values(), key=lambda p: p.created_at, reverse=True
            )[:5]
            stats["recent_patterns"] = [
                {"name": p.name, "created_at": p.created_at.isoformat()} for p in recent
            ]

            return stats

        except Exception as e:
            logger.error(f"Failed to get pattern statistics: {e}")
            return {}

    async def get_matching_metrics(self) -> Dict[str, Any]:
        """Get pattern matching performance metrics"""
        try:
            total_matches = self.matching_metrics["total_matches"]
            if total_matches == 0:
                return self.matching_metrics

            return {
                **self.matching_metrics,
                "success_rate": (
                    self.matching_metrics["successful_matches"] / total_matches
                )
                * 100,
                "failure_rate": (
                    self.matching_metrics["failed_matches"] / total_matches
                )
                * 100,
                "cache_size": len(self.pattern_cache),
                "total_patterns": len(self.patterns),
            }

        except Exception as e:
            logger.error(f"Failed to get matching metrics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            # Clear caches
            self.pattern_cache.clear()

            logger.info("CodePatternMatcher cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup CodePatternMatcher: {e}")
