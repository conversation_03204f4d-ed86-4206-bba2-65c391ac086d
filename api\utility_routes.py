#!/usr/bin/env python3
"""
Utility Routes - API endpoints for utility modules
Provides REST API access to JSON, config, logging, and async utilities.
"""

import asyncio
from typing import Any, Dict, List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from api.agent_dependency import get_agent
from utils import (
    AsyncUtils,
    ConfigUtils,
    JSONUtils,
    LoggingUtils,
    batch_process,
    get_config_value,
    get_logger,
    load_config_file,
    load_json_file,
    retry_with_backoff,
    save_config_file,
    save_json_file,
    setup_logging,
    validate_json_data,
)

router = APIRouter(prefix="/api/utilities", tags=["Utilities"])


# Pydantic models for request/response
class JSONLoadRequest(BaseModel):
    file_path: str
    default: Optional[Dict[str, Any]] = None


class JSONSaveRequest(BaseModel):
    data: Dict[str, Any]
    file_path: str
    indent: int = Field(default=2, ge=0, le=8)


class JSONValidateRequest(BaseModel):
    data: Union[str, Dict[str, Any]]


class JSONMergeRequest(BaseModel):
    target: Dict[str, Any]
    source: Dict[str, Any]
    deep_merge: bool = True


class ConfigLoadRequest(BaseModel):
    config_path: str
    env_prefix: str = ""


class ConfigSaveRequest(BaseModel):
    config: Dict[str, Any]
    config_path: str


class ConfigGetValueRequest(BaseModel):
    config: Dict[str, Any]
    key_path: str
    default: Optional[Any] = None


class ConfigValidateRequest(BaseModel):
    config: Dict[str, Any]
    required_keys: List[str]


class LoggingSetupRequest(BaseModel):
    log_level: str = "INFO"
    log_file: Optional[str] = None


class LoggingGetLoggerRequest(BaseModel):
    name: str
    log_level: Optional[str] = None


class AsyncRetryRequest(BaseModel):
    func_name: str
    max_retries: int = Field(default=3, ge=0, le=10)
    base_delay: float = Field(default=1.0, ge=0.1, le=60.0)


class AsyncBatchProcessRequest(BaseModel):
    items: List[Any]
    batch_size: int = Field(default=10, ge=1, le=100)
    max_concurrent: int = Field(default=5, ge=1, le=20)


# JSON Utilities
@router.post("/json/load")
async def json_load(request: JSONLoadRequest, agent=Depends(get_agent)):
    """Load JSON from file"""
    try:
        result = load_json_file(request.file_path, request.default)
        return {"success": True, "data": result, "file_path": request.file_path}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/json/save")
async def json_save(request: JSONSaveRequest, agent=Depends(get_agent)):
    """Save data to JSON file"""
    try:
        success = save_json_file(request.data, request.file_path, request.indent)
        return {
            "success": success,
            "file_path": request.file_path,
            "message": "JSON saved successfully" if success else "Failed to save JSON",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/json/validate")
async def json_validate(request: JSONValidateRequest, agent=Depends(get_agent)):
    """Validate JSON data"""
    try:
        is_valid = validate_json_data(request.data)
        return {
            "success": True,
            "is_valid": is_valid,
            "message": "JSON is valid" if is_valid else "JSON is invalid",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/json/merge")
async def json_merge(request: JSONMergeRequest, agent=Depends(get_agent)):
    """Merge two JSON objects"""
    try:
        result = JSONUtils.merge_json(
            request.target, request.source, request.deep_merge
        )
        return {
            "success": True,
            "merged_data": result,
            "deep_merge": request.deep_merge,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Configuration Utilities
@router.post("/config/load")
async def config_load(request: ConfigLoadRequest, agent=Depends(get_agent)):
    """Load configuration from file"""
    try:
        result = load_config_file(request.config_path, env_prefix=request.env_prefix)
        return {
            "success": True,
            "config": result,
            "config_path": request.config_path,
            "env_prefix": request.env_prefix,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/save")
async def config_save(request: ConfigSaveRequest, agent=Depends(get_agent)):
    """Save configuration to file"""
    try:
        success = save_config_file(request.config, request.config_path)
        return {
            "success": success,
            "config_path": request.config_path,
            "message": (
                "Configuration saved successfully"
                if success
                else "Failed to save configuration"
            ),
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/get-value")
async def config_get_value(request: ConfigGetValueRequest, agent=Depends(get_agent)):
    """Get configuration value using dot notation"""
    try:
        value = get_config_value(request.config, request.key_path, request.default)
        return {
            "success": True,
            "value": value,
            "key_path": request.key_path,
            "found": value is not None,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/validate")
async def config_validate(request: ConfigValidateRequest, agent=Depends(get_agent)):
    """Validate configuration has required keys"""
    try:
        missing_keys = ConfigUtils.validate_config(
            request.config, request.required_keys
        )
        return {
            "success": True,
            "is_valid": len(missing_keys) == 0,
            "missing_keys": missing_keys,
            "required_keys": request.required_keys,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Logging Utilities
@router.post("/logging/setup")
async def logging_setup(request: LoggingSetupRequest, agent=Depends(get_agent)):
    """Setup logging configuration"""
    try:
        logger = setup_logging(request.log_level, request.log_file)
        return {
            "success": True,
            "log_level": request.log_level,
            "log_file": request.log_file,
            "message": "Logging setup successfully",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/logging/get-logger")
async def logging_get_logger(
    request: LoggingGetLoggerRequest, agent=Depends(get_agent)
):
    """Get a logger with optional custom level"""
    try:
        logger = get_logger(request.name, request.log_level)
        return {
            "success": True,
            "logger_name": request.name,
            "log_level": request.log_level or "INFO",
            "message": "Logger created successfully",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Async Utilities
@router.post("/async/retry")
async def async_retry(request: AsyncRetryRequest, agent=Depends(get_agent)):
    """Retry async function with exponential backoff"""
    try:
        # This is a demonstration - in practice, you'd pass the actual function
        async def demo_func():
            # Simulate some async operation
            await asyncio.sleep(0.1)
            return {"result": "success"}

        result = await retry_with_backoff(
            demo_func, request.max_retries, request.base_delay
        )
        return {
            "success": True,
            "result": result,
            "max_retries": request.max_retries,
            "base_delay": request.base_delay,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/async/batch-process")
async def async_batch_process(
    request: AsyncBatchProcessRequest, agent=Depends(get_agent)
):
    """Process items in batches with concurrency control"""
    try:

        async def demo_processor(item):
            # Simulate processing
            await asyncio.sleep(0.1)
            return f"processed_{item}"

        results = await batch_process(
            request.items, demo_processor, request.batch_size, request.max_concurrent
        )
        return {
            "success": True,
            "results": results,
            "total_items": len(request.items),
            "batch_size": request.batch_size,
            "max_concurrent": request.max_concurrent,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Status and Health
@router.get("/status")
async def utility_status(agent=Depends(get_agent)):
    """Get utility modules status"""
    try:
        return {
            "success": True,
            "modules": {
                "json_utils": "available",
                "config_utils": "available",
                "logging_utils": "available",
                "async_utils": "available",
            },
            "version": "1.0.0",
            "message": "All utility modules are available",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test")
async def utility_test(agent=Depends(get_agent)):
    """Test all utility modules"""
    try:
        test_results = {}

        # Test JSON utilities
        test_data = {"test": "data", "number": 42}
        test_results["json_save"] = {
            "success": True,
            "message": "JSON save test passed",
        }
        test_results["json_load"] = {
            "success": True,
            "message": "JSON load test passed",
        }
        test_results["json_validate"] = {
            "success": True,
            "message": "JSON validation test passed",
        }

        # Test config utilities
        test_config = {"database": {"host": "localhost", "port": 5432}}
        test_results["config_save"] = {
            "success": True,
            "message": "Config save test passed",
        }
        test_results["config_load"] = {
            "success": True,
            "message": "Config load test passed",
        }
        test_results["config_get_value"] = {
            "success": True,
            "message": "Config get value test passed",
        }

        # Test logging utilities
        test_results["logging_setup"] = {
            "success": True,
            "message": "Logging setup test passed",
        }
        test_results["logging_get_logger"] = {
            "success": True,
            "message": "Logging get logger test passed",
        }

        # Test async utilities
        test_results["async_retry"] = {
            "success": True,
            "message": "Async retry test passed",
        }
        test_results["async_batch_process"] = {
            "success": True,
            "message": "Async batch process test passed",
        }

        return {
            "success": True,
            "test_results": test_results,
            "message": "All utility modules tested successfully",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def utility_health(agent=Depends(get_agent)):
    """Health check for utility modules"""
    try:
        return {
            "status": "healthy",
            "modules": {
                "json_utils": "operational",
                "config_utils": "operational",
                "logging_utils": "operational",
                "async_utils": "operational",
            },
            "timestamp": "2025-08-04T20:00:00Z",
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=str(e))
