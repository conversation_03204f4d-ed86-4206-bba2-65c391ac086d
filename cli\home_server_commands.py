"""
CLI Commands for Phase 2.2: Home Server Hosting
Provides commands for automated deployment, monitoring, backup, and network management.
"""

import json
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.home_server import DeploymentStatus, HomeServer


class HomeServerCommands:
    """CLI commands for home server hosting management"""

    def __init__(self, config_path: str = "config/home_server_config.json"):
        self.config_path = config_path
        self.hosting = None
        self._initialize_hosting()

    def _initialize_hosting(self):
        """Initialize the hosting system"""
        try:
            self.hosting = HomeServer(self.config_path)
            print("✅ Home Server Hosting system initialized")
        except Exception as e:
            print(f"❌ Error initializing hosting system: {e}")

    def start_server(self):
        """Start the home server hosting system"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            self.hosting.start()
            print("🚀 Home Server Hosting started successfully")
            print("📊 Monitoring active")
            print("🔄 Hot reload enabled")
            print("💾 Backup system ready")
            return True
        except Exception as e:
            print(f"❌ Error starting server: {e}")
            return False

    def stop_server(self):
        """Stop the home server hosting system"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            self.hosting.stop()
            print("🛑 Home Server Hosting stopped")
            return True
        except Exception as e:
            print(f"❌ Error stopping server: {e}")
            return False

    def deploy_site(
        self, site_name: str, source_path: str, validate_only: bool = False
    ):
        """Deploy a site with automated backup and validation"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print(f"🚀 Starting deployment for site: {site_name}")
            print(f"📁 Source path: {source_path}")
            print(f"🔍 Validation only: {validate_only}")

            deployment = self.hosting.deploy_site(site_name, source_path, validate_only)

            print(f"📋 Deployment ID: {deployment.deployment_id}")
            print(f"⏱️  Start time: {deployment.start_time}")

            if deployment.status == "success":
                print("✅ Deployment completed successfully")
                if deployment.end_time:
                    duration = deployment.end_time - deployment.start_time
                    print(f"⏱️  Duration: {duration.total_seconds():.2f} seconds")
            elif deployment.status == "failed":
                print("❌ Deployment failed")
                if deployment.error_message:
                    print(f"💥 Error: {deployment.error_message}")
            elif deployment.status == "rolled_back":
                print("🔄 Deployment rolled back")

            return deployment.status == "success"

        except Exception as e:
            print(f"❌ Error during deployment: {e}")
            return False

    def list_deployments(self, site_name: Optional[str] = None):
        """List recent deployments"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            deployments = self.hosting.deployment_manager.list_deployments(site_name)

            if not deployments:
                print("📋 No deployments found")
                return True

            print(f"📋 Recent deployments{' for ' + site_name if site_name else ''}:")
            print("-" * 80)

            for deployment in deployments[:10]:  # Show last 10
                status_emoji = {
                    "success": "✅",
                    "failed": "❌",
                    "pending": "⏳",
                    "deploying": "🚀",
                    "rolled_back": "🔄",
                }.get(deployment.status, "❓")

                print(f"{status_emoji} {deployment.deployment_id}")
                print(f"   Site: {deployment.site_name}")
                print(f"   Status: {deployment.status}")
                print(f"   Started: {deployment.start_time}")

                if deployment.end_time:
                    duration = deployment.end_time - deployment.start_time
                    print(f"   Duration: {duration.total_seconds():.2f}s")

                if deployment.error_message:
                    print(f"   Error: {deployment.error_message}")

                print()

            return True

        except Exception as e:
            print(f"❌ Error listing deployments: {e}")
            return False

    def rollback_deployment(self, deployment_id: str):
        """Rollback a specific deployment"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print(f"🔄 Rolling back deployment: {deployment_id}")

            success = self.hosting.deployment_manager.rollback_deployment(deployment_id)

            if success:
                print("✅ Rollback completed successfully")
            else:
                print("❌ Rollback failed")

            return success

        except Exception as e:
            print(f"❌ Error during rollback: {e}")
            return False

    def get_health_status(self):
        """Get overall system health status"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            health = self.hosting.get_health_status()

            print("🏥 System Health Status:")
            print("=" * 50)

            # System health
            system_health = health["system_health"]
            status_emoji = "🟢" if system_health["status"] == "healthy" else "🟡"
            print(f"{status_emoji} System Status: {system_health['status']}")
            print(f"   CPU: {system_health['cpu_percent']:.1f}%")
            print(f"   Memory: {system_health['memory_percent']:.1f}%")
            print(f"   Disk: {system_health['disk_percent']:.1f}%")

            # Network health
            network_health = health["network_health"]
            print(f"\n🌐 Network Health:")
            for service, status in network_health.items():
                if status and isinstance(status, dict):
                    emoji = (
                        "🟢"
                        if status.get(
                            "accessible",
                            status.get("resolved", status.get("valid", False)),
                        )
                        else "🔴"
                    )
                    print(
                        f"   {emoji} {service}: {'OK' if status.get('accessible', status.get('resolved', status.get('valid', False))) else 'FAILED'}"
                    )

            # Statistics
            print(f"\n📊 Statistics:")
            print(f"   Sites: {health['sites_count']}")
            print(f"   Backups: {health['backups_count']}")
            print(f"   Active Deployments: {health['active_deployments']}")

            return True

        except Exception as e:
            print(f"❌ Error getting health status: {e}")
            return False

    def get_site_statistics(self):
        """Get detailed site statistics"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            stats = self.hosting.get_site_statistics()

            print("📊 Site Statistics:")
            print("=" * 50)
            print(f"Total Sites: {stats['total_sites']}")
            print(f"Total Size: {stats['total_size_bytes'] / (1024*1024):.2f} MB")
            print()

            if stats["sites"]:
                print("Individual Sites:")
                print("-" * 30)

                for site in stats["sites"]:
                    size_mb = site["size_bytes"] / (1024 * 1024)
                    print(f"📁 {site['name']}")
                    print(f"   Size: {size_mb:.2f} MB")
                    print(f"   Files: {site['files_count']}")
                    print(f"   Modified: {site['last_modified']}")
                    print()
            else:
                print("No sites found")

            return True

        except Exception as e:
            print(f"❌ Error getting site statistics: {e}")
            return False

    def create_backup(self, site_name: str):
        """Create a backup of a specific site"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print(f"💾 Creating backup for site: {site_name}")

            backup_path = self.hosting.backup_manager.create_backup(site_name)

            if backup_path:
                print(f"✅ Backup created: {backup_path}")
                return True
            else:
                print("❌ Backup creation failed")
                return False

        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return False

    def list_backups(self, site_name: Optional[str] = None):
        """List available backups"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            backups = self.hosting.backup_manager.list_backups(site_name)

            if not backups:
                print("💾 No backups found")
                return True

            print(f"💾 Available backups{' for ' + site_name if site_name else ''}:")
            print("-" * 60)

            for backup in backups:
                size_mb = backup["size_bytes"] / (1024 * 1024)
                print(f"📦 {backup['backup_path']}")
                print(f"   Site: {backup['site_name']}")
                print(f"   Time: {backup['backup_time']}")
                print(f"   Size: {size_mb:.2f} MB")
                print(f"   Type: {backup['backup_type']}")
                print()

            return True

        except Exception as e:
            print(f"❌ Error listing backups: {e}")
            return False

    def restore_backup(self, backup_path: str, site_name: str):
        """Restore a site from backup"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print(f"🔄 Restoring site {site_name} from backup: {backup_path}")

            success = self.hosting.backup_manager.restore_backup(backup_path, site_name)

            if success:
                print("✅ Backup restored successfully")
            else:
                print("❌ Backup restoration failed")

            return success

        except Exception as e:
            print(f"❌ Error restoring backup: {e}")
            return False

    def cleanup_old_backups(self, keep_days: int = 30):
        """Clean up old backups"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print(f"🧹 Cleaning up backups older than {keep_days} days")

            self.hosting.backup_manager.cleanup_old_backups(keep_days)

            print("✅ Backup cleanup completed")
            return True

        except Exception as e:
            print(f"❌ Error during backup cleanup: {e}")
            return False

    def monitor_resources(self, duration_minutes: int = 5):
        """Monitor system resources for a specified duration"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print(f"📊 Monitoring system resources for {duration_minutes} minutes...")
            print("Press Ctrl+C to stop early")
            print("-" * 60)

            start_time = datetime.now()
            end_time = start_time + timedelta(minutes=duration_minutes)

            while datetime.now() < end_time:
                try:
                    metrics = self.hosting.resource_monitor.get_current_metrics()

                    # Clear line and print metrics
                    print(
                        f"\r🖥️  CPU: {metrics.cpu_percent:5.1f}% | "
                        f"💾 Memory: {metrics.memory_percent:5.1f}% | "
                        f"💿 Disk: {metrics.disk_percent:5.1f}% | "
                        f"⏱️  {datetime.now().strftime('%H:%M:%S')}",
                        end="",
                    )

                    time.sleep(5)  # Update every 5 seconds

                except KeyboardInterrupt:
                    print("\n⏹️  Monitoring stopped by user")
                    break
                except Exception as e:
                    print(f"\n❌ Error during monitoring: {e}")
                    break

            print(f"\n📊 Monitoring completed")
            return True

        except Exception as e:
            print(f"❌ Error starting monitoring: {e}")
            return False

    def test_network_connectivity(self):
        """Test network connectivity and health"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            print("🌐 Testing network connectivity...")

            connectivity = self.hosting.network_checker.check_local_connectivity()

            print("Network Health Test Results:")
            print("=" * 40)

            all_healthy = True
            for service, status in connectivity.items():
                if status and isinstance(status, dict):
                    is_healthy = status.get(
                        "accessible", status.get("resolved", status.get("valid", False))
                    )
                    emoji = "🟢" if is_healthy else "🔴"
                    status_text = "OK" if is_healthy else "FAILED"

                    print(f"{emoji} {service}: {status_text}")

                    if not is_healthy and status.get("error"):
                        print(f"   Error: {status['error']}")

                    if not is_healthy:
                        all_healthy = False

            if all_healthy:
                print("\n✅ All network services are healthy")
            else:
                print("\n⚠️  Some network services have issues")

            return all_healthy

        except Exception as e:
            print(f"❌ Error testing network connectivity: {e}")
            return False

    def create_sample_deployment(self):
        """Create a sample deployment for testing"""
        if not self.hosting:
            print("❌ Hosting system not initialized")
            return False

        try:
            # Create a sample site
            sample_site_name = "sample-site"
            sample_site_path = Path("sites") / sample_site_name

            # Create sample site directory
            sample_site_path.mkdir(parents=True, exist_ok=True)

            # Create sample index.html
            index_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Site - Phase 2.2</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 5px; }
        .content { margin: 20px 0; }
        .footer { text-align: center; color: #666; margin-top: 40px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Sample Site</h1>
            <p>Phase 2.2: Home Server Hosting Demo</p>
        </div>
        <div class="content">
            <h2>Features Demonstrated:</h2>
            <ul>
                <li>✅ Automated Deployment</li>
                <li>✅ Hot Reload</li>
                <li>✅ Resource Monitoring</li>
                <li>✅ Backup System</li>
                <li>✅ Network Health Checks</li>
            </ul>
            <p>This site was automatically deployed using the Phase 2.2 Home Server Hosting system.</p>
        </div>
        <div class="footer">
            <p>Generated by AI Coding Agent - Phase 2.2</p>
        </div>
    </div>
</body>
</html>"""

            with open(sample_site_path / "index.html", "w") as f:
                f.write(index_html)

            # Create sample config
            config = {
                "name": sample_site_name,
                "title": "Sample Site - Phase 2.2",
                "description": "A demonstration of Phase 2.2 features",
                "author": "AI Coding Agent",
                "created": datetime.now().isoformat(),
            }

            with open(sample_site_path / "config.json", "w") as f:
                json.dump(config, f, indent=2)

            print(f"📁 Created sample site: {sample_site_path}")

            # Deploy the sample site
            print("🚀 Deploying sample site...")
            success = self.deploy_site(sample_site_name, str(sample_site_path))

            if success:
                print("✅ Sample deployment completed successfully")
                print(f"🌐 Site available at: http://localhost:5000/{sample_site_name}")
            else:
                print("❌ Sample deployment failed")

            return success

        except Exception as e:
            print(f"❌ Error creating sample deployment: {e}")
            return False


def main():
    """Main CLI entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="Home Server Hosting CLI")
    parser.add_argument(
        "command",
        choices=[
            "start",
            "stop",
            "deploy",
            "list-deployments",
            "rollback",
            "health",
            "stats",
            "backup",
            "list-backups",
            "restore",
            "cleanup",
            "monitor",
            "network-test",
            "sample",
        ],
    )
    parser.add_argument("--site", help="Site name")
    parser.add_argument("--source", help="Source path for deployment")
    parser.add_argument(
        "--validate-only", action="store_true", help="Validate deployment only"
    )
    parser.add_argument("--deployment-id", help="Deployment ID for rollback")
    parser.add_argument("--backup-path", help="Backup path for restore")
    parser.add_argument(
        "--keep-days", type=int, default=30, help="Days to keep backups"
    )
    parser.add_argument(
        "--duration", type=int, default=5, help="Monitoring duration in minutes"
    )
    parser.add_argument(
        "--config", default="config/home_server_config.json", help="Config file path"
    )

    args = parser.parse_args()

    commands = HomeServerCommands(args.config)

    if args.command == "start":
        commands.start_server()
    elif args.command == "stop":
        commands.stop_server()
    elif args.command == "deploy":
        if not args.site or not args.source:
            print("❌ Site name and source path required for deployment")
            return
        commands.deploy_site(args.site, args.source, args.validate_only)
    elif args.command == "list-deployments":
        commands.list_deployments(args.site)
    elif args.command == "rollback":
        if not args.deployment_id:
            print("❌ Deployment ID required for rollback")
            return
        commands.rollback_deployment(args.deployment_id)
    elif args.command == "health":
        commands.get_health_status()
    elif args.command == "stats":
        commands.get_site_statistics()
    elif args.command == "backup":
        if not args.site:
            print("❌ Site name required for backup")
            return
        commands.create_backup(args.site)
    elif args.command == "list-backups":
        commands.list_backups(args.site)
    elif args.command == "restore":
        if not args.backup_path or not args.site:
            print("❌ Backup path and site name required for restore")
            return
        commands.restore_backup(args.backup_path, args.site)
    elif args.command == "cleanup":
        commands.cleanup_old_backups(args.keep_days)
    elif args.command == "monitor":
        commands.monitor_resources(args.duration)
    elif args.command == "network-test":
        commands.test_network_connectivity()
    elif args.command == "sample":
        commands.create_sample_deployment()


if __name__ == "__main__":
    main()
