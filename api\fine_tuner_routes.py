import os
from typing import Any, Dict, Optional

import httpx
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from api.agent_dependency import (
    get_agent,
)  # keep parity with existing pattern if present

router = APIRouter(prefix="/api/fine-tuner", tags=["Fine Tuner"])

FINE_TUNER_HOST = os.getenv("FINE_TUNER_SERVICE_HOST", "http://localhost")
FINE_TUNER_PORT = os.getenv("FINE_TUNER_PORT", "8002")
BASE_URL = f"{FINE_TUNER_HOST}:{FINE_TUNER_PORT}"


class StartRequest(BaseModel):
    dataset_path: Optional[str] = None
    model_name: Optional[str] = None
    epochs: Optional[int] = 1
    learning_rate: Optional[float] = 1e-4
    output_dir: Optional[str] = "outputs"


class EvaluateRequest(BaseModel):
    model_artifact: str
    eval_dataset_path: str


@router.get("/health")
async def health(agent: Any = Depends(get_agent)) -> Dict[str, Any]:  # noqa: ARG001
    async with httpx.AsyncClient(timeout=10) as client:
        r = await client.get(f"{BASE_URL}/health")
        r.raise_for_status()
        return r.json()


@router.get("/status")
async def status(agent: Any = Depends(get_agent)) -> Dict[str, Any]:  # noqa: ARG001
    async with httpx.AsyncClient(timeout=10) as client:
        r = await client.get(f"{BASE_URL}/status")
        r.raise_for_status()
        return r.json()


@router.post("/start")
async def start(
    req: StartRequest, agent: Any = Depends(get_agent)
) -> Dict[str, Any]:  # noqa: ARG001
    async with httpx.AsyncClient(timeout=30) as client:
        r = await client.post(f"{BASE_URL}/start", json=req.model_dump())
        if r.status_code == 409:
            raise HTTPException(
                status_code=409, detail=r.json().get("detail", "Conflict")
            )
        r.raise_for_status()
        return r.json()


@router.post("/stop")
async def stop(agent: Any = Depends(get_agent)) -> Dict[str, Any]:  # noqa: ARG001
    async with httpx.AsyncClient(timeout=15) as client:
        r = await client.post(f"{BASE_URL}/stop")
        if r.status_code == 409:
            raise HTTPException(
                status_code=409, detail=r.json().get("detail", "Conflict")
            )
        r.raise_for_status()
        return r.json()


@router.get("/progress")
async def progress(agent: Any = Depends(get_agent)) -> Dict[str, Any]:  # noqa: ARG001
    async with httpx.AsyncClient(timeout=10) as client:
        r = await client.get(f"{BASE_URL}/progress")
        r.raise_for_status()
        return r.json()


@router.post("/evaluate")
async def evaluate(
    req: EvaluateRequest, agent: Any = Depends(get_agent)
) -> Dict[str, Any]:  # noqa: ARG001
    async with httpx.AsyncClient(timeout=60) as client:
        r = await client.post(f"{BASE_URL}/evaluate", json=req.model_dump())
        if r.status_code >= 400:
            # bubble underlying error
            try:
                detail = r.json()
            except Exception:
                detail = {"detail": r.text}
            raise HTTPException(status_code=r.status_code, detail=detail)
        return r.json()
