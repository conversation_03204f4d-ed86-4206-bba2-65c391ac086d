{"analysis_window": 3600, "correlation_threshold": 0.7, "confidence_threshold": 0.8, "max_causal_depth": 3, "min_impact_score": 0.1, "pattern_analysis": {"min_data_points": 10, "trend_analysis_window": 20, "volatility_threshold": 0.1}, "root_cause_analysis": {"correlation_weight": 0.6, "relationship_weight": 0.4, "min_correlation_strength": 0.7}, "impact_assessment": {"base_impact": 0.5, "model_factor": 0.2, "severity_multipliers": {"cpu_usage": 1.2, "memory_usage": 1.1, "network_latency": 0.9, "disk_io": 0.8, "unknown": 0.5}}, "recommendation_generation": {"cpu_optimization": ["Optimize CPU-intensive operations", "Implement caching strategies", "Consider horizontal scaling"], "memory_optimization": ["Implement memory pooling", "Optimize data structures", "Add garbage collection tuning"], "network_optimization": ["Optimize network calls", "Implement connection pooling", "Consider CDN for static assets"], "general_optimization": ["Investigate root cause further", "Monitor system performance", "Review recent changes"]}, "confidence_calculation": {"correlation_weight": 0.7, "relationship_weight": 0.3, "min_confidence": 0.5}}