"""
Fine-Tuning API Routes
======================

This module provides REST API endpoints for the fine-tuning pipeline,
including training, evaluation, deployment, and automated triggers.
"""

import logging
import time
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from pydantic import BaseModel, Field

from api.agent_dependency import get_agent

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/fine-tuning", tags=["Fine-Tuning"])


# Pydantic models for request/response
class TrainingRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to train")
    dataset_type: str = Field(default="all", description="Type of dataset to use")
    training_config: Optional[Dict[str, Any]] = Field(
        default=None, description="Training configuration"
    )


class EvaluationRequest(BaseModel):
    model_path: str = Field(..., description="Path to the model to evaluate")
    model_name: str = Field(default="unknown", description="Name of the model")
    test_file: str = Field(
        default="data/fine_tuning/test_dataset.jsonl", description="Test file path"
    )


class DeploymentRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to deploy")
    auto_switch: bool = Field(
        default=True, description="Automatically switch to this model"
    )


class TriggerRequest(BaseModel):
    trigger_type: str = Field(
        ..., description="Type of trigger (performance, scheduled, data_drift, usage)"
    )
    model_name: str = Field(..., description="Name of the model")
    conditions: Dict[str, Any] = Field(..., description="Trigger conditions")


class PerformanceReportRequest(BaseModel):
    model_name: Optional[str] = Field(default=None, description="Specific model name")
    format: str = Field(default="json", description="Report format")


class PipelineRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model")
    dataset_type: str = Field(default="all", description="Type of dataset")
    auto_deploy: bool = Field(
        default=True, description="Automatically deploy after training"
    )


@router.post("/train")
async def train_model(
    request: TrainingRequest, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Train a model using the fine-tuning pipeline"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.train_model(
            model_name=request.model_name,
            dataset_type=request.dataset_type,
            training_config=request.training_config,
        )

        if result.get("success"):
            return {
                "success": True,
                "message": f"Model training started for {request.model_name}",
                "result": result,
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Training failed")
            )

    except Exception as e:
        logger.error(f"Error in train_model endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/evaluate")
async def evaluate_model(
    request: EvaluationRequest, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Evaluate a trained model"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.evaluate_model(
            model_path=request.model_path,
            model_name=request.model_name,
            test_file=request.test_file,
        )

        if result.get("success"):
            return {
                "success": True,
                "message": f"Model evaluation completed for {request.model_name}",
                "result": result,
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Evaluation failed")
            )

    except Exception as e:
        logger.error(f"Error in evaluate_model endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/deploy")
async def deploy_model(
    request: DeploymentRequest, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Deploy a trained model"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.deploy_model(
            model_name=request.model_name, auto_switch=request.auto_switch
        )

        if result.get("success"):
            return {
                "success": True,
                "message": f"Model deployed successfully: {request.model_name}",
                "result": result,
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Deployment failed")
            )

    except Exception as e:
        logger.error(f"Error in deploy_model endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def list_models(agent=Depends(get_agent)) -> Dict[str, Any]:
    """List all available models"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.list_models()

        if result.get("success"):
            return {
                "success": True,
                "models": result["models"],
                "active_model": result["active_model"],
                "total_models": result["total_models"],
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to list models")
            )

    except Exception as e:
        logger.error(f"Error in list_models endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models/{model_name}/status")
async def get_model_status(model_name: str, agent=Depends(get_agent)) -> Dict[str, Any]:
    """Get status of a specific model"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.get_model_status(model_name=model_name)

        if result.get("success"):
            return {
                "success": True,
                "model_info": result["model_info"],
                "performance_history": result["performance_history"],
            }
        else:
            raise HTTPException(
                status_code=404, detail=result.get("error", "Model not found")
            )

    except Exception as e:
        logger.error(f"Error in get_model_status endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/triggers")
async def create_trigger(
    request: TriggerRequest, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Create an automated fine-tuning trigger"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)

        # Extract conditions based on trigger type
        kwargs = {
            "trigger_type": request.trigger_type,
            "model_name": request.model_name,
        }

        if request.trigger_type == "performance":
            kwargs.update(
                {
                    "performance_threshold": request.conditions.get(
                        "performance_threshold", 0.7
                    ),
                    "check_interval_hours": request.conditions.get(
                        "check_interval_hours", 24
                    ),
                }
            )
        elif request.trigger_type == "scheduled":
            kwargs.update(
                {
                    "schedule_type": request.conditions.get("schedule_type", "weekly"),
                    "schedule_value": request.conditions.get(
                        "schedule_value", "monday"
                    ),
                }
            )
        elif request.trigger_type == "data_drift":
            kwargs.update(
                {
                    "drift_threshold": request.conditions.get("drift_threshold", 0.1),
                    "check_interval_hours": request.conditions.get(
                        "check_interval_hours", 12
                    ),
                }
            )
        elif request.trigger_type == "usage":
            kwargs.update(
                {
                    "usage_threshold": request.conditions.get("usage_threshold", 1000),
                    "time_window_hours": request.conditions.get(
                        "time_window_hours", 24
                    ),
                }
            )
        else:
            raise HTTPException(
                status_code=400, detail=f"Unknown trigger type: {request.trigger_type}"
            )

        result = await commands.create_trigger(**kwargs)

        if result.get("success"):
            return {
                "success": True,
                "message": f"Trigger created successfully for {request.model_name}",
                "trigger": result["trigger"],
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to create trigger")
            )

    except Exception as e:
        logger.error(f"Error in create_trigger endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/triggers")
async def list_triggers(agent=Depends(get_agent)) -> Dict[str, Any]:
    """List all automated triggers"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.list_triggers()

        if result.get("success"):
            return {
                "success": True,
                "triggers": result["triggers"],
                "scheduler_active": result["scheduler_active"],
                "total_triggers": result["total_triggers"],
                "active_triggers": result["active_triggers"],
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to list triggers")
            )

    except Exception as e:
        logger.error(f"Error in list_triggers endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/triggers/scheduler/start")
async def start_trigger_scheduler(agent=Depends(get_agent)) -> Dict[str, Any]:
    """Start the automated trigger scheduler"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.start_trigger_scheduler()

        if result.get("success"):
            return {
                "success": True,
                "message": "Trigger scheduler started successfully",
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to start scheduler")
            )

    except Exception as e:
        logger.error(f"Error in start_trigger_scheduler endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/triggers/scheduler/stop")
async def stop_trigger_scheduler(agent=Depends(get_agent)) -> Dict[str, Any]:
    """Stop the automated trigger scheduler"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.stop_trigger_scheduler()

        if result.get("success"):
            return {
                "success": True,
                "message": "Trigger scheduler stopped successfully",
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to stop scheduler")
            )

    except Exception as e:
        logger.error(f"Error in stop_trigger_scheduler endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance/summary")
async def get_performance_summary(
    model_name: Optional[str] = None, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Get performance monitoring summary"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.get_performance_summary(model_name=model_name)

        if result.get("success"):
            return {"success": True, "summary": result["summary"]}
        else:
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Failed to get performance summary"),
            )

    except Exception as e:
        logger.error(f"Error in get_performance_summary endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/performance/report")
async def generate_performance_report(
    request: PerformanceReportRequest, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Generate performance report"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.generate_performance_report(
            model_name=request.model_name, format=request.format
        )

        if result.get("success"):
            return {"success": True, "report": result["report"]}
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to generate report")
            )

    except Exception as e:
        logger.error(f"Error in generate_performance_report endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pipeline/complete")
async def run_complete_pipeline(
    request: PipelineRequest,
    background_tasks: BackgroundTasks,
    agent=Depends(get_agent),
) -> Dict[str, Any]:
    """Run the complete fine-tuning pipeline"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)

        # Run in background for long-running operations
        def run_pipeline():
            import asyncio

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    commands.run_complete_pipeline(
                        model_name=request.model_name,
                        dataset_type=request.dataset_type,
                        auto_deploy=request.auto_deploy,
                    )
                )
                return result
            finally:
                loop.close()

        background_tasks.add_task(run_pipeline)

        return {
            "success": True,
            "message": f"Complete pipeline started for {request.model_name}",
            "pipeline_id": f"pipeline_{request.model_name}_{int(time.time())}",
        }

    except Exception as e:
        logger.error(f"Error in run_complete_pipeline endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup")
async def cleanup_old_models(
    days_old: int = 30, backup: bool = True, agent=Depends(get_agent)
) -> Dict[str, Any]:
    """Clean up old models and data"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)
        result = await commands.cleanup_old_models(days_old=days_old, backup=backup)

        if result.get("success"):
            return {
                "success": True,
                "message": f"Cleanup completed: {result['total_removed']} models removed",
                "removed_models": result["removed_models"],
                "total_removed": result["total_removed"],
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Cleanup failed")
            )

    except Exception as e:
        logger.error(f"Error in cleanup_old_models endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def fine_tuning_health(agent=Depends(get_agent)) -> Dict[str, Any]:
    """Check fine-tuning system health"""
    try:
        from cli.fine_tuning_commands import FineTuningCommands

        commands = FineTuningCommands(agent)

        # Check if components are available
        components_status = {
            "trainer": commands.trainer is not None,
            "evaluator": commands.evaluator is not None,
            "integration": commands.integration is not None,
            "performance_monitor": commands.performance_monitor is not None,
            "automated_triggers": commands.automated_triggers is not None,
        }

        # Get basic status
        models_result = await commands.list_models()
        triggers_result = await commands.list_triggers()

        health_status = {
            "status": "healthy" if all(components_status.values()) else "degraded",
            "components": components_status,
            "models_available": models_result.get("success", False),
            "triggers_available": triggers_result.get("success", False),
            "total_models": (
                models_result.get("total_models", 0)
                if models_result.get("success")
                else 0
            ),
            "active_triggers": (
                triggers_result.get("active_triggers", 0)
                if triggers_result.get("success")
                else 0
            ),
        }

        return {"success": True, "health": health_status}

    except Exception as e:
        logger.error(f"Error in fine_tuning_health endpoint: {e}")
        return {"success": False, "health": {"status": "unhealthy", "error": str(e)}}


# Import time module for pipeline ID generation
