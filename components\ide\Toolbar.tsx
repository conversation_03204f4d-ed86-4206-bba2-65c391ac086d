import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import {
  Play,
  Square,
  Download,
  Upload,
  Settings,
  Sun,
  Moon,
  FolderOpen,
  Save,
  Share2,
  GitBranch,
  Zap,
  UploadCloud,
  Loader2,
  CheckCircle,
  XCircle,
  Shield,
  Activity,
  Database,
  TestTube,
  HelpCircle,
  Brain,
  FileText,
  FolderPlus,
  ExternalLink,
  Copy,
  Check,
  Bug,
  User,
  LogOut
} from 'lucide-react';
import { deploymentService } from '@/services/DeploymentService';
import { sslService } from '@/services/SSLService';
import { maintenanceService } from '@/services/MaintenanceService';
import { performanceService } from '@/services/PerformanceService';
import { testingService } from '@/services/TestingService';
import { helpService } from '@/services/HelpService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { UploadProgress } from '@/components/ui/UploadProgress';
import { ToolbarProjectInfo } from './ToolbarProjectInfo';
import { ToolbarMainActions } from './ToolbarMainActions';
import { ToolbarUtilities } from './ToolbarUtilities';

interface ToolbarProps {
  currentProject: string;
  onProjectChange: (project: string) => void;
  onModelHealthToggle?: (show: boolean) => void;
  documentationPanelToggle?: () => void;
  showDocumentationPanel?: boolean;
  onToggleErrorDetection?: () => void;
  showErrorDetection?: boolean;
  onToggleBonusFeatures?: () => void;
  showBonusFeatures?: boolean;
  onMobileMenuToggle?: () => void;
  className?: string;
}

export const Toolbar = memo<ToolbarProps>(({
  currentProject,
  onProjectChange,
  onModelHealthToggle,
  documentationPanelToggle,
  showDocumentationPanel,
  onToggleErrorDetection,
  showErrorDetection = false,
  onToggleBonusFeatures,
  showBonusFeatures = false,
  onMobileMenuToggle,
  className = ''
}) => {
  const { user, isAuthenticated, logout } = useAuth();
  const [isRunning, setIsRunning] = React.useState(false);
  const [deploying, setDeploying] = useState(false);
  const [deployStatus, setDeployStatus] = useState<'idle' | 'deploying' | 'success' | 'error'>('idle');
  const [sslGenerating, setSslGenerating] = useState(false);
  const [maintenanceRunning, setMaintenanceRunning] = useState(false);
  const [performanceAnalyzing, setPerformanceAnalyzing] = useState(false);
  const [testingRunning, setTestingRunning] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showModelHealth, setShowModelHealth] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [sharing, setSharing] = useState(false);
  const [copied, setCopied] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleRun = useCallback(() => {
    setIsRunning(true);
    // Simulate running the project
    setTimeout(() => {
      setIsRunning(false);
    }, 3000);
  }, [setIsRunning]);

  const handleStop = useCallback(() => {
    setIsRunning(false);
  }, [setIsRunning]);

  const handleSave = useCallback(() => {
    // Handle save functionality
    console.log('Saving project...');
  }, []);

  const handleDeploy = useCallback(async () => {
    setDeploying(true);
    setDeployStatus('deploying');
    try {
      const status = await deploymentService.deploy();
      setDeployStatus(status.status);
      if (status.status === 'success') {
        toast.success('Deployment successful!');
      } else if (status.status === 'error') {
        toast.error('Deployment failed: ' + status.message);
      }
    } catch (error: any) {
      setDeployStatus('error');
      toast.error('Deployment failed: ' + error.message);
    } finally {
      setDeploying(false);
      setTimeout(() => setDeployStatus('idle'), 4000);
    }
  }, [setDeploying, setDeployStatus, toast]);

  const handleSSLGenerate = useCallback(async () => {
    setSslGenerating(true);
    try {
      const status = await sslService.generateCertificate('localhost');
      if (status.certificateValid) {
        toast.success('SSL certificate generated successfully!');
      } else {
        toast.error('SSL certificate generation failed');
      }
    } catch (error: any) {
      toast.error('SSL generation failed: ' + error.message);
    } finally {
      setSslGenerating(false);
    }
  }, [setSslGenerating, toast]);

  const handleHealthCheck = useCallback(async () => {
    setMaintenanceRunning(true);
    try {
      const task = await maintenanceService.runHealthCheck();
      if (task.status === 'completed') {
        toast.success('Health check completed successfully!');
      } else {
        toast.error('Health check failed');
      }
    } catch (error: any) {
      toast.error('Health check failed: ' + error.message);
    } finally {
      setMaintenanceRunning(false);
    }
  }, [setMaintenanceRunning, toast]);

  const handleBackup = useCallback(async () => {
    setMaintenanceRunning(true);
    try {
      const backup = await maintenanceService.createBackup();
      if (backup.status === 'completed') {
        toast.success('Backup created successfully!');
      } else {
        toast.error('Backup creation failed');
      }
    } catch (error: any) {
      toast.error('Backup failed: ' + error.message);
    } finally {
      setMaintenanceRunning(false);
    }
  }, [setMaintenanceRunning, toast]);

  const handlePerformanceAnalysis = useCallback(async () => {
    setPerformanceAnalyzing(true);
    try {
      const metrics = performanceService.getMetrics();
      const bundleAnalysis = await performanceService.analyzeBundle();

      toast.success(`Performance Analysis Complete! Load time: ${metrics.pageLoadTime}ms`);
      console.log('Performance Metrics:', metrics);
      console.log('Bundle Analysis:', bundleAnalysis);
    } catch (error: any) {
      toast.error('Performance analysis failed: ' + error.message);
    } finally {
      setPerformanceAnalyzing(false);
    }
  }, [setPerformanceAnalyzing, toast]);

  const handleRunTests = useCallback(async () => {
    setTestingRunning(true);
    try {
      const results = await testingService.runAllTests();
      const report = testingService.generateReport();

      const totalTests = report.summary.totalTests;
      const passedTests = report.summary.passedTests;
      const coverage = report.summary.coverage;

      toast.success(`Tests Complete! ${passedTests}/${totalTests} passed (${coverage}% coverage)`);
      console.log('Test Results:', results);
      console.log('Test Report:', report);
    } catch (error: any) {
      toast.error('Test execution failed: ' + error.message);
    } finally {
      setTestingRunning(false);
    }
  }, [setTestingRunning, toast]);

  const handleRunUnitTests = useCallback(async () => {
    setTestingRunning(true);
    try {
      const results = await testingService.runUnitTests();
      toast.success(`Unit Tests: ${results.passedTests}/${results.totalTests} passed`);
    } catch (error: any) {
      toast.error('Unit tests failed: ' + error.message);
    } finally {
      setTestingRunning(false);
    }
  }, [setTestingRunning, toast]);

  const handleShowHelp = useCallback(() => {
    setShowHelp(!showHelp);
    const quickTips = helpService.getQuickTips();
    const randomTip = quickTips[Math.floor(Math.random() * quickTips.length)];
    toast.success(`💡 ${randomTip}`);
  }, [showHelp, setShowHelp, toast]);

  const handleShowModelHealth = useCallback(() => {
    const newState = !showModelHealth;
    setShowModelHealth(newState);
    onModelHealthToggle?.(newState);
  }, [showModelHealth, setShowModelHealth, onModelHealthToggle]);

  const handleUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, [fileInputRef]);

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const newUploadFiles = Array.from(files).map((file, index) => ({
      id: `upload-${Date.now()}-${index}`,
      name: file.name,
      size: file.size,
      progress: 0,
      status: 'uploading' as const,
      file
    }));

    setUploadFiles(prev => [...prev, ...newUploadFiles]);

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const uploadFile = newUploadFiles[i];

        // Simulate upload progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setUploadFiles(prev =>
            prev.map(f =>
              f.id === uploadFile.id
                ? { ...f, progress }
                : f
            )
          );
        }

        const content = await file.text();
        // Here you would typically upload to your file management system
        console.log(`Uploading ${file.name}...`);

        // Mark as completed
        setUploadFiles(prev =>
          prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'completed' as const, progress: 100 }
              : f
          )
        );

        toast.success(`Uploaded ${file.name} successfully!`);
      }
    } catch (error: any) {
      // Mark as error
      setUploadFiles(prev =>
        prev.map(f =>
          newUploadFiles.some(nf => nf.id === f.id)
            ? { ...f, status: 'error' as const, error: error.message }
            : f
        )
      );
      toast.error('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [setUploadFiles, setUploading, toast]);

  const handleRemoveUpload = useCallback((fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
  }, [setUploadFiles]);

  const handleRetryUpload = useCallback((fileId: string) => {
    const uploadFile = uploadFiles.find(f => f.id === fileId);
    if (uploadFile) {
      // Reset progress and retry
      setUploadFiles(prev =>
        prev.map(f =>
          f.id === fileId
            ? { ...f, status: 'uploading' as const, progress: 0, error: undefined }
            : f
        )
      );
      // Here you would retry the actual upload
    }
  }, [uploadFiles, setUploadFiles]);

  const handleDownload = useCallback(async () => {
    setDownloading(true);
    try {
      // Create a zip file of the current project
      const projectData = {
        name: currentProject,
        files: [], // Get files from file manager
        timestamp: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentProject}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Project downloaded successfully!');
    } catch (error: any) {
      toast.error('Download failed: ' + error.message);
    } finally {
      setDownloading(false);
    }
  }, [currentProject, setDownloading, toast]);

  const handleShare = useCallback(async () => {
    setSharing(true);
    try {
      const shareData = {
        title: currentProject,
        text: 'Check out my project!',
        url: window.location.href
      };

      if (navigator.share) {
        await navigator.share(shareData);
        toast.success('Shared successfully!');
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        setCopied(true);
        toast.success('Link copied to clipboard!');
        setTimeout(() => setCopied(false), 2000);
      }
    } catch (error: any) {
      toast.error('Share failed: ' + error.message);
    } finally {
      setSharing(false);
    }
  }, [currentProject, setSharing, setCopied, toast]);

  const handleSettings = useCallback(() => {
    setShowSettings(!showSettings);
    toast.success('Settings panel toggled!');
  }, [showSettings, setShowSettings, toast]);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && !e.shiftKey && !e.altKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            handleSave();
            break;
          case 'r':
            e.preventDefault();
            handleRun();
            break;
          case 'd':
            e.preventDefault();
            handleDeploy();
            break;
          case 't':
            e.preventDefault();
            handleRunTests();
            break;
          case 'u':
            e.preventDefault();
            handleUpload();
            break;
          case '/':
            e.preventDefault();
            documentationPanelToggle?.();
            break;
          case 'e':
            e.preventDefault();
            onToggleErrorDetection?.();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, handleRun, handleDeploy, handleRunTests, handleUpload, documentationPanelToggle, onToggleErrorDetection]);

  return (
    <div className={`flex items-center justify-between px-4 py-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Left Section - Project Info */}
      <ToolbarProjectInfo
        currentProject={currentProject}
        onProjectChange={onProjectChange}
        onMobileMenuToggle={onMobileMenuToggle}
      />

      {/* Center Section - Main Actions */}
      <ToolbarMainActions
        isRunning={isRunning}
        deploying={deploying}
        deployStatus={deployStatus}
        sslGenerating={sslGenerating}
        maintenanceRunning={maintenanceRunning}
        performanceAnalyzing={performanceAnalyzing}
        testingRunning={testingRunning}
        showModelHealth={showModelHealth}
        showHelp={showHelp}
        handleSave={handleSave}
        handleRun={handleRun}
        handleStop={handleStop}
        handleDeploy={handleDeploy}
        handleSSLGenerate={handleSSLGenerate}
        handleHealthCheck={handleHealthCheck}
        handleBackup={handleBackup}
        handlePerformanceAnalysis={handlePerformanceAnalysis}
        handleRunTests={handleRunTests}
        handleShowModelHealth={handleShowModelHealth}
        handleShowHelp={handleShowHelp}
      />

      {/* Right Section - Utilities */}
      <ToolbarUtilities
        documentationPanelToggle={documentationPanelToggle}
        showDocumentationPanel={showDocumentationPanel}
        onUpload={handleUpload}
        uploading={uploading}
        onDownload={handleDownload}
        downloading={downloading}
        onShare={handleShare}
        sharing={sharing}
        copied={copied}
        onSettings={handleSettings}
        onToggleErrorDetection={onToggleErrorDetection || (() => {})}
        showErrorDetection={showErrorDetection}
        onToggleBonusFeatures={onToggleBonusFeatures || (() => {})}
        showBonusFeatures={showBonusFeatures}
        fileInputRef={fileInputRef}
        handleFileUpload={handleFileUpload}
        uploadFiles={uploadFiles}
        handleRemoveUpload={handleRemoveUpload}
        handleRetryUpload={handleRetryUpload}
      />

      {/* Hidden file input for uploads */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".html,.css,.js,.jsx,.ts,.tsx,.json,.md,.txt"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Upload Progress */}
      <UploadProgress
        files={uploadFiles}
        onRemove={handleRemoveUpload}
        onRetry={handleRetryUpload}
      />
    </div>
  );
});
