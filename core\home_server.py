#!/usr/bin/env python3
"""
Unified Home Server System - Phase 3.2
Combines hosting, pipeline, deployment, monitoring, and rollback capabilities.
"""

import asyncio
import hashlib
import json
import logging
import os
import shutil
import signal
import socket
import ssl
import subprocess
import sys
import tempfile
import time
import zipfile
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from threading import Thread, Timer
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import aiohttp
import psutil
import requests
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer
from website_generator import WebsiteGenerator

from core.managers import BackupManager, DeploymentManager
from db.database_manager import DatabaseManager

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/home_server.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class ServerResource:
    """Server resource metrics"""

    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_sent: int
    network_recv: int
    timestamp: datetime


@dataclass
class DeploymentStatus:
    """Deployment status information"""

    site_name: str
    status: str  # 'pending', 'deploying', 'success', 'failed', 'rolled_back'
    start_time: datetime
    deployment_id: str
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    backup_path: Optional[str] = None


class PipelineState:
    """Manages pipeline state and progress tracking"""

    def __init__(self, state_file: str = "data/pipeline_state.json"):
        self.state_file = state_file
        self.state = self._load_state()

    def _load_state(self) -> Dict[str, Any]:
        """Load pipeline state from file"""
        try:
            Path(self.state_file).parent.mkdir(parents=True, exist_ok=True)
            if Path(self.state_file).exists():
                with open(self.state_file, "r") as f:
                    return json.load(f)
        except Exception as e:
            logging.warning(f"Failed to load pipeline state: {e}")

        return {
            "version": "1.0.0",
            "deployments": [],
            "rollbacks": [],
            "current_deployment": None,
            "pipeline_status": "idle",
            "last_updated": datetime.now().isoformat(),
        }

    def _save_state(self):
        """Save pipeline state to file"""
        try:
            self.state["last_updated"] = datetime.now().isoformat()
            with open(self.state_file, "w") as f:
                json.dump(self.state, f, indent=2)
        except Exception as e:
            logging.error(f"Failed to save pipeline state: {e}")

    def start_deployment(
        self, deployment_id: str, site_name: str, config: Dict[str, Any]
    ):
        """Start a new deployment"""
        deployment_info = {
            "deployment_id": deployment_id,
            "site_name": site_name,
            "config": config,
            "status": "in_progress",
            "start_time": datetime.now().isoformat(),
            "steps": [],
            "rollback_available": False,
        }

        self.state["deployments"].append(deployment_info)
        self.state["current_deployment"] = deployment_id
        self.state["pipeline_status"] = "deploying"
        self._save_state()

        return deployment_info

    def update_deployment_step(
        self,
        deployment_id: str,
        step_name: str,
        status: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        """Update deployment step status"""
        deployment = self._find_deployment(deployment_id)
        if deployment:
            step_info = {
                "name": step_name,
                "status": status,
                "timestamp": datetime.now().isoformat(),
                "details": details or {},
            }
            deployment["steps"].append(step_info)
            self._save_state()

    def complete_deployment(
        self,
        deployment_id: str,
        status: str = "completed",
        rollback_available: bool = True,
    ):
        """Complete a deployment"""
        deployment = self._find_deployment(deployment_id)
        if deployment:
            deployment["status"] = status
            deployment["end_time"] = datetime.now().isoformat()
            deployment["rollback_available"] = rollback_available
            self.state["current_deployment"] = None
            self.state["pipeline_status"] = "idle"
            self._save_state()

    def create_rollback_point(self, deployment_id: str, backup_path: str):
        """Create a rollback point for a deployment"""
        rollback_info = {
            "deployment_id": deployment_id,
            "backup_path": backup_path,
            "created_at": datetime.now().isoformat(),
            "status": "available",
        }
        self.state["rollbacks"].append(rollback_info)
        self._save_state()

    def execute_rollback(self, deployment_id: str) -> bool:
        """Execute a rollback for a deployment"""
        rollback = self._find_rollback(deployment_id)
        if rollback:
            rollback["status"] = "executed"
            rollback["executed_at"] = datetime.now().isoformat()
            self._save_state()
            return True
        return False

    def _find_deployment(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Find a deployment by ID"""
        for deployment in self.state["deployments"]:
            if deployment["deployment_id"] == deployment_id:
                return deployment
        return None

    def _find_rollback(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Find a rollback by deployment ID"""
        for rollback in self.state["rollbacks"]:
            if rollback["deployment_id"] == deployment_id:
                return rollback
        return None

    def get_deployment_status(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Get deployment status"""
        return self._find_deployment(deployment_id)

    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get overall pipeline status"""
        return {
            "status": self.state["pipeline_status"],
            "current_deployment": self.state["current_deployment"],
            "total_deployments": len(self.state["deployments"]),
            "total_rollbacks": len(self.state["rollbacks"]),
            "last_updated": self.state["last_updated"],
        }


class ResourceMonitor:
    """Monitor server resources and network health"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.monitoring_enabled = config.get("monitoring", {}).get("enabled", True)
        self.metrics_history: List[ServerResource] = []
        self.max_history_size = 1000
        self.monitoring_thread: Optional[Thread] = None
        self._stop_monitoring = False

    def start_monitoring(self):
        """Start resource monitoring in background thread"""
        if not self.monitoring_enabled:
            logger.info("Resource monitoring disabled in config")
            return

        self.monitoring_thread = Thread(target=self._monitor_loop, daemon=True)
        self.monitoring_thread.start()
        logger.info("Resource monitoring started")

    def stop_monitoring(self):
        """Stop resource monitoring"""
        self._stop_monitoring = True
        if self.monitoring_thread:
            self.monitoring_thread.join()
        logger.info("Resource monitoring stopped")

    def _monitor_loop(self):
        """Main monitoring loop"""
        while not self._stop_monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)

                # Keep only recent history
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history = self.metrics_history[
                        -self.max_history_size :
                    ]

                # Check for resource alerts
                self._check_alerts(metrics)

                time.sleep(30)  # Monitor every 30 seconds
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait longer on error

    def _collect_metrics(self) -> ServerResource:
        """Collect current system metrics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")
            network = psutil.net_io_counters()

            return ServerResource(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent,
                network_sent=network.bytes_sent,
                network_recv=network.bytes_recv,
                timestamp=datetime.now(),
            )
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return ServerResource(0, 0, 0, 0, 0, datetime.now())

    def _check_alerts(self, metrics: ServerResource):
        """Check for resource alerts"""
        alerts = self.config.get("monitoring", {}).get("alerts", {})

        if metrics.cpu_percent > alerts.get("cpu_threshold", 80):
            logger.warning(f"High CPU usage: {metrics.cpu_percent}%")

        if metrics.memory_percent > alerts.get("memory_threshold", 80):
            logger.warning(f"High memory usage: {metrics.memory_percent}%")

        if metrics.disk_percent > alerts.get("disk_threshold", 90):
            logger.warning(f"High disk usage: {metrics.disk_percent}%")

    def get_current_metrics(self) -> ServerResource:
        """Get current system metrics"""
        return self._collect_metrics()

    def get_metrics_history(self, hours: int = 24) -> List[ServerResource]:
        """Get metrics history for specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [m for m in self.metrics_history if m.timestamp > cutoff_time]

    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status"""
        current = self.get_current_metrics()

        return {
            "status": (
                "healthy"
                if current.cpu_percent < 80 and current.memory_percent < 80
                else "warning"
            ),
            "cpu_percent": current.cpu_percent,
            "memory_percent": current.memory_percent,
            "disk_percent": current.disk_percent,
            "network_active": current.network_sent > 0 or current.network_recv > 0,
            "last_updated": current.timestamp.isoformat(),
            "timestamp": current.timestamp.isoformat(),
        }


class NetworkHealthChecker:
    """Check network connectivity and health"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ports_to_check = config.get("network", {}).get("ports", [80, 443, 8080])
        self.dns_servers = config.get("network", {}).get(
            "dns_servers", ["8.8.8.8", "1.1.1.1"]
        )

    def check_local_connectivity(self) -> Dict[str, Any]:
        """Check local network connectivity"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "ports": {},
            "dns": {},
            "ssl": {},
            "overall_status": "unknown",
            "status": "unknown",
        }

        # Check ports
        for port in self.ports_to_check:
            results["ports"][str(port)] = self._check_port(port)

        # Check DNS
        results["dns"] = self._check_dns()

        # Check SSL certificate
        results["ssl"] = self._check_ssl_certificate()

        # Determine overall status
        port_statuses = [
            port_check.get("status", "unknown")
            for port_check in results["ports"].values()
        ]
        dns_status = results["dns"].get("status", "unknown")
        ssl_status = results["ssl"].get("status", "unknown")

        all_checks = port_statuses + [dns_status, ssl_status]
        if all(check == "ok" for check in all_checks):
            results["overall_status"] = "healthy"
            results["status"] = "healthy"
        elif any(check == "error" for check in all_checks):
            results["overall_status"] = "error"
            results["status"] = "error"
        else:
            results["overall_status"] = "warning"
            results["status"] = "warning"

        return results

    def _check_port(self, port: int) -> Dict[str, Any]:
        """Check if a port is accessible"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(("localhost", port))
            sock.close()

            if result == 0:
                return {"status": "ok", "accessible": True, "port": port, "error": None}
            else:
                return {
                    "status": "error",
                    "accessible": False,
                    "error": f"Port {port} not accessible",
                    "port": port,
                }
        except Exception as e:
            return {
                "status": "error",
                "accessible": False,
                "error": str(e),
                "port": port,
            }

    def _check_dns(self) -> Dict[str, Any]:
        """Check DNS resolution"""
        try:
            import socket

            socket.gethostbyname("localhost")
            return {
                "status": "ok",
                "resolved": True,
                "domain": "localhost",
                "error": None,
            }
        except Exception as e:
            return {
                "status": "error",
                "resolved": False,
                "error": str(e),
                "domain": "localhost",
            }

    def _check_ssl_certificate(self) -> Dict[str, Any]:
        """Check SSL certificate validity"""
        try:
            context = ssl.create_default_context()
            with socket.create_connection(("google.com", 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname="google.com") as ssock:
                    cert = ssock.getpeercert()
                    return {
                        "status": "ok",
                        "valid": True,
                        "expires": cert.get("notAfter", "unknown"),
                    }
        except Exception as e:
            return {"status": "error", "valid": False, "error": str(e)}


class IdempotentScriptManager:
    """Manage idempotent deployment scripts"""

    def __init__(self, scripts_dir: str = "scripts/pipeline"):
        self.scripts_dir = Path(scripts_dir)
        self.scripts_dir.mkdir(parents=True, exist_ok=True)

    def create_script(
        self, script_name: str, content: str, checksum: Optional[str] = None
    ) -> str:
        """Create an idempotent script"""
        script_path = self.scripts_dir / f"{script_name}.py"

        # Add idempotent wrapper
        script_content = f'''#!/usr/bin/env python3
"""
Idempotent Script: {script_name}
Generated: {datetime.now().isoformat()}
Checksum: {checksum or "unknown"}
"""

import os
import sys
import json
import hashlib
from pathlib import Path

def calculate_checksum(content):
    return hashlib.md5(content.encode()).hexdigest()

def is_already_executed(script_path, expected_checksum):
    state_file = script_path.with_suffix('.state')
    if state_file.exists():
        with open(state_file, 'r') as f:
            state = json.load(f)
            return state.get('checksum') == expected_checksum
    return False

def mark_executed(script_path, checksum):
    state_file = script_path.with_suffix('.state')
    state = {{
        'executed_at': '{datetime.now().isoformat()}',
        'checksum': checksum,
        'status': 'completed'
    }}
    with open(state_file, 'w') as f:
        json.dump(state, f, indent=2)

def main():
    script_content = """{content}"""
    current_checksum = calculate_checksum(script_content)

    if is_already_executed(Path(__file__), current_checksum):
        print(f"Script {{os.path.basename(__file__)}} already executed with same content")
        return 0

    try:
        # Execute the actual script content
        exec(script_content)
        mark_executed(Path(__file__), current_checksum)
        print(f"Script {{os.path.basename(__file__)}} executed successfully")
        return 0
    except Exception as e:
        print(f"Script execution failed: {{e}}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''

        with open(script_path, "w") as f:
            f.write(script_content)

        os.chmod(script_path, 0o755)
        return str(script_path)

    async def execute_script(
        self, script_name: str, args: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute an idempotent script"""
        script_path = self.scripts_dir / f"{script_name}.py"

        if not script_path.exists():
            return {
                "success": False,
                "error": f"Script {script_name} not found",
                "output": "",
                "return_code": -1,
            }

        try:
            # Prepare environment variables for script
            env = os.environ.copy()
            if args:
                env.update({f"SCRIPT_ARG_{k.upper()}": str(v) for k, v in args.items()})

            # Execute script
            process = await asyncio.create_subprocess_exec(
                str(script_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env,
            )

            stdout, stderr = await process.communicate()

            return {
                "success": process.returncode == 0,
                "output": stdout.decode(),
                "error": stderr.decode(),
                "return_code": process.returncode,
                "script_path": str(script_path),
            }

        except Exception as e:
            return {"success": False, "error": str(e), "output": "", "return_code": -1}

    def list_scripts(self) -> List[str]:
        """List available scripts"""
        return [f.stem for f in self.scripts_dir.glob("*.py")]


class RollbackManager:
    """Manage deployment rollbacks"""

    def __init__(self, backup_dir: str = "backups"):
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

    async def create_backup(self, deployment_id: str, site_path: str) -> str:
        """Create a backup for rollback"""
        try:
            site_path = Path(site_path)
            if not site_path.exists():
                raise FileNotFoundError(f"Site path does not exist: {site_path}")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{deployment_id}_{timestamp}.zip"
            backup_path = self.backup_dir / backup_name

            # Create zip backup
            with zipfile.ZipFile(backup_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for file_path in site_path.rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(site_path)
                        zipf.write(file_path, arcname)

            logger.info(f"Backup created: {backup_path}")
            return str(backup_path)

        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            raise

    async def restore_backup(self, backup_path: str, target_path: str) -> bool:
        """Restore from backup"""
        try:
            backup_path = Path(backup_path)
            target_path = Path(target_path)

            if not backup_path.exists():
                raise FileNotFoundError(f"Backup not found: {backup_path}")

            # Create target directory
            target_path.mkdir(parents=True, exist_ok=True)

            # Extract backup
            with zipfile.ZipFile(backup_path, "r") as zipf:
                zipf.extractall(target_path)

            logger.info(f"Backup restored to: {target_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to restore backup: {e}")
            return False

    async def cleanup_old_backups(self, max_backups: int = 10):
        """Clean up old backups"""
        try:
            backup_files = sorted(
                self.backup_dir.glob("*.zip"),
                key=lambda x: x.stat().st_mtime,
                reverse=True,
            )

            if len(backup_files) > max_backups:
                for old_backup in backup_files[max_backups:]:
                    old_backup.unlink()
                    logger.info(f"Removed old backup: {old_backup}")

        except Exception as e:
            logger.error(f"Failed to cleanup old backups: {e}")

    def list_backups(self) -> List[Dict[str, Any]]:
        """List available backups"""
        backups = []
        for backup_file in self.backup_dir.glob("*.zip"):
            stat = backup_file.stat()
            backups.append(
                {
                    "path": str(backup_file),
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                }
            )
        return sorted(backups, key=lambda x: x["modified"], reverse=True)


class DeploymentValidator:
    """Validate deployments before and after execution"""

    def __init__(self):
        self.validation_rules = self._load_validation_rules()

    def _load_validation_rules(self) -> Dict[str, Any]:
        """Load validation rules"""
        return {
            "pre_deployment": {
                "required_files": ["index.html", "package.json"],
                "max_size_mb": 100,
                "required_permissions": ["read", "write"],
                "security_checks": ["no_sensitive_data", "valid_ssl"],
            },
            "post_deployment": {
                "health_checks": ["http_200", "no_errors"],
                "performance_checks": ["load_time_under_3s", "bundle_size_under_2mb"],
                "accessibility_checks": ["valid_html", "responsive_design"],
            },
        }

    async def validate_pre_deployment(
        self, config: Dict[str, Any], site_path: str
    ) -> Dict[str, Any]:
        """Validate before deployment"""
        results = {"valid": True, "errors": [], "warnings": [], "checks": {}}

        site_path = Path(site_path)

        # Check required files
        for required_file in self.validation_rules["pre_deployment"]["required_files"]:
            file_path = site_path / required_file
            if file_path.exists():
                results["checks"][f"file_{required_file}"] = "ok"
            else:
                results["checks"][f"file_{required_file}"] = "missing"
                results["warnings"].append(f"Required file missing: {required_file}")

        # Check size
        total_size = sum(f.stat().st_size for f in site_path.rglob("*") if f.is_file())
        max_size = self.validation_rules["pre_deployment"]["max_size_mb"] * 1024 * 1024

        if total_size > max_size:
            results["checks"]["size"] = "too_large"
            results["errors"].append(
                f"Site size ({total_size / 1024 / 1024:.1f}MB) exceeds limit"
            )
        else:
            results["checks"]["size"] = "ok"

        # Check permissions
        if os.access(site_path, os.R_OK | os.W_OK):
            results["checks"]["permissions"] = "ok"
        else:
            results["checks"]["permissions"] = "insufficient"
            results["errors"].append("Insufficient permissions")

        results["valid"] = len(results["errors"]) == 0
        return results

    async def validate_post_deployment(self, site_path: str) -> Dict[str, Any]:
        """Validate after deployment"""
        results = {"valid": True, "errors": [], "warnings": [], "checks": {}}

        # Basic health check
        try:
            # This would typically check the deployed site
            # For now, we'll do basic file checks
            site_path = Path(site_path)

            if site_path.exists():
                results["checks"]["site_exists"] = "ok"
            else:
                results["checks"]["site_exists"] = "missing"
                results["errors"].append("Deployed site not found")

            # Check for common error files
            error_files = ["error.log", "500.html", "error.html"]
            for error_file in error_files:
                if (site_path / error_file).exists():
                    results["checks"][f"no_{error_file}"] = "error_found"
                    results["warnings"].append(f"Error file found: {error_file}")
                else:
                    results["checks"][f"no_{error_file}"] = "ok"

        except Exception as e:
            results["checks"]["health_check"] = "failed"
            results["errors"].append(f"Health check failed: {e}")

        results["valid"] = len(results["errors"]) == 0
        return results


class HotReloadManager:
    """Manage hot reload functionality"""

    def __init__(self, config: Dict[str, Any], deployment_manager: DeploymentManager):
        self.config = config
        self.deployment_manager = deployment_manager
        self.observer = Observer()
        self.watched_sites: Dict[str, Path] = {}
        self.reload_callbacks: List[Callable] = []
        self.hot_reload_enabled = config.get("hot_reload", {}).get("enabled", True)

        # Add missing attribute that tests expect
        self.watched_paths = []

    def start_watching(self):
        """Start watching for file changes"""
        if not self.hot_reload_enabled:
            logger.info("Hot reload disabled in config")
            return

        try:
            self.observer.start()
            logger.info("Hot reload watching started")
        except Exception as e:
            logger.error(f"Failed to start hot reload: {e}")

    def stop_watching(self):
        """Stop watching for file changes"""
        try:
            self.observer.stop()
            self.observer.join()
            logger.info("Hot reload watching stopped")
        except Exception as e:
            logger.error(f"Error stopping hot reload: {e}")

    def _watch_site(self, site_name: str, site_path: Path):
        """Watch a specific site for changes"""
        handler = SiteChangeHandler(site_name, self._handle_site_change)
        self.observer.schedule(handler, str(site_path), recursive=True)
        self.watched_sites[site_name] = site_path
        self.watched_paths.append(str(site_path))
        logger.info(f"Started watching site: {site_name}")

    def _handle_site_change(self, site_name: str, file_path: str):
        """Handle file change in watched site"""
        logger.info(f"File change detected in {site_name}: {file_path}")

        # Trigger reload callbacks
        for callback in self.reload_callbacks:
            try:
                callback(site_name, file_path)
            except Exception as e:
                logger.error(f"Error in reload callback: {e}")

    def add_reload_callback(self, callback: Callable):
        """Add a callback for reload events"""
        self.reload_callbacks.append(callback)

    def watch_new_site(self, site_name: str, site_path: Path):
        """Start watching a new site"""
        if site_name not in self.watched_sites:
            self._watch_site(site_name, site_path)


class SiteChangeHandler(FileSystemEventHandler):
    """Handle file system changes for hot reload"""

    def __init__(self, site_name: str, change_callback: Callable):
        self.site_name = site_name
        self.change_callback = change_callback
        self.last_modified = {}

    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return

        # Debounce rapid changes
        current_time = time.time()
        if event.src_path in self.last_modified:
            if current_time - self.last_modified[event.src_path] < 1.0:
                return

        self.last_modified[event.src_path] = current_time

        # Call the change callback
        self.change_callback(self.site_name, event.src_path)


class HomeServer:
    """Unified Home Server System"""

    def __init__(self, config_path: str = "config/home_server_config.json"):
        self.config_path = config_path
        self.config = self._load_config()

        # Initialize components
        self.pipeline_state = PipelineState()
        self.resource_monitor = ResourceMonitor(self.config)
        self.network_checker = NetworkHealthChecker(self.config)
        self.script_manager = IdempotentScriptManager()
        self.rollback_manager = RollbackManager()
        self.deployment_validator = DeploymentValidator()

        # Initialize managers
        try:
            self.backup_manager = BackupManager(self.config)
            self.deployment_manager = DeploymentManager()

            # Initialize TemplateManager for WebsiteGenerator
            from templates.template_manager import TemplateManager

            template_manager = TemplateManager()
            self.website_generator = WebsiteGenerator(template_manager)

            # Initialize DatabaseManager with a default model
            from db.models import Project

            self.database_manager = DatabaseManager(Project)
        except Exception as e:
            logger.warning(f"Some managers not available: {e}")
            self.backup_manager = None
            self.deployment_manager = None
            self.website_generator = None
            self.database_manager = None

        # Initialize hot reload
        self.hot_reload_manager = HotReloadManager(self.config, self.deployment_manager)

        # Add missing attributes that tests expect
        self.sites_dir = self.config.get("deployment", {}).get("sites_dir", "sites")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, "r") as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load config: {e}")

        return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "deployment": {
                "sites_dir": "sites",
                "backup_enabled": True,
                "max_backups": 10,
                "auto_rollback": True,
            },
            "monitoring": {
                "enabled": True,
                "interval": 30,
                "alerts": {
                    "cpu_threshold": 80,
                    "memory_threshold": 80,
                    "disk_threshold": 90,
                },
            },
            "network": {
                "ports": [80, 443, 8080],
                "dns_servers": ["8.8.8.8", "1.1.1.1"],
            },
            "hot_reload": {"enabled": True, "debounce_ms": 1000},
        }

    def start(self):
        """Start the home server system"""
        try:
            # Start monitoring
            self.resource_monitor.start_monitoring()

            # Start hot reload
            self.hot_reload_manager.start_watching()

            logger.info("Home server system started successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to start home server: {e}")
            return False

    def stop(self):
        """Stop the home server system"""
        try:
            # Stop monitoring
            self.resource_monitor.stop_monitoring()

            # Stop hot reload
            self.hot_reload_manager.stop_watching()

            logger.info("Home server system stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping home server: {e}")
            return False

    async def deploy_site(
        self, site_name: str, source_path: str, config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Deploy a site with full pipeline"""
        deployment_id = f"{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            # Start deployment
            self.pipeline_state.start_deployment(deployment_id, site_name, config or {})

            # Pre-deployment validation
            self.pipeline_state.update_deployment_step(
                deployment_id, "pre_validation", "running"
            )
            validation_result = await self.deployment_validator.validate_pre_deployment(
                config or {}, source_path
            )

            if not validation_result["valid"]:
                self.pipeline_state.complete_deployment(deployment_id, "failed")
                return {
                    "success": False,
                    "deployment_id": deployment_id,
                    "error": "Pre-deployment validation failed",
                    "validation_errors": validation_result["errors"],
                }

            self.pipeline_state.update_deployment_step(
                deployment_id, "pre_validation", "completed"
            )

            # Create backup
            if self.config["deployment"]["backup_enabled"]:
                self.pipeline_state.update_deployment_step(
                    deployment_id, "backup", "running"
                )
                backup_path = await self.rollback_manager.create_backup(
                    deployment_id, source_path
                )
                self.pipeline_state.create_rollback_point(deployment_id, backup_path)
                self.pipeline_state.update_deployment_step(
                    deployment_id, "backup", "completed"
                )

            # Deploy site
            self.pipeline_state.update_deployment_step(
                deployment_id, "deployment", "running"
            )

            # Use website generator if available
            if self.website_generator:
                result = await self.website_generator.generate_site(
                    site_name, source_path, config or {}
                )
            else:
                # Fallback deployment
                target_path = Path(self.config["deployment"]["sites_dir"]) / site_name
                target_path.mkdir(parents=True, exist_ok=True)
                shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                result = {"success": True, "output_path": str(target_path)}

            if not result.get("success", False):
                self.pipeline_state.complete_deployment(deployment_id, "failed")
                return {
                    "success": False,
                    "deployment_id": deployment_id,
                    "error": "Deployment failed",
                    "details": result,
                }

            self.pipeline_state.update_deployment_step(
                deployment_id, "deployment", "completed"
            )

            # Post-deployment validation
            self.pipeline_state.update_deployment_step(
                deployment_id, "post_validation", "running"
            )
            post_validation = await self.deployment_validator.validate_post_deployment(
                result["output_path"]
            )

            if not post_validation["valid"]:
                if self.config["deployment"]["auto_rollback"]:
                    await self.rollback_deployment(deployment_id)
                    return {
                        "success": False,
                        "deployment_id": deployment_id,
                        "error": "Post-deployment validation failed, rolled back",
                        "validation_errors": post_validation["errors"],
                    }
                else:
                    self.pipeline_state.complete_deployment(deployment_id, "failed")
                    return {
                        "success": False,
                        "deployment_id": deployment_id,
                        "error": "Post-deployment validation failed",
                        "validation_errors": post_validation["errors"],
                    }

            self.pipeline_state.update_deployment_step(
                deployment_id, "post_validation", "completed"
            )

            # Start watching for hot reload
            self.hot_reload_manager.watch_new_site(
                site_name, Path(result["output_path"])
            )

            # Complete deployment
            self.pipeline_state.complete_deployment(deployment_id, "completed", True)

            return {
                "success": True,
                "deployment_id": deployment_id,
                "site_path": result["output_path"],
                "backup_path": (
                    backup_path if self.config["deployment"]["backup_enabled"] else None
                ),
            }

        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            self.pipeline_state.complete_deployment(deployment_id, "failed")
            return {"success": False, "deployment_id": deployment_id, "error": str(e)}

    async def rollback_deployment(self, deployment_id: str) -> Dict[str, Any]:
        """Rollback a deployment"""
        try:
            deployment = self.pipeline_state.get_deployment_status(deployment_id)
            if not deployment:
                return {"success": False, "error": "Deployment not found"}

            rollback = self.pipeline_state._find_rollback(deployment_id)
            if not rollback:
                return {"success": False, "error": "No rollback point available"}

            # Execute rollback
            success = await self.rollback_manager.restore_backup(
                rollback["backup_path"], f"sites/{deployment['site_name']}"
            )

            if success:
                self.pipeline_state.execute_rollback(deployment_id)
                return {"success": True, "deployment_id": deployment_id}
            else:
                return {"success": False, "error": "Rollback restoration failed"}

        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return {"success": False, "error": str(e)}

    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status"""
        return {
            "system_health": self.resource_monitor.get_health_status(),
            "network": self.network_checker.check_local_connectivity(),
            "network_health": self.network_checker.check_local_connectivity(),
            "pipeline": self.pipeline_state.get_pipeline_status(),
            "timestamp": datetime.now().isoformat(),
        }

    def get_site_statistics(self) -> Dict[str, Any]:
        """Get site deployment statistics"""
        sites_dir = Path(self.config.get("deployment", {}).get("sites_dir", "sites"))
        sites = []

        if sites_dir.exists():
            for site_dir in sites_dir.iterdir():
                if site_dir.is_dir():
                    sites.append(
                        {
                            "name": site_dir.name,
                            "size": self._get_directory_size(site_dir),
                            "created": datetime.fromtimestamp(
                                site_dir.stat().st_ctime
                            ).isoformat(),
                            "modified": datetime.fromtimestamp(
                                site_dir.stat().st_mtime
                            ).isoformat(),
                        }
                    )

        return {
            "total_sites": len(sites),
            "sites": sites,
            "total_size": sum(site["size"] for site in sites),
        }

    def _get_directory_size(self, path: Path) -> int:
        """Calculate directory size in bytes"""
        total_size = 0
        try:
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.error(f"Error calculating directory size: {e}")
        return total_size

    async def create_idempotent_script(self, script_name: str, content: str) -> str:
        """Create an idempotent script"""
        return self.script_manager.create_script(script_name, content)

    async def execute_idempotent_script(
        self, script_name: str, args: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute an idempotent script"""
        return await self.script_manager.execute_script(script_name, args)

    def list_idempotent_scripts(self) -> List[str]:
        """List available idempotent scripts"""
        return self.script_manager.list_scripts()

    async def cleanup_old_backups(self, max_backups: int = 10):
        """Clean up old backups"""
        await self.rollback_manager.cleanup_old_backups(max_backups)

    def get_deployment_status(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Get deployment status"""
        return self.pipeline_state.get_deployment_status(deployment_id)

    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get pipeline status"""
        return self.pipeline_state.get_pipeline_status()

    def list_deployments(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List recent deployments"""
        return self.pipeline_state.state["deployments"][-limit:]

    def list_rollbacks(self) -> List[Dict[str, Any]]:
        """List available rollbacks"""
        return self.pipeline_state.state["rollbacks"]


async def main():
    """Main entry point for the home server system"""
    home_server = HomeServer()

    try:
        # Start the system
        if home_server.start():
            logger.info("Home server system running...")

            # Keep running
            while True:
                await asyncio.sleep(1)
        else:
            logger.error("Failed to start home server system")
            return 1

    except KeyboardInterrupt:
        logger.info("Shutting down home server system...")
        home_server.stop()
        return 0
    except Exception as e:
        logger.error(f"Home server system error: {e}")
        return 1


if __name__ == "__main__":
    asyncio.run(main())
