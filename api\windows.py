"""
Window functions for signal processing.

This module provides various window functions.
"""

import numpy as np
from typing import Any, Optional, Tuple, Union


def get_window(
    window: Union[str, Tuple[str, float], np.ndarray],
    Nx: int,
    fftbins: bool = True,
) -> np.ndarray:
    """
    Return a window of a given length and type.

    Args:
        window: Window type or window array
        Nx: Number of points in the output window
        fftbins: If True, create a "periodic" window for use with FFT

    Returns:
        Window array
    """
    # This is a simplified implementation
    if isinstance(window, str):
        if window == "hann":
            return np.hanning(Nx)
        elif window == "hamming":
            return np.hamming(Nx)
        elif window == "blackman":
            return np.blackman(Nx)
        elif window == "bartlett":
            return np.bartlett(Nx)
        elif window == "boxcar":
            return np.ones(Nx)
        elif window == "kaiser":
            # Default beta for Kaiser window
            beta = 8.0
            return np.kaiser(Nx, beta)
        else:
            # Default to boxcar for unknown windows
            return np.ones(Nx)
    elif isinstance(window, np.ndarray):
        # If window is already an array, return it
        return window
    else:
        # Default to boxcar
        return np.ones(Nx)


def hann(
    M: int,
    sym: bool = True,
) -> np.ndarray:
    """
    Return a Hann window.

    Args:
        M: Number of points in the output window
        sym: When True, generates a symmetric window

    Returns:
        Hann window
    """
    return np.hanning(M)


def hamming(
    M: int,
    sym: bool = True,
) -> np.ndarray:
    """
    Return a Hamming window.

    Args:
        M: Number of points in the output window
        sym: When True, generates a symmetric window

    Returns:
        Hamming window
    """
    return np.hamming(M)


def blackman(
    M: int,
    sym: bool = True,
) -> np.ndarray:
    """
    Return a Blackman window.

    Args:
        M: Number of points in the output window
        sym: When True, generates a symmetric window

    Returns:
        Blackman window
    """
    return np.blackman(M)


def bartlett(
    M: int,
    sym: bool = True,
) -> np.ndarray:
    """
    Return a Bartlett window.

    Args:
        M: Number of points in the output window
        sym: When True, generates a symmetric window

    Returns:
        Bartlett window
    """
    return np.bartlett(M)


def kaiser(
    M: int,
    beta: float,
    sym: bool = True,
) -> np.ndarray:
    """
    Return a Kaiser window.

    Args:
        M: Number of points in the output window
        beta: Shape parameter
        sym: When True, generates a symmetric window

    Returns:
        Kaiser window
    """
    return np.kaiser(M, beta)


# Export the main functions
__all__ = ["get_window", "hann", "hamming", "blackman", "bartlett", "kaiser"]
