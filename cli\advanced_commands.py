"""
Advanced CLI commands for Phase 2.1 features.
Provides commands for theme customization, asset optimization, and content metadata.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from core.website_generator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ThemeManager, WebsiteGenerator
from db.database_manager import DatabaseManager

# Configure logging
logger = logging.getLogger(__name__)


class AdvancedGeneratorCommands:
    """Advanced generator commands for Phase 2.1 features"""

    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        # Use the TemplateManager from templates.template_manager module
        from templates.template_manager import Template<PERSON>anager as WGTemplateManager

        self.generator = WebsiteGenerator(WGTemplateManager(), db_manager)
        self.template_manager = self.generator.template_manager
        self.theme_manager = self.generator.theme_manager
        self.asset_pipeline = self.generator.asset_pipeline

    def create_advanced_site(self, site_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a website with advanced features including theme customization
        """
        try:
            print(f"🚀 Creating advanced website: {site_config['name']}")

            # Validate configuration
            self._validate_advanced_config(site_config)

            # Create website
            result = self.generator.create_website(site_config)

            if result["status"] == "success":
                print(f"✅ Website created successfully!")
                print(f"📁 Site path: {result['site_path']}")

                # Show asset optimization results
                if "asset_optimization" in result:
                    asset_results = result["asset_optimization"]
                    if asset_results["status"] == "success":
                        print("🎨 Asset optimization completed")
                        if "results" in asset_results:
                            results = asset_results["results"]
                            print(
                                f"   📸 Images: {results['images']['processed']} processed"
                            )
                            print(
                                f"   🎨 Styles: {results['styles']['processed']} processed"
                            )
                            print(
                                f"   📜 Scripts: {results['scripts']['processed']} processed"
                            )

                # Show metadata creation results
                if "metadata_creation" in result and result["metadata_creation"]:
                    metadata_results = result["metadata_creation"]
                    if metadata_results["status"] == "success":
                        print("💾 Content metadata saved to database")
                        print(f"   📊 Project ID: {metadata_results['project_id']}")
                        print(f"   📄 Content ID: {metadata_results['content_id']}")

                return result
            else:
                print(f"❌ Failed to create website: {result['message']}")
                return result

        except Exception as e:
            error_msg = f"Failed to create advanced website: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def create_theme(self, theme_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new theme with customizations
        """
        try:
            print(f"🎨 Creating theme: {theme_config['name']}")

            # Validate theme configuration
            self._validate_theme_config(theme_config)

            # Create theme
            result = self.theme_manager.create_theme(
                theme_config["name"],
                theme_config["base_template"],
                theme_config["customizations"],
            )

            if result["status"] == "success":
                print(f"✅ Theme created successfully!")
                print(f"📁 Theme path: {result['theme_path']}")

                # Show theme details
                theme_info = self.theme_manager.get_theme(theme_config["name"])
                if "assets" in theme_info:
                    assets = theme_info["assets"]
                    print("📦 Theme assets:")
                    print(f"   🖼️  Images: {len(assets['images'])}")
                    print(f"   🎨 Styles: {len(assets['styles'])}")
                    print(f"   📜 Scripts: {len(assets['scripts'])}")
                    print(f"   🔤 Fonts: {len(assets['fonts'])}")

                return result
            else:
                print(f"❌ Failed to create theme: {result['message']}")
                return result

        except Exception as e:
            error_msg = f"Failed to create theme: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def list_themes(self) -> Dict[str, Any]:
        """
        List all available themes with details
        """
        try:
            themes = self.theme_manager.list_themes()

            if not themes:
                print("📭 No themes found")
                return {"status": "success", "themes": {}}

            print("🎨 Available themes:")
            for theme_name, theme_info in themes.items():
                print(f"\n📋 {theme_name}:")
                print(f"   📁 Base template: {theme_info.get('base_template', 'N/A')}")
                print(f"   📅 Created: {theme_info.get('created_at', 'N/A')}")
                print(f"   📁 Path: {theme_info.get('path', 'N/A')}")

                if "assets" in theme_info:
                    assets = theme_info["assets"]
                    print(f"   📦 Assets: {sum(len(v) for v in assets.values())} total")

            return {"status": "success", "themes": themes}

        except Exception as e:
            error_msg = f"Failed to list themes: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def optimize_assets(self, site_name: str) -> Dict[str, Any]:
        """
        Optimize assets for a specific site
        """
        try:
            print(f"⚡ Optimizing assets for site: {site_name}")

            site_dir = Path("sites") / site_name
            if not site_dir.exists():
                return {"status": "error", "message": f"Site '{site_name}' not found"}

            result = self.asset_pipeline.optimize_assets(site_dir, site_name)

            if result["status"] == "success":
                print("✅ Asset optimization completed!")
                if "results" in result:
                    results = result["results"]
                    print(f"   📸 Images: {results['images']['processed']} processed")
                    print(f"   🎨 Styles: {results['styles']['processed']} processed")
                    print(f"   📜 Scripts: {results['scripts']['processed']} processed")

                    # Show any errors
                    total_errors = (
                        len(results["images"]["errors"])
                        + len(results["styles"]["errors"])
                        + len(results["scripts"]["errors"])
                    )
                    if total_errors > 0:
                        print(f"   ⚠️  {total_errors} errors encountered")

                return result
            else:
                print(f"❌ Asset optimization failed: {result['message']}")
                return result

        except Exception as e:
            error_msg = f"Failed to optimize assets: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def get_site_statistics(self, site_name: str) -> Dict[str, Any]:
        """
        Get detailed statistics for a site
        """
        try:
            sites = self.generator.list_sites()

            if site_name not in sites:
                return {"status": "error", "message": f"Site '{site_name}' not found"}

            site_info = sites[site_name]

            print(f"📊 Statistics for site: {site_name}")
            print(f"   📁 Total files: {site_info['statistics']['total_files']}")
            print(f"   📂 Directories: {site_info['statistics']['directories']}")
            print(f"   💾 Size: {site_info['statistics']['size_mb']} MB")
            print(f"   🎨 Theme: {site_info.get('theme', 'default')}")
            print(f"   📅 Created: {site_info.get('generated_at', 'N/A')}")
            print(
                f"   🔧 Generator version: {site_info.get('generator_version', 'N/A')}"
            )

            # Show features
            if "features" in site_info:
                features = site_info["features"]
                print("   ✨ Features:")
                print(
                    f"      🎨 Theme customization: {features.get('theme_customization', False)}"
                )
                print(
                    f"      ⚡ Asset optimization: {features.get('asset_optimization', False)}"
                )
                print(
                    f"      💾 Content metadata: {features.get('content_metadata', False)}"
                )

            return {"status": "success", "site_info": site_info}

        except Exception as e:
            error_msg = f"Failed to get site statistics: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def _validate_advanced_config(self, config: Dict[str, Any]) -> None:
        """Validate advanced site configuration"""
        required_fields = ["name", "template", "title", "description"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")

        # Validate theme if specified
        if "theme" in config:
            try:
                self.theme_manager.get_theme(config["theme"])
            except ValueError as e:
                raise ValueError(f"Theme validation failed: {str(e)}")

        # Validate content metadata
        if "keywords" in config:
            keywords = config["keywords"]
            if isinstance(keywords, str):
                keyword_list = [k.strip() for k in keywords.split(",")]
                if len(keyword_list) > 10:
                    raise ValueError("Maximum 10 keywords allowed")

    def _validate_theme_config(self, config: Dict[str, Any]) -> None:
        """Validate theme configuration"""
        required_fields = ["name", "base_template", "customizations"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")

        # Validate customizations structure
        customizations = config["customizations"]
        if not isinstance(customizations, dict):
            raise ValueError("Customizations must be a dictionary")

        # Validate CSS customizations
        if "css" in customizations:
            css_customizations = customizations["css"]
            if not isinstance(css_customizations, dict):
                raise ValueError("CSS customizations must be a dictionary")

            for key, value in css_customizations.items():
                if not isinstance(key, str) or not isinstance(value, str):
                    raise ValueError(
                        "CSS customizations must be string key-value pairs"
                    )

    def create_sample_theme(self) -> Dict[str, Any]:
        """
        Create a sample theme for demonstration
        """
        sample_config = {
            "name": "sample-modern",
            "base_template": "modern",
            "customizations": {
                "css": {
                    "primary-color": "#10b981",
                    "secondary-color": "#059669",
                    "accent-color": "#34d399",
                    "text-color": "#1f2937",
                    "background-color": "#f9fafb",
                },
                "html": {
                    "title": "Sample Modern Theme",
                    "description": "A sample modern theme with green colors",
                    "author": "AI Coding Agent",
                    "keywords": "sample, modern, green, clean",
                },
            },
        }

        return self.create_theme(sample_config)

    def create_sample_advanced_site(self) -> Dict[str, Any]:
        """
        Create a sample advanced website for demonstration
        """
        sample_config = {
            "name": "sample-advanced-site",
            "template": "modern",
            "theme": "sample-modern",
            "title": "Sample Advanced Website",
            "description": "A demonstration of advanced website generation features",
            "author": "AI Coding Agent",
            "keywords": "sample, advanced, demonstration, features",
        }

        return self.create_advanced_site(sample_config)


# CLI interface functions
def create_advanced_site_cli(args: Dict[str, Any]) -> None:
    """CLI function to create an advanced site"""
    commands = AdvancedGeneratorCommands()
    result = commands.create_advanced_site(args)

    if result["status"] == "success":
        print("\n🎉 Advanced website creation completed successfully!")
    else:
        print(f"\n❌ Error: {result['message']}")


def create_theme_cli(args: Dict[str, Any]) -> None:
    """CLI function to create a theme"""
    commands = AdvancedGeneratorCommands()
    result = commands.create_theme(args)

    if result["status"] == "success":
        print("\n🎉 Theme creation completed successfully!")
    else:
        print(f"\n❌ Error: {result['message']}")


def list_themes_cli() -> None:
    """CLI function to list themes"""
    commands = AdvancedGeneratorCommands()
    result = commands.list_themes()

    if result["status"] != "success":
        print(f"\n❌ Error: {result['message']}")


def optimize_assets_cli(site_name: str) -> None:
    """CLI function to optimize assets"""
    commands = AdvancedGeneratorCommands()
    result = commands.optimize_assets(site_name)

    if result["status"] == "success":
        print("\n🎉 Asset optimization completed successfully!")
    else:
        print(f"\n❌ Error: {result['message']}")


def get_statistics_cli(site_name: str) -> None:
    """CLI function to get site statistics"""
    commands = AdvancedGeneratorCommands()
    result = commands.get_site_statistics(site_name)

    if result["status"] != "success":
        print(f"\n❌ Error: {result['message']}")


def demo_advanced_features() -> None:
    """Demonstrate advanced features"""
    print("🚀 Demonstrating Advanced Generator Features (Phase 2.1)")
    print("=" * 60)

    commands = AdvancedGeneratorCommands()

    # Create sample theme
    print("\n1. Creating sample theme...")
    theme_result = commands.create_sample_theme()

    if theme_result["status"] == "success":
        # Create sample advanced site
        print("\n2. Creating sample advanced site...")
        site_result = commands.create_sample_advanced_site()

        if site_result["status"] == "success":
            # Show statistics
            print("\n3. Getting site statistics...")
            commands.get_site_statistics("sample-advanced-site")

            # Optimize assets
            print("\n4. Optimizing assets...")
            commands.optimize_assets("sample-advanced-site")

            print("\n✅ Advanced features demonstration completed!")
        else:
            print(f"❌ Failed to create sample site: {site_result['message']}")
    else:
        print(f"❌ Failed to create sample theme: {theme_result['message']}")


if __name__ == "__main__":
    # Run demonstration
    demo_advanced_features()
