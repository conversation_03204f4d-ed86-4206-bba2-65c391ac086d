# Use multi-stage build for optimal size and security
FROM nvidia/cuda:12.1.0-devel-ubuntu22.04 as builder

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3.11 \
    python3.11-dev \
    python3-pip \
    git \
    wget \
    curl \
    vim \
    libglib2.0-0 \
    libx11-6 \
    libsm6 \
    libxext6 \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=off
ENV PIP_DISABLE_PIP_VERSION_CHECK=on
ENV PIP_DEFAULT_TIMEOUT=100

# Install pip and setuptools
RUN pip3 install --upgrade pip setuptools wheel

# Create non-root user for security
RUN groupadd --system appgroup && useradd --system --gid appgroup appuser
USER appuser

# Create app directory
WORKDIR /app

# Copy fine-tuning requirements from config directory
COPY config/requirements-fine-tuning.txt ./requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Set ownership to non-root user
RUN chown -R appuser:appgroup /app

# Create health check endpoint
RUN echo 'from fastapi import FastAPI\nfrom starlette.responses import JSONResponse\n\napp = FastAPI()\n\<EMAIL>("/health")\nasync def health_check():\n    return JSONResponse(content={"status": "healthy", "service": "fine_tuner"}, status_code=200)' > /app/health_check.py

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD python3 /app/health_check.py

# Use non-root user
USER appuser

# Expose port for the fine-tuner service
EXPOSE 8002

# Set entrypoint
ENTRYPOINT ["python3", "/app/fine_tuning/fine_tuner_service.py"]
