# AI Coding Agent API Documentation

## Overview

The AI Coding Agent API provides a comprehensive RESTful interface for AI-powered coding assistance, file management, and project operations. This API is built with FastAPI and follows modern REST conventions.

## Table of Contents

- [Authentication](#authentication)
- [Base URL](#base-url)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Endpoints](#endpoints)
  - [Chat Endpoints](#chat-endpoints)
  - [Upload Endpoints](#upload-endpoints)
  - [Advanced Learning Endpoints](#advanced-learning-endpoints)
  - [Advanced Learning Enhancements Endpoints](#advanced-learning-enhancements-endpoints)
  - [Monitoring Endpoints](#monitoring-endpoints)
  - [Dashboard Endpoints](#dashboard-endpoints)
  - [Error Handling Endpoints](#error-handling-endpoints)
  - [Health Check Endpoints](#health-check-endpoints)
- [Models](#models)
- [Configuration](#configuration)
- [Middleware](#middleware)
- [CLI Integration](#cli-integration)
- [Local LLM Integration](#local-llm-integration)

## Authentication

The API supports Bearer token authentication. Include your API key in the Authorization header:

```bash
Authorization: Bearer your-api-key-here
```

**Note**: Health check and documentation endpoints are publicly accessible.

## Base URL

```
http://localhost:8000/api/v1
```

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r config/requirements.txt
   ```

2. **Start the API server:**
   ```bash
   python scripts/start_api.py
   ```

3. **Run comprehensive tests:**
   ```bash
   python scripts/test_api_comprehensive.py
   ```

4. **Access API documentation:**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## Error Handling

All API responses follow a consistent error format:

```json
{
  "success": false,
  "error": {
    "code": "error_code",
    "message": "Human-readable error message",
    "type": "ExceptionType",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid API key)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `413` - Payload Too Large (file/request too big)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error
- `503` - Service Unavailable (external service down)

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default**: 60 requests per minute per IP
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`
- **Response**: 429 status code when limit exceeded

## Endpoints

### Chat Endpoints

#### POST /chat

Send a message to the AI assistant.

**Request Body:**
```json
{
  "prompt": "Write a Python function to calculate fibonacci numbers",
  "context": {
    "file": "math_utils.py",
    "code": "def add(a, b): return a + b"
  },
  "intent": "code_generation",
  "history": [
    {"role": "user", "content": "Hello"},
    {"role": "assistant", "content": "Hi! How can I help you?"}
  ],
  "model": "deepseek-coder:1.3b",
  "complexity_hint": "medium"
}
```

**Response:**
```json
{
  "success": true,
  "response": {
    "content": "Here's a Python function to calculate fibonacci numbers...",
    "model": "deepseek-coder:1.3b",
    "success": true,
    "timestamp": "2024-01-01T12:00:00Z",
    "intent": "code_generation",
    "context_used": true,
    "history_length": 2,
    "complexity_hint": "medium"
  },
  "metadata": {
    "model_selected": "deepseek-coder:1.3b",
    "prompt_length": 45,
    "enhanced_prompt_length": 234,
    "processing_time": 2.5,
    "model_processing_time": 2.1,
    "model_available": true
  }
}
```

#### GET /chat/models

Get list of available AI models.

**Response:**
```json
[
  {
    "name": "deepseek-coder:1.3b",
    "type": "local",
    "status": "available",
    "description": "Local Ollama model: deepseek-coder:1.3b",
    "capabilities": ["text-generation", "code-generation"]
  }
]
```

#### POST /chat/test

Test the chat service.

**Response:**
```json
{
  "success": true,
  "message": "Chat service is working",
  "timestamp": "2024-01-01T12:00:00Z",
  "model": "deepseek-coder:1.3b"
}
```

### Upload Endpoints

#### POST /upload-site

Upload a website project.

**Form Data:**
- `files`: Array of files to upload
- `target_name`: Optional target site name
- `review_first`: Whether to review before processing (default: true)
- `cleanup_after`: Whether to cleanup after processing (default: false)

**Response:**
```json
{
  "success": true,
  "upload_id": "upload_123456",
  "message": "Upload processed successfully",
  "files_processed": 15,
  "upload_path": "sites/my-website"
}
```

#### GET /sites/list

List all uploaded sites.

**Response:**
```json
[
  {
    "name": "my-website",
    "path": "sites/my-website",
    "created_at": "2024-01-01T12:00:00Z",
    "last_modified": "2024-01-01T12:00:00Z",
    "file_count": 15,
    "size_mb": 2.5,
    "framework": "react",
    "status": "active"
  }
]
```

#### POST /sites/{site_name}/validate

Validate a site's structure and files.

**Response:**
```json
{
  "success": true,
  "valid": true,
  "issues": [],
  "warnings": ["Missing favicon.ico"]
}
```

#### GET /sites/{site_name}/manifest

Get site manifest with file structure.

**Response:**
```json
{
  "success": true,
  "manifest": {
    "site_name": "my-website",
    "files": [
      {
        "path": "index.html",
        "size": 1024,
        "type": "html"
      }
    ],
    "total_files": 15,
    "total_size": 2560000
  }
}
```

### Advanced Learning Endpoints

#### GET /advanced-learning/learning/summary

Get learning system summary and statistics.

**Response:**
```json
{
  "success": true,
  "summary": {
    "total_events": 150,
    "patterns_learned": 25,
    "preferences_learned": 12,
    "learning_accuracy": 0.85,
    "last_learning_session": "2024-01-01T12:00:00Z"
  }
}
```

#### GET /advanced-learning/learning/recommendations

Get personalized learning recommendations.

**Query Parameters:**
- `query`: Search query for recommendations
- `limit`: Maximum number of recommendations (default: 10)

**Response:**
```json
{
  "success": true,
  "recommendations": [
    {
      "type": "code_pattern",
      "confidence": 0.92,
      "description": "Use async/await pattern for database operations",
      "context": "database_operations"
    }
  ]
}
```

#### POST /advanced-learning/learning/event

Record a learning event.

**Request Body:**
```json
{
  "event_type": "code_generation",
  "user_id": "user123",
  "session_id": "session456",
  "context": {"language": "python", "complexity": "medium"},
  "data": {"code_length": 150, "success": true},
  "outcome": "success",
  "feedback_score": 0.9
}
```

#### POST /advanced-learning/learning/code-pattern

Learn from code patterns.

**Request Body:**
```json
{
  "code": "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
  "language": "python",
  "context": "mathematical_functions",
  "outcome": "success",
  "feedback_score": 0.95
}
```

#### POST /advanced-learning/learning/user-preference

Learn user preferences.

**Request Body:**
```json
{
  "user_id": "user123",
  "preference_type": "code_style",
  "preference_key": "indentation",
  "preference_value": "spaces",
  "confidence": 0.9
}
```

#### POST /advanced-learning/learning/performance-insight

Learn from performance insights.

**Request Body:**
```json
{
  "component": "code_generation",
  "metric": "response_time",
  "value": 2.5,
  "threshold": 3.0,
  "trend": "improving"
}
```

### Advanced Learning Enhancements Endpoints

The Advanced Learning Enhancements provide cutting-edge AI capabilities including meta-learning optimization, Pareto optimization, workload prediction, cascade failure detection, federated learning, capability discovery, adversarial detection, and graceful degradation management.

#### Meta Learning Optimizer

##### POST /advanced-learning-enhancements/meta-learning/optimize

Optimize learning rates for multiple models.

**Request Body:**
```json
{
  "model_performance": {
    "model1": [0.8, 0.85, 0.9],
    "model2": [0.7, 0.75, 0.8]
  }
}
```

**Response:**
```json
{
  "success": true,
  "optimized_parameters": {
    "model1": 0.001,
    "model2": 0.002
  },
  "models_optimized": 2,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

##### GET /advanced-learning-enhancements/meta-learning/insights

Get insights from meta learning optimizer.

**Response:**
```json
{
  "success": true,
  "insights": {
    "optimization_history": [...],
    "model_performance_trends": {...},
    "recommendations": [...]
  }
}
```

#### Pareto Optimizer

##### POST /advanced-learning-enhancements/pareto/solutions

Find Pareto optimal solutions.

**Request Body:**
```json
{
  "objectives": ["speed", "quality"],
  "current_performance": {
    "speed": 0.8,
    "quality": 0.9
  },
  "constraints": {
    "max_memory": 4096,
    "max_response_time": 2000
  }
}
```

##### GET /advanced-learning-enhancements/pareto/frontier

Get current Pareto frontier.

**Response:**
```json
{
  "success": true,
  "pareto_frontier": [...],
  "frontier_size": 5
}
```

#### Workload Predictor

##### POST /advanced-learning-enhancements/workload/predict

Predict demand spikes.

**Request Body:**
```json
{
  "historical_patterns": {
    "requests_per_minute": [10, 15, 20, 25, 30],
    "response_times": [100, 120, 150, 180, 200]
  },
  "external_factors": {
    "time_of_day": 0.8,
    "day_of_week": 0.6
  }
}
```

**Response:**
```json
{
  "success": true,
  "predicted_demand": 1.2,
  "confidence": 0.85,
  "time_horizon": 3600,
  "risk_level": "low",
  "recommendations": ["scale_up"]
}
```

##### GET /advanced-learning-enhancements/workload/insights

Get workload prediction insights.

**Response:**
```json
{
  "success": true,
  "insights": {
    "prediction_accuracy": 0.87,
    "historical_predictions": [...],
    "model_performance": {...}
  }
}
```

#### Cascade Predictor

##### POST /advanced-learning-enhancements/cascade/detect

Detect cascade failure risks.

**Request Body:**
```json
{
  "current_loads": {
    "model1": 0.8,
    "model2": 0.7,
    "model3": 0.9
  },
  "failure_patterns": {
    "model1": [0.1, 0.15, 0.2],
    "model2": [0.05, 0.1, 0.15]
  }
}
```

**Response:**
```json
{
  "success": true,
  "risk_level": "medium",
  "risk_score": 0.6,
  "vulnerable_components": ["model1"],
  "mitigation_strategies": ["load_balancing"]
}
```

##### GET /advanced-learning-enhancements/cascade/insights

Get cascade prediction insights.

**Response:**
```json
{
  "success": true,
  "insights": {
    "risk_history": [...],
    "detection_accuracy": 0.92,
    "prevented_cascades": 3
  }
}
```

#### Federated Learning Manager

##### POST /advanced-learning-enhancements/federated/start

Start federated learning session.

**Request Body:**
```json
{
  "participants": ["node1", "node2", "node3"],
  "model_configuration": {
    "learning_rate": 0.01,
    "batch_size": 32
  }
}
```

**Response:**
```json
{
  "success": true,
  "session_id": "fed_session_123",
  "participants": ["node1", "node2", "node3"],
  "status": "active",
  "rounds_planned": 10
}
```

##### GET /advanced-learning-enhancements/federated/status

Get federated learning status.

**Query Parameters:**
- `session_id`: Optional session ID to get specific session status

**Response:**
```json
{
  "success": true,
  "status": {
    "active_sessions": 2,
    "total_participants": 6,
    "average_accuracy": 0.89
  }
}
```

#### Capability Discovery

##### POST /advanced-learning-enhancements/capability/discover

Discover model capabilities.

**Request Body:**
```json
{
  "model_id": "test_model_001",
  "test_scenarios": [
    {
      "scenario": "code_generation",
      "complexity": "high"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "model_id": "test_model_001",
  "discovered_capabilities": ["code_generation", "bug_fixing"],
  "capability_scores": {
    "code_generation": 0.92,
    "bug_fixing": 0.78
  },
  "confidence_levels": {
    "code_generation": 0.95,
    "bug_fixing": 0.82
  }
}
```

##### GET /advanced-learning-enhancements/capability/insights

Get capability discovery insights.

**Response:**
```json
{
  "success": true,
  "insights": {
    "discovered_models": 15,
    "capability_database": {...},
    "discovery_accuracy": 0.88
  }
}
```

#### Adversarial Detector

##### POST /advanced-learning-enhancements/adversarial/detect

Detect adversarial activity.

**Request Body:**
```json
{
  "user_behavior": {
    "request_pattern": "suspicious",
    "frequency": "high"
  },
  "system_metrics": {
    "cpu_usage": 0.9,
    "memory_usage": 0.8
  }
}
```

**Response:**
```json
{
  "success": true,
  "threat_detected": true,
  "threat_level": "medium",
  "threat_type": "rate_limit_bypass",
  "confidence": 0.87,
  "mitigation_actions": ["rate_limiting", "logging"]
}
```

##### GET /advanced-learning-enhancements/adversarial/insights

Get adversarial detection insights.

**Response:**
```json
{
  "success": true,
  "insights": {
    "detected_threats": 5,
    "false_positives": 1,
    "detection_accuracy": 0.94,
    "threat_patterns": {...}
  }
}
```

#### Degradation Manager

##### POST /advanced-learning-enhancements/degradation/assess

Assess degradation risk.

**Request Body:**
```json
{
  "system_metrics": {
    "cpu_usage": 0.9,
    "memory_usage": 0.8,
    "response_time": 2000
  },
  "performance_thresholds": {
    "max_cpu": 0.8,
    "max_memory": 0.8,
    "max_response_time": 1500
  }
}
```

**Response:**
```json
{
  "success": true,
  "degradation_risk": 0.7,
  "risk_level": "high",
  "affected_components": ["model1", "model2"],
  "graceful_degradation_plan": ["disable_non_essential", "load_balancing"]
}
```

##### GET /advanced-learning-enhancements/degradation/insights

Get degradation management insights.

**Response:**
```json
{
  "success": true,
  "insights": {
    "degradation_events": 2,
    "prevented_degradations": 8,
    "graceful_degradations": 3,
    "system_health_score": 0.85
  }
}
```

#### System-wide Endpoints

##### GET /advanced-learning-enhancements/status

Get status of all advanced learning enhancements.

**Response:**
```json
{
  "success": true,
  "components_status": {
    "meta_learning_optimizer": {"available": true, "status": "initialized"},
    "pareto_optimizer": {"available": true, "status": "initialized"},
    "workload_predictor": {"available": true, "status": "initialized"},
    "cascade_predictor": {"available": true, "status": "initialized"},
    "federated_learning_manager": {"available": true, "status": "initialized"},
    "capability_discovery": {"available": true, "status": "initialized"},
    "adversarial_detector": {"available": true, "status": "initialized"},
    "degradation_manager": {"available": true, "status": "initialized"}
  },
  "total_components": 8,
  "available_components": 8
}
```

##### POST /advanced-learning-enhancements/cycle

Run a complete enhancement cycle for all components.

**Response:**
```json
{
  "success": true,
  "cycle_results": {
    "meta_learning": {...},
    "workload_prediction": {...},
    "cascade_detection": {...},
    "adversarial_detection": {...},
    "degradation_assessment": {...}
  },
  "components_processed": 5
}
```

##### GET /advanced-learning-enhancements/health

Health check for advanced learning enhancements.

**Response:**
```json
{
  "status": "healthy",
  "service": "Advanced Learning Enhancements API",
  "version": "2.0.0",
  "components": [
    "Meta Learning Optimizer",
    "Pareto Optimizer",
    "Workload Predictor",
    "Cascade Predictor",
    "Federated Learning Manager",
    "Capability Discovery",
    "Adversarial Detector",
    "Degradation Manager"
  ]
}
```

### Error Handling Endpoints

#### POST /errors/report

Report an error from the client.

**Request Body:**
```json
{
  "message": "Failed to load component",
  "type": "frontend_error",
  "severity": "medium",
  "context": {
    "component": "FileEditor",
    "user_action": "save_file"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Error reported successfully"
}
```

#### GET /errors/health

Check error handling service health.

**Response:**
```json
{
  "status": "healthy",
  "error_count": 0,
  "last_error": null
}
```

### Health Check Endpoints

#### GET /health

Comprehensive health check for all services.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00Z",
  "services": {
    "system_resources": "healthy",
    "database": "healthy",
    "ai_models": "healthy",
    "file_system": "healthy",
    "network": "healthy",
    "api_endpoints": "healthy"
  },
  "uptime_seconds": 3600
}
```

#### GET /health/{service}

Get detailed health information for a specific service.

**Response:**
```json
{
  "name": "ai_models",
  "status": "healthy",
  "response_time": 0.15,
  "details": {
    "available_models": ["deepseek-coder:1.3b", "yi-coder:1.5b"],
    "required_models": ["deepseek-coder:1.3b", "yi-coder:1.5b"],
    "missing_models": []
  }
}
```

## Models

### ChatRequest
```python
{
  "prompt": str,                    # Required: User's message
  "context": Optional[Dict],        # Additional context
  "intent": Optional[str],          # User's intent
  "history": Optional[List],        # Conversation history
  "model": Optional[str],           # AI model to use
  "complexity_hint": Optional[str]  # Task complexity hint
}
```

### ChatResponse
```python
{
  "success": bool,                  # Whether request succeeded
  "response": Dict[str, Any],       # AI response content
  "metadata": Dict[str, Any]        # Request metadata
}
```

### ModelInfo
```python
{
  "name": str,                      # Model name
  "type": str,                      # Model type (local/cloud)
  "status": str,                    # Model status
  "description": Optional[str],     # Model description
  "capabilities": List[str]         # Model capabilities
}
```

### SiteInfo
```python
{
  "name": str,                      # Site name
  "path": str,                      # Site path
  "created_at": datetime,           # Creation timestamp
  "last_modified": datetime,        # Last modification
  "file_count": int,                # Number of files
  "size_mb": float,                 # Total size in MB
  "framework": Optional[str],       # Detected framework
  "status": str                     # Site status
}
```

## Configuration

The API can be configured through environment variables or a configuration file:

### Environment Variables

```bash
# Server settings
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false
API_RELOAD=false

# Security
API_KEY=your-secret-api-key
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
RATE_LIMIT_RPM=60
MAX_CONTENT_LENGTH_MB=10

# Models
DEFAULT_MODEL=deepseek-coder:1.3b
AVAILABLE_MODELS=deepseek-coder:1.3b,yi-coder:1.5b
MODEL_TIMEOUT_SECONDS=30

# Upload settings
MAX_UPLOAD_SIZE_MB=100
MAX_INDIVIDUAL_FILE_SIZE_MB=50

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/api.log

# Feature flags
ENABLE_WEBSOCKETS=true
ENABLE_FILE_EDITING=true
ENABLE_GIT_OPERATIONS=true
ENABLE_PREVIEW_SERVER=true
ENABLE_COMMAND_EXECUTION=true
```

### Configuration File

Create `config/api_config.json`:

```json
{
  "host": "0.0.0.0",
  "port": 8000,
  "debug": false,
  "api_key": "your-secret-api-key",
  "rate_limit_requests_per_minute": 60,
  "default_model": "deepseek-coder:1.3b",
  "max_upload_size_mb": 100,
  "enable_websockets": true,
  "enable_file_editing": true
}
```

## Middleware

The API includes several middleware components:

### Rate Limiting
- Limits requests per IP address
- Configurable rate limits
- Automatic cleanup of old requests

### Logging
- Request/response logging
- Performance timing
- Structured log format

### Security
- Security headers (CSP, XSS protection, etc.)
- Request validation
- Content length limits

### Error Handling
- Global exception handling
- Standardized error responses
- Error logging and reporting

### Authentication
- API key validation
- Bearer token support
- Public endpoint exclusions

### Metrics
- Request counting
- Response time tracking
- Service health monitoring

## Usage Examples

### Python Client

```python
import requests

# Configure client
API_BASE = "http://localhost:8000/api/v1"
API_KEY = "your-api-key"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# Send chat message
response = requests.post(
    f"{API_BASE}/chat",
    json={
        "prompt": "Write a Python function to sort a list",
        "intent": "code_generation"
    },
    headers=headers
)

if response.status_code == 200:
    result = response.json()
    print(result["response"]["content"])
else:
    print(f"Error: {response.status_code} - {response.text}")
```

### JavaScript Client

```javascript
const API_BASE = 'http://localhost:8000/api/v1';
const API_KEY = 'your-api-key';

async function sendChatMessage(prompt) {
    try {
        const response = await fetch(`${API_BASE}/chat`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: prompt,
                intent: 'code_generation'
            })
        });

        if (response.ok) {
            const result = await response.json();
            return result.response.content;
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('Chat request failed:', error);
        throw error;
    }
}

// Usage
sendChatMessage('Write a JavaScript function to validate email')
    .then(content => console.log(content))
    .catch(error => console.error(error));
```

### cURL Examples

```bash
# Send chat message
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a Python function to calculate factorial",
    "intent": "code_generation"
  }'

# Get available models
curl -X GET "http://localhost:8000/api/v1/chat/models" \
  -H "Authorization: Bearer your-api-key"

# Check health
curl -X GET "http://localhost:8000/health"

# List sites
curl -X GET "http://localhost:8000/api/sites/list" \
  -H "Authorization: Bearer your-api-key"
```

## Development

### Running the API

```bash
# Install dependencies
pip install -r requirements.txt

# Run with uvicorn
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

# Run with custom config
API_KEY=dev-key uvicorn api.main:app --host 0.0.0.0 --port 8000
```

### Testing

```bash
# Run tests
pytest tests/api/

# Test specific endpoint
pytest tests/api/test_chat_routes.py

# Run with coverage
pytest tests/api/ --cov=api --cov-report=html
```

### Documentation

The API automatically generates OpenAPI/Swagger documentation:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## CLI Integration

The AI Coding Agent provides comprehensive CLI commands that mirror all API functionality, enabling local Ollama LLMs to interact with the system through command-line interfaces.

### CLI Command Structure

All CLI commands follow a consistent pattern:

```python
class FeatureNameCommands:
    def __init__(self, agent):
        self.agent = agent

    async def feature_action(self, **kwargs) -> Dict[str, Any]:
        """Execute feature action"""
        try:
            # Implementation
            return {"success": True, "result": result}
        except Exception as e:
            return {"success": False, "error": str(e)}
```

### Available CLI Commands

#### Advanced Learning Commands
- `get_learning_summary()` - Get learning system summary
- `get_learning_recommendations(query, limit)` - Get personalized recommendations
- `record_learning_event(event_type, user_id, session_id, context, data, outcome, feedback_score)` - Record learning events
- `learn_code_pattern(code, language, context, outcome, feedback_score)` - Learn from code patterns
- `learn_user_preference(user_id, preference_type, preference_key, preference_value, confidence)` - Learn user preferences
- `learn_performance_insight(component, metric, value, threshold, trend)` - Learn from performance insights

#### Advanced Learning Enhancements Commands
- `optimize_learning_rates(model_performance)` - Optimize learning rates for models
- `find_pareto_solutions(objectives, current_performance, constraints)` - Find Pareto optimal solutions
- `predict_demand(historical_patterns, external_factors)` - Predict demand spikes
- `detect_cascade_risk(current_loads, failure_patterns)` - Detect cascade failure risks
- `start_federated_learning(participants, model_configuration)` - Start federated learning session
- `discover_capabilities(model_id, test_scenarios)` - Discover model capabilities
- `detect_adversarial_activity(user_behavior, system_metrics)` - Detect adversarial activity
- `assess_degradation_risk(system_metrics, performance_thresholds)` - Assess degradation risk

### CLI Usage Examples

```python
# Initialize CLI commands
from cli.advanced_learning_commands import AdvancedLearningCommands
from cli.advanced_learning_enhancements_commands import AdvancedLearningEnhancementsCommands

# Create agent instance
agent = AIAgent("config/smart_routing_config.json")

# Initialize CLI commands
learning_commands = AdvancedLearningCommands(agent)
enhancement_commands = AdvancedLearningEnhancementsCommands(agent)

# Use CLI commands
result = await learning_commands.get_learning_summary()
result = await enhancement_commands.optimize_learning_rates({
    "model1": [0.8, 0.85, 0.9],
    "model2": [0.7, 0.75, 0.8]
})
```

## Local LLM Integration

The AI Coding Agent is specifically designed for integration with local Ollama LLMs, providing both CLI and API interfaces that enable local models to utilize all system capabilities.

### Supported Local Models

- `deepseek-coder:1.3b`
- `yi-coder:1.5b`
- `qwen2.5-coder:3b`
- `starcoder2:3b`
- `mistral:7b-instruct-q4_0`

### LLM-Friendly Design Features

#### Structured Responses
All CLI and API responses return JSON format for easy parsing by LLMs:

```json
{
  "success": true,
  "result": {...},
  "metadata": {...},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Clear Command Names
Commands use descriptive, intuitive names that LLMs can easily understand:
- `optimize_learning_rates` - Clear purpose and expected input
- `detect_cascade_risk` - Descriptive action and target
- `get_learning_summary` - Simple retrieval operation

#### Comprehensive Error Handling
All commands provide detailed error information:

```json
{
  "success": false,
  "error": "Detailed error message with context",
  "error_code": "SPECIFIC_ERROR_CODE",
  "suggestions": ["Try this", "Or try that"]
}
```

#### Status Indicators
Every response includes success/failure status for reliable LLM decision-making.

### LLM Integration Examples

#### Using CLI Commands with Ollama

```python
import ollama
from cli.advanced_learning_enhancements_commands import AdvancedLearningEnhancementsCommands

# Initialize CLI commands
commands = AdvancedLearningEnhancementsCommands(agent)

# LLM can use CLI commands
async def llm_optimize_models():
    result = await commands.optimize_learning_rates({
        "model1": [0.8, 0.85, 0.9],
        "model2": [0.7, 0.75, 0.8]
    })

    if result["success"]:
        return f"Optimized {result['models_optimized']} models"
    else:
        return f"Optimization failed: {result['error']}"
```

#### Using API Endpoints with Ollama

```python
import requests

# LLM can make API calls
def llm_predict_workload():
    response = requests.post(
        "http://localhost:8000/api/v1/advanced-learning-enhancements/workload/predict",
        json={
            "historical_patterns": {
                "requests_per_minute": [10, 15, 20, 25, 30]
            },
            "external_factors": {
                "time_of_day": 0.8
            }
        }
    )

    if response.status_code == 200:
        data = response.json()
        return f"Predicted demand: {data['predicted_demand']}"
    else:
        return f"Prediction failed: {response.text}"
```

### Best Practices for LLM Integration

1. **Use CLI for Direct Operations**: CLI commands provide direct access to system functionality
2. **Use API for External Integration**: API endpoints enable integration with other systems
3. **Handle Errors Gracefully**: Always check `success` field in responses
4. **Provide Context**: Include relevant context in requests for better results
5. **Use Structured Data**: Leverage JSON request/response format for reliable parsing

## Support

For API support and questions:

- **Documentation**: This README and generated docs
- **Issues**: GitHub repository issues
- **Email**: <EMAIL>

## Version History

- **v1.0.0** - Initial release with chat, upload, and error handling
- **v1.1.0** - Added health checks and improved error handling
- **v1.2.0** - Added Pydantic models and configuration management
- **v2.0.0** - Added Advanced Learning Enhancements with CLI and API integration
- **v2.1.0** - Enhanced local LLM integration and comprehensive CLI commands
