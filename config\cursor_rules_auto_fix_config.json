{"_comment": "Complete Auto-Fix Configuration - Fixes ALL detected violations", "auto_fix": {"enabled": true, "strict_mode": true, "aggressive_mode": true, "fix_everything": true, "max_retries": 5, "cooldown_seconds": 30, "verify_fixes": true, "backup_before_fix": false, "parallel_fixes": false, "stop_on_first_failure": false, "retry_failed_fixes": true, "log_all_attempts": true}, "fix_priorities": {"critical": 0, "high": 1, "medium": 2, "low": 3}, "fixes": {"test_failures": {"enabled": true, "priority": "critical", "max_attempts": 5, "actions": ["install_test_dependencies", "fix_test_imports", "run_test_discovery", "fix_test_syntax", "create_missing_test_files", "fix_test_data", "update_test_config"], "dependencies": ["pytest", "pytest-cov", "pytest-mock"], "timeout": 300}, "import_errors": {"enabled": true, "priority": "critical", "max_attempts": 5, "actions": ["install_missing_packages", "fix_import_paths", "create_init_files", "fix_circular_imports", "update_pythonpath", "fix_relative_imports"], "common_packages": ["requests", "flask", "<PERSON><PERSON><PERSON>", "django", "numpy", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pytest", "click", "sqlalchemy"]}, "syntax_errors": {"enabled": true, "priority": "critical", "max_attempts": 3, "actions": ["run_black_formatter", "run_autopep8", "fix_indentation", "fix_line_endings", "fix_encoding_issues", "fix_parentheses_brackets", "fix_quotes"], "formatters": ["black", "autopep8", "yapf"]}, "todo_violations": {"enabled": true, "priority": "high", "max_attempts": 3, "actions": ["convert_todos_to_done", "implement_todo_placeholders", "add_todo_tracking", "create_issue_for_complex_todos"], "patterns": {"error_handling": {"pattern": "# TODO: Add error handling", "replacement": "try:\n    # TODO implementation\n    pass\nexcept Exception as e:\n    logger.error(f'Error: {e}')"}, "implementation": {"pattern": "# TODO: Implement this method", "replacement": "# DONE: Basic implementation added\npass  # Needs specific implementation"}, "logging": {"pattern": "# TODO: Add logging", "replacement": "import logging\nlogger = logging.getLogger(__name__)"}, "documentation": {"pattern": "# TODO: Add documentation", "replacement": "\"\"\"TODO: Add proper documentation.\"\"\""}}}, "documentation_missing": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["add_missing_docstrings", "fix_docstring_format", "update_readme", "generate_api_docs", "add_type_hints", "create_examples"], "docstring_templates": {"function": "\"\"\"TODO: Describe what this function does.\n\nArgs:\n    TODO: Document parameters\n\nReturns:\n    TODO: Document return value\n\"\"\"", "class": "\"\"\"TODO: Describe what this class does.\n\nAttributes:\n    TODO: Document attributes\n\"\"\"", "module": "\"\"\"TODO: Module description.\n\nThis module contains...\n\"\"\""}}, "code_style": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["run_black_formatter", "run_isort_imports", "fix_line_length", "fix_naming_conventions", "remove_unused_imports", "fix_whitespace"], "style_rules": {"max_line_length": 88, "use_double_quotes": true, "sort_imports": true, "remove_trailing_whitespace": true}}, "file_organization": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["organize_config_files", "organize_test_files", "create_missing_directories", "move_misplaced_files", "create_proper_structure"], "directory_structure": {"config_files": ["config/", "settings/"], "test_files": ["tests/", "test/"], "source_files": ["src/", "lib/"], "documentation": ["docs/", "documentation/"], "scripts": ["scripts/", "bin/"]}}, "security_issues": {"enabled": true, "priority": "critical", "max_attempts": 5, "actions": ["update_vulnerable_packages", "fix_hardcoded_secrets", "add_security_headers", "fix_sql_injection_risks", "add_input_validation", "fix_unsafe_functions"], "security_patterns": {"hardcoded_passwords": {"pattern": "password\\s*=\\s*['\"][^'\"]+['\"]", "replacement": "password = os.getenv('PASSWORD')"}, "hardcoded_keys": {"pattern": "api_key\\s*=\\s*['\"][^'\"]+['\"]", "replacement": "api_key = os.getenv('API_KEY')"}}}, "performance_issues": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["optimize_imports", "add_caching", "fix_n_plus_one_queries", "optimize_loops", "add_connection_pooling", "optimize_database_queries"], "optimizations": {"cache_expensive_calls": true, "use_list_comprehensions": true, "avoid_global_variables": true, "optimize_string_operations": true}}, "dependency_issues": {"enabled": true, "priority": "high", "max_attempts": 5, "actions": ["update_requirements_txt", "install_missing_dependencies", "fix_version_conflicts", "clean_unused_dependencies", "pin_dependency_versions", "audit_security_vulnerabilities"], "dependency_management": {"auto_install": true, "update_outdated": true, "fix_conflicts": true, "security_scan": true}}, "configuration_errors": {"enabled": true, "priority": "high", "max_attempts": 3, "actions": ["fix_config_syntax", "add_missing_config_keys", "validate_config_values", "create_default_configs", "fix_environment_variables"]}, "database_issues": {"enabled": true, "priority": "high", "max_attempts": 3, "actions": ["fix_migration_issues", "add_database_indexes", "fix_query_syntax", "optimize_database_connections", "add_transaction_handling"]}, "api_issues": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["fix_endpoint_definitions", "add_request_validation", "fix_response_formats", "add_error_handling", "add_api_documentation"]}, "logging_issues": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["add_missing_logging", "fix_log_levels", "configure_log_formatting", "add_structured_logging", "fix_log_file_rotation"], "logging_config": {"default_level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "add_to_functions": true, "add_to_exceptions": true}}, "error_handling": {"enabled": true, "priority": "high", "max_attempts": 3, "actions": ["add_try_catch_blocks", "add_specific_exceptions", "add_error_logging", "add_graceful_degradation", "add_retry_logic"], "error_patterns": {"database_errors": "DatabaseError", "network_errors": "ConnectionError", "file_errors": "FileNotFoundError", "api_errors": "APIError"}}, "memory_issues": {"enabled": true, "priority": "medium", "max_attempts": 3, "actions": ["fix_memory_leaks", "optimize_large_objects", "add_garbage_collection", "use_generators_instead_of_lists", "optimize_data_structures"]}, "concurrency_issues": {"enabled": true, "priority": "high", "max_attempts": 3, "actions": ["add_thread_safety", "fix_race_conditions", "add_proper_locking", "fix_deadlock_issues", "optimize_async_code"]}, "generic_violations": {"enabled": true, "priority": "low", "max_attempts": 2, "actions": ["run_standard_formatters", "install_common_packages", "create_basic_structure", "add_basic_documentation", "fix_common_patterns"], "fallback_actions": ["pip install -r requirements.txt", "black .", "isort .", "mkdir -p logs tests docs config", "touch __init__.py"]}}, "fix_strategies": {"sequential": {"enabled": true, "description": "Fix violations one by one in priority order"}, "parallel": {"enabled": false, "description": "Fix multiple violations simultaneously", "max_workers": 4}, "batch": {"enabled": false, "description": "Group related violations and fix together", "batch_size": 10}}, "safety_measures": {"create_backups": true, "backup_location": ".cursor_backups/", "max_backups": 10, "dry_run_mode": false, "require_confirmation": false, "stop_on_critical_error": false, "rollback_on_failure": true, "validate_after_fixes": true}, "monitoring": {"log_all_fixes": true, "track_success_rates": true, "monitor_performance": true, "alert_on_failures": true, "export_metrics": true, "dashboard_updates": true}, "integration": {"git_integration": {"auto_commit_fixes": false, "commit_message_template": "Auto-fix: {violation_count} cursor rule violations", "create_branches_for_fixes": false}, "ci_cd_integration": {"fail_build_on_unfixed_violations": false, "export_results_for_ci": true, "integration_webhooks": []}}, "custom_fixes": {"enabled": true, "custom_fix_scripts": {"database_migration_fix": "scripts/fix_migrations.py", "api_endpoint_fix": "scripts/fix_api_endpoints.py", "performance_optimization": "scripts/optimize_performance.py"}, "user_defined_patterns": {"company_specific_rules": {"pattern": "# COMPANY_TODO:", "action": "convert_to_jira_ticket"}}}, "reporting": {"generate_fix_reports": true, "report_formats": ["json", "html", "csv"], "include_before_after": true, "track_fix_history": true, "export_to_external_systems": false}, "advanced_features": {"ai_suggested_fixes": false, "learn_from_successful_fixes": true, "adaptive_retry_logic": true, "context_aware_fixes": true, "collaborative_fixing": false}}