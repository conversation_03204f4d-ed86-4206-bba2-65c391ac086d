FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
WORKDIR /app
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir docker fastapi uvicorn pydantic requests

FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
WORKDIR /app
COPY --from=builder /opt/venv /opt/venv
COPY core/error_watcher_agent.py /app/error_watcher_agent.py
COPY models/ollama_manager.py /app/models/ollama_manager.py
COPY config/error_watcher_agent_config.json /app/config/error_watcher_agent_config.json
RUN mkdir -p /app/models /app/config \
 && adduser --system appuser \
 && chown -R appuser:appuser /app
USER appuser
EXPOSE 8091
HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD curl --fail http://localhost:8091/health || exit 1
ENTRYPOINT ["uvicorn", "error_watcher_agent:app", "--host", "0.0.0.0", "--port", "8091"]
