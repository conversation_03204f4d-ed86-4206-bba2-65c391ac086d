import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { toast } from 'react-hot-toast';
import {
  Clock,
  Globe,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';

interface AuthLog {
  id: number;
  event_type: string;
  ip_address: string;
  user_agent: string;
  details: Record<string, any>;
  created_at: string;
}

export const AuthLogs: React.FC = () => {
  const [logs, setLogs] = useState<AuthLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const { user } = useAuthStore();

  useEffect(() => {
    if (user) {
      loadAuthLogs();
    }
  }, [user]);

  const loadAuthLogs = async () => {
    try {
      setIsLoading(true);
      // This would call your API to get auth logs
      // const response = await api.getAuthLogs();
      // setLogs(response.data);

      // Mock data for now
      const mockLogs: AuthLog[] = [
        {
          id: 1,
          event_type: 'login',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          details: { success: true, session_id: 1 },
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          event_type: 'login_failed',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          details: { reason: 'invalid_password', attempts: 1 },
          created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          event_type: 'logout',
          ip_address: '*********',
          user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
          details: { session_id: 2 },
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 4,
          event_type: 'session_revoked',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          details: { session_id: 3, revoked_by_user: true },
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 5,
          event_type: 'password_change',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          details: { success: true },
          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];

      setLogs(mockLogs);
    } catch (error) {
      toast.error('Failed to load authentication logs');
      console.error('Error loading auth logs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const exportLogs = async () => {
    try {
      // This would call your API to export logs
      // const response = await api.exportAuthLogs();
      // Download the file

      toast.success('Authentication logs exported successfully');
    } catch (error) {
      toast.error('Failed to export logs');
      console.error('Error exporting logs:', error);
    }
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'login':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'login_failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'logout':
        return <Shield className="w-4 h-4 text-blue-600" />;
      case 'session_revoked':
        return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'password_change':
        return <Shield className="w-4 h-4 text-purple-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getEventColor = (eventType: string) => {
    switch (eventType) {
      case 'login':
        return 'bg-green-100 text-green-800';
      case 'login_failed':
        return 'bg-red-100 text-red-800';
      case 'logout':
        return 'bg-blue-100 text-blue-800';
      case 'session_revoked':
        return 'bg-orange-100 text-orange-800';
      case 'password_change':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatEventType = (eventType: string) => {
    return eventType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`;
    return `${Math.floor(diffInMinutes / 1440)} days ago`;
  };

  const filteredLogs = logs.filter(log => {
    if (filter === 'all') return true;
    if (filter === 'successful') return log.event_type === 'login' || log.event_type === 'password_change';
    if (filter === 'failed') return log.event_type === 'login_failed';
    if (filter === 'security') return log.event_type === 'session_revoked' || log.event_type === 'logout';
    return true;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading authentication logs...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Authentication Logs</h2>
          <p className="text-gray-600 mt-1">
            Review your account activity and security events
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Events</option>
            <option value="successful">Successful Logins</option>
            <option value="failed">Failed Attempts</option>
            <option value="security">Security Events</option>
          </select>
          <Button
            onClick={exportLogs}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="space-y-3">
        {filteredLogs.map((log) => (
          <div
            key={log.id}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                {getEventIcon(log.event_type)}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h3 className="text-sm font-medium text-gray-900">
                    {formatEventType(log.event_type)}
                  </h3>
                  <Badge
                    variant="secondary"
                    className={`text-xs ${getEventColor(log.event_type)}`}
                  >
                    {log.event_type}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {getTimeAgo(log.created_at)}
                  </span>
                </div>

                <div className="mt-1 text-sm text-gray-500 space-y-1">
                  <div className="flex items-center space-x-2">
                    <Globe className="w-3 h-3" />
                    <span>{log.ip_address}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(log.created_at)}</span>
                  </div>

                  {log.details && Object.keys(log.details).length > 0 && (
                    <div className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded">
                      <strong>Details:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredLogs.length === 0 && (
        <div className="text-center py-8">
          <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Authentication Logs</h3>
          <p className="text-gray-600">
            {filter === 'all'
              ? 'No authentication events found.'
              : `No ${filter} events found.`
            }
          </p>
        </div>
      )}

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-yellow-900">Security Monitoring</h4>
            <p className="mt-1 text-sm text-yellow-800">
              We monitor your account for suspicious activity. If you notice any unusual login attempts
              or don't recognize an IP address, please contact support immediately.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
