import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { toast } from 'react-hot-toast';
import {
  Monitor,
  Smartphone,
  Tablet,
  Globe,
  Clock,
  MapPin,
  X,
  RefreshCw,
  Shield,
  Activity
} from 'lucide-react';

interface Session {
  id: number;
  ip_address: string;
  user_agent: string;
  device_info: {
    browser: string;
    os: string;
    device: string;
  };
  created_at: string;
  last_activity: string;
  expires_at: string;
  is_current: boolean;
}

export const SessionManager: React.FC = () => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [revokingSession, setRevokingSession] = useState<number | null>(null);
  const { user } = useAuthStore();

  useEffect(() => {
    if (user) {
      loadSessions();
    }
  }, [user]);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      // This would call your API to get sessions
      // const response = await api.getUserSessions();
      // setSessions(response.data);

      // Mock data for now
      const mockSessions: Session[] = [
        {
          id: 1,
          ip_address: "*************",
          user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          device_info: {
            browser: "Chrome",
            os: "Windows",
            device: "Desktop"
          },
          created_at: new Date().toISOString(),
          last_activity: new Date().toISOString(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          is_current: true
        },
        {
          id: 2,
          ip_address: "*********",
          user_agent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
          device_info: {
            browser: "Safari",
            os: "iOS",
            device: "Mobile"
          },
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          last_activity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          is_current: false
        }
      ];

      setSessions(mockSessions);
    } catch (error) {
      toast.error('Failed to load sessions');
      console.error('Error loading sessions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const revokeSession = async (sessionId: number) => {
    try {
      setRevokingSession(sessionId);
      // This would call your API to revoke the session
      // await api.revokeSession(sessionId);

      // Remove from local state
      setSessions(prev => prev.filter(session => session.id !== sessionId));
      toast.success('Session revoked successfully');
    } catch (error) {
      toast.error('Failed to revoke session');
      console.error('Error revoking session:', error);
    } finally {
      setRevokingSession(null);
    }
  };

  const revokeAllOtherSessions = async () => {
    try {
      // This would call your API to revoke all other sessions
      // await api.revokeAllOtherSessions();

      // Keep only current session
      setSessions(prev => prev.filter(session => session.is_current));
      toast.success('All other sessions revoked successfully');
    } catch (error) {
      toast.error('Failed to revoke sessions');
      console.error('Error revoking sessions:', error);
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case 'mobile':
        return <Smartphone className="w-4 h-4" />;
      case 'tablet':
        return <Tablet className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`;
    return `${Math.floor(diffInMinutes / 1440)} days ago`;
  };

  const getStatusColor = (session: Session) => {
    if (session.is_current) return 'bg-green-100 text-green-800';
    const lastActivity = new Date(session.last_activity);
    const now = new Date();
    const diffInHours = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) return 'bg-blue-100 text-blue-800';
    if (diffInHours < 24) return 'bg-yellow-100 text-yellow-800';
    return 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading sessions...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Active Sessions</h2>
          <p className="text-gray-600 mt-1">
            Manage your active sessions across different devices
          </p>
        </div>
        <Button
          onClick={revokeAllOtherSessions}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Shield className="w-4 h-4" />
          Revoke All Others
        </Button>
      </div>

      <div className="grid gap-4">
        {sessions.map((session) => (
          <div
            key={session.id}
            className={`p-4 border rounded-lg ${
              session.is_current ? 'border-green-200 bg-green-50' : 'border-gray-200'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  {getDeviceIcon(session.device_info.device)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-sm font-medium text-gray-900">
                      {session.device_info.browser} on {session.device_info.os}
                    </h3>
                    {session.is_current && (
                      <Badge variant="success" className="text-xs">
                        Current Session
                      </Badge>
                    )}
                    <Badge
                      variant="secondary"
                      className={`text-xs ${getStatusColor(session)}`}
                    >
                      {session.is_current ? 'Active' : getTimeAgo(session.last_activity)}
                    </Badge>
                  </div>

                  <div className="mt-1 text-sm text-gray-500 space-y-1">
                    <div className="flex items-center space-x-2">
                      <Globe className="w-3 h-3" />
                      <span>{session.ip_address}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-3 h-3" />
                      <span>Last active: {formatDate(session.last_activity)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Activity className="w-3 h-3" />
                      <span>Created: {formatDate(session.created_at)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {!session.is_current && (
                <Button
                  onClick={() => revokeSession(session.id)}
                  variant="ghost"
                  size="sm"
                  disabled={revokingSession === session.id}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  {revokingSession === session.id ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <X className="w-4 h-4" />
                  )}
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {sessions.length === 0 && (
        <div className="text-center py-8">
          <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Sessions</h3>
          <p className="text-gray-600">
            You don't have any active sessions at the moment.
          </p>
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900">Security Tips</h4>
            <ul className="mt-2 text-sm text-blue-800 space-y-1">
              <li>• Regularly review your active sessions</li>
              <li>• Revoke sessions from devices you no longer use</li>
              <li>• Use strong, unique passwords for your account</li>
              <li>• Enable two-factor authentication for extra security</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
