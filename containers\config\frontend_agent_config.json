{"agent_name": "FrontendAgent", "version": "1.0.0", "description": "Specialized agent for frontend development tasks", "framework_preferences": {"primary_framework": "nextjs", "secondary_frameworks": ["react", "vue", "angular"], "typescript_enabled": true, "strict_mode": true, "eslint_enabled": true, "prettier_enabled": true}, "styling_options": {"primary_styling": "tailwind", "secondary_styling": ["css", "scss", "styled-components"], "component_library": "shadcn/ui", "icon_library": "lucide-react", "color_scheme": "light", "dark_mode_support": true, "responsive_design": true, "accessibility": true}, "development_settings": {"hot_reload": true, "source_maps": true, "optimization": true, "bundle_analysis": true, "performance_monitoring": true, "error_boundaries": true}, "component_generation": {"auto_imports": true, "component_structure": "atomic", "naming_convention": "pascal_case", "file_extension": ".tsx", "include_tests": true, "include_stories": false}, "project_structure": {"src_directory": "src", "components_directory": "components", "pages_directory": "pages", "styles_directory": "styles", "utils_directory": "utils", "types_directory": "types", "hooks_directory": "hooks"}, "dependencies": {"core_dependencies": ["react", "react-dom", "next", "typescript"], "styling_dependencies": ["tailwindcss", "autoprefixer", "postcss"], "ui_dependencies": ["@radix-ui/react-slot", "class-variance-authority", "clsx", "tailwind-merge"], "development_dependencies": ["@types/react", "@types/node", "eslint", "prettier"]}, "performance": {"image_optimization": true, "code_splitting": true, "lazy_loading": true, "caching_strategy": "aggressive", "compression": true, "minification": true}, "security": {"content_security_policy": true, "xss_protection": true, "input_validation": true, "output_sanitization": true, "secure_headers": true}, "testing": {"testing_framework": "jest", "testing_library": "@testing-library/react", "coverage_threshold": 80, "e2e_testing": "playwright", "visual_testing": false}, "deployment": {"build_optimization": true, "static_export": true, "cdn_integration": true, "environment_variables": true, "health_checks": true}, "logging": {"level": "INFO", "console_logging": true, "file_logging": false, "error_tracking": true}, "development": {"debug_mode": false, "development_features": true, "test_mode": false}}