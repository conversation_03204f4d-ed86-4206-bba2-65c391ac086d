"""
API Health Check System
Provides comprehensive health monitoring for all API components and dependencies.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import psutil
import requests

from api.config import get_config
from api.models import HealthStatus

logger = logging.getLogger(__name__)


@dataclass
class ServiceHealth:
    """Health status of a service"""

    name: str
    status: str  # "healthy", "degraded", "unhealthy"
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    last_check: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)


class HealthChecker:
    """Comprehensive health checker for API components"""

    def __init__(self):
        self.config = get_config()
        self.start_time = datetime.now()
        self.service_health: Dict[str, ServiceHealth] = {}
        self.health_history: List[Dict[str, Any]] = []

    def get_uptime_seconds(self) -> float:
        """Get service uptime in seconds"""
        return (datetime.now() - self.start_time).total_seconds()

    def check_system_resources(self) -> ServiceHealth:
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory usage
            memory = psutil.virtual_memory()

            # Disk usage
            disk = psutil.disk_usage("/")

            # Determine status based on resource usage
            status = "healthy"
            if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
                status = "degraded"
            if cpu_percent > 95 or memory.percent > 95 or disk.percent > 95:
                status = "unhealthy"

            details = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_mb": memory.available / (1024 * 1024),
                "disk_percent": disk.percent,
                "disk_free_gb": disk.free / (1024 * 1024 * 1024),
            }

            return ServiceHealth(
                name="system_resources", status=status, details=details
            )

        except Exception as e:
            logger.error(f"System resource check failed: {e}")
            return ServiceHealth(
                name="system_resources", status="unhealthy", error_message=str(e)
            )

    def check_database_connection(self) -> ServiceHealth:
        """Check database connectivity"""
        try:
            # Import here to avoid circular imports
            try:
                from db.database_manager import DatabaseManager

                start_time = time.time()
                db_manager = DatabaseManager()

                # Test basic database operations
                db_manager.get_connection()

                response_time = time.time() - start_time
            except ImportError:
                # Mock database check for testing
                response_time = 0.001

            return ServiceHealth(
                name="database",
                status="healthy",
                response_time=response_time,
                details={"connection_pool_size": 10},  # Example detail
            )

        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return ServiceHealth(
                name="database", status="unhealthy", error_message=str(e)
            )

    def check_ai_models(self) -> ServiceHealth:
        """Check AI model availability"""
        try:
            start_time = time.time()

            # Test connection to Ollama
            response = requests.get("http://localhost:11434/api/tags", timeout=5)

            response_time = time.time() - start_time

            if response.status_code == 200:
                models = response.json().get("models", [])
                available_models = [model["name"] for model in models]

                # Check if required models are available
                required_models = self.config.available_models
                missing_models = [
                    model for model in required_models if model not in available_models
                ]

                status = "healthy"
                if missing_models:
                    status = "degraded"

                return ServiceHealth(
                    name="ai_models",
                    status=status,
                    response_time=response_time,
                    details={
                        "available_models": available_models,
                        "required_models": required_models,
                        "missing_models": missing_models,
                    },
                )
            else:
                return ServiceHealth(
                    name="ai_models",
                    status="unhealthy",
                    response_time=response_time,
                    error_message=f"Ollama API returned status {response.status_code}",
                )

        except Exception as e:
            logger.error(f"AI models health check failed: {e}")
            return ServiceHealth(
                name="ai_models", status="unhealthy", error_message=str(e)
            )

    def check_file_system(self) -> ServiceHealth:
        """Check file system health"""
        try:
            # Check critical directories
            critical_dirs = ["sites", "uploads", "backups", "logs"]
            missing_dirs = []

            for dir_name in critical_dirs:
                dir_path = Path(dir_name)
                if not dir_path.exists():
                    missing_dirs.append(dir_name)
                elif not dir_path.is_dir():
                    missing_dirs.append(f"{dir_name} (not a directory)")

            # Check disk space
            disk = psutil.disk_usage("/")
            free_space_gb = disk.free / (1024 * 1024 * 1024)

            status = "healthy"
            if missing_dirs:
                status = "degraded"
            if free_space_gb < 1:  # Less than 1GB free
                status = "unhealthy"

            return ServiceHealth(
                name="file_system",
                status=status,
                details={
                    "missing_directories": missing_dirs,
                    "free_space_gb": free_space_gb,
                    "total_space_gb": disk.total / (1024 * 1024 * 1024),
                },
            )

        except Exception as e:
            logger.error(f"File system health check failed: {e}")
            return ServiceHealth(
                name="file_system", status="unhealthy", error_message=str(e)
            )

    def check_network_connectivity(self) -> ServiceHealth:
        """Check network connectivity"""
        try:
            start_time = time.time()

            # Test external connectivity
            response = requests.get("https://httpbin.org/get", timeout=10)

            response_time = time.time() - start_time

            if response.status_code == 200:
                return ServiceHealth(
                    name="network",
                    status="healthy",
                    response_time=response_time,
                    details={"external_connectivity": True},
                )
            else:
                return ServiceHealth(
                    name="network",
                    status="degraded",
                    response_time=response_time,
                    error_message=f"External connectivity test returned status {response.status_code}",
                )

        except Exception as e:
            logger.error(f"Network health check failed: {e}")
            return ServiceHealth(
                name="network", status="unhealthy", error_message=str(e)
            )

    def check_api_endpoints(self) -> ServiceHealth:
        """Check internal API endpoints"""
        try:
            start_time = time.time()

            # Test internal endpoints
            base_url = f"http://localhost:{self.config.port}"
            endpoints_to_test = ["/health", "/api/v1/chat/models", "/api/sites/list"]

            failed_endpoints = []
            total_response_time = 0

            for endpoint in endpoints_to_test:
                try:
                    endpoint_start = time.time()
                    response = requests.get(f"{base_url}{endpoint}", timeout=5)
                    endpoint_time = time.time() - endpoint_start
                    total_response_time += endpoint_time

                    if response.status_code >= 400:
                        failed_endpoints.append(f"{endpoint} ({response.status_code})")

                except Exception as e:
                    failed_endpoints.append(f"{endpoint} (connection error)")

            avg_response_time = (
                total_response_time / len(endpoints_to_test) if endpoints_to_test else 0
            )

            status = "healthy"
            if failed_endpoints:
                status = (
                    "degraded"
                    if len(failed_endpoints) < len(endpoints_to_test)
                    else "unhealthy"
                )

            return ServiceHealth(
                name="api_endpoints",
                status=status,
                response_time=avg_response_time,
                details={
                    "tested_endpoints": len(endpoints_to_test),
                    "failed_endpoints": failed_endpoints,
                    "success_rate": (len(endpoints_to_test) - len(failed_endpoints))
                    / len(endpoints_to_test),
                },
            )

        except Exception as e:
            logger.error(f"API endpoints health check failed: {e}")
            return ServiceHealth(
                name="api_endpoints", status="unhealthy", error_message=str(e)
            )

    def run_all_checks(self) -> HealthStatus:
        """Run all health checks and return comprehensive status"""
        checks = [
            self.check_system_resources,
            self.check_database_connection,
            self.check_ai_models,
            self.check_file_system,
            self.check_network_connectivity,
            self.check_api_endpoints,
        ]

        services = {}
        overall_status = "healthy"

        for check in checks:
            health = check()
            services[health.name] = health.status

            # Update overall status
            if health.status == "unhealthy":
                overall_status = "unhealthy"
            elif health.status == "degraded" and overall_status == "healthy":
                overall_status = "degraded"

            # Store in history
            self.service_health[health.name] = health

        # Add to history
        health_record = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": overall_status,
            "services": services,
            "uptime_seconds": self.get_uptime_seconds(),
        }
        self.health_history.append(health_record)

        # Keep only last 100 records
        if len(self.health_history) > 100:
            self.health_history = self.health_history[-100:]

        return HealthStatus(
            status=overall_status,
            version=self.config.version if hasattr(self.config, "version") else "1.0.0",
            timestamp=datetime.now(),
            services=services,
            uptime_seconds=self.get_uptime_seconds(),
        )

    def get_health_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get health check history for the specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            record
            for record in self.health_history
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]

    def get_service_details(self, service_name: str) -> Optional[ServiceHealth]:
        """Get detailed health information for a specific service"""
        return self.service_health.get(service_name)


# Global health checker instance
_health_checker: Optional[HealthChecker] = None


def get_health_checker() -> HealthChecker:
    """Get the global health checker instance"""
    global _health_checker
    if _health_checker is None:
        _health_checker = HealthChecker()
    return _health_checker


def check_health() -> HealthStatus:
    """Quick health check function"""
    return get_health_checker().run_all_checks()
