"""
WebSocket manager for real-time communication in dashboard backend.
Handles WebSocket connections, message broadcasting, and connection management.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from uuid import uuid4

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from dashboard.models import WebSocketMessage

# Configure logging
logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections and message broadcasting"""

    def __init__(self):
        self.active_connections: Dict[str, Any] = {}
        self.user_connections: Dict[int, Set[str]] = (
            {}
        )  # user_id -> set of connection_ids
        self.connection_users: Dict[str, Any] = {}  # connection_id -> user_id

    async def connect(self, websocket: WebSocket, user_id: int) -> str:
        """Connect a new WebSocket and associate it with a user"""
        await websocket.accept()

        connection_id = str(uuid4())
        self.active_connections[connection_id] = websocket
        self.connection_users[connection_id] = user_id

        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)

        logger.info(f"WebSocket connected: {connection_id} for user {user_id}")
        return connection_id

    def disconnect(self, connection_id: str):
        """Disconnect a WebSocket connection"""
        if connection_id in self.active_connections:
            user_id = self.connection_users.get(connection_id)
            if user_id and user_id in self.user_connections:
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]

            del self.active_connections[connection_id]
            del self.connection_users[connection_id]

            logger.info(f"WebSocket disconnected: {connection_id}")

    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send a message to a specific connection"""
        if connection_id in self.active_connections:
            try:
                await self.active_connections[connection_id].send_text(
                    json.dumps(message, default=str)
                )
            except Exception as e:
                logger.error(f"Error sending message to {connection_id}: {e}")
                self.disconnect(connection_id)

    async def send_personal_message_to_user(
        self, message: Dict[str, Any], user_id: int
    ):
        """Send a message to all connections of a specific user"""
        if user_id in self.user_connections:
            for connection_id in self.user_connections[user_id].copy():
                await self.send_personal_message(message, connection_id)

    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected clients"""
        disconnected_connections = []

        for connection_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error broadcasting to {connection_id}: {e}")
                disconnected_connections.append(connection_id)

        # Clean up disconnected connections
        for connection_id in disconnected_connections:
            self.disconnect(connection_id)

    async def broadcast_to_users(self, message: Dict[str, Any], user_ids: List[int]):
        """Broadcast a message to specific users"""
        for user_id in user_ids:
            await self.send_personal_message_to_user(message, user_id)


class WebSocketManager:
    """Main WebSocket manager for the dashboard"""

    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.message_handlers: Dict[str, Any] = {}
        self._register_default_handlers()

    def _register_default_handlers(self):
        """Register default message handlers"""
        self.message_handlers.update(
            {
                "ping": self._handle_ping,
                "subscribe": self._handle_subscribe,
                "unsubscribe": self._handle_unsubscribe,
            }
        )

    async def handle_websocket(self, websocket: WebSocket, user_id: int, db: Session):
        """Handle a WebSocket connection"""
        connection_id = await self.connection_manager.connect(websocket, user_id)

        try:
            # Send welcome message
            welcome_message = WebSocketMessage(
                type="welcome",
                data={
                    "connection_id": connection_id,
                    "user_id": user_id,
                    "message": "Connected to AI Coding Agent Dashboard",
                },
            )
            await self.send_personal_message(welcome_message.dict(), connection_id)

            # Handle incoming messages
            while True:
                try:
                    data = await websocket.receive_text()
                    message_data = json.loads(data)

                    await self._handle_message(message_data, connection_id, user_id, db)

                except json.JSONDecodeError:
                    error_message = WebSocketMessage(
                        type="error", data={"message": "Invalid JSON format"}
                    )
                    await self.send_personal_message(
                        error_message.dict(), connection_id
                    )

        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected: {connection_id}")
        except Exception as e:
            logger.error(f"Error handling WebSocket {connection_id}: {e}")
        finally:
            self.connection_manager.disconnect(connection_id)

    async def _handle_message(
        self,
        message_data: Dict[str, Any],
        connection_id: str,
        user_id: int,
        db: Session,
    ):
        """Handle incoming WebSocket messages"""
        message_type = message_data.get("type")

        if not message_type:
            error_message = WebSocketMessage(
                type="error", data={"message": "Message type is required"}
            )
            await self.send_personal_message(error_message.dict(), connection_id)
            return

        # Find and execute handler
        handler = self.message_handlers.get(message_type)
        if handler:
            try:
                await handler(message_data, connection_id, user_id, db)
            except Exception as e:
                logger.error(f"Error in message handler {message_type}: {e}")
                error_message = WebSocketMessage(
                    type="error",
                    data={"message": f"Error processing {message_type}: {str(e)}"},
                )
                await self.send_personal_message(error_message.dict(), connection_id)
        else:
            error_message = WebSocketMessage(
                type="error", data={"message": f"Unknown message type: {message_type}"}
            )
            await self.send_personal_message(error_message.dict(), connection_id)

    async def _handle_ping(
        self,
        message_data: Dict[str, Any],
        connection_id: str,
        user_id: int,
        db: Session,
    ):
        """Handle ping messages"""
        pong_message = WebSocketMessage(
            type="pong", data={"timestamp": datetime.utcnow().isoformat()}
        )
        await self.send_personal_message(pong_message.dict(), connection_id)

    async def _handle_subscribe(
        self,
        message_data: Dict[str, Any],
        connection_id: str,
        user_id: int,
        db: Session,
    ):
        """Handle subscription messages"""
        channel = message_data.get("data", {}).get("channel")
        if channel:
            # Basic implementation - in production this would use a proper pub/sub system
            # For now, implement basic channel subscription logic

            # Initialize channel subscriptions if not exists
            if not hasattr(self.connection_manager, "channel_subscriptions"):
                self.connection_manager.channel_subscriptions = {}

            if not hasattr(self.connection_manager, "user_channels"):
                self.connection_manager.user_channels = {}

            # Add connection to channel
            if channel not in self.connection_manager.channel_subscriptions:
                self.connection_manager.channel_subscriptions[channel] = set()

            self.connection_manager.channel_subscriptions[channel].add(connection_id)

            # Track user's channels
            if user_id not in self.connection_manager.user_channels:
                self.connection_manager.user_channels[user_id] = set()

            self.connection_manager.user_channels[user_id].add(channel)

            logger.info(
                f"User {user_id} subscribed to channel {channel} via connection {connection_id}"
            )

            success_message = WebSocketMessage(
                type="subscribed",
                data={
                    "channel": channel,
                    "message": f"Successfully subscribed to {channel}",
                },
            )
            await self.send_personal_message(success_message.dict(), connection_id)

    async def _handle_unsubscribe(
        self,
        message_data: Dict[str, Any],
        connection_id: str,
        user_id: int,
        db: Session,
    ):
        """Handle unsubscription messages"""
        channel = message_data.get("data", {}).get("channel")
        if channel:
            # Basic implementation - in production this would use a proper pub/sub system
            # For now, implement basic channel unsubscription logic

            # Remove connection from channel
            if hasattr(self.connection_manager, "channel_subscriptions"):
                if channel in self.connection_manager.channel_subscriptions:
                    self.connection_manager.channel_subscriptions[channel].discard(
                        connection_id
                    )

                    # Remove empty channels
                    if not self.connection_manager.channel_subscriptions[channel]:
                        del self.connection_manager.channel_subscriptions[channel]

            # Remove channel from user's channels
            if hasattr(self.connection_manager, "user_channels"):
                if user_id in self.connection_manager.user_channels:
                    self.connection_manager.user_channels[user_id].discard(channel)

                    # Remove empty user entries
                    if not self.connection_manager.user_channels[user_id]:
                        del self.connection_manager.user_channels[user_id]

            logger.info(
                f"User {user_id} unsubscribed from channel {channel} via connection {connection_id}"
            )

            success_message = WebSocketMessage(
                type="unsubscribed",
                data={
                    "channel": channel,
                    "message": f"Successfully unsubscribed from {channel}",
                },
            )
            await self.send_personal_message(success_message.dict(), connection_id)

    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send a personal message to a specific connection"""
        await self.connection_manager.send_personal_message(message, connection_id)

    async def send_personal_message_to_user(
        self, message: Dict[str, Any], user_id: int
    ):
        """Send a personal message to a specific user"""
        await self.connection_manager.send_personal_message_to_user(message, user_id)

    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connections"""
        await self.connection_manager.broadcast(message)

    async def broadcast_to_users(self, message: Dict[str, Any], user_ids: List[int]):
        """Broadcast a message to specific users"""
        await self.connection_manager.broadcast_to_users(message, user_ids)

    def register_handler(self, message_type: str, handler: callable):
        """Register a custom message handler"""
        self.message_handlers[message_type] = handler

    def get_connection_count(self) -> int:
        """Get the number of active connections"""
        return len(self.connection_manager.active_connections)

    def get_user_connection_count(self, user_id: int) -> int:
        """Get the number of connections for a specific user"""
        return len(self.connection_manager.user_connections.get(user_id, set()))


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
