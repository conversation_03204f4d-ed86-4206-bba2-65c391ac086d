"""
Command Line Interface for AI Coding Agent.
"""

import json
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import click

from cli.cleanup_commands import cleanup
from cli.enhanced_site_container_commands import EnhancedSiteContainerCommands
from cli.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationError, error_handler
from cli.state_tracker import state_tracker, track_event
from cli.supabase_commands import register_commands
from config import get_config, update_config
from core.managers.deployment_manager import DeploymentManager
from db import close_db, get_db, get_db_session, init_db
from models import model_manager
from scripts.server_launcher import ServerLauncher

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class State:
    """Global application state"""

    def __init__(self):
        self.config = get_config()
        self._db = None
        self._server = None
        self._deployment_manager = None
        self._initialized = False
        self._tracker = state_tracker

    def initialize(self) -> None:
        """Initialize the application state"""
        if not self._initialized:
            track_event("state", "Initializing application state")
            self._initialized = True

    @property
    def db(self) -> Optional[Any]:
        if self._db is None:
            track_event("database", "Initializing database connection")
            self._db = get_db_session()
        return self._db

    @property
    def server(self) -> Optional[Any]:
        if self._server is None:
            track_event("server", "Initializing server")
            self._server = ServerLauncher()
        return self._server

    @property
    def deployment_manager(self) -> Optional[Any]:
        if self._deployment_manager is None:
            track_event("deployment", "Initializing deployment manager")
            self._deployment_manager = DeploymentManager()
        return self._deployment_manager

    @property
    def tracker(self) -> Any:
        return self._tracker

    def close(self) -> None:
        """Clean up resources"""
        track_event("state", "Cleaning up application state")
        if self._db:
            close_db()
            self._db = None
        if self._server:
            self._server = None
        self._initialized = False


# Global state
state = State()


@click.group()
@click.option("--debug/--no-debug", default=False, help="Enable debug mode")
@click.option("--state-file", type=click.Path(), help="Path to state file")
@click.pass_context
@error_handler
def cli(ctx, debug, state_file) -> None:
    """AI Coding Agent - Command Line Interface"""
    # Set up logging
    log_level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Initialize application state
    ctx.ensure_object(dict)
    if state_file:
        state.tracker.state_file = state_file

    try:
        state.initialize()
        track_event(
            "cli",
            "CLI initialized",
            {"debug": debug, "state_file": state.tracker.state_file},
        )

        # Initialize database
        init_db()
        track_event("database", "Database initialized")

    except Exception as e:
        track_event("error", "Initialization failed", {"error": str(e)})
        logger.error(f"Initialization failed: {str(e)}")
        if debug:
            logger.exception("Debug information:")
        raise CliError(f"Initialization failed: {str(e)}")

    # Store state in context
    ctx.obj["state"] = state


@cli.command()
@click.argument("name")
@click.option("--template", default="default", help="Template to use")
@click.option("--output", type=click.Path(), default=".", help="Output directory")
@click.pass_context
@error_handler
def create(ctx, name: str, template: str, output: str) -> None:
    """Create a new project"""
    track_event(
        "create",
        "Creating new project",
        {"name": name, "template": template, "output": output},
    )

    click.echo(f"Creating new project '{name}' with template '{template}'")

    # Validate inputs
    if not name.strip():
        raise ValidationError("name", "Project name cannot be empty")

    # Check if output directory exists
    output_path = Path(output).absolute()
    if not output_path.exists():
        output_path.mkdir(parents=True)

    # Implementation will be added here
    track_event(
        "create",
        "Project created successfully",
        {"project_path": str(output_path / name)},
    )
    click.echo(f"[SUCCESS] Created project {name} in {output}")
    click.echo(f"Next steps:")
    click.echo(f"  cd {output_path/name}")
    click.echo(f"  ai-agent host")


@cli.command()
@click.argument("path", type=click.Path(exists=True))
@click.option("--port", type=int, default=5000, help="Port to run the server on")
@click.option("--host", default="0.0.0.0", help="Host to bind to")
@click.option("--debug/--no-debug", default=False, help="Enable debug mode")
@click.pass_context
@error_handler
def host(ctx, path: str, port: int, host: str, debug: bool) -> None:
    """Host a project locally"""
    track_event(
        "host",
        "Starting local server",
        {"path": path, "port": port, "host": host, "debug": debug},
    )

    click.echo(f"[STARTING] Starting development server at http://{host}:{port}")
    click.echo(f"[FOLDER] Serving from: {path}")

    try:
        # Implementation will be added
        server = state.server
        # Configure and start server here

        track_event("host", "Server started successfully")
        click.echo("[SUCCESS] Server is running. Press Ctrl+C to stop.")

        # Keep the server running
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        track_event("host", "Server stopped by user")
        click.echo("\n[STOPPED] Server stopped")
    except Exception as e:
        track_event("error", "Server error", {"error": str(e)})
        logger.error(f"Server error: {str(e)}")
        if debug:
            logger.exception("Debug information:")
        raise CliError(f"Failed to start server: {str(e)}")


@cli.command()
@click.argument("path", type=click.Path(exists=True))
@click.option("--env", default="production", help="Deployment environment")
@click.option(
    "--confirm/--no-confirm", default=True, help="Ask for confirmation before deploying"
)
@click.pass_context
@error_handler
def deploy(ctx, path: str, env: str, confirm: bool) -> None:
    """Deploy a project"""
    track_event("deploy", "Starting deployment", {"path": path, "environment": env})

    click.echo(f"[DEPLOYING] Preparing to deploy from: {path}")
    click.echo(f"[ENVIRONMENT] Environment: {env}")

    if confirm and not click.confirm("Are you sure you want to deploy?"):
        track_event("deploy", "Deployment cancelled by user")
        click.echo("[CANCELLED] Deployment cancelled")
        return

    try:
        # Implementation will be added
        deployment = state.deployment_manager.deploy(path, env=env)

        track_event(
            "deploy",
            "Deployment successful",
            {"deployment_id": deployment.id, "url": deployment.url},
        )

        click.echo("[SUCCESS] Deployment successful!")
        if deployment.url:
            click.echo(f"[LIVE] Your site is live at: {deployment.url}")

    except Exception as e:
        track_event("error", "Deployment failed", {"error": str(e)})
        logger.error(f"Deployment failed: {str(e)}")
        if ctx.obj.get("debug"):
            logger.exception("Debug information:")
        raise CliError(f"Deployment failed: {str(e)}")


@cli.command()
@click.argument("name")
@click.option("--template", default="default", help="Template to use")
@click.option("--output", type=click.Path(), default=".", help="Output directory")
@click.option("--port", type=int, default=5000, help="Port to run the server on")
@click.option("--host", default="0.0.0.0", help="Host to bind to")
@click.option("--no-open", is_flag=True, help="Do not open browser automatically")
@click.pass_context
@error_handler
def create_and_host(
    ctx, name: str, template: str, output: str, port: int, host: str, no_open: bool
) -> None:
    """Create and host a project in one command"""
    track_event(
        "create_and_host",
        "Starting create and host operation",
        {
            "name": name,
            "template": template,
            "output": output,
            "port": port,
            "host": host,
        },
    )

    # Create the project
    ctx.invoke(create, name=name, template=template, output=output)

    # Build the full path to the project
    project_path = str(Path(output).absolute() / name)

    # Open browser if not disabled
    if not no_open and host in ["0.0.0.0", "127.0.0.1"]:
        import webbrowser

        webbrowser.open(f"http://{host}:{port}")

    # Start hosting the project
    click.echo("\n[STARTING] Starting development server...")
    ctx.invoke(host, path=project_path, port=port, host=host)

    track_event("create_and_host", "Create and host completed successfully")


@cli.command()
@click.pass_context
@error_handler
def list_templates(ctx) -> List[str]:
    """List available templates"""
    track_event("templates", "Listing available templates")

    # Get templates from configuration or default
    templates = state.config.get("templates", ["default", "react", "vue", "angular"])

    click.echo("[TEMPLATES] Available templates:")
    for i, template in enumerate(templates, 1):
        click.echo(f"  {i}. {template}")

    track_event("templates", f"Listed {len(templates)} templates")
    return templates


@cli.command()
@click.pass_context
@error_handler
def status(ctx) -> Dict[str, str]:
    """Show current system status"""
    track_event("status", "Checking system status")

    status_info = {
        "Database": (
            "[CONNECTED] Connected" if state.db else "[DISCONNECTED] Disconnected"
        ),
        "Server": "[RUNNING] Running" if state._server else "[STOPPED] Stopped",
        "Deployment Manager": (
            "[READY] Ready"
            if state.deployment_manager
            else "[UNAVAILABLE] Not available"
        ),
        "State File": state.tracker.state_file,
        "Last Event": (
            state.tracker.get_events(limit=1)[0]["message"]
            if state.tracker.get_events()
            else "No events yet"
        ),
    }

    click.echo("[STATUS] System Status:")
    for key, value in status_info.items():
        click.echo(f"  {key}: {value}")

    return status_info


def main() -> None:
    """Entry point for the CLI"""
    try:
        # Register Supabase commands
        register_commands(cli)
        # Register cleanup commands
        cli.add_command(cleanup)
        # Register architect commands
        from cli.architect_commands import architect

        cli.add_command(architect)
        # Register bonus features commands
        from cli.bonus_features_commands import bonus

        cli.add_command(bonus)
        cli()
    except Exception as e:
        track_event("error", "Unhandled exception", {"error": str(e)})
        logger.critical(f"Unhandled exception: {str(e)}")
        sys.exit(1)
    finally:
        state.close()
        track_event("shutdown", "CLI shutdown complete")


if __name__ == "__main__":
    main()

__all__ = ["cli", "main", "State"]
