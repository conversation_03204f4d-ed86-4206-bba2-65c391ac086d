"""
SEO Plugin for WebsiteGenerator
Provides SEO optimization features for generated websites.
"""

import json
import re
from pathlib import Path
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from core.plugins.base import PluginBase, PluginConfig, PluginPriority


class SEOPlugin(PluginBase):
    """SEO optimization plugin"""

    def __init__(self, config: PluginConfig):
        super().__init__(config)
        self.seo_settings = config.settings.get("seo", {})
        self.meta_tags = config.settings.get("meta_tags", {})
        self.structured_data = config.settings.get("structured_data", {})
        self.sitemap_enabled = config.settings.get("sitemap_enabled", True)
        self.robots_txt_enabled = config.settings.get("robots_txt_enabled", True)

    async def _initialize_plugin(self) -> None:
        """Initialize SEO plugin"""
        self.logger.info("SEO plugin initialized")

    async def _cleanup_plugin(self) -> None:
        """Cleanup SEO plugin"""
        self.logger.info("SEO plugin cleaned up")

    async def _pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Pre-build SEO optimizations"""
        try:
            # Extract SEO information from site config
            seo_info = self._extract_seo_info(site_config)

            # Validate SEO settings
            validation_result = self._validate_seo_settings(seo_info)

            return {
                "seo_info": seo_info,
                "validation": validation_result,
                "optimizations_applied": [],
            }

        except Exception as e:
            self.logger.error(f"SEO pre_build failed: {e}")
            return {"error": str(e)}

    async def _post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Post-build SEO optimizations"""
        try:
            optimizations = []

            # Add meta tags to HTML files
            meta_result = await self._add_meta_tags(site_dir, site_config)
            if meta_result:
                optimizations.append("meta_tags")

            # Generate sitemap
            if self.sitemap_enabled:
                sitemap_result = await self._generate_sitemap(site_dir, site_config)
                if sitemap_result:
                    optimizations.append("sitemap")

            # Generate robots.txt
            if self.robots_txt_enabled:
                robots_result = await self._generate_robots_txt(site_dir, site_config)
                if robots_result:
                    optimizations.append("robots_txt")

            # Add structured data
            structured_result = await self._add_structured_data(site_dir, site_config)
            if structured_result:
                optimizations.append("structured_data")

            # Optimize images with alt tags
            image_result = await self._optimize_images(site_dir, site_config)
            if image_result:
                optimizations.append("image_optimization")

            return {
                "optimizations_applied": optimizations,
                "seo_score": self._calculate_seo_score(site_dir),
                "recommendations": self._generate_seo_recommendations(site_dir),
            }

        except Exception as e:
            self.logger.error(f"SEO post_build failed: {e}")
            return {"error": str(e)}

    async def _on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Handle SEO-related errors"""
        self.logger.error(f"SEO plugin encountered error: {error}")

    def _extract_seo_info(self, site_config: Dict[str, Any]) -> Dict[str, Any]:
        """Extract SEO information from site configuration"""
        return {
            "title": site_config.get("title", ""),
            "description": site_config.get("description", ""),
            "keywords": site_config.get("keywords", []),
            "author": site_config.get("author", ""),
            "canonical_url": site_config.get("canonical_url", ""),
            "og_image": site_config.get("og_image", ""),
            "twitter_card": site_config.get("twitter_card", "summary"),
            "language": site_config.get("language", "en"),
            "robots": site_config.get("robots", "index, follow"),
        }

    def _validate_seo_settings(self, seo_info: Dict[str, Any]) -> Dict[str, Any]:
        """Validate SEO settings"""
        issues = []
        warnings = []

        # Check title length
        title = seo_info.get("title", "")
        if len(title) < 30:
            issues.append("Title is too short (should be 30-60 characters)")
        elif len(title) > 60:
            warnings.append("Title is too long (should be 30-60 characters)")

        # Check description length
        description = seo_info.get("description", "")
        if len(description) < 120:
            issues.append("Description is too short (should be 120-160 characters)")
        elif len(description) > 160:
            warnings.append("Description is too long (should be 120-160 characters)")

        # Check for required fields
        if not title:
            issues.append("Missing title")
        if not description:
            issues.append("Missing description")

        return {"valid": len(issues) == 0, "issues": issues, "warnings": warnings}

    async def _add_meta_tags(self, site_dir: Path, site_config: Dict[str, Any]) -> bool:
        """Add meta tags to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))
            seo_info = self._extract_seo_info(site_config)

            for html_file in html_files:
                await self._process_html_file(html_file, seo_info)

            self.logger.info(f"Added meta tags to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add meta tags: {e}")
            return False

    async def _process_html_file(
        self, html_file: Path, seo_info: Dict[str, Any]
    ) -> None:
        """Process a single HTML file to add meta tags"""
        try:
            # Read HTML content
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Generate meta tags
            meta_tags = self._generate_meta_tags(seo_info)

            # Insert meta tags into head section
            modified_content = self._insert_meta_tags(content, meta_tags)

            # Write back to file
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to process HTML file {html_file}: {e}")

    def _generate_meta_tags(self, seo_info: Dict[str, Any]) -> str:
        """Generate meta tags HTML"""
        meta_tags = []

        # Basic meta tags
        if seo_info.get("title"):
            meta_tags.append(f'<title>{seo_info["title"]}</title>')

        if seo_info.get("description"):
            meta_tags.append(
                f'<meta name="description" content="{seo_info["description"]}">'
            )

        if seo_info.get("keywords"):
            keywords = ", ".join(seo_info["keywords"])
            meta_tags.append(f'<meta name="keywords" content="{keywords}">')

        if seo_info.get("author"):
            meta_tags.append(f'<meta name="author" content="{seo_info["author"]}">')

        if seo_info.get("robots"):
            meta_tags.append(f'<meta name="robots" content="{seo_info["robots"]}">')

        if seo_info.get("language"):
            meta_tags.append(f'<meta name="language" content="{seo_info["language"]}">')

        # Open Graph tags
        if seo_info.get("title"):
            meta_tags.append(
                f'<meta property="og:title" content="{seo_info["title"]}">'
            )

        if seo_info.get("description"):
            meta_tags.append(
                f'<meta property="og:description" content="{seo_info["description"]}">'
            )

        if seo_info.get("og_image"):
            meta_tags.append(
                f'<meta property="og:image" content="{seo_info["og_image"]}">'
            )

        # Twitter Card tags
        meta_tags.append(
            f'<meta name="twitter:card" content="{seo_info.get("twitter_card", "summary")}">'
        )

        if seo_info.get("title"):
            meta_tags.append(
                f'<meta name="twitter:title" content="{seo_info["title"]}">'
            )

        if seo_info.get("description"):
            meta_tags.append(
                f'<meta name="twitter:description" content="{seo_info["description"]}">'
            )

        return "\n    ".join(meta_tags)

    def _insert_meta_tags(self, content: str, meta_tags: str) -> str:
        """Insert meta tags into HTML head section"""
        # Find head tag
        head_pattern = r"(<head[^>]*>)"
        match = re.search(head_pattern, content, re.IGNORECASE)

        if match:
            head_start = match.end()
            return (
                content[:head_start]
                + "\n    "
                + meta_tags
                + "\n"
                + content[head_start:]
            )
        else:
            # If no head tag, add one
            body_pattern = r"(<body[^>]*>)"
            match = re.search(body_pattern, content, re.IGNORECASE)

            if match:
                body_start = match.start()
                head_section = f"<head>\n    {meta_tags}\n</head>\n"
                return content[:body_start] + head_section + content[body_start:]
            else:
                # If no body tag either, add both
                return f"<head>\n    {meta_tags}\n</head>\n<body>\n{content}\n</body>"

    async def _generate_sitemap(
        self, site_dir: Path, site_config: Dict[str, Any]
    ) -> bool:
        """Generate sitemap.xml"""
        try:
            # Find all HTML files
            html_files = list(site_dir.rglob("*.html"))

            # Generate sitemap content
            sitemap_content = self._generate_sitemap_xml(html_files, site_config)

            # Write sitemap
            sitemap_file = site_dir / "sitemap.xml"
            with open(sitemap_file, "w", encoding="utf-8") as f:
                f.write(sitemap_content)

            self.logger.info(f"Generated sitemap with {len(html_files)} URLs")
            return True

        except Exception as e:
            self.logger.error(f"Failed to generate sitemap: {e}")
            return False

    def _generate_sitemap_xml(
        self, html_files: List[Path], site_config: Dict[str, Any]
    ) -> str:
        """Generate sitemap XML content"""
        base_url = site_config.get("base_url", "https://example.com")

        xml_lines = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">',
        ]

        for html_file in html_files:
            # Convert file path to URL
            relative_path = html_file.relative_to(
                html_file.parents[len(html_file.parts) - len(html_file.parents)]
            )
            url = f"{base_url.rstrip('/')}/{str(relative_path).replace('\\', '/')}"

            xml_lines.extend(
                [
                    "  <url>",
                    f"    <loc>{url}</loc>",
                    "    <lastmod>2024-01-01</lastmod>",
                    "    <changefreq>weekly</changefreq>",
                    "    <priority>0.8</priority>",
                    "  </url>",
                ]
            )

        xml_lines.append("</urlset>")
        return "\n".join(xml_lines)

    async def _generate_robots_txt(
        self, site_dir: Path, site_config: Dict[str, Any]
    ) -> bool:
        """Generate robots.txt"""
        try:
            base_url = site_config.get("base_url", "https://example.com")

            robots_content = f"""User-agent: *
Allow: /

Sitemap: {base_url.rstrip('/')}/sitemap.xml
"""

            robots_file = site_dir / "robots.txt"
            with open(robots_file, "w", encoding="utf-8") as f:
                f.write(robots_content)

            self.logger.info("Generated robots.txt")
            return True

        except Exception as e:
            self.logger.error(f"Failed to generate robots.txt: {e}")
            return False

    async def _add_structured_data(
        self, site_dir: Path, site_config: Dict[str, Any]
    ) -> bool:
        """Add structured data to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))
            structured_data = self._generate_structured_data(site_config)

            for html_file in html_files:
                await self._add_structured_data_to_file(html_file, structured_data)

            self.logger.info(f"Added structured data to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add structured data: {e}")
            return False

    def _generate_structured_data(self, site_config: Dict[str, Any]) -> str:
        """Generate structured data JSON-LD"""
        seo_info = self._extract_seo_info(site_config)

        structured_data = {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": seo_info.get("title", ""),
            "description": seo_info.get("description", ""),
            "url": seo_info.get("canonical_url", ""),
            "author": {"@type": "Person", "name": seo_info.get("author", "")},
        }

        return json.dumps(structured_data, indent=2)

    async def _add_structured_data_to_file(
        self, html_file: Path, structured_data: str
    ) -> None:
        """Add structured data to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add structured data script tag
            script_tag = (
                f'<script type="application/ld+json">\n{structured_data}\n</script>'
            )

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + "    " + script_tag + "\n" + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add structured data to {html_file}: {e}")

    async def _optimize_images(
        self, site_dir: Path, site_config: Dict[str, Any]
    ) -> bool:
        """Optimize images with alt tags"""
        try:
            # This is a placeholder for image optimization
            # In a real implementation, this would:
            # 1. Find all img tags without alt attributes
            # 2. Generate descriptive alt text
            # 3. Add alt attributes to img tags

            self.logger.info("Image optimization completed")
            return True

        except Exception as e:
            self.logger.error(f"Failed to optimize images: {e}")
            return False

    def _calculate_seo_score(self, site_dir: Path) -> int:
        """Calculate SEO score for the website"""
        score = 0

        # Check for meta tags
        html_files = list(site_dir.rglob("*.html"))
        for html_file in html_files:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

                if '<meta name="description"' in content:
                    score += 10
                if '<meta name="keywords"' in content:
                    score += 5
                if '<meta property="og:' in content:
                    score += 10
                if '<meta name="twitter:' in content:
                    score += 5
                if "application/ld+json" in content:
                    score += 15

        # Check for sitemap
        if (site_dir / "sitemap.xml").exists():
            score += 10

        # Check for robots.txt
        if (site_dir / "robots.txt").exists():
            score += 5

        return min(100, score)

    def _generate_seo_recommendations(self, site_dir: Path) -> List[str]:
        """Generate SEO recommendations"""
        recommendations = []

        # Check for missing files
        if not (site_dir / "sitemap.xml").exists():
            recommendations.append("Add a sitemap.xml file")

        if not (site_dir / "robots.txt").exists():
            recommendations.append("Add a robots.txt file")

        # Check HTML files for common issues
        html_files = list(site_dir.rglob("*.html"))
        for html_file in html_files:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

                if "<title>" not in content:
                    recommendations.append(f"Add title tag to {html_file.name}")

                if '<meta name="description"' not in content:
                    recommendations.append(f"Add meta description to {html_file.name}")

        return recommendations


# Plugin configuration
seo_plugin_config = PluginConfig(
    name="seo_optimizer",
    version="1.0.0",
    description="SEO optimization plugin for websites",
    author="WebsiteGenerator Team",
    priority=PluginPriority.HIGH,
    enabled=True,
    settings={
        "seo": {
            "enabled": True,
            "auto_generate_meta": True,
            "add_structured_data": True,
        },
        "meta_tags": {"default_robots": "index, follow", "default_language": "en"},
        "structured_data": {
            "add_website_schema": True,
            "add_organization_schema": False,
        },
        "sitemap_enabled": True,
        "robots_txt_enabled": True,
    },
)
