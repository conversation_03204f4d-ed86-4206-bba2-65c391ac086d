"""
Secure configuration management for AI Coding Agent.
Handles secrets, environment variables, and secure defaults.
"""

import os
import secrets
from pathlib import Path
from typing import Any, Dict, Optional


class SecureConfig:
    """Secure configuration manager with environment variable support."""

    def __init__(self, validate_required: bool = False):
        """Initialize secure config.

        Args:
            validate_required: If True, validate required environment variables.
                              If False, skip validation (useful for development).
        """
        if validate_required:
            self._validate_required_env_vars()

    def _validate_required_env_vars(self) -> None:
        """Validate that all required environment variables are set."""
        required_vars = ["SECRET_KEY", "DATABASE_URL", "JWT_SECRET_KEY"]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}"
            )

    @property
    def secret_key(self) -> str:
        """Get the secret key from environment."""
        return os.getenv("SECRET_KEY", self._generate_secure_key())

    @property
    def jwt_secret_key(self) -> str:
        """Get the JWT secret key from environment."""
        return os.getenv("JWT_SECRET_KEY", self._generate_secure_key())

    @property
    def database_url(self) -> str:
        """Get the database URL from environment."""
        return os.getenv("DATABASE_URL", "sqlite:///data/ai_coding_agent.db")

    @property
    def oauth_client_id(self) -> Optional[str]:
        """Get OAuth client ID from environment."""
        return os.getenv("OAUTH_CLIENT_ID")

    @property
    def oauth_client_secret(self) -> Optional[str]:
        """Get OAuth client secret from environment."""
        return os.getenv("OAUTH_CLIENT_SECRET")

    @property
    def encryption_key(self) -> bytes:
        """Get encryption key from environment or generate one."""
        key = os.getenv("ENCRYPTION_KEY")
        if key:
            return key.encode()
        return self._generate_encryption_key()

    def _generate_secure_key(self) -> str:
        """Generate a cryptographically secure random key."""
        return secrets.token_urlsafe(32)

    def _generate_encryption_key(self) -> bytes:
        """Generate a 32-byte encryption key."""
        return secrets.token_bytes(32)

    def get_security_headers(self) -> Dict[str, str]:
        """Get recommended security headers."""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }

    def is_production(self) -> bool:
        """Check if running in production mode."""
        return os.getenv("ENVIRONMENT", "development").lower() == "production"

    def get_cors_origins(self) -> list:
        """Get allowed CORS origins from environment."""
        origins = os.getenv("CORS_ORIGINS", "http://localhost:3000")
        return [origin.strip() for origin in origins.split(",")]


# Global secure config instance
secure_config = SecureConfig(validate_required=False)
