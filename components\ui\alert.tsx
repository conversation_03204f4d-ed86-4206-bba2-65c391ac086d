import React from 'react';
import { AlertCircle, CheckCircle, Info, XCircle } from 'lucide-react';

interface AlertProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  className?: string;
}

interface AlertDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

const getVariantClasses = (variant: string) => {
  switch (variant) {
    case 'success':
      return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200';
    case 'warning':
      return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';
    case 'error':
      return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';
    case 'info':
      return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';
    default:
      return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800 text-gray-800 dark:text-gray-200';
  }
};

const getIcon = (variant: string) => {
  switch (variant) {
    case 'success':
      return <CheckCircle className="w-4 h-4" />;
    case 'warning':
      return <AlertCircle className="w-4 h-4" />;
    case 'error':
      return <XCircle className="w-4 h-4" />;
    case 'info':
      return <Info className="w-4 h-4" />;
    default:
      return <Info className="w-4 h-4" />;
  }
};

export const Alert: React.FC<AlertProps> = ({
  children,
  variant = 'default',
  className = ''
}) => (
  <div className={`flex items-start gap-3 p-4 border rounded-lg ${getVariantClasses(variant)} ${className}`}>
    {getIcon(variant)}
    <div className="flex-1">
      {children}
    </div>
  </div>
);

export const AlertDescription: React.FC<AlertDescriptionProps> = ({
  children,
  className = ''
}) => (
  <div className={`text-sm ${className}`}>
    {children}
  </div>
);
