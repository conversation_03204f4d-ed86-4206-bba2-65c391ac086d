#!/usr/bin/env python3
"""
Update Agent - Automated Dependency Management
Handles pip-audit, npm audit, dependency updates, testing, and Git integration.
"""

import asyncio
import hashlib
import json
import logging
import os
import re
import shutil
import subprocess
import sys
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

# Configure logging
logger = logging.getLogger(__name__)


class UpdateError(Exception):
    """Custom exception for update operations"""

    def __init__(
        self,
        message: str,
        error_code: str = "UPDATE_ERROR",
        details: Optional[Dict] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class SecurityAuditor:
    """Handles security auditing for Python and Node.js dependencies"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.requirements_file = self.project_root / "requirements.txt"
        self.package_json = self.project_root / "package.json"
        self.audit_results = {}

    async def run_pip_audit(self) -> Dict[str, Any]:
        """Run pip-audit to check for Python dependency vulnerabilities"""
        try:
            logger.info("Running pip-audit for Python dependencies...")

            # Run pip-audit
            result = subprocess.run(
                ["pip-audit", "--format", "json"],
                capture_output=True,
                text=True,
                cwd=str(self.project_root),  # Convert Path to str
                timeout=300,  # 5 minute timeout
            )

            if result.returncode == 0:
                # No vulnerabilities found
                self.audit_results["pip"] = {
                    "status": "clean",
                    "vulnerabilities": [],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
                logger.info("✅ pip-audit: No vulnerabilities found")
            else:
                # Vulnerabilities found
                try:
                    vulnerabilities = json.loads(result.stdout)
                    self.audit_results["pip"] = {
                        "status": "vulnerabilities_found",
                        "vulnerabilities": vulnerabilities,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }
                    logger.warning(
                        f"⚠️ pip-audit: Found {len(vulnerabilities)} vulnerabilities"
                    )
                except json.JSONDecodeError:
                    # Fallback parsing for non-JSON output
                    self.audit_results["pip"] = {
                        "status": "vulnerabilities_found",
                        "vulnerabilities": [{"raw_output": result.stdout}],
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }
                    logger.warning("⚠️ pip-audit: Found vulnerabilities (raw output)")

            return self.audit_results["pip"]

        except subprocess.TimeoutExpired:
            error_msg = "pip-audit timed out after 5 minutes"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PIP_AUDIT_TIMEOUT")
        except FileNotFoundError:
            error_msg = "pip-audit not found. Install with: pip install pip-audit"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PIP_AUDIT_NOT_FOUND")
        except Exception as e:
            error_msg = f"pip-audit failed: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PIP_AUDIT_FAILED")

    async def run_npm_audit(self) -> Dict[str, Any]:
        """Run npm audit to check for Node.js dependency vulnerabilities"""
        try:
            logger.info("Running npm audit for Node.js dependencies...")

            # Run npm audit
            result = subprocess.run(
                ["npm", "audit", "--json"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=300,  # 5 minute timeout
            )

            if result.returncode == 0:
                # No vulnerabilities found
                self.audit_results["npm"] = {
                    "status": "clean",
                    "vulnerabilities": {},
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
                logger.info("✅ npm audit: No vulnerabilities found")
            else:
                # Vulnerabilities found
                try:
                    audit_data = json.loads(result.stdout)
                    self.audit_results["npm"] = {
                        "status": "vulnerabilities_found",
                        "vulnerabilities": audit_data,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }

                    # Count vulnerabilities by severity
                    vuln_count = 0
                    if "vulnerabilities" in audit_data:
                        vuln_count = len(audit_data["vulnerabilities"])

                    logger.warning(f"⚠️ npm audit: Found {vuln_count} vulnerabilities")
                except json.JSONDecodeError:
                    # Fallback parsing for non-JSON output
                    self.audit_results["npm"] = {
                        "status": "vulnerabilities_found",
                        "vulnerabilities": {"raw_output": result.stdout},
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }
                    logger.warning("⚠️ npm audit: Found vulnerabilities (raw output)")

            return self.audit_results["npm"]

        except subprocess.TimeoutExpired:
            error_msg = "npm audit timed out after 5 minutes"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NPM_AUDIT_TIMEOUT")
        except FileNotFoundError:
            error_msg = "npm not found. Please install Node.js"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NPM_NOT_FOUND")
        except Exception as e:
            error_msg = f"npm audit failed: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NPM_AUDIT_FAILED")

    async def run_full_security_audit(self) -> Dict[str, Any]:
        """Run both pip-audit and npm audit"""
        logger.info("🔒 Starting comprehensive security audit...")

        results = {
            "pip": await self.run_pip_audit(),
            "npm": await self.run_npm_audit(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Determine overall status
        pip_has_vulns = results["pip"]["status"] == "vulnerabilities_found"
        npm_has_vulns = results["npm"]["status"] == "vulnerabilities_found"

        if pip_has_vulns or npm_has_vulns:
            results["overall_status"] = "vulnerabilities_found"
            logger.warning("⚠️ Security audit: Vulnerabilities found")
        else:
            results["overall_status"] = "clean"
            logger.info("✅ Security audit: All dependencies are secure")

        return results


class DependencyUpdater:
    """Handles dependency updates for Python and Node.js packages"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.requirements_file = self.project_root / "requirements.txt"
        self.package_json = self.project_root / "package.json"
        self.update_results = {}

    def _parse_requirements_txt(self) -> Dict[str, str]:
        """Parse requirements.txt to get current package versions"""
        packages = {}
        if self.requirements_file.exists():
            with open(self.requirements_file, "r") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#"):
                        # Handle different requirement formats
                        if "==" in line:
                            package, version = line.split("==", 1)
                            packages[package] = version
                        elif ">=" in line:
                            package, version = line.split(">=", 1)
                            packages[package] = f">={version}"
                        elif "<=" in line:
                            package, version = line.split("<=", 1)
                            packages[package] = f"<={version}"
                        else:
                            packages[line] = "latest"
        return packages

    def _parse_package_json(self) -> Dict[str, str]:
        """Parse package.json to get current package versions"""
        packages = {}
        if self.package_json.exists():
            with open(self.package_json, "r") as f:
                data = json.load(f)
                # Combine dependencies and devDependencies
                deps = data.get("dependencies", {})
                dev_deps = data.get("devDependencies", {})
                packages.update(deps)
                packages.update(dev_deps)
        return packages

    async def check_python_updates(self) -> Dict[str, Any]:
        """Check for available Python package updates"""
        try:
            logger.info("Checking for Python package updates...")

            # Get current packages
            current_packages = self._parse_requirements_txt()

            # Check for updates using pip
            updates_available = []

            for package, current_version in current_packages.items():
                if current_version in ["latest", ">=*", "<=*"]:
                    continue  # Skip packages with flexible versioning

                try:
                    # Check latest version
                    result = subprocess.run(
                        ["pip", "index", "versions", package, "--json"],
                        capture_output=True,
                        text=True,
                        timeout=30,
                    )

                    if result.returncode == 0:
                        try:
                            data = json.loads(result.stdout)
                            if "versions" in data and data["versions"]:
                                # First is latest
                                latest_version = data["versions"][0]
                                if latest_version != current_version:
                                    updates_available.append(
                                        {
                                            "package": package,
                                            "current": current_version,
                                            "latest": latest_version,
                                            "update_type": self._determine_update_type(
                                                current_version, latest_version
                                            ),
                                        }
                                    )
                        except json.JSONDecodeError:
                            continue
                except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                    continue

            self.update_results["python"] = {
                "status": "checked",
                "current_packages": current_packages,
                "updates_available": updates_available,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            logger.info(
                f"📦 Python: Found {len(updates_available)} packages with updates available"
            )
            return self.update_results["python"]

        except Exception as e:
            error_msg = f"Failed to check Python updates: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PYTHON_UPDATE_CHECK_FAILED")

    async def check_node_updates(self) -> Dict[str, Any]:
        """Check for available Node.js package updates"""
        try:
            logger.info("Checking for Node.js package updates...")

            # Get current packages
            current_packages = self._parse_package_json()

            # Check for updates using npm outdated
            result = subprocess.run(
                ["npm", "outdated", "--json"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=120,
            )

            updates_available = []

            if (
                result.returncode == 1
            ):  # npm outdated returns 1 when updates are available
                try:
                    outdated_data = json.loads(result.stdout)
                    for package, info in outdated_data.items():
                        if (
                            isinstance(info, dict)
                            and "current" in info
                            and "latest" in info
                        ):
                            updates_available.append(
                                {
                                    "package": package,
                                    "current": info["current"],
                                    "latest": info["latest"],
                                    "wanted": info.get("wanted", info["latest"]),
                                    "update_type": self._determine_update_type(
                                        info["current"], info["latest"]
                                    ),
                                }
                            )
                except json.JSONDecodeError:
                    logger.warning("Could not parse npm outdated output")

            self.update_results["node"] = {
                "status": "checked",
                "current_packages": current_packages,
                "updates_available": updates_available,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            logger.info(
                f"📦 Node.js: Found {len(updates_available)} packages with updates available"
            )
            return self.update_results["node"]

        except subprocess.TimeoutExpired:
            error_msg = "npm outdated timed out"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NPM_OUTDATED_TIMEOUT")
        except Exception as e:
            error_msg = f"Failed to check Node.js updates: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NODE_UPDATE_CHECK_FAILED")

    def _determine_update_type(self, current: str, latest: str) -> str:
        """Determine if update is patch, minor, or major"""
        try:
            # Simple version comparison (basic implementation)
            current_parts = current.split(".")
            latest_parts = latest.split(".")

            if len(current_parts) >= 3 and len(latest_parts) >= 3:
                if current_parts[0] != latest_parts[0]:
                    return "major"
                elif current_parts[1] != latest_parts[1]:
                    return "minor"
                else:
                    return "patch"
            else:
                return "unknown"
        except:
            return "unknown"

    async def update_python_packages(
        self, packages: Optional[List[str]] = None, update_type: str = "patch"
    ) -> Dict[str, Any]:
        """Update Python packages"""
        try:
            logger.info(f"Updating Python packages (type: {update_type})...")

            # Create backup of requirements.txt
            backup_file = self.requirements_file.with_suffix(".txt.backup")
            shutil.copy2(self.requirements_file, backup_file)

            # Update packages
            updated_packages = []
            failed_packages = []

            current_packages = self._parse_requirements_txt()

            for package, current_version in current_packages.items():
                if packages and package not in packages:
                    continue

                try:
                    # Update package
                    result = subprocess.run(
                        ["pip", "install", "--upgrade", package],
                        capture_output=True,
                        text=True,
                        timeout=60,
                    )

                    if result.returncode == 0:
                        # Get new version
                        version_result = subprocess.run(
                            ["pip", "show", package],
                            capture_output=True,
                            text=True,
                            timeout=30,
                        )

                        if version_result.returncode == 0:
                            # Parse version from pip show output
                            for line in version_result.stdout.split("\n"):
                                if line.startswith("Version:"):
                                    new_version = line.split(":", 1)[1].strip()
                                    updated_packages.append(
                                        {
                                            "package": package,
                                            "old_version": current_version,
                                            "new_version": new_version,
                                        }
                                    )
                                    break
                    else:
                        failed_packages.append(
                            {"package": package, "error": result.stderr}
                        )

                except subprocess.TimeoutExpired:
                    failed_packages.append(
                        {"package": package, "error": "Update timed out"}
                    )
                except Exception as e:
                    failed_packages.append({"package": package, "error": str(e)})

            # Update requirements.txt with new versions
            await self._update_requirements_txt()

            return {
                "status": "completed",
                "updated_packages": updated_packages,
                "failed_packages": failed_packages,
                "backup_file": str(backup_file),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            error_msg = f"Failed to update Python packages: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PYTHON_UPDATE_FAILED")

    async def update_node_packages(
        self, packages: Optional[List[str]] = None, update_type: str = "patch"
    ) -> Dict[str, Any]:
        """Update Node.js packages"""
        try:
            logger.info(f"Updating Node.js packages (type: {update_type})...")

            # Create backup of package.json
            backup_file = self.package_json.with_suffix(".json.backup")
            shutil.copy2(self.package_json, backup_file)

            # Update packages
            if packages:
                # Update specific packages
                for package in packages:
                    subprocess.run(
                        ["npm", "update", package], cwd=self.project_root, timeout=60
                    )
            else:
                # Update all packages
                subprocess.run(["npm", "update"], cwd=self.project_root, timeout=120)

            return {
                "status": "completed",
                "backup_file": str(backup_file),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except subprocess.TimeoutExpired:
            error_msg = "npm update timed out"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NPM_UPDATE_TIMEOUT")
        except Exception as e:
            error_msg = f"Failed to update Node.js packages: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NODE_UPDATE_FAILED")

    async def _update_requirements_txt(self):
        """Update requirements.txt with current package versions"""
        try:
            # Generate new requirements.txt
            result = subprocess.run(
                ["pip", "freeze"], capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                with open(self.requirements_file, "w") as f:
                    f.write(result.stdout)
                logger.info("Updated requirements.txt with current versions")
        except Exception as e:
            logger.warning(f"Failed to update requirements.txt: {e}")


class TestRunner:
    """Handles running the full test suite after updates"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)

    async def run_python_tests(self) -> Dict[str, Any]:
        """Run Python test suite"""
        try:
            logger.info("Running Python test suite...")

            result = subprocess.run(
                ["python", "-m", "pytest", "tests/", "-v", "--tb=short"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=300,  # 5 minute timeout
            )

            # Parse test results
            test_output = result.stdout
            test_errors = result.stderr

            # Extract test summary
            passed = 0
            failed = 0
            errors = 0

            for line in test_output.split("\n"):
                if "passed" in line and "failed" in line:
                    # Parse pytest summary line
                    parts = line.split()
                    for part in parts:
                        if part.endswith("passed"):
                            passed = int(part.split()[0])
                        elif part.endswith("failed"):
                            failed = int(part.split()[0])
                        elif part.endswith("error"):
                            errors = int(part.split()[0])

            return {
                "status": "completed",
                "return_code": result.returncode,
                "passed": passed,
                "failed": failed,
                "errors": errors,
                "output": test_output,
                "errors_output": test_errors,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except subprocess.TimeoutExpired:
            error_msg = "Python tests timed out"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PYTHON_TESTS_TIMEOUT")
        except Exception as e:
            error_msg = f"Python tests failed: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "PYTHON_TESTS_FAILED")

    async def run_node_tests(self) -> Dict[str, Any]:
        """Run Node.js test suite"""
        try:
            logger.info("Running Node.js test suite...")

            result = subprocess.run(
                ["npm", "test"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=300,  # 5 minute timeout
            )

            return {
                "status": "completed",
                "return_code": result.returncode,
                "output": result.stdout,
                "errors_output": result.stderr,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except subprocess.TimeoutExpired:
            error_msg = "Node.js tests timed out"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NODE_TESTS_TIMEOUT")
        except Exception as e:
            error_msg = f"Node.js tests failed: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "NODE_TESTS_FAILED")

    async def run_full_test_suite(self) -> Dict[str, Any]:
        """Run both Python and Node.js test suites"""
        logger.info("🧪 Running full test suite...")

        results = {
            "python": await self.run_python_tests(),
            "node": await self.run_node_tests(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Determine overall test status
        python_success = results["python"]["return_code"] == 0
        node_success = results["node"]["return_code"] == 0

        if python_success and node_success:
            results["overall_status"] = "passed"
            logger.info("✅ All tests passed")
        else:
            results["overall_status"] = "failed"
            logger.error("❌ Some tests failed")

        return results


class GitManager:
    """Handles Git operations for committing updates"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)

    def check_git_status(self) -> Dict[str, Any]:
        """Check Git repository status"""
        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            if result.returncode == 0:
                changes = (
                    result.stdout.strip().split("\n") if result.stdout.strip() else []
                )
                return {
                    "status": "clean" if not changes else "dirty",
                    "changes": changes,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                return {
                    "status": "error",
                    "error": result.stderr,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    def commit_changes(
        self, message: str = "chore: bump dependencies"
    ) -> Dict[str, Any]:
        """Commit changes to Git"""
        try:
            logger.info(f"Committing changes: {message}")

            # Stage all changes
            stage_result = subprocess.run(
                ["git", "add", "."],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            if stage_result.returncode != 0:
                raise UpdateError(
                    f"Failed to stage changes: {stage_result.stderr}",
                    "GIT_STAGE_FAILED",
                )

            # Commit changes
            commit_result = subprocess.run(
                ["git", "commit", "-m", message],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            if commit_result.returncode == 0:
                # Get commit hash
                hash_result = subprocess.run(
                    ["git", "rev-parse", "HEAD"],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root,
                )

                commit_hash = (
                    hash_result.stdout.strip()
                    if hash_result.returncode == 0
                    else "unknown"
                )

                return {
                    "status": "committed",
                    "commit_hash": commit_hash,
                    "message": message,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                raise UpdateError(
                    f"Failed to commit changes: {commit_result.stderr}",
                    "GIT_COMMIT_FAILED",
                )

        except Exception as e:
            error_msg = f"Git commit failed: {str(e)}"
            logger.error(error_msg)
            raise UpdateError(error_msg, "GIT_COMMIT_FAILED")


class UpdateAgent:
    """Main update agent that orchestrates the entire update process"""

    def __init__(
        self, project_root: str = ".", config: Optional[Dict[str, Any]] = None
    ):
        self.project_root = Path(project_root)
        self.config = config or {}

        # Initialize components
        self.security_auditor = SecurityAuditor(project_root)
        self.dependency_updater = DependencyUpdater(project_root)
        self.test_runner = TestRunner(project_root)
        self.git_manager = GitManager(project_root)

        # Results tracking
        self.update_session = {
            "session_id": self._generate_session_id(),
            "start_time": datetime.now(timezone.utc).isoformat(),
            "results": {},
        }

    def _generate_session_id(self) -> str:
        """Generate a unique session ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = hashlib.md5(f"{timestamp}{os.getpid()}".encode()).hexdigest()[
            :8
        ]
        return f"update_{timestamp}_{random_suffix}"

    async def run_security_audit(self) -> Dict[str, Any]:
        """Run comprehensive security audit"""
        logger.info("🔒 Starting security audit...")

        try:
            audit_results = await self.security_auditor.run_full_security_audit()
            self.update_session["results"]["security_audit"] = audit_results

            return audit_results
        except Exception as e:
            logger.error(f"Security audit failed: {e}")
            raise

    async def check_for_updates(self) -> Dict[str, Any]:
        """Check for available dependency updates"""
        logger.info("📦 Checking for dependency updates...")

        try:
            python_updates = await self.dependency_updater.check_python_updates()
            node_updates = await self.dependency_updater.check_node_updates()

            update_check_results = {
                "python": python_updates,
                "node": node_updates,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            self.update_session["results"]["update_check"] = update_check_results

            return update_check_results
        except Exception as e:
            logger.error(f"Update check failed: {e}")
            raise

    async def perform_updates(
        self, update_type: str = "patch", packages: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Perform dependency updates"""
        logger.info(f"🔄 Performing dependency updates (type: {update_type})...")

        try:
            python_update = await self.dependency_updater.update_python_packages(
                packages, update_type
            )
            node_update = await self.dependency_updater.update_node_packages(
                packages, update_type
            )

            update_results = {
                "python": python_update,
                "node": node_update,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            self.update_session["results"]["updates"] = update_results

            return update_results
        except Exception as e:
            logger.error(f"Updates failed: {e}")
            raise

    async def run_tests(self) -> Dict[str, Any]:
        """Run full test suite"""
        logger.info("🧪 Running test suite...")

        try:
            test_results = await self.test_runner.run_full_test_suite()
            self.update_session["results"]["tests"] = test_results

            return test_results
        except Exception as e:
            logger.error(f"Tests failed: {e}")
            raise

    async def commit_changes(self, message: Optional[str] = None) -> Dict[str, Any]:
        """Commit changes to Git

        Args:
            message: Optional commit message. If None, generates a default message.

        Returns:
            Dictionary with commit results
        """
        logger.info("📝 Committing changes to Git...")

        try:
            # Check if there are changes to commit
            git_status = self.git_manager.check_git_status()

            if git_status["status"] == "clean":
                return {
                    "status": "no_changes",
                    "message": "No changes to commit",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Generate commit message
            commit_message = message or ""
            if not commit_message.strip():
                update_count = 0
                if "updates" in self.update_session["results"]:
                    python_updates = len(
                        self.update_session["results"]["updates"]["python"].get(
                            "updated_packages", []
                        )
                    )
                    update_count += python_updates

                commit_message = (
                    f"chore: bump dependencies ({update_count} packages updated)"
                )

            # Ensure we have a non-empty string
            if not commit_message.strip():
                commit_message = "chore: automated dependency updates"

            commit_result = self.git_manager.commit_changes(commit_message.strip())
            self.update_session["results"]["git_commit"] = commit_result

            return commit_result
        except Exception as e:
            logger.error(f"Git commit failed: {e}")
            raise

    async def run_full_update_cycle(
        self, update_type: str = "patch", auto_commit: bool = True
    ) -> Dict[str, Any]:
        """Run the complete update cycle"""
        logger.info("🚀 Starting full update cycle...")

        try:
            # Step 1: Security audit
            security_results = await self.run_security_audit()

            # Step 2: Check for updates
            update_check = await self.check_for_updates()

            # Step 3: Perform updates (if any are available)
            python_updates = len(update_check["python"]["updates_available"])
            node_updates = len(update_check["node"]["updates_available"])

            if python_updates == 0 and node_updates == 0:
                logger.info("✅ No updates available")
                self.update_session["results"]["summary"] = {
                    "status": "no_updates",
                    "message": "No dependency updates available",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
                return self.update_session

            # Step 4: Perform updates
            update_results = await self.perform_updates(update_type)

            # Step 5: Run tests
            test_results = await self.run_tests()

            # Step 6: Commit changes if tests pass and auto_commit is enabled
            commit_results = None
            if test_results["overall_status"] == "passed" and auto_commit:
                commit_results = await self.commit_changes()
            elif test_results["overall_status"] == "failed":
                logger.error("❌ Tests failed after updates - not committing changes")

            # Step 7: Final security audit
            final_security = await self.run_security_audit()

            # Generate summary
            self.update_session["results"]["summary"] = {
                "status": "completed",
                "python_updates": python_updates,
                "node_updates": node_updates,
                "tests_passed": test_results["overall_status"] == "passed",
                "changes_committed": commit_results is not None,
                "final_security_status": final_security["overall_status"],
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            self.update_session["end_time"] = datetime.now(timezone.utc).isoformat()

            logger.info("✅ Update cycle completed successfully")
            return self.update_session

        except Exception as e:
            logger.error(f"Update cycle failed: {e}")
            self.update_session["results"]["error"] = {
                "message": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
            self.update_session["end_time"] = datetime.now(timezone.utc).isoformat()
            raise

    def save_update_report(self, report_path: str = "") -> str:
        """Save update session report to file

        Args:
            report_path: Path to save report (empty string for default location).

        Returns:
            Path where report was saved
        """
        # Ensure we have a valid path string
        if not isinstance(report_path, str):
            report_path = ""

        # Clean and validate the path
        clean_path = report_path.strip()
        if not clean_path:
            clean_path = f"logs/update_report_{self.update_session['session_id']}.json"

        # Create Path object and ensure parent directories exist
        report_file = Path(clean_path).absolute()
        report_file.parent.mkdir(parents=True, exist_ok=True)

        # Write the report
        with open(report_file, "w") as f:
            json.dump(self.update_session, f, indent=2, default=str)

        logger.info(f"📄 Update report saved to: {report_file}")
        return str(report_file)


# Convenience functions for CLI integration
async def run_update_cycle(
    update_type: str = "patch", auto_commit: bool = True, project_root: str = ""
) -> Dict[str, Any]:
    """Run a complete update cycle"""
    project_root = project_root if project_root else "."
    agent = UpdateAgent(project_root)
    return await agent.run_full_update_cycle(update_type, auto_commit)


async def run_security_audit_only(project_root: str = ".") -> Dict[str, Any]:
    """Run only the security audit"""
    agent = UpdateAgent(project_root)
    return await agent.run_security_audit()


async def check_updates_only(project_root: str = ".") -> Dict[str, Any]:
    """Check for updates without performing them"""
    agent = UpdateAgent(project_root)
    return await agent.check_for_updates()
