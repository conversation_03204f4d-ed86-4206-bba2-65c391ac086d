"""
Plugin System for WebsiteGenerator
Provides extensible plugin architecture for website generation.
"""

from core.plugins.base import <PERSON>lugin<PERSON><PERSON>, PluginManager, PluginRegistry
from core.plugins.performance_plugin import PerformancePlugin
from core.plugins.security_plugin import SecurityPlugin
from core.plugins.seo_plugin import SEOPlugin

__all__ = [
    "PluginBase",
    "PluginManager",
    "PluginRegistry",
    "SEOPlugin",
    "PerformancePlugin",
    "SecurityPlugin",
]
