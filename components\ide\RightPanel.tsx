import React, { memo } from 'react';
import { DocumentationPanel } from './DocumentationPanel';
import { ModelHealthPanel } from './ModelHealthPanel';
import { ChatPanel } from './ChatPanel';
import { ErrorDetectionPanel } from './ErrorDetectionPanel';
import { BonusFeaturesPanel } from './BonusFeaturesPanel';

interface RightPanelProps {
  showDocumentationPanel: boolean;
  showModelHealth: boolean;
  showErrorDetection: boolean;
  showBonusFeatures: boolean;
  handleErrorSelect: (error: any) => void;
}

export const RightPanel = memo<RightPanelProps>(({
  showDocumentationPanel,
  showModelHealth,
  showErrorDetection,
  showBonusFeatures,
  handleErrorSelect
}) => (
  <div className="h-full bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700">
    {showDocumentationPanel ? (
      <DocumentationPanel />
    ) : showModelHealth ? (
      <ModelHealthPanel />
    ) : showBonusFeatures ? (
      <BonusFeaturesPanel />
    ) : (
      <ChatPanel />
    )}
    {showErrorDetection && (
      <ErrorDetectionPanel projectPath="." onErrorSelect={handleErrorSelect} />
    )}
  </div>
));
