{"agent_name": "ArchitectAgent", "version": "1.0.0", "description": "High-level task orchestrator for AI Coding Agent", "task_management": {"max_concurrent_tasks": 5, "task_timeout_minutes": 120, "retry_attempts": 3, "retry_delay_seconds": 30, "cleanup_interval_hours": 24, "max_task_history": 100}, "agent_coordination": {"frontend_agent": {"enabled": true, "priority": "high", "timeout_seconds": 300}, "backend_agent": {"enabled": true, "priority": "high", "timeout_seconds": 300}, "container_agent": {"enabled": true, "priority": "medium", "timeout_seconds": 600}, "learning_agent": {"enabled": true, "priority": "low", "timeout_seconds": 180}, "shell_ops_agent": {"enabled": true, "priority": "medium", "timeout_seconds": 240}, "security_agent": {"enabled": true, "priority": "critical", "timeout_seconds": 120}, "monitoring_agent": {"enabled": true, "priority": "low", "timeout_seconds": 60}}, "task_parsing": {"intent_classification": {"enabled": true, "confidence_threshold": 0.7, "fallback_intent": "general_development"}, "requirement_extraction": {"enabled": true, "extract_technologies": true, "extract_functionality": true, "extract_constraints": true}, "complexity_analysis": {"enabled": true, "factors": ["lines_of_code", "dependencies", "integrations", "security_requirements"]}}, "progress_tracking": {"update_interval_seconds": 30, "detailed_logging": true, "progress_notifications": true, "completion_notifications": true, "error_notifications": true}, "error_handling": {"max_retries": 3, "retry_delay_seconds": 30, "escalation_threshold": 2, "fallback_strategies": ["simplified_approach", "manual_intervention", "alternative_solution"]}, "performance": {"max_memory_usage_mb": 1024, "max_cpu_usage_percent": 80, "resource_monitoring": true, "performance_optimization": true}, "security": {"input_validation": true, "output_sanitization": true, "access_control": true, "audit_logging": true, "security_scanning": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_logging": true, "console_logging": true, "log_rotation": true, "max_log_size_mb": 10, "backup_count": 5}, "monitoring": {"health_checks": true, "metrics_collection": true, "alerting": true, "dashboard_integration": true}, "integration": {"api_endpoints": {"enabled": true, "rate_limiting": true, "authentication": true, "cors_enabled": true}, "cli_interface": {"enabled": true, "command_validation": true, "help_documentation": true}, "webhook_support": {"enabled": true, "webhook_timeout_seconds": 30, "retry_failed_webhooks": true}}, "development": {"debug_mode": false, "development_features": false, "test_mode": false, "mock_agents": false}}