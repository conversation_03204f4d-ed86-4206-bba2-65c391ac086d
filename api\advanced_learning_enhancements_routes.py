#!/usr/bin/env python3
"""
Advanced Learning Enhancements API Routes

Provides REST API endpoints for the Advanced Learning Enhancements system components:
- Meta Learning Optimizer
- Pareto Optimizer
- Workload Predictor
- Cascade Predictor
- Federated Learning Manager
- Capability Discovery
- Adversarial Detector
- Degradation Manager
"""

import json
import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/advanced-learning-enhancements", tags=["Advanced Learning Enhancements"]
)


# Pydantic models for request/response
class ModelPerformanceRequest(BaseModel):
    model_config = {"protected_namespaces": ()}
    model_performance: Dict[str, List[float]]


class ParetoOptimizationRequest(BaseModel):
    objectives: List[str]
    current_performance: Dict[str, float]
    constraints: Dict[str, Any]


class WorkloadPredictionRequest(BaseModel):
    historical_patterns: Dict[str, List[float]]
    external_factors: Dict[str, float]


class CascadeRiskRequest(BaseModel):
    current_loads: Dict[str, float]
    failure_patterns: Dict[str, List[float]]


class FederatedLearningRequest(BaseModel):
    model_config = {"protected_namespaces": ()}
    participants: List[str]
    model_configuration: Dict[str, Any]


class CapabilityDiscoveryRequest(BaseModel):
    model_config = {"protected_namespaces": ()}
    model_id: str
    test_scenarios: List[Dict[str, Any]]


class AdversarialDetectionRequest(BaseModel):
    user_behavior: Dict[str, Any]
    system_metrics: Dict[str, float]


class DegradationAssessmentRequest(BaseModel):
    system_metrics: Dict[str, float]
    performance_thresholds: Dict[str, float]


# Dependency to get agent instance
def get_agent():
    try:
        from api.agent_dependency import get_global_agent

        agent = get_global_agent()
        if agent is None:
            raise ValueError("Global agent not initialized")
        return agent
    except Exception as e:
        logger.error(f"Error getting agent instance: {e}")

        # Return a mock agent for testing
        class MockAIAgent:
            async def get_meta_learning_insights(self):
                return {"success": True, "insights": "mock"}

            async def get_pareto_frontier(self):
                return {"success": True, "frontier": "mock"}

            async def get_workload_insights(self):
                return {"success": True, "insights": "mock"}

            async def get_cascade_insights(self):
                return {"success": True, "insights": "mock"}

            async def get_federated_status(self):
                return {"success": True, "status": "mock"}

            async def get_capability_insights(self):
                return {"success": True, "insights": "mock"}

            async def get_adversarial_insights(self):
                return {"success": True, "insights": "mock"}

            async def get_degradation_insights(self):
                return {"success": True, "insights": "mock"}

        return MockAIAgent()


# Meta Learning Optimizer Routes
@router.post("/meta-learning/optimize")
async def optimize_learning_rates(
    request: ModelPerformanceRequest, agent=Depends(get_agent)
):
    """Optimize learning rates for multiple models"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.optimize_learning_rates(request.model_performance)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error optimizing learning rates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/meta-learning/insights")
async def get_meta_learning_insights(agent=Depends(get_agent)):
    """Get insights from meta learning optimizer"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_meta_learning_insights()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting meta learning insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Pareto Optimizer Routes
@router.post("/pareto/solutions")
async def find_pareto_solutions(
    request: ParetoOptimizationRequest, agent=Depends(get_agent)
):
    """Find Pareto optimal solutions"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.find_pareto_solutions(
            request.objectives, request.current_performance, request.constraints
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error finding Pareto solutions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pareto/frontier")
async def get_pareto_frontier(agent=Depends(get_agent)):
    """Get current Pareto frontier"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_pareto_frontier()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting Pareto frontier: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Workload Predictor Routes
@router.post("/workload/predict")
async def predict_demand(request: WorkloadPredictionRequest, agent=Depends(get_agent)):
    """Predict demand spikes"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.predict_demand(
            request.historical_patterns, request.external_factors
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error predicting demand: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workload/insights")
async def get_workload_insights(agent=Depends(get_agent)):
    """Get workload prediction insights"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_workload_insights()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting workload insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Cascade Predictor Routes
@router.post("/cascade/detect")
async def detect_cascade_risk(request: CascadeRiskRequest, agent=Depends(get_agent)):
    """Detect cascade failure risks"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.detect_cascade_risk(
            request.current_loads, request.failure_patterns
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error detecting cascade risk: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cascade/insights")
async def get_cascade_insights(agent=Depends(get_agent)):
    """Get cascade prediction insights"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_cascade_insights()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting cascade insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Federated Learning Manager Routes
@router.post("/federated/start")
async def start_federated_learning(
    request: FederatedLearningRequest, agent=Depends(get_agent)
):
    """Start federated learning session"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.start_federated_learning(
            request.participants, request.model_configuration
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error starting federated learning: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/federated/status")
async def get_federated_status(
    session_id: Optional[str] = None, agent=Depends(get_agent)
):
    """Get federated learning status"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_federated_status(session_id)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting federated status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Capability Discovery Routes
@router.post("/capability/discover")
async def discover_capabilities(
    request: CapabilityDiscoveryRequest, agent=Depends(get_agent)
):
    """Discover model capabilities"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.discover_capabilities(
            request.model_id, request.test_scenarios
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error discovering capabilities: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/capability/insights")
async def get_capability_insights(agent=Depends(get_agent)):
    """Get capability discovery insights"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_capability_insights()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting capability insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Adversarial Detector Routes
@router.post("/adversarial/detect")
async def detect_adversarial_activity(
    request: AdversarialDetectionRequest, agent=Depends(get_agent)
):
    """Detect adversarial activity"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.detect_adversarial_activity(
            request.user_behavior, request.system_metrics
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error detecting adversarial activity: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/adversarial/insights")
async def get_adversarial_insights(agent=Depends(get_agent)):
    """Get adversarial detection insights"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_adversarial_insights()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting adversarial insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Degradation Manager Routes
@router.post("/degradation/assess")
async def assess_degradation_risk(
    request: DegradationAssessmentRequest, agent=Depends(get_agent)
):
    """Assess degradation risk"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.assess_degradation_risk(
            request.system_metrics, request.performance_thresholds
        )

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error assessing degradation risk: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/degradation/insights")
async def get_degradation_insights(agent=Depends(get_agent)):
    """Get degradation management insights"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_degradation_insights()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting degradation insights: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# System-wide Routes
@router.get("/status")
async def get_all_enhancements_status(agent=Depends(get_agent)):
    """Get status of all advanced learning enhancements"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.get_all_enhancements_status()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error getting all enhancements status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cycle")
async def run_enhancement_cycle(agent=Depends(get_agent)):
    """Run a complete enhancement cycle for all components"""
    try:
        from cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        commands = AdvancedLearningEnhancementsCommands(agent)

        result = await commands.run_enhancement_cycle()

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except Exception as e:
        logger.error(f"Error running enhancement cycle: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Health check route
@router.get("/health")
async def health_check():
    """Health check for advanced learning enhancements"""
    return {
        "status": "healthy",
        "service": "Advanced Learning Enhancements API",
        "version": "2.0.0",
        "components": [
            "Meta Learning Optimizer",
            "Pareto Optimizer",
            "Workload Predictor",
            "Cascade Predictor",
            "Federated Learning Manager",
            "Capability Discovery",
            "Adversarial Detector",
            "Degradation Manager",
        ],
    }
