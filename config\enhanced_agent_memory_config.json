{"memory_tracking": {"enabled": true, "max_attempts_per_task": 3, "cooldown_seconds": 300, "verify_tasks": true, "reset_on_success": true, "verification_level": "basic", "persistence": true, "cleanup_interval": 86400, "max_memory_size": 1000, "backup_memory": true, "compression": false}, "verification": {"enabled": true, "levels": {"none": {"description": "No verification", "checks": []}, "basic": {"description": "Basic success/failure verification", "checks": ["result_success_check", "error_check", "execution_time_check"]}, "comprehensive": {"description": "Comprehensive verification with external checks", "checks": ["result_success_check", "error_check", "execution_time_check", "external_validation", "performance_check", "resource_usage_check"]}}, "timeout_seconds": 30, "retry_verification": true, "max_verification_attempts": 2}, "cooldown": {"enabled": true, "default_seconds": 300, "task_specific": {"test_execution": 60, "file_operations": 30, "network_requests": 120, "database_operations": 180, "heavy_computation": 600}, "exponential_backoff": true, "max_cooldown_seconds": 3600}, "performance_monitoring": {"enabled": true, "track_execution_time": true, "track_resource_usage": true, "track_success_rate": true, "track_error_patterns": true, "alert_thresholds": {"execution_time_seconds": 300, "error_rate_percent": 20, "memory_usage_mb": 512, "cpu_usage_percent": 80}}, "persistence": {"enabled": true, "storage_type": "json", "backup_enabled": true, "backup_interval_hours": 24, "max_backups": 7, "compression": false, "encryption": false}, "cleanup": {"enabled": true, "cleanup_interval_hours": 24, "max_age_days": 30, "max_tasks_per_agent": 1000, "preserve_successful": false, "preserve_failed": true}, "logging": {"enabled": true, "level": "INFO", "memory_events": true, "verification_events": true, "cooldown_events": true, "performance_events": true}}