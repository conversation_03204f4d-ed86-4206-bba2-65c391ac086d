import React, { useState } from 'react';
import { aiService } from '@/services/AIService';
import { fileManager } from '@/services/FileManager';

interface DocumentationPanelProps {
  className?: string;
  currentProject?: string;
  projectDescription?: string;
  author?: string;
  version?: string;
}

const DOC_TYPES = [
  { value: 'api', label: 'API Documentation' },
  { value: 'user_guide', label: 'User Guide' },
  { value: 'technical', label: 'Technical Documentation' },
  { value: 'code_comment', label: 'Code Comments' }
];

export const DocumentationPanel: React.FC<DocumentationPanelProps> = ({ className = '', currentProject = 'Untitled Project', projectDescription = '', author = '', version = '' }) => {
  const [loading, setLoading] = useState(false);
  const [documentation, setDocumentation] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [docType, setDocType] = useState<string>('api');
  const [showVersionModal, setShowVersionModal] = useState(false);
  const [versionFile, setVersionFile] = useState<string | null>(null);
  const [selectedVersion, setSelectedVersion] = useState<any | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Gather all files in the project
  const files = fileManager.getAllFiles();

  // Handle file selection
  const handleFileToggle = (fileName: string) => {
    setSelectedFiles(prev =>
      prev.includes(fileName)
        ? prev.filter(f => f !== fileName)
        : [...prev, fileName]
    );
  };

  const handleSelectAllFiles = () => {
    setSelectedFiles(files.map(f => f.name));
  };

  const handleDeselectAllFiles = () => {
    setSelectedFiles([]);
  };

  const handleGenerateDocumentation = async () => {
    setLoading(true);
    setError(null);
    setDocumentation('');
    try {
      // Build structured context
      const context = {
        project: {
          name: currentProject,
          description: projectDescription,
          author,
          version
        },
        files: files.filter(f => selectedFiles.includes(f.name)).map(f => ({ name: f.name, content: f.content })),
        docType
      };
      const prompt = `Generate ${DOC_TYPES.find(d => d.value === docType)?.label || 'documentation'} for the following project and files.\nProject: ${context.project.name}\nDescription: ${context.project.description}\nFiles: ${context.files.map(f => f.name).join(', ')}.`;
      const response = await aiService.generateResponse(prompt, 'content');
      setDocumentation(response.content);
    } catch (err: any) {
      setError('Failed to generate documentation.');
    } finally {
      setLoading(false);
    }
  };

  // Export documentation as Markdown
  const handleExport = () => {
    if (!documentation) return;
    const blob = new Blob([documentation], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentProject || 'documentation'}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Version History Modal
  const handleVersionHistory = () => {
    setShowVersionModal(true);
    setVersionFile(selectedFiles[0] || null); // Show first selected file by default
    setSelectedVersion(null);
  };

  const closeVersionModal = () => {
    setShowVersionModal(false);
    setVersionFile(null);
    setSelectedVersion(null);
  };

  const handleViewVersion = (version: any) => {
    setSelectedVersion(version);
  };

  // Restore version logic
  const handleRestoreVersion = (version: any) => {
    if (!versionFile) return;
    const file = files.find(f => f.name === versionFile);
    if (!file) return;
    if (window.confirm('Are you sure you want to restore this version? This will overwrite the current file content.')) {
      try {
        fileManager.restoreFromVersion(file.id, version.id);
        setSuccessMessage('Version restored successfully.');
        setTimeout(() => setSuccessMessage(null), 3000);
        closeVersionModal();
      } catch (err: any) {
        setError('Failed to restore version.');
      }
    }
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
          Documentation
        </h3>
      </div>

      {/* File and Doc Type Selection */}
      <div className="flex flex-col gap-2 p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Files:</span>
          <button onClick={handleSelectAllFiles} className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Select All</button>
          <button onClick={handleDeselectAllFiles} className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600">Deselect All</button>
        </div>
        <div className="flex flex-wrap gap-2">
          {files.map(f => (
            <label key={f.name} className="flex items-center gap-1 text-xs cursor-pointer">
              <input
                type="checkbox"
                checked={selectedFiles.includes(f.name)}
                onChange={() => handleFileToggle(f.name)}
                className="w-3 h-3"
              />
              {f.name}
            </label>
          ))}
        </div>
        <div className="flex items-center gap-2 mt-2">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Doc Type:</span>
          <select
            value={docType}
            onChange={e => setDocType(e.target.value)}
            className="text-xs px-2 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded"
          >
            {DOC_TYPES.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2 p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <button
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm disabled:opacity-50"
          onClick={handleGenerateDocumentation}
          disabled={loading || selectedFiles.length === 0}
        >
          {loading ? 'Generating...' : 'Generate Documentation'}
        </button>
        <button className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors text-sm" onClick={handleExport} disabled={loading || !documentation}>
          Export
        </button>
        <button className="px-3 py-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors text-sm" onClick={handleVersionHistory} disabled={loading || selectedFiles.length === 0}>
          Version History
        </button>
      </div>

      {/* Documentation Display */}
      <div className="flex-1 overflow-auto p-4 bg-white dark:bg-gray-900">
        {successMessage && (
          <div className="text-green-600 text-center mb-4">{successMessage}</div>
        )}
        {error && (
          <div className="text-red-500 text-center mb-4">{error}</div>
        )}
        {documentation ? (
          <div className="prose dark:prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: documentation.replace(/\n/g, '<br/>') }} />
        ) : !loading && !error ? (
          <div className="text-gray-500 dark:text-gray-400 text-center mt-16">
            <p className="text-lg font-medium">No documentation generated yet</p>
            <p className="text-sm">Use the actions above to generate or view documentation for your project.</p>
          </div>
        ) : null}
      </div>

      {/* Version History Modal */}
      {showVersionModal && versionFile && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full p-6 relative">
            <button onClick={closeVersionModal} className="absolute top-2 right-2 text-gray-500 hover:text-gray-900 dark:hover:text-gray-100">&times;</button>
            <h4 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Version History for {versionFile}</h4>
            <div className="max-h-80 overflow-y-auto divide-y divide-gray-200 dark:divide-gray-700">
              {(fileManager.getFileVersions(files.find(f => f.name === versionFile)?.id || '') || []).map((version: any) => (
                <div key={version.id} className="py-2 flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">{new Date(version.timestamp).toLocaleString()}</span>
                    <span className="text-xs text-gray-400">{version.author}</span>
                    <span className="text-xs text-gray-400 italic">{version.message}</span>
                    <button className="ml-auto text-xs text-blue-600 hover:underline" onClick={() => handleViewVersion(version)}>View</button>
                    <button className="text-xs text-green-600 hover:underline" onClick={() => handleRestoreVersion(version)}>Restore</button>
                  </div>
                  {selectedVersion?.id === version.id && (
                    <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded mt-1 border border-gray-200 dark:border-gray-700 text-xs whitespace-pre-wrap">
                      {version.content}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentationPanel;
