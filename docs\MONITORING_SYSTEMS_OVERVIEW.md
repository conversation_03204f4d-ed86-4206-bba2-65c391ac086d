# 📊 Monitoring Systems Overview

## 🎯 Two Distinct Monitoring Systems

This project has **two separate monitoring systems** that serve different purposes and should be used together for comprehensive monitoring coverage.

## 1. 🔍 Cursor Rules Monitor (Essential)

### Purpose
**Real-time cursor rules compliance enforcement** - ensures ALL cursor rules are followed during development.

### Key Features
- ✅ **Real-time compliance checking** every 30 seconds
- ✅ **Automatic violation detection** and blocking
- ✅ **Compliance score tracking** (0-100%)
- ✅ **Historical violation logging**
- ✅ **Immediate alerts** for critical violations
- ✅ **Continuous enforcement** of all cursor rules

### Critical Requirements
- **MUST be running** during all development sessions
- **Compliance score >90%** required to proceed
- **Prevents violations** before they occur
- **Enforces cursor rules** in real-time

### Usage
```bash
# ✅ CORRECT: Verify monitoring is running (Windows PowerShell)
.\.venv\Scripts\Activate.ps1; python scripts/cursor_rules_monitor.py --status

# ✅ CORRECT: Start monitoring if not running (Windows PowerShell)
.\.venv\Scripts\Activate.ps1; python scripts/cursor_rules_monitor.py --strict --daemon

# ✅ CORRECT: Verify monitoring is running (UNIX/macOS)
source .venv/bin/activate && python scripts/cursor_rules_monitor.py --status

# ✅ CORRECT: Start monitoring if not running (UNIX/macOS)
source .venv/bin/activate && python scripts/cursor_rules_monitor.py --strict --daemon
```

### PID File
- **Location**: `logs/cursor_rules_monitor.pid`
- **Purpose**: Prevents multiple instances
- **Recovery**: Automatic cleanup of stale PID files

### Integration
- **Git hooks**: Pre-commit and pre-push validation
- **CI/CD**: Automated compliance checking
- **Development workflow**: Real-time feedback
- **Monitoring dashboard**: Web-based compliance tracking

## 2. 🖥️ System Monitoring Agent (Optional)

### Purpose
**System health monitoring** - tracks CPU, memory, disk, network, and process metrics for production/deployment environments.

### Key Features
- ✅ **System metrics collection** (CPU, memory, disk, network)
- ✅ **Performance tracking** and alerting
- ✅ **Container health checks**
- ✅ **Resource usage monitoring**
- ✅ **Historical metrics storage**
- ✅ **Prometheus integration**

### Use Cases
- **Production monitoring** and alerting
- **Container health checks**
- **Performance tracking**
- **Resource usage analysis**
- **System diagnostics**

### Usage
```bash
# ✅ CORRECT: Start system monitoring agent
python -m monitoring.monitoring_agent

# ✅ CORRECT: Check system monitoring status
python scripts/check_monitoring_status.py

# ✅ CORRECT: Use CLI commands
python cli/monitoring_commands.py status
python cli/monitoring_commands.py start
python cli/monitoring_commands.py stop
```

### PID File
- **Location**: `logs/monitor.pid`
- **Purpose**: Prevents multiple instances
- **Recovery**: Automatic cleanup of stale PID files

### Integration
- **Docker containers**: Health checks and monitoring
- **Prometheus**: Metrics export
- **Grafana**: Dashboard visualization
- **Alerting**: Email, Slack, webhook notifications

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Monitoring Systems                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  🔍 Cursor Rules Monitor (Essential)                       │
│  ├── Real-time compliance checking                         │
│  ├── Violation detection and blocking                      │
│  ├── Compliance score tracking                             │
│  ├── Development workflow integration                      │
│  └── PID: logs/cursor_rules_monitor.pid                   │
│                                                             │
│  🖥️ System Monitoring Agent (Optional)                    │
│  ├── System health metrics                                 │
│  ├── Performance tracking                                  │
│  ├── Container health checks                              │
│  ├── Production monitoring                                 │
│  └── PID: logs/monitor.pid                                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 When to Use Each System

### Cursor Rules Monitor (Always)
- ✅ **During development sessions**
- ✅ **Before committing code**
- ✅ **In CI/CD pipelines**
- ✅ **When working on code changes**
- ✅ **For compliance enforcement**

### System Monitoring Agent (Optional)
- ✅ **Production deployments**
- ✅ **Container environments**
- ✅ **Performance monitoring**
- ✅ **Resource usage tracking**
- ✅ **System diagnostics**

## 🚨 Critical Differences

| Aspect | Cursor Rules Monitor | System Monitoring Agent |
|--------|---------------------|------------------------|
| **Purpose** | Code compliance enforcement | System health monitoring |
| **Criticality** | **ESSENTIAL** - Must be running | Optional - Nice to have |
| **Scope** | Code quality, TODOs, tests | CPU, memory, disk, network |
| **Frequency** | Every 30 seconds | Every 60 seconds |
| **PID File** | `cursor_rules_monitor.pid` | `monitor.pid` |
| **Integration** | Development workflow | Production monitoring |

## 🎯 Best Practices

### Development Workflow
1. **Always start** cursor rules monitor before development
2. **Verify compliance** score >90% before proceeding
3. **Address violations** immediately when detected
4. **Use system monitoring** for production environments

### Production Deployment
1. **Deploy both** monitoring systems in containers
2. **Configure alerts** for critical issues
3. **Monitor resource usage** with system monitoring agent
4. **Track compliance** with cursor rules monitor

### Troubleshooting
1. **Check PID files** for stale processes
2. **Verify virtual environment** is activated
3. **Check logs** for error messages
4. **Restart monitoring** if issues persist

## 📁 File Locations

### Cursor Rules Monitor
- **Main script**: `scripts/cursor_rules_monitor.py`
- **PID file**: `logs/cursor_rules_monitor.pid`
- **Logs**: `logs/cursor_rules_monitor.log`
- **Config**: `config/cursor_monitor_config.json`

### System Monitoring Agent
- **Main module**: `monitoring/monitoring_agent.py`
- **PID file**: `logs/monitor.pid`
- **Logs**: `logs/monitoring.log`
- **CLI**: `cli/monitoring_commands.py`
- **Tests**: `tests/test_monitoring_agent_pid.py`

## 🔧 Configuration

### Cursor Rules Monitor
```json
{
  "monitor": {
    "check_interval": 30,
    "strict_mode": true,
    "daemon_mode": true,
    "log_level": "INFO"
  }
}
```

### System Monitoring Agent
```json
{
  "cpu_threshold": 80.0,
  "memory_threshold": 75.0,
  "disk_threshold": 85.0,
  "check_interval": 60,
  "alert_cooldown": 300
}
```

## 🎉 Summary

**Keep both monitoring systems** - they serve complementary purposes:

- **Cursor Rules Monitor**: Essential for development compliance
- **System Monitoring Agent**: Optional for production monitoring

Together, they provide comprehensive monitoring coverage for both development workflow and system health.
