"""
Cursor Rules Enforcer
Comprehensive compliance enforcement system for cursor rules
Ensures ALL cursor rules are followed before any code changes
"""

import ast
import importlib.util
import json
import logging
import os
import re
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class CursorRulesEnforcer:
    """Comprehensive cursor rules compliance enforcement system"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.rules_file = self.project_root / ".cursor" / "rules" / "cursorrules.md"
        self.config_dir = self.project_root / "config"
        self.requirements_file = self.config_dir / "requirements.txt"

        # Load cursor rules
        self.cursor_rules = self._load_cursor_rules()

        # Compliance tracking
        self.compliance_db = self.project_root / "data" / "cursor_compliance.json"
        self.compliance_data = self._load_compliance_data()

        # Critical rule violations
        self.critical_violations = []
        self.warnings = []

        # Test results tracking
        self.test_results = {}

        # TODO tracking
        self.todo_status = {}

    def _load_cursor_rules(self) -> Dict[str, Any]:
        """Load and parse cursor rules from markdown file"""
        try:
            with open(self.rules_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Parse rules into structured format
            rules = {
                "critical_requirements": self._extract_critical_requirements(content),
                "file_organization": self._extract_file_organization_rules(content),
                "testing_requirements": self._extract_testing_requirements(content),
                "dependency_management": self._extract_dependency_rules(content),
                "cli_api_requirements": self._extract_cli_api_rules(content),
                "ai_model_requirements": self._extract_ai_model_rules(content),
                "git_workflow": self._extract_git_workflow_rules(content),
                "security_requirements": self._extract_security_rules(content),
                "todo_completion": self._extract_todo_rules(content),
                "file_cleanup": self._extract_file_cleanup_rules(content),
                "mock_data_cleanup": self._extract_mock_data_rules(content),
            }

            return rules
        except Exception as e:
            logger.error(f"Failed to load cursor rules: {e}")
            return {}

    def _extract_critical_requirements(self, content: str) -> Dict[str, Any]:
        """Extract critical requirements from cursor rules"""
        critical_rules = {}

        # Extract 100% TODO completion requirement
        todo_pattern = r"🚨\s*CRITICAL:\s*100%\s*TODO\s*COMPLETION\s*REQUIREMENT"
        if re.search(todo_pattern, content, re.IGNORECASE):
            critical_rules["todo_completion_100_percent"] = True

        # Extract 100% test success requirement
        test_pattern = r"🚨\s*CRITICAL:\s*100%\s*TEST\s*SUCCESS\s*REQUIREMENT"
        if re.search(test_pattern, content, re.IGNORECASE):
            critical_rules["test_success_100_percent"] = True

        # Extract file deletion requirement
        deletion_pattern = r"🗑️\s*CRITICAL:\s*ALWAYS\s*DELETE\s*OBSOLETE\s*FILES"
        if re.search(deletion_pattern, content, re.IGNORECASE):
            critical_rules["mandatory_file_deletion"] = True

        # Extract dependency management requirement
        dep_pattern = r"📦\s*CRITICAL:\s*ALWAYS\s*ADD\s*NEW\s*DEPENDENCIES\s*TO\s*REQUIREMENTS\.TXT"
        if re.search(dep_pattern, content, re.IGNORECASE):
            critical_rules["mandatory_dependency_management"] = True

        # Extract CLI/API requirement
        cli_pattern = (
            r"🖥️\s*CRITICAL:\s*ALWAYS\s*CREATE\s*CLI\s*COMMANDS\s*FOR\s*EVERY\s*FEATURE"
        )
        if re.search(cli_pattern, content, re.IGNORECASE):
            critical_rules["mandatory_cli_api_creation"] = True

        return critical_rules

    def _extract_file_organization_rules(self, content: str) -> Dict[str, Any]:
        """Extract file organization rules"""
        rules = {}

        # Extract directory structure rules
        dir_patterns = {
            "docs_directory": r"Documentation must go in docs/",
            "config_directory": r"All configuration files must go in config/",
            "scripts_directory": r"Scripts and entry points must go in scripts/",
            "tests_directory": r"All test code must go in tests/",
            "database_directory": r"All .db and database-related files must go in database/",
            "logs_directory": r"All logs must go in logs/",
            "data_directory": r"Data files must go in data/",
        }

        for rule_name, pattern in dir_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                rules[rule_name] = True

        return rules

    def _extract_testing_requirements(self, content: str) -> Dict[str, Any]:
        """Extract testing requirements"""
        rules = {}

        # Extract test success requirements
        if re.search(
            r"ALL test runs MUST achieve 100% success rate", content, re.IGNORECASE
        ):
            rules["test_success_100_percent"] = True

        if re.search(r"NEVER accept test results below 100%", content, re.IGNORECASE):
            rules["no_test_failures_accepted"] = True

        if re.search(
            r"Test failures indicate code quality issues", content, re.IGNORECASE
        ):
            rules["test_failures_require_fixes"] = True

        return rules

    def _extract_dependency_rules(self, content: str) -> Dict[str, Any]:
        """Extract dependency management rules"""
        rules = {}

        if re.search(
            r"ALWAYS add new dependencies to requirements\.txt", content, re.IGNORECASE
        ):
            rules["mandatory_requirements_update"] = True

        if re.search(
            r"Pin dependency versions with exact versions", content, re.IGNORECASE
        ):
            rules["exact_version_pinning"] = True

        if re.search(r"Test with clean environment", content, re.IGNORECASE):
            rules["clean_environment_testing"] = True

        return rules

    def _extract_cli_api_rules(self, content: str) -> Dict[str, Any]:
        """Extract CLI and API creation rules"""
        rules = {}

        if re.search(
            r"ALWAYS create CLI commands for every feature", content, re.IGNORECASE
        ):
            rules["mandatory_cli_creation"] = True

        if re.search(
            r"ALWAYS create API routes for every feature", content, re.IGNORECASE
        ):
            rules["mandatory_api_creation"] = True

        if re.search(r"CLI and API creation is MANDATORY", content, re.IGNORECASE):
            rules["cli_api_mandatory"] = True

        return rules

    def _extract_ai_model_rules(self, content: str) -> Dict[str, Any]:
        """Extract AI model requirements"""
        rules = {}

        if re.search(
            r"This project ONLY uses local Ollama models", content, re.IGNORECASE
        ):
            rules["local_ollama_only"] = True

        # Extract approved models
        models = re.findall(r"`([^`]+)`", content)
        ollama_models = [
            m
            for m in models
            if "ollama" in m.lower()
            or any(
                x in m.lower()
                for x in ["deepseek", "yi-coder", "qwen", "starcoder", "mistral"]
            )
        ]
        if ollama_models:
            rules["approved_models"] = ollama_models

        return rules

    def _extract_git_workflow_rules(self, content: str) -> Dict[str, Any]:
        """Extract git workflow rules"""
        rules = {}

        if re.search(
            r"NEVER commit code with less than 100% test success",
            content,
            re.IGNORECASE,
        ):
            rules["no_commit_without_test_success"] = True

        if re.search(r"ALWAYS use conventional commit format", content, re.IGNORECASE):
            rules["conventional_commit_format"] = True

        if re.search(r"Repository Location.*G:\\AICodingAgent", content, re.IGNORECASE):
            rules["repository_location"] = "G:\\AICodingAgent"

        return rules

    def _extract_security_rules(self, content: str) -> Dict[str, Any]:
        """Extract security requirements"""
        rules = {}

        if re.search(r"Validate all user inputs", content, re.IGNORECASE):
            rules["input_validation"] = True

        if re.search(
            r"Use HTTPS for all external communications", content, re.IGNORECASE
        ):
            rules["https_required"] = True

        if re.search(r"Implement proper authentication", content, re.IGNORECASE):
            rules["authentication_required"] = True

        return rules

    def _extract_todo_rules(self, content: str) -> Dict[str, Any]:
        """Extract TODO completion rules"""
        rules = {}

        if re.search(r"ALL TODOs MUST be completed to 100%", content, re.IGNORECASE):
            rules["mandatory_todo_completion"] = True

        if re.search(r"NEVER leave TODOs in 'in_progress'", content, re.IGNORECASE):
            rules["no_incomplete_todos"] = True

        if re.search(r"TODO completion is MANDATORY", content, re.IGNORECASE):
            rules["todo_completion_mandatory"] = True

        return rules

    def _extract_file_cleanup_rules(self, content: str) -> Dict[str, Any]:
        """Extract file cleanup rules"""
        rules = {}

        if re.search(r"ALWAYS delete obsolete files", content, re.IGNORECASE):
            rules["mandatory_obsolete_file_deletion"] = True

        if re.search(r"NEVER leave duplicate or unused files", content, re.IGNORECASE):
            rules["no_duplicate_files"] = True

        if re.search(r"File deletion is MANDATORY", content, re.IGNORECASE):
            rules["file_deletion_mandatory"] = True

        return rules

    def _extract_mock_data_rules(self, content: str) -> Dict[str, Any]:
        """Extract mock data cleanup rules"""
        rules = {}

        if re.search(
            r"ALWAYS remove mock data after feature testing", content, re.IGNORECASE
        ):
            rules["mandatory_mock_data_cleanup"] = True

        if re.search(
            r"NEVER leave mock data in production code", content, re.IGNORECASE
        ):
            rules["no_mock_data_in_production"] = True

        return rules

    def _load_compliance_data(self) -> Dict[str, Any]:
        """Load compliance tracking data"""
        try:
            if self.compliance_db.exists():
                with open(self.compliance_db, "r") as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load compliance data: {e}")

        return {
            "last_check": None,
            "violations": [],
            "compliance_score": 0.0,
            "rule_checks": {},
        }

    def _save_compliance_data(self):
        """Save compliance tracking data"""
        try:
            self.compliance_db.parent.mkdir(parents=True, exist_ok=True)
            with open(self.compliance_db, "w") as f:
                json.dump(self.compliance_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save compliance data: {e}")

    def enforce_cursor_rules(self) -> Dict[str, Any]:
        """Main enforcement method - checks ALL cursor rules"""
        logger.info("🔍 Starting cursor rules enforcement check...")

        # Reset violation tracking
        self.critical_violations = []
        self.warnings = []

        # Run all compliance checks
        checks = {
            "file_organization": self._check_file_organization(),
            "todo_completion": self._check_todo_completion(),
            "test_success": self._check_test_success(),
            "dependency_management": self._check_dependency_management(),
            "cli_api_compliance": self._check_cli_api_compliance(),
            "ai_model_compliance": self._check_ai_model_compliance(),
            "git_workflow": self._check_git_workflow(),
            "security_compliance": self._check_security_compliance(),
            "file_cleanup": self._check_file_cleanup(),
            "mock_data_cleanup": self._check_mock_data_cleanup(),
            "virtual_environment": self._check_virtual_environment(),
        }

        # Calculate overall compliance score
        total_checks = len(checks)
        passed_checks = sum(
            1 for check in checks.values() if check.get("status") == "passed"
        )
        compliance_score = (
            (passed_checks / total_checks) * 100 if total_checks > 0 else 0
        )

        # Update compliance data
        self.compliance_data.update(
            {
                "last_check": datetime.now().isoformat(),
                "violations": self.critical_violations,
                "warnings": self.warnings,
                "compliance_score": compliance_score,
                "rule_checks": checks,
            }
        )

        self._save_compliance_data()

        # Generate report
        report = {
            "status": "blocked" if self.critical_violations else "passed",
            "compliance_score": compliance_score,
            "critical_violations": self.critical_violations,
            "warnings": self.warnings,
            "checks": checks,
            "recommendations": self._generate_recommendations(checks),
        }

        # Log results
        if self.critical_violations:
            logger.error(
                f"❌ Cursor rules enforcement FAILED: {len(self.critical_violations)} critical violations"
            )
            for violation in self.critical_violations:
                logger.error(f"   🚨 {violation}")
        else:
            logger.info(
                f"✅ Cursor rules enforcement PASSED: {compliance_score:.1f}% compliance"
            )

        return report

    def check_compliance(self) -> Dict[str, Any]:
        """Check compliance and return format expected by monitor"""
        try:
            # Run the full enforcement check
            result = self.enforce_cursor_rules()

            # Convert to monitor-expected format
            return {
                "compliance_score": result.get("compliance_score", 0),
                "violations": result.get("critical_violations", []),
                "warnings": result.get("warnings", []),
                "status": result.get("status", "unknown"),
                "checks": result.get("checks", {}),
                "recommendations": result.get("recommendations", []),
            }
        except Exception as e:
            logger.error(f"❌ Compliance check failed: {e}")
            return {
                "compliance_score": 0,
                "violations": [f"Compliance check error: {str(e)}"],
                "warnings": [],
                "status": "error",
                "checks": {},
                "recommendations": ["Fix compliance check error"],
            }

    def _check_file_organization(self) -> Dict[str, Any]:
        """Check file organization compliance"""
        violations = []
        warnings = []

        # Load configuration for allowed root files
        config_file = (
            self.project_root / "config" / "cursor_rules_enforcement_config.json"
        )
        allowed_root_files = []

        try:
            with open(config_file, "r") as f:
                config = json.load(f)
                allowed_root_files = config.get("file_organization", {}).get(
                    "root_allowed_files", []
                )
        except Exception:
            # Fallback to default allowed files
            allowed_root_files = [
                ".gitignore",
                "README.md",
                "setup.py",
                "pyproject.toml",
                "package.json",
                "package-lock.json",
                "tsconfig.json",
            ]

        # Check for files in wrong directories
        root_files = [
            f
            for f in self.project_root.iterdir()
            if f.is_file() and f.suffix in [".py", ".ts", ".tsx", ".js", ".jsx"]
        ]
        for file in root_files:
            if file.name not in allowed_root_files:
                violations.append(f"File {file.name} should not be in project root")

        # Check for config files in wrong location (excluding standard Node.js files)
        config_files = (
            list(self.project_root.glob("*.json"))
            + list(self.project_root.glob("*.ini"))
            + list(self.project_root.glob("*.toml"))
        )
        standard_node_files = [
            "package.json",
            "package-lock.json",
            "tsconfig.json",
            "next.config.js",
            "tailwind.config.js",
            "postcss.config.js",
        ]

        for file in config_files:
            if (
                file.name not in allowed_root_files
                and file.name not in standard_node_files
            ):
                warnings.append(
                    f"Config file {file.name} should be in config/ directory"
                )

        # Check for scripts in wrong location
        script_files = (
            list(self.project_root.glob("*.py"))
            + list(self.project_root.glob("*.sh"))
            + list(self.project_root.glob("*.bat"))
        )
        for file in script_files:
            if file.name not in ["setup.py", "pyproject.toml"]:
                warnings.append(
                    f"Script file {file.name} should be in scripts/ directory"
                )

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
        }

    def _check_todo_completion(self) -> Dict[str, Any]:
        """Check TODO completion compliance"""
        violations = []
        warnings = []

        # Scan for TODOs in code
        todo_pattern = r"#\s*TODO.*(?:in_progress|pending|not\s+started)"
        incomplete_todos = []

        for file_path in self.project_root.rglob("*.py"):
            if "venv" not in str(file_path) and "node_modules" not in str(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        matches = re.findall(todo_pattern, content, re.IGNORECASE)
                        if matches:
                            incomplete_todos.extend(
                                [f"{file_path}: {match}" for match in matches]
                            )
                except Exception:
                    continue

        if incomplete_todos:
            violations.append(f"Found {len(incomplete_todos)} incomplete TODOs")
            violations.extend(incomplete_todos[:5])  # Show first 5

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
            "incomplete_todos": incomplete_todos,
        }

    def _check_test_success(self) -> Dict[str, Any]:
        """Check test success compliance"""
        violations = []
        warnings = []

        # Check if tests exist and run them
        test_files = list(self.project_root.rglob("test_*.py")) + list(
            self.project_root.rglob("*_test.py")
        )

        if not test_files:
            warnings.append("No test files found")
            return {"status": "warning", "violations": violations, "warnings": warnings}

        # Try to run tests
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pytest", "--tb=short"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=60,
            )

            # Parse test results
            if "FAILED" in result.stdout or result.returncode != 0:
                violations.append("Tests are failing - 100% success rate required")
                self.critical_violations.append(
                    "Test failures detected - fix before proceeding"
                )

            # Extract success rate
            success_match = re.search(r"(\d+) passed", result.stdout)
            failed_match = re.search(r"(\d+) failed", result.stdout)

            if success_match and failed_match:
                passed = int(success_match.group(1))
                failed = int(failed_match.group(1))
                total = passed + failed
                success_rate = (passed / total) * 100 if total > 0 else 0

                if success_rate < 100:
                    violations.append(
                        f"Test success rate is {success_rate:.1f}% - 100% required"
                    )
                    self.critical_violations.append(
                        f"Test success rate below 100%: {success_rate:.1f}%"
                    )

                self.test_results = {
                    "passed": passed,
                    "failed": failed,
                    "total": total,
                    "success_rate": success_rate,
                }

        except subprocess.TimeoutExpired:
            warnings.append("Test execution timed out")
        except Exception as e:
            warnings.append(f"Could not run tests: {e}")

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
            "test_results": self.test_results,
        }

    def _check_dependency_management(self) -> Dict[str, Any]:
        """Check dependency management compliance"""
        violations = []
        warnings = []

        # Check if requirements.txt exists
        if not self.requirements_file.exists():
            violations.append("requirements.txt not found in config/ directory")
            self.critical_violations.append("Missing requirements.txt file")
        else:
            # Check for exact version pinning
            try:
                with open(self.requirements_file, "r") as f:
                    content = f.read()
                    lines = content.strip().split("\n")

                    for line in lines:
                        line = line.strip()
                        if line and not line.startswith("#") and "==" not in line:
                            warnings.append(
                                f"Dependency {line} should use exact version pinning (==)"
                            )

            except Exception as e:
                warnings.append(f"Could not read requirements.txt: {e}")

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
        }

    def _check_cli_api_compliance(self) -> Dict[str, Any]:
        """Check CLI and API creation compliance"""
        violations = []
        warnings = []

        # Check for CLI commands
        cli_dir = self.project_root / "cli"
        if not cli_dir.exists():
            violations.append("CLI directory not found")
        else:
            cli_files = list(cli_dir.glob("*.py"))
            if not cli_files:
                warnings.append("No CLI command files found")

        # Check for API routes
        api_dir = self.project_root / "api"
        if not api_dir.exists():
            violations.append("API directory not found")
        else:
            api_files = list(api_dir.glob("*_routes.py"))
            if not api_files:
                warnings.append("No API route files found")

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
        }

    def _check_ai_model_compliance(self) -> Dict[str, Any]:
        """Check AI model compliance"""
        violations = []
        warnings = []

        # Load configuration
        config_file = (
            self.project_root / "config" / "cursor_rules_enforcement_config.json"
        )
        current_mode = "local_only"

        try:
            with open(config_file, "r") as f:
                config = json.load(f)
                current_mode = config.get("ai_models", {}).get(
                    "current_mode", "local_only"
                )
        except Exception:
            pass

        # Check for cloud model usage based on current mode
        if current_mode == "local_only":
            cloud_model_patterns = [
                r"openai\.com",
                r"api\.anthropic\.com",
                r"gpt-",
                r"claude-",
                r"text-davinci",
                r"gpt-4",
                r"gpt-3\.5",
            ]

            for file_path in self.project_root.rglob("*.py"):
                if "venv" not in str(file_path) and "node_modules" not in str(
                    file_path
                ):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()
                            for pattern in cloud_model_patterns:
                                if re.search(pattern, content, re.IGNORECASE):
                                    warnings.append(
                                        f"Cloud model usage detected in {file_path} (future mode will allow this)"
                                    )
                    except Exception:
                        continue
        else:
            # In hybrid mode, just warn about usage
            warnings.append(
                "AI model compliance check is in hybrid mode - cloud models allowed"
            )

        return {
            "status": "passed" if not violations else "warning",
            "violations": violations,
            "warnings": warnings,
            "current_mode": current_mode,
        }

    def _check_git_workflow(self) -> Dict[str, Any]:
        """Check git workflow compliance"""
        violations = []
        warnings = []

        # Check repository location
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--show-toplevel"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            if result.returncode == 0:
                repo_path = result.stdout.strip()
                if (
                    "G:\\AICodingAgent" not in repo_path
                    and "G:/AICodingAgent" not in repo_path
                ):
                    warnings.append(
                        f"Repository should be in G:\\AICodingAgent, found in {repo_path}"
                    )
        except Exception:
            warnings.append("Could not determine git repository location")

        # Check for uncommitted changes
        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
            )

            if result.stdout.strip():
                warnings.append("There are uncommitted changes")
        except Exception:
            warnings.append("Could not check git status")

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
        }

    def _check_security_compliance(self) -> Dict[str, Any]:
        """Check security compliance"""
        violations = []
        warnings = []

        # Check for hardcoded secrets
        secret_patterns = [
            r"password\s*=\s*['\"][^'\"]+['\"]",
            r"api_key\s*=\s*['\"][^'\"]+['\"]",
            r"secret\s*=\s*['\"][^'\"]+['\"]",
            r"token\s*=\s*['\"][^'\"]+['\"]",
        ]

        for file_path in self.project_root.rglob("*.py"):
            if "venv" not in str(file_path) and "node_modules" not in str(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        for pattern in secret_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                warnings.append(
                                    f"Potential hardcoded secret in {file_path}"
                                )
                except Exception:
                    continue

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
        }

    def _check_file_cleanup(self) -> Dict[str, Any]:
        """Check file cleanup compliance"""
        violations = []
        warnings = []

        # Check for duplicate files
        file_hashes = {}
        duplicates = []

        for file_path in self.project_root.rglob("*.py"):
            if "venv" not in str(file_path) and "node_modules" not in str(file_path):
                try:
                    with open(file_path, "rb") as f:
                        content = f.read()
                        file_hash = hash(content)
                        if file_hash in file_hashes:
                            duplicates.append((file_hashes[file_hash], file_path))
                        else:
                            file_hashes[file_hash] = file_path
                except Exception:
                    continue

        if duplicates:
            warnings.append(f"Found {len(duplicates)} potential duplicate files")

        # Check for obsolete files (files with .old, .bak, .tmp extensions)
        obsolete_extensions = [".old", ".bak", ".tmp", ".backup"]
        obsolete_files = []

        for ext in obsolete_extensions:
            obsolete_files.extend(self.project_root.rglob(f"*{ext}"))

        if obsolete_files:
            warnings.append(f"Found {len(obsolete_files)} potentially obsolete files")

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
            "duplicates": len(duplicates),
            "obsolete_files": len(obsolete_files),
        }

    def _check_mock_data_cleanup(self) -> Dict[str, Any]:
        """Check mock data cleanup compliance"""
        violations = []
        warnings = []

        # Check for mock data files
        mock_patterns = [r"mock_", r"test_data", r"sample_", r"demo_", r"example_"]

        mock_files = []
        for pattern in mock_patterns:
            mock_files.extend(self.project_root.rglob(f"*{pattern}*"))

        # Filter out test files
        mock_files = [
            f
            for f in mock_files
            if "test" not in str(f).lower() and "tests" not in str(f)
        ]

        if mock_files:
            warnings.append(f"Found {len(mock_files)} potential mock data files")

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
            "mock_files": len(mock_files),
        }

    def _check_virtual_environment(self) -> Dict[str, Any]:
        """Check virtual environment compliance"""
        violations = []
        warnings = []

        # Check if virtual environment is activated
        if hasattr(sys, "real_prefix") or (
            hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
        ):
            # Virtual environment is active
            pass
        else:
            warnings.append(
                "Virtual environment not detected - ensure venv is activated for Python work"
            )

        return {
            "status": "passed" if not violations else "failed",
            "violations": violations,
            "warnings": warnings,
        }

    def _generate_recommendations(self, checks: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on check results"""
        recommendations = []

        for check_name, check_result in checks.items():
            if check_result.get("status") == "failed":
                if check_name == "test_success":
                    recommendations.append("Fix all test failures before proceeding")
                elif check_name == "todo_completion":
                    recommendations.append(
                        "Complete all pending TODOs before ending session"
                    )
                elif check_name == "dependency_management":
                    recommendations.append(
                        "Update requirements.txt with exact dependency versions"
                    )
                elif check_name == "cli_api_compliance":
                    recommendations.append(
                        "Create CLI commands and API routes for all features"
                    )
                elif check_name == "ai_model_compliance":
                    recommendations.append(
                        "Remove all cloud AI model usage - use local Ollama models only"
                    )

            if check_result.get("warnings"):
                # Show first 2 warnings
                for warning in check_result["warnings"][:2]:
                    recommendations.append(f"Warning: {warning}")

        return recommendations

    def pre_commit_check(self) -> bool:
        """Run pre-commit cursor rules check"""
        report = self.enforce_cursor_rules()

        if report["status"] == "blocked":
            print("❌ Cursor rules enforcement FAILED")
            print("Critical violations found:")
            for violation in report["critical_violations"]:
                print(f"   🚨 {violation}")
            print("\nRecommendations:")
            for rec in report["recommendations"]:
                print(f"   💡 {rec}")
            return False

        print(
            f"✅ Cursor rules enforcement PASSED: {report['compliance_score']:.1f}% compliance"
        )
        return True

    def get_compliance_report(self) -> Dict[str, Any]:
        """Get detailed compliance report"""
        return self.compliance_data

    def update_todo_status(self, todo_id: str, status: str, description: str = ""):
        """Update TODO status tracking"""
        self.todo_status[todo_id] = {
            "status": status,
            "description": description,
            "updated_at": datetime.now().isoformat(),
        }

    def check_todo_completion(self) -> bool:
        """Check if all TODOs are completed"""
        incomplete_todos = [
            todo_id
            for todo_id, data in self.todo_status.items()
            if data["status"] in ["pending", "in_progress"]
        ]

        if incomplete_todos:
            self.critical_violations.append(
                f"Found {len(incomplete_todos)} incomplete TODOs"
            )
            return False

        return True


def main():
    """Main function for command-line usage"""
    enforcer = CursorRulesEnforcer()

    if len(sys.argv) > 1 and sys.argv[1] == "pre-commit":
        success = enforcer.pre_commit_check()
        sys.exit(0 if success else 1)
    else:
        report = enforcer.enforce_cursor_rules()
        print(json.dumps(report, indent=2))


if __name__ == "__main__":
    main()
