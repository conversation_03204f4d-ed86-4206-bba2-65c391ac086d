"""
Unified Configuration System for AI Coding Agent.
Consolidates multiple configuration implementations into a single comprehensive solution.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional

try:
    from utils.config_loader import ConfigLoader

    # Create wrapper functions for backward compatibility
    def load_config(config_path: str) -> dict:
        return ConfigLoader.load_config_with_defaults(config_path, {})

    def get_default_config() -> dict:
        return {}

    def save_config(config_path: str, config: dict) -> bool:
        return ConfigLoader.save_config(config_path, config)

    def update_config(config_path: str, updates: dict) -> dict:
        current = load_config(config_path)
        current.update(updates)
        save_config(config_path, current)
        return current

except ImportError as e:
    print(f"Warning: Could not import from utils.config_loader: {e}")

    # Provide fallback implementations
    def load_config(config_path: str) -> dict:
        import json
        from pathlib import Path

        try:
            config_file = Path(config_path)
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def get_default_config() -> dict:
        return {}

    def save_config(config_path: str, config: dict) -> bool:
        import json
        from pathlib import Path

        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2)
            return True
        except Exception:
            return False

    def update_config(config_path: str, updates: dict) -> dict:
        current = load_config(config_path)
        current.update(updates)
        save_config(config_path, current)
        return current


try:
    from config.security import secure_config
except ImportError as e:
    print(f"Warning: Could not import from security: {e}")
    secure_config = None

__all__ = [
    "load_config",
    "get_default_config",
    "save_config",
    "update_config",
    "secure_config",
    "get_smart_routing_config",
    "get_agent_config",
    "list_available_configs",
]


class ConfigObject:
    """Configuration object that allows attribute access to dictionary values"""

    def __init__(self, config_dict: dict):
        for key, value in config_dict.items():
            if isinstance(value, dict):
                setattr(self, key, ConfigObject(value))
            else:
                setattr(self, key, value)

    def __getitem__(self, key):
        return getattr(self, key)

    def get(self, key, default=None):
        return getattr(self, key, default)


# Available configuration files
AVAILABLE_CONFIGS = {
    # Smart routing and core configs
    "smart_routing": "smart_routing_config.json",
    "architect": "architect_agent_config.json",
    "backend": "backend_agent_config.json",
    "frontend": "frontend_agent_config.json",
    "container": "container_agent_config.json",
    "shell_ops": "shell_ops_agent_config.json",
    "learning": "learning_agent_config.json",
    "security": "security_agent_config.json",
    "monitoring": "monitoring_agent_config.json",
    # Specialized configs
    "ai_models": "ai_models_config.json",
    "ollama": "ollama_config.json",
    "cms": "cms_config.json",
    "maintenance": "maintenance_config.json",
    "testing": "testing_config.json",
    "ssl": "ssl_config.json",
    "advanced_learning": "advanced_learning_config.json",
    "best_practices": "best_practices_config.json",
    "framework_monitor": "framework_monitor_config.json",
    "automated_learner": "automated_learner_config.json",
    # Security configs
    "advanced_security": "advanced_security_config.json",
    "llm_security": "llm_security_config.json",
    "advanced_threat_intelligence": "advanced_threat_intelligence_config.json",
    "container_security": "container_security_config.json",
    "zero_trust": "zero_trust_config.json",
    "safety": "safety_config.json",
    # Learning and optimization configs
    "meta_learning": "meta_learning_config.json",
    "pareto_optimization": "pareto_optimization_config.json",
    "workload_prediction": "workload_prediction_config.json",
    "cascade_prediction": "cascade_prediction_config.json",
    "federated_learning": "federated_learning_config.json",
    "capability_discovery": "capability_discovery_config.json",
    "adversarial_detection": "adversarial_detection_config.json",
    "degradation": "degradation_config.json",
    # Performance and monitoring configs
    "performance": "performance_config.json",
    "gpu_optimization": "gpu_optimization_config.json",
    "disaster_recovery": "disaster_recovery_config.json",
    "complex_tasks": "complex_tasks_config.json",
    "documentation_generation": "documentation_generation_config.json",
    "advanced_code_review": "advanced_code_review_config.json",
    "enhanced_code_generation": "enhanced_code_generation_config.json",
    "pipeline": "pipeline_config.json",
    "cli_web": "cli_web_config.json",
    "home_server": "home_server_config.json",
    "theme": "theme_config.json",
    "home_network": "home_network_config.json",
    # Docker and deployment configs
    "fine_tuner_docker": "fine_tuner_docker_config.json",
    "dashboard_docker": "dashboard_docker_config.json",
    "migration_docker": "migration_docker_config.json",
    "model_optimizer_docker": "model_optimizer_docker_config.json",
    "disaster_recovery_docker": "disaster_recovery_docker_config.json",
    "threat_detection_docker": "threat_detection_docker_config.json",
    "learning_docker": "learning_docker_config.json",
    # Other configs
    "collaboration": "collaboration_config.json",
    "error_watcher": "error_watcher_agent_config.json",
    "ast_generator": "ast_generator_config.json",
    "cursor_rules_enforcement": "cursor_rules_enforcement_config.json",
    "nas_integration": "nas_integration_config.json",
    "quantum_ready": "quantum_ready_config.json",
    "business_impact": "business_impact_config.json",
    "causal_analysis": "causal_analysis_config.json",
    "external_hosting": "external_hosting_config.json",
    "fine_tuning": "fine_tuning_config.json",
}


def get_smart_routing_config() -> ConfigObject:
    """Get the smart routing configuration specifically"""
    return get_config("smart_routing_config.json")


def get_agent_config(agent_type: str) -> ConfigObject:
    """Get configuration for a specific agent type"""
    config_file = AVAILABLE_CONFIGS.get(agent_type)
    if not config_file:
        raise ValueError(
            f"Unknown agent type: {agent_type}. Available types: {list(AVAILABLE_CONFIGS.keys())}"
        )
    return get_config(config_file)


def list_available_configs() -> Dict[str, str]:
    """List all available configuration files"""
    return AVAILABLE_CONFIGS.copy()


# Convenience function for backward compatibility
def get_config(config_file: str = "config.json"):
    """Load configuration from JSON file - backward compatibility"""
    import os
    from pathlib import Path

    # Try multiple path resolution strategies
    config_paths = [
        Path(config_file),  # Absolute or relative to current directory
        Path(__file__).parent / config_file,  # Relative to config directory
        Path.cwd() / config_file,  # Relative to current working directory
    ]

    for config_path in config_paths:
        if config_path.exists():
            config_dict = load_config(str(config_path))
            return ConfigObject(config_dict)

    # If no file found, raise error with all attempted paths
    raise FileNotFoundError(
        f"Config file not found: {config_file}. Tried paths: {[str(p) for p in config_paths]}"
    )
