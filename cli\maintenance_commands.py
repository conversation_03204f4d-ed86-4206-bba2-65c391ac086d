"""
CLI Commands for Phase 2.4: Maintenance Engine
Provides commands for broken-link checking, dependency updates, and maintenance tasks.
Integrates with Phase 2.3 CMS & Content for comprehensive site maintenance.
"""

import json
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.table import Table

from content.cms_content_manager import ContentManager
from core.maintenance_engine import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    DependencyManager,
    MaintenanceEngine,
)

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))


class MaintenanceCommands:
    """CLI commands for maintenance engine"""

    def __init__(self, config_path: str = "config/maintenance_config.json"):
        self.config_path = config_path
        self.engine: Optional[MaintenanceEngine] = None
        self.cms_manager: Optional[ContentManager] = None
        self._initialize_engine()

    def _initialize_engine(self):
        """Initialize the maintenance engine"""
        try:
            # Initialize Maintenance Engine
            self.engine = MaintenanceEngine(self.config_path)
            print(" Maintenance Engine initialized")

            # Initialize CMS integration if available
            try:
                self.cms_manager = ContentManager("config/cms_config.json")
                # Connect CMS to link checker
                if (
                    hasattr(self.engine, "link_checker")
                    and self.engine.link_checker is not None
                ):
                    setattr(self.engine.link_checker, "cms_manager", self.cms_manager)
                print(" CMS integration enabled")
            except Exception as e:
                print(f" CMS integration failed: {e}")
        except Exception as e:
            print(f" Error initializing Maintenance Engine: {e}")

    def check_links(self, priority: str = "medium", include_cms: bool = True):
        """Check for broken links"""
        if not self.engine or not self.engine.link_checker:
            print("Maintenance Engine or link checker not initialized")
            return False

        try:
            print("Starting link check...")

            # Schedule link check task
            task_id = self.engine.schedule_link_check(priority)
            print(f"Scheduled link check task: {task_id}")

            # Wait for task to complete (in a real scenario, this would be async)
            print("Waiting for link check to complete...")
            time.sleep(2)  # Simulate task execution

            # Get link health report
            link_report = self.engine.link_checker.get_broken_links_report()

            print(f" Link check completed")
            print(f"  Results:")
            print(f"   Total links: {link_report.get('total_links', 0)}")
            print(f"   Valid links: {link_report.get('valid_links', 0)}")
            print(f"   Broken links: {link_report.get('broken_links', 0)}")
            print(f"   Health: {link_report.get('health_percentage', 0):.1f}%")

            # Show broken links if any
            broken_links = link_report.get("broken_links_list", [])
            if broken_links:
                print(f"\n Broken links found:")
                for link in broken_links[:10]:  # Show first 10
                    print(f"   - {link['url']} (Status: {link['status_code']})")
                    if link.get("source_file"):
                        print(f"     Source: {link['source_file']}")
                if len(broken_links) > 10:
                    print(f"   ... and {len(broken_links) - 10} more")
            else:
                print(" No broken links found!")

            return True

        except Exception as e:
            print(f" Error during link check: {e}")
            return False

    def check_dependencies(self, priority: str = "medium"):
        """Check for dependency updates"""
        if not self.engine or not self.engine.dependency_manager:
            print(" Maintenance Engine or dependency manager not initialized")
            return False

        try:
            print(" Checking for dependency updates...")

            # Schedule dependency check task
            task_id = self.engine.schedule_dependency_check(priority)
            print(f" Scheduled dependency check task: {task_id}")

            # Wait for task to complete
            print(" Waiting for dependency check to complete...")
            time.sleep(2)  # Simulate task execution

            # Get dependency updates
            updates = self.engine.dependency_manager.check_python_dependencies()

            print(f" Dependency check completed")
            print(f" Results:")
            print(f"   Total updates found: {len(updates)}")

            if updates:
                print(f"\n Available updates:")
                for update in updates:
                    print(f"   - {update.package_name}")
                    print(f"     Current: {update.current_version}")
                    print(f"     Available: {update.available_version}")
                    print(f"     Type: {update.update_type}")
                    print(f"     Security: {'Yes' if update.security_impact else 'No'}")
                    print(
                        f"     Breaking: {'Yes' if update.breaking_changes else 'No'}"
                    )
                    print(
                        f"     Approval: {'Required' if update.approval_required else 'Auto'}"
                    )
                    print()
            else:
                print(" All dependencies are up to date!")

            return True

        except Exception as e:
            print(f" Error during dependency check: {e}")
            return False

    def apply_dependency_update(self, package_name: str, user_id: str = "cli_user"):
        """Apply a dependency update"""
        if not self.engine or not self.engine.dependency_manager:
            print(" Maintenance Engine or dependency manager not initialized")
            return False

        try:
            print(f" Applying update for {package_name}...")

            success = self.engine.dependency_manager.apply_dependency_update(
                package_name, user_id
            )

            if success:
                print(f" Successfully applied update for {package_name}")
            else:
                print(f" Failed to apply update for {package_name}")

            return success

        except Exception as e:
            print(f" Error applying dependency update: {e}")
            return False

    def get_maintenance_status(self):
        """Get overall maintenance status"""
        if not self.engine:
            print(" Maintenance Engine not initialized")
            return False

        try:
            print(" Getting maintenance status...")

            status = self.engine.get_maintenance_status()

            print(f" Maintenance Status:")
            print(f"   Last updated: {status.get('last_updated', 'Unknown')}")

            # Task statistics
            task_stats = status.get("task_statistics", {})
            if task_stats:
                print(f"\n Task Statistics:")
                for task_type, stats in task_stats.items():
                    print(f"   {task_type}:")
                    for status_name, count in stats.items():
                        print(f"     {status_name}: {count}")

            # Link health
            link_health = status.get("link_health", {})
            if link_health:
                print(f"\n Link Health:")
                print(f"   Total links: {link_health.get('total_links', 0)}")
                print(f"   Valid links: {link_health.get('valid_links', 0)}")
                print(f"   Broken links: {link_health.get('broken_links', 0)}")
                print(f"   Health: {link_health.get('health_percentage', 0):.1f}%")

            # Pending updates
            pending_updates = status.get("pending_dependency_updates", 0)
            print(f"\n Pending dependency updates: {pending_updates}")

            # Recent tasks
            recent_tasks = status.get("recent_tasks", [])
            if recent_tasks:
                print(f"\n Recent Tasks:")
                for task in recent_tasks[:5]:  # Show last 5
                    print(
                        f"   - {task['task_type']} ({task['status']}) - {task['created_at']}"
                    )

            return True

        except Exception as e:
            print(f" Error getting maintenance status: {e}")
            return False

    def approve_task(self, task_id: str, user_id: str = "cli_user"):
        """Approve a maintenance task"""
        if not self.engine:
            print(" Maintenance Engine not initialized")
            return False

        try:
            print(f" Approving task {task_id}...")

            success = self.engine.approve_task(task_id, user_id)

            if success:
                print(f" Task {task_id} approved successfully")
            else:
                print(f" Failed to approve task {task_id}")

            return success

        except Exception as e:
            print(f"Error approving task: {e}")
            return False

    def run_full_maintenance(self):
        """Run full maintenance cycle"""
        if not self.engine:
            print(" Maintenance Engine not initialized")
            return False

        try:
            print(" Starting full maintenance cycle...")

            # Step 1: Link check
            print("\n Step 1: Checking links...")
            self.check_links()

            # Step 2: Dependency check
            print("\n Step 2: Checking dependencies...")
            self.check_dependencies()

            # Step 3: Get overall status
            print("\n Step 3: Getting maintenance status...")
            self.get_maintenance_status()

            print("\n Full maintenance cycle completed!")
            return True

        except Exception as e:
            print(f" Error during full maintenance cycle: {e}")
            return False

    def audit_cms_content(self):
        """Audit CMS content for maintenance issues"""
        if not self.cms_manager:
            print(" CMS Manager not available")
            return False

        try:
            print(" Auditing CMS content...")

            # Get all content
            all_content = self.cms_manager.list_content()

            print(f" CMS Content Audit:")
            print(f"   Total content items: {len(all_content)}")

            # Content type breakdown
            type_counts = {}
            for item in all_content:
                type_counts[item.content_type] = (
                    type_counts.get(item.content_type, 0) + 1
                )

            print(f"\n Content by type:")
            for content_type, count in type_counts.items():
                print(f"   {content_type}: {count}")

            # Status breakdown
            status_counts = {}
            for item in all_content:
                status_counts[item.status] = status_counts.get(item.status, 0) + 1

            print(f"\n Content by status:")
            for status, count in status_counts.items():
                print(f"   {status}: {count}")

            # Check for content issues
            issues = []
            for item in all_content:
                # Check content length
                if len(item.content) < 50:
                    issues.append(
                        f"Short content: {item.title} ({len(item.content)} chars)"
                    )

                # Check for missing tags
                if not item.tags:
                    issues.append(f"No tags: {item.title}")

                # Check for old content
                days_old = (datetime.now() - item.updated_at).days
                if days_old > 365:
                    issues.append(f"Old content: {item.title} ({days_old} days old)")

            if issues:
                print(f"\n Content issues found:")
                for issue in issues[:10]:  # Show first 10
                    print(f"   - {issue}")
                if len(issues) > 10:
                    print(f"   ... and {len(issues) - 10} more issues")
            else:
                print(" No content issues found!")

            return True

        except Exception as e:
            print(f" Error during CMS content audit: {e}")
            return False

    def generate_maintenance_report(self, output_file: Optional[str] = None):
        """Generate comprehensive maintenance report"""
        if not self.engine or not self.engine.link_checker:
            print(" Maintenance Engine or link checker not initialized")
            return False

        try:
            print(" Generating maintenance report...")

            # Collect all data
            status = self.engine.get_maintenance_status()
            link_report = self.engine.link_checker.get_broken_links_report()

            # Create report
            report = {
                "generated_at": datetime.now().isoformat(),
                "maintenance_status": status,
                "link_health": link_report,
                "cms_audit": {},
            }

            # Add CMS audit if available
            if self.cms_manager:
                try:
                    all_content = self.cms_manager.list_content()
                    report["cms_audit"] = {
                        "total_items": len(all_content),
                        "type_breakdown": {},
                        "status_breakdown": {},
                        "issues": [],
                    }

                    # Type breakdown
                    for item in all_content:
                        report["cms_audit"]["type_breakdown"][item.content_type] = (
                            report["cms_audit"]["type_breakdown"].get(
                                item.content_type, 0
                            )
                            + 1
                        )

                    # Status breakdown
                    for item in all_content:
                        report["cms_audit"]["status_breakdown"][item.status] = (
                            report["cms_audit"]["status_breakdown"].get(item.status, 0)
                            + 1
                        )

                except Exception as e:
                    report["cms_audit"]["error"] = str(e)

            # Save report
            if output_file:
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(report, f, indent=2, default=str)
                print(f" Report saved to {output_file}")
            else:
                # Print summary
                print(f" Maintenance Report Generated:")
                print(f"   Generated: {report['generated_at']}")
                print(
                    f"   Link health: {report['link_health'].get('health_percentage', 0):.1f}%"
                )
                print(f"   CMS items: {report['cms_audit'].get('total_items', 0)}")
                print(
                    f"   Pending updates: {status.get('pending_dependency_updates', 0)}"
                )

            return True

        except Exception as e:
            print(f" Error generating maintenance report: {e}")
            return False

    def schedule_maintenance_tasks(self):
        """Schedule regular maintenance tasks"""
        if not self.engine:
            print(" Maintenance Engine not initialized")
            return False

        try:
            print(" Scheduling maintenance tasks...")

            # Schedule link check
            link_task_id = self.engine.schedule_link_check("medium")
            print(f" Scheduled link check: {link_task_id}")

            # Schedule dependency check
            dep_task_id = self.engine.schedule_dependency_check("medium")
            print(f" Scheduled dependency check: {dep_task_id}")

            print(" Maintenance tasks scheduled successfully!")
            return True

        except Exception as e:
            print(f" Error scheduling maintenance tasks: {e}")
            return False


def main():
    """Main CLI entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="Maintenance Engine CLI")
    parser.add_argument(
        "command",
        choices=[
            "check-links",
            "check-dependencies",
            "apply-update",
            "status",
            "approve-task",
            "full-maintenance",
            "audit-cms",
            "generate-report",
            "schedule",
        ],
    )
    parser.add_argument(
        "--priority", choices=["low", "medium", "high", "critical"], default="medium"
    )
    parser.add_argument("--package", help="Package name for dependency updates")
    parser.add_argument("--task-id", help="Task ID for approval")
    parser.add_argument("--user-id", default="cli_user", help="User ID for actions")
    parser.add_argument("--output", help="Output file for reports")
    parser.add_argument(
        "--config", default="config/maintenance_config.json", help="Config file path"
    )

    args = parser.parse_args()

    # Initialize commands
    commands = MaintenanceCommands(args.config)

    # Execute command
    if args.command == "check-links":
        commands.check_links(args.priority)
    elif args.command == "check-dependencies":
        commands.check_dependencies(args.priority)
    elif args.command == "apply-update":
        if not args.package:
            print(" Package name required for apply-update")
            sys.exit(1)
        commands.apply_dependency_update(args.package, args.user_id)
    elif args.command == "status":
        commands.get_maintenance_status()
    elif args.command == "approve-task":
        if not args.task_id:
            print(" Task ID required for approve-task")
            sys.exit(1)
        commands.approve_task(args.task_id, args.user_id)
    elif args.command == "full-maintenance":
        commands.run_full_maintenance()
    elif args.command == "audit-cms":
        commands.audit_cms_content()
    elif args.command == "generate-report":
        commands.generate_maintenance_report(args.output)
    elif args.command == "schedule":
        commands.schedule_maintenance_tasks()


if __name__ == "__main__":
    main()
