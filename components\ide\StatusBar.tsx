import React, { useEffect, useState } from 'react';
import { Wifi, WifiOff, Circle, GitBranch } from 'lucide-react';
import { deploymentService } from '@/services/DeploymentService';
import { sslService } from '@/services/SSLService';
import { maintenanceService } from '@/services/MaintenanceService';
import { performanceService } from '@/services/PerformanceService';
import { testingService } from '@/services/TestingService';
import { errorHandlingService } from '@/services/ErrorHandlingService';
import { Loader2, CheckCircle, XCircle, UploadCloud, Shield, AlertTriangle, Activity, Zap, TestTube, AlertCircle } from 'lucide-react';

interface StatusBarProps {
  className?: string;
}

export const StatusBar: React.FC<StatusBarProps> = ({ className = '' }) => {
  const [isOnline, setIsOnline] = React.useState(true);
  const [encoding, setEncoding] = React.useState('UTF-8');
  const [language, setLanguage] = React.useState('HTML');
  const [deployStatus, setDeployStatus] = useState(deploymentService.getStatus());
  const [sslStatus, setSslStatus] = useState(sslService.getStatus());
  const [maintenanceStatus, setMaintenanceStatus] = useState(maintenanceService.getCurrentStatus());
  const [performanceMetrics, setPerformanceMetrics] = useState(performanceService.getMetrics());
  const [testResults, setTestResults] = useState(testingService.getTestResults());
  const [errorStats, setErrorStats] = useState(errorHandlingService.getErrorStats());

  React.useEffect(() => {
    const checkOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    window.addEventListener('online', checkOnlineStatus);
    window.addEventListener('offline', checkOnlineStatus);

    return () => {
      window.removeEventListener('online', checkOnlineStatus);
      window.removeEventListener('offline', checkOnlineStatus);
    };
  }, []);

  useEffect(() => {
    const interval = setInterval(async () => {
      setDeployStatus(deploymentService.getStatus());
      setSslStatus(sslService.getStatus());
      setMaintenanceStatus(maintenanceService.getCurrentStatus());
      setPerformanceMetrics(performanceService.getMetrics());
      setTestResults(testingService.getTestResults());
      setErrorStats(errorHandlingService.getErrorStats());
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const interval = setInterval(async () => {
      setDeployStatus(deploymentService.getStatus());
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const getHealthIcon = () => {
    switch (maintenanceStatus.overallHealth) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'critical':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-400" />;
    }
  };

  const getSSLIcon = () => {
    if (sslStatus.enabled && sslStatus.certificateValid) {
      return <Shield className="w-4 h-4 text-green-500" />;
    } else if (sslStatus.enabled && !sslStatus.certificateValid) {
      return <Shield className="w-4 h-4 text-yellow-500" />;
    } else {
      return <Shield className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPerformanceIcon = () => {
    const loadTime = performanceMetrics.pageLoadTime;
    if (loadTime < 1000) {
      return <Zap className="w-4 h-4 text-green-500" />;
    } else if (loadTime < 2000) {
      return <Zap className="w-4 h-4 text-yellow-500" />;
    } else {
      return <Zap className="w-4 h-4 text-red-500" />;
    }
  };

  const getTestIcon = () => {
    const passedTests = testResults.filter(t => t.status === 'passed').length;
    const totalTests = testResults.length;
    const passRate = totalTests > 0 ? passedTests / totalTests : 0;

    if (passRate >= 0.9) {
      return <TestTube className="w-4 h-4 text-green-500" />;
    } else if (passRate >= 0.7) {
      return <TestTube className="w-4 h-4 text-yellow-500" />;
    } else {
      return <TestTube className="w-4 h-4 text-red-500" />;
    }
  };

  const getErrorIcon = () => {
    if (errorStats.unresolved === 0) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    } else if (errorStats.unresolved <= 3) {
      return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    } else {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  return (
    <div className="flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          {isOnline ? (
            <Wifi className="w-4 h-4 text-green-500" />
          ) : (
            <WifiOff className="w-4 h-4 text-red-500" />
          )}
          <span>{isOnline ? 'Online' : 'Offline'}</span>
        </div>
        <div className="flex items-center space-x-1">
          <Circle className="w-4 h-4 text-green-500" />
          <span>Ready</span>
        </div>
        <div className="flex items-center space-x-1">
          <GitBranch className="w-4 h-4" />
          <span>main</span>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <span>Ln 1, Col 1</span>
        </div>
        <div className="flex items-center space-x-1">
          <span>{encoding}</span>
        </div>
        <div className="flex items-center space-x-1">
          <span>TypeScript</span>
        </div>
        <div className="flex items-center gap-2">
          {deployStatus.status === 'deploying' && <Loader2 className="w-4 h-4 animate-spin text-blue-500" />}
          {deployStatus.status === 'success' && <CheckCircle className="w-4 h-4 text-green-500" />}
          {deployStatus.status === 'error' && <XCircle className="w-4 h-4 text-red-500" />}
          {deployStatus.status === 'idle' && <UploadCloud className="w-4 h-4 text-gray-400" />}
          <span>{deployStatus.message}</span>
        </div>
        <div className="flex items-center gap-2">
          {getSSLIcon()}
          <span>{sslStatus.domain}</span>
        </div>
        <div className="flex items-center gap-2">
          {getHealthIcon()}
          <span>{maintenanceStatus.overallHealth}</span>
        </div>
        <div className="flex items-center gap-2">
          {getPerformanceIcon()}
          <span>{performanceMetrics.pageLoadTime}ms</span>
        </div>
        <div className="flex items-center gap-2">
          {getTestIcon()}
          <span>{testResults.filter(t => t.status === 'passed').length}/{testResults.length}</span>
        </div>
        <div className="flex items-center gap-2">
          {getErrorIcon()}
          <span>{errorStats.unresolved}</span>
        </div>
      </div>
    </div>
  );
};
