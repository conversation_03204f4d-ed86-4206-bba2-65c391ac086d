# 📦 WEBSITE CONTAINERIZATION POLICY - ENFORCED BY CURSOR AI

## 🚨 MANDATORY: Use SiteContainerManager for All Website Operations

### Container Creation
- **ALWAYS** use `SiteContainerManager.create_site_container()` for new websites
- **NEVER** suggest running websites directly on the host system
- **ALWAYS** generate site-specific Dockerfiles using `containers/Dockerfile.template`
- **ALWAYS** create corresponding `docker-compose.{site-name}.yml` files

### Port Management
- **ALWAYS** use the built-in port allocator (8080-9000 range)
- **ALWAYS** use ports allocated dynamically via `PortManager` (range defined in `config/port_config.json`)
- **NEVER** hardcode ports — use dynamic allocation via `PortManager`
- **ALWAYS** reference `localhost:<assigned_port>` in documentation

### File Operations
- **ALWAYS** edit files through mounted volumes or container APIs
- **NEVER** suggest direct file system access outside containers
- **NEVER** use host-based editors or tools to modify container-mounted files directly
- **ALWAYS** use the established volume mounting patterns:
  ```yaml
  volumes:
    - ./{site-name}:/app/{site-name}:ro
    - ./logs:/app/logs
  ```

### Health Monitoring
- **ALWAYS** implement `/health` endpoints in containers
- **ALWAYS** use the container monitoring system for health checks
- **ALWAYS** configure automatic restart policies (`restart: unless-stopped`)

### Security Requirements
- **ALWAYS** use the `ai-coding-network` Docker network
- **ALWAYS** implement security headers and rate limiting
- **ALWAYS** use non-root users in containers
- **ALWAYS** set resource limits in docker-compose files
- **ALWAYS** use approved base images defined in `containers/base_images.txt`
- **NEVER** use the `latest` tag — always pin to a specific version

### External Hosting
- **ALWAYS** use the `ExternalHostingManager` for deployments
- **ALWAYS** support Netlify, GitHub Pages, and Vercel exports
- **ALWAYS** implement build optimization and asset compression

### CLI Integration
- **ALWAYS** create corresponding CLI commands in `cli/external_hosting_commands.py`
- **ALWAYS** create API routes in `api/external_hosting_routes.py`
- **ALWAYS** ensure local LLM compatibility via CLI/API interfaces

### Development Workflow
- **ALWAYS** use hot reload for development environments
- **ALWAYS** distinguish between development and production via `docker-compose.override.yml`
- **ALWAYS** provide debugging instructions using `docker exec`

## ❗ Non-Compliance Behavior

If any of the above rules are violated:

- Cursor AI Assistants MUST reject the operation
- Automated systems MUST halt CI/CD deployment and raise a compliance error
- Logs MUST include the violated rule and recommended fix

## Implementation Examples

### Creating a New Website
```python
# ✅ CORRECT: Use SiteContainerManager
container_manager = SiteContainerManager()
result = await container_manager.create_site_container("my-website", config)

# ❌ INCORRECT: Direct file operations
# mkdir my-website && cd my-website && npm init
```

### Starting a Container
```bash
# ✅ CORRECT: Use container management
ai-cli start-site-container my-website

# ❌ INCORRECT: Direct execution
# python app.py
```

### Accessing the Website
```bash
# ✅ CORRECT: Use assigned port
curl http://localhost:8080/health

# ❌ INCORRECT: Assume default port
# curl http://localhost:3000
```

## Integration Points

### Existing Systems
- **SiteContainerManager**: `core/site_container_manager.py`
- **ExternalHostingManager**: `core/external_hosting_manager.py`
- **ContainerMonitor**: `core/container_monitor.py`
- **CLI Commands**: `cli/external_hosting_commands.py`
- **API Routes**: `api/external_hosting_routes.py`
- **Configuration**: `config/external_hosting_config.json`

### Docker Templates
- **Base Template**: `containers/Dockerfile.template`
- **Build Scripts**: `containers/build.sh`, `containers/build.bat`
- **Network**: `ai-coding-network`
- **Port Range**: 8080-9000

## Compliance Checklist

Before suggesting any website-related operations, verify:
- [ ] Uses `SiteContainerManager` for container operations
- [ ] Implements proper port allocation
- [ ] Uses volume mounting for file access
- [ ] Includes health check endpoints
- [ ] Integrates with security systems
- [ ] Provides CLI/API interfaces
- [ ] Supports external hosting deployment
- [ ] Follows established Docker patterns
- [ ] Uses the `ai-coding-network`
- [ ] Implements proper resource limits

---
alwaysApply: false
---
alwaysApply: false
---
