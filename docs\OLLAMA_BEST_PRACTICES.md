# 🚀 Ollama Model Best Practices & Optimization Guide

## Overview
This guide provides comprehensive strategies and best practices for optimizing Ollama model performance in the AI Coding Agent project.

## 📋 **Model Selection & Configuration**

### **1. Model-Specific Optimizations**

#### **DeepSeek Coder (6.7B) - Primary Code Generation**
```json
{
  "temperature": 0.3,
  "top_p": 0.9,
  "top_k": 40,
  "max_tokens": 4096,
  "mirostat": 2,
  "mirostat_tau": 5.0,
  "mirostat_eta": 0.1
}
```
**Best Use Cases:**
- Complex code generation
- Bug fixing and debugging
- Architecture design
- Multi-file projects

**Optimization Tips:**
- Use lower temperature (0.3) for consistent code generation
- Enable mirostat for better response consistency
- Use larger context windows for complex projects

#### **Yi Coder (1.5B) - Fast Analysis**
```json
{
  "temperature": 0.4,
  "top_p": 0.85,
  "top_k": 30,
  "max_tokens": 2048,
  "mirostat": 1,
  "mirostat_tau": 3.0
}
```
**Best Use Cases:**
- Quick code reviews
- Intent parsing
- Simple code modifications
- Real-time suggestions

**Optimization Tips:**
- Use for quick, lightweight tasks
- Lower token limits for faster responses
- Higher concurrent request limits

#### **Qwen2.5 (3B) - Content Creation**
```json
{
  "temperature": 0.7,
  "top_p": 0.95,
  "top_k": 50,
  "max_tokens": 3072,
  "mirostat": 2,
  "mirostat_tau": 4.0
}
```
**Best Use Cases:**
- Documentation generation
- Blog writing
- Content creation
- Explanations

**Optimization Tips:**
- Higher temperature for creative content
- Larger token limits for longer content
- Use for content-heavy tasks

#### **StarCoder2 (3B) - Advanced Code**
```json
{
  "temperature": 0.5,
  "top_p": 0.9,
  "top_k": 40,
  "max_tokens": 4096,
  "mirostat": 2,
  "mirostat_tau": 4.5
}
```
**Best Use Cases:**
- Advanced code generation
- Complex problem solving
- Refactoring
- Architecture design

**Optimization Tips:**
- Balanced temperature for creativity and consistency
- Large context windows for complex projects
- Use for sophisticated coding tasks

#### **Mistral (7B) - General Assistance**
```json
{
  "temperature": 0.6,
  "top_p": 0.9,
  "top_k": 40,
  "max_tokens": 3072,
  "mirostat": 2,
  "mirostat_tau": 4.0
}
```
**Best Use Cases:**
- General assistance
- Teaching and explanations
- Problem solving
- Conversation

**Optimization Tips:**
- Balanced parameters for versatility
- Good for educational content
- Use for general-purpose tasks

## 🔧 **System-Level Optimizations**

### **1. Memory Management**
```bash
# Monitor memory usage
free -h
htop

# Optimize memory allocation
export OLLAMA_HOST=0.0.0.0
export OLLAMA_ORIGINS=*
export OLLAMA_MODELS=/path/to/models
```

### **2. GPU Optimization**
```bash
# Check GPU availability
nvidia-smi

# Optimize GPU layers
# For 6-8GB VRAM: 35 layers
# For 4-6GB VRAM: 25 layers
# For 2-4GB VRAM: 15 layers
```

### **3. Network Optimization**
```bash
# Test API latency
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:11434/api/tags"

# Optimize network settings
# Increase TCP buffer sizes
# Use keep-alive connections
# Implement connection pooling
```

## 📝 **Prompt Engineering Best Practices**

### **1. System Prompts**
```python
# Code Generation System Prompt
system_prompt = """You are an expert software developer with deep knowledge of multiple programming languages and frameworks.

Your responses should:
- Be clear, efficient, and well-documented
- Follow best practices and design patterns
- Include error handling and edge cases
- Be production-ready and maintainable
- Use modern language features when appropriate

Always provide complete, runnable code examples."""
```

### **2. Context Management**
```python
# Context compression for long conversations
def compress_context(context: str, max_tokens: int = 4000) -> str:
    """Compress context to fit within token limits."""
    if len(context) <= max_tokens:
        return context

    # Keep important parts (system prompt, recent messages)
    important_parts = context[:1000]  # System prompt
    recent_parts = context[-2000:]    # Recent messages

    return important_parts + "\n...\n" + recent_parts
```

### **3. Few-Shot Learning**
```python
# Example few-shot prompt
few_shot_prompt = """
Example 1:
Input: Create a Python function to calculate fibonacci numbers
Output:
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

Example 2:
Input: Create a React component for a todo list
Output:
function TodoList() {
    const [todos, setTodos] = useState([]);
    // ... component implementation
}

Now create: {user_request}
"""
```

## 💾 **Caching Strategies**

### **1. Response Caching**
```python
import hashlib
import json
from typing import Dict, Any

class ModelResponseCache:
    def __init__(self, max_size: int = 200, ttl_seconds: int = 7200):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds

    def get_cache_key(self, model: str, prompt: str, params: Dict[str, Any]) -> str:
        """Generate cache key from model, prompt, and parameters."""
        content = f"{model}:{prompt}:{json.dumps(params, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()

    def get(self, key: str) -> Optional[str]:
        """Get cached response if valid."""
        if key in self.cache:
            entry = self.cache[key]
            if time.time() - entry["timestamp"] < self.ttl_seconds:
                return entry["response"]
            else:
                del self.cache[key]
        return None

    def set(self, key: str, response: str):
        """Cache response with timestamp."""
        if len(self.cache) >= self.max_size:
            # Remove oldest entry
            oldest_key = min(self.cache.keys(),
                           key=lambda k: self.cache[k]["timestamp"])
            del self.cache[oldest_key]

        self.cache[key] = {
            "response": response,
            "timestamp": time.time()
        }
```

### **2. Model Loading Caching**
```python
class ModelLoader:
    def __init__(self):
        self.loaded_models = {}
        self.load_times = {}

    async def load_model(self, model_name: str) -> bool:
        """Load model with caching."""
        if model_name in self.loaded_models:
            return True

        try:
            # Load model via Ollama API
            response = requests.post(f"{OLLAMA_URL}/api/pull",
                                   json={"name": model_name})
            if response.status_code == 200:
                self.loaded_models[model_name] = True
                self.load_times[model_name] = time.time()
                return True
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
            return False
```

## 🔄 **Load Balancing & Fallback**

### **1. Intelligent Model Selection**
```python
class ModelSelector:
    def __init__(self, config: Dict[str, Any]):
        self.models = config["models"]
        self.performance_metrics = {}

    def select_model(self, task_type: str, priority: str = "balanced") -> str:
        """Select best model based on task type and performance."""
        candidates = []

        for model_name, config in self.models.items():
            if task_type in config["use_cases"]:
                performance = self.get_model_performance(model_name)
                candidates.append((model_name, performance))

        if priority == "speed":
            return min(candidates, key=lambda x: x[1]["avg_response_time"])[0]
        elif priority == "quality":
            return max(candidates, key=lambda x: x[1]["success_rate"])[0]
        else:
            # Balanced selection
            return self._balanced_selection(candidates)

    def _balanced_selection(self, candidates: List[Tuple[str, Dict]]) -> str:
        """Select model with balanced performance metrics."""
        best_score = 0
        best_model = candidates[0][0]

        for model_name, metrics in candidates:
            score = (
                metrics["success_rate"] * 0.5 +
                (1 / metrics["avg_response_time"]) * 0.3 +
                (1 - metrics["error_rate"]) * 0.2
            )
            if score > best_score:
                best_score = score
                best_model = model_name

        return best_model
```

### **2. Fallback Strategy**
```python
class FallbackManager:
    def __init__(self, config: Dict[str, Any]):
        self.fallback_chains = config["fallback_strategy"]

    async def execute_with_fallback(self, task: Dict[str, Any]) -> str:
        """Execute task with automatic fallback."""
        primary_model = task["model"]
        fallback_models = self.fallback_chains.get(primary_model, [])

        # Try primary model
        try:
            response = await self._call_model(primary_model, task)
            if response["success"]:
                return response["content"]
        except Exception as e:
            logger.warning(f"Primary model {primary_model} failed: {e}")

        # Try fallback models
        for fallback_model in fallback_models:
            try:
                response = await self._call_model(fallback_model, task)
                if response["success"]:
                    logger.info(f"Used fallback model: {fallback_model}")
                    return response["content"]
            except Exception as e:
                logger.warning(f"Fallback model {fallback_model} failed: {e}")

        raise Exception("All models failed")
```

## 📊 **Performance Monitoring**

### **1. Metrics Collection**
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}

    def record_request(self, model: str, response_time: float,
                      success: bool, tokens_used: int):
        """Record performance metrics."""
        if model not in self.metrics:
            self.metrics[model] = {
                "total_requests": 0,
                "successful_requests": 0,
                "total_response_time": 0,
                "total_tokens": 0,
                "errors": []
            }

        self.metrics[model]["total_requests"] += 1
        self.metrics[model]["total_response_time"] += response_time
        self.metrics[model]["total_tokens"] += tokens_used

        if success:
            self.metrics[model]["successful_requests"] += 1
        else:
            self.metrics[model]["errors"].append({
                "timestamp": time.time(),
                "response_time": response_time
            })

    def get_model_performance(self, model: str) -> Dict[str, Any]:
        """Get performance metrics for a model."""
        if model not in self.metrics:
            return {}

        metrics = self.metrics[model]
        total_requests = metrics["total_requests"]

        return {
            "success_rate": metrics["successful_requests"] / total_requests,
            "avg_response_time": metrics["total_response_time"] / total_requests,
            "avg_tokens_per_request": metrics["total_tokens"] / total_requests,
            "error_rate": len(metrics["errors"]) / total_requests,
            "total_requests": total_requests
        }
```

### **2. Health Checks**
```python
class HealthChecker:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        self.ollama_url = ollama_url

    async def check_model_health(self, model: str) -> Dict[str, Any]:
        """Check health of specific model."""
        try:
            # Test model with simple prompt
            test_prompt = "Hello"
            start_time = time.time()

            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": model,
                    "prompt": test_prompt,
                    "stream": False
                },
                timeout=30
            )

            response_time = time.time() - start_time

            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "response_time": response_time,
                    "error": None
                }
            else:
                return {
                    "status": "error",
                    "response_time": response_time,
                    "error": f"HTTP {response.status_code}"
                }
        except Exception as e:
            return {
                "status": "error",
                "response_time": None,
                "error": str(e)
            }
```

## 🛠️ **Resource Management**

### **1. Memory Optimization**
```python
class MemoryManager:
    def __init__(self, max_memory_percent: int = 85):
        self.max_memory_percent = max_memory_percent

    def check_memory_usage(self) -> Dict[str, Any]:
        """Check current memory usage."""
        memory = psutil.virtual_memory()
        return {
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_percent": memory.percent,
            "is_critical": memory.percent > self.max_memory_percent
        }

    def cleanup_if_needed(self):
        """Clean up resources if memory usage is high."""
        memory_info = self.check_memory_usage()

        if memory_info["is_critical"]:
            logger.warning("High memory usage detected, cleaning up...")

            # Clear caches
            # Unload unused models
            # Force garbage collection
            import gc
            gc.collect()
```

### **2. Model Lifecycle Management**
```python
class ModelLifecycleManager:
    def __init__(self, idle_timeout: int = 1800):
        self.idle_timeout = idle_timeout
        self.model_last_used = {}

    def mark_model_used(self, model: str):
        """Mark model as recently used."""
        self.model_last_used[model] = time.time()

    def get_idle_models(self) -> List[str]:
        """Get models that have been idle for too long."""
        current_time = time.time()
        idle_models = []

        for model, last_used in self.model_last_used.items():
            if current_time - last_used > self.idle_timeout:
                idle_models.append(model)

        return idle_models

    async def unload_idle_models(self):
        """Unload models that have been idle for too long."""
        idle_models = self.get_idle_models()

        for model in idle_models:
            try:
                # Unload model via Ollama API
                response = requests.delete(f"{OLLAMA_URL}/api/delete",
                                        json={"name": model})
                if response.status_code == 200:
                    del self.model_last_used[model]
                    logger.info(f"Unloaded idle model: {model}")
            except Exception as e:
                logger.error(f"Failed to unload model {model}: {e}")
```

## 🎯 **Best Practices Summary**

### **1. Model Selection**
- Use appropriate model for task type
- Consider performance vs. quality trade-offs
- Implement intelligent fallback strategies

### **2. Parameter Tuning**
- Lower temperature for consistent outputs
- Use mirostat for better response consistency
- Optimize token limits for task requirements

### **3. Resource Management**
- Monitor memory and GPU usage
- Implement automatic cleanup
- Use caching for frequently requested content

### **4. Performance Optimization**
- Collect and analyze performance metrics
- Implement health checks
- Use load balancing for high-traffic scenarios

### **5. Error Handling**
- Implement robust fallback strategies
- Log and monitor errors
- Provide graceful degradation

### **6. Monitoring & Alerting**
- Track response times and success rates
- Monitor resource usage
- Set up alerts for performance degradation

## 📈 **Performance Benchmarks**

### **Expected Performance Metrics**
| Model | Avg Response Time | Success Rate | Memory Usage |
|-------|------------------|--------------|--------------|
| DeepSeek 6.7B | 15-30s | 95% | 4GB |
| Yi 1.5B | 3-8s | 92% | 1GB |
| Qwen2.5 3B | 8-15s | 90% | 2GB |
| StarCoder2 3B | 10-20s | 88% | 3GB |
| Mistral 7B | 6-12s | 92% | 4GB |

### **Optimization Targets**
- Response time < 20 seconds for most tasks
- Success rate > 90% across all models
- Memory usage < 85% of available RAM
- GPU utilization > 70% when available

---

**Last Updated**: July 23, 2025
**Version**: 1.0.0
**Status**: ✅ **ACTIVE**
