"""
CLI commands for Validation Service management.
Designed to be imported into the existing CLI entrypoint and invoked by local LLMs.
"""

import asyncio
import os
from typing import Any, Dict, Optional

import httpx

VALIDATION_HOST = os.getenv("VALIDATION_SERVICE_HOST", "http://localhost")
VALIDATION_PORT = os.getenv("VALIDATION_PORT", "8004")
BASE_URL = f"{VALIDATION_HOST}:{VALIDATION_PORT}"


class ValidationCommands:
    """
    CLI command set for managing the Validation Service.
    Designed to be imported into the existing CLI entrypoint and invoked by local LLMs.
    """

    def __init__(self) -> None:
        pass

    async def health(self) -> Dict[str, Any]:
        """Check validation service health"""
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/health")
            r.raise_for_status()
            return r.json()

    async def status(self) -> Dict[str, Any]:
        """Get validation service status"""
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/status")
            r.raise_for_status()
            return r.json()

    async def start(
        self,
        site_path: Optional[str] = None,
        validation_type: Optional[str] = "full",
        detailed: bool = False,
        rules: Optional[list] = None,
    ) -> Dict[str, Any]:
        """Start a validation job"""
        payload = {
            "site_path": site_path,
            "validation_type": validation_type,
            "detailed": detailed,
            "rules": rules or [],
        }
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return r.json()

    async def stop(self, job_id: str) -> Dict[str, Any]:
        """Stop a validation job"""
        async with httpx.AsyncClient(timeout=15) as client:
            r = await client.post(f"{BASE_URL}/stop/{job_id}")
            r.raise_for_status()
            return r.json()

    async def progress(self, job_id: str) -> Dict[str, Any]:
        """Get validation job progress"""
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/progress/{job_id}")
            r.raise_for_status()
            return r.json()

    async def results(self, job_id: str) -> Dict[str, Any]:
        """Get validation results for a job"""
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/results/{job_id}")
            r.raise_for_status()
            return r.json()

    async def rules(self) -> Dict[str, Any]:
        """Get available validation rules"""
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/rules")
            r.raise_for_status()
            return r.json()

    async def metrics(self) -> Dict[str, Any]:
        """Get quality metrics"""
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/metrics")
            r.raise_for_status()
            return r.json()

    async def validate_site(
        self, site_path: str, detailed: bool = False
    ) -> Dict[str, Any]:
        """Validate a specific site"""
        return await self.start(
            site_path=site_path, validation_type="full", detailed=detailed
        )

    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate system configuration"""
        return await self.start(validation_type="configuration", detailed=True)

    async def validate_dependencies(self) -> Dict[str, Any]:
        """Validate system dependencies"""
        return await self.start(validation_type="dependencies", detailed=True)

    async def validate_security(self) -> Dict[str, Any]:
        """Validate security settings"""
        return await self.start(validation_type="security", detailed=True)

    async def validate_performance(self) -> Dict[str, Any]:
        """Validate performance settings"""
        return await self.start(validation_type="performance", detailed=True)

    async def list_jobs(self) -> Dict[str, Any]:
        """List all validation jobs"""
        # This would need to be implemented in the service
        # For now, return a placeholder
        return {
            "message": "List jobs functionality not yet implemented",
            "status": "not_implemented",
        }

    async def cleanup_jobs(self, older_than_hours: int = 24) -> Dict[str, Any]:
        """Clean up old validation jobs"""
        # This would need to be implemented in the service
        # For now, return a placeholder
        return {
            "message": "Cleanup jobs functionality not yet implemented",
            "status": "not_implemented",
            "older_than_hours": older_than_hours,
        }


# Optional: quick manual test
if __name__ == "__main__":

    async def _demo() -> None:
        cli = ValidationCommands()
        print("Health check:", await cli.health())
        print("Status:", await cli.status())
        print("Rules:", await cli.rules())
        print("Metrics:", await cli.metrics())

    asyncio.run(_demo())
