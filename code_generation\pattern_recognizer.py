"""
Dict[str, Any] Recognizer Module

Identifies coding patterns, best practices, anti-patterns, and provides
learning capabilities for project-specific patterns.

Phase 19 Implementation - Enhanced Code Generation
"""

import hashlib
import json
import logging
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set

logger = logging.getLogger(__name__)


@dataclass
class Pattern:
    """Represents a recognized code pattern."""

    name: str
    type: str  # "best_practice", "anti_pattern", "design_pattern", "custom"
    description: str
    confidence: float
    line_numbers: List[int]
    code_snippet: str
    language: str
    category: str


@dataclass
class RecognitionResult:
    """Result of pattern recognition analysis."""

    patterns: List[dict[str, Any]]
    insights: List[str]
    best_practices: List[str]
    anti_patterns: List[str]
    design_patterns: List[str]
    custom_patterns: List[str]
    learning_data: dict[str, Any]


class PatternRecognizer:
    """
    Advanced pattern recognition system for code analysis.

    Provides:
    - Best practice detection
    - Anti-pattern identification
    - Design pattern recognition
    - Custom pattern learning
    - Project-specific pattern analysis
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the pattern recognizer.

        Args:
            config: Configuration dictionary for pattern recognition
        """
        self.config = config or {}
        self.supported_languages = ["python", "javascript", "typescript", "java", "cpp"]
        self.health_status = "healthy"

        # Load predefined patterns
        self.best_practices = self._load_best_practices()
        self.anti_patterns = self._load_anti_patterns()
        self.design_patterns = self._load_design_patterns()

        # Learning system
        self.learned_patterns: dict[str, List[dict[str, Any]]] = {}
        self.pattern_database: dict[str, Any] = {}

        logger.info("Pattern Recognizer initialized successfully")

    async def recognize_patterns(
        self, code: str, language: str, project_context: Optional[dict[str, Any]] = None
    ) -> dict[str, Any]:
        """
        Recognize patterns in the given code.

        Args:
            code: Source code to analyze
            language: Programming language
            project_context: Optional project context for learning

        Returns:
            Dictionary with pattern recognition results
        """
        try:
            if language not in self.supported_languages:
                raise ValueError(f"Unsupported language: {language}")

            # Recognize different types of patterns
            best_practices = await self._recognize_best_practices(code, language)
            anti_patterns = await self._recognize_anti_patterns(code, language)
            design_patterns = await self._recognize_design_patterns(code, language)
            custom_patterns = await self._recognize_custom_patterns(
                code, language, project_context
            )

            # Combine all patterns
            all_patterns = (
                best_practices + anti_patterns + design_patterns + custom_patterns
            )

            # Generate insights
            insights = self._generate_insights(all_patterns, code, language)

            # Prepare learning data
            learning_data = self._prepare_learning_data(all_patterns, project_context)

            result = RecognitionResult(
                patterns=all_patterns,
                insights=insights,
                best_practices=[p.get("name", "") for p in best_practices],
                anti_patterns=[p.get("name", "") for p in anti_patterns],
                design_patterns=[p.get("name", "") for p in design_patterns],
                custom_patterns=[p.get("name", "") for p in custom_patterns],
                learning_data=learning_data,
            )

            return {
                "patterns": [self._pattern_to_dict(p) for p in all_patterns],
                "insights": insights,
                "best_practices": result.best_practices,
                "anti_patterns": result.anti_patterns,
                "design_patterns": result.design_patterns,
                "custom_patterns": result.custom_patterns,
                "pattern_count": len(all_patterns),
                "learning_data": learning_data,
            }

        except Exception as e:
            logger.error(f"Error recognizing patterns: {e}")
            raise

    async def learn_patterns(
        self, code: str, language: str, project_context: Optional[dict[str, Any]] = None
    ) -> dict[str, Any]:
        """
        Learn new patterns from the given code.

        Args:
            code: Source code to learn from
            language: Programming language
            project_context: Optional project context

        Returns:
            Dictionary with learning results
        """
        try:
            # Extract potential patterns
            potential_patterns = self._extract_potential_patterns(code, language)

            # Analyze pattern frequency and context
            learned_patterns = self._analyze_pattern_frequency(
                potential_patterns, project_context
            )

            # Store learned patterns
            project_id = (
                project_context.get("project_id", "default")
                if project_context
                else "default"
            )
            if project_id not in self.learned_patterns:
                self.learned_patterns[project_id] = []

            self.learned_patterns[project_id].extend(learned_patterns)

            return {
                "patterns_learned": len(learned_patterns),
                "total_patterns": len(self.learned_patterns[project_id]),
                "project_id": project_id,
            }

        except Exception as e:
            logger.error(f"Error learning patterns: {e}")
            raise

    async def _recognize_best_practices(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Recognize best practices in the code."""
        patterns = []
        lines = code.split("\n")

        for i, line in enumerate(lines, 1):
            line_patterns = self._check_line_best_practices(line, language, i)
            patterns.extend(line_patterns)

        # Check for multi-line best practices
        multi_line_patterns = self._check_multi_line_best_practices(code, language)
        patterns.extend(multi_line_patterns)

        return patterns

    async def _recognize_anti_patterns(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Recognize anti-patterns in the code."""
        patterns = []
        lines = code.split("\n")

        for i, line in enumerate(lines, 1):
            line_patterns = self._check_line_anti_patterns(line, language, i)
            patterns.extend(line_patterns)

        # Check for multi-line anti-patterns
        multi_line_patterns = self._check_multi_line_anti_patterns(code, language)
        patterns.extend(multi_line_patterns)

        return patterns

    async def _recognize_design_patterns(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Recognize design patterns in the code."""
        patterns: List[Dict[str, Any]] = []

        # Check for common design patterns
        design_pattern_checks = self._get_design_pattern_checks(language)

        for pattern_name, check_func in design_pattern_checks.items():
            if check_func(code):
                pattern = dict(
                    name=pattern_name,
                    type="design_pattern",
                    description=f"Detected {pattern_name} design pattern",
                    confidence=0.8,
                    line_numbers=[1],  # Simplified
                    code_snippet=code[:200] + "..." if len(code) > 200 else code,
                    language=language,
                    category="design",
                )
                patterns.append(pattern)

        return patterns

    async def _recognize_custom_patterns(
        self, code: str, language: str, project_context: Optional[dict[str, Any]]
    ) -> List[dict[str, Any]]:
        """Recognize custom/learned patterns in the code."""
        patterns: List[Dict[str, Any]] = []

        if not project_context:
            return patterns

        project_id = project_context.get("project_id", "default")
        if project_id in self.learned_patterns:
            learned_patterns = self.learned_patterns[project_id]

            for learned_pattern in learned_patterns:
                if self._matches_learned_pattern(code, learned_pattern):
                    patterns.append(learned_pattern)

        return patterns

    def _check_line_best_practices(
        self, line: str, language: str, line_number: int
    ) -> List[Dict[str, Any]]:
        """Check for best practices in a single line."""
        patterns = []

        if language == "python":
            # Check for proper naming conventions
            if re.search(r"def [a-z_][a-z0-9_]*\(", line):
                patterns.append(
                    dict(
                        name="snake_case_function_naming",
                        type="best_practice",
                        description="Function uses snake_case naming convention",
                        confidence=0.9,
                        line_numbers=[line_number],
                        code_snippet=line,
                        language=language,
                        category="naming",
                    )
                )

            # Check for type hints
            if "def " in line and "-> " in line:
                patterns.append(
                    dict(
                        name="type_hints_used",
                        type="best_practice",
                        description="Function uses type hints",
                        confidence=0.9,
                        line_numbers=[line_number],
                        code_snippet=line,
                        language=language,
                        category="type_safety",
                    )
                )

        elif language in ["javascript", "typescript"]:
            # Check for const usage
            if re.search(r"const\s+\w+\s*=", line):
                patterns.append(
                    dict(
                        name="const_declaration",
                        type="best_practice",
                        description="Uses const for immutable variables",
                        confidence=0.8,
                        line_numbers=[line_number],
                        code_snippet=line,
                        language=language,
                        category="immutability",
                    )
                )

        return patterns

    def _check_line_anti_patterns(
        self, line: str, language: str, line_number: int
    ) -> List[Dict[str, Any]]:
        """Check for anti-patterns in a single line."""
        patterns = []

        if language == "python":
            # Check for bare except
            if re.search(r"except:", line):
                patterns.append(
                    dict(
                        name="bare_except",
                        type="anti_pattern",
                        description="Bare except clause catches all exceptions",
                        confidence=0.9,
                        line_numbers=[line_number],
                        code_snippet=line,
                        language=language,
                        category="error_handling",
                    )
                )

            # Check for magic numbers
            if re.search(r"\b\d{3,}\b", line):
                patterns.append(
                    dict(
                        name="magic_number",
                        type="anti_pattern",
                        description="Magic number detected",
                        confidence=0.7,
                        line_numbers=[line_number],
                        code_snippet=line,
                        language=language,
                        category="maintainability",
                    )
                )

        elif language in ["javascript", "typescript"]:
            # Check for var usage
            if re.search(r"\bvar\s+", line):
                patterns.append(
                    dict(
                        name="var_usage",
                        type="anti_pattern",
                        description="Consider using const or let instead of var",
                        confidence=0.8,
                        line_numbers=[line_number],
                        code_snippet=line,
                        language=language,
                        category="scope",
                    )
                )

        return patterns

    def _check_multi_line_best_practices(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Check for multi-line best practices."""
        patterns = []

        if language == "python":
            # Check for docstrings
            if '"""' in code or "'''" in code:
                patterns.append(
                    dict(
                        name="docstring_present",
                        type="best_practice",
                        description="Code contains docstrings",
                        confidence=0.8,
                        line_numbers=[1],
                        code_snippet=code[:100] + "...",
                        language=language,
                        category="documentation",
                    )
                )

        return patterns

    def _check_multi_line_anti_patterns(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Check for multi-line anti-patterns."""
        patterns = []

        # Check for long functions
        lines = code.split("\n")
        if len(lines) > 50:
            patterns.append(
                dict[str, Any](
                    name="long_function",
                    type="anti_pattern",
                    description="Function is too long (consider breaking it down)",
                    confidence=0.7,
                    line_numbers=list(range(1, len(lines) + 1)),
                    code_snippet=code[:200] + "...",
                    language=language,
                    category="complexity",
                )
            )

        return patterns

    def _get_design_pattern_checks(
        self, language: str
    ) -> Dict[str, Callable[[str], bool]]:
        """Get design pattern check functions for the language."""
        checks = {}

        if language == "python":
            checks["Singleton"] = lambda code: "class" in code and "instance" in code
            checks["Factory"] = (
                lambda code: "def create_" in code or "def factory_" in code
            )
            checks["Observer"] = lambda code: "notify" in code and "subscribe" in code

        elif language in ["javascript", "typescript"]:
            checks["Module Dict[str, Any]"] = (
                lambda code: "(function(" in code or "export" in code
            )
            checks["Observer"] = (
                lambda code: "addEventListener" in code or "emit" in code
            )

        return checks

    def _extract_potential_patterns(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Extract potential patterns from code for learning."""
        patterns = []

        # Extract function patterns
        function_patterns = self._extract_function_patterns(code, language)
        patterns.extend(function_patterns)

        # Extract class patterns
        class_patterns = self._extract_class_patterns(code, language)
        patterns.extend(class_patterns)

        # Extract naming patterns
        naming_patterns = self._extract_naming_patterns(code, language)
        patterns.extend(naming_patterns)

        return patterns

    def _extract_function_patterns(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Extract function-related patterns."""
        patterns = []

        if language == "python":
            # Extract function definitions
            function_matches = re.finditer(r"def\s+(\w+)\s*\([^)]*\)\s*:", code)
            for match in function_matches:
                patterns.append(
                    {
                        "type": "function",
                        "name": match.group(1),
                        "pattern": match.group(0),
                        "language": language,
                    }
                )

        return patterns

    def _extract_class_patterns(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Extract class-related patterns."""
        patterns = []

        if language == "python":
            # Extract class definitions
            class_matches = re.finditer(r"class\s+(\w+)", code)
            for match in class_matches:
                patterns.append(
                    {
                        "type": "class",
                        "name": match.group(1),
                        "pattern": match.group(0),
                        "language": language,
                    }
                )

        return patterns

    def _extract_naming_patterns(
        self, code: str, language: str
    ) -> List[Dict[str, Any]]:
        """Extract naming convention patterns."""
        patterns = []

        if language == "python":
            # Extract variable naming patterns
            var_matches = re.finditer(r"(\w+)\s*=", code)
            for match in var_matches:
                var_name = match.group(1)
                if re.match(r"^[a-z_][a-z0-9_]*$", var_name):
                    patterns.append(
                        {
                            "type": "naming",
                            "name": "snake_case_variable",
                            "pattern": var_name,
                            "language": language,
                        }
                    )

        return patterns

    def _analyze_pattern_frequency(
        self, patterns: List[dict[str, Any]], project_context: Optional[dict[str, Any]]
    ) -> List[dict[str, Any]]:
        """Analyze pattern frequency and create learned patterns."""
        learned_patterns = []

        # Group patterns by type and name
        pattern_groups: Dict[str, List[Dict[str, Any]]] = {}
        for pattern in patterns:
            key = f"{pattern['type']}_{pattern['name']}"
            if key not in pattern_groups:
                pattern_groups[key] = []
            pattern_groups[key].append(pattern)

        # Create learned patterns for frequently occurring patterns
        for key, group in pattern_groups.items():
            if len(group) >= 2:  # Dict[str, Any] appears at least twice
                pattern = dict(
                    name=f"Learned_{key}",
                    type="custom",
                    description=f"Learned pattern: {key}",
                    confidence=0.6,
                    line_numbers=[1],
                    code_snippet=group[0]["pattern"],
                    language=group[0]["language"],
                    category="learned",
                )
                learned_patterns.append(pattern)

        return learned_patterns

    def _matches_learned_pattern(self, code: str, pattern: Dict[str, Any]) -> bool:
        """Check if code matches a learned pattern."""
        # Simple pattern matching (in real implementation, would be more sophisticated)
        return pattern.get("code_snippet", "") in code

    def _generate_insights(
        self, patterns: List[Dict[str, Any]], code: str, language: str
    ) -> List[str]:
        """Generate insights from recognized patterns."""
        insights = []

        # Count pattern types
        best_practice_count = len(
            [p for p in patterns if p.get("type") == "best_practice"]
        )
        anti_pattern_count = len(
            [p for p in patterns if p.get("type") == "anti_pattern"]
        )
        design_pattern_count = len(
            [p for p in patterns if p.get("type") == "design_pattern"]
        )

        if best_practice_count > 0:
            insights.append(f"Code follows {best_practice_count} best practices")

        if anti_pattern_count > 0:
            insights.append(
                f"Found {anti_pattern_count} potential anti-patterns to address"
            )

        if design_pattern_count > 0:
            insights.append(f"Uses {design_pattern_count} design patterns")

        # Language-specific insights
        if language == "python":
            if any("type_hints" in p.get("name", "") for p in patterns):
                insights.append("Good use of type hints for code clarity")

        return insights

    def _prepare_learning_data(
        self, patterns: List[Dict[str, Any]], project_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Prepare data for pattern learning."""
        return {
            "patterns_found": len(patterns),
            "pattern_types": list(set(p.get("type", "") for p in patterns)),
            "project_context": project_context,
            "learning_enabled": self.config.get("learning_enabled", True),
        }

    def _pattern_to_dict(self, pattern: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Dict[str, Any] object to dictionary."""
        return {
            "name": pattern.get("name", ""),
            "type": pattern.get("type", ""),
            "description": pattern.get("description", ""),
            "confidence": pattern.get("confidence", 0.0),
            "line_numbers": pattern.get("line_numbers", []),
            "code_snippet": pattern.get("code_snippet", ""),
            "language": pattern.get("language", ""),
            "category": pattern.get("category", ""),
        }

    def _load_best_practices(self) -> Dict[str, List[str]]:
        """Load predefined best practices."""
        return {
            "python": [
                "snake_case_naming",
                "type_hints",
                "docstrings",
                "list_comprehensions",
                "context_managers",
            ],
            "javascript": [
                "const_declaration",
                "arrow_functions",
                "template_literals",
                "destructuring",
            ],
            "typescript": ["type_annotations", "interfaces", "enums", "generics"],
        }

    def _load_anti_patterns(self) -> Dict[str, List[str]]:
        """Load predefined anti-patterns."""
        return {
            "python": [
                "bare_except",
                "magic_numbers",
                "global_variables",
                "deep_nesting",
            ],
            "javascript": [
                "var_usage",
                "callback_hell",
                "eval_usage",
                "global_variables",
            ],
            "typescript": ["any_type", "type_assertions", "namespace_usage"],
        }

    def _load_design_patterns(self) -> Dict[str, List[str]]:
        """Load predefined design patterns."""
        return {
            "python": ["singleton", "factory", "observer", "strategy", "decorator"],
            "javascript": ["module_pattern", "observer", "factory", "singleton"],
        }

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get the health status of the pattern recognizer.

        Returns:
            Dictionary with health status information
        """
        return {
            "status": self.health_status,
            "supported_languages": self.supported_languages,
            "learned_patterns_count": sum(
                len(patterns) for patterns in self.learned_patterns.values()
            ),
            "config": self.config,
        }
