"""
Unified WebsiteGenerator - Combines functionality from both original and enhanced versions.
Maintains backward compatibility while adding modern features.
Enhanced with full async/await implementation, progress tracking, and concurrent site generation.
"""

import asyncio
import json
import logging
import re
import shutil
import time
import zipfile
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    List,
    Optional,
    Protocol,
    Set,
    Union,
    runtime_checkable,
)

import aiofiles
import aiohttp

from core.managers import BackupManager
from core.validators import CSSValidator, HTMLValidator, SafetyValidator
from db.database_manager import DatabaseManager
from db.models import CodeFile, Project
from templates.template_manager import TemplateManager

if TYPE_CHECKING:
    from sqlalchemy.orm import Session
else:
    try:
        from sqlalchemy.orm import Session
    except ImportError:
        # SQLAlchemy not available, create mock Session type
        class Session:
            pass


# Type alias for database session
DatabaseSession = Union[Session, Any]


@runtime_checkable
class DatabaseManagerProtocol(Protocol):
    def create(self, db: DatabaseSession, *, obj_in: Dict[str, Any]) -> Any: ...

    def get_multi(
        self, db: DatabaseSession, *, skip: int = 0, limit: int = 100, **filters: Any
    ) -> List[Any]: ...
    def get(self, db: DatabaseSession, id: Any) -> Optional[Any]: ...

    def update(
        self, db: DatabaseSession, *, db_obj: Any, obj_in: Dict[str, Any]
    ) -> Any: ...
    def remove(self, db: DatabaseSession, *, id: int) -> Any: ...

    model: type


# Import with fallback handling
try:
    from db.database_manager import DatabaseManager
    from db.models import CodeFile, Project
    from templates.template_manager import TemplateManager
except ImportError as e:
    # Fallback for when running as script
    try:
        from template_manager import TemplateManager  # type: ignore

        from db.database_manager import DatabaseManager  # type: ignore
        from db.models import CodeFile, Project  # type: ignore
    except ImportError:
        try:
            from template_manager import TemplateManager  # type: ignore

            from db.database_manager import DatabaseManager  # type: ignore
            from db.models import CodeFile, Project  # type: ignore
        except ImportError as e:
            raise ImportError(f"Required dependencies not found: {str(e)}")

# Configure logging
logger = logging.getLogger(__name__)


class BuildStatus(Enum):
    """Website build status enumeration"""

    PENDING = "pending"
    BUILDING = "building"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BuildProgress:
    """Build progress tracking with async support"""

    status: BuildStatus = BuildStatus.PENDING
    current_step: str = ""
    progress_percent: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    build_id: str = ""
    site_name: str = ""
    steps_completed: int = 0
    total_steps: int = 0
    current_operation: str = ""
    estimated_time_remaining: Optional[float] = None


class ProgressCallback(Protocol):
    """Progress callback interface for real-time updates"""

    async def __call__(self, progress: BuildProgress) -> None: ...


class WebsitePlugin(Protocol):
    """Plugin interface for website generation with async support"""

    async def pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Called before website build starts"""
        ...

    async def post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Called after website build completes"""
        ...

    async def on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Called when an error occurs during build"""
        ...


class CacheManager:
    """Manages caching for templates, assets, and build results"""

    def __init__(self, cache_dir: str = ".cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.template_cache: Dict[str, Any] = {}
        self.asset_cache: Dict[str, Any] = {}

    def get_template_cache_key(self, template_name: str, theme_name: str) -> str:
        """Generate cache key for template"""
        return f"template_{template_name}_{theme_name}"

    def get_cached_template(
        self, template_name: str, theme_name: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached template data"""
        cache_key = self.get_template_cache_key(template_name, theme_name)
        return self.template_cache.get(cache_key)

    def cache_template(
        self, template_name: str, theme_name: str, template_data: Dict[str, Any]
    ) -> None:
        """Cache template data"""
        cache_key = self.get_template_cache_key(template_name, theme_name)
        self.template_cache[cache_key] = template_data

    def clear_cache(self) -> None:
        """Clear all cached data"""
        self.template_cache.clear()
        self.asset_cache.clear()
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)
            self.cache_dir.mkdir()


class AsyncCacheManager:
    """Async template and asset caching system"""

    def __init__(self, cache_dir: str = ".cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

    def get_template_cache_key(self, template_name: str, theme_name: str) -> str:
        """Generate cache key for template"""
        return f"template_{template_name}_{theme_name}"

    async def get_cached_template(
        self, template_name: str, theme_name: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached template data asynchronously"""
        cache_key = self.get_template_cache_key(template_name, theme_name)
        cache_file = self.cache_dir / f"{cache_key}.json"
        if cache_file.exists():
            try:
                async with aiofiles.open(cache_file, "r") as f:
                    content = await f.read()
                    return json.loads(content)
            except Exception as e:
                logger.warning(f"Failed to load cached template {cache_key}: {e}")
        return None

    async def cache_template(
        self, template_name: str, theme_name: str, template_data: Dict[str, Any]
    ) -> None:
        """Cache template data asynchronously"""
        cache_key = self.get_template_cache_key(template_name, theme_name)
        cache_file = self.cache_dir / f"{cache_key}.json"
        try:
            async with aiofiles.open(cache_file, "w") as f:
                await f.write(json.dumps(template_data, indent=2))
        except Exception as e:
            logger.error(f"Failed to cache template {cache_key}: {e}")

    async def clear_cache(self) -> None:
        """Clear all cached data asynchronously"""
        try:
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")


class ThemeManager:
    """Manages theme customization and asset pipeline"""

    def __init__(self, themes_dir: str = "themes"):
        self.themes_dir = Path(themes_dir)
        self.themes_dir.mkdir(exist_ok=True)
        self.theme_config = self._load_theme_config()

    def _load_theme_config(self) -> Dict[str, Any]:
        """Load theme configuration from JSON"""
        config_path = self.themes_dir / "themes.json"
        if not config_path.exists():
            return {}
        with open(config_path, "r") as f:
            return json.load(f)

    def _save_theme_config(self) -> None:
        """Save theme configuration to JSON"""
        config_path = self.themes_dir / "themes.json"
        with open(config_path, "w") as f:
            json.dump(self.theme_config, f, indent=2)

    def create_theme(
        self, name: str, base_template: str, customizations: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a new theme based on a template with customizations"""
        try:
            theme_path = self.themes_dir / name
            if theme_path.exists():
                raise ValueError(f"Theme '{name}' already exists")

            # Create theme directory structure
            theme_path.mkdir(parents=True)
            assets_path = theme_path / "assets"
            assets_path.mkdir()

            # Copy base template files
            template_path = Path(f"templates/{base_template}")
            if not template_path.exists():
                raise ValueError(f"Base template '{base_template}' not found")

            shutil.copytree(template_path, theme_path / "base", dirs_exist_ok=True)

            # Apply customizations
            self._apply_theme_customizations(theme_path, customizations)

            # Update theme configuration
            self.theme_config[name] = {
                "name": name,
                "base_template": base_template,
                "customizations": customizations,
                "path": str(theme_path),
                "created": datetime.now().isoformat(),
                "assets": self._scan_assets(assets_path),
            }
            self._save_theme_config()

            return {
                "status": "success",
                "theme": self.theme_config[name],
                "message": f"Theme '{name}' created successfully",
            }

        except Exception as e:
            logger.error(f"Failed to create theme '{name}': {e}")
            return {"status": "error", "message": f"Failed to create theme: {str(e)}"}

    def _apply_theme_customizations(
        self, theme_path: Path, customizations: Dict[str, Any]
    ) -> None:
        """Apply theme customizations to files"""
        for file_path, content in customizations.get("files", {}).items():
            full_path = theme_path / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            with open(full_path, "w") as f:
                f.write(content)

        # Apply CSS variable replacements
        css_vars = customizations.get("css_variables", {})
        if css_vars:
            css_files = list(theme_path.rglob("*.css"))
            for css_file in css_files:
                with open(css_file, "r") as f:
                    css_content = f.read()

                for var_name, value in css_vars.items():
                    css_content = self._replace_css_variable(
                        css_content, var_name, value
                    )

                with open(css_file, "w") as f:
                    f.write(css_content)

    def _replace_css_variable(self, css_content: str, variable: str, value: str) -> str:
        """Replace CSS variable in content"""
        pattern = rf"--{re.escape(variable)}:\s*[^;]+;"
        replacement = f"--{variable}: {value};"
        return re.sub(pattern, replacement, css_content)

    def _scan_assets(self, assets_path: Path) -> Dict[str, Any]:
        """Scan theme assets directory"""
        assets = {"images": [], "styles": [], "scripts": [], "fonts": []}

        if assets_path.exists():
            for file_path in assets_path.rglob("*"):
                if file_path.is_file():
                    rel_path = file_path.relative_to(assets_path)
                    ext = file_path.suffix.lower()

                    if ext in [".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp"]:
                        assets["images"].append(str(rel_path))
                    elif ext == ".css":
                        assets["styles"].append(str(rel_path))
                    elif ext in [".js", ".ts"]:
                        assets["scripts"].append(str(rel_path))
                    elif ext in [".woff", ".woff2", ".ttf", ".otf", ".eot"]:
                        assets["fonts"].append(str(rel_path))

        return assets

    def get_theme(self, name: str) -> Dict[str, Any]:
        """Get theme information"""
        if name not in self.theme_config:
            raise ValueError(f"Theme '{name}' not found")
        return self.theme_config[name]

    def list_themes(self) -> Dict[str, Any]:
        """List all available themes"""
        return {
            "status": "success",
            "themes": list(self.theme_config.keys()),
            "theme_details": self.theme_config,
        }


class AssetPipeline:
    """Manages asset optimization and processing"""

    def __init__(self, output_dir: str = "dist"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def optimize_assets(self, source_dir: Path, site_name: str) -> Dict[str, Any]:
        """Optimize assets for a website"""
        try:
            target_dir = self.output_dir / site_name
            target_dir.mkdir(parents=True, exist_ok=True)

            # Copy all files first
            shutil.copytree(source_dir, target_dir, dirs_exist_ok=True)

            # Optimize different asset types
            image_results = self._optimize_images(source_dir, target_dir)
            style_results = self._optimize_styles(source_dir, target_dir)
            script_results = self._optimize_scripts(source_dir, target_dir)

            # Check if any assets were processed
            total_processed = (
                image_results["processed"]
                + style_results["processed"]
                + script_results["processed"]
            )

            if total_processed == 0:
                return {
                    "status": "success",
                    "message": "No assets to optimize",
                    "results": {
                        "images": image_results,
                        "styles": style_results,
                        "scripts": script_results,
                    },
                }

            return {
                "status": "success",
                "message": "Assets optimized successfully",
                "results": {
                    "images": image_results,
                    "styles": style_results,
                    "scripts": script_results,
                },
            }

        except Exception as e:
            logger.error(f"Asset optimization failed: {e}")
            return {
                "status": "error",
                "message": f"Asset optimization failed: {str(e)}",
            }

    async def optimize_assets_async(
        self, source_dir: Path, site_name: str
    ) -> Dict[str, Any]:
        """Async version of asset optimization"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, self.optimize_assets, source_dir, site_name
        )

    def _optimize_images(self, source_dir: Path, target_dir: Path) -> Dict[str, Any]:
        """Optimize image files"""
        image_files = (
            list(source_dir.rglob("*.jpg"))
            + list(source_dir.rglob("*.jpeg"))
            + list(source_dir.rglob("*.png"))
            + list(source_dir.rglob("*.gif"))
        )

        optimized_count = 0
        errors = []
        for img_file in image_files:
            try:
                # For now, just copy images (actual optimization would require PIL/Pillow)
                rel_path = img_file.relative_to(source_dir)
                target_path = target_dir / rel_path
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(img_file, target_path)
                optimized_count += 1
            except Exception as e:
                error_msg = f"Failed to optimize image {img_file}: {e}"
                logger.warning(error_msg)
                errors.append(error_msg)

        return {
            "status": "success",
            "processed": len(image_files),
            "optimized": optimized_count,
            "errors": errors,
        }

    def _optimize_styles(self, source_dir: Path, target_dir: Path) -> Dict[str, Any]:
        """Optimize CSS files"""
        css_files = list(source_dir.rglob("*.css"))

        processed_count = 0
        errors = []
        for css_file in css_files:
            try:
                rel_path = css_file.relative_to(source_dir)
                target_path = target_dir / rel_path
                target_path.parent.mkdir(parents=True, exist_ok=True)

                # Read and minify CSS (basic minification)
                with open(css_file, "r") as f:
                    css_content = f.read()

                # Basic CSS minification
                css_content = re.sub(
                    r"\s+", " ", css_content
                )  # Remove extra whitespace
                css_content = re.sub(
                    r";\s*}", "}", css_content
                )  # Remove trailing semicolons
                # Remove space after {
                css_content = re.sub(r"{\s*", "{", css_content)
                # Remove space before }
                css_content = re.sub(r"\s*}", "}", css_content)

                with open(target_path, "w") as f:
                    f.write(css_content)
                processed_count += 1
            except Exception as e:
                error_msg = f"Failed to optimize CSS {css_file}: {e}"
                logger.warning(error_msg)
                errors.append(error_msg)

        return {
            "status": "success",
            "processed": len(css_files),
            "optimized": processed_count,
            "errors": errors,
        }

    def _optimize_scripts(self, source_dir: Path, target_dir: Path) -> Dict[str, Any]:
        """Optimize JavaScript files"""
        js_files = list(source_dir.rglob("*.js"))

        processed_count = 0
        errors = []
        for js_file in js_files:
            try:
                rel_path = js_file.relative_to(source_dir)
                target_path = target_dir / rel_path
                target_path.parent.mkdir(parents=True, exist_ok=True)

                # For now, just copy JS files (actual minification would require a JS minifier)
                shutil.copy2(js_file, target_path)
                processed_count += 1
            except Exception as e:
                error_msg = f"Failed to optimize JS {js_file}: {e}"
                logger.warning(error_msg)
                errors.append(error_msg)

        return {
            "status": "success",
            "processed": len(js_files),
            "optimized": processed_count,
            "errors": errors,
        }

    async def _validate_assets_async(self, source_dir: Path) -> Dict[str, Any]:
        """Validate assets asynchronously"""
        validation_results = {
            "html_files": [],
            "css_files": [],
            "js_files": [],
            "images": [],
            "errors": [],
        }

        try:
            # Validate HTML files
            html_files = list(source_dir.rglob("*.html"))
            for html_file in html_files:
                try:
                    with open(html_file, "r") as f:
                        html_content = f.read()

                    # Basic HTML validation
                    if "<html" in html_content and "</html>" in html_content:
                        validation_results["html_files"].append(
                            {"file": str(html_file), "status": "valid"}
                        )
                    else:
                        validation_results["html_files"].append(
                            {
                                "file": str(html_file),
                                "status": "invalid",
                                "error": "Missing HTML structure",
                            }
                        )
                except Exception as e:
                    validation_results["errors"].append(
                        f"Failed to validate {html_file}: {e}"
                    )

            # Validate CSS files
            css_files = list(source_dir.rglob("*.css"))
            for css_file in css_files:
                try:
                    with open(css_file, "r") as f:
                        css_content = f.read()

                    # Basic CSS validation
                    if "{" in css_content and "}" in css_content:
                        validation_results["css_files"].append(
                            {"file": str(css_file), "status": "valid"}
                        )
                    else:
                        validation_results["css_files"].append(
                            {
                                "file": str(css_file),
                                "status": "invalid",
                                "error": "Invalid CSS structure",
                            }
                        )
                except Exception as e:
                    validation_results["errors"].append(
                        f"Failed to validate {css_file}: {e}"
                    )

            return {"status": "success", "validation_results": validation_results}

        except Exception as e:
            return {"status": "error", "message": f"Asset validation failed: {str(e)}"}


class ContentMetadata:
    """Manages content metadata and database operations"""

    def __init__(self, db_manager: Optional[DatabaseManagerProtocol] = None):
        self.db_manager = db_manager

    def create_content_record(
        self,
        site_name: str,
        content_type: str,
        metadata: Dict[str, Any],
        db_session: Optional[Any] = None,
    ) -> Dict[str, Any]:
        """Create a content metadata record"""
        try:
            if not self.db_manager:
                return {
                    "status": "warning",
                    "message": "No database manager available, skipping metadata creation",
                }

            # Create content record
            content_data = {
                "site_name": site_name,
                "content_type": content_type,
                "metadata": metadata,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }

            if db_session:
                # Use provided session
                result = self.db_manager.create(db_session, obj_in=content_data)
                return {
                    "status": "success",
                    "message": "Content metadata created successfully",
                    "record_id": getattr(result, "id", None),
                }
            else:
                # Create new session
                session = self.db_manager.create_session()
                try:
                    result = self.db_manager.create(session, obj_in=content_data)
                    session.commit()
                    return {
                        "status": "success",
                        "message": "Content metadata created successfully",
                        "record_id": getattr(result, "id", None),
                    }
                finally:
                    if hasattr(session, "close"):
                        session.close()

        except Exception as e:
            logger.error(f"Failed to create content metadata: {e}")
            return {
                "status": "error",
                "message": f"Failed to create content metadata: {str(e)}",
            }

    def get_content_metadata(
        self, site_name: str, db_session: Optional[DatabaseSession] = None
    ) -> Dict[str, Any]:
        """Get content metadata for a site"""
        try:
            if not self.db_manager:
                return {
                    "status": "warning",
                    "message": "No database manager available",
                    "metadata": [],
                }

            if db_session:
                # Use provided session
                records = self.db_manager.get_multi(db_session, site_name=site_name)
            else:
                # Create new session
                session = self.db_manager.create_session()
                try:
                    records = self.db_manager.get_multi(session, site_name=site_name)
                finally:
                    if hasattr(session, "close"):
                        session.close()

            return {
                "status": "success",
                "metadata": [record.__dict__ for record in records],
            }

        except Exception as e:
            logger.error(f"Failed to get content metadata: {e}")
            return {
                "status": "error",
                "message": f"Failed to get content metadata: {str(e)}",
            }


class WebsiteGenerator:
    """Unified website generator with enhanced features and backward compatibility"""

    def __init__(
        self,
        template_manager: TemplateManager,
        database_manager: Optional[DatabaseManagerProtocol] = None,
        max_workers: int = 4,
    ):
        self.template_manager = template_manager
        self.theme_manager = ThemeManager()
        self.asset_pipeline = AssetPipeline()
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # Enhanced components
        self.cache_manager = CacheManager()
        self.backup_manager = BackupManager.create_simple("backups")
        self.safety_validator = SafetyValidator()

        # Initialize content_metadata with proper DatabaseManager
        self.content_metadata = ContentMetadata(database_manager)
        if database_manager is None:
            try:
                # Create default DatabaseManager with Project model if available
                from db.database_manager import DatabaseManager
                from db.models import Project

                self.content_metadata = ContentMetadata(
                    DatabaseManager(Project)
                )  # type: ignore
            except ImportError:
                pass  # Skip if dependencies aren't available

        # Plugin system
        self.plugins: List[WebsitePlugin] = []

        # Build tracking
        self.active_builds: Dict[str, BuildProgress] = {}

        # Create sites directory
        self.sites_dir = Path("sites")
        self.sites_dir.mkdir(exist_ok=True)

        logger.info(f"Unified WebsiteGenerator initialized with {max_workers} workers")

    def add_plugin(self, plugin: WebsitePlugin) -> None:
        """Add a plugin to the generator"""
        self.plugins.append(plugin)
        logger.info(f"Plugin added: {plugin.__class__.__name__}")

    def create_website(self, site_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new website using a template with enhanced safety features
        (Synchronous version for backward compatibility)
        """
        try:
            # Validate site configuration
            self._validate_site_config(site_config)

            # Get template
            template_name = site_config["template"]
            template_info = self.template_manager.get_template(template_name)

            # Create site directory with safety validation
            site_name = site_config["name"]
            site_dir = self.sites_dir / site_name

            # Validate target path
            SafetyValidator.validate_target_path(str(site_dir))

            if site_dir.exists():
                # Create backup before overwriting
                backup_path = self.backup_manager.create_site_backup(
                    site_dir, site_name
                )
                logger.info(f"Backup created before overwrite: {backup_path}")
                shutil.rmtree(site_dir)

            # Create site directory
            site_dir.mkdir(parents=True, exist_ok=True)

            # Apply theme customizations if specified
            theme_name = site_config.get("theme", "default")
            try:
                theme_info = self.theme_manager.get_theme(theme_name)
                template_path = Path(theme_info["path"]) / "base"
            except ValueError:
                # Fallback to default theme if requested theme not found
                logger.warning(
                    f"Theme '{theme_name}' not found, falling back to 'default'"
                )
                theme_info = self.theme_manager.get_theme("default")
                template_path = Path(theme_info["path"]) / "base"

            # Copy template files
            shutil.copytree(template_path, site_dir, dirs_exist_ok=True)

            # Customize website
            self._customize_website(site_dir, site_config)

            # Create site manifest
            manifest = SafetyValidator.create_site_manifest(site_dir, site_config)
            manifest["safety_checks"]["backup_created"] = True

            # Save site manifest
            manifest_path = site_dir / "site.config.json"
            with open(manifest_path, "w") as f:
                json.dump(manifest, f, indent=2)

            # Save config.json for backward compatibility
            config_path = site_dir / "config.json"
            config_data = {
                "name": site_config.get("name", ""),
                "title": site_config.get("title", site_config.get("name", "")),
                "description": site_config.get("description", ""),
                "author": site_config.get("author", ""),
                "keywords": site_config.get("keywords", ""),
                "template": site_config.get("template", ""),
                "theme": site_config.get("theme", "default"),
                "created_at": datetime.now().isoformat(),
            }
            with open(config_path, "w") as f:
                json.dump(config_data, f, indent=2)

            # Optimize assets with safety validation
            asset_results = self.asset_pipeline.optimize_assets(site_dir, site_name)
            if asset_results["status"] == "error":
                logger.warning(f"Asset optimization failed: {asset_results['message']}")

            # Save content metadata to database if available
            metadata_results = None
            if self.content_metadata and hasattr(
                self.content_metadata.db_manager, "create_session"
            ):
                try:
                    db_session = self.content_metadata.db_manager.create_session()  # type: ignore
                    metadata_results = self.content_metadata.create_content_record(  # type: ignore
                        site_name=site_name,
                        content_type="index",
                        metadata=site_config,
                        db_session=db_session,
                    )
                    if metadata_results["status"] == "error":
                        logger.warning(
                            f"Metadata creation failed: {metadata_results['message']}"
                        )
                    if db_session is not None and hasattr(db_session, "close"):
                        db_session.close()
                except Exception as e:
                    logger.warning(f"Database operation failed: {e}")

            return {
                "status": "success",
                "message": f"Website '{site_name}' created successfully",
                "site_path": str(site_dir),
                "manifest": manifest,
                "asset_results": asset_results,
                "metadata_results": metadata_results,
            }

        except Exception as e:
            logger.error(f"Website creation failed: {e}")
            return {"status": "error", "message": f"Website creation failed: {str(e)}"}

    async def create_website_async(self, site_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create website asynchronously with progress tracking"""
        build_id = f"{site_config['name']}_{int(time.time())}"
        progress = BuildProgress(start_time=datetime.now())
        self.active_builds[build_id] = progress

        try:
            progress.status = BuildStatus.BUILDING
            progress.current_step = "Validating configuration"
            progress.progress_percent = 10.0

            # Validate configuration
            self._validate_site_config(site_config)

            # Run pre-build plugins
            progress.current_step = "Running pre-build plugins"
            progress.progress_percent = 20.0
            for plugin in self.plugins:
                try:
                    plugin.pre_build(site_config, self.sites_dir / site_config["name"])
                except Exception as e:
                    logger.warning(f"Plugin pre_build failed: {e}")

            # Create site directory
            progress.current_step = "Creating site directory"
            progress.progress_percent = 30.0
            site_dir = await self._create_site_directory_async(site_config)

            # Copy and customize template
            progress.current_step = "Setting up template"
            progress.progress_percent = 50.0
            await self._setup_template_async(site_dir, site_config)

            # Optimize assets
            progress.current_step = "Optimizing assets"
            progress.progress_percent = 70.0
            asset_results = await self.asset_pipeline.optimize_assets_async(
                site_dir, site_config["name"]
            )

            # Run post-build plugins
            progress.current_step = "Running post-build plugins"
            progress.progress_percent = 90.0
            for plugin in self.plugins:
                try:
                    plugin.post_build(site_config, site_dir)
                except Exception as e:
                    logger.warning(f"Plugin post_build failed: {e}")

            # Finalize
            progress.current_step = "Finalizing"
            progress.progress_percent = 100.0
            result = await self._finalize_site_async(
                site_dir, site_config, asset_results
            )

            progress.status = BuildStatus.SUCCESS
            progress.end_time = datetime.now()

            return result

        except Exception as e:
            progress.status = BuildStatus.FAILED
            progress.error_message = str(e)
            progress.end_time = datetime.now()

            # Run error plugins
            for plugin in self.plugins:
                try:
                    plugin.on_error(e, site_config)
                except Exception as plugin_error:
                    logger.error(f"Plugin error handler failed: {plugin_error}")

            return {
                "status": "error",
                "message": f"Website creation failed: {str(e)}",
                "build_id": build_id,
            }

    async def _create_site_directory_async(self, site_config: Dict[str, Any]) -> Path:
        """Create site directory asynchronously"""
        site_name = site_config["name"]
        site_dir = self.sites_dir / site_name

        # Validate target path
        SafetyValidator.validate_target_path(str(site_dir))

        if site_dir.exists():
            # Create backup before overwriting
            backup_path = await asyncio.get_event_loop().run_in_executor(
                None, self.backup_manager.create_site_backup, site_dir, site_name
            )
            logger.info(f"Backup created before overwrite: {backup_path}")
            await asyncio.get_event_loop().run_in_executor(
                None, shutil.rmtree, site_dir
            )

        # Create site directory
        site_dir.mkdir(parents=True, exist_ok=True)
        return site_dir

    def _create_backup_sync(self, site_dir: Path, site_name: str) -> str:
        """Create backup synchronously"""
        return self.backup_manager.create_site_backup(site_dir, site_name)

    async def _setup_template_async(
        self, site_dir: Path, site_config: Dict[str, Any]
    ) -> None:
        """Setup template asynchronously"""
        template_name = site_config["template"]
        template_info = self.template_manager.get_template(template_name)

        # Check cache first
        theme_name = site_config.get("theme", "default")
        cached_template = self.cache_manager.get_cached_template(
            template_name, theme_name
        )

        if cached_template:
            await asyncio.get_event_loop().run_in_executor(
                None, self._apply_cached_template, site_dir, cached_template
            )
        else:
            # Apply template synchronously (template operations are usually fast)
            await asyncio.get_event_loop().run_in_executor(
                None, self._apply_template_sync, site_dir, template_info, theme_name
            )

    def _apply_cached_template(
        self, site_dir: Path, template_data: Dict[str, Any]
    ) -> None:
        """Apply cached template data"""
        # Implementation for applying cached template
        pass

    def _apply_template_sync(
        self, site_dir: Path, template_info: Dict[str, Any], theme_name: str
    ) -> None:
        """Apply template synchronously"""
        # Copy template files
        template_path = Path(template_info["path"])
        shutil.copytree(template_path, site_dir, dirs_exist_ok=True)

        # Apply theme customizations
        try:
            theme_info = self.theme_manager.get_theme(theme_name)
            theme_path = Path(theme_info["path"]) / "base"
            if theme_path.exists():
                shutil.copytree(theme_path, site_dir, dirs_exist_ok=True)
        except ValueError:
            logger.warning(f"Theme '{theme_name}' not found, using default")

        # Customize website
        self._customize_website(site_dir, {"name": site_dir.name, "theme": theme_name})

    async def _finalize_site_async(
        self, site_dir: Path, site_config: Dict[str, Any], asset_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Finalize site creation asynchronously"""
        # Create site manifest
        manifest = SafetyValidator.create_site_manifest(site_dir, site_config)
        manifest["safety_checks"]["backup_created"] = True
        manifest["asset_optimization"] = asset_results

        # Save manifest
        manifest_path = site_dir / "site.config.json"
        await asyncio.get_event_loop().run_in_executor(
            None, self._save_manifest_sync, manifest_path, manifest
        )

        # Save config.json for backward compatibility
        config_path = site_dir / "config.json"
        config_data = {
            "name": site_config.get("name", ""),
            "title": site_config.get("title", site_config.get("name", "")),
            "description": site_config.get("description", ""),
            "author": site_config.get("author", ""),
            "keywords": site_config.get("keywords", ""),
            "template": site_config.get("template", ""),
            "theme": site_config.get("theme", "default"),
            "created_at": datetime.now().isoformat(),
        }
        await asyncio.get_event_loop().run_in_executor(
            None, self._save_manifest_sync, config_path, config_data
        )

        return {
            "status": "success",
            "message": f"Website '{site_config['name']}' created successfully",
            "site_path": str(site_dir),
            "manifest": manifest,
            "asset_results": asset_results,
        }

    def _save_manifest_sync(
        self, manifest_path: Path, manifest: Dict[str, Any]
    ) -> None:
        """Save manifest synchronously"""
        with open(manifest_path, "w") as f:
            json.dump(manifest, f, indent=2)

    def _validate_site_config(self, config: Dict[str, Any]) -> None:
        """Validate site configuration"""
        required_fields = ["name", "template"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")

        if not config["name"] or not config["name"].strip():
            raise ValueError("Site name cannot be empty")

        if not config["template"] or not config["template"].strip():
            raise ValueError("Template name cannot be empty")

    def get_build_status(self, build_id: str) -> Optional[BuildProgress]:
        """Get build status for a specific build"""
        return self.active_builds.get(build_id)

    def cancel_build(self, build_id: str) -> bool:
        """Cancel a running build"""
        if build_id in self.active_builds:
            progress = self.active_builds[build_id]
            if progress.status == BuildStatus.BUILDING:
                progress.status = BuildStatus.CANCELLED
                progress.end_time = datetime.now()
                return True
        return False

    def clear_completed_builds(self) -> None:
        """Clear completed builds from memory"""
        completed_builds = [
            build_id
            for build_id, progress in self.active_builds.items()
            if progress.status
            in [BuildStatus.SUCCESS, BuildStatus.FAILED, BuildStatus.CANCELLED]
        ]
        for build_id in completed_builds:
            del self.active_builds[build_id]

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the generator"""
        return {
            "active_builds": len(self.active_builds),
            "completed_builds": len(
                [
                    p
                    for p in self.active_builds.values()
                    if p.status in [BuildStatus.SUCCESS, BuildStatus.FAILED]
                ]
            ),
            "cache_hits": len(self.cache_manager.template_cache),
            "plugins_loaded": len(self.plugins),
        }

    def _customize_website(self, site_dir: Path, config: Dict[str, Any]) -> None:
        """Customize website with configuration"""
        # Replace placeholders in HTML files
        for html_file in site_dir.rglob("*.html"):
            try:
                with open(html_file, "r") as f:
                    content = f.read()

                # Replace common placeholders
                content = content.replace("{{SITE_NAME}}", config.get("name", ""))
                content = content.replace(
                    "{{SITE_TITLE}}", config.get("title", config.get("name", ""))
                )
                content = content.replace(
                    "{{SITE_DESCRIPTION}}", config.get("description", "")
                )

                # Replace lowercase placeholders (common in templates)
                content = content.replace(
                    "{{title}}", config.get("title", config.get("name", ""))
                )
                content = content.replace(
                    "{{description}}", config.get("description", "")
                )
                content = content.replace("{{author}}", config.get("author", ""))
                content = content.replace("{{version}}", config.get("version", "1.0"))

                with open(html_file, "w") as f:
                    f.write(content)
            except Exception as e:
                logger.warning(f"Failed to customize {html_file}: {e}")

    def list_sites(self) -> Dict[str, Any]:
        """List all created sites"""
        try:
            sites = []
            total_size = 0

            for site_dir in self.sites_dir.iterdir():
                if site_dir.is_dir():
                    site_size = self._get_directory_size(site_dir)
                    total_size += site_size

                    sites.append(
                        {
                            "name": site_dir.name,
                            "path": str(site_dir),
                            "size_mb": round(site_size / (1024 * 1024), 2),
                            "created": datetime.fromtimestamp(
                                site_dir.stat().st_ctime
                            ).isoformat(),
                            "modified": datetime.fromtimestamp(
                                site_dir.stat().st_mtime
                            ).isoformat(),
                        }
                    )

            return {
                "status": "success",
                "sites": sites,
                "total_sites": len(sites),
                "total_size_mb": round(total_size / (1024 * 1024), 2),
            }

        except Exception as e:
            logger.error(f"Failed to list sites: {e}")
            return {"status": "error", "message": f"Failed to list sites: {str(e)}"}

    def _get_directory_size(self, directory: Path) -> float:
        """Calculate directory size in bytes"""
        total_size = 0
        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            logger.warning(f"Failed to calculate size for {directory}: {e}")
        return total_size

    def delete_site(self, site_name: str) -> Dict[str, Any]:
        """Delete a website"""
        try:
            site_dir = self.sites_dir / site_name
            if not site_dir.exists():
                return {"status": "error", "message": f"Site '{site_name}' not found"}

            # Create backup before deletion
            backup_path = self.backup_manager.create_site_backup(site_dir, site_name)
            logger.info(f"Backup created before deletion: {backup_path}")

            # Delete site directory
            shutil.rmtree(site_dir)

            return {
                "status": "success",
                "message": f"Site '{site_name}' deleted successfully",
                "backup_path": backup_path,
            }

        except Exception as e:
            logger.error(f"Failed to delete site '{site_name}': {e}")
            return {"status": "error", "message": f"Failed to delete site: {str(e)}"}

    def create_theme(self, theme_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new theme"""
        try:
            name = theme_config["name"]
            base_template = theme_config["base_template"]
            customizations = theme_config.get("customizations", {})

            return self.theme_manager.create_theme(name, base_template, customizations)

        except Exception as e:
            logger.error(f"Failed to create theme: {e}")
            return {"status": "error", "message": f"Failed to create theme: {str(e)}"}

    def list_themes(self) -> Dict[str, Any]:
        """List all available themes"""
        return self.theme_manager.list_themes()


class SEOPlugin:
    """SEO optimization plugin for website generation with async support"""

    async def pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """SEO pre-build operations"""
        # Add meta tags and SEO optimization
        return {"status": "success", "message": "SEO pre-build completed"}

    async def post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """SEO post-build operations"""
        # Generate sitemap and robots.txt
        return {"status": "success", "message": "SEO post-build completed"}

    async def on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Handle SEO-related errors"""
        logger.error(f"SEO plugin error: {error}")


class EnhancedWebsiteGenerator:
    """Enhanced WebsiteGenerator with progress tracking and concurrent builds"""

    def __init__(
        self,
        template_manager: TemplateManager,
        database_manager: Optional[DatabaseManagerProtocol] = None,
        max_workers: int = 4,
        max_concurrent_builds: int = 3,
    ):
        self.template_manager = template_manager
        self.database_manager = database_manager
        self.max_workers = max_workers
        self.max_concurrent_builds = max_concurrent_builds
        self.plugins: List[WebsitePlugin] = []
        self.progress_callbacks: Set[ProgressCallback] = set()
        self.active_builds: Dict[str, BuildProgress] = {}
        self.build_semaphore = asyncio.Semaphore(max_concurrent_builds)
        self.cache_manager = AsyncCacheManager()
        self.asset_pipeline = AssetPipeline()

    def add_plugin(self, plugin: WebsitePlugin) -> None:
        """Add a plugin to the generator"""
        self.plugins.append(plugin)

    def add_progress_callback(self, callback: ProgressCallback) -> None:
        """Add a progress callback for real-time updates"""
        self.progress_callbacks.add(callback)

    def remove_progress_callback(self, callback: ProgressCallback) -> None:
        """Remove a progress callback"""
        self.progress_callbacks.discard(callback)

    async def _notify_progress(self, progress: BuildProgress) -> None:
        """Notify all progress callbacks"""
        for callback in self.progress_callbacks:
            try:
                await callback(progress)
            except Exception as e:
                logger.error(f"Progress callback error: {e}")

    async def create_website_async(
        self,
        site_config: Dict[str, Any],
        progress_callback: Optional[ProgressCallback] = None,
    ) -> Dict[str, Any]:
        """Create website with enhanced async support and progress tracking"""
        async with self.build_semaphore:
            build_id = f"build_{int(time.time())}_{site_config.get('name', 'unknown')}"

            progress = BuildProgress(
                status=BuildStatus.BUILDING,
                build_id=build_id,
                site_name=site_config.get("name", "unknown"),
                start_time=datetime.now(timezone.utc),
                total_steps=8,
                current_step="Initializing build",
            )

            self.active_builds[build_id] = progress

            if progress_callback:
                self.add_progress_callback(progress_callback)

            try:
                # Step 1: Validate configuration
                progress.current_step = "Validating configuration"
                progress.steps_completed = 1
                await self._notify_progress(progress)
                await self._validate_site_config_async(site_config)

                # Step 2: Create site directory
                progress.current_step = "Creating site directory"
                progress.steps_completed = 2
                await self._notify_progress(progress)
                site_dir = await self._create_site_directory_async(site_config)

                # Step 3: Run pre-build plugins
                progress.current_step = "Running pre-build plugins"
                progress.steps_completed = 3
                await self._notify_progress(progress)
                await self._run_pre_build_plugins_async(site_config, site_dir)

                # Step 4: Setup template
                progress.current_step = "Setting up template"
                progress.steps_completed = 4
                await self._notify_progress(progress)
                await self._setup_template_async(site_dir, site_config)

                # Step 5: Customize website
                progress.current_step = "Customizing website"
                progress.steps_completed = 5
                await self._notify_progress(progress)
                await self._customize_website_async(site_dir, site_config)

                # Step 6: Optimize assets
                progress.current_step = "Optimizing assets"
                progress.steps_completed = 6
                await self._notify_progress(progress)
                asset_results = await self.asset_pipeline.optimize_assets_async(
                    site_dir, site_config.get("name", "unknown")
                )

                # Step 7: Run post-build plugins
                progress.current_step = "Running post-build plugins"
                progress.steps_completed = 7
                await self._notify_progress(progress)
                await self._run_post_build_plugins_async(site_config, site_dir)

                # Step 8: Finalize site
                progress.current_step = "Finalizing site"
                progress.steps_completed = 8
                await self._notify_progress(progress)
                result = await self._finalize_site_async(
                    site_dir, site_config, asset_results
                )

                progress.status = BuildStatus.SUCCESS
                progress.end_time = datetime.now(timezone.utc)
                progress.progress_percent = 100.0
                await self._notify_progress(progress)

                return result

            except Exception as e:
                progress.status = BuildStatus.FAILED
                progress.error_message = str(e)
                progress.end_time = datetime.now(timezone.utc)
                await self._notify_progress(progress)
                await self._run_error_plugins_async(e, site_config)
                raise
            finally:
                if progress_callback:
                    self.remove_progress_callback(progress_callback)
                if build_id in self.active_builds:
                    del self.active_builds[build_id]

    async def _validate_site_config_async(self, config: Dict[str, Any]) -> None:
        """Validate site configuration asynchronously"""
        required_fields = ["name", "template", "theme"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field: {field}")

    async def _create_site_directory_async(self, site_config: Dict[str, Any]) -> Path:
        """Create site directory asynchronously"""
        site_name = site_config["name"]
        site_dir = Path(f"sites/{site_name}")
        site_dir.mkdir(parents=True, exist_ok=True)
        return site_dir

    async def _run_pre_build_plugins_async(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> None:
        """Run pre-build plugins asynchronously"""
        for plugin in self.plugins:
            try:
                await plugin.pre_build(site_config, site_dir)
            except Exception as e:
                logger.error(f"Pre-build plugin error: {e}")

    async def _setup_template_async(
        self, site_dir: Path, site_config: Dict[str, Any]
    ) -> None:
        """Setup template asynchronously"""
        template_name = site_config["template"]
        theme_name = site_config.get("theme", "default")

        # Try to get cached template
        cached_template = await self.cache_manager.get_cached_template(
            template_name, theme_name
        )
        if cached_template:
            await self._apply_cached_template_async(site_dir, cached_template)
        else:
            template_info = self.template_manager.get_template(template_name)
            await self._apply_template_async(site_dir, template_info, theme_name)

    async def _apply_cached_template_async(
        self, site_dir: Path, template_data: Dict[str, Any]
    ) -> None:
        """Apply cached template asynchronously"""
        # Implementation for applying cached template
        pass

    async def _apply_template_async(
        self, site_dir: Path, template_info: Dict[str, Any], theme_name: str
    ) -> None:
        """Apply template asynchronously"""
        self._copy_template_files(template_info, site_dir)

    def _copy_template_files(
        self, template_info: Dict[str, Any], site_dir: Path
    ) -> None:
        """Copy template files to site directory"""
        template_path = Path(template_info.get("path", ""))
        if template_path.exists():
            shutil.copytree(template_path, site_dir, dirs_exist_ok=True)

    async def _customize_website_async(
        self, site_dir: Path, config: Dict[str, Any]
    ) -> None:
        """Customize website asynchronously"""
        self._apply_customizations(site_dir, config)

    def _apply_customizations(self, site_dir: Path, config: Dict[str, Any]) -> None:
        """Apply customizations to website"""
        customizations = config.get("customizations", {})
        # Apply customizations logic here
        pass

    async def _run_post_build_plugins_async(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> None:
        """Run post-build plugins asynchronously"""
        for plugin in self.plugins:
            try:
                await plugin.post_build(site_config, site_dir)
            except Exception as e:
                logger.error(f"Post-build plugin error: {e}")

    async def _run_error_plugins_async(
        self, error: Exception, site_config: Dict[str, Any]
    ) -> None:
        """Run error plugins asynchronously"""
        for plugin in self.plugins:
            try:
                await plugin.on_error(error, site_config)
            except Exception as e:
                logger.error(f"Error plugin error: {e}")

    async def _finalize_site_async(
        self, site_dir: Path, site_config: Dict[str, Any], asset_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Finalize site asynchronously"""
        site_name = site_config["name"]

        # Calculate directory size
        directory_size = await self._get_directory_size_async(site_dir)

        # Create manifest
        manifest = {
            "site_name": site_name,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "template": site_config["template"],
            "theme": site_config.get("theme", "default"),
            "directory_size": directory_size,
            "asset_optimization": asset_results,
            "build_id": f"build_{int(time.time())}",
        }

        manifest_path = site_dir / "manifest.json"
        self._save_manifest(manifest_path, manifest)

        return {
            "success": True,
            "site_name": site_name,
            "site_path": str(site_dir),
            "directory_size": directory_size,
            "manifest": manifest,
        }

    async def _get_directory_size_async(self, directory: Path) -> float:
        """Get directory size asynchronously"""
        return self._calculate_directory_size(directory)

    def _calculate_directory_size(self, directory: Path) -> float:
        """Calculate directory size in MB"""
        total_size = 0
        for path in directory.rglob("*"):
            if path.is_file():
                total_size += path.stat().st_size
        return total_size / (1024 * 1024)  # Convert to MB

    def _save_manifest(self, manifest_path: Path, manifest: Dict[str, Any]) -> None:
        """Save manifest file"""
        with open(manifest_path, "w") as f:
            json.dump(manifest, f, indent=2)

    async def get_build_status(self, build_id: str) -> Optional[BuildProgress]:
        """Get build status"""
        return self.active_builds.get(build_id)

    async def cancel_build(self, build_id: str) -> bool:
        """Cancel a build"""
        if build_id in self.active_builds:
            progress = self.active_builds[build_id]
            progress.status = BuildStatus.CANCELLED
            progress.end_time = datetime.now(timezone.utc)
            await self._notify_progress(progress)
            del self.active_builds[build_id]
            return True
        return False

    async def list_active_builds(self) -> Dict[str, BuildProgress]:
        """List all active builds"""
        return self.active_builds.copy()

    async def cleanup(self):
        """Cleanup resources"""
        await self.cache_manager.clear_cache()
        await self.asset_pipeline.cleanup()


async def main():
    """Main function for testing"""
    # Create template manager
    template_manager = TemplateManager()

    # Create website generator
    generator = WebsiteGenerator(template_manager)

    # Add SEO plugin
    generator.add_plugin(SEOPlugin())

    # Create a test website
    site_config = {
        "name": "test-site",
        "template": "modern",
        "title": "Test Website",
        "description": "A test website",
    }

    result = await generator.create_website_async(site_config)
    print(f"Website creation result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
