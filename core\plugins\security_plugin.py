"""
Security Plugin for WebsiteGenerator
Provides security hardening features for generated websites.
"""

import json
import re
from pathlib import Path
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from core.plugins.base import PluginBase, PluginConfig, PluginPriority


class SecurityPlugin(PluginBase):
    """Security hardening plugin"""

    def __init__(self, config: PluginConfig):
        super().__init__(config)
        self.security_settings = config.settings.get("security", {})
        self.add_csp = config.settings.get("add_csp", True)
        self.add_hsts = config.settings.get("add_hsts", True)
        self.add_xss_protection = config.settings.get("add_xss_protection", True)
        self.add_content_type_options = config.settings.get(
            "add_content_type_options", True
        )
        self.add_frame_options = config.settings.get("add_frame_options", True)
        self.scan_vulnerabilities = config.settings.get("scan_vulnerabilities", True)

    async def _initialize_plugin(self) -> None:
        """Initialize security plugin"""
        self.logger.info("Security plugin initialized")

    async def _cleanup_plugin(self) -> None:
        """Cleanup security plugin"""
        self.logger.info("Security plugin cleaned up")

    async def _pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Pre-build security analysis"""
        try:
            # Analyze security posture
            security_analysis = await self._analyze_security(site_dir)

            # Generate security plan
            security_plan = self._generate_security_plan(security_analysis)

            return {
                "security_analysis": security_analysis,
                "security_plan": security_plan,
                "hardening_applied": [],
            }

        except Exception as e:
            self.logger.error(f"Security pre_build failed: {e}")
            return {"error": str(e)}

    async def _post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Post-build security hardening"""
        try:
            hardening = []

            # Add Content Security Policy
            if self.add_csp:
                csp_result = await self._add_csp_headers(site_dir)
                if csp_result:
                    hardening.append("content_security_policy")

            # Add HSTS headers
            if self.add_hsts:
                hsts_result = await self._add_hsts_headers(site_dir)
                if hsts_result:
                    hardening.append("hsts_headers")

            # Add XSS protection
            if self.add_xss_protection:
                xss_result = await self._add_xss_protection(site_dir)
                if xss_result:
                    hardening.append("xss_protection")

            # Add content type options
            if self.add_content_type_options:
                cto_result = await self._add_content_type_options(site_dir)
                if cto_result:
                    hardening.append("content_type_options")

            # Add frame options
            if self.add_frame_options:
                fo_result = await self._add_frame_options(site_dir)
                if fo_result:
                    hardening.append("frame_options")

            # Scan for vulnerabilities
            if self.scan_vulnerabilities:
                scan_result = await self._scan_vulnerabilities(site_dir)
                if scan_result:
                    hardening.append("vulnerability_scan")

            # Generate security report
            security_report = await self._generate_security_report(site_dir)

            return {
                "hardening_applied": hardening,
                "security_report": security_report,
                "recommendations": self._generate_security_recommendations(site_dir),
            }

        except Exception as e:
            self.logger.error(f"Security post_build failed: {e}")
            return {"error": str(e)}

    async def _on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Handle security-related errors"""
        self.logger.error(f"Security plugin encountered error: {error}")

    async def _analyze_security(self, site_dir: Path) -> Dict[str, Any]:
        """Analyze security posture of the website"""
        try:
            analysis = {
                "total_files": 0,
                "html_files": [],
                "js_files": [],
                "css_files": [],
                "security_issues": [],
                "vulnerabilities": [],
            }

            # Analyze all files
            for file_path in site_dir.rglob("*"):
                if file_path.is_file():
                    analysis["total_files"] += 1

                    # Categorize files
                    if file_path.suffix.lower() == ".html":
                        analysis["html_files"].append(str(file_path))
                    elif file_path.suffix.lower() == ".js":
                        analysis["js_files"].append(str(file_path))
                    elif file_path.suffix.lower() == ".css":
                        analysis["css_files"].append(str(file_path))

            # Check for common security issues
            await self._check_security_issues(site_dir, analysis)

            return analysis

        except Exception as e:
            self.logger.error(f"Failed to analyze security: {e}")
            return {}

    async def _check_security_issues(
        self, site_dir: Path, analysis: Dict[str, Any]
    ) -> None:
        """Check for common security issues"""
        try:
            # Check HTML files for security issues
            for html_file in site_dir.rglob("*.html"):
                with open(html_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # Check for inline scripts
                if re.search(r"<script[^>]*>", content, re.IGNORECASE):
                    analysis["security_issues"].append(
                        f"Inline scripts found in {html_file.name}"
                    )

                # Check for inline styles
                if re.search(r"<style[^>]*>", content, re.IGNORECASE):
                    analysis["security_issues"].append(
                        f"Inline styles found in {html_file.name}"
                    )

                # Check for external scripts without integrity
                script_tags = re.findall(
                    r'<script[^>]*src="([^"]*)"[^>]*>', content, re.IGNORECASE
                )
                for script_src in script_tags:
                    if not re.search(r"integrity=", script_src, re.IGNORECASE):
                        analysis["security_issues"].append(
                            f"External script without integrity: {script_src}"
                        )

                # Check for external styles without integrity
                link_tags = re.findall(
                    r'<link[^>]*href="([^"]*\.css)"[^>]*>', content, re.IGNORECASE
                )
                for link_href in link_tags:
                    if not re.search(r"integrity=", link_href, re.IGNORECASE):
                        analysis["security_issues"].append(
                            f"External CSS without integrity: {link_href}"
                        )

            # Check JavaScript files for vulnerabilities
            for js_file in site_dir.rglob("*.js"):
                with open(js_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # Check for eval usage
                if "eval(" in content:
                    analysis["vulnerabilities"].append(
                        f"eval() usage found in {js_file.name}"
                    )

                # Check for innerHTML usage
                if "innerHTML" in content:
                    analysis["security_issues"].append(
                        f"innerHTML usage found in {js_file.name}"
                    )

                # Check for document.write usage
                if "document.write(" in content:
                    analysis["security_issues"].append(
                        f"document.write() usage found in {js_file.name}"
                    )

        except Exception as e:
            self.logger.error(f"Failed to check security issues: {e}")

    def _generate_security_plan(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate security hardening plan"""
        plan = {
            "headers_to_add": [],
            "vulnerabilities_to_fix": [],
            "best_practices_to_implement": [],
            "estimated_improvement": 0,
        }

        # Add security headers
        plan["headers_to_add"].extend(
            [
                "Content-Security-Policy",
                "Strict-Transport-Security",
                "X-Content-Type-Options",
                "X-Frame-Options",
                "X-XSS-Protection",
            ]
        )

        # Fix vulnerabilities
        if analysis.get("vulnerabilities"):
            plan["vulnerabilities_to_fix"].extend(analysis["vulnerabilities"])
            plan["estimated_improvement"] += len(analysis["vulnerabilities"]) * 10

        # Implement best practices
        if analysis.get("security_issues"):
            plan["best_practices_to_implement"].extend(analysis["security_issues"])
            plan["estimated_improvement"] += len(analysis["security_issues"]) * 5

        return plan

    async def _add_csp_headers(self, site_dir: Path) -> bool:
        """Add Content Security Policy headers to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_csp_to_file(html_file)

            self.logger.info(f"Added CSP headers to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add CSP headers: {e}")
            return False

    async def _add_csp_to_file(self, html_file: Path) -> None:
        """Add CSP to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Generate CSP policy
            csp_policy = self._generate_csp_policy(content)

            # Add CSP meta tag
            csp_meta = (
                f'<meta http-equiv="Content-Security-Policy" content="{csp_policy}">'
            )

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + "    " + csp_meta + "\n" + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add CSP to {html_file}: {e}")

    def _generate_csp_policy(self, content: str) -> str:
        """Generate Content Security Policy based on content"""
        # Basic CSP policy
        csp_parts = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self'",
            "connect-src 'self'",
            "frame-src 'none'",
            "object-src 'none'",
        ]

        # Check for external resources and adjust policy
        external_scripts = re.findall(
            r'<script[^>]*src="(https?://[^"]*)"', content, re.IGNORECASE
        )
        external_styles = re.findall(
            r'<link[^>]*href="(https?://[^"]*\.css)"', content, re.IGNORECASE
        )

        if external_scripts:
            domains = set(urlparse(src).netloc for src in external_scripts)
            csp_parts.append(f"script-src 'self' 'unsafe-inline' {' '.join(domains)}")

        if external_styles:
            domains = set(urlparse(src).netloc for src in external_styles)
            csp_parts.append(f"style-src 'self' 'unsafe-inline' {' '.join(domains)}")

        return "; ".join(csp_parts)

    async def _add_hsts_headers(self, site_dir: Path) -> bool:
        """Add HSTS headers to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_hsts_to_file(html_file)

            self.logger.info(f"Added HSTS headers to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add HSTS headers: {e}")
            return False

    async def _add_hsts_to_file(self, html_file: Path) -> None:
        """Add HSTS to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add HSTS meta tag
            hsts_meta = '<meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">'

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + "    " + hsts_meta + "\n" + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add HSTS to {html_file}: {e}")

    async def _add_xss_protection(self, site_dir: Path) -> bool:
        """Add XSS protection headers to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_xss_protection_to_file(html_file)

            self.logger.info(f"Added XSS protection to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add XSS protection: {e}")
            return False

    async def _add_xss_protection_to_file(self, html_file: Path) -> None:
        """Add XSS protection to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add XSS protection meta tag
            xss_meta = '<meta http-equiv="X-XSS-Protection" content="1; mode=block">'

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + "    " + xss_meta + "\n" + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add XSS protection to {html_file}: {e}")

    async def _add_content_type_options(self, site_dir: Path) -> bool:
        """Add content type options headers to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_content_type_options_to_file(html_file)

            self.logger.info(
                f"Added content type options to {len(html_files)} HTML files"
            )
            return True

        except Exception as e:
            self.logger.error(f"Failed to add content type options: {e}")
            return False

    async def _add_content_type_options_to_file(self, html_file: Path) -> None:
        """Add content type options to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add content type options meta tag
            cto_meta = '<meta http-equiv="X-Content-Type-Options" content="nosniff">'

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + "    " + cto_meta + "\n" + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add content type options to {html_file}: {e}")

    async def _add_frame_options(self, site_dir: Path) -> bool:
        """Add frame options headers to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_frame_options_to_file(html_file)

            self.logger.info(f"Added frame options to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add frame options: {e}")
            return False

    async def _add_frame_options_to_file(self, html_file: Path) -> None:
        """Add frame options to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add frame options meta tag
            fo_meta = '<meta http-equiv="X-Frame-Options" content="DENY">'

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + "    " + fo_meta + "\n" + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add frame options to {html_file}: {e}")

    async def _scan_vulnerabilities(self, site_dir: Path) -> bool:
        """Scan for vulnerabilities"""
        try:
            # This is a placeholder for vulnerability scanning
            # In a real implementation, this would:
            # 1. Scan for common vulnerabilities
            # 2. Check for outdated dependencies
            # 3. Validate security configurations

            self.logger.info("Vulnerability scan completed")
            return True

        except Exception as e:
            self.logger.error(f"Failed to scan vulnerabilities: {e}")
            return False

    async def _generate_security_report(self, site_dir: Path) -> Dict[str, Any]:
        """Generate security report"""
        try:
            # Re-analyze after hardening
            analysis = await self._analyze_security(site_dir)

            # Calculate security score
            score = self._calculate_security_score(analysis)

            return {
                "total_files": analysis.get("total_files", 0),
                "security_score": score,
                "vulnerabilities_found": len(analysis.get("vulnerabilities", [])),
                "security_issues_found": len(analysis.get("security_issues", [])),
                "headers_added": [
                    "Content-Security-Policy",
                    "Strict-Transport-Security",
                    "X-Content-Type-Options",
                    "X-Frame-Options",
                    "X-XSS-Protection",
                ],
            }

        except Exception as e:
            self.logger.error(f"Failed to generate security report: {e}")
            return {}

    def _calculate_security_score(self, analysis: Dict[str, Any]) -> int:
        """Calculate security score"""
        score = 100

        # Deduct points for vulnerabilities
        vulnerabilities_count = len(analysis.get("vulnerabilities", []))
        score -= vulnerabilities_count * 15

        # Deduct points for security issues
        issues_count = len(analysis.get("security_issues", []))
        score -= issues_count * 5

        return max(0, score)

    def _generate_security_recommendations(self, site_dir: Path) -> List[str]:
        """Generate security recommendations"""
        recommendations = []

        # Check for common security issues
        html_files = list(site_dir.rglob("*.html"))
        for html_file in html_files:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

                # Check for missing security headers
                if "Content-Security-Policy" not in content:
                    recommendations.append(
                        f"Add Content Security Policy to {html_file.name}"
                    )

                if "Strict-Transport-Security" not in content:
                    recommendations.append(f"Add HSTS header to {html_file.name}")

                # Check for inline scripts
                if re.search(r"<script[^>]*>", content, re.IGNORECASE):
                    recommendations.append(
                        f"Remove inline scripts from {html_file.name}"
                    )

        return recommendations


# Plugin configuration
security_plugin_config = PluginConfig(
    name="security_hardener",
    version="1.0.0",
    description="Security hardening plugin for websites",
    author="WebsiteGenerator Team",
    priority=PluginPriority.CRITICAL,
    enabled=True,
    settings={
        "security": {"enabled": True, "target_score": 95},
        "add_csp": True,
        "add_hsts": True,
        "add_xss_protection": True,
        "add_content_type_options": True,
        "add_frame_options": True,
        "scan_vulnerabilities": True,
    },
)
