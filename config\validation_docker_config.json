{"validation_service": {"enabled": true, "port": 8004, "host": "0.0.0.0", "docker": {"image": "containers-validation:latest", "container_name": "ai-coding-validation", "ports": {"host": 8004, "container": 8004}, "volumes": {"source": "./validation", "target": "/app/validation", "mode": "ro"}, "environment": {"VALIDATION_SERVICE_ENABLED": "true", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "PORT": "8004", "VALIDATION_HOST": "0.0.0.0", "VALIDATION_PORT": "8004"}, "health_check": {"interval": "30s", "timeout": "10s", "retries": 3, "start_period": "40s"}}}}