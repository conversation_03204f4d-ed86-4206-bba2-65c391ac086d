"""
Complex Tasks CLI Commands

Command-line interface for managing complex tasks including
task creation, execution, monitoring, and reporting.
"""

import asyncio
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import click

from complex_tasks import ComplexTaskManager
from complex_tasks.models import TaskStatus, TaskType
from utils.config_loader import Config<PERSON>oader
from utils.logger import get_logger

logger = get_logger(__name__)


@click.group()
def complex_tasks():
    """Complex Tasks Management Commands"""
    pass


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--title", "-t", required=True, help="Task title")
@click.option("--description", "-d", required=True, help="Task description")
@click.option(
    "--type",
    "task_type",
    required=True,
    type=click.Choice(
        [
            "architecture_design",
            "system_integration",
            "performance_optimization",
            "complex_problem_solving",
        ]
    ),
    help="Task type",
)
@click.option(
    "--complexity",
    required=True,
    type=click.Choice(["simple", "moderate", "complex", "very_complex", "extreme"]),
    help="Task complexity",
)
@click.option("--cpu-cores", default=2, help="CPU cores required")
@click.option("--memory-gb", default=4, help="Memory in GB required")
@click.option("--gpu-required", is_flag=True, help="GPU required")
@click.option("--gpu-memory-gb", default=0, help="GPU memory in GB")
@click.option("--priority", default=5, help="Task priority (1-10)")
@click.option("--deadline", help="Task deadline (YYYY-MM-DD HH:MM:SS)")
@click.option("--requirements", multiple=True, help="Task requirements")
@click.option("--constraints", multiple=True, help="Task constraints")
@click.option("--tags", multiple=True, help="Task tags")
@click.option("--output", "-o", help="Output file for task data")
def create(
    config: str,
    title: str,
    description: str,
    task_type: str,
    complexity: str,
    cpu_cores: int,
    memory_gb: int,
    gpu_required: bool,
    gpu_memory_gb: int,
    priority: int,
    deadline: Optional[str],
    requirements: tuple,
    constraints: tuple,
    tags: tuple,
    output: Optional[str],
):
    """Create a new complex task"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Prepare task data
        task_data = {
            "title": title,
            "description": description,
            "task_type": task_type,
            "complexity": complexity,
            "resource_allocation": {
                "cpu_cores": cpu_cores,
                "memory_gb": memory_gb,
                "gpu_required": gpu_required,
                "gpu_memory_gb": gpu_memory_gb if gpu_required else 0,
                "storage_gb": 10,
                "network_bandwidth_mbps": 100,
                "estimated_duration_hours": complex_config.get("task_complexity", {})
                .get(complexity, {})
                .get("estimated_duration_hours", 4.0),
                "max_concurrent_tasks": 1,
                "priority_level": priority,
            },
            "requirements": list(requirements),
            "constraints": list(constraints),
            "priority": priority,
            "tags": list(tags),
        }

        if deadline:
            task_data["deadline"] = deadline

        # Create task
        async def create_task():
            return await task_manager.create_task(task_data)

        task = asyncio.run(create_task())

        # Output result
        result = {
            "task_id": task.task_id,
            "title": task.title,
            "status": task.status.value,
            "created_at": task.created_at.isoformat(),
            "priority": task.priority,
            "complexity": task.complexity.value,
            "task_type": task.task_type.value,
        }

        if output:
            with open(output, "w") as f:
                json.dump(result, f, indent=2)
            click.echo(f"Task created and saved to {output}")
        else:
            click.echo(json.dumps(result, indent=2))

        logger.info(f"Created complex task: {task.task_id} - {task.title}")

    except Exception as e:
        click.echo(f"Error creating task: {e}", err=True)
        logger.error(f"Error creating task: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", required=True, help="Task ID to execute")
@click.option("--output", "-o", help="Output file for execution results")
def execute(config: str, task_id: str, output: Optional[str]):
    """Execute a complex task"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Execute task
        async def execute_task():
            return await task_manager.execute_task(task_id)

        result = asyncio.run(execute_task())

        # Output result
        if output:
            with open(output, "w") as f:
                json.dump(result, f, indent=2, default=str)
            click.echo(f"Task execution results saved to {output}")
        else:
            click.echo(json.dumps(result, indent=2, default=str))

        logger.info(f"Executed complex task: {task_id}")

    except Exception as e:
        click.echo(f"Error executing task: {e}", err=True)
        logger.error(f"Error executing task: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option(
    "--status",
    type=click.Choice(["pending", "in_progress", "completed", "failed", "cancelled"]),
    help="Filter by status",
)
@click.option(
    "--type",
    "task_type",
    type=click.Choice(
        [
            "architecture_design",
            "system_integration",
            "performance_optimization",
            "complex_problem_solving",
        ]
    ),
    help="Filter by task type",
)
@click.option("--output", "-o", help="Output file for task list")
def list_tasks(
    config: str, status: Optional[str], task_type: Optional[str], output: Optional[str]
):
    """List complex tasks"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # List tasks
        async def list_tasks_async():
            return await task_manager.list_tasks(
                status=TaskStatus(status) if status else None,
                task_type=TaskType(task_type) if task_type else None,
            )

        tasks = asyncio.run(list_tasks_async())

        # Output result
        if output:
            with open(output, "w") as f:
                json.dump(tasks, f, indent=2, default=str)
            click.echo(f"Task list saved to {output}")
        else:
            click.echo(json.dumps(tasks, indent=2, default=str))

        logger.info(f"Listed {len(tasks)} complex tasks")

    except Exception as e:
        click.echo(f"Error listing tasks: {e}", err=True)
        logger.error(f"Error listing tasks: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", required=True, help="Task ID to get status for")
@click.option("--output", "-o", help="Output file for task status")
def status(config: str, task_id: str, output: Optional[str]):
    """Get detailed status of a complex task"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Get task status
        async def get_status():
            return await task_manager.get_task_status(task_id)

        status_data = asyncio.run(get_status())

        # Output result
        if output:
            with open(output, "w") as f:
                json.dump(status_data, f, indent=2, default=str)
            click.echo(f"Task status saved to {output}")
        else:
            click.echo(json.dumps(status_data, indent=2, default=str))

        logger.info(f"Retrieved status for task: {task_id}")

    except Exception as e:
        click.echo(f"Error getting task status: {e}", err=True)
        logger.error(f"Error getting task status: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--output", "-o", help="Output file for analytics")
def analytics(config: str, output: Optional[str]):
    """Get analytics for all complex tasks"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Get analytics
        async def get_analytics():
            return await task_manager.get_task_analytics()

        analytics_data = asyncio.run(get_analytics())

        # Output result
        if output:
            with open(output, "w") as f:
                json.dump(analytics_data, f, indent=2, default=str)
            click.echo(f"Analytics saved to {output}")
        else:
            click.echo(json.dumps(analytics_data, indent=2, default=str))

        logger.info("Retrieved complex tasks analytics")

    except Exception as e:
        click.echo(f"Error getting analytics: {e}", err=True)
        logger.error(f"Error getting analytics: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", required=True, help="Task ID to cancel")
def cancel(config: str, task_id: str):
    """Cancel a running complex task"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Cancel task
        async def cancel_task():
            await task_manager.cancel_task(task_id)

        asyncio.run(cancel_task())

        click.echo(f"Task {task_id} cancelled successfully")
        logger.info(f"Cancelled complex task: {task_id}")

    except Exception as e:
        click.echo(f"Error cancelling task: {e}", err=True)
        logger.error(f"Error cancelling task: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", required=True, help="Task ID to update")
@click.option("--title", help="New task title")
@click.option("--description", help="New task description")
@click.option("--priority", type=int, help="New task priority (1-10)")
@click.option("--deadline", help="New task deadline (YYYY-MM-DD HH:MM:SS)")
@click.option("--tags", multiple=True, help="New task tags")
@click.option("--output", "-o", help="Output file for updated task")
def update(
    config: str,
    task_id: str,
    title: Optional[str],
    description: Optional[str],
    priority: Optional[int],
    deadline: Optional[str],
    tags: tuple,
    output: Optional[str],
):
    """Update a complex task"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Prepare updates
        updates: Dict[str, Any] = {}
        if title:
            updates["title"] = title
        if description:
            updates["description"] = description
        if priority is not None:
            updates["priority"] = priority
        if deadline:
            updates["deadline"] = deadline
        if tags:
            updates["tags"] = list(tags)

        # Update task
        async def update_task():
            return await task_manager.update_task(task_id, updates)

        updated_task = asyncio.run(update_task())

        # Output result
        result = {
            "task_id": updated_task.task_id,
            "title": updated_task.title,
            "status": updated_task.status.value,
            "updated_at": updated_task.updated_at.isoformat(),
            "priority": updated_task.priority,
            "tags": updated_task.tags,
        }

        if output:
            with open(output, "w") as f:
                json.dump(result, f, indent=2)
            click.echo(f"Updated task saved to {output}")
        else:
            click.echo(json.dumps(result, indent=2))

        logger.info(f"Updated complex task: {task_id}")

    except Exception as e:
        click.echo(f"Error updating task: {e}", err=True)
        logger.error(f"Error updating task: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--days", default=30, help="Number of days to keep completed tasks")
def cleanup(config: str, days: int):
    """Clean up completed tasks older than specified days"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Cleanup tasks
        async def cleanup_tasks():
            return await task_manager.cleanup_completed_tasks(days)

        cleaned_count = asyncio.run(cleanup_tasks())

        click.echo(f"Cleaned up {cleaned_count} completed tasks older than {days} days")
        logger.info(f"Cleaned up {cleaned_count} completed tasks")

    except Exception as e:
        click.echo(f"Error cleaning up tasks: {e}", err=True)
        logger.error(f"Error cleaning up tasks: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", help="Task ID for specific report (optional)")
@click.option("--output", "-o", help="Output file for progress report")
def report(config: str, task_id: Optional[str], output: Optional[str]):
    """Generate progress report for complex tasks"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Generate report
        async def generate_report():
            if task_id:
                return await task_manager.progress_tracker.generate_progress_report(
                    task_id
                )
            else:
                return await task_manager.progress_tracker.generate_progress_report()

        report_content = asyncio.run(generate_report())

        # Output result
        if output:
            with open(output, "w") as f:
                f.write(report_content)
            click.echo(f"Progress report saved to {output}")
        else:
            click.echo(report_content)

        logger.info(
            f"Generated progress report for {'task ' + task_id if task_id else 'all tasks'}"
        )

    except Exception as e:
        click.echo(f"Error generating report: {e}", err=True)
        logger.error(f"Error generating report: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", help="Task ID for specific export (optional)")
@click.option("--output", "-o", required=True, help="Output file for data export")
def export(config: str, task_id: Optional[str], output: str):
    """Export complex tasks data"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        # Export data
        async def export_data():
            if task_id:
                await task_manager.progress_tracker.export_progress_data(
                    output, task_id
                )
            else:
                await task_manager.progress_tracker.export_progress_data(output)

        asyncio.run(export_data())

        click.echo(f"Complex tasks data exported to {output}")
        logger.info(f"Exported complex tasks data to {output}")

    except Exception as e:
        click.echo(f"Error exporting data: {e}", err=True)
        logger.error(f"Error exporting data: {e}")


@complex_tasks.command()
@click.option(
    "--config",
    "-c",
    default="config/complex_tasks_config.json",
    help="Configuration file path",
)
@click.option("--task-id", required=True, help="Task ID to monitor")
@click.option("--interval", default=30, help="Monitoring interval in seconds")
def monitor(config: str, task_id: str, interval: int):
    """Monitor a complex task in real-time"""
    try:
        # Load configuration
        config_data = ConfigLoader.load_json_config(config, {})
        complex_config = config_data.get("complex_tasks", {})

        # Create task manager
        task_manager = ComplexTaskManager(complex_config)

        click.echo(f"Monitoring task {task_id} (Press Ctrl+C to stop)...")

        async def monitor_task():
            while True:
                try:
                    status = await task_manager.get_task_status(task_id)
                    progress = status["task"]["completion_percentage"]
                    current_phase = (
                        status["latest_progress"]["current_phase"]
                        if status["latest_progress"]
                        else "Unknown"
                    )

                    click.echo(
                        f"[{datetime.now().strftime('%H:%M:%S')}] Progress: {progress:.1f}% | Phase: {current_phase}"
                    )

                    if progress >= 100.0:
                        click.echo("Task completed!")
                        break

                    await asyncio.sleep(interval)

                except KeyboardInterrupt:
                    click.echo("\nMonitoring stopped by user")
                    break
                except Exception as e:
                    click.echo(f"Error monitoring task: {e}")
                    break

        asyncio.run(monitor_task())

    except Exception as e:
        click.echo(f"Error starting monitoring: {e}", err=True)
        logger.error(f"Error starting monitoring: {e}")


# Import required classes for the CLI
