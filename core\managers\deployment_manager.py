import json
import logging
import os
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# Import the centralized config loader
from config import load_config


# Simple config loader replacement
class ConfigLoader:
    @staticmethod
    def load_config_with_defaults(config_path, defaults):
        try:
            config = load_config(config_path)
            # Merge with defaults
            for key, value in defaults.items():
                if key not in config:
                    config[key] = value
            return config
        except (FileNotFoundError, ImportError):
            return defaults


# Set up logging
logging.basicConfig(
    filename="logs/deployment.log",
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)


class DeploymentManager:
    """Unified deployment manager supporting basic and automated deployments"""

    def __init__(
        self,
        config_path: str = "config/deployment.json",
        config_dict: Optional[Dict[str, Any]] = None,
        backup_manager=None,
    ):
        if config_dict:
            # Initialize with config dictionary (automated deployment mode)
            self.config = config_dict
            self.backup_manager = backup_manager
            self.sites_dir = Path(config_dict.get("sites_dir", "sites"))
            self.deployments: Dict[str, Any] = {}
            self.deploy_dir = Path(config_dict.get("deploy_dir", "deployments"))
        else:
            # Initialize with config file (basic deployment mode)
            self.config = ConfigLoader.load_config_with_defaults(
                config_path,
                {
                    "sites_dir": "sites",
                    "deploy_dir": "deployments",
                    "backup_dir": "backups",
                    "domain": "localhost",
                    "port": 5000,
                    "ssl": {
                        "enabled": False,
                        "cert": "ssl/cert.pem",
                        "key": "ssl/key.pem",
                    },
                },
            )
            self.sites_dir = Path(self.config.get("sites_dir", "sites"))
            self.backup_manager = None
            self.deployments = {}

        self.deploy_dir = Path(self.config.get("deploy_dir", "deployments"))
        self.deploy_dir.mkdir(exist_ok=True)

    def deploy_site(self, site_name: str) -> Dict[str, Any]:
        """Deploy a site"""
        try:
            site_dir = self.sites_dir / site_name
            if not site_dir.exists():
                return self._error_response("Site not found")

            # Create deployment directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            deploy_path = self.deploy_dir / f"{site_name}_{timestamp}"
            deploy_path.mkdir(parents=True, exist_ok=True)

            # Copy site files
            shutil.copytree(site_dir, deploy_path, dirs_exist_ok=True)

            # Create deployment metadata
            metadata = {
                "site": site_name,
                "timestamp": timestamp,
                "source": str(site_dir),
                "destination": str(deploy_path),
            }

            metadata_path = deploy_path / "deployment.json"
            with open(metadata_path, "w") as f:
                json.dump(metadata, f, indent=2)

            # Update nginx configuration if needed
            if self.config.get("nginx_enabled", False):
                self._update_nginx_config(site_name, deploy_path)

            logging.info(f"Successfully deployed site {site_name}")
            return {
                "status": "success",
                "message": f"Site {site_name} deployed successfully",
                "deploy_path": str(deploy_path),
            }

        except Exception as e:
            logging.error(f"Deployment failed: {str(e)}")
            return self._error_response(str(e))

    def deploy_site_validation_only(self, site_name: str) -> Dict[str, Any]:
        """Deploy a site with validation only"""
        try:
            site_dir = self.sites_dir / site_name
            if not site_dir.exists():
                return self._error_response("Site not found")

            # Just validate without deploying
            return {
                "status": "success",
                "message": f"Site {site_name} validation successful",
                "validation_only": True,
            }

        except Exception as e:
            logging.error(f"Validation failed: {str(e)}")
            return self._error_response(str(e))

    def list_deployments(self) -> List[Dict[str, Any]]:
        """List all deployments"""
        deployments = []
        try:
            for deploy_path in self.deploy_dir.iterdir():
                if deploy_path.is_dir():
                    metadata_path = deploy_path / "deployment.json"
                    if metadata_path.exists():
                        with open(metadata_path, "r") as f:
                            metadata = json.load(f)
                        deployments.append(metadata)
        except Exception as e:
            logging.error(f"Error listing deployments: {e}")

        return deployments

    def rollback_deployment(self, site_name: str, timestamp: str) -> Dict[str, Any]:
        """Rollback a deployment"""
        try:
            deploy_path = self.deploy_dir / f"{site_name}_{timestamp}"
            if not deploy_path.exists():
                return self._error_response("Deployment not found")

            # For now, just return success
            return {
                "status": "success",
                "message": f"Rollback of {site_name}_{timestamp} successful",
            }

        except Exception as e:
            logging.error(f"Rollback failed: {str(e)}")
            return self._error_response(str(e))

    def _update_nginx_config(self, site_name: str, deploy_path: Path) -> None:
        """Update nginx configuration"""
        try:
            nginx_config = f"""
server {{
    listen 80;
    server_name {self.config["domain"]};

    location / {{
        root {deploy_path};
        index index.html;
        try_files $uri $uri/ /index.html;
    }}
}}
"""

            # Use a local nginx config directory for development
            nginx_dir = Path("config/nginx/sites-available")
            nginx_dir.mkdir(parents=True, exist_ok=True)
            nginx_path = nginx_dir / site_name

            with open(nginx_path, "w") as f:
                f.write(nginx_config)

            # For development, just log the config location
            logging.info(f"Nginx config written to: {nginx_path}")

            # Only try to reload nginx if we're on a system that supports it
            if os.name != "nt":  # Not Windows
                try:
                    # Create symlink if needed
                    link_path = Path("/etc/nginx/sites-enabled") / site_name
                    if not link_path.exists():
                        os.symlink(nginx_path, link_path)

                    # Test and reload nginx
                    subprocess.run(["nginx", "-t"], check=True)
                    subprocess.run(["systemctl", "reload", "nginx"], check=True)
                except Exception as nginx_error:
                    logging.warning(
                        f"Nginx reload failed (this is normal in development): {nginx_error}"
                    )

        except Exception as e:
            logging.error(f"Failed to update nginx config: {str(e)}")
            raise

    def _get_current_deployment(self, site_name: str) -> Optional[Path]:
        """Get the current deployment path"""
        try:
            # Find the latest deployment
            deployments = list(self.deploy_dir.glob(f"{site_name}_*"))
            if deployments:
                return max(deployments, key=lambda x: x.stat().st_mtime)
            return None
        except Exception:
            return None

    def _error_response(self, message: str) -> Dict[str, Any]:
        """Create error response"""
        return {"status": "error", "message": message}

    # ========== AUTOMATED DEPLOYMENT METHODS ==========
    # These methods provide compatibility with automated deployment systems

    def deploy_site_automated(
        self, site_name: str, source_path: str, validate_only: bool = False
    ):
        """Deploy a site with automatic backup and validation (automated mode)"""
        from dataclasses import dataclass
        from datetime import datetime

        @dataclass
        class DeploymentStatus:
            site_name: str
            status: str
            start_time: datetime
            end_time: Optional[datetime]
            error_message: Optional[str]
            deployment_id: str
            backup_path: Optional[str]

        deployment_id = f"{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        deployment = DeploymentStatus(
            site_name=site_name,
            status="pending",
            start_time=datetime.now(),
            end_time=None,
            error_message=None,
            deployment_id=deployment_id,
            backup_path=None,
        )

        self.deployments[deployment_id] = deployment

        try:
            logging.info(f"Starting deployment {deployment_id} for site {site_name}")
            deployment.status = "deploying"

            # Create backup before deployment
            if not validate_only and self.backup_manager:
                backup_path = self.backup_manager.create_directory_backup(
                    site_name, str(self.sites_dir)
                )
                deployment.backup_path = backup_path
                logging.info(f"Backup created: {backup_path}")

            # Validate deployment
            if not self._validate_deployment_automated(source_path):
                raise ValueError("Deployment validation failed")

            if validate_only:
                deployment.status = "success"
                deployment.end_time = datetime.now()
                logging.info(f"Deployment validation successful for {site_name}")
                return deployment

            # Perform deployment
            target_path = self.sites_dir / site_name
            if target_path.exists():
                shutil.rmtree(target_path)

            shutil.copytree(source_path, target_path)

            # Post-deployment validation
            if not self._validate_deployed_site_automated(target_path):
                # Rollback on validation failure
                logging.warning(
                    f"Post-deployment validation failed, rolling back {site_name}"
                )
                self._rollback_deployment_automated(deployment)
                return deployment

            deployment.status = "success"
            deployment.end_time = datetime.now()
            logging.info(f"Deployment {deployment_id} completed successfully")

        except Exception as e:
            deployment.status = "failed"
            deployment.end_time = datetime.now()
            deployment.error_message = str(e)
            logging.error(f"Deployment {deployment_id} failed: {e}")

            # Rollback on failure
            if deployment.backup_path:
                self._rollback_deployment_automated(deployment)

        return deployment

    def rollback_deployment_automated(self, deployment_id: str) -> bool:
        """Rollback a specific deployment (automated mode)"""
        if deployment_id not in self.deployments:
            logging.error(f"Deployment {deployment_id} not found")
            return False

        deployment = self.deployments.get(deployment_id)
        return self._rollback_deployment_automated(deployment)

    def _validate_deployment_automated(self, source_path: str) -> bool:
        """Validate deployment source (automated mode)"""
        try:
            source = Path(source_path)
            if not source.exists():
                logging.error(f"Source path does not exist: {source_path}")
                return False

            # Basic validation - check for essential files
            if source.is_file():
                return source.suffix in [".zip", ".tar.gz", ".tgz"]

            # For directories, check for common web files
            if source.is_dir():
                web_files = list(source.glob("*.html")) + list(source.glob("*.htm"))
                return len(web_files) > 0

            return True
        except Exception as e:
            logging.error(f"Deployment validation error: {e}")
            return False

    def _validate_deployed_site_automated(self, target_path: Path) -> bool:
        """Validate deployed site (automated mode)"""
        try:
            if not target_path.exists():
                return False

            # Check for index file
            index_files = ["index.html", "index.htm", "default.html"]
            for index_file in index_files:
                if (target_path / index_file).exists():
                    return True

            # If no index file, check if there are any HTML files
            html_files = list(target_path.glob("*.html")) + list(
                target_path.glob("*.htm")
            )
            return len(html_files) > 0

        except Exception as e:
            logging.error(f"Site validation error: {e}")
            return False

    def _rollback_deployment_automated(self, deployment) -> bool:
        """Rollback deployment using backup (automated mode)"""
        if not deployment.backup_path:
            logging.error(f"No backup available for rollback of {deployment.site_name}")
            return False

        try:
            if self.backup_manager:
                success = self.backup_manager.restore_directory_backup(
                    deployment.backup_path, deployment.site_name, str(self.sites_dir)
                )

                if success:
                    deployment.status = "rolled_back"
                    deployment.end_time = datetime.now()
                    logging.info(
                        f"Deployment {deployment.deployment_id} rolled back successfully"
                    )
                    return True
                else:
                    logging.error(
                        f"Failed to rollback deployment {deployment.deployment_id}"
                    )
                    return False
            else:
                logging.error("No backup manager available for rollback")
                return False

        except Exception as e:
            logging.error(f"Rollback error: {e}")
            return False


# Example usage
if __name__ == "__main__":
    manager = DeploymentManager()
    try:
        # Deploy a site
        result = manager.deploy_site("my-site")
        print(f"Deployment result: {result}")

    except Exception as e:
        print(f"Error: {str(e)}")
