"""
Progress Tracker

Tracks and reports progress of complex tasks, including milestones,
timeline management, and progress visualization.
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from complex_tasks.models import ComplexTask, ProgressReport
from utils.logger import get_logger

sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

logger = get_logger(__name__)


class ProgressTracker:
    """
    Progress tracking system for complex tasks.

    Provides comprehensive progress tracking including:
    - Milestone tracking
    - Timeline management
    - Progress reporting
    - Risk assessment
    - Resource tracking
    - Progress visualization
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the progress tracker"""
        self.config = config
        self.tracking_interval = config.get("tracking_interval", 60)  # seconds
        self.history_size = config.get("history_size", 1000)

        # Progress tracking state
        self.tasks_progress: Dict[str, List[ProgressReport]] = {}
        self.milestones: Dict[str, List[Dict[str, Any]]] = {}
        self.risks: Dict[str, List[Dict[str, Any]]] = {}
        self.resources: Dict[str, Dict[str, Any]] = {}

        # Tracking state
        self.is_tracking = False
        self.tracking_task: Optional[asyncio.Task] = None

        logger.info("Progress Tracker initialized")

    async def start_tracking(self) -> None:
        """Start progress tracking"""
        if self.is_tracking:
            logger.warning("Progress tracking already started")
            return

        self.is_tracking = True
        self.tracking_task = asyncio.create_task(self._tracking_loop())
        logger.info("Progress tracking started")

    async def stop_tracking(self) -> None:
        """Stop progress tracking"""
        if not self.is_tracking:
            logger.warning("Progress tracking not started")
            return

        self.is_tracking = False
        if self.tracking_task:
            self.tracking_task.cancel()
            try:
                await self.tracking_task
            except asyncio.CancelledError:
                pass

        logger.info("Progress tracking stopped")

    async def _tracking_loop(self) -> None:
        """Main tracking loop"""
        while self.is_tracking:
            try:
                # Update progress for all tracked tasks
                await self._update_all_progress()

                # Check for milestone completions
                await self._check_milestones()

                # Assess risks
                await self._assess_risks()

                # Update resource tracking
                await self._update_resource_tracking()

                # Wait for next tracking interval
                await asyncio.sleep(self.tracking_interval)

            except Exception as e:
                logger.error(f"Error in tracking loop: {e}")
                await asyncio.sleep(self.tracking_interval)

    async def track_progress(self, progress_report: ProgressReport) -> None:
        """Track progress for a specific task"""
        task_id = progress_report.task_id

        if task_id not in self.tasks_progress:
            self.tasks_progress[task_id] = []

        self.tasks_progress[task_id].append(progress_report)

        # Limit history size
        if len(self.tasks_progress[task_id]) > self.history_size:
            self.tasks_progress[task_id].pop(0)

        logger.info(
            f"Progress tracked for task {task_id}: {progress_report.progress_percentage}%"
        )

    async def _update_all_progress(self) -> None:
        """Update progress for all tracked tasks"""
        for task_id in self.tasks_progress.keys():
            # This would typically involve checking actual task status
            # For now, we'll just log that we're updating
            logger.debug(f"Updating progress for task {task_id}")

    async def _check_milestones(self) -> None:
        """Check for milestone completions"""
        for task_id, milestones in self.milestones.items():
            for milestone in milestones:
                if not milestone.get("completed") and milestone.get("due_date"):
                    if datetime.now() > milestone["due_date"]:
                        milestone["status"] = "overdue"
                        logger.warning(
                            f"Milestone overdue for task {task_id}: {milestone['name']}"
                        )
                    elif milestone.get("completion_criteria"):
                        # Check if milestone completion criteria are met
                        if await self._check_milestone_criteria(task_id, milestone):
                            milestone["completed"] = True
                            milestone["completed_date"] = datetime.now()
                            milestone["status"] = "completed"
                            logger.info(
                                f"Milestone completed for task {task_id}: {milestone['name']}"
                            )

    async def _check_milestone_criteria(
        self, task_id: str, milestone: Dict[str, Any]
    ) -> bool:
        """Check if milestone completion criteria are met"""
        criteria = milestone.get("completion_criteria", {})

        if task_id in self.tasks_progress and self.tasks_progress[task_id]:
            latest_progress = self.tasks_progress[task_id][-1]

            # Check progress percentage
            if "progress_percentage" in criteria:
                if (
                    latest_progress.progress_percentage
                    < criteria["progress_percentage"]
                ):
                    return False

            # Check specific milestones completed
            if "milestones_completed" in criteria:
                required_milestones = criteria["milestones_completed"]
                completed_milestones = latest_progress.milestones_completed
                if not all(
                    milestone in completed_milestones
                    for milestone in required_milestones
                ):
                    return False

        return True

    async def _assess_risks(self) -> None:
        """Assess risks for all tracked tasks"""
        for task_id in self.tasks_progress.keys():
            if task_id in self.risks:
                for risk in self.risks[task_id]:
                    if not risk.get("mitigated"):
                        # Check if risk has materialized
                        if await self._check_risk_materialization(task_id, risk):
                            risk["materialized"] = True
                            risk["materialized_date"] = datetime.now()
                            logger.warning(
                                f"Risk materialized for task {task_id}: {risk['description']}"
                            )

    async def _check_risk_materialization(
        self, task_id: str, risk: Dict[str, Any]
    ) -> bool:
        """Check if a risk has materialized"""
        indicators = risk.get("indicators", {})

        if task_id in self.tasks_progress and self.tasks_progress[task_id]:
            latest_progress = self.tasks_progress[task_id][-1]

            # Check progress indicators
            if "progress_stalled" in indicators:
                if len(self.tasks_progress[task_id]) >= 2:
                    prev_progress = self.tasks_progress[task_id][-2]
                    if (
                        latest_progress.progress_percentage
                        == prev_progress.progress_percentage
                    ):
                        return True

            # Check timeline indicators
            if "timeline_slippage" in indicators:
                if latest_progress.estimated_completion:
                    if datetime.now() > latest_progress.estimated_completion:
                        return True

            # Check issue indicators
            if "issues_increased" in indicators:
                if (
                    len(latest_progress.issues_encountered)
                    > indicators["issues_increased"]
                ):
                    return True

        return False

    async def _update_resource_tracking(self) -> None:
        """Update resource tracking for all tasks"""
        for task_id in self.resources.keys():
            # This would typically involve checking actual resource usage
            # For now, we'll just log that we're updating
            logger.debug(f"Updating resource tracking for task {task_id}")

    async def add_milestone(self, task_id: str, milestone: Dict[str, Any]) -> None:
        """Add a milestone for tracking"""
        if task_id not in self.milestones:
            self.milestones[task_id] = []

        milestone_data = {
            "id": milestone.get(
                "id", f"milestone_{len(self.milestones[task_id]) + 1 or 0}"
            ),
            "name": milestone["name"],
            "description": milestone.get("description", ""),
            "due_date": (
                datetime.fromisoformat(milestone["due_date"])
                if milestone.get("due_date")
                else None
            ),
            "completion_criteria": milestone.get("completion_criteria", {}),
            "status": "pending",
            "completed": False,
            "created_date": datetime.now(),
        }

        self.milestones[task_id].append(milestone_data)
        logger.info(f"Milestone added for task {task_id}: {milestone_data['name']}")

    async def add_risk(self, task_id: str, risk: Dict[str, Any]) -> None:
        """Add a risk for tracking"""
        if task_id not in self.risks:
            self.risks[task_id] = []

        risk_data = {
            "id": risk.get("id", f"risk_{len(self.risks[task_id]) + 1 or 0}"),
            "description": risk["description"],
            "severity": risk.get("severity", "medium"),
            "probability": risk.get("probability", 0.5),
            "impact": risk.get("impact", "medium"),
            "mitigation_strategy": risk.get("mitigation_strategy", ""),
            "indicators": risk.get("indicators", {}),
            "status": "active",
            "materialized": False,
            "mitigated": False,
            "created_date": datetime.now(),
        }

        self.risks[task_id].append(risk_data)
        logger.info(f"Risk added for task {task_id}: {risk_data['description']}")

    async def track_resources(self, task_id: str, resources: Dict[str, Any]) -> None:
        """Track resources for a task"""
        self.resources[task_id] = {
            "cpu_usage": resources.get("cpu_usage", 0.0),
            "memory_usage": resources.get("memory_usage", 0.0),
            "disk_usage": resources.get("disk_usage", 0.0),
            "network_usage": resources.get("network_usage", 0.0),
            "gpu_usage": resources.get("gpu_usage", 0.0),
            "last_updated": datetime.now(),
        }

        logger.debug(f"Resources tracked for task {task_id}")

    async def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """Get progress information for a specific task"""
        if task_id not in self.tasks_progress:
            return {"error": "Task not found"}

        progress_history = self.tasks_progress[task_id]
        if not progress_history:
            return {"error": "No progress data available"}

        latest_progress = progress_history[-1]

        # Calculate progress trends
        progress_trend = self._calculate_progress_trend(progress_history)

        # Get milestone information
        milestones = self.milestones.get(task_id, [])

        # Get risk information
        risks = self.risks.get(task_id, [])

        # Get resource information
        resources = self.resources.get(task_id, {})

        return {
            "task_id": task_id,
            "latest_progress": latest_progress.to_dict(),
            "progress_history": [
                p.to_dict() for p in progress_history[-10:]
            ],  # Last 10 entries
            "progress_trend": progress_trend,
            "milestones": milestones,
            "risks": risks,
            "resources": resources,
            "completion_estimate": self._estimate_completion(task_id, progress_history),
        }

    def _calculate_progress_trend(
        self, progress_history: List[ProgressReport]
    ) -> Dict[str, Any]:
        """Calculate progress trend"""
        if len(progress_history) < 2:
            return {"trend": "insufficient_data", "rate": 0.0}

        # Calculate progress rate
        recent_progress = progress_history[-5:]  # Last 5 entries
        if len(recent_progress) >= 2:
            time_diff = (
                recent_progress[-1].timestamp - recent_progress[0].timestamp
            ).total_seconds() / 3600  # hours
            progress_diff = (
                recent_progress[-1].progress_percentage
                - recent_progress[0].progress_percentage
            )

            if time_diff > 0:
                rate = progress_diff / time_diff  # % per hour
            else:
                rate = 0.0
        else:
            rate = 0.0

        # Determine trend
        if rate > 5.0:
            trend = "accelerating"
        elif rate > 1.0:
            trend = "steady"
        elif rate > 0.0:
            trend = "slow"
        else:
            trend = "stalled"

        return {"trend": trend, "rate_per_hour": rate, "rate_per_day": rate * 24}

    def _estimate_completion(
        self, task_id: str, progress_history: List[ProgressReport]
    ) -> Dict[str, Any]:
        """Estimate task completion"""
        if len(progress_history) < 2:
            return {"estimated_completion": None, "confidence": "low"}

        latest_progress = progress_history[-1]
        trend = self._calculate_progress_trend(progress_history)

        if trend["rate_per_day"] > 0:
            remaining_progress = 100.0 - latest_progress.progress_percentage
            days_to_completion = remaining_progress / trend["rate_per_day"]
            estimated_completion = datetime.now() + timedelta(days=days_to_completion)

            # Determine confidence based on data quality
            if len(progress_history) >= 10:
                confidence = "high"
            elif len(progress_history) >= 5:
                confidence = "medium"
            else:
                confidence = "low"
        else:
            estimated_completion = None
            confidence = "low"

        return {
            "estimated_completion": (
                estimated_completion.isoformat() if estimated_completion else None
            ),
            "confidence": confidence,
            "remaining_progress": 100.0 - latest_progress.progress_percentage,
            "rate_per_day": trend["rate_per_day"],
        }

    async def get_progress_summary(self) -> Dict[str, Any]:
        """Get summary of all tracked progress"""
        summary: Dict[str, Any] = {
            "total_tasks": len(self.tasks_progress),
            "active_tasks": 0,
            "completed_tasks": 0,
            "overdue_tasks": 0,
            "total_milestones": 0,
            "completed_milestones": 0,
            "overdue_milestones": 0,
            "total_risks": 0,
            "materialized_risks": 0,
            "mitigated_risks": 0,
            "tasks": [],
        }

        for task_id in self.tasks_progress.keys():
            task_info = await self.get_task_progress(task_id)
            if "error" not in task_info:
                latest_progress = task_info["latest_progress"]

                # Count task status
                if latest_progress["status"] == "completed":
                    summary["completed_tasks"] += 1
                elif latest_progress["status"] in ["in_progress", "review"]:
                    summary["active_tasks"] += 1

                # Count milestones
                milestones = task_info.get("milestones", [])
                summary["total_milestones"] += len(milestones)
                summary["completed_milestones"] += len(
                    [m for m in milestones if m.get("completed")]
                )
                summary["overdue_milestones"] += len(
                    [m for m in milestones if m.get("status") == "overdue"]
                )

                # Count risks
                risks = task_info.get("risks", [])
                summary["total_risks"] += len(risks)
                summary["materialized_risks"] += len(
                    [r for r in risks if r.get("materialized")]
                )
                summary["mitigated_risks"] += len(
                    [r for r in risks if r.get("mitigated")]
                )

                # Add task to summary
                summary["tasks"].append(
                    {
                        "task_id": task_id,
                        "status": latest_progress["status"],
                        "progress": latest_progress["progress_percentage"],
                        "estimated_completion": task_info.get(
                            "completion_estimate", {}
                        ).get("estimated_completion"),
                    }
                )

        return summary

    async def generate_progress_report(self, task_id: Optional[str] = None) -> str:
        """Generate progress report"""
        if task_id:
            # Generate report for specific task
            task_progress = await self.get_task_progress(task_id)
            if "error" in task_progress:
                return f"Error: {task_progress['error']}"

            return self._generate_task_progress_report(task_id, task_progress)
        else:
            # Generate summary report for all tasks
            summary = await self.get_progress_summary()
            return self._generate_summary_progress_report(summary)

    def _generate_task_progress_report(
        self, task_id: str, task_progress: Dict[str, Any]
    ) -> str:
        """Generate progress report for a specific task"""
        latest = task_progress["latest_progress"]
        trend = task_progress["progress_trend"]
        completion_estimate = task_progress["completion_estimate"]

        report = f"""
# Task Progress Report

## Task Information
- **Task ID**: {task_id}
- **Status**: {latest['status']}
- **Current Progress**: {latest['progress_percentage']:.1f}%
- **Current Phase**: {latest['current_phase']}

## Progress Trend
- **Trend**: {trend['trend']}
- **Rate**: {trend['rate_per_day']:.1f}% per day
- **Rate**: {trend['rate_per_hour']:.1f}% per hour

## Completion Estimate
- **Estimated Completion**: {completion_estimate['estimated_completion'] or 'Unknown'}
- **Confidence**: {completion_estimate['confidence']}
- **Remaining Progress**: {completion_estimate['remaining_progress']:.1f}%

## Recent Activity
- **Last Updated**: {latest['timestamp']}
- **Milestones Completed**: {len(latest['milestones_completed'])}
- **Issues Encountered**: {len(latest['issues_encountered'])}
- **Solutions Implemented**: {len(latest['solutions_implemented'])}

## Milestones
{chr(10).join(f"- {m['name']}: {m['status']}" for m in task_progress.get('milestones', []))}

## Risks
{chr(10).join(f"- {r['description']}: {r['status']}" for r in task_progress.get('risks', []))}

---
*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
        """

        return report

    def _generate_summary_progress_report(self, summary: Dict[str, Any]) -> str:
        """Generate summary progress report"""
        report = f"""
# Progress Summary Report

## Overview
- **Total Tasks**: {summary['total_tasks']}
- **Active Tasks**: {summary['active_tasks']}
- **Completed Tasks**: {summary['completed_tasks']}
- **Overdue Tasks**: {summary['overdue_tasks']}

## Milestones
- **Total Milestones**: {summary['total_milestones']}
- **Completed Milestones**: {summary['completed_milestones']}
- **Overdue Milestones**: {summary['overdue_milestones']}

## Risks
- **Total Risks**: {summary['total_risks']}
- **Materialized Risks**: {summary['materialized_risks']}
- **Mitigated Risks**: {summary['mitigated_risks']}

## Task Details
{chr(10).join(f"- {task['task_id']}: {task['status']} ({task['progress']:.1f}%)" for task in summary['tasks'])}

---
*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
        """

        return report

    async def export_progress_data(
        self, file_path: str, task_id: Optional[str] = None
    ) -> None:
        """Export progress data to file"""
        if task_id:
            # Export data for specific task
            task_progress = await self.get_task_progress(task_id)
            export_data = {
                "task_id": task_id,
                "export_timestamp": datetime.now().isoformat(),
                "progress_data": task_progress,
            }
        else:
            # Export data for all tasks
            summary = await self.get_progress_summary()
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "summary": summary,
                "all_tasks_progress": {
                    task_id: await self.get_task_progress(task_id)
                    for task_id in self.tasks_progress.keys()
                },
            }

        with open(file_path, "w") as f:
            json.dump(export_data, f, indent=2, default=str)

        logger.info(f"Progress data exported to {file_path}")

    async def cleanup_old_data(self, days_to_keep: int = 30) -> int:
        """Clean up old progress data"""
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)

        initial_count = sum(len(progress) for progress in self.tasks_progress.values())

        # Clean up old progress reports
        for task_id in self.tasks_progress:
            self.tasks_progress[task_id] = [
                progress
                for progress in self.tasks_progress[task_id]
                if progress.timestamp > cutoff_time
            ]

        # Clean up old milestones
        for task_id in self.milestones:
            self.milestones[task_id] = [
                milestone
                for milestone in self.milestones[task_id]
                if milestone.get("created_date", datetime.now()) > cutoff_time
            ]

        # Clean up old risks
        for task_id in self.risks:
            self.risks[task_id] = [
                risk
                for risk in self.risks[task_id]
                if risk.get("created_date", datetime.now()) > cutoff_time
            ]

        final_count = sum(len(progress) for progress in self.tasks_progress.values())
        cleaned_count = initial_count - final_count

        logger.info(f"Cleaned up {cleaned_count} old progress data entries")

        return cleaned_count
