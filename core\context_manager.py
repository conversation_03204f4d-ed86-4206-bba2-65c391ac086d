"""
Context Manager for AI Coding Agent - Implements context-aware suggestions
"""

import json
from datetime import datetime
from typing import Dict, List, Optional


class ContextManager:
    def __init__(self):
        self.context_history = []
        self.user_preferences = {}
        self.suggestion_weights = {"recent": 0.4, "frequency": 0.3, "relevance": 0.3}

    def track_context(self, context: Dict) -> None:
        """Track and store context information with timestamp"""
        context_entry = {"timestamp": datetime.now().isoformat(), "data": context}
        self.context_history.append(context_entry)

    def get_current_context(self) -> Dict:
        """Get the most recent context"""
        if not self.context_history:
            return {}
        return self.context_history[-1]["data"]

    def rank_suggestions(self, suggestions: List[Dict]) -> List[Dict]:
        """Rank suggestions based on multiple factors"""
        if not suggestions:
            return []

        # Apply ranking algorithm
        ranked = []
        for suggestion in suggestions:
            score = 0
            # Score based on recent usage
            score += self.suggestion_weights["recent"] * self._get_recency_score(
                suggestion
            )
            # Score based on frequency
            score += self.suggestion_weights["frequency"] * self._get_frequency_score(
                suggestion
            )
            # Score based on current context relevance
            score += self.suggestion_weights["relevance"] * self._get_relevance_score(
                suggestion
            )

            ranked.append({"suggestion": suggestion, "score": score})

        # Sort by score descending
        return sorted(ranked, key=lambda x: x["score"], reverse=True)

    def learn_preference(self, suggestion: Dict, accepted: bool) -> None:
        """Update user preferences based on suggestion acceptance"""
        suggestion_type = suggestion.get("type", "general")
        if suggestion_type not in self.user_preferences:
            self.user_preferences[suggestion_type] = {"accepted": 0, "rejected": 0}

        if accepted:
            self.user_preferences[suggestion_type]["accepted"] += 1
        else:
            self.user_preferences[suggestion_type]["rejected"] += 1

    def generate_preview(self, suggestion: Dict) -> Dict:
        """Generate a preview of what applying the suggestion would look like"""
        preview = {
            "description": suggestion.get("description", ""),
            "before": suggestion.get("before", ""),
            "after": suggestion.get("after", ""),
            "impact": suggestion.get("impact", {}),
            "confidence": suggestion.get("confidence", 0),
        }
        return preview

    def _get_recency_score(self, suggestion: Dict) -> float:
        """Calculate recency score (0-1) for a suggestion"""
        # Implementation depends on how recency is tracked
        return 0.5  # Placeholder

    def _get_frequency_score(self, suggestion: Dict) -> float:
        """Calculate frequency score (0-1) for a suggestion"""
        # Implementation depends on frequency tracking
        return 0.5  # Placeholder

    def _get_relevance_score(self, suggestion: Dict) -> float:
        """Calculate relevance score (0-1) based on current context"""
        # Implementation depends on context analysis
        return 0.5  # Placeholder

    def save_state(self, file_path: str) -> None:
        """Save manager state to file"""
        state = {
            "context_history": self.context_history,
            "user_preferences": self.user_preferences,
        }
        with open(file_path, "w") as f:
            json.dump(state, f)

    def load_state(self, file_path: str) -> None:
        """Load manager state from file"""
        try:
            with open(file_path, "r") as f:
                state = json.load(f)
                self.context_history = state.get("context_history", [])
                self.user_preferences = state.get("user_preferences", {})
        except FileNotFoundError:
            pass
