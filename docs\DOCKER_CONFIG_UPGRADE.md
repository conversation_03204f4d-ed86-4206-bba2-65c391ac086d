# 🐳 Docker Configuration Upgrade Guide

**AI Coding Agent Project - Docker Optimization Roadmap**

This guide provides a comprehensive checklist for upgrading the Docker configuration in the AI Coding Agent project, addressing the file copying strategy and other optimizations while maintaining strict cursor rule compliance.

## 📋 Prerequisites

- [ ] Virtual environment activated: `.\.venv\Scripts\Activate.ps1` (Windows) or `source .venv/bin/activate` (Unix)
- [ ] Cursor rules monitoring system verified: `python scripts/cursor_rules_monitor.py --status`
- [ ] Docker daemon running and accessible
- [ ] `ai-coding-network` Docker network exists: `docker network create ai-coding-network`
- [ ] All tests passing 100%: `python -m pytest`
- [ ] No open TODOs in the project

## 🚨 Immediate Fixes (Priority 1)

### Port Consistency & Health Checks
- [ ] **Fix port mapping inconsistency in `core/site_container_manager.py`**
  - **Issue**: Dynamic sites expose port 8000 but compose maps to port 80
  - **Files**: `core/site_container_manager.py` lines 578, 630, 638
  - **Action**: Standardize container port to 80 for all sites
  - **Code Change**:
    ```python
    # In _create_site_dockerfile method, change:
    EXPOSE 8000  # OLD
    EXPOSE 80    # NEW

    # In healthcheck, change:
    CMD curl -f http://localhost:8000/health  # OLD
    CMD curl -f http://localhost:80/health    # NEW
    ```
  - **Verification**: `docker-compose logs site-{name}` shows no port binding errors

- [ ] **Fix health endpoint for Python http.server**
  - **Issue**: `/health` endpoint doesn't exist for `python -m http.server`
  - **Action**: Change healthcheck to root path or implement health endpoint
  - **Code Change**:
    ```python
    # Option A: Use root path
    CMD curl -f http://localhost:80/ || exit 1

    # Option B: Implement simple health server
    CMD ["python", "-c", "import http.server; import socketserver; ...]
    ```
  - **Verification**: `curl http://localhost:{port}/health` returns 200

### Non-Root User Implementation
- [ ] **Add non-root user to dynamic site Dockerfiles**
  - **Files**: `core/site_container_manager.py` lines 558-586
  - **Action**: Mirror nginx template security pattern
  - **Code Change**:
    ```dockerfile
    # Add after system dependencies install:
    RUN groupadd --gid 1000 appuser && \
        useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

    # Before CMD, add:
    RUN chown -R appuser:appuser /app
    USER appuser
    ```
  - **Verification**: `docker exec site-{name} whoami` returns `appuser`

## 🔒 Security Enhancements (Priority 2)

### Secrets Externalization
- [ ] **Replace inline environment with env_file in compose generation**
  - **Files**: `core/site_container_manager.py` lines 620-647
  - **Action**: Use env_file instead of inline environment arrays
  - **Code Change**:
    ```python
    # Replace environment array with:
    "env_file": ["../.env", f"../.env.{site_name}"],
    # Remove: "environment": ["SITE_NAME=" + site_name, "PORT=80"],
    ```
  - **Verification**: No secrets visible in `docker-compose config`

- [ ] **Create site-specific environment files**
  - **Action**: Generate `.env.{site_name}` files for each site
  - **Location**: Project root directory
  - **Content Template**:
    ```bash
    SITE_NAME={site_name}
    CONTAINER_PORT=80
    ENVIRONMENT=production
    ```
  - **Verification**: Files exist and are loaded by containers

### Base Image Pinning
- [ ] **Pin all base images to specific versions**
  - **Files**: All Dockerfile templates and generated Dockerfiles
  - **Action**: Replace `latest` tags with pinned versions
  - **Examples**:
    ```dockerfile
    FROM python:3.11-slim@sha256:...  # Instead of python:3.11-slim
    FROM nginx:1.25-alpine@sha256:... # Instead of nginx:alpine
    FROM node:18-alpine@sha256:...    # Instead of node:18-alpine
    ```
  - **Verification**: `docker images` shows no `:latest` tags for project images

## ⚡ Performance Optimizations (Priority 3)

### Intelligent File Copying Strategy
- [ ] **Enhance framework detection in SiteContainerManager**
  - **Files**: `core/site_container_manager.py`
  - **Action**: Add `_detect_site_characteristics` method
  - **Implementation**:
    ```python
    def _detect_site_characteristics(self, site_path: Path) -> Dict[str, Any]:
        """Detect site type and optimize build strategy"""
        characteristics = {
            "framework": "static",
            "needs_build": False,
            "build_output_dir": None,
            "runtime_files": [],
            "dev_files": []
        }

        if (site_path / "package.json").exists():
            # Detect React/Next.js/etc.
            pass

        return characteristics
    ```
  - **Verification**: Method correctly identifies site frameworks

- [ ] **Implement optimized multi-stage Dockerfile generation**
  - **Action**: Replace simple `COPY . /usr/share/nginx/html/` with intelligent copying
  - **Benefits**: Smaller images, better caching, security isolation
  - **Implementation**: Copy only necessary runtime files, exclude dev dependencies
  - **Verification**: Image sizes reduced by 30-50%

### Per-Site .dockerignore Generation
- [ ] **Auto-generate .dockerignore for each site**
  - **Files**: `core/site_container_manager.py`
  - **Action**: Create `_create_site_dockerignore` method
  - **Content**: Framework-specific ignore patterns
  - **Verification**: Build contexts are significantly smaller

### Resource Limits & Logging
- [ ] **Add resource limits to all site containers**
  - **Files**: `core/site_container_manager.py` compose generation
  - **Action**: Add deploy.resources section
  - **Code Change**:
    ```yaml
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    ```
  - **Verification**: `docker stats` shows resource constraints applied

- [ ] **Implement log rotation for all containers**
  - **Action**: Add logging configuration to compose
  - **Code Change**:
    ```yaml
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    ```
  - **Verification**: Log files don't exceed size limits

## 🤖 AI Integration Enhancements (Priority 4)

### AI-Enhanced Container Analysis
- [ ] **Create AIEnhancedContainerManager class**
  - **Files**: New file `core/ai_container_manager.py`
  - **Action**: Extend SiteContainerManager with AI capabilities
  - **Features**: Site structure analysis, optimization suggestions
  - **Integration**: Use local Ollama models for analysis
  - **Implementation Example**:
    ```python
    class AIEnhancedContainerManager(SiteContainerManager):
        async def analyze_and_optimize_site(self, site_name: str) -> Dict[str, Any]:
            """Use AI to analyze site and suggest optimizations"""
            site_path = self.sites_dir / site_name
            analysis = await self._ai_analyze_site_structure(site_path)
            return {
                "dockerfile_strategy": analysis["recommended_strategy"],
                "security_hardening": analysis["security_recommendations"],
                "performance_optimizations": analysis["performance_tips"]
            }
    ```
  - **Verification**: AI provides meaningful optimization suggestions

### CLI Integration
- [ ] **Add AI optimization commands to CLI**
  - **Files**: `cli/external_hosting_commands.py`
  - **Commands**: `create-optimized-container`, `analyze-site-performance`
  - **Implementation**:
    ```python
    @click.command()
    @click.argument('site_name')
    @click.option('--optimize', is_flag=True, help='Use AI optimization')
    async def create_optimized_container(site_name: str, optimize: bool):
        """Create optimized container with AI enhancements"""
        container_manager = AIEnhancedContainerManager()
        if optimize:
            analysis = await container_manager.analyze_and_optimize_site(site_name)
            click.echo(f"AI Analysis: {analysis}")
        result = await container_manager.create_site_container(site_name, {
            "optimization_enabled": optimize
        })
    ```
  - **Verification**: Commands work and provide useful output

### Intelligent Dockerfile Generation
- [ ] **Implement framework-specific optimizations**
  - **Action**: Use AI to detect optimal build strategies
  - **Features**: Dependency analysis, build caching, security scanning
  - **Integration**: Leverage existing Ollama models for code analysis
  - **Verification**: Generated Dockerfiles are more efficient and secure

## 📊 Monitoring & Observability (Priority 5)

### Container Performance Monitoring
- [ ] **Implement container metrics collection**
  - **Files**: `core/site_container_manager.py`
  - **Action**: Add `monitor_container_performance` method
  - **Metrics**: CPU, memory, network, disk usage
  - **Verification**: Metrics are collected and actionable

### Health Check Standardization
- [ ] **Ensure all containers have proper health endpoints**
  - **Action**: Implement `/health` endpoints for all site types
  - **Static sites**: Add nginx location for health
  - **Dynamic sites**: Implement health route
  - **Verification**: All health checks pass consistently

## 🧪 Testing & Validation

### Comprehensive Testing
- [ ] **Create Docker configuration tests**
  - **Files**: `tests/test_docker_config.py`
  - **Tests**: Port consistency, security, resource limits
  - **Verification**: All tests pass 100%

- [ ] **Validate cursor rule compliance**
  - **Action**: Run cursor rules monitor after each change
  - **Command**: `python scripts/cursor_rules_monitor.py --status`
  - **Requirement**: Compliance score > 90%

### Integration Testing
- [ ] **Test complete site creation workflow**
  - **Action**: Create test sites of different types
  - **Verification**: All sites build, start, and serve correctly
  - **Performance**: Build times improved, image sizes reduced

## 📝 Documentation Updates

- [ ] **Update README_Docker.md with new patterns**
- [ ] **Document new CLI commands and options**
- [ ] **Create troubleshooting guide for common issues**
- [ ] **Update API documentation for new container features**

## 🔧 Detailed Verification Commands

### Port Consistency Verification
```bash
# Verify container port mapping
docker ps --format "table {{.Names}}\t{{.Ports}}" | grep site-

# Test health endpoints
for site in $(docker ps --format "{{.Names}}" | grep site-); do
  port=$(docker port $site 80/tcp | cut -d: -f2)
  echo "Testing $site on port $port"
  curl -f http://localhost:$port/health || curl -f http://localhost:$port/
done
```

### Security Verification
```bash
# Verify non-root users
docker exec site-test-site whoami  # Should return 'appuser'

# Check for secrets in compose files
grep -r "password\|secret\|key" containers/docker-compose.*.yml || echo "No secrets found"

# Verify base image versions
docker images | grep -E "(latest|alpine)$" | wc -l  # Should be 0
```

### Performance Verification
```bash
# Check image sizes (should be smaller after optimization)
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep ai-coding-site

# Verify resource limits
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Check build context sizes
find sites/ -name .dockerignore -exec wc -l {} \; | awk '{sum+=$1} END {print "Total ignore patterns:", sum}'
```

## 🚨 Troubleshooting Guide

### Common Issues and Solutions

**Issue**: Port binding conflicts
```bash
# Solution: Check port allocation
python -c "
from core.site_container_manager import PortManager
pm = PortManager()
print('Allocated ports:', pm.allocated_ports)
print('Port assignments:', pm.port_assignments)
"
```

**Issue**: Health checks failing
```bash
# Debug health check
docker exec site-{name} curl -v http://localhost:80/health
# If 404, check if health endpoint exists or use root path
```

**Issue**: Build context too large
```bash
# Check build context size
docker build --no-cache --progress=plain sites/{site_name} 2>&1 | grep "transferring context"
# Solution: Ensure .dockerignore is generated and comprehensive
```

**Issue**: Container running as root
```bash
# Verify user
docker exec site-{name} id
# Should show uid=1000(appuser) gid=1000(appuser)
```

## 📋 Implementation Code Examples

### Enhanced SiteContainerManager Methods

```python
# Add to core/site_container_manager.py

async def _create_site_dockerignore(self, site_path: Path, characteristics: Dict) -> None:
    """Generate optimized .dockerignore for the site"""
    ignore_patterns = [
        "# AI Coding Agent - Auto-generated .dockerignore",
        "node_modules/", "__pycache__/", ".git/", ".vscode/",
        "*.log", "*.tmp", ".env*", "!.env.example",
        "tests/", "__tests__/", "*.test.*", "coverage/",
        "docs/", "README.md", "*.md"
    ]

    # Add framework-specific ignores
    if characteristics["framework"] == "nextjs":
        ignore_patterns.extend([".next/", "out/", "coverage/"])
    elif characteristics["framework"] == "react":
        ignore_patterns.extend(["build/", "dist/", "coverage/"])
    elif characteristics["framework"] == "python":
        ignore_patterns.extend(["__pycache__/", "*.pyc", ".pytest_cache/"])

    dockerignore_path = site_path / ".dockerignore"
    with open(dockerignore_path, "w") as f:
        f.write("\n".join(ignore_patterns))

    logger.info(f"Generated .dockerignore for {site_path.name}")

def _generate_security_hardened_dockerfile(self, site_name: str, framework: str) -> str:
    """Generate security-hardened Dockerfile with non-root user"""
    if framework == "static":
        return f"""
# Security-hardened static site Dockerfile
FROM nginx:1.25-alpine@sha256:2d194184b067db3598771b4cf326cfe6ad5051937ba2cc83222d94d4cd9c81bd

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

# Copy site files with proper ownership
COPY --chown=appuser:appuser {site_name}/ /usr/share/nginx/html/

# Configure nginx for non-root
RUN chown -R appuser:appuser /var/cache/nginx /var/log/nginx /etc/nginx/conf.d \\
    && touch /var/run/nginx.pid \\
    && chown appuser:appuser /var/run/nginx.pid

# Switch to non-root user
USER appuser

EXPOSE 80
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
"""
    else:
        return f"""
# Security-hardened dynamic site Dockerfile
FROM python:3.11-slim@sha256:f2ee145f3bc4e061f8dfe7e6ebd427a410121495a0bd26e7622136db060b7e8e

# Create non-root user early
RUN groupadd --gid 1000 appuser && \\
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

# Install system dependencies
RUN apt-get update && apt-get upgrade -y && \\
    apt-get install -y --no-install-recommends curl && \\
    apt-get clean && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy and install dependencies
COPY --chown=appuser:appuser {site_name}/requirements.txt* ./
RUN if [ -f requirements.txt ]; then pip install --no-cache-dir -r requirements.txt; fi

# Copy application code
COPY --chown=appuser:appuser {site_name}/ .

# Switch to non-root user
USER appuser

EXPOSE 80
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

CMD ["python", "-m", "http.server", "80", "--bind", "0.0.0.0"]
"""
```

### Environment File Templates

Create these files in project root:

**.env.template**:
```bash
# Site-specific environment template
SITE_NAME=
CONTAINER_PORT=80
ENVIRONMENT=production
LOG_LEVEL=info
HEALTH_CHECK_INTERVAL=30s
```

**.env.{site_name}** (auto-generated):
```bash
SITE_NAME={site_name}
CONTAINER_PORT=80
ENVIRONMENT=production
BUILD_TIMESTAMP={timestamp}
```

## ✅ Final Verification Checklist

- [ ] All containers run as non-root users
- [ ] No port mapping inconsistencies
- [ ] All health checks pass
- [ ] Resource limits applied and working
- [ ] Log rotation configured
- [ ] No secrets in compose files
- [ ] All base images pinned
- [ ] .dockerignore files generated
- [ ] AI integration functional
- [ ] 100% test success maintained
- [ ] Cursor rules compliance > 90%
- [ ] Performance improvements measurable

## 🚀 Implementation Timeline

**Week 1**: Complete Priority 1 (Immediate Fixes)
**Week 2**: Complete Priority 2 (Security Enhancements)
**Week 3**: Complete Priority 3 (Performance Optimizations)
**Week 4**: Complete Priority 4-5 (AI Integration & Monitoring)

---

**Note**: Always activate virtual environment and verify cursor rules monitoring before making changes. Commit frequently with conventional commit messages following the project's standards.
