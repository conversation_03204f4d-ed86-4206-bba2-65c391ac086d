networks:
  ai-coding-network:
    driver: bridge
secrets:
  db_password:
    file: ./secrets/db_password.txt
  grafana_password:
    file: ./secrets/grafana_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  redis_password:
    file: ./secrets/redis_password.txt
  secret_key:
    file: ./secrets/secret_key.txt
services:
  api:
    build:
      context: ..
      dockerfile: containers/Dockerfile.api
    container_name: ai-coding-api
    depends_on:
      db:
        condition: service_healthy
      ollama:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file: ../.env
    environment:
      API_PORT: 8000
      CACHE_TTL: 3600
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      LOG_LEVEL: INFO
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      REDIS_URL: redis://redis:6379
      SECRET_KEY_FILE: /run/secrets/secret_key
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_URL: ${SUPABASE_URL}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    # Security options
    security_opt:
    - no-new-privileges:true
    read_only: false
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    secrets:
    - secret_key
    - jwt_secret
    volumes:
    - app_data:/app/data
    - app_logs:/app/logs
    - app_config:/app/config:ro
    - app_backups:/app/backups
    - app_sites:/app/sites
    - app_uploads:/app/uploads
    - app_ssl:/app/ssl
    - app_database:/app/database
    - app_test_reports:/app/test_reports
    - containers\extracted\docker-compose_api_ports.json:/app/config/ports.json:ro
  backup:
    build:
      context: ..
      dockerfile: containers/Dockerfile.backup
    container_name: ai-coding-backup
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8086/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
    # Security options
    security_opt:
    - no-new-privileges:true
    read_only: false
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    volumes:
    - app_backups:/app/backups
  container_agent:
    build:
      context: ..
      dockerfile: containers/Dockerfile.container
    container_name: ai-coding-container-agent
    depends_on:
      api:
        condition: service_healthy
      nginx:
        condition: service_healthy
    environment:
      CONFIG_PATH: /app/config/container_agent_config.json
      DOCKER_HOST: unix:///var/run/docker.sock
      NETWORK_NAME: ai-coding-network
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8093/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
    volumes:
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - app_config:/app/config:ro
  cursor_monitor:
    build:
      context: ..
      dockerfile: containers/Dockerfile.cursor-monitor
    cap_add:
    - CHOWN
    - SETGID
    - SETUID
    cap_drop:
    - ALL
    container_name: ai-coding-cursor-monitor
    cpus: 0.5
    depends_on:
      api:
        condition: service_healthy
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file: ../.env
    environment:
      CACHE_TTL: 3600
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
      HEALTH_CHECK_PORT: 8094
      LOG_LEVEL: INFO
      MONITOR_INTERVAL: 30
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONDONTWRITEBYTECODE: 1
      PYTHONHASHSEED: 1
      PYTHONOPTIMIZE: 1
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      REDIS_URL: redis://redis:6379
      STRICT_MODE: true
      TZ: UTC
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - python
      - scripts/simple_health_check.py
      timeout: 15s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
        tag: '{{.Name}}'
    mem_limit: 512m
    mem_reservation: 256m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    security_opt:
    - no-new-privileges:true
    ulimits:
      nofile:
        hard: 65536
        soft: 65536
    volumes:
    - app_logs:/app/logs
    - app_config:/app/config:ro
    - app_data:/app/data
    - containers/extracted/docker-compose_cursor_monitor_ports.json:/app/config/ports.json:ro
  dashboard:
    build:
      context: ..
      dockerfile: containers/Dockerfile.dashboard
    container_name: ai-coding-dashboard
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8080/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    volumes:
    - app_dashboard:/app/dashboard
    - app_config:/app/config:ro
    - containers\extracted\docker-compose_dashboard_ports.json:/app/config/ports.json:ro
  db:
    container_name: ai-coding-db
    environment:
      POSTGRES_DB: ${DB_NAME:-ai_coding_agent}
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_USER: ${DB_USER:-ai_coding_user}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD-SHELL
      - pg_isready -U ${DB_USER:-ai_coding_user} -d ${DB_NAME:-ai_coding_agent}
      timeout: 10s
    image: postgres:15-alpine
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    secrets:
    - db_password
    volumes:
    - pgdata:/var/lib/postgresql/data
    - containers\extracted\docker-compose_db_ports.json:/app/config/ports.json:ro
  disaster-recovery:
    build:
      context: ..
      dockerfile: containers/Dockerfile.disaster-recovery
    container_name: ai-coding-disaster-recovery
    depends_on:
      api:
        condition: service_healthy
      db:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8089/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
  error_watcher:
    build:
      context: ..
      dockerfile: containers/Dockerfile.errorwatcher
    container_name: ai-coding-error-watcher
    depends_on:
      api:
        condition: service_healthy
      file_watcher:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8091/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    volumes:
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - app_config:/app/config:ro
    - containers\extracted\docker-compose_error_watcher_ports.json:/app/config/ports.json:ro
  file_watcher:
    build:
      context: ..
      dockerfile: containers/Dockerfile.filewatcher
    container_name: ai-coding-file-watcher
    depends_on:
      api:
        condition: service_healthy
      nginx:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8090/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    volumes:
    - app_sites:/app/sites
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - containers\extracted\docker-compose_file_watcher_ports.json:/app/config/ports.json:ro
  fine-tuner:
    build:
      context: ..
      dockerfile: containers/Dockerfile.fine-tuner
    container_name: ai-coding-fine-tuner
    env_file: ../.env
    environment:
      CACHE_TTL: 3600
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
      FINE_TUNER_HOST: 0.0.0.0
      FINE_TUNER_PORT: ${FINE_TUNER_PORT:-8002}
      LOG_LEVEL: INFO
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      REDIS_URL: redis://redis:6379
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - --fail
      - http://localhost:${FINE_TUNER_PORT:-8002}/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    volumes:
    - app_fine_tuning:/app/fine_tuning:ro
    - app_logs:/app/logs
    - app_fine_tuning_data:/app/data
    - containers\extracted\docker-compose_fine-tuner_ports.json:/app/config/ports.json:ro
  fine-tuning:
    build:
      context: ..
      dockerfile: containers/Dockerfile.fine-tuning
    container_name: ai-coding-fine-tuning
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    restart: 'no'
  frontend:
    build:
      context: ..
      dockerfile: containers/Dockerfile.frontend
    container_name: ai-coding-frontend
    depends_on:
      api:
        condition: service_healthy
    env_file: ../.env
    environment:
      NEXT_PUBLIC_API_URL: http://api:8000
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:3000/api/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    volumes:
    - containers\extracted\docker-compose_frontend_ports.json:/app/config/ports.json:ro
  grafana:
    container_name: ai-coding-grafana
    depends_on:
      prometheus:
        condition: service_healthy
    environment:
      GF_SECURITY_ADMIN_PASSWORD_FILE: /run/secrets/grafana_password
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:3000/api/health
      timeout: 10s
    image: grafana/grafana:latest
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    secrets:
    - grafana_password
    volumes:
    - grafana_data:/var/lib/grafana
    - containers\extracted\docker-compose_grafana_ports.json:/app/config/ports.json:ro
  learning:
    build:
      context: ..
      dockerfile: containers/Dockerfile.learning
    container_name: ai-coding-learning
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8084/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    volumes:
    - app_learning:/app/learning
    - app_config:/app/config:ro
    - containers\extracted\docker-compose_learning_ports.json:/app/config/ports.json:ro
  migration-runner:
    build:
      context: ..
      dockerfile: containers/Dockerfile.migration-runner
    container_name: ai-coding-migration-runner
    depends_on:
      db:
        condition: service_healthy
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: 'no'
  model-optimizer:
    build:
      context: ..
      dockerfile: containers/Dockerfile.model-optimizer
    container_name: ai-coding-model-optimizer
    depends_on:
      ollama:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8092/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
  monitoring:
    build:
      context: ..
      dockerfile: containers/Dockerfile.monitoring
    container_name: ai-coding-monitoring
    depends_on:
      api:
        condition: service_healthy
    environment:
      MONITORING_ENABLED: 'true'
      MONITORING_PID_FILE: /app/logs/monitor.pid
      PYTHONPATH: /app
      PYTHONUNBUFFERED: '1'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - python
      - -c
      - import os, psutil; pid_file='/app/logs/monitor.pid'; exit(0 if os.path.exists(pid_file)
        and psutil.pid_exists(int(open(pid_file).read().strip())) else 1)
      timeout: 30s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ../logs:/app/logs
    - ../data:/app/data
    - containers\extracted\docker-compose_monitoring_ports.json:/app/config/ports.json:ro
  nginx:
    container_name: ai-coding-nginx
    depends_on:
      api:
        condition: service_healthy
      frontend:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost/health
      timeout: 10s
    image: nginx:alpine
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ../nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    - app_ssl:/etc/nginx/ssl:ro
    - containers\extracted\docker-compose_nginx_ports.json:/app/config/ports.json:ro
  ollama:
    build:
      context: ..
      dockerfile: ollama/Dockerfile
    container_name: ai-coding-ollama
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - ollama
      - ps
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ollama_data:/root/.ollama
    - containers\extracted\docker-compose_ollama_ports.json:/app/config/ports.json:ro
  prometheus:
    container_name: ai-coding-prometheus
    depends_on:
      monitoring:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:9090/-/healthy
      timeout: 10s
    image: prom/prometheus:latest
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ../monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    - prometheus_data:/prometheus
    - containers\extracted\docker-compose_prometheus_ports.json:/app/config/ports.json:ro
  redis:
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password} --appendonly
      yes
    container_name: ai-coding-redis
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - redis-cli
      - -a
      - ${REDIS_PASSWORD:-redis_password}
      - ping
      timeout: 10s
    image: redis:7-alpine
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - redis_data:/data
    - containers\extracted\docker-compose_redis_ports.json:/app/config/ports.json:ro
  scheduler:
    build:
      context: ..
      dockerfile: containers/Dockerfile.scheduler
    container_name: ai-coding-scheduler
    depends_on:
      api:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8085/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
  security:
    build:
      context: ..
      dockerfile: containers/Dockerfile.security
    container_name: ai-coding-security
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8087/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
  threat-detection:
    build:
      context: ..
      dockerfile: containers/Dockerfile.threat-detection
    container_name: ai-coding-threat-detection
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8088/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
  validation:
    build:
      context: ..
      dockerfile: containers/Dockerfile.validation
    container_name: ai-coding-validation
    env_file: ../.env
    environment:
      CACHE_TTL: 3600
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
      LOG_LEVEL: INFO
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      REDIS_URL: redis://redis:6379
      VALIDATION_HOST: 0.0.0.0
      VALIDATION_PORT: ${VALIDATION_PORT:-8004}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - curl
      - --fail
      - http://localhost:${VALIDATION_PORT:-8004}/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ../validation:/app/validation:ro
    - ../core:/app/core:ro
    - app_logs:/app/logs
    - app_validation_data:/app/data/validation
    - app_config:/app/config:ro
    - containers\extracted\docker-compose_validation_ports.json:/app/config/ports.json:ro
version: '3.8'
volumes:
  app_backups:
    driver: local
  app_config:
    driver: local
  app_dashboard:
    driver: local
  app_data:
    driver: local
  app_database:
    driver: local
  app_fine_tuning:
    driver: local
  app_fine_tuning_data:
    driver: local
  app_learning:
    driver: local
  app_logs:
    driver: local
  app_sites:
    driver: local
  app_ssl:
    driver: local
  app_test_reports:
    driver: local
  app_uploads:
    driver: local
  app_validation_data:
    driver: local
  grafana_data:
    driver: local
  ollama_data:
    driver: local
  pgdata:
    driver: local
  prometheus_data:
    driver: local
  redis_data:
    driver: local
x-common-env:
  CACHE_TTL: 3600
  DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
  LOG_LEVEL: INFO
  OLLAMA_URL: http://host.docker.internal:11434
  PYTHONPATH: /app
  PYTHONUNBUFFERED: 1
  REDIS_URL: redis://redis:6379
x-common-healthcheck:
  interval: 30s
  retries: 3
  start_period: 40s
  timeout: 10s
x-common-resources:
  limits:
    cpus: '1.0'
    memory: 1G
  reservations:
    cpus: '0.5'
    memory: 512M
x-common-volumes:
- app_data:/app/data
- app_logs:/app/logs
- app_config:/app/config:ro
- app_backups:/app/backups
x-security-config:
  cap_add:
  - NET_BIND_SERVICE
  cap_drop:
  - ALL
  read_only: true
  security_opt:
  - no-new-privileges:true
