"""
Array API Compatibility Module

This module provides compatibility with the Array API specification.
See https://data-apis.org/array-api/latest/ for more details.
"""

import numpy as np
from typing import Any, List, Optional, Tuple, Union

# Re-export numpy as the default array API implementation
numpy = np

def get_namespace(*arrays: Any) -> Tuple[Any, bool]:
    """
    Get the namespace for the given arrays.

    This is a simplified implementation that returns numpy as the default namespace.
    In a full implementation, this would inspect the arrays to determine their
    common namespace.

    Parameters
    ----------
    *arrays : array objects
        Array objects to inspect.

    Returns
    -------
    namespace : module
        The namespace shared by the arrays.
    is_array_api_compliant : bool
        True if the arrays implement the Array API spec.
    """
    # For now, always return numpy as the namespace
    return numpy, False

# Common array API functions
def asarray(obj: Any, /, *, dtype: Optional[Any] = None, copy: Optional[bool] = None, device: Optional[Any] = None) -> Any:
    """Convert input to an array."""
    return np.asarray(obj, dtype=dtype, copy=copy)

def zeros(shape: Union[int, Tuple[int, ...]], *, dtype: Optional[Any] = None, device: Optional[Any] = None) -> Any:
    """Return a new array of given shape and type, filled with zeros."""
    return np.zeros(shape, dtype=dtype)

def ones(shape: Union[int, Tuple[int, ...]], *, dtype: Optional[Any] = None, device: Optional[Any] = None) -> Any:
    """Return a new array of given shape and type, filled with ones."""
    return np.ones(shape, dtype=dtype)

def empty(shape: Union[int, Tuple[int, ...]], *, dtype: Optional[Any] = None, device: Optional[Any] = None) -> Any:
    """Return a new array of given shape and type, without initializing entries."""
    return np.empty(shape, dtype=dtype)

def full(shape: Union[int, Tuple[int, ...]], fill_value: Any, *, dtype: Optional[Any] = None, device: Optional[Any] = None) -> Any:
    """Return a new array of given shape and type, filled with fill_value."""
    return np.full(shape, fill_value, dtype=dtype)

def eye(n_rows: int, n_cols: Optional[int] = None, /, *, k: int = 0, dtype: Optional[Any] = None, device: Optional[Any] = None) -> Any:
    """Return a 2-D array with ones on the diagonal and zeros elsewhere."""
    return np.eye(n_rows, n_cols, k=k, dtype=dtype)

def linspace(start: Union[int, float], stop: Union[int, float], /, num: int, *, dtype: Optional[Any] = None, device: Optional[Any] = None, endpoint: bool = True) -> Any:
    """Return evenly spaced numbers over a specified interval."""
    return np.linspace(start, stop, num, dtype=dtype, endpoint=endpoint)

def arange(start: Union[int, float], /, stop: Optional[Union[int, float]] = None, step: Union[int, float] = 1, *, dtype: Optional[Any] = None, device: Optional[Any] = None) -> Any:
    """Return evenly spaced values within a given interval."""
    return np.arange(start, stop, step, dtype=dtype)

def meshgrid(*arrays: Any, indexing: str = "xy") -> List[Any]:
    """Return coordinate matrices from coordinate vectors."""
    return list(np.meshgrid(*arrays, indexing=indexing))

# Device support (simplified)
class Device:
    """Device object for array API compatibility."""

    def __init__(self, device: str):
        self.device = device

    def __str__(self) -> str:
        return self.device

    def __repr__(self) -> str:
        return f"Device('{self.device}')"

# Default device
def default_device() -> Device:
    """Return the default device."""
    return Device("cpu")

# Export common functions
__all__ = [
    "numpy",
    "get_namespace",
    "asarray",
    "zeros",
    "ones",
    "empty",
    "full",
    "eye",
    "linspace",
    "arange",
    "meshgrid",
    "Device",
    "default_device",
]
