networks:
  ai-coding-network:
    external: true
services:
  test-site:
    build:
      context: sites
      dockerfile: ../containers/Dockerfile.test-site
    container_name: site-test-site
    environment:
    - SITE_NAME=test-site
    - PORT=80
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:80/health
      timeout: 10s
    networks:
    - ai-coding-network
    ports:
    - 8080:80
    restart: unless-stopped
    volumes:
    - ./test-site:/app/test-site:ro
    - ./logs:/app/logs
version: '3.8'
