{"agent_name": "CursorAgent", "version": "1.0.0", "description": "AI Agent with automatic cursor rules integration for consistent code generation", "cursor_rules_path": "config/cursorrules.md", "cursor_rules_fallback_paths": [".cursor/rules/cursorrules.md", "docs/cursorrules.md", "cursorrules.md"], "model_settings": {"model_name": "yi-coder:1.5b", "system_prompt": "You are a helpful AI assistant specializing in code generation and development tasks. You MUST follow all cursor rules provided in the system prompt for every task.", "temperature": 0.1, "max_tokens": 4000, "top_p": 0.9}, "enforcement": {"strict_mode": true, "auto_validate": true, "pre_commit_checks": true, "post_execution_validation": true, "compliance_reporting": true}, "validation": {"check_todos": true, "check_testing": true, "check_docker": true, "check_static_analysis": true, "check_dependencies": true}, "logging": {"level": "INFO", "include_rules_validation": true, "include_compliance_status": true, "log_execution_details": true}, "integration": {"pre_commit_hooks": true, "ci_validation": true, "automated_testing": true, "compliance_dashboard": true}, "error_handling": {"fail_on_rules_violation": false, "warn_on_rules_violation": true, "auto_correct_minor_issues": false, "provide_suggestions": true}, "performance": {"cache_rules": true, "cache_duration_hours": 24, "parallel_validation": true, "optimize_prompt_length": true}}