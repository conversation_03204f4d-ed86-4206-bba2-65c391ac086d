#!/usr/bin/env python3
"""
Task Manager Module - Command Routing and Task Execution
Extracted from core/agent.py for modular organization
"""

from typing import TYPE_CHECKING, Any, Dict, List, Optional

from core.agents.base_agent import Agent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

if TYPE_CHECKING:
    from core.agents.agent_main import AIAgent


class CommandRouter:
    """Routes commands to appropriate handlers"""

    def __init__(self, agent: "AIAgent"):
        self.agent = agent
        self.logger = agent.logger
        self.error_handler = agent.error_handler

    async def route_command(self, command: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """Route a command to its appropriate handler"""
        try:
            self.logger.info(f"Routing command: {command}", command=command, args=args)

            # Map commands to handler methods
            command_handlers = {
                # Content management
                "create_content": self.agent.create_content,
                "list_content": self.agent.list_content,
                "update_content": self.agent.update_content,
                "delete_content": self.agent.delete_content,
                "generate_content": self.agent.generate_content,
                "check_links": self.agent.check_links,
                # Maintenance
                "update_dependencies": self.agent.update_dependencies,
                "approve_updates": self.agent.approve_updates,
                "maintenance_status": self.agent.maintenance_status,
                "run_maintenance": self.agent.run_maintenance,
                # Testing
                "test_html": self.agent.test_html,
                "test_css": self.agent.test_css,
                "test_playwright": self.agent.test_playwright,
                "test_deployment": self.agent.test_deployment,
                "test_suite": self.agent.test_suite,
                "test_results": self.agent.test_results,
                # Deployment
                "deploy": self.agent.deploy,
                "rollback": self.agent.rollback,
                "deployment_status": self.agent.deployment_status,
                "list_deployments": self.agent.list_deployments,
                # Website generation
                "generate_site": self.agent.generate_site,
                "list_templates": self.agent.list_templates,
                "customize_theme": self.agent.customize_theme,
                # Security
                "ssl_status": self.agent.ssl_status,
                "ssl_renew": self.agent.ssl_renew,
                "ssl_install": self.agent.ssl_install,
                "security_scan": self.agent.security_scan,
                "security_audit": self.agent.security_audit,
                "security_status": self.agent.security_status,
                # System
                "system_health": self.agent.system_health,
                "system_config": self.agent.system_config,
                "system_logs": self.agent.system_logs,
                # Learning and optimization
                "run_enhancement_cycle": self.agent.run_enhancement_cycle,
                "get_enhancement_status": self.agent.get_enhancement_status,
                "enable_enhancement": self.agent.enable_enhancement,
                "disable_enhancement": self.agent.disable_enhancement,
                "optimize_model": self.agent.optimize_model,
                "get_optimization_status": self.agent.get_optimization_status,
                "record_model_performance": self.agent.record_model_performance,
                "get_optimization_recommendations": self.agent.get_optimization_recommendations,
                "get_learning_summary": self.agent.get_learning_summary,
                "get_learning_recommendations": self.agent.get_learning_recommendations,
                "record_learning_event": self.agent.record_learning_event,
                "learn_code_pattern": self.agent.learn_code_pattern,
                "learn_user_preference": self.agent.learn_user_preference,
                "learn_performance_insight": self.agent.learn_performance_insight,
            }

            # Get the handler for this command
            handler = command_handlers.get(command)
            if not handler:
                return {
                    "success": False,
                    "error": f"Unknown command: {command}",
                    "available_commands": list(command_handlers.keys()),
                }

            # Execute the handler
            result = await handler(args)
            return result

        except Exception as e:
            return self.error_handler.handle_error(e, f"Command routing: {command}")


class TaskManager:
    """Manages task execution and coordination"""

    def __init__(self, agent: "AIAgent"):
        self.agent = agent
        self.logger = agent.logger
        self.error_handler = agent.error_handler
        self.command_router = CommandRouter(agent)

    async def execute_task(
        self, task_type: str, task_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a task with proper error handling and logging"""
        try:
            self.logger.info(
                f"Executing task: {task_type}", task_type=task_type, task_data=task_data
            )

            # Route to appropriate handler
            result = await self.command_router.route_command(task_type, task_data)

            # Record the task execution
            self.agent.feedback_manager.record_command(task_type, task_data, result)

            return result

        except Exception as e:
            error_result = self.error_handler.handle_error(
                e, f"Task execution: {task_type}"
            )
            self.agent.feedback_manager.record_command(
                task_type, task_data, error_result
            )
            return error_result

    async def execute_batch_tasks(
        self, tasks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Execute multiple tasks in sequence"""
        results = []
        for task in tasks:
            task_type = task.get("type")
            task_data = task.get("data", {})
            result = await self.execute_task(task_type, task_data)
            results.append(result)
        return results
