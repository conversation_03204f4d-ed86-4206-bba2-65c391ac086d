#!/usr/bin/env python3
"""
Learning Service CLI Commands
Provides command-line interface for automated learning system management

Commands:
- Check learning service status
- Get learning summary and recommendations
- Learn code patterns and user preferences
- Manage learning components
- Monitor learning performance
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict, Optional

import aiohttp

from core.agent import AIAgent
from learning.advanced_learning_enhancements import MetaLearningOptimizer
from learning.automated_learner import AutomatedLearner
from learning.best_practices_learner import BestPracticesLearner

# Add project root to path for imports
project_root = Path(__file__).parent.parent

sys.path.insert(0, str(project_root))


class LearningCommands:
    """CLI commands for learning service management"""

    def __init__(self, agent=None):
        self.agent = agent
        self.learning_service_url = "http://localhost:8084"

        # Initialize local learning components if agent is provided
        if agent:
            self.automated_learner = agent.automated_learner
            self.best_practices_learner = agent.best_practices_learner
            self.framework_monitor = agent.framework_monitor
        else:
            self.automated_learner = None
            self.best_practices_learner = None
            self.framework_monitor = None

    async def check_learning_service_status(self, **kwargs) -> Dict[str, Any]:
        """Check learning service status"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.learning_service_url}/health"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "command": "check_learning_service_status",
                            "result": data,
                            "message": "Learning service status retrieved",
                        }
                    else:
                        return {
                            "success": False,
                            "command": "check_learning_service_status",
                            "error": f"Service returned status {response.status}",
                            "message": "Failed to get learning service status",
                        }
        except Exception as e:
            return {
                "success": False,
                "command": "check_learning_service_status",
                "error": str(e),
                "message": "Failed to connect to learning service",
            }

    async def get_learning_summary(self, **kwargs) -> Dict[str, Any]:
        """Get learning summary"""
        try:
            if self.agent:
                # Use local agent
                result = await self.agent.get_learning_summary({})
                return {
                    "success": True,
                    "command": "get_learning_summary",
                    "result": result,
                    "message": "Learning summary retrieved from local agent",
                }
            else:
                # Use learning service
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.learning_service_url}/learning/summary"
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return {
                                "success": True,
                                "command": "get_learning_summary",
                                "result": data,
                                "message": "Learning summary retrieved from service",
                            }
                        else:
                            return {
                                "success": False,
                                "command": "get_learning_summary",
                                "error": f"Service returned status {response.status}",
                                "message": "Failed to get learning summary",
                            }
        except Exception as e:
            return {
                "success": False,
                "command": "get_learning_summary",
                "error": str(e),
                "message": "Failed to get learning summary",
            }

    async def get_learning_recommendations(self, **kwargs) -> Dict[str, Any]:
        """Get learning recommendations"""
        try:
            if self.agent:
                # Use local agent
                result = await self.agent.get_learning_recommendations({})
                return {
                    "success": True,
                    "command": "get_learning_recommendations",
                    "result": result,
                    "message": "Learning recommendations retrieved from local agent",
                }
            else:
                # Use learning service
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.learning_service_url}/learning/recommendations"
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return {
                                "success": True,
                                "command": "get_learning_recommendations",
                                "result": data,
                                "message": "Learning recommendations retrieved from service",
                            }
                        else:
                            return {
                                "success": False,
                                "command": "get_learning_recommendations",
                                "error": f"Service returned status {response.status}",
                                "message": "Failed to get learning recommendations",
                            }
        except Exception as e:
            return {
                "success": False,
                "command": "get_learning_recommendations",
                "error": str(e),
                "message": "Failed to get learning recommendations",
            }

    async def learn_code_pattern(
        self, pattern_data: Dict[str, Any], **kwargs
    ) -> Dict[str, Any]:
        """Learn a new code pattern"""
        try:
            if self.agent:
                # Use local agent
                result = await self.agent.learn_code_pattern(pattern_data)
                return {
                    "success": True,
                    "command": "learn_code_pattern",
                    "result": result,
                    "message": "Code pattern learned by local agent",
                }
            else:
                # Use learning service
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.learning_service_url}/learn/pattern", json=pattern_data
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return {
                                "success": True,
                                "command": "learn_code_pattern",
                                "result": data,
                                "message": "Code pattern learned by service",
                            }
                        else:
                            return {
                                "success": False,
                                "command": "learn_code_pattern",
                                "error": f"Service returned status {response.status}",
                                "message": "Failed to learn code pattern",
                            }
        except Exception as e:
            return {
                "success": False,
                "command": "learn_code_pattern",
                "error": str(e),
                "message": "Failed to learn code pattern",
            }

    async def learn_user_preference(
        self, preference_data: Dict[str, Any], **kwargs
    ) -> Dict[str, Any]:
        """Learn user preference"""
        try:
            if self.agent:
                # Use local agent
                result = await self.agent.learn_user_preference(preference_data)
                return {
                    "success": True,
                    "command": "learn_user_preference",
                    "result": result,
                    "message": "User preference learned by local agent",
                }
            else:
                # Use learning service
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.learning_service_url}/learn/preference",
                        json=preference_data,
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return {
                                "success": True,
                                "command": "learn_user_preference",
                                "result": data,
                                "message": "User preference learned by service",
                            }
                        else:
                            return {
                                "success": False,
                                "command": "learn_user_preference",
                                "error": f"Service returned status {response.status}",
                                "message": "Failed to learn user preference",
                            }
        except Exception as e:
            return {
                "success": False,
                "command": "learn_user_preference",
                "error": str(e),
                "message": "Failed to learn user preference",
            }

    async def get_learning_status(self, **kwargs) -> Dict[str, Any]:
        """Get comprehensive learning system status"""
        try:
            if self.agent:
                # Use local agent
                result = await self.agent.get_status()
                return {
                    "success": True,
                    "command": "get_learning_status",
                    "result": result,
                    "message": "Learning status retrieved from local agent",
                }
            else:
                # Use learning service
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.learning_service_url}/status"
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            return {
                                "success": True,
                                "command": "get_learning_status",
                                "result": data,
                                "message": "Learning status retrieved from service",
                            }
                        else:
                            return {
                                "success": False,
                                "command": "get_learning_status",
                                "error": f"Service returned status {response.status}",
                                "message": "Failed to get learning status",
                            }
        except Exception as e:
            return {
                "success": False,
                "command": "get_learning_status",
                "error": str(e),
                "message": "Failed to get learning status",
            }

    async def test_learning_components(self, **kwargs) -> Dict[str, Any]:
        """Test learning components functionality"""
        try:
            test_results = {}

            # Test automated learner
            if self.automated_learner:
                try:
                    test_results["automated_learner"] = {
                        "status": "available",
                        "type": type(self.automated_learner).__name__,
                    }
                except Exception as e:
                    test_results["automated_learner"] = {
                        "status": "error",
                        "error": str(e),
                    }
            else:
                test_results["automated_learner"] = {"status": "not_available"}

            # Test best practices learner
            if self.best_practices_learner:
                try:
                    test_results["best_practices_learner"] = {
                        "status": "available",
                        "type": type(self.best_practices_learner).__name__,
                    }
                except Exception as e:
                    test_results["best_practices_learner"] = {
                        "status": "error",
                        "error": str(e),
                    }
            else:
                test_results["best_practices_learner"] = {"status": "not_available"}

            # Test framework monitor
            if self.framework_monitor:
                try:
                    test_results["framework_monitor"] = {
                        "status": "available",
                        "type": type(self.framework_monitor).__name__,
                    }
                except Exception as e:
                    test_results["framework_monitor"] = {
                        "status": "error",
                        "error": str(e),
                    }
            else:
                test_results["framework_monitor"] = {"status": "not_available"}

            return {
                "success": True,
                "command": "test_learning_components",
                "result": test_results,
                "message": "Learning components test completed",
            }
        except Exception as e:
            return {
                "success": False,
                "command": "test_learning_components",
                "error": str(e),
                "message": "Failed to test learning components",
            }

    async def optimize_learning_system(self, **kwargs) -> Dict[str, Any]:
        """Optimize learning system performance"""
        try:
            if self.agent and hasattr(self.agent, "optimize_learning_system"):
                result = await self.agent.optimize_learning_system({})
                return {
                    "success": True,
                    "command": "optimize_learning_system",
                    "result": result,
                    "message": "Learning system optimization completed",
                }
            else:
                return {
                    "success": False,
                    "command": "optimize_learning_system",
                    "error": "Optimization method not available",
                    "message": "Learning system optimization not supported",
                }
        except Exception as e:
            return {
                "success": False,
                "command": "optimize_learning_system",
                "error": str(e),
                "message": "Failed to optimize learning system",
            }

    async def export_learning_data(
        self, export_path: str = "learning_export.json", **kwargs
    ) -> Dict[str, Any]:
        """Export learning data to file"""
        try:
            if self.agent:
                # Get learning data from agent
                learning_data = {
                    "automated_learner": (
                        self.automated_learner.get_learning_data()
                        if self.automated_learner
                        else {}
                    ),
                    "best_practices_learner": (
                        self.best_practices_learner.get_learning_data()
                        if self.best_practices_learner
                        else {}
                    ),
                    "framework_monitor": (
                        self.framework_monitor.get_learning_data()
                        if self.framework_monitor
                        else {}
                    ),
                    "export_timestamp": asyncio.get_event_loop().time(),
                }

                # Save to file
                with open(export_path, "w") as f:
                    json.dump(learning_data, f, indent=2)

                return {
                    "success": True,
                    "command": "export_learning_data",
                    "result": {
                        "export_path": export_path,
                        "data_size": len(json.dumps(learning_data)),
                    },
                    "message": f"Learning data exported to {export_path}",
                }
            else:
                return {
                    "success": False,
                    "command": "export_learning_data",
                    "error": "No local agent available",
                    "message": "Cannot export learning data without local agent",
                }
        except Exception as e:
            return {
                "success": False,
                "command": "export_learning_data",
                "error": str(e),
                "message": "Failed to export learning data",
            }

    async def import_learning_data(self, import_path: str, **kwargs) -> Dict[str, Any]:
        """Import learning data from file"""
        try:
            if not Path(import_path).exists():
                return {
                    "success": False,
                    "command": "import_learning_data",
                    "error": f"File {import_path} not found",
                    "message": "Import file does not exist",
                }

            if self.agent:
                # Load data from file
                with open(import_path, "r") as f:
                    learning_data = json.load(f)

                # Import data to components
                if self.automated_learner and "automated_learner" in learning_data:
                    self.automated_learner.import_learning_data(
                        learning_data["automated_learner"]
                    )

                if (
                    self.best_practices_learner
                    and "best_practices_learner" in learning_data
                ):
                    self.best_practices_learner.import_learning_data(
                        learning_data["best_practices_learner"]
                    )

                if self.framework_monitor and "framework_monitor" in learning_data:
                    self.framework_monitor.import_learning_data(
                        learning_data["framework_monitor"]
                    )

                return {
                    "success": True,
                    "command": "import_learning_data",
                    "result": {
                        "import_path": import_path,
                        "imported_components": list(learning_data.keys()),
                    },
                    "message": f"Learning data imported from {import_path}",
                }
            else:
                return {
                    "success": False,
                    "command": "import_learning_data",
                    "error": "No local agent available",
                    "message": "Cannot import learning data without local agent",
                }
        except Exception as e:
            return {
                "success": False,
                "command": "import_learning_data",
                "error": str(e),
                "message": "Failed to import learning data",
            }


# CLI command mapping for integration with main agent
LEARNING_COMMANDS = {
    "learning_check_status": LearningCommands().check_learning_service_status,
    "learning_get_summary": LearningCommands().get_learning_summary,
    "learning_get_recommendations": LearningCommands().get_learning_recommendations,
    "learning_learn_pattern": LearningCommands().learn_code_pattern,
    "learning_learn_preference": LearningCommands().learn_user_preference,
    "learning_get_status": LearningCommands().get_learning_status,
    "learning_test_components": LearningCommands().test_learning_components,
    "learning_optimize_system": LearningCommands().optimize_learning_system,
    "learning_export_data": LearningCommands().export_learning_data,
    "learning_import_data": LearningCommands().import_learning_data,
}


if __name__ == "__main__":
    # Test the CLI commands
    async def test_commands():
        commands = LearningCommands()

        print("Testing Learning CLI commands...")

        # Test status check
        print("\n1. Testing status check...")
        result = await commands.check_learning_service_status()
        print(f"Status check: {result}")

        # Test learning summary
        print("\n2. Testing learning summary...")
        result = await commands.get_learning_summary()
        print(f"Learning summary: {result}")

        # Test component testing
        print("\n3. Testing component testing...")
        result = await commands.test_learning_components()
        print(f"Component test: {result}")

    asyncio.run(test_commands())
