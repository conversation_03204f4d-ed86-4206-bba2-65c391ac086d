{"quantum_advantage_threshold": 0.3, "hybrid_optimization_enabled": true, "quantum_task_types": ["optimization", "simulation", "cryptography"], "classical_fallback_enabled": true, "quantum_resource_limits": {"qubits": 100, "coherence_time": 100, "error_rate": 0.01}, "quantum_capabilities": {"optimization": {"quantum_advantage": 0.5, "classical_equivalent": "genetic_algorithm", "resource_requirements": {"qubits": 50, "coherence_time": 50, "error_rate": 0.005}, "suitability_score": 0.8}, "simulation": {"quantum_advantage": 0.4, "classical_equivalent": "monte_carlo", "resource_requirements": {"qubits": 80, "coherence_time": 80, "error_rate": 0.008}, "suitability_score": 0.7}, "cryptography": {"quantum_advantage": 0.6, "classical_equivalent": "rsa_encryption", "resource_requirements": {"qubits": 100, "coherence_time": 100, "error_rate": 0.001}, "suitability_score": 0.9}}, "hybrid_routing": {"optimization_tasks": {"quantum_threshold": 0.3, "fallback_strategy": "classical_optimization"}, "simulation_tasks": {"quantum_threshold": 0.4, "fallback_strategy": "classical_simulation"}, "cryptography_tasks": {"quantum_threshold": 0.5, "fallback_strategy": "classical_encryption"}}, "resource_optimization": {"quantum_allocation_weight": 0.6, "classical_allocation_weight": 0.4, "dynamic_reallocation": true}}