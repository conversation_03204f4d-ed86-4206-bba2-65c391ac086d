"""
Task Queue Manager
Handles deferred task execution, prioritization, and scheduling
"""

import asyncio
import heapq
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from queue import PriorityQueue
from typing import Any, Callable, Dict, List, Optional

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """Task priority levels"""

    LOW = 4
    MEDIUM = 3
    HIGH = 2
    CRITICAL = 1


class TaskStatus(Enum):
    """Task status in queue"""

    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY_PENDING = "retry_pending"


@dataclass
class QueuedTask:
    """Represents a task in the queue"""

    id: str
    name: str
    description: str
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    max_retries: int = 3
    retry_count: int = 0
    retry_delay_minutes: int = 5
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    executor_func: Optional[Callable] = None
    executor_args: tuple = field(default_factory=tuple)
    executor_kwargs: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        if self.scheduled_at is None:
            self.scheduled_at = self.created_at

    def __lt__(self, other):
        """Priority queue comparison - lower priority number = higher priority"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.scheduled_at < other.scheduled_at


class TaskQueueManager:
    """Manages deferred task execution with prioritization and scheduling"""

    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_queue: List[QueuedTask] = []
        self.running_tasks: Dict[str, QueuedTask] = {}
        self.completed_tasks: Dict[str, QueuedTask] = {}
        self.task_counter = 0
        self.is_running = False
        self.queue_processor_task: Optional[asyncio.Task] = None
        self.lock = asyncio.Lock()

    async def start(self):
        """Start the task queue processor"""
        if self.is_running:
            return

        self.is_running = True
        self.queue_processor_task = asyncio.create_task(self._process_queue())
        logger.info("Task queue manager started")

    async def stop(self):
        """Stop the task queue processor"""
        self.is_running = False

        # Cancel the main queue processor task
        if self.queue_processor_task:
            self.queue_processor_task.cancel()
            try:
                await self.queue_processor_task
            except asyncio.CancelledError:
                pass

        # Cancel all running tasks and their execution tasks
        running_task_ids = list(self.running_tasks.keys())
        for task_id in running_task_ids:
            task = self.running_tasks[task_id]
            task.status = TaskStatus.CANCELLED

            # Cancel the execution task if it exists
            if "execution_task" in task.metadata:
                execution_task = task.metadata["execution_task"]
                if not execution_task.done():
                    execution_task.cancel()
                    try:
                        await execution_task
                    except asyncio.CancelledError:
                        pass

            # Move to completed tasks
            self.completed_tasks[task_id] = task
            del self.running_tasks[task_id]
            logger.info(f"Cancelled running task {task_id}")

        # Clear the queue
        self.task_queue.clear()

        logger.info("Task queue manager stopped")

    async def add_task(
        self,
        name: str,
        description: str,
        executor_func: Callable,
        priority: TaskPriority = TaskPriority.MEDIUM,
        scheduled_at: Optional[datetime] = None,
        max_retries: int = 3,
        retry_delay_minutes: int = 5,
        dependencies: List[str] = None,
        **kwargs,
    ) -> str:
        """Add a task to the queue"""
        async with self.lock:
            task_id = f"queued_task_{self.task_counter:06d}"
            self.task_counter += 1

            task = QueuedTask(
                id=task_id,
                name=name,
                description=description,
                priority=priority,
                status=TaskStatus.QUEUED,
                created_at=datetime.now(),
                scheduled_at=scheduled_at or datetime.now(),
                max_retries=max_retries,
                retry_delay_minutes=retry_delay_minutes,
                dependencies=dependencies or [],
                executor_func=executor_func,
                executor_kwargs=kwargs,
            )

            heapq.heappush(self.task_queue, task)
            logger.info(f"Added task {task_id} to queue with priority {priority.name}")
            return task_id

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific task"""
        # Check running tasks
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            return {
                "id": task.id,
                "name": task.name,
                "status": task.status.value,
                "priority": task.priority.name,
                "created_at": task.created_at.isoformat(),
                "scheduled_at": task.scheduled_at.isoformat(),
                "retry_count": task.retry_count,
                "max_retries": task.max_retries,
            }

        # Check completed tasks
        if task_id in self.completed_tasks:
            task = self.completed_tasks[task_id]
            return {
                "id": task.id,
                "name": task.name,
                "status": task.status.value,
                "priority": task.priority.name,
                "created_at": task.created_at.isoformat(),
                "scheduled_at": task.scheduled_at.isoformat(),
                "retry_count": task.retry_count,
                "max_retries": task.max_retries,
            }

        # Check queued tasks
        for task in self.task_queue:
            if task.id == task_id:
                return {
                    "id": task.id,
                    "name": task.name,
                    "status": task.status.value,
                    "priority": task.priority.name,
                    "created_at": task.created_at.isoformat(),
                    "scheduled_at": task.scheduled_at.isoformat(),
                    "retry_count": task.retry_count,
                    "max_retries": task.max_retries,
                }

        return None

    async def get_queue_status(self) -> Dict[str, Any]:
        """Get overall queue status"""
        return {
            "queued_tasks": len(self.task_queue),
            "running_tasks": len(self.running_tasks),
            "completed_tasks": len(self.completed_tasks),
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "is_running": self.is_running,
        }

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        async with self.lock:
            # Check running tasks
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                task.status = TaskStatus.CANCELLED
                del self.running_tasks[task_id]
                self.completed_tasks[task_id] = task
                logger.info(f"Cancelled running task {task_id}")
                return True

            # Check queued tasks
            for i, task in enumerate(self.task_queue):
                if task.id == task_id:
                    task.status = TaskStatus.CANCELLED
                    self.completed_tasks[task_id] = task
                    self.task_queue.pop(i)
                    heapq.heapify(self.task_queue)  # Re-heapify after removal
                    logger.info(f"Cancelled queued task {task_id}")
                    return True

            return False

    async def _process_queue(self):
        """Main queue processing loop"""
        while self.is_running:
            try:
                await self._process_available_tasks()
                await asyncio.sleep(1)  # Check every second
            except Exception as e:
                logger.error(f"Error in queue processor: {e}")
                await asyncio.sleep(5)  # Wait longer on error

    async def _process_available_tasks(self):
        """Process tasks that are ready to run"""
        async with self.lock:
            # Check if we can run more tasks
            if len(self.running_tasks) >= self.max_concurrent_tasks:
                return

            # Process queued tasks
            while (
                self.task_queue and len(self.running_tasks) < self.max_concurrent_tasks
            ):
                task = heapq.heappop(self.task_queue)

                # Check if task is scheduled to run
                if task.scheduled_at > datetime.now():
                    # Put it back in queue
                    heapq.heappush(self.task_queue, task)
                    break

                # Check dependencies
                if not await self._check_dependencies(task):
                    # Put it back in queue
                    heapq.heappush(self.task_queue, task)
                    break

                # Start the task
                await self._start_task(task)

    async def _check_dependencies(self, task: QueuedTask) -> bool:
        """Check if task dependencies are satisfied"""
        for dep_id in task.dependencies:
            if dep_id in self.running_tasks:
                return False  # Dependency still running
            if dep_id not in self.completed_tasks:
                return False  # Dependency not completed
            if self.completed_tasks[dep_id].status == TaskStatus.FAILED:
                return False  # Dependency failed
        return True

    async def _start_task(self, task: QueuedTask):
        """Start execution of a task"""
        task.status = TaskStatus.RUNNING
        self.running_tasks[task.id] = task

        # Create execution task with proper error handling
        async def execute_with_cleanup():
            try:
                await self._execute_task(task)
            except asyncio.CancelledError:
                # Task was cancelled during shutdown
                task.status = TaskStatus.CANCELLED
                logger.info(f"Task {task.id} was cancelled")
            except Exception as e:
                logger.error(f"Unexpected error in task {task.id}: {e}")
                task.status = TaskStatus.FAILED
                task.metadata["error"] = str(e)
            finally:
                # Ensure task is moved to completed tasks
                async with self.lock:
                    if task.id in self.running_tasks:
                        del self.running_tasks[task.id]
                    if task.id not in self.completed_tasks:
                        self.completed_tasks[task.id] = task

        # Create the task and store reference for potential cancellation
        execution_task = asyncio.create_task(execute_with_cleanup())
        task.metadata["execution_task"] = execution_task

        logger.info(f"Started task {task.id}: {task.name}")

    async def _execute_task(self, task: QueuedTask):
        """Execute a task with retry logic"""
        try:
            if task.executor_func:
                result = await task.executor_func(
                    *task.executor_args, **task.executor_kwargs
                )
                task.status = TaskStatus.COMPLETED
                task.metadata["result"] = result
                logger.info(f"Task {task.id} completed successfully")
            else:
                raise ValueError("No executor function provided")

        except Exception as e:
            logger.error(f"Task {task.id} failed: {e}")
            task.metadata["error"] = str(e)

            # Check if we should retry
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.RETRY_PENDING
                task.scheduled_at = datetime.now() + timedelta(
                    minutes=task.retry_delay_minutes
                )

                # Put back in queue for retry
                async with self.lock:
                    heapq.heappush(self.task_queue, task)
                    logger.info(
                        f"Task {task.id} scheduled for retry {task.retry_count}/{task.max_retries}"
                    )
            else:
                task.status = TaskStatus.FAILED
                logger.error(f"Task {task.id} failed after {task.max_retries} retries")


# Global instance
task_queue_manager = TaskQueueManager()
