"""
Database connection module for dashboard backend.
Provides database session management and connection utilities.
"""

import logging
from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import StaticPool

from config.security import secure_config
from db import Base
from db.models import APIToken, CodeFile, Deployment, ModelRun, Project, Settings, User

# Configure logging
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = secure_config.database_url

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
    echo=False,  # Set to True for SQL debugging
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_tables():
    """Create all database tables with validation"""
    try:
        # Check if tables already exist
        from sqlalchemy import inspect

        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()

        if existing_tables:
            logger.info(f"Existing tables found: {existing_tables}")

        # Create all tables with current schema
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")

        # Verify tables were created
        inspector = inspect(engine)
        created_tables = inspector.get_table_names()
        logger.info(f"Database now contains tables: {created_tables}")

        # Verify specific required tables exist
        required_tables = ["users", "projects", "code_files", "deployments"]
        missing_tables = [
            table for table in required_tables if table not in created_tables
        ]
        if missing_tables:
            raise Exception(f"Missing required tables: {missing_tables}")

        return True
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


def get_db() -> Generator[Session, None, None]:
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """Get database session as context manager"""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def init_db():
    """Initialize database with default data"""
    with get_db_context() as db:
        # Check if we already have users
        from db.database_manager import UserManager

        user_manager = UserManager()

        existing_users = user_manager.get_multi(db, limit=1)
        if existing_users:
            logger.info("Database already initialized")
            return

        # Check if admin user already exists
        existing_admin = user_manager.get_by_username(db, username="admin")
        if existing_admin:
            logger.info("Admin user already exists")
            return

        # Create default admin user with a secure password
        import secrets

        from passlib.context import CryptContext

        # Create password context for hashing
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

        # Generate a secure random password
        admin_password = secrets.token_urlsafe(16)

        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=pwd_context.hash(admin_password),
            is_active=True,
            is_superuser=True,
        )

        db.add(admin_user)
        db.commit()

        logger.info(
            f"Default admin user created with secure password. Please check secure logs for the password."
        )
        # Note: In a real application, you would want to send this password to the admin securely
        # or require them to reset it on first login


def health_check() -> bool:
    """Check database health"""
    try:
        with get_db_context() as db:
            # Simple query to test connection
            from sqlalchemy.sql import text

            db.execute(text("SELECT 1"))
            return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


def get_database_stats() -> dict:
    """Get database statistics"""
    try:
        with get_db_context() as db:
            stats = {}

            # Count records in each table
            for model in [
                User,
                Project,
                CodeFile,
                Deployment,
                ModelRun,
                APIToken,
                Settings,
            ]:
                count = db.query(model).count()
                stats[model.__tablename__] = count

            return stats
    except Exception as e:
        logger.error(f"Error getting database stats: {e}")
        return {}


def backup_database(backup_path: str) -> bool:
    """Create a database backup"""
    try:
        import os
        import shutil
        from datetime import datetime

        # Create backup directory if it doesn't exist
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)

        # Copy database file
        shutil.copy2("./database/ai_coding_agent.db", backup_path)

        logger.info(f"Database backed up to: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Error backing up database: {e}")
        return False


def restore_database(backup_path: str) -> bool:
    """Restore database from backup"""
    try:
        import os
        import shutil

        if not os.path.exists(backup_path):
            logger.error(f"Backup file not found: {backup_path}")
            return False

        # Close all connections
        global engine
        engine.dispose()

        # Restore database file
        shutil.copy2(backup_path, "./database/ai_coding_agent.db")

        # Recreate engine
        engine = create_engine(
            DATABASE_URL,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
            echo=False,
        )

        logger.info(f"Database restored from: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Error restoring database: {e}")
        return False
