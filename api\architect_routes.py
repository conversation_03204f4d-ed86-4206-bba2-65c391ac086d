import asyncio

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from pydantic import BaseModel

from core.agents import ArchitectAgent

router = APIRouter(prefix="/architect", tags=["ArchitectAgent"])


class ArchitectCommandRequest(BaseModel):
    command: str
    priority: str = None


class ArchitectStatusRequest(BaseModel):
    task_id: str


# Dependency provider (singleton pattern for now)
architect_agent = ArchitectAgent()


def get_architect_agent():
    return architect_agent


@router.post("/process")
async def process_command(
    request: ArchitectCommandRequest,
    agent: ArchitectAgent = Depends(get_architect_agent),
):
    result = await agent.process_user_command(request.command, request.priority)
    return result


@router.get("/status/{task_id}")
async def get_status(
    task_id: str, agent: ArchitectAgent = Depends(get_architect_agent)
):
    result = await agent.get_task_status(task_id)
    if not result.get("success"):
        raise HTTPException(status_code=404, detail=result.get("error"))
    return result


@router.get("/all")
async def get_all(agent: ArchitectAgent = Depends(get_architect_agent)):
    return await agent.get_all_tasks()
