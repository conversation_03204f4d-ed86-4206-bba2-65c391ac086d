{"generation": {"default_strategy": "hybrid", "strategies": {"template_based": {"enabled": true, "priority": 1, "max_template_size": 1000}, "ast_manipulation": {"enabled": true, "priority": 2, "max_ast_depth": 20}, "pattern_based": {"enabled": true, "priority": 3, "max_patterns": 100}, "hybrid": {"enabled": true, "priority": 0, "combination_weights": {"template": 0.3, "ast": 0.4, "pattern": 0.3}}}, "quality_thresholds": {"min_complexity_score": 0.6, "min_quality_score": 0.7, "max_generation_time": 30.0}}, "languages": {"python": {"enabled": true, "priority": 1, "features": {"type_hints": true, "async_await": true, "dataclasses": true, "enums": true, "docstrings": true, "error_handling": true}, "conventions": {"naming": "snake_case", "max_line_length": 88, "import_style": "absolute"}}, "typescript": {"enabled": true, "priority": 2, "features": {"strict_mode": true, "interfaces": true, "generics": true, "async_await": true, "decorators": true}, "conventions": {"naming": "camelCase", "max_line_length": 100, "import_style": "relative"}}, "javascript": {"enabled": true, "priority": 3, "features": {"es6_modules": true, "async_await": true, "destructuring": true, "arrow_functions": true}, "conventions": {"naming": "camelCase", "max_line_length": 100, "import_style": "relative"}}, "jsx": {"enabled": true, "priority": 4, "features": {"functional_components": true, "hooks": true, "props_validation": true, "styled_components": true}, "conventions": {"naming": "PascalCase", "max_line_length": 100, "import_style": "relative"}}, "tsx": {"enabled": true, "priority": 5, "features": {"functional_components": true, "hooks": true, "typescript": true, "styled_components": true}, "conventions": {"naming": "PascalCase", "max_line_length": 100, "import_style": "relative"}}}, "ast_manipulation": {"max_ast_size": 10000, "max_merge_attempts": 5, "preserve_comments": true, "preserve_formatting": true, "node_cache_size": 1000, "template_cache_size": 500}, "code_analysis": {"complexity_analysis": {"enabled": true, "max_cyclomatic_complexity": 10, "max_cognitive_complexity": 15, "max_nesting_depth": 5}, "quality_analysis": {"enabled": true, "min_maintainability_index": 65, "check_code_smells": true, "check_violations": true}, "pattern_analysis": {"enabled": true, "extract_patterns": true, "learn_patterns": true, "max_patterns_per_file": 50}}, "refactoring": {"enabled": true, "strategies": {"rename": true, "extract": true, "inline": true, "move": true, "transform": true}, "preserve_semantics": true, "validate_changes": true}, "performance": {"max_generation_time": 30.0, "max_analysis_time": 10.0, "max_refactoring_time": 15.0, "enable_caching": true, "cache_ttl": 3600, "parallel_processing": true, "max_workers": 4}, "templates": {"base_templates": {"python": {"module": "templates/python/module.py", "class": "templates/python/class.py", "function": "templates/python/function.py", "api": "templates/python/api.py"}, "typescript": {"module": "templates/typescript/module.ts", "class": "templates/typescript/class.ts", "function": "templates/typescript/function.ts", "component": "templates/typescript/component.tsx"}, "javascript": {"module": "templates/javascript/module.js", "class": "templates/javascript/class.js", "function": "templates/javascript/function.js", "component": "templates/javascript/component.jsx"}}, "custom_templates": {"enabled": true, "directory": "templates/custom", "auto_load": true}}, "patterns": {"library": {"enabled": true, "auto_learn": true, "max_patterns": 1000, "min_usage_count": 3, "min_success_rate": 0.7}, "matching": {"enabled": true, "similarity_threshold": 0.8, "max_candidates": 10, "context_weight": 0.6}, "customization": {"enabled": true, "parameter_substitution": true, "constraint_application": true, "validation": true}}, "integration": {"agents": {"frontend_agent": {"enabled": true, "priority": 1, "strategies": ["template_based", "pattern_based"]}, "backend_agent": {"enabled": true, "priority": 1, "strategies": ["ast_manipulation", "pattern_based"]}}, "cli": {"enabled": true, "commands": ["generate", "analyze", "refactor", "merge"]}, "api": {"enabled": true, "endpoints": ["/generate", "/analyze", "/refactor", "/merge"]}}, "logging": {"level": "INFO", "file": "logs/ast_generator.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "monitoring": {"enabled": true, "metrics": {"generation_success_rate": true, "average_generation_time": true, "language_usage": true, "strategy_effectiveness": true}, "alerts": {"high_failure_rate": 0.1, "slow_generation": 30.0, "memory_usage": 0.8}}}