"""
Pydantic models for dashboard API requests and responses.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class UserBase(BaseModel):
    """Base user model"""

    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., pattern=r"^[^@]+@[^@]+\.[^@]+$")


class UserCreate(UserBase):
    """User creation model"""

    password: str = Field(..., min_length=8)


class UserUpdate(BaseModel):
    """User update model"""

    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[str] = Field(None, pattern=r"^[^@]+@[^@]+\.[^@]+$")
    password: Optional[str] = Field(None, min_length=8)


class UserResponse(UserBase):
    """User response model"""

    id: int
    is_active: bool
    is_superuser: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ProjectBase(BaseModel):
    """Base project model"""

    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None


class ProjectCreate(ProjectBase):
    """Project creation model"""

    pass


class ProjectUpdate(BaseModel):
    """Project update model"""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None


class ProjectResponse(ProjectBase):
    """Project response model"""

    id: int
    owner_id: int
    created_at: datetime
    updated_at: datetime
    file_count: Optional[int] = 0
    deployment_count: Optional[int] = 0

    class Config:
        from_attributes = True


class CodeFileBase(BaseModel):
    """Base code file model"""

    filename: str = Field(..., min_length=1, max_length=255)
    content: Optional[str] = None
    path: Optional[str] = None
    language: Optional[str] = None
    is_directory: bool = False
    parent_id: Optional[int] = None


class CodeFileCreate(CodeFileBase):
    """Code file creation model"""

    project_id: int


class CodeFileUpdate(BaseModel):
    """Code file update model"""

    filename: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = None
    path: Optional[str] = None
    language: Optional[str] = None
    is_directory: Optional[bool] = None
    parent_id: Optional[int] = None


class CodeFileResponse(CodeFileBase):
    """Code file response model"""

    id: int
    project_id: int
    created_at: datetime
    updated_at: datetime
    children: List = []

    class Config:
        from_attributes = True


class DeploymentBase(BaseModel):
    """Base deployment model"""

    environment: str = Field(..., pattern=r"^(development|staging|production)$")
    url: Optional[str] = None
    commit_hash: Optional[str] = None
    logs: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class DeploymentCreate(DeploymentBase):
    """Deployment creation model"""

    project_id: int


class DeploymentUpdate(BaseModel):
    """Deployment update model"""

    status: Optional[str] = Field(
        None, pattern=r"^(pending|in_progress|success|failed)$"
    )
    environment: Optional[str] = Field(
        None, pattern=r"^(development|staging|production)$"
    )
    url: Optional[str] = None
    commit_hash: Optional[str] = None
    logs: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    completed_at: Optional[datetime] = None


class DeploymentResponse(DeploymentBase):
    """Deployment response model"""

    id: int
    project_id: int
    status: str
    created_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ModelRunBase(BaseModel):
    """Base model run model"""

    model_name: str = Field(..., min_length=1, max_length=100)
    prompt: Optional[str] = None
    response: Optional[str] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)
    duration: Optional[int] = None
    tokens_used: Optional[int] = None
    success: bool = True
    error_message: Optional[str] = None


class ModelRunCreate(ModelRunBase):
    """Model run creation model"""

    pass


class ModelRunResponse(ModelRunBase):
    """Model run response model"""

    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class APITokenBase(BaseModel):
    """Base API token model"""

    name: str = Field(..., min_length=1, max_length=100)
    expires_at: Optional[datetime] = None
    scopes: List[str] = Field(default_factory=list)


class APITokenCreate(APITokenBase):
    """API token creation model"""

    pass


class APITokenResponse(APITokenBase):
    """API token response model"""

    id: int
    token: str
    user_id: int
    is_active: bool
    created_at: datetime
    last_used_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SettingsBase(BaseModel):
    """Base settings model"""

    key: str = Field(..., min_length=1, max_length=100)
    value: Optional[Dict[str, Any]] = None
    description: Optional[str] = None


class SettingsCreate(SettingsBase):
    """Settings creation model"""

    pass


class SettingsUpdate(BaseModel):
    """Settings update model"""

    value: Optional[Dict[str, Any]] = None
    description: Optional[str] = None


class SettingsResponse(SettingsBase):
    """Settings response model"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class LoginRequest(BaseModel):
    """Login request model"""

    username: str
    password: str


class LoginResponse(BaseModel):
    """Login response model"""

    access_token: str
    token_type: str = "bearer"
    user: UserResponse


class TokenData(BaseModel):
    """Token data model"""

    username: Optional[str] = None
    scopes: List[Dict[str, Any]] = []


class WebSocketMessage(BaseModel):
    """WebSocket message model"""

    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class PaginationParams(BaseModel):
    """Pagination parameters"""

    skip: int = Field(0, ge=0, description="Number of records to skip")
    limit: int = Field(100, ge=1, le=1000, description="Number of records to return")


class SearchParams(BaseModel):
    """Search parameters"""

    query: Optional[str] = None
    filters: Dict[str, Any] = Field(default_factory=dict)
    sort_by: Optional[str] = None
    sort_order: str = Field("asc", pattern=r"^(asc|desc)$")


# Supabase Configuration Models
class SupabaseConfigBase(BaseModel):
    """Base Supabase configuration model"""

    project_url: str = Field(..., description="Supabase project URL")
    api_key: str = Field(..., description="Supabase API key")
    service_role_key: Optional[str] = Field(
        None, description="Supabase service role key"
    )
    database_url: Optional[str] = Field(None, description="Database connection URL")
    region: Optional[str] = Field(None, description="Supabase region")
    organization_id: Optional[str] = Field(None, description="Supabase organization ID")


class SupabaseConfigCreate(BaseModel):
    supabase_url: str
    supabase_anon_key: str
    supabase_service_role_key: str
    project_ref: str
    organization_id: str

    class Config:
        json_schema_extra = {
            "example": {
                "supabase_url": "https://your-project.supabase.co",
                "supabase_anon_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "supabase_service_role_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "project_ref": "your-project-ref",
                "organization_id": "org-123456",
            }
        }


class SupabaseConfigUpdate(BaseModel):
    supabase_url: Optional[str] = None
    supabase_anon_key: Optional[str] = None
    supabase_service_role_key: Optional[str] = None
    project_ref: Optional[str] = None
    organization_id: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "supabase_url": "https://your-project.supabase.co",
                "supabase_anon_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "supabase_service_role_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "project_ref": "your-project-ref",
                "organization_id": "org-123456",
            }
        }


class SupabaseConfigResponse(BaseModel):
    id: str
    project_id: str
    supabase_url: str
    project_ref: str
    organization_id: str
    linked: bool
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Database Migration Models
class DatabaseMigrationBase(BaseModel):
    """Base database migration model"""

    name: str = Field(..., min_length=1, max_length=255, description="Migration name")
    description: Optional[str] = Field(None, description="Migration description")
    migration_type: str = Field(
        ..., pattern=r"^(up|down|both)$", description="Migration type"
    )
    status: str = Field(
        "pending",
        pattern=r"^(pending|applied|failed|rolled_back)$",
        description="Migration status",
    )
    file_path: Optional[str] = Field(None, description="Migration file path")
    sql_content: Optional[str] = Field(None, description="SQL content of the migration")


class DatabaseMigrationCreate(BaseModel):
    name: str
    description: str
    sql_content: str
    version: str

    class Config:
        json_schema_extra = {
            "example": {
                "name": "create_users_table",
                "description": "Create the initial users table",
                "sql_content": "CREATE TABLE users (id SERIAL PRIMARY KEY);",
                "version": "1.0.0",
            }
        }


class DatabaseMigrationUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    sql_content: Optional[str] = None
    version: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "name": "create_users_table",
                "description": "Create the initial users table",
                "sql_content": "CREATE TABLE users (id SERIAL PRIMARY KEY);",
                "version": "1.0.0",
            }
        }


class DatabaseMigrationResponse(BaseModel):
    id: str
    project_id: str
    name: str
    description: str
    sql_content: str
    status: str
    version: str
    checksum: str
    applied_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Supabase Table Models
class SupabaseTableBase(BaseModel):
    """Base Supabase table model"""

    table_name: str = Field(..., min_length=1, max_length=255, description="Table name")
    schema_name: str = Field("public", description="Schema name")
    description: Optional[str] = Field(None, description="Table description")
    columns: Dict[str, Any] = Field(
        default_factory=dict, description="Table columns definition"
    )
    indexes: List[Dict[str, Any]] = Field(
        default_factory=list, description="Table indexes"
    )
    constraints: List[Dict[str, Any]] = Field(
        default_factory=list, description="Table constraints"
    )
    rls_enabled: bool = Field(False, description="Row Level Security enabled")
    rls_policies: List[Dict[str, Any]] = Field(
        default_factory=list, description="RLS policies"
    )


class SupabaseTableCreate(BaseModel):
    table_name: str
    schema_name: str = "public"
    columns: str  # JSON string
    indexes: str = "[]"  # JSON string
    constraints: str = "[]"  # JSON string

    class Config:
        json_schema_extra = {
            "example": {
                "table_name": "users",
                "schema_name": "public",
                "columns": '[{"name": "id", "type": "serial", "primary_key": true}]',
                "indexes": "[]",
                "constraints": "[]",
            }
        }


class SupabaseTableUpdate(BaseModel):
    table_name: Optional[str] = None
    schema_name: Optional[str] = None
    columns: Optional[str] = None
    indexes: Optional[str] = None
    constraints: Optional[str] = None

    class Config:
        json_schema_extra = {
            "example": {
                "table_name": "users",
                "schema_name": "public",
                "columns": '[{"name": "id", "type": "serial", "primary_key": true}]',
                "indexes": "[]",
                "constraints": "[]",
            }
        }


class SupabaseTableResponse(BaseModel):
    id: str
    project_id: str
    table_name: str
    schema_name: str
    columns: str
    indexes: str
    constraints: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Migration Deployment Models
class MigrationDeployRequest(BaseModel):
    """Migration deployment request model"""

    force: bool = Field(
        False, description="Force deployment even if there are conflicts"
    )
    dry_run: bool = Field(
        False, description="Perform a dry run without applying changes"
    )


class MigrationDeployResponse(BaseModel):
    id: str
    name: str
    status: str
    applied_at: Optional[datetime] = None
    message: str

    class Config:
        from_attributes = True


class MigrationRollbackRequest(BaseModel):
    """Migration rollback request model"""

    force: bool = Field(False, description="Force rollback even if there are conflicts")
    dry_run: bool = Field(
        False, description="Perform a dry run without applying changes"
    )


class MigrationRollbackResponse(BaseModel):
    """Migration rollback response model"""

    success: bool
    message: str
    rollback_id: Optional[str] = None
    logs: Optional[str] = None
    rolled_back_at: Optional[datetime] = None


# Supabase CLI Response Models
class SupabaseCLIResponse(BaseModel):
    """Supabase CLI command response model"""

    success: bool
    output: str
    error: Optional[str] = None
    exit_code: int
    command: str
    duration: Optional[float] = None


# Project Backend Models
class ProjectBackendUpdate(BaseModel):
    """Project backend update model"""

    backend_type: str = Field(
        ..., pattern=r"^(supabase|custom|none)$", description="Backend type"
    )
    project_type: Optional[str] = Field(None, description="Project type")


# Forward references are handled automatically in Pydantic v1
