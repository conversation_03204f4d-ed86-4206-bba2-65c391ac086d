#!/usr/bin/env python3
"""
TaskDistribution - Implements multiple distribution strategies for task allocation

This module provides various task distribution strategies including:
1. Round-robin distribution
2. Weighted distribution
3. Priority-based distribution
4. Load-aware distribution
5. Capability-based distribution
6. Hybrid distribution strategies
"""

import asyncio
import json
import logging
import random
import statistics
import time
from collections import defaultdict, deque
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class DistributionStrategy(Enum):
    """Task distribution strategies"""

    ROUND_ROBIN = "round_robin"
    WEIGHTED = "weighted"
    PRIORITY_BASED = "priority_based"
    LOAD_AWARE = "load_aware"
    CAPABILITY_BASED = "capability_based"
    HYBRID = "hybrid"
    ADAPTIVE = "adaptive"


class TaskPriority(Enum):
    """Task priority levels"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class DistributionDecision:
    """Represents a task distribution decision"""

    task_id: str
    selected_agent_id: str
    strategy_used: DistributionStrategy
    confidence_score: float
    reasoning: str
    alternative_agents: List[str] = field(default_factory=list)
    distribution_metrics: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class AgentCapability:
    """Represents agent capabilities for distribution"""

    agent_id: str
    agent_type: str
    capabilities: List[str] = field(default_factory=list)
    specializations: List[str] = field(default_factory=list)
    performance_score: float = 0.0
    availability_score: float = 1.0
    current_load: int = 0
    max_capacity: int = 10
    priority_weight: float = 1.0


class TaskDistribution:
    """
    Implements multiple distribution strategies for intelligent task allocation.
    """

    def __init__(self, config_path: str = "config/task_distribution_config.json"):
        self.config = self._load_config(config_path)
        self.agent_capabilities: Dict[str, AgentCapability] = {}
        self.distribution_history: deque = deque(maxlen=1000)
        self.strategy_performance: Dict[str, Dict[str, float]] = defaultdict(
            lambda: {
                "success_rate": 0.0,
                "average_confidence": 0.0,
                "total_decisions": 0,
            }
        )

        # Distribution state
        self.current_strategy = DistributionStrategy(
            self.config.get("default_strategy", "hybrid")
        )
        self.round_robin_index = 0

        logger.info("TaskDistribution initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded task distribution config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "distribution": {
                "default_strategy": "hybrid",
                "confidence_threshold": 0.7,
                "max_alternatives": 3,
                "fallback_strategy": "round_robin",
            },
            "strategies": {
                "round_robin": {"enabled": True, "weight": 1.0},
                "weighted": {
                    "enabled": True,
                    "weight": 1.0,
                    "factors": ["performance", "availability", "load"],
                },
                "priority_based": {
                    "enabled": True,
                    "weight": 1.0,
                    "priority_weights": {
                        "critical": 4.0,
                        "high": 2.0,
                        "medium": 1.0,
                        "low": 0.5,
                    },
                },
                "load_aware": {"enabled": True, "weight": 1.0, "load_threshold": 0.8},
                "capability_based": {
                    "enabled": True,
                    "weight": 1.0,
                    "capability_match_weight": 0.6,
                },
                "hybrid": {
                    "enabled": True,
                    "weight": 1.0,
                    "strategy_weights": {
                        "capability_based": 0.4,
                        "load_aware": 0.3,
                        "performance_based": 0.3,
                    },
                },
                "adaptive": {
                    "enabled": True,
                    "weight": 1.0,
                    "learning_rate": 0.1,
                    "performance_window": 100,
                },
            },
            "performance": {
                "track_distribution_history": True,
                "track_strategy_performance": True,
                "metrics_retention_days": 30,
            },
        }

    async def register_agent(
        self,
        agent_id: str,
        agent_type: str,
        capabilities: List[str] = None,
        specializations: List[str] = None,
        max_capacity: int = None,
    ) -> bool:
        """Register an agent for task distribution"""
        try:
            if agent_id not in self.agent_capabilities:
                max_capacity = max_capacity or 10

                self.agent_capabilities[agent_id] = AgentCapability(
                    agent_id=agent_id,
                    agent_type=agent_type,
                    capabilities=capabilities or [],
                    specializations=specializations or [],
                    max_capacity=max_capacity,
                )

                logger.info(
                    f"Registered agent {agent_id} ({agent_type}) for task distribution"
                )
            return True
        except Exception as e:
            logger.error(f"Failed to register agent {agent_id}: {e}")
            return False

    async def distribute_task(
        self,
        task_id: str,
        task_type: str,
        requirements: List[str] = None,
        priority: TaskPriority = TaskPriority.MEDIUM,
        available_agents: List[str] = None,
        strategy: DistributionStrategy = None,
    ) -> DistributionDecision:
        """
        Distribute a task to the most suitable agent using the specified strategy
        """
        try:
            # Use default strategy if none specified
            if strategy is None:
                strategy = self.current_strategy

            # Filter available agents
            if available_agents is None:
                available_agents = list(self.agent_capabilities.keys())

            available_agents = await self._filter_available_agents(
                available_agents, requirements
            )

            if not available_agents:
                raise ValueError("No available agents for task distribution")

            # Apply distribution strategy
            if strategy == DistributionStrategy.ROUND_ROBIN:
                decision = await self._round_robin_distribution(
                    task_id, available_agents
                )
            elif strategy == DistributionStrategy.WEIGHTED:
                decision = await self._weighted_distribution(
                    task_id, available_agents, requirements
                )
            elif strategy == DistributionStrategy.PRIORITY_BASED:
                decision = await self._priority_based_distribution(
                    task_id, available_agents, priority
                )
            elif strategy == DistributionStrategy.LOAD_AWARE:
                decision = await self._load_aware_distribution(
                    task_id, available_agents
                )
            elif strategy == DistributionStrategy.CAPABILITY_BASED:
                decision = await self._capability_based_distribution(
                    task_id, available_agents, requirements
                )
            elif strategy == DistributionStrategy.HYBRID:
                decision = await self._hybrid_distribution(
                    task_id, available_agents, requirements, priority
                )
            elif strategy == DistributionStrategy.ADAPTIVE:
                decision = await self._adaptive_distribution(
                    task_id, available_agents, requirements, priority
                )
            else:
                decision = await self._round_robin_distribution(
                    task_id, available_agents
                )

            # Store decision in history
            self.distribution_history.append(decision)

            # Update strategy performance
            self._update_strategy_performance(strategy.value, decision.confidence_score)

            logger.info(
                f"Distributed task {task_id} to agent {decision.selected_agent_id} using {strategy.value}"
            )

            return decision

        except Exception as e:
            logger.error(f"Failed to distribute task {task_id}: {e}")
            return await self._fallback_distribution(task_id, available_agents or [])

    async def _filter_available_agents(
        self, agent_ids: List[str], requirements: List[str] = None
    ) -> List[str]:
        """Filter agents based on availability and requirements"""
        available_agents = []

        for agent_id in agent_ids:
            capability = self.agent_capabilities.get(agent_id)
            if not capability:
                continue

            # Check availability
            if capability.availability_score < 0.3:
                continue

            # Check load
            load_ratio = capability.current_load / capability.max_capacity
            if load_ratio >= 0.9:
                continue

            # Check requirements if specified
            if requirements:
                if not self._check_requirements_match(capability, requirements):
                    continue

            available_agents.append(agent_id)

        return available_agents

    def _check_requirements_match(
        self, capability: AgentCapability, requirements: List[str]
    ) -> bool:
        """Check if agent capabilities match task requirements"""
        try:
            if not requirements:
                return True

            # Check capabilities and specializations
            agent_capabilities = [cap.lower() for cap in capability.capabilities]
            agent_specializations = [
                spec.lower() for spec in capability.specializations
            ]

            for requirement in requirements:
                requirement_lower = requirement.lower()

                # Check if requirement matches capabilities or specializations
                if (
                    requirement_lower in agent_capabilities
                    or requirement_lower in agent_specializations
                    or any(
                        req in agent_capabilities for req in requirement_lower.split()
                    )
                    or any(
                        req in agent_specializations
                        for req in requirement_lower.split()
                    )
                ):
                    continue
                else:
                    return False

            return True

        except Exception as e:
            logger.error(f"Failed to check requirements match: {e}")
            return True

    async def _round_robin_distribution(
        self, task_id: str, available_agents: List[str]
    ) -> DistributionDecision:
        """Round-robin distribution strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Get next agent in round-robin order
            selected_agent_id = available_agents[
                self.round_robin_index % len(available_agents)
            ]
            self.round_robin_index += 1

            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.ROUND_ROBIN,
                confidence_score=0.5,
                reasoning="Round-robin distribution",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "load_ratio": capability.current_load / capability.max_capacity,
                    "availability_score": capability.availability_score,
                },
            )

        except Exception as e:
            logger.error(f"Round-robin distribution failed: {e}")
            raise

    async def _weighted_distribution(
        self, task_id: str, available_agents: List[str], requirements: List[str] = None
    ) -> DistributionDecision:
        """Weighted distribution strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Calculate weights for each agent
            agent_weights = []
            total_weight = 0

            for agent_id in available_agents:
                capability = self.agent_capabilities[agent_id]

                # Calculate weight based on multiple factors
                performance_weight = capability.performance_score
                availability_weight = capability.availability_score
                load_weight = 1.0 - (capability.current_load / capability.max_capacity)

                # Combine weights
                total_weight_score = (
                    performance_weight * 0.4
                    + availability_weight * 0.4
                    + load_weight * 0.2
                )

                agent_weights.append((agent_id, total_weight_score))
                total_weight += total_weight_score

            if total_weight == 0:
                return await self._round_robin_distribution(task_id, available_agents)

            # Select agent based on weights
            random_value = random.uniform(0, total_weight)
            current_weight = 0

            for agent_id, weight in agent_weights:
                current_weight += weight
                if random_value <= current_weight:
                    selected_agent_id = agent_id
                    break
            else:
                selected_agent_id = agent_weights[-1][0]

            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.WEIGHTED,
                confidence_score=total_weight_score,
                reasoning=f"Weighted distribution (weight={total_weight_score:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "weight_score": total_weight_score,
                    "load_ratio": capability.current_load / capability.max_capacity,
                    "availability_score": capability.availability_score,
                },
            )

        except Exception as e:
            logger.error(f"Weighted distribution failed: {e}")
            raise

    async def _priority_based_distribution(
        self, task_id: str, available_agents: List[str], priority: TaskPriority
    ) -> DistributionDecision:
        """Priority-based distribution strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Get priority weights
            priority_weights = (
                self.config.get("strategies", {})
                .get("priority_based", {})
                .get("priority_weights", {})
            )
            priority_weight = priority_weights.get(priority.value, 1.0)

            # Calculate priority-adjusted scores
            agent_scores = []
            for agent_id in available_agents:
                capability = self.agent_capabilities[agent_id]

                # Base score from performance and availability
                base_score = (
                    capability.performance_score * 0.6
                    + capability.availability_score * 0.4
                )

                # Apply priority weight
                priority_score = base_score * priority_weight

                agent_scores.append((agent_id, priority_score))

            # Sort by priority score (descending)
            agent_scores.sort(key=lambda x: x[1], reverse=True)
            selected_agent_id = agent_scores[0][0]

            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.PRIORITY_BASED,
                confidence_score=agent_scores[0][1],
                reasoning=f"Priority-based distribution (priority={priority.value}, score={agent_scores[0][1]:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "priority": priority.value,
                    "priority_weight": priority_weight,
                    "priority_score": agent_scores[0][1],
                },
            )

        except Exception as e:
            logger.error(f"Priority-based distribution failed: {e}")
            raise

    async def _load_aware_distribution(
        self, task_id: str, available_agents: List[str]
    ) -> DistributionDecision:
        """Load-aware distribution strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Find agent with lowest load
            agent_loads = []
            for agent_id in available_agents:
                capability = self.agent_capabilities[agent_id]
                load_ratio = capability.current_load / capability.max_capacity
                agent_loads.append((agent_id, load_ratio))

            # Sort by load ratio (ascending)
            agent_loads.sort(key=lambda x: x[1])
            selected_agent_id = agent_loads[0][0]

            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.LOAD_AWARE,
                confidence_score=1.0 - agent_loads[0][1],
                reasoning=f"Load-aware distribution (load_ratio={agent_loads[0][1]:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "load_ratio": agent_loads[0][1],
                    "selected_load": capability.current_load,
                    "max_capacity": capability.max_capacity,
                },
            )

        except Exception as e:
            logger.error(f"Load-aware distribution failed: {e}")
            raise

    async def _capability_based_distribution(
        self, task_id: str, available_agents: List[str], requirements: List[str] = None
    ) -> DistributionDecision:
        """Capability-based distribution strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Calculate capability scores
            agent_capability_scores = []
            for agent_id in available_agents:
                capability = self.agent_capabilities[agent_id]

                # Calculate capability match score
                capability_score = 0.0
                if requirements:
                    capability_score = self._calculate_capability_match_score(
                        capability, requirements
                    )
                else:
                    capability_score = (
                        len(capability.capabilities) / 10.0
                    )  # Normalize by max expected capabilities

                agent_capability_scores.append((agent_id, capability_score))

            # Sort by capability score (descending)
            agent_capability_scores.sort(key=lambda x: x[1], reverse=True)
            selected_agent_id = agent_capability_scores[0][0]

            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.CAPABILITY_BASED,
                confidence_score=agent_capability_scores[0][1],
                reasoning=f"Capability-based distribution (capability_score={agent_capability_scores[0][1]:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "capability_score": agent_capability_scores[0][1],
                    "capabilities": capability.capabilities,
                    "specializations": capability.specializations,
                },
            )

        except Exception as e:
            logger.error(f"Capability-based distribution failed: {e}")
            raise

    def _calculate_capability_match_score(
        self, capability: AgentCapability, requirements: List[str]
    ) -> float:
        """Calculate capability match score for requirements"""
        try:
            if not requirements:
                return 0.5

            matches = 0
            total_requirements = len(requirements)

            agent_capabilities = [cap.lower() for cap in capability.capabilities]
            agent_specializations = [
                spec.lower() for spec in capability.specializations
            ]

            for requirement in requirements:
                requirement_lower = requirement.lower()

                # Check exact matches
                if (
                    requirement_lower in agent_capabilities
                    or requirement_lower in agent_specializations
                ):
                    matches += 1
                # Check partial matches
                elif any(
                    req in agent_capabilities for req in requirement_lower.split()
                ):
                    matches += 0.5
                elif any(
                    req in agent_specializations for req in requirement_lower.split()
                ):
                    matches += 0.7

            return matches / total_requirements if total_requirements > 0 else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate capability match score: {e}")
            return 0.0

    async def _hybrid_distribution(
        self,
        task_id: str,
        available_agents: List[str],
        requirements: List[str] = None,
        priority: TaskPriority = TaskPriority.MEDIUM,
    ) -> DistributionDecision:
        """Hybrid distribution strategy combining multiple approaches"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Get strategy weights
            strategy_weights = (
                self.config.get("strategies", {})
                .get("hybrid", {})
                .get("strategy_weights", {})
            )

            # Calculate scores using different strategies
            agent_scores = {}

            for agent_id in available_agents:
                capability = self.agent_capabilities[agent_id]

                # Capability-based score
                capability_score = 0.0
                if requirements:
                    capability_score = self._calculate_capability_match_score(
                        capability, requirements
                    )

                # Load-aware score
                load_score = 1.0 - (capability.current_load / capability.max_capacity)

                # Performance-based score
                performance_score = capability.performance_score

                # Combine scores using weights
                hybrid_score = (
                    capability_score * strategy_weights.get("capability_based", 0.4)
                    + load_score * strategy_weights.get("load_aware", 0.3)
                    + performance_score * strategy_weights.get("performance_based", 0.3)
                )

                agent_scores[agent_id] = hybrid_score

            # Select agent with highest hybrid score
            selected_agent_id = max(agent_scores.items(), key=lambda x: x[1])[0]
            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.HYBRID,
                confidence_score=agent_scores[selected_agent_id],
                reasoning=f"Hybrid distribution (score={agent_scores[selected_agent_id]:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "hybrid_score": agent_scores[selected_agent_id],
                    "capability_score": capability_score if requirements else 0.0,
                    "load_score": load_score,
                    "performance_score": performance_score,
                },
            )

        except Exception as e:
            logger.error(f"Hybrid distribution failed: {e}")
            raise

    async def _adaptive_distribution(
        self,
        task_id: str,
        available_agents: List[str],
        requirements: List[str] = None,
        priority: TaskPriority = TaskPriority.MEDIUM,
    ) -> DistributionDecision:
        """Adaptive distribution strategy that learns from previous decisions"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Analyze historical performance for each agent
            agent_performance = {}
            for agent_id in available_agents:
                performance_score = self._calculate_historical_performance(agent_id)
                agent_performance[agent_id] = performance_score

            # Combine with current capabilities and load
            agent_scores = {}
            for agent_id in available_agents:
                capability = self.agent_capabilities[agent_id]

                # Historical performance
                historical_score = agent_performance.get(agent_id, 0.5)

                # Current factors
                capability_score = 0.0
                if requirements:
                    capability_score = self._calculate_capability_match_score(
                        capability, requirements
                    )

                load_score = 1.0 - (capability.current_load / capability.max_capacity)
                availability_score = capability.availability_score

                # Adaptive score (weighted combination)
                adaptive_score = (
                    historical_score * 0.4
                    + capability_score * 0.3
                    + load_score * 0.2
                    + availability_score * 0.1
                )

                agent_scores[agent_id] = adaptive_score

            # Select agent with highest adaptive score
            selected_agent_id = max(agent_scores.items(), key=lambda x: x[1])[0]
            capability = self.agent_capabilities[selected_agent_id]

            return DistributionDecision(
                task_id=task_id,
                selected_agent_id=selected_agent_id,
                strategy_used=DistributionStrategy.ADAPTIVE,
                confidence_score=agent_scores[selected_agent_id],
                reasoning=f"Adaptive distribution (score={agent_scores[selected_agent_id]:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
                distribution_metrics={
                    "adaptive_score": agent_scores[selected_agent_id],
                    "historical_performance": agent_performance.get(
                        selected_agent_id, 0.5
                    ),
                    "capability_score": capability_score if requirements else 0.0,
                    "load_score": load_score,
                },
            )

        except Exception as e:
            logger.error(f"Adaptive distribution failed: {e}")
            raise

    def _calculate_historical_performance(self, agent_id: str) -> float:
        """Calculate historical performance for an agent"""
        try:
            # Analyze recent distribution decisions
            recent_decisions = [
                d
                for d in self.distribution_history
                if d.selected_agent_id == agent_id
                and d.created_at > datetime.now() - timedelta(days=7)
            ]

            if not recent_decisions:
                return 0.5  # Default score for new agents

            # Calculate average confidence score
            confidence_scores = [d.confidence_score for d in recent_decisions]
            return statistics.mean(confidence_scores) if confidence_scores else 0.5

        except Exception as e:
            logger.error(
                f"Failed to calculate historical performance for {agent_id}: {e}"
            )
            return 0.5

    async def _fallback_distribution(
        self, task_id: str, available_agents: List[str]
    ) -> DistributionDecision:
        """Fallback distribution when other strategies fail"""
        if not available_agents:
            raise ValueError("No agents available for distribution")

        selected_agent_id = available_agents[0]
        capability = self.agent_capabilities.get(selected_agent_id)

        return DistributionDecision(
            task_id=task_id,
            selected_agent_id=selected_agent_id,
            strategy_used=DistributionStrategy.ROUND_ROBIN,
            confidence_score=0.3,
            reasoning="Fallback distribution",
            alternative_agents=available_agents[1:],
            distribution_metrics={
                "fallback": True,
                "load_ratio": (
                    capability.current_load / capability.max_capacity
                    if capability
                    else 0.0
                ),
            },
        )

    def _update_strategy_performance(
        self, strategy: str, confidence_score: float
    ) -> None:
        """Update strategy performance metrics"""
        try:
            metrics = self.strategy_performance[strategy]
            metrics["total_decisions"] += 1

            # Update average confidence
            current_avg = metrics["average_confidence"]
            total_decisions = metrics["total_decisions"]
            metrics["average_confidence"] = (
                (current_avg * (total_decisions - 1)) + confidence_score
            ) / total_decisions

        except Exception as e:
            logger.error(f"Failed to update strategy performance: {e}")

    async def get_distribution_metrics(self) -> Dict[str, Any]:
        """Get distribution performance metrics"""
        try:
            return {
                "total_decisions": len(self.distribution_history),
                "strategy_performance": dict(self.strategy_performance),
                "current_strategy": self.current_strategy.value,
                "registered_agents": len(self.agent_capabilities),
                "recent_decisions": len(
                    [
                        d
                        for d in self.distribution_history
                        if d.created_at > datetime.now() - timedelta(hours=1)
                    ]
                ),
            }

        except Exception as e:
            logger.error(f"Failed to get distribution metrics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup old distribution data"""
        try:
            cutoff_time = datetime.now() - timedelta(
                days=self.config.get("performance", {}).get(
                    "metrics_retention_days", 30
                )
            )

            # Cleanup distribution history
            self.distribution_history = deque(
                [d for d in self.distribution_history if d.created_at > cutoff_time],
                maxlen=1000,
            )

            logger.info("TaskDistribution cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup TaskDistribution: {e}")
