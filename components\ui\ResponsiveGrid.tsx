import React, { ReactNode } from 'react';

export interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  autoFit?: boolean;
  autoFill?: boolean;
  minWidth?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  cols = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 'md',
  autoFit = false,
  autoFill = false,
  minWidth = '250px',
}) => {
  const gapClasses = {
    none: '',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  const getGridCols = () => {
    if (autoFit) {
      return `grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-1`;
    }
    if (autoFill) {
      return `grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-1`;
    }

    const colClasses = [
      'grid-cols-1',
      cols.sm && cols.sm > 1 ? `sm:grid-cols-${cols.sm}` : '',
      cols.md && cols.md > 1 ? `md:grid-cols-${cols.md}` : '',
      cols.lg && cols.lg > 1 ? `lg:grid-cols-${cols.lg}` : '',
      cols.xl && cols.xl > 1 ? `xl:grid-cols-${cols.xl}` : '',
      cols['2xl'] && cols['2xl'] > 1 ? `2xl:grid-cols-${cols['2xl']}` : '',
    ].filter(Boolean);

    return colClasses.join(' ');
  };

  const gridClasses = [
    'grid',
    getGridCols(),
    gapClasses[gap],
    className,
  ].filter(Boolean).join(' ');

  const style: React.CSSProperties = {};
  if (autoFit) {
    style.gridTemplateColumns = `repeat(auto-fit, minmax(${minWidth}, 1fr))`;
  } else if (autoFill) {
    style.gridTemplateColumns = `repeat(auto-fill, minmax(${minWidth}, 1fr))`;
  }

  return (
    <div className={gridClasses} style={style}>
      {children}
    </div>
  );
};

// Predefined grid layouts
export const CardGrid: React.FC<ResponsiveGridProps> = (props) => (
  <ResponsiveGrid
    cols={{ sm: 1, md: 2, lg: 3, xl: 4 }}
    gap="lg"
    {...props}
  />
);

export const ListGrid: React.FC<ResponsiveGridProps> = (props) => (
  <ResponsiveGrid
    cols={{ sm: 1, md: 1, lg: 2, xl: 3 }}
    gap="md"
    {...props}
  />
);

export const AutoGrid: React.FC<ResponsiveGridProps> = (props) => (
  <ResponsiveGrid
    autoFit
    minWidth="300px"
    gap="md"
    {...props}
  />
);
