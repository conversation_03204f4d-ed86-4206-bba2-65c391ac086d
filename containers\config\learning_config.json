{"update_interval_minutes": 60, "max_items_per_source": 100, "learning_threshold": 0.7, "auto_apply_critical": true, "notification_enabled": true, "sources": {"security": [{"name": "NVD CVE Database", "url": "https://nvd.nist.gov/vuln/data-feeds", "type": "api", "category": "security", "update_frequency": 30, "enabled": true}, {"name": "GitHub Security Advisories", "url": "https://api.github.com/repos/{owner}/{repo}/security-advisories", "type": "api", "category": "security", "update_frequency": 60, "enabled": true}, {"name": "OWASP Security Blog", "url": "https://owasp.org/feed/", "type": "rss", "category": "security", "update_frequency": 120, "enabled": true}, {"name": "Snyk Vulnerability Database", "url": "https://snyk.io/vuln/", "type": "api", "category": "security", "update_frequency": 60, "enabled": true}, {"name": "CVE Database", "url": "https://cve.mitre.org/data/downloads/", "type": "api", "category": "security", "update_frequency": 120, "enabled": true}], "ui_libraries": [{"name": "React Blog", "url": "https://react.dev/blog/rss.xml", "type": "rss", "category": "ui_library", "update_frequency": 240, "enabled": true}, {"name": "Vue.js Blog", "url": "https://blog.vuejs.org/feed.xml", "type": "rss", "category": "ui_library", "update_frequency": 240, "enabled": true}, {"name": "Angular Blog", "url": "https://blog.angular.io/feed", "type": "rss", "category": "ui_library", "update_frequency": 240, "enabled": true}, {"name": "NPM Security Advisories", "url": "https://registry.npmjs.org/-/npm/v1/security/advisories", "type": "api", "category": "ui_library", "update_frequency": 60, "enabled": true}, {"name": "Svelte Blog", "url": "https://svelte.dev/blog/rss.xml", "type": "rss", "category": "ui_library", "update_frequency": 360, "enabled": true}, {"name": "Next.js Blog", "url": "https://nextjs.org/feed.xml", "type": "rss", "category": "ui_library", "update_frequency": 240, "enabled": true}, {"name": "Nuxt.js Blog", "url": "https://nuxt.com/blog/rss.xml", "type": "rss", "category": "ui_library", "update_frequency": 360, "enabled": true}], "languages": [{"name": "Python Blog", "url": "https://blog.python.org/feeds/posts/default", "type": "rss", "category": "language", "update_frequency": 360, "enabled": true}, {"name": "Node.js Blog", "url": "https://nodejs.org/en/feed/blog.xml", "type": "rss", "category": "language", "update_frequency": 360, "enabled": true}, {"name": "TypeScript Blog", "url": "https://devblogs.microsoft.com/typescript/feed/", "type": "rss", "category": "language", "update_frequency": 360, "enabled": true}, {"name": "JavaScript Weekly", "url": "https://javascriptweekly.com/rss/", "type": "rss", "category": "language", "update_frequency": 720, "enabled": true}, {"name": "Python Weekly", "url": "https://www.pythonweekly.com/rss/", "type": "rss", "category": "language", "update_frequency": 720, "enabled": true}], "frameworks": [{"name": "Django Blog", "url": "https://www.djangoproject.com/weblog/feed/", "type": "rss", "category": "framework", "update_frequency": 360, "enabled": true}, {"name": "Flask Blog", "url": "https://flask.palletsprojects.com/blog/feed.xml", "type": "rss", "category": "framework", "update_frequency": 720, "enabled": true}, {"name": "Express.js Blog", "url": "https://expressjs.com/blog/feed.xml", "type": "rss", "category": "framework", "update_frequency": 720, "enabled": true}, {"name": "FastAPI Blog", "url": "https://fastapi.tiangolo.com/blog/feed.xml", "type": "rss", "category": "framework", "update_frequency": 720, "enabled": true}], "tools": [{"name": "ESLint Blog", "url": "https://eslint.org/blog/feed.xml", "type": "rss", "category": "tools", "update_frequency": 720, "enabled": true}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://prettier.io/blog/feed.xml", "type": "rss", "category": "tools", "update_frequency": 720, "enabled": true}, {"name": "Webpack Blog", "url": "https://webpack.js.org/blog/feed.xml", "type": "rss", "category": "tools", "update_frequency": 720, "enabled": true}]}, "learning_patterns": {"security": ["CVE-\\d{4}-\\d{4,7}", "vulnerability:xss", "vulnerability:sql_injection", "vulnerability:csrf", "vulnerability:rce", "affected:react", "affected:vue", "affected:angular", "affected:django", "affected:flask"], "ui_library": ["framework:react", "framework:vue", "framework:angular", "framework:svelte", "version:\\d+\\.\\d+\\.\\d+", "feature:hooks", "feature:composables", "feature:directives"], "language": ["language:python", "language:javascript", "language:typescript", "syntax:async_await", "syntax:generators", "syntax:decorators", "syntax:type_hints"]}, "knowledge_base": {"security": {"vulnerability_patterns": ["cross-site scripting", "sql injection", "cross-site request forgery", "remote code execution", "privilege escalation", "authentication bypass"], "mitigation_strategies": ["input validation", "output encoding", "parameterized queries", "csrf tokens", "content security policy", "secure headers"]}, "ui_library": {"best_practices": ["component composition", "state management", "performance optimization", "accessibility", "responsive design", "testing strategies"], "common_patterns": ["custom hooks", "higher-order components", "render props", "context providers", "error boundaries"]}, "language": {"modern_features": ["async/await", "generators", "decorators", "type hints", "pattern matching", "structural typing"], "best_practices": ["code organization", "error handling", "performance optimization", "testing", "documentation"]}}, "notification_settings": {"critical_severity": {"email": true, "slack": true, "webhook": true, "immediate": true}, "high_severity": {"email": true, "slack": true, "webhook": false, "immediate": false}, "medium_severity": {"email": false, "slack": true, "webhook": false, "immediate": false}, "low_severity": {"email": false, "slack": false, "webhook": false, "immediate": false}}, "auto_update_settings": {"security_rules": {"enabled": true, "update_patterns": true, "update_scanners": true, "update_dependencies": true}, "code_generation": {"enabled": true, "update_templates": true, "update_best_practices": true, "update_libraries": true}, "linting_rules": {"enabled": true, "update_eslint": true, "update_prettier": true, "update_custom_rules": true}}, "integration_settings": {"github": {"enabled": true, "api_key": null, "repositories": ["facebook/react", "vuejs/vue", "angular/angular", "microsoft/TypeScript", "python/cpython", "nodejs/node"]}, "npm": {"enabled": true, "packages": ["react", "vue", "angular", "typescript", "express", "next", "nuxt"]}, "pypi": {"enabled": true, "packages": ["django", "flask", "<PERSON><PERSON><PERSON>", "requests", "urllib3", "cryptography"]}}, "performance_settings": {"max_concurrent_requests": 10, "request_timeout": 30, "retry_attempts": 3, "cache_duration": 3600, "max_database_size": "1GB"}}