describe('IDE Error & Success Feedback', () => {
  beforeEach(() => {
    cy.visit('/')
    // Wait for the IDE to load
    cy.waitForComponent('ide-layout')
  })

  describe('Site Validation Feedback', () => {
    it('shows error toast when site validation fails', () => {
      // Simulate site validation error by intercepting the API call
      cy.simulateServerError('/api/v1/sites/validate', 500)

      cy.get('[data-testid="sidebar-site-validate"]').first().click()
      cy.expectErrorToast(/validation failed|error validating site/i)
    })

    it('shows success toast when site validation succeeds', () => {
      // Mock successful validation response
      cy.intercept('POST', '/api/v1/sites/validate', {
        statusCode: 200,
        body: { success: true, message: 'Site validated successfully' }
      }).as('validateSuccess')

      cy.get('[data-testid="sidebar-site-validate"]').first().click()
      cy.wait('@validateSuccess')
      cy.expectSuccessToast(/validated successfully|validation complete/i)
    })

    it('shows error toast when manifest load fails', () => {
      cy.simulateServerError('/api/v1/sites/manifest', 404)

      cy.get('[data-testid="sidebar-site-manifest"]').first().click()
      cy.expectErrorToast(/failed to load manifest|manifest not found/i)
    })
  })

  describe('Error Detection Feedback', () => {
    it('shows error toast for error detection scan failure', () => {
      cy.simulateServerError('/api/v1/errors/scan', 500)

      cy.runErrorScan()
      cy.expectErrorToast(/error scanning project|scan failed/i)
    })

    it('shows success toast for error detection scan', () => {
      cy.intercept('POST', '/api/v1/errors/scan', {
        statusCode: 200,
        body: { success: true, errors: [], message: 'Scan complete' }
      }).as('scanSuccess')

      cy.runErrorScan()
      cy.wait('@scanSuccess')
      cy.expectSuccessToast(/scan complete|scan successful/i)
    })

    it('shows error toast for auto-fix failure', () => {
      cy.simulateServerError('/api/v1/errors/auto-fix', 500)

      cy.get('[data-testid="error-detection-auto-fix"]').click()
      cy.expectErrorToast(/auto-fix failed|fix error/i)
    })

    it('shows success toast for auto-fix', () => {
      cy.intercept('POST', '/api/v1/errors/auto-fix', {
        statusCode: 200,
        body: { success: true, fixed: 3, message: 'Auto-fix completed' }
      }).as('autoFixSuccess')

      cy.get('[data-testid="error-detection-auto-fix"]').click()
      cy.wait('@autoFixSuccess')
      cy.expectSuccessToast(/auto-fix completed|fixes applied/i)
    })
  })

  describe('Chat & AI Feedback', () => {
    it('shows error toast for prompt enhancement failure', () => {
      cy.simulateServerError('/api/v1/prompt/enhance', 500)

      cy.enhancePrompt('Enhance this prompt')
      cy.expectErrorToast(/enhancement error|failed to enhance/i)
    })

    it('shows error toast for AI chat failure', () => {
      cy.simulateNetworkError()

      cy.sendChatMessage('Hello AI')
      cy.expectErrorToast(/ai response error|ai unavailable/i)
    })

    it('shows timeout message for slow AI responses', () => {
      cy.simulateSlowNetwork(12000) // 12 second delay

      cy.sendChatMessage('Hello AI')
      cy.get('[data-testid="chat-messages"]').should('contain', 'AI is unavailable')
    })

    it('shows success for AI response', () => {
      cy.intercept('POST', '/api/v1/chat', {
        statusCode: 200,
        body: {
          success: true,
          response: {
            content: '[AI] Hello! How can I help you today?',
            model: 'deepseek-coder:1.3b'
          }
        }
      }).as('chatSuccess')

      cy.sendChatMessage('Hello AI')
      cy.wait('@chatSuccess')
      cy.waitForAIResponse()
    })
  })

  describe('File Operations Feedback', () => {
    it('shows error toast for file upload failure', () => {
      cy.simulateServerError('/api/v1/files/upload', 413) // File too large

      cy.uploadFile('large-file.html')
      cy.expectErrorToast(/error uploading file|upload failed/i)
    })

    it('shows success toast for file upload', () => {
      cy.intercept('POST', '/api/v1/files/upload', {
        statusCode: 200,
        body: { success: true, filename: 'index.html', message: 'File uploaded successfully' }
      }).as('uploadSuccess')

      cy.uploadFile('index.html')
      cy.wait('@uploadSuccess')
      cy.expectSuccessToast(/uploaded index.html|upload successful/i)
    })

    it('shows error toast for file save failure', () => {
      cy.simulateServerError('/api/v1/files/save', 500)

      cy.get('[data-testid="code-editor"]').type('console.log("test")')
      cy.get('[data-testid="save-button"]').click()
      cy.expectErrorToast(/error saving file|save failed/i)
    })

    it('shows success toast for file save', () => {
      cy.intercept('POST', '/api/v1/files/save', {
        statusCode: 200,
        body: { success: true, message: 'File saved successfully' }
      }).as('saveSuccess')

      cy.get('[data-testid="code-editor"]').type('console.log("test")')
      cy.get('[data-testid="save-button"]').click()
      cy.wait('@saveSuccess')
      cy.expectSuccessToast(/file saved|save successful/i)
    })
  })

  describe('Model Health Feedback', () => {
    it('shows error toast for model health test failure', () => {
      cy.simulateServerError('/api/v1/models/test', 500)

      cy.testModelHealth('deepseek-coder')
      cy.expectErrorToast(/failed to test model|test error/i)
    })

    it('shows success toast for model health test', () => {
      cy.intercept('POST', '/api/v1/models/test', {
        statusCode: 200,
        body: { success: true, model: 'deepseek-coder', status: 'healthy' }
      }).as('modelTestSuccess')

      cy.testModelHealth('deepseek-coder')
      cy.wait('@modelTestSuccess')
      cy.expectSuccessToast(/test successful|model healthy/i)
    })

    it('shows error toast for cache clear failure', () => {
      cy.simulateServerError('/api/v1/models/cache/clear', 500)

      cy.get('[data-testid="model-cache-clear"]').click()
      cy.expectErrorToast(/failed to clear cache|cache error/i)
    })

    it('shows success toast for cache clear', () => {
      cy.intercept('POST', '/api/v1/models/cache/clear', {
        statusCode: 200,
        body: { success: true, message: 'Cache cleared successfully' }
      }).as('cacheClearSuccess')

      cy.get('[data-testid="model-cache-clear"]').click()
      cy.wait('@cacheClearSuccess')
      cy.expectSuccessToast(/cache cleared|clear successful/i)
    })
  })

  describe('Performance & GPU Feedback', () => {
    it('shows error toast for GPU monitor fetch failure', () => {
      cy.simulateServerError('/api/v1/performance/gpu', 500)

      cy.get('[data-testid="gpu-monitor"]').should('be.visible')
      cy.expectErrorToast(/error fetching gpu|gpu error/i)
    })

    it('shows error toast for performance fetch failure', () => {
      cy.simulateServerError('/api/v1/performance/stats', 500)

      cy.get('[data-testid="performance-panel"]').click()
      cy.expectErrorToast(/error fetching performance|performance error/i)
    })
  })

  describe('Documentation Feedback', () => {
    it('shows error toast for documentation generation failure', () => {
      cy.simulateServerError('/api/v1/documentation/generate', 500)

      cy.get('[data-testid="documentation-generate"]').click()
      cy.expectErrorToast(/error generating documentation|generation failed/i)
    })

    it('shows success toast for documentation generation', () => {
      cy.intercept('POST', '/api/v1/documentation/generate', {
        statusCode: 200,
        body: { success: true, message: 'Documentation generated successfully' }
      }).as('docGenSuccess')

      cy.get('[data-testid="documentation-generate"]').click()
      cy.wait('@docGenSuccess')
      cy.expectSuccessToast(/documentation generated|generation complete/i)
    })
  })

  describe('Accessibility & User Experience', () => {
    it('supports keyboard navigation for error panels', () => {
      cy.get('[data-testid="error-detection-panel"]').focus()
      cy.get('[data-testid="error-detection-panel"]').should('have.attr', 'tabindex')

      // Test keyboard navigation
      cy.get('[data-testid="error-detection-panel"]').type('{enter}')
      cy.get('[data-testid="error-detection-scan"]').should('be.focused')
    })

    it('has proper ARIA labels for error states', () => {
      cy.get('[data-testid="error-detection-panel"]').should('have.attr', 'aria-label')
      cy.get('[data-testid="chat-input"]').should('have.attr', 'aria-label')
      cy.get('[data-testid="file-upload"]').should('have.attr', 'aria-label')
    })

    it('shows loading states during async operations', () => {
      cy.simulateSlowNetwork(2000)

      cy.sendChatMessage('Test message')
      cy.get('[data-testid="chat-send"]').should('have.class', 'loading')

      // Wait for operation to complete
      cy.waitForLoading('[data-testid="chat-send"]')
    })
  })

  describe('Error Recovery & Resilience', () => {
    it('recovers from network errors gracefully', () => {
      // Simulate network error then recovery
      cy.simulateNetworkError()
      cy.sendChatMessage('Test message')
      cy.expectErrorToast(/ai response error/i)

      // Clear the error and try again
      cy.clearToasts()
      cy.sendChatMessage('Test message 2')
      cy.expectErrorToast(/ai response error/i)
    })

    it('handles multiple rapid user interactions', () => {
      // Rapid clicking should not cause errors
      cy.get('[data-testid="error-detection-scan"]').click()
      cy.get('[data-testid="error-detection-scan"]').click()
      cy.get('[data-testid="error-detection-scan"]').click()

      // Should handle gracefully without multiple error toasts
      cy.get('.react-hot-toast').should('have.length.at.most', 2)
    })

    it('provides fallback UI when components fail to load', () => {
      // Simulate component load failure
      cy.intercept('GET', '/api/v1/components/status', { statusCode: 500 }).as('componentError')

      cy.reload()
      cy.wait('@componentError')

      // Should show fallback UI instead of blank screen
      cy.get('[data-testid="fallback-ui"]').should('be.visible')
    })
  })

  describe('Toast Management', () => {
    it('auto-dismisses toasts after timeout', () => {
      cy.get('[data-testid="sidebar-site-validate"]').first().click()
      cy.expectErrorToast(/validation failed/i)

      // Wait for toast to auto-dismiss (usually 4-5 seconds)
      cy.wait(6000)
      cy.get('.react-hot-toast').should('not.exist')
    })

    it('allows manual toast dismissal', () => {
      cy.get('[data-testid="sidebar-site-validate"]').first().click()
      cy.expectErrorToast(/validation failed/i)

      cy.get('[data-testid="toast-close"]').first().click()
      cy.get('.react-hot-toast').should('not.exist')
    })

    it('stacks multiple toasts properly', () => {
      // Trigger multiple operations quickly
      cy.get('[data-testid="error-detection-scan"]').click()
      cy.get('[data-testid="sidebar-site-validate"]').first().click()
      cy.get('[data-testid="model-cache-clear"]').click()

      // Should show multiple toasts stacked
      cy.get('.react-hot-toast').should('have.length.at.least', 2)
    })
  })
})
