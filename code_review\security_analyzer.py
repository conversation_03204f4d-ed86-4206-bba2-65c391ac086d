"""
Security Analyzer

Comprehensive security analysis engine for detecting vulnerabilities,
security best practices violations, and compliance issues.

Phase 20 Implementation - Advanced Code Review
"""

import ast
import asyncio
import logging
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class SecurityVulnerability:
    """Represents a detected security vulnerability."""

    id: str
    type: str  # "injection", "authentication", "authorization", "data_exposure", "compliance"
    severity: str  # "critical", "high", "medium", "low"
    title: str
    description: str
    line_number: int
    column: int
    code_snippet: str
    language: str
    category: str
    fix_suggestion: Optional[str] = None
    confidence: float = 0.8
    cwe_id: Optional[str] = None


@dataclass
class SecurityAnalysisResult:
    """Result of security analysis."""

    security_score: float
    vulnerabilities: List[SecurityVulnerability]
    risk_level: str
    compliance_issues: List[Dict[str, Any]]
    recommendations: List[str]
    confidence: float


class SecurityAnalyzer:
    """
    Security analyzer for detecting vulnerabilities and security issues.

    This class performs comprehensive security analysis including
    vulnerability detection, compliance checking, and best practices validation.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the security analyzer.

        Args:
            config: Configuration dictionary for security analysis
        """
        self.config = config or {}
        self.vulnerability_patterns = self._load_vulnerability_patterns()
        self.compliance_rules = self._load_compliance_rules()

        logger.info("Security Analyzer initialized successfully")

    async def analyze_security(
        self, code: str, language: str, file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive security analysis.

        Args:
            code: Code to analyze
            language: Programming language
            file_path: Optional file path for context

        Returns:
            Dictionary with security analysis results
        """
        try:
            logger.info(f"Starting security analysis for {language} file: {file_path}")

            # Detect vulnerabilities
            vulnerabilities = await self._detect_vulnerabilities(
                code, language, file_path
            )

            # Check compliance
            compliance_issues = await self._check_compliance(code, language, file_path)

            # Calculate security score
            security_score = self._calculate_security_score(
                vulnerabilities, compliance_issues
            )

            # Determine risk level
            risk_level = self._determine_risk_level(vulnerabilities)

            # Generate recommendations
            recommendations = self._generate_security_recommendations(
                vulnerabilities, compliance_issues
            )

            # Calculate confidence
            confidence = self._calculate_confidence(vulnerabilities, compliance_issues)

            return {
                "security_score": security_score,
                "vulnerabilities": [
                    self._vulnerability_to_dict(v) for v in vulnerabilities
                ],
                "risk_level": risk_level,
                "compliance_issues": compliance_issues,
                "recommendations": recommendations,
                "confidence": confidence,
                "total_vulnerabilities": len(vulnerabilities),
                "critical_count": len(
                    [v for v in vulnerabilities if v.severity == "critical"]
                ),
                "high_count": len([v for v in vulnerabilities if v.severity == "high"]),
                "medium_count": len(
                    [v for v in vulnerabilities if v.severity == "medium"]
                ),
                "low_count": len([v for v in vulnerabilities if v.severity == "low"]),
            }

        except Exception as e:
            logger.error(f"Error in security analysis: {e}")
            return {
                "security_score": 0.0,
                "vulnerabilities": [],
                "risk_level": "unknown",
                "compliance_issues": [],
                "recommendations": [],
                "confidence": 0.0,
                "total_vulnerabilities": 0,
                "critical_count": 0,
                "high_count": 0,
                "medium_count": 0,
                "low_count": 0,
            }

    async def _detect_vulnerabilities(
        self, code: str, language: str, file_path: Optional[str]
    ) -> List[SecurityVulnerability]:
        """Detect security vulnerabilities in the code."""
        vulnerabilities = []
        lines = code.split("\n")

        try:
            # Language-specific vulnerability detection
            if language == "python":
                vulnerabilities.extend(
                    await self._detect_python_vulnerabilities(code, lines)
                )
            elif language in ["javascript", "typescript"]:
                vulnerabilities.extend(
                    await self._detect_javascript_vulnerabilities(code, lines, language)
                )
            elif language == "java":
                vulnerabilities.extend(
                    await self._detect_java_vulnerabilities(code, lines)
                )
            elif language == "cpp":
                vulnerabilities.extend(
                    await self._detect_cpp_vulnerabilities(code, lines)
                )

            # Generic vulnerability detection
            vulnerabilities.extend(
                await self._detect_generic_vulnerabilities(code, lines, language)
            )

        except Exception as e:
            logger.error(f"Error detecting vulnerabilities: {e}")

        return vulnerabilities

    async def _detect_python_vulnerabilities(
        self, code: str, lines: List[str]
    ) -> List[SecurityVulnerability]:
        """Detect Python-specific security vulnerabilities."""
        vulnerabilities = []

        try:
            # Parse the code
            tree = ast.parse(code)

            # Check for common Python vulnerabilities
            for node in ast.walk(tree):
                vulnerabilities.extend(self._check_python_node_security(node, lines))

            # Check for hardcoded secrets
            vulnerabilities.extend(self._check_hardcoded_secrets(code, lines))

            # Check for unsafe eval/exec usage
            vulnerabilities.extend(self._check_unsafe_execution(code, lines))

        except SyntaxError:
            # Handle syntax errors
            vulnerability = SecurityVulnerability(
                id="syntax_error",
                type="error",
                severity="medium",
                title="Syntax Error",
                description="Code contains syntax errors that may indicate security issues",
                line_number=1,
                column=1,
                code_snippet=lines[0] if lines else "",
                language="python",
                category="syntax",
                fix_suggestion="Fix syntax errors to ensure code security",
            )
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    async def _detect_javascript_vulnerabilities(
        self, code: str, lines: List[str], language: str
    ) -> List[SecurityVulnerability]:
        """Detect JavaScript/TypeScript-specific security vulnerabilities."""
        vulnerabilities = []

        # Check for XSS vulnerabilities
        if re.search(r"innerHTML\s*=", code, re.IGNORECASE):
            vulnerability = SecurityVulnerability(
                id="xss_innerhtml",
                type="injection",
                severity="high",
                title="Potential XSS via innerHTML",
                description="Using innerHTML can lead to XSS attacks",
                line_number=self._find_line_number(code, "innerHTML"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "innerHTML"),
                language=language,
                category="xss",
                fix_suggestion="Use textContent or proper sanitization",
                cwe_id="CWE-79",
            )
            vulnerabilities.append(vulnerability)

        # Check for eval usage
        if re.search(r"\beval\s*\(", code, re.IGNORECASE):
            vulnerability = SecurityVulnerability(
                id="unsafe_eval",
                type="injection",
                severity="critical",
                title="Unsafe eval() usage",
                description="eval() can execute arbitrary code and is a security risk",
                line_number=self._find_line_number(code, "eval"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "eval"),
                language=language,
                category="code_injection",
                fix_suggestion="Avoid eval() and use safer alternatives",
                cwe_id="CWE-95",
            )
            vulnerabilities.append(vulnerability)

        # Check for hardcoded secrets
        vulnerabilities.extend(self._check_hardcoded_secrets(code, lines))

        return vulnerabilities

    async def _detect_java_vulnerabilities(
        self, code: str, lines: List[str]
    ) -> List[SecurityVulnerability]:
        """Detect Java-specific security vulnerabilities."""
        vulnerabilities = []

        # Check for SQL injection
        if re.search(r"executeQuery\s*\(.*\+", code):
            vulnerability = SecurityVulnerability(
                id="sql_injection",
                type="injection",
                severity="critical",
                title="Potential SQL Injection",
                description="String concatenation in SQL queries can lead to injection attacks",
                line_number=self._find_line_number(code, "executeQuery"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "executeQuery"),
                language="java",
                category="sql_injection",
                fix_suggestion="Use PreparedStatement with parameterized queries",
                cwe_id="CWE-89",
            )
            vulnerabilities.append(vulnerability)

        # Check for hardcoded secrets
        vulnerabilities.extend(self._check_hardcoded_secrets(code, lines))

        return vulnerabilities

    async def _detect_cpp_vulnerabilities(
        self, code: str, lines: List[str]
    ) -> List[SecurityVulnerability]:
        """Detect C++-specific security vulnerabilities."""
        vulnerabilities = []

        # Check for buffer overflow
        if re.search(r"strcpy\s*\(", code, re.IGNORECASE):
            vulnerability = SecurityVulnerability(
                id="buffer_overflow",
                type="memory",
                severity="critical",
                title="Potential Buffer Overflow",
                description="strcpy() can cause buffer overflow vulnerabilities",
                line_number=self._find_line_number(code, "strcpy"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "strcpy"),
                language="cpp",
                category="buffer_overflow",
                fix_suggestion="Use strncpy() or std::string for safer string handling",
                cwe_id="CWE-120",
            )
            vulnerabilities.append(vulnerability)

        # Check for hardcoded secrets
        vulnerabilities.extend(self._check_hardcoded_secrets(code, lines))

        return vulnerabilities

    async def _detect_generic_vulnerabilities(
        self, code: str, lines: List[str], language: str
    ) -> List[SecurityVulnerability]:
        """Detect generic security vulnerabilities."""
        vulnerabilities = []

        # Check for hardcoded secrets
        vulnerabilities.extend(self._check_hardcoded_secrets(code, lines))

        # Check for weak encryption
        if re.search(r"MD5|SHA1", code, re.IGNORECASE):
            vulnerability = SecurityVulnerability(
                id="weak_encryption",
                type="cryptography",
                severity="medium",
                title="Weak Cryptographic Algorithm",
                description="MD5 and SHA1 are cryptographically broken",
                line_number=self._find_line_number(code, "MD5|SHA1"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "MD5|SHA1"),
                language=language,
                category="weak_crypto",
                fix_suggestion="Use SHA-256 or stronger algorithms",
                cwe_id="CWE-327",
            )
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    def _check_python_node_security(
        self, node: ast.AST, lines: List[str]
    ) -> List[SecurityVulnerability]:
        """Check Python AST node for security issues."""
        vulnerabilities = []

        # Check for eval usage
        if (
            isinstance(node, ast.Call)
            and isinstance(node.func, ast.Name)
            and node.func.id == "eval"
        ):
            vulnerability = SecurityVulnerability(
                id="unsafe_eval",
                type="injection",
                severity="critical",
                title="Unsafe eval() usage",
                description="eval() can execute arbitrary code and is a security risk",
                line_number=getattr(node, "lineno", 1),
                column=getattr(node, "col_offset", 1),
                code_snippet=(
                    lines[getattr(node, "lineno", 1) - 1]
                    if getattr(node, "lineno", 1) <= len(lines)
                    else ""
                ),
                language="python",
                category="code_injection",
                fix_suggestion="Avoid eval() and use safer alternatives",
                cwe_id="CWE-95",
            )
            vulnerabilities.append(vulnerability)

        # Check for exec usage
        if (
            isinstance(node, ast.Call)
            and isinstance(node.func, ast.Name)
            and node.func.id == "exec"
        ):
            vulnerability = SecurityVulnerability(
                id="unsafe_exec",
                type="injection",
                severity="critical",
                title="Unsafe exec() usage",
                description="exec() can execute arbitrary code and is a security risk",
                line_number=getattr(node, "lineno", 1),
                column=getattr(node, "col_offset", 1),
                code_snippet=(
                    lines[getattr(node, "lineno", 1) - 1]
                    if getattr(node, "lineno", 1) <= len(lines)
                    else ""
                ),
                language="python",
                category="code_injection",
                fix_suggestion="Avoid exec() and use safer alternatives",
                cwe_id="CWE-95",
            )
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    def _check_hardcoded_secrets(
        self, code: str, lines: List[str]
    ) -> List[SecurityVulnerability]:
        """Check for hardcoded secrets in the code."""
        vulnerabilities = []

        # Check for API keys
        if re.search(r'api_key\s*=\s*["\'][^"\']+["\']', code, re.IGNORECASE):
            vulnerability = SecurityVulnerability(
                id="hardcoded_api_key",
                type="data_exposure",
                severity="high",
                title="Hardcoded API Key",
                description="API keys should not be hardcoded in source code",
                line_number=self._find_line_number(code, "api_key"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "api_key"),
                language="generic",
                category="secret_exposure",
                fix_suggestion="Use environment variables or secure configuration management",
                cwe_id="CWE-259",
            )
            vulnerabilities.append(vulnerability)

        # Check for passwords
        if re.search(r'password\s*=\s*["\'][^"\']+["\']', code, re.IGNORECASE):
            vulnerability = SecurityVulnerability(
                id="hardcoded_password",
                type="data_exposure",
                severity="critical",
                title="Hardcoded Password",
                description="Passwords should not be hardcoded in source code",
                line_number=self._find_line_number(code, "password"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "password"),
                language="generic",
                category="secret_exposure",
                fix_suggestion="Use environment variables or secure password management",
                cwe_id="CWE-259",
            )
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    def _check_unsafe_execution(
        self, code: str, lines: List[str]
    ) -> List[SecurityVulnerability]:
        """Check for unsafe execution patterns."""
        vulnerabilities = []

        # Check for subprocess with shell=True
        if re.search(r"subprocess\.run.*shell\s*=\s*True", code):
            vulnerability = SecurityVulnerability(
                id="shell_injection",
                type="injection",
                severity="high",
                title="Potential Shell Injection",
                description="Using shell=True can lead to command injection",
                line_number=self._find_line_number(code, "shell=True"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "shell=True"),
                language="python",
                category="command_injection",
                fix_suggestion="Avoid shell=True and use list arguments",
                cwe_id="CWE-78",
            )
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    async def _check_compliance(
        self, code: str, language: str, file_path: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Check for compliance issues."""
        compliance_issues = []

        # Check for GDPR compliance (PII handling)
        if re.search(r"email|phone|address|ssn|credit_card", code, re.IGNORECASE):
            compliance_issues.append(
                {
                    "type": "gdpr",
                    "severity": "medium",
                    "description": "Potential PII handling detected",
                    "recommendation": "Ensure proper data protection measures",
                }
            )

        # Check for logging of sensitive data
        if re.search(r"log.*password|log.*token|log.*key", code, re.IGNORECASE):
            compliance_issues.append(
                {
                    "type": "logging",
                    "severity": "high",
                    "description": "Potential logging of sensitive data",
                    "recommendation": "Avoid logging sensitive information",
                }
            )

        return compliance_issues

    def _calculate_security_score(
        self,
        vulnerabilities: List[SecurityVulnerability],
        compliance_issues: List[Dict[str, Any]],
    ) -> float:
        """Calculate security score based on vulnerabilities and compliance issues."""
        if not vulnerabilities and not compliance_issues:
            return 1.0

        # Calculate vulnerability penalty
        vulnerability_penalty = 0.0
        for vuln in vulnerabilities:
            if vuln.severity == "critical":
                vulnerability_penalty += 0.3
            elif vuln.severity == "high":
                vulnerability_penalty += 0.2
            elif vuln.severity == "medium":
                vulnerability_penalty += 0.1
            elif vuln.severity == "low":
                vulnerability_penalty += 0.05

        # Calculate compliance penalty
        compliance_penalty = len(compliance_issues) * 0.05

        # Calculate final score
        total_penalty = min(vulnerability_penalty + compliance_penalty, 1.0)
        return max(0.0, 1.0 - total_penalty)

    def _determine_risk_level(
        self, vulnerabilities: List[SecurityVulnerability]
    ) -> str:
        """Determine overall risk level based on vulnerabilities."""
        if any(v.severity == "critical" for v in vulnerabilities):
            return "critical"
        elif any(v.severity == "high" for v in vulnerabilities):
            return "high"
        elif any(v.severity == "medium" for v in vulnerabilities):
            return "medium"
        elif any(v.severity == "low" for v in vulnerabilities):
            return "low"
        else:
            return "none"

    def _generate_security_recommendations(
        self,
        vulnerabilities: List[SecurityVulnerability],
        compliance_issues: List[Dict[str, Any]],
    ) -> List[str]:
        """Generate security recommendations."""
        recommendations = []

        # Add vulnerability-specific recommendations
        for vuln in vulnerabilities:
            if vuln.fix_suggestion:
                recommendations.append(vuln.fix_suggestion)

        # Add compliance recommendations
        for issue in compliance_issues:
            if "recommendation" in issue:
                recommendations.append(issue["recommendation"])

        # Add general recommendations
        if vulnerabilities:
            recommendations.append("Implement regular security audits")
            recommendations.append("Use automated security scanning tools")

        return list(set(recommendations))  # Remove duplicates

    def _calculate_confidence(
        self,
        vulnerabilities: List[SecurityVulnerability],
        compliance_issues: List[Dict[str, Any]],
    ) -> float:
        """Calculate confidence in the security analysis."""
        if not vulnerabilities and not compliance_issues:
            return 0.9  # High confidence when no issues found

        # Calculate average confidence from vulnerabilities
        if vulnerabilities:
            avg_confidence = sum(v.confidence for v in vulnerabilities) / len(
                vulnerabilities
            )
            return min(0.95, avg_confidence)

        return 0.8

    def _vulnerability_to_dict(
        self, vulnerability: SecurityVulnerability
    ) -> Dict[str, Any]:
        """Convert SecurityVulnerability to dictionary."""
        return {
            "id": vulnerability.id,
            "type": vulnerability.type,
            "severity": vulnerability.severity,
            "title": vulnerability.title,
            "description": vulnerability.description,
            "line_number": vulnerability.line_number,
            "column": vulnerability.column,
            "code_snippet": vulnerability.code_snippet,
            "language": vulnerability.language,
            "category": vulnerability.category,
            "fix_suggestion": vulnerability.fix_suggestion,
            "confidence": vulnerability.confidence,
            "cwe_id": vulnerability.cwe_id,
        }

    def _find_line_number(self, code: str, pattern: str) -> int:
        """Find line number containing a pattern."""
        lines = code.split("\n")
        for i, line in enumerate(lines):
            if re.search(pattern, line, re.IGNORECASE):
                return i + 1
        return 1

    def _get_line_with_pattern(self, code: str, pattern: str) -> str:
        """Get the line containing a pattern."""
        lines = code.split("\n")
        for line in lines:
            if re.search(pattern, line, re.IGNORECASE):
                return line.strip()
        return ""

    def _load_vulnerability_patterns(self) -> Dict[str, List[str]]:
        """Load vulnerability detection patterns."""
        return {
            "sql_injection": [
                r"executeQuery\s*\(.*\+",
                r"execute\s*\(.*\+",
                r"query\s*=\s*.*\+",
            ],
            "xss": [r"innerHTML\s*=", r"document\.write\s*\(", r"eval\s*\("],
            "command_injection": [
                r"shell\s*=\s*True",
                r"os\.system\s*\(",
                r"subprocess\.call.*shell",
            ],
        }

    def _load_compliance_rules(self) -> Dict[str, List[str]]:
        """Load compliance checking rules."""
        return {
            "gdpr": [r"email", r"phone", r"address", r"ssn", r"credit_card"],
            "logging": [r"log.*password", r"log.*token", r"log.*key"],
        }

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the security analyzer."""
        return {
            "status": "healthy",
            "vulnerability_patterns_loaded": len(self.vulnerability_patterns),
            "compliance_rules_loaded": len(self.compliance_rules),
            "supported_languages": [
                "python",
                "javascript",
                "typescript",
                "java",
                "cpp",
            ],
        }
