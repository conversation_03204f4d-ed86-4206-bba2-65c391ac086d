"""
Resource Monitor

Monitors and manages system resources during complex task execution,
including CPU, memory, storage, network, and GPU utilization.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import psutil

from complex_tasks.models import ResourceAllocation
from utils.logger import get_logger

sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

logger = get_logger(__name__)


class ResourceMonitor:
    """
    System resource monitor for complex task execution.

    Monitors and manages:
    - CPU utilization and cores
    - Memory usage and allocation
    - Storage space and I/O
    - Network bandwidth and latency
    - GPU utilization (if available)
    - Process monitoring
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the resource monitor"""
        self.config = config
        self.monitoring_interval = config.get("monitoring_interval", 5)  # seconds
        self.history_size = config.get("history_size", 1000)
        self.alert_thresholds = config.get(
            "alert_thresholds",
            {
                "cpu_usage": 90.0,
                "memory_usage": 85.0,
                "disk_usage": 90.0,
                "network_latency": 100.0,  # ms
            },
        )

        # Resource history
        self.cpu_history: List[Dict[str, Any]] = []
        self.memory_history: List[Dict[str, Any]] = []
        self.disk_history: List[Dict[str, Any]] = []
        self.network_history: List[Dict[str, Any]] = []
        self.gpu_history: List[Dict[str, Any]] = []

        # Monitoring state
        self.is_monitoring = False
        self.monitoring_task: Optional[asyncio.Task] = None

        # Alerts
        self.alerts: List[Dict[str, Any]] = []

        # Task progress tracking
        self.tasks_progress: Dict[str, Any] = {}

        logger.info("Resource Monitor initialized")

    async def start_monitoring(self) -> None:
        """Start resource monitoring"""
        if self.is_monitoring:
            logger.warning("Resource monitoring already started")
            return

        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Resource monitoring started")

    async def stop_monitoring(self) -> None:
        """Stop resource monitoring"""
        if not self.is_monitoring:
            logger.warning("Resource monitoring not started")
            return

        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info("Resource monitoring stopped")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect current resource usage
                current_usage = await self._collect_resource_usage()

                # Store in history
                await self._store_usage_history(current_usage)

                # Check for alerts
                await self._check_alerts(current_usage)

                # Wait for next monitoring interval
                await asyncio.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_interval)

    async def _collect_resource_usage(self) -> Dict[str, Any]:
        """Collect current resource usage"""
        timestamp = datetime.now()

        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()

        # Memory usage
        memory = psutil.virtual_memory()

        # Disk usage
        disk = psutil.disk_usage("/")
        disk_io = psutil.disk_io_counters()

        # Network usage
        network = psutil.net_io_counters()
        network_connections = len(psutil.net_connections())

        # GPU usage (if available)
        gpu_usage = await self._get_gpu_usage()

        return {
            "timestamp": timestamp,
            "cpu": {
                "usage_percent": cpu_percent,
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None,
                "load_average": (
                    psutil.getloadavg() if hasattr(psutil, "getloadavg") else None
                ),
            },
            "memory": {
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3),
                "used_gb": memory.used / (1024**3),
                "usage_percent": memory.percent,
                "swap_total_gb": psutil.swap_memory().total / (1024**3),
                "swap_used_gb": psutil.swap_memory().used / (1024**3),
            },
            "disk": {
                "total_gb": disk.total / (1024**3),
                "used_gb": disk.used / (1024**3),
                "free_gb": disk.free / (1024**3),
                "usage_percent": (disk.used / disk.total) * 100,
                "read_bytes": disk_io.read_bytes if disk_io else 0,
                "write_bytes": disk_io.write_bytes if disk_io else 0,
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv,
                "connections": network_connections,
            },
            "gpu": gpu_usage,
        }

    async def _get_gpu_usage(self) -> Optional[Dict[str, Any]]:
        """Get GPU usage if available"""
        try:
            # Try to import GPU monitoring libraries
            import GPUtil  # type: ignore[import-untyped]

            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]  # Get first GPU
                return {
                    "name": gpu.name,
                    "memory_total_mb": gpu.memoryTotal,
                    "memory_used_mb": gpu.memoryUsed,
                    "memory_free_mb": gpu.memoryFree,
                    "load_percent": gpu.load * 100,
                    "temperature_c": gpu.temperature,
                }
        except ImportError:
            pass
        except Exception as e:
            logger.debug(f"GPU monitoring not available: {e}")

        return None

    async def _store_usage_history(self, usage: Dict[str, Any]) -> None:
        """Store usage data in history"""
        timestamp = usage["timestamp"]

        # Store CPU usage
        self.cpu_history.append(
            {
                "timestamp": timestamp,
                "usage_percent": usage["cpu"]["usage_percent"],
                "count": usage["cpu"]["count"],
                "frequency_mhz": usage["cpu"]["frequency_mhz"],
            }
        )

        # Store memory usage
        self.memory_history.append(
            {
                "timestamp": timestamp,
                "total_gb": usage["memory"]["total_gb"],
                "used_gb": usage["memory"]["used_gb"],
                "usage_percent": usage["memory"]["usage_percent"],
            }
        )

        # Store disk usage
        self.disk_history.append(
            {
                "timestamp": timestamp,
                "total_gb": usage["disk"]["total_gb"],
                "used_gb": usage["disk"]["used_gb"],
                "usage_percent": usage["disk"]["usage_percent"],
            }
        )

        # Store network usage
        self.network_history.append(
            {
                "timestamp": timestamp,
                "bytes_sent": usage["network"]["bytes_sent"],
                "bytes_recv": usage["network"]["bytes_recv"],
                "connections": usage["network"]["connections"],
            }
        )

        # Store GPU usage if available
        if usage["gpu"]:
            self.gpu_history.append({"timestamp": timestamp, **usage["gpu"]})

        # Limit history size
        for history in [
            self.cpu_history,
            self.memory_history,
            self.disk_history,
            self.network_history,
            self.gpu_history,
        ]:
            if len(history) > self.history_size:
                history.pop(0)

    async def _check_alerts(self, usage: Dict[str, Any]) -> None:
        """Check for resource usage alerts"""
        alerts = []

        # CPU alert
        if usage["cpu"]["usage_percent"] > self.alert_thresholds["cpu_usage"]:
            alerts.append(
                {
                    "type": "cpu_high",
                    "severity": "warning",
                    "message": f"CPU usage is {usage['cpu']['usage_percent']:.1f}%",
                    "timestamp": usage["timestamp"],
                    "value": usage["cpu"]["usage_percent"],
                    "threshold": self.alert_thresholds["cpu_usage"],
                }
            )

        # Memory alert
        if usage["memory"]["usage_percent"] > self.alert_thresholds["memory_usage"]:
            alerts.append(
                {
                    "type": "memory_high",
                    "severity": "warning",
                    "message": f"Memory usage is {usage['memory']['usage_percent']:.1f}%",
                    "timestamp": usage["timestamp"],
                    "value": usage["memory"]["usage_percent"],
                    "threshold": self.alert_thresholds["memory_usage"],
                }
            )

        # Disk alert
        if usage["disk"]["usage_percent"] > self.alert_thresholds["disk_usage"]:
            alerts.append(
                {
                    "type": "disk_high",
                    "severity": "warning",
                    "message": f"Disk usage is {usage['disk']['usage_percent']:.1f}%",
                    "timestamp": usage["timestamp"],
                    "value": usage["disk"]["usage_percent"],
                    "threshold": self.alert_thresholds["disk_usage"],
                }
            )

        # Add new alerts
        self.alerts.extend(alerts)

        # Log alerts
        for alert in alerts:
            logger.warning(f"Resource alert: {alert['message']}")

    async def check_availability(self, allocation: ResourceAllocation) -> bool:
        """Check if requested resources are available"""
        current_usage = await self.get_current_usage()

        # Check CPU availability
        available_cpu = current_usage["cpu"]["count"] - (
            current_usage["cpu"]["usage_percent"] / 100 * current_usage["cpu"]["count"]
        )
        if allocation.cpu_cores > available_cpu:
            logger.warning(
                f"Insufficient CPU cores: requested {allocation.cpu_cores}, available {available_cpu:.1f}"
            )
            return False

        # Check memory availability
        available_memory_gb = current_usage["memory"]["available_gb"]
        if allocation.memory_gb > available_memory_gb:
            logger.warning(
                f"Insufficient memory: requested {allocation.memory_gb}GB, available {available_memory_gb:.1f}GB"
            )
            return False

        # Check storage availability
        available_storage_gb = current_usage["disk"]["free_gb"]
        if allocation.storage_gb > available_storage_gb:
            logger.warning(
                f"Insufficient storage: requested {allocation.storage_gb}GB, available {available_storage_gb:.1f}GB"
            )
            return False

        # Check GPU availability if required
        if allocation.gpu_required and not current_usage["gpu"]:
            logger.warning("GPU required but not available")
            return False

        return True

    async def get_current_usage(self) -> Dict[str, Any]:
        """Get current resource usage"""
        if not self.cpu_history:
            # If no history, collect current usage
            return await self._collect_resource_usage()

        # Return latest usage from history
        latest = {
            "timestamp": self.cpu_history[-1]["timestamp"],
            "cpu": {
                "usage_percent": self.cpu_history[-1]["usage_percent"],
                "count": self.cpu_history[-1]["count"],
                "frequency_mhz": self.cpu_history[-1]["frequency_mhz"],
            },
            "memory": {
                "total_gb": self.memory_history[-1]["total_gb"],
                "used_gb": self.memory_history[-1]["used_gb"],
                "usage_percent": self.memory_history[-1]["usage_percent"],
            },
            "disk": {
                "total_gb": self.disk_history[-1]["total_gb"],
                "used_gb": self.disk_history[-1]["used_gb"],
                "usage_percent": self.disk_history[-1]["usage_percent"],
            },
            "network": {
                "bytes_sent": self.network_history[-1]["bytes_sent"],
                "bytes_recv": self.network_history[-1]["bytes_recv"],
                "connections": self.network_history[-1]["connections"],
            },
            "gpu": self.gpu_history[-1] if self.gpu_history else None,
        }

        return latest

    async def get_usage_history(
        self, resource_type: str = "all", duration_hours: int = 24
    ) -> Dict[str, Any]:
        """Get resource usage history"""
        cutoff_time = datetime.now() - timedelta(hours=duration_hours)

        if resource_type == "all":
            return {
                "cpu": [
                    entry
                    for entry in self.cpu_history
                    if entry["timestamp"] > cutoff_time
                ],
                "memory": [
                    entry
                    for entry in self.memory_history
                    if entry["timestamp"] > cutoff_time
                ],
                "disk": [
                    entry
                    for entry in self.disk_history
                    if entry["timestamp"] > cutoff_time
                ],
                "network": [
                    entry
                    for entry in self.network_history
                    if entry["timestamp"] > cutoff_time
                ],
                "gpu": [
                    entry
                    for entry in self.gpu_history
                    if entry["timestamp"] > cutoff_time
                ],
            }
        elif resource_type == "cpu":
            return {
                "cpu": [
                    entry
                    for entry in self.cpu_history
                    if entry["timestamp"] > cutoff_time
                ]
            }
        elif resource_type == "memory":
            return {
                "memory": [
                    entry
                    for entry in self.memory_history
                    if entry["timestamp"] > cutoff_time
                ]
            }
        elif resource_type == "disk":
            return {
                "disk": [
                    entry
                    for entry in self.disk_history
                    if entry["timestamp"] > cutoff_time
                ]
            }
        elif resource_type == "network":
            return {
                "network": [
                    entry
                    for entry in self.network_history
                    if entry["timestamp"] > cutoff_time
                ]
            }
        elif resource_type == "gpu":
            return {
                "gpu": [
                    entry
                    for entry in self.gpu_history
                    if entry["timestamp"] > cutoff_time
                ]
            }
        else:
            raise ValueError(f"Unknown resource type: {resource_type}")

    async def get_usage_statistics(self, duration_hours: int = 24) -> Dict[str, Any]:
        """Get resource usage statistics"""
        history = await self.get_usage_history("all", duration_hours)

        stats = {}

        # CPU statistics
        if history["cpu"]:
            cpu_usage_values = [entry["usage_percent"] for entry in history["cpu"]]
            stats["cpu"] = {
                "average": sum(cpu_usage_values) / len(cpu_usage_values),
                "max": max(cpu_usage_values),
                "min": min(cpu_usage_values),
                "count": len(cpu_usage_values),
            }

        # Memory statistics
        if history["memory"]:
            memory_usage_values = [
                entry["usage_percent"] for entry in history["memory"]
            ]
            stats["memory"] = {
                "average": sum(memory_usage_values) / len(memory_usage_values),
                "max": max(memory_usage_values),
                "min": min(memory_usage_values),
                "count": len(memory_usage_values),
            }

        # Disk statistics
        if history["disk"]:
            disk_usage_values = [entry["usage_percent"] for entry in history["disk"]]
            stats["disk"] = {
                "average": sum(disk_usage_values) / len(disk_usage_values),
                "max": max(disk_usage_values),
                "min": min(disk_usage_values),
                "count": len(disk_usage_values),
            }

        # Network statistics
        if history["network"]:
            network_bytes_sent = [entry["bytes_sent"] for entry in history["network"]]
            network_bytes_recv = [entry["bytes_recv"] for entry in history["network"]]
            stats["network"] = {
                "total_bytes_sent": sum(network_bytes_sent),
                "total_bytes_recv": sum(network_bytes_recv),
                "average_connections": sum(
                    entry["connections"] for entry in history["network"]
                )
                / len(history["network"]),
                "count": len(history["network"]),
            }

        # GPU statistics
        if history["gpu"]:
            gpu_load_values = [entry["load_percent"] for entry in history["gpu"]]
            gpu_memory_values = [entry["memory_used_mb"] for entry in history["gpu"]]
            stats["gpu"] = {
                "average_load": sum(gpu_load_values) / len(gpu_load_values),
                "max_load": max(gpu_load_values),
                "average_memory_used": sum(gpu_memory_values) / len(gpu_memory_values),
                "max_memory_used": max(gpu_memory_values),
                "count": len(history["gpu"]),
            }

        return stats

    async def get_alerts(
        self, severity: Optional[str] = None, duration_hours: int = 24
    ) -> List[Dict[str, Any]]:
        """Get resource alerts"""
        cutoff_time = datetime.now() - timedelta(hours=duration_hours)

        filtered_alerts = [
            alert for alert in self.alerts if alert["timestamp"] > cutoff_time
        ]

        if severity:
            filtered_alerts = [
                alert for alert in filtered_alerts if alert["severity"] == severity
            ]

        return filtered_alerts

    async def clear_alerts(self) -> None:
        """Clear all alerts"""
        self.alerts.clear()
        logger.info("All alerts cleared")

    async def set_alert_thresholds(self, thresholds: Dict[str, float]) -> None:
        """Set alert thresholds"""
        self.alert_thresholds.update(thresholds)
        logger.info(f"Alert thresholds updated: {thresholds}")

    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            "platform": {
                "system": sys.platform,
                "release": (
                    sys.getwindowsversion()
                    if hasattr(sys, "getwindowsversion")
                    else None
                ),
                "version": sys.version,
                "machine": getattr(sys, "machine", "unknown"),
            },
            "cpu": {
                "count": psutil.cpu_count(),
                "count_logical": psutil.cpu_count(logical=True),
                "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            },
            "memory": {
                "total_gb": psutil.virtual_memory().total / (1024**3),
                "available_gb": psutil.virtual_memory().available / (1024**3),
            },
            "disk": {
                "total_gb": psutil.disk_usage("/").total / (1024**3),
                "free_gb": psutil.disk_usage("/").free / (1024**3),
            },
            "network": {"interfaces": list(psutil.net_if_addrs().keys())},
        }

    async def export_usage_data(self, file_path: str, duration_hours: int = 24) -> None:
        """Export usage data to file"""
        history = await self.get_usage_history("all", duration_hours)
        stats = await self.get_usage_statistics(duration_hours)
        alerts = await self.get_alerts(duration_hours=duration_hours)

        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "duration_hours": duration_hours,
            "history": history,
            "statistics": stats,
            "alerts": alerts,
        }

        with open(file_path, "w") as f:
            json.dump(export_data, f, indent=2, default=str)

        logger.info(f"Usage data exported to {file_path}")

    async def cleanup_old_data(self, days_to_keep: int = 7) -> int:
        """Clean up old usage data"""
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)

        initial_count = (
            len(self.cpu_history)
            + len(self.memory_history)
            + len(self.disk_history)
            + len(self.network_history)
            + len(self.gpu_history)
        )

        # Clean up CPU history
        self.cpu_history = [
            entry for entry in self.cpu_history if entry["timestamp"] > cutoff_time
        ]

        # Clean up memory history
        self.memory_history = [
            entry for entry in self.memory_history if entry["timestamp"] > cutoff_time
        ]

        # Clean up disk history
        self.disk_history = [
            entry for entry in self.disk_history if entry["timestamp"] > cutoff_time
        ]

        # Clean up network history
        self.network_history = [
            entry for entry in self.network_history if entry["timestamp"] > cutoff_time
        ]

        # Clean up GPU history
        self.gpu_history = [
            entry for entry in self.gpu_history if entry["timestamp"] > cutoff_time
        ]

        # Clean up old alerts
        self.alerts = [
            alert for alert in self.alerts if alert["timestamp"] > cutoff_time
        ]

        final_count = (
            len(self.cpu_history)
            + len(self.memory_history)
            + len(self.disk_history)
            + len(self.network_history)
            + len(self.gpu_history)
        )

        cleaned_count = initial_count - final_count
        logger.info(f"Cleaned up {cleaned_count} old data entries")

        return cleaned_count

    async def track_progress(self, progress_report) -> None:
        """Track progress for a task"""
        task_id = progress_report.task_id

        if task_id not in self.tasks_progress:
            self.tasks_progress[task_id] = []

        self.tasks_progress[task_id].append(progress_report)
        logger.info(f"Tracking progress for task: {task_id}")
