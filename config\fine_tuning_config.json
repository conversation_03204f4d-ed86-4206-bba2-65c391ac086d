{"data_preprocessing": {"input_file": "data/fine_tuning/raw_dataset.jsonl", "output_dir": "data/fine_tuning/processed", "min_length": 10, "max_length": 10000, "test_size": 0.1, "val_size": 0.1}, "training": {"model_name": "microsoft/DialoGPT-medium", "max_length": 512, "use_lora": true, "lora_r": 16, "lora_alpha": 32, "lora_dropout": 0.1, "target_modules": ["q_proj", "v_proj"], "num_epochs": 3, "batch_size": 4, "gradient_accumulation_steps": 4, "learning_rate": 0.0002, "warmup_steps": 100, "weight_decay": 0.01, "logging_steps": 10, "eval_steps": 100, "save_steps": 500, "save_total_limit": 3, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "use_wandb": false, "wandb_project": "ai-coding-agent-finetuning"}, "evaluation": {"max_samples": 1000, "metrics": ["bleu", "rouge", "perplexity", "code_quality"], "output_dir": "evaluation_results", "generation_config": {"max_new_tokens": 512, "temperature": 0.7, "do_sample": true, "top_p": 0.9, "top_k": 50}}}