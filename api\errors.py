"""
Error classes for the API module.

This module provides custom exception classes for handling various API-related errors.
"""

from typing import Any, Dict, Optional


class HfHubHTTPError(Exception):
    """Exception raised when an HTTP error occurs during Hub API calls."""

    def __init__(self, message: str, response: Optional[Any] = None, request_id: Optional[str] = None):
        self.message = message
        self.response = response
        self.request_id = request_id
        super().__init__(self.message)

    def __str__(self) -> str:
        if self.request_id:
            return f"{self.message} (Request ID: {self.request_id})"
        return self.message


class EntryNotFoundError(Exception):
    """Exception raised when an entry is not found."""

    def __init__(self, message: str, entry_type: Optional[str] = None):
        self.message = message
        self.entry_type = entry_type
        super().__init__(self.message)


class XetAuthorizationError(Exception):
    """Exception raised when Xet authorization fails."""

    def __init__(self, message: str, status_code: Optional[int] = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class XetRefreshTokenError(Exception):
    """Exception raised when Xet token refresh fails."""

    def __init__(self, message: str, status_code: Optional[int] = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


# Export all exception classes
__all__ = [
    "HfHubHTTPError",
    "EntryNotFoundError",
    "XetAuthorizationError",
    "XetRefreshTokenError"
]
