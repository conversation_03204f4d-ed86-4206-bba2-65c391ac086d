#!/usr/bin/env python3
"""
ASTManipulator - Language-specific AST operations

This module provides comprehensive AST manipulation capabilities including:
1. AST creation and modification
2. Template customization
3. AST merging strategies
4. Refactoring transformations
5. Multi-language AST support
6. Code structure manipulation
"""

import ast
import copy
import json
import logging
import re
import time
import warnings
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import astor

# Suppress astor deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="astor")

# Import CodeLanguage from ast_code_generator
try:
    from core.code_generation.ast_code_generator import CodeLanguage
except ImportError:
    # Fallback if circular import
    class CodeLanguage(Enum):
        """Supported programming languages"""

        PYTHON = "python"
        TYPESCRIPT = "typescript"
        JAVASCRIPT = "javascript"
        JSX = "jsx"
        TSX = "tsx"


logger = logging.getLogger(__name__)


class ASTNodeType(Enum):
    """AST node types"""

    MODULE = "module"
    FUNCTION = "function"
    CLASS = "class"
    IMPORT = "import"
    VARIABLE = "variable"
    EXPRESSION = "expression"
    STATEMENT = "statement"


class ManipulationStrategy(Enum):
    """AST manipulation strategies"""

    INSERT = "insert"
    REPLACE = "replace"
    DELETE = "delete"
    MERGE = "merge"
    TRANSFORM = "transform"


@dataclass
class ASTNode:
    """Represents an AST node with metadata"""

    node_type: ASTNodeType
    ast_node: ast.AST
    name: Optional[str] = None
    line_number: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ManipulationResult:
    """Result of AST manipulation"""

    success: bool
    modified_ast: Optional[ast.AST] = None
    changes_made: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ASTManipulator:
    """
    Provides language-specific AST manipulation capabilities for code generation.
    """

    def __init__(self, config_path: str = "config/ast_manipulator_config.json"):
        self.config = self._load_config(config_path)

        # AST manipulation state
        self.node_cache: Dict[str, ast.AST] = {}
        self.template_cache: Dict[str, ast.AST] = {}

        # Performance tracking
        self.manipulation_metrics = {
            "total_manipulations": 0,
            "successful_manipulations": 0,
            "failed_manipulations": 0,
            "average_manipulation_time": 0.0,
            "node_type_usage": {},
        }

        logger.info("ASTManipulator initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded AST manipulator config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "manipulation": {
                "enable_caching": True,
                "cache_size": 500,
                "max_ast_depth": 50,
                "enable_validation": True,
                "enable_optimization": True,
            },
            "python": {
                "enabled": True,
                "parser": "ast",
                "formatter": "astor",
                "conventions": {
                    "max_line_length": 88,
                    "indent_size": 4,
                    "quote_style": "double",
                },
            },
            "typescript": {
                "enabled": True,
                "parser": "typescript-parser",
                "formatter": "prettier",
                "conventions": {
                    "indent_size": 2,
                    "quote_style": "single",
                    "semicolons": True,
                },
            },
            "javascript": {
                "enabled": True,
                "parser": "esprima",
                "formatter": "prettier",
                "conventions": {
                    "indent_size": 2,
                    "quote_style": "single",
                    "semicolons": True,
                },
            },
            "templates": {
                "default_template_path": "templates/ast_templates",
                "auto_load_templates": True,
                "template_cache_size": 100,
            },
        }

    async def create_base_ast(self, language: Union[str, CodeLanguage]) -> ast.AST:
        """
        Create a base AST for the specified language.

        :param language: Programming language (str or CodeLanguage enum)
        :return: Base AST structure
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        if language.lower() == "python":
            return self._create_python_base_ast()
        elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
            return await self._create_js_base_ast(language)
        else:
            raise ValueError(f"Unsupported language: {language}")

    def _create_python_base_ast(self) -> ast.AST:
        """Create a base Python AST structure"""
        try:
            # Create a basic module with imports and main structure
            imports = []
            functions = []
            classes = []

            # Add basic imports
            imports.append(
                ast.ImportFrom(
                    module="typing",
                    names=[ast.alias(name="List", asname=None)],
                    level=0,
                )
            )

            # Create module
            module = ast.Module(body=imports + functions + classes, type_ignores=[])

            return module

        except Exception as e:
            logger.error(f"Failed to create Python base AST: {e}")
            raise

    async def _create_js_base_ast(self, language: str) -> ast.AST:
        """Create a base JavaScript/TypeScript AST structure"""
        try:
            # For now, create a basic structure
            # In a full implementation, this would use proper JS/TS AST
            module_ast = ast.Module(body=[], type_ignores=[])
            return module_ast

        except Exception as e:
            logger.error(f"Failed to create JS base AST: {e}")
            raise

    async def create_default_template(
        self, language: Union[str, CodeLanguage]
    ) -> ast.AST:
        """
        Create a default template AST for the specified language.

        :param language: Programming language (str or CodeLanguage enum)
        :return: Default template AST
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        if language.lower() == "python":
            return self._create_python_default_template()
        elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
            return await self._create_js_default_template(language)
        else:
            raise ValueError(f"Unsupported language: {language}")

    def _create_python_default_template(self) -> ast.AST:
        """Create a default Python template"""
        try:
            # Create a basic Python template
            template_code = '''
"""
Generated code template
"""

from typing import List, Dict, Any, Optional

def main():
    """Main function"""
    pass

if __name__ == "__main__":
    main()
'''
            return ast.parse(template_code)

        except Exception as e:
            logger.error(f"Failed to create Python default template: {e}")
            raise

    async def _create_js_default_template(self, language: str) -> ast.AST:
        """Create a default JavaScript/TypeScript template"""
        try:
            # Create a basic JS/TS template
            # This is a placeholder - actual implementation would create proper JS/TS AST
            module_ast = ast.Module(body=[], type_ignores=[])
            return module_ast

        except Exception as e:
            logger.error(f"Failed to create JS default template: {e}")
            raise

    async def customize_template(
        self, template: ast.AST, requirements: List[str], constraints: List[str]
    ) -> ast.AST:
        """Customize a template based on requirements and constraints"""
        try:
            customized_ast = copy.deepcopy(template)

            # Apply requirements
            for requirement in requirements:
                customized_ast = await self._apply_requirement(
                    customized_ast, requirement
                )

            # Apply constraints
            for constraint in constraints:
                customized_ast = await self.apply_constraint(customized_ast, constraint)

            return customized_ast

        except Exception as e:
            logger.error(f"Failed to customize template: {e}")
            raise

    async def _apply_requirement(self, ast_tree: ast.AST, requirement: str) -> ast.AST:
        """Apply a requirement to the AST"""
        try:
            requirement_lower = requirement.lower()

            # Handle different types of requirements
            if "function" in requirement_lower:
                ast_tree = await self._add_function(ast_tree, requirement)
            elif "class" in requirement_lower:
                ast_tree = await self._add_class(ast_tree, requirement)
            elif "import" in requirement_lower:
                ast_tree = await self._add_import(ast_tree, requirement)
            elif "variable" in requirement_lower:
                ast_tree = await self._add_variable(ast_tree, requirement)
            else:
                # Generic requirement handling
                ast_tree = await self._add_generic_requirement(ast_tree, requirement)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to apply requirement '{requirement}': {e}")
            return ast_tree

    async def apply_constraint(
        self,
        ast_tree: ast.AST,
        constraint: str,
        language: Union[str, CodeLanguage] = "python",
    ) -> ast.AST:
        """
        Apply a constraint to the AST.

        :param ast_tree: AST to modify
        :param constraint: Constraint to apply
        :param language: Programming language (str or CodeLanguage enum)
        :return: Modified AST
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        logger.info(f"Applying constraint '{constraint}' to {language} AST")

        try:
            # Apply language-specific constraints
            if language.lower() == "python":
                return await self._apply_python_constraint(ast_tree, constraint)
            elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
                return await self._apply_js_constraint(ast_tree, constraint)
            else:
                return await self._add_generic_constraint(ast_tree, constraint)
        except Exception as e:
            logger.error(f"Failed to apply constraint '{constraint}': {e}")
            return ast_tree

    async def _apply_python_constraint(
        self, ast_tree: ast.AST, constraint: str
    ) -> ast.AST:
        """Apply Python-specific constraints to the AST"""
        constraint_lower = constraint.lower()

        if "async" in constraint_lower:
            ast_tree = await self._make_async(ast_tree)
        elif "type" in constraint_lower:
            ast_tree = await self._add_type_hints(ast_tree)
        elif "docstring" in constraint_lower:
            ast_tree = await self._add_docstrings(ast_tree)
        elif "error" in constraint_lower:
            ast_tree = await self._add_error_handling(ast_tree)
        else:
            ast_tree = await self._add_generic_constraint(ast_tree, constraint)

        return ast_tree

    async def _apply_js_constraint(self, ast_tree: ast.AST, constraint: str) -> ast.AST:
        """Apply JavaScript/TypeScript-specific constraints to the AST"""
        constraint_lower = constraint.lower()

        if "async" in constraint_lower:
            # Handle async for JS/TS
            ast_tree = await self._add_generic_constraint(ast_tree, "async")
        elif "type" in constraint_lower:
            # Handle TypeScript types
            ast_tree = await self._add_generic_constraint(ast_tree, "typescript")
        else:
            ast_tree = await self._add_generic_constraint(ast_tree, constraint)

        return ast_tree

    async def _add_function(self, ast_tree: ast.AST, requirement: str) -> ast.AST:
        """Add a function to the AST"""
        try:
            # Extract function name from requirement
            function_name = self._extract_function_name(requirement)

            # Create function AST
            function_ast = ast.FunctionDef(
                name=function_name,
                args=ast.arguments(
                    posonlyargs=[], args=[], kwonlyargs=[], defaults=[], kw_defaults=[]
                ),
                body=[ast.Pass()],
                decorator_list=[],
                returns=None,
            )

            # Add to module body
            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(function_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add function: {e}")
            return ast_tree

    async def _add_class(self, ast_tree: ast.AST, requirement: str) -> ast.AST:
        """Add a class to the AST"""
        try:
            # Extract class name from requirement
            class_name = self._extract_class_name(requirement)

            # Create class AST
            class_ast = ast.ClassDef(
                name=class_name,
                bases=[],
                keywords=[],
                body=[ast.Pass()],
                decorator_list=[],
            )

            # Add to module body
            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(class_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add class: {e}")
            return ast_tree

    async def _add_import(self, ast_tree: ast.AST, requirement: str) -> ast.AST:
        """Add an import to the AST"""
        try:
            # Extract module name from requirement
            module_name = self._extract_module_name(requirement)

            # Create import AST
            import_ast = ast.Import(names=[ast.alias(name=module_name, asname=None)])

            # Add to module body at the beginning
            if isinstance(ast_tree, ast.Module):
                ast_tree.body.insert(0, import_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add import: {e}")
            return ast_tree

    async def _add_variable(self, ast_tree: ast.AST, requirement: str) -> ast.AST:
        """Add a variable to the AST"""
        try:
            # Extract variable name from requirement
            variable_name = self._extract_variable_name(requirement)

            # Create variable AST
            variable_ast = ast.Assign(
                targets=[ast.Name(id=variable_name, ctx=ast.Store())],
                value=ast.Constant(value=None),
            )

            # Add to module body
            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(variable_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add variable: {e}")
            return ast_tree

    async def _add_generic_requirement(
        self, ast_tree: ast.AST, requirement: str
    ) -> ast.AST:
        """Add a generic requirement to the AST"""
        try:
            # Create a comment node for the requirement
            comment_ast = ast.Expr(
                value=ast.Constant(value=f"# TODO: Implement {requirement}")
            )

            # Add to module body
            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(comment_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add generic requirement: {e}")
            return ast_tree

    async def _make_async(self, ast_tree: ast.AST) -> ast.AST:
        """Make functions async"""
        try:
            # Find all function definitions and make them async
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    node.name = f"async_{node.name}"
                    # Add async keyword (this would require AST modification)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to make async: {e}")
            return ast_tree

    async def _add_type_hints(self, ast_tree: ast.AST) -> ast.AST:
        """Add type hints to functions"""
        try:
            # Find all function definitions and add type hints
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    # Add return type hint
                    node.returns = ast.Name(id="Any", ctx=ast.Load())

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add type hints: {e}")
            return ast_tree

    async def _add_docstrings(self, ast_tree: ast.AST) -> ast.AST:
        """Add docstrings to functions and classes"""
        try:
            # Find all function and class definitions and add docstrings
            for node in ast.walk(ast_tree):
                if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                    if (
                        not node.body
                        or not isinstance(node.body[0], ast.Expr)
                        or not isinstance(node.body[0].value, ast.Constant)
                    ):
                        # Add docstring
                        docstring = ast.Expr(
                            value=ast.Constant(value=f"Documentation for {node.name}")
                        )
                        node.body.insert(0, docstring)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add docstrings: {e}")
            return ast_tree

    async def _add_error_handling(self, ast_tree: ast.AST) -> ast.AST:
        """Add error handling to functions"""
        try:
            # Find all function definitions and add try-except blocks
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    # Wrap function body in try-except
                    try_block = ast.Try(
                        body=node.body,
                        handlers=[
                            ast.ExceptHandler(
                                type=ast.Name(id="Exception", ctx=ast.Load()),
                                name="e",
                                body=[ast.Pass()],
                            )
                        ],
                        orelse=[],
                        finalbody=[],
                    )
                    node.body = [try_block]

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add error handling: {e}")
            return ast_tree

    async def _add_generic_constraint(
        self, ast_tree: ast.AST, constraint: str
    ) -> ast.AST:
        """Add a generic constraint to the AST"""
        try:
            # Create a comment node for the constraint
            comment_ast = ast.Expr(
                value=ast.Constant(value=f"# Constraint: {constraint}")
            )

            # Add to module body
            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(comment_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add generic constraint: {e}")
            return ast_tree

    async def add_imports(
        self, ast_tree: ast.AST, imports: List[str], language: Union[str, CodeLanguage]
    ) -> ast.AST:
        """
        Add imports to the AST.

        :param ast_tree: AST to modify
        :param imports: List of imports to add
        :param language: Programming language (str or CodeLanguage enum)
        :return: Modified AST
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        if language.lower() == "python":
            return await self._add_python_imports(ast_tree, imports)
        elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
            return await self._add_js_imports(ast_tree, imports, language)
        else:
            raise ValueError(f"Unsupported language: {language}")

    async def _add_python_imports(
        self, ast_tree: ast.AST, imports: List[str]
    ) -> ast.AST:
        """Add Python imports to the AST"""
        try:
            if not isinstance(ast_tree, ast.Module):
                return ast_tree

            # Create import statements
            import_nodes = []
            for import_str in imports:
                if "from" in import_str:
                    # Handle "from module import name" format
                    parts = import_str.split()
                    if len(parts) >= 4:
                        module = parts[1]
                        name = parts[3]
                        import_nodes.append(
                            ast.ImportFrom(
                                module=module,
                                names=[ast.alias(name=name, asname=None)],
                                level=0,
                            )
                        )
                else:
                    # Handle "import module" format
                    import_nodes.append(
                        ast.Import(names=[ast.alias(name=import_str, asname=None)])
                    )

            # Add imports at the beginning
            ast_tree.body = import_nodes + ast_tree.body

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add Python imports: {e}")
            return ast_tree

    async def _add_js_imports(
        self, ast_tree: ast.AST, imports: List[str], language: str
    ) -> ast.AST:
        """Add JavaScript/TypeScript imports to the AST"""
        try:
            # This is a placeholder for JS/TS import handling
            # In a full implementation, this would create proper JS/TS import AST nodes
            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add JS imports: {e}")
            return ast_tree

    async def add_functionality(
        self, ast_tree: ast.AST, requirement: str, language: Union[str, CodeLanguage]
    ) -> ast.AST:
        """
        Add functionality to the AST.

        :param ast_tree: AST to modify
        :param requirement: Functionality requirement
        :param language: Programming language (str or CodeLanguage enum)
        :return: Modified AST
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        if language.lower() == "python":
            return await self._add_python_functionality(ast_tree, requirement)
        elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
            return await self._add_js_functionality(ast_tree, requirement, language)
        else:
            raise ValueError(f"Unsupported language: {language}")

    async def _add_python_functionality(
        self, ast_tree: ast.AST, requirement: str
    ) -> ast.AST:
        """Add Python functionality based on requirement"""
        try:
            logger.info(f"Adding Python functionality for requirement: {requirement}")

            # Parse requirement and add appropriate functionality
            requirement_lower = requirement.lower()

            if "api" in requirement_lower:
                # Add API-related functionality
                logger.info("Adding API functionality")
                ast_tree = await self._add_api_functionality(ast_tree)
            elif "database" in requirement_lower:
                # Add database-related functionality
                logger.info("Adding database functionality")
                ast_tree = await self._add_database_functionality(ast_tree)
            elif "web" in requirement_lower:
                # Add web-related functionality
                logger.info("Adding web functionality")
                ast_tree = await self._add_web_functionality(ast_tree)
            else:
                # Add generic functionality
                logger.info("Adding generic functionality")
                ast_tree = await self._add_generic_functionality(ast_tree, requirement)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add Python functionality: {e}")
            return ast_tree

    async def _add_js_functionality(
        self, ast_tree: ast.AST, requirement: str, language: str
    ) -> ast.AST:
        """Add JavaScript/TypeScript functionality based on requirement"""
        try:
            # This is a placeholder for JS/TS functionality
            # In a full implementation, this would add appropriate JS/TS AST nodes
            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add JS functionality: {e}")
            return ast_tree

    async def _add_api_functionality(self, ast_tree: ast.AST) -> ast.AST:
        """Add API-related functionality"""
        try:
            # Add FastAPI or similar API framework imports and structure
            api_imports = [
                "from fastapi import FastAPI, HTTPException",
                "from pydantic import BaseModel",
                "from typing import List, Optional",
            ]

            for import_str in api_imports:
                ast_tree = await self._add_python_imports(ast_tree, [import_str])

            # Add basic API structure
            app_ast = ast.Assign(
                targets=[ast.Name(id="app", ctx=ast.Store())],
                value=ast.Call(
                    func=ast.Name(id="FastAPI", ctx=ast.Load()), args=[], keywords=[]
                ),
            )

            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(app_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add API functionality: {e}")
            return ast_tree

    async def _add_database_functionality(self, ast_tree: ast.AST) -> ast.AST:
        """Add database-related functionality"""
        try:
            # Add database imports
            db_imports = [
                "import sqlite3",
                "from sqlalchemy import create_engine, Column, Integer, String",
                "from sqlalchemy.ext.declarative import declarative_base",
            ]

            for import_str in db_imports:
                ast_tree = await self._add_python_imports(ast_tree, [import_str])

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add database functionality: {e}")
            return ast_tree

    async def _add_web_functionality(self, ast_tree: ast.AST) -> ast.AST:
        """Add web-related functionality"""
        try:
            # Add web framework imports
            web_imports = [
                "from flask import Flask, render_template, request",
                "import requests",
            ]

            for import_str in web_imports:
                ast_tree = await self._add_python_imports(ast_tree, [import_str])

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add web functionality: {e}")
            return ast_tree

    async def _add_generic_functionality(
        self, ast_tree: ast.AST, requirement: str
    ) -> ast.AST:
        """Add generic functionality"""
        try:
            logger.info(f"Adding generic functionality for requirement: {requirement}")

            # Create a function based on the requirement
            function_name = (
                self._extract_function_name(requirement) or "handle_requirement"
            )
            logger.info(f"Extracted function name: {function_name}")

            function_ast = ast.FunctionDef(
                name=function_name,
                args=ast.arguments(
                    posonlyargs=[], args=[], kwonlyargs=[], defaults=[], kw_defaults=[]
                ),
                body=[
                    ast.Expr(
                        value=ast.Constant(value=f"# Implementation for: {requirement}")
                    ),
                    ast.Pass(),
                ],
                decorator_list=[],
                returns=None,
            )

            if isinstance(ast_tree, ast.Module):
                ast_tree.body.append(function_ast)

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to add generic functionality: {e}")
            return ast_tree

    async def merge_asts(
        self,
        existing_ast: ast.AST,
        new_ast: ast.AST,
        language: Union[str, CodeLanguage],
    ) -> ast.AST:
        """
        Merge two ASTs.

        :param existing_ast: Existing AST
        :param new_ast: New AST to merge
        :param language: Programming language (str or CodeLanguage enum)
        :return: Merged AST
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        if language.lower() == "python":
            return await self._merge_python_asts(existing_ast, new_ast)
        elif language.lower() in ["javascript", "jsx", "typescript", "tsx"]:
            return await self._merge_js_asts(existing_ast, new_ast, language)
        else:
            raise ValueError(f"Unsupported language: {language}")

    async def _merge_python_asts(
        self, existing_ast: ast.AST, new_ast: ast.AST
    ) -> ast.AST:
        """Merge Python ASTs"""
        try:
            if not isinstance(existing_ast, ast.Module) or not isinstance(
                new_ast, ast.Module
            ):
                return existing_ast

            # Merge module bodies
            existing_ast.body.extend(new_ast.body)

            return existing_ast

        except Exception as e:
            logger.error(f"Failed to merge Python ASTs: {e}")
            return existing_ast

    async def _merge_js_asts(
        self, existing_ast: ast.AST, new_ast: ast.AST, language: str
    ) -> ast.AST:
        """Merge JavaScript/TypeScript ASTs"""
        try:
            # This is a placeholder for JS/TS AST merging
            # In a full implementation, this would properly merge JS/TS ASTs
            return existing_ast

        except Exception as e:
            logger.error(f"Failed to merge JS ASTs: {e}")
            return existing_ast

    async def apply_refactoring_rule(
        self,
        ast_tree: ast.AST,
        rule: Dict[str, Any],
        language: Union[str, CodeLanguage],
    ) -> ast.AST:
        """
        Apply a refactoring rule to the AST.

        :param ast_tree: AST to modify
        :param rule: Refactoring rule to apply
        :param language: Programming language (str or CodeLanguage enum)
        :return: Modified AST
        """
        # Convert CodeLanguage enum to string if needed
        if isinstance(language, CodeLanguage):
            language = language.value

        rule_type = rule.get("type", "unknown")

        if rule_type == "rename":
            return await self._apply_rename_rule(ast_tree, rule)
        elif rule_type == "extract":
            return await self._apply_extract_rule(ast_tree, rule)
        elif rule_type == "inline":
            return await self._apply_inline_rule(ast_tree, rule)
        elif rule_type == "move":
            return await self._apply_move_rule(ast_tree, rule)
        else:
            logger.warning(f"Unknown refactoring rule type: {rule_type}")
            return ast_tree

    async def _apply_rename_rule(
        self, ast_tree: ast.AST, rule: Dict[str, Any]
    ) -> ast.AST:
        """Apply rename refactoring rule"""
        try:
            old_name = rule.get("old_name")
            new_name = rule.get("new_name")

            if not old_name or not new_name:
                return ast_tree

            # Find and rename all occurrences
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Name) and node.id == old_name:
                    node.id = new_name

            return ast_tree

        except Exception as e:
            logger.error(f"Failed to apply rename rule: {e}")
            return ast_tree

    async def _apply_extract_rule(
        self, ast_tree: ast.AST, rule: Dict[str, Any]
    ) -> ast.AST:
        """Apply extract refactoring rule"""
        try:
            # This is a simplified implementation
            # In a full implementation, this would extract code into functions/methods
            return ast_tree

        except Exception as e:
            logger.error(f"Failed to apply extract rule: {e}")
            return ast_tree

    async def _apply_inline_rule(
        self, ast_tree: ast.AST, rule: Dict[str, Any]
    ) -> ast.AST:
        """Apply inline refactoring rule"""
        try:
            # This is a simplified implementation
            # In a full implementation, this would inline functions/methods
            return ast_tree

        except Exception as e:
            logger.error(f"Failed to apply inline rule: {e}")
            return ast_tree

    async def _apply_move_rule(
        self, ast_tree: ast.AST, rule: Dict[str, Any]
    ) -> ast.AST:
        """Apply move refactoring rule"""
        try:
            # This is a simplified implementation
            # In a full implementation, this would move code between modules/classes
            return ast_tree

        except Exception as e:
            logger.error(f"Failed to apply move rule: {e}")
            return ast_tree

    # Helper methods for extracting information from requirements
    def _extract_function_name(self, requirement: str) -> Optional[str]:
        """Extract function name from requirement"""
        try:
            logger.info(f"Extracting function name from: {requirement}")

            # Simple pattern matching for function names
            patterns = [
                r"function\s+(\w+)",
                r"def\s+(\w+)",
                r"create\s+(\w+)",
                r"(\w+)\s+function",
                r"calculates?\s+(\w+)",
                r"(\w+)\s+numbers?",
                r"(\w+)\s+algorithm",
                r"(\w+)\s+calculation",
                r"fibonacci",
                r"(\w+)\s+fibonacci",
                r"fibonacci\s+(\w+)",
            ]

            for pattern in patterns:
                match = re.search(pattern, requirement, re.IGNORECASE)
                if match:
                    logger.info(f"Pattern '{pattern}' matched: {match.group(1)}")
                    return match.group(1)

            # Fallback: extract first word that looks like a function name
            words = requirement.split()
            for word in words:
                if (
                    word.isalpha()
                    and word[0].islower()
                    and len(word) > 3
                    and word
                    not in [
                        "that",
                        "this",
                        "with",
                        "from",
                        "into",
                        "during",
                        "including",
                        "until",
                        "against",
                        "among",
                        "throughout",
                        "despite",
                        "towards",
                        "upon",
                    ]
                ):
                    logger.info(f"Fallback extracted word: {word}")
                    return word

            logger.info("No function name extracted")
            return None

        except Exception as e:
            logger.error(f"Failed to extract function name: {e}")
            return None

    def _extract_class_name(self, requirement: str) -> Optional[str]:
        """Extract class name from requirement"""
        try:
            # Simple pattern matching for class names
            patterns = [
                r"class\s+(\w+)",
                r"(\w+)\s+class",
                r"(\w+)\s+model",
                r"(\w+)\s+service",
            ]

            for pattern in patterns:
                match = re.search(pattern, requirement, re.IGNORECASE)
                if match:
                    return match.group(1)

            return None

        except Exception as e:
            logger.error(f"Failed to extract class name: {e}")
            return None

    def _extract_module_name(self, requirement: str) -> Optional[str]:
        """Extract module name from requirement"""
        try:
            # Simple pattern matching for module names
            patterns = [
                r"import\s+(\w+)",
                r"from\s+(\w+)",
                r"use\s+(\w+)",
                r"require\s+['\"](\w+)['\"]",
            ]

            for pattern in patterns:
                match = re.search(pattern, requirement, re.IGNORECASE)
                if match:
                    return match.group(1)

            return None

        except Exception as e:
            logger.error(f"Failed to extract module name: {e}")
            return None

    def _extract_variable_name(self, requirement: str) -> Optional[str]:
        """Extract variable name from requirement"""
        try:
            # Simple pattern matching for variable names
            patterns = [
                r"variable\s+(\w+)",
                r"(\w+)\s+variable",
                r"let\s+(\w+)",
                r"const\s+(\w+)",
                r"var\s+(\w+)",
            ]

            for pattern in patterns:
                match = re.search(pattern, requirement, re.IGNORECASE)
                if match:
                    return match.group(1)

            return None

        except Exception as e:
            logger.error(f"Failed to extract variable name: {e}")
            return None

    async def get_manipulation_metrics(self) -> Dict[str, Any]:
        """Get AST manipulation performance metrics"""
        try:
            total_manipulations = self.manipulation_metrics["total_manipulations"]
            if total_manipulations == 0:
                return self.manipulation_metrics

            return {
                **self.manipulation_metrics,
                "success_rate": (
                    self.manipulation_metrics["successful_manipulations"]
                    / total_manipulations
                )
                * 100,
                "failure_rate": (
                    self.manipulation_metrics["failed_manipulations"]
                    / total_manipulations
                )
                * 100,
                "cache_size": len(self.node_cache),
            }

        except Exception as e:
            logger.error(f"Failed to get manipulation metrics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            # Clear caches
            self.node_cache.clear()
            self.template_cache.clear()

            logger.info("ASTManipulator cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup ASTManipulator: {e}")
