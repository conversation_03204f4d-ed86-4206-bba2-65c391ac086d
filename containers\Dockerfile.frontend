# AI Coding Agent - Frontend Service Dockerfile
# Follows cursorrules.md Docker-First Policy requirements
# Multi-stage build with security hardening

# Stage 1: Dependencies
FROM node:18-alpine AS deps

WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Copy package files for dependency installation
COPY package.json package-lock.json ./

# Install dependencies (production only)
RUN npm ci --omit=dev --frozen-lockfile

# Stage 2: Builder
FROM node:18-alpine AS builder

WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code and configuration
COPY package.json ./
COPY pages/ ./pages/
COPY components/ ./components/
COPY public/ ./public/
COPY styles/ ./styles/
COPY lib/ ./lib/
COPY types/ ./types/
COPY contexts/ ./contexts/
COPY hooks/ ./hooks/
COPY services/ ./services/
COPY store/ ./store/
COPY next.config.* ./
COPY tsconfig.json ./
COPY tailwind.config.* ./
COPY postcss.config.* ./

# Build the application
RUN npm run build

# Stage 3: Runtime
FROM node:18-alpine AS runtime

WORKDIR /app

# Set production environment
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built application from builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Create non-root user for security
RUN addgroup --system --gid 1000 nodejs \
    && adduser --system --uid 1000 --ingroup nodejs nodejs \
    && chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose application port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["npm", "start"]
