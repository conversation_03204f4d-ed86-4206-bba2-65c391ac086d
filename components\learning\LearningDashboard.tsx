/**
 * Learning Dashboard Component
 * Displays learning insights, recommendations, and system status
 */

import React, { useEffect } from 'react';
import { useLearningStore } from '../../store/learningStore';
import { LearningCard } from './LearningCard';
import { LearningRecommendations } from './LearningRecommendations';
import { LearningAnalytics } from './LearningAnalytics';
import { LearningStatus } from './LearningStatus';

export const LearningDashboard: React.FC = () => {
  const {
    status,
    summary,
    recommendations,
    loading,
    error,
    lastUpdate,
    refreshAll,
  } = useLearningStore();

  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  if (loading && !status && !summary) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500" data-testid="loading-spinner"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading learning data</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={refreshAll}
                    className="bg-red-100 text-red-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Learning Dashboard</h1>
          <p className="mt-2 text-gray-600">
            Monitor and manage the AI learning system performance and insights
          </p>
          {lastUpdate && (
            <p className="mt-1 text-sm text-gray-500">
              Last updated: {lastUpdate.toLocaleString()}
            </p>
          )}
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <LearningCard
            title="System Status"
            value={status?.data?.status || 'Unknown'}
            icon="status"
            color="blue"
          />
          <LearningCard
            title="Active Models"
            value={status?.data?.active_models || 0}
            icon="models"
            color="green"
          />
          <LearningCard
            title="Total Patterns"
            value={summary?.summary?.total_patterns_learned || 0}
            icon="patterns"
            color="purple"
          />
          <LearningCard
            title="System Health"
            value={summary?.summary?.system_health || 'Unknown'}
            icon="health"
            color="yellow"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Analytics */}
          <div className="lg:col-span-2">
            <LearningAnalytics />
          </div>

          {/* Status */}
          <div>
            <LearningStatus />
          </div>
        </div>

        {/* Recommendations */}
        <div className="mt-8">
          <LearningRecommendations />
        </div>
      </div>
    </div>
  );
};
