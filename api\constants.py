"""
Constants for the API module.

This module provides constants used throughout the API.
"""

# Default endpoint for Hugging Face Hub
ENDPOINT = "https://huggingface.co"

# Enable HF Transfer
HF_HUB_ENABLE_HF_TRANSFER = False

# Other constants that might be needed
DEFAULT_REVISION = "main"
DEFAULT_REPO_TYPE = "model"

# File size limits
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
MAX_LFS_FILE_SIZE = 100 * 1024 * 1024  # 100MB

# Upload limits
MAX_UPLOAD_BATCH_SIZE = 256
MAX_FETCH_BATCH_SIZE = 1000

# Timeouts
DEFAULT_TIMEOUT = 30
UPLOAD_TIMEOUT = 300

# Export all constants
__all__ = [
    "ENDPOINT",
    "HF_HUB_ENABLE_HF_TRANSFER",
    "DEFAULT_REVISION",
    "DEFAULT_REPO_TYPE",
    "MAX_FILE_SIZE",
    "MAX_LFS_FILE_SIZE",
    "MAX_UPLOAD_BATCH_SIZE",
    "MAX_FETCH_BATCH_SIZE",
    "DEFAULT_TIMEOUT",
    "UPLOAD_TIMEOUT"
]
