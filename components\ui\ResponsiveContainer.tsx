import React, { ReactNode } from 'react';

export interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  center?: boolean;
  fluid?: boolean;
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  maxWidth = 'xl',
  padding = 'md',
  center = true,
  fluid = false,
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
    none: '',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 sm:px-6',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-6 sm:px-8 lg:px-12',
    xl: 'px-8 sm:px-12 lg:px-16',
  };

  const containerClasses = [
    fluid ? 'w-full' : maxWidthClasses[maxWidth],
    center && !fluid ? 'mx-auto' : '',
    paddingClasses[padding],
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

// Specialized responsive containers
export const PageContainer: React.FC<ResponsiveContainerProps> = (props) => (
  <ResponsiveContainer maxWidth="2xl" padding="lg" {...props} />
);

export const ContentContainer: React.FC<ResponsiveContainerProps> = (props) => (
  <ResponsiveContainer maxWidth="xl" padding="md" {...props} />
);

export const NarrowContainer: React.FC<ResponsiveContainerProps> = (props) => (
  <ResponsiveContainer maxWidth="lg" padding="md" {...props} />
);
