"""
Enhanced Code Generator - Main Engine

The core code generation engine that coordinates all enhanced code generation
features including completion, bug detection, optimization, and pattern recognition.

Phase 19 Implementation - Enhanced Code Generation
"""

import asyncio
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from code_generation.bug_detector import BugDetector
from code_generation.code_analyzer import CodeAnalyzer
from code_generation.code_optimizer import CodeOptimizer
from code_generation.deepseek_integration import DeepseekIntegration
from code_generation.editor_integration import EditorIntegration
from code_generation.pattern_recognizer import PatternRecognizer

logger = logging.getLogger(__name__)


@dataclass
class CodeGenerationRequest:
    """Request structure for code generation operations."""

    code: str
    language: str
    context: Optional[str] = None
    cursor_position: Optional[int] = None
    file_path: Optional[str] = None
    project_context: Optional[Dict[str, Any]] = None


@dataclass
class CodeGenerationResponse:
    """Response structure for code generation operations."""

    generated_code: str
    suggestions: List[str]
    confidence: float
    analysis_results: Dict[str, Any]
    optimization_suggestions: List[str]
    pattern_insights: List[str]
    execution_time: float


class EnhancedCodeGenerator:
    """
    Main enhanced code generation engine.

    Coordinates all code generation features including:
    - Code completion with context awareness
    - Bug detection and fixing
    - Code optimization
    - Pattern recognition
    - Editor integration
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the enhanced code generator.

        Args:
            config: Configuration dictionary for the code generator
        """
        self.config = config or self._load_default_config()
        self.deepseek_integration = DeepseekIntegration(self.config.get("model", {}))
        self.code_analyzer = CodeAnalyzer(self.config.get("analysis", {}))
        self.pattern_recognizer = PatternRecognizer(self.config.get("patterns", {}))
        self.bug_detector = BugDetector(self.config.get("bug_detection", {}))
        self.code_optimizer = CodeOptimizer(self.config.get("optimization", {}))
        self.editor_integration = EditorIntegration(self.config.get("editor", {}))

        logger.info("Enhanced Code Generator initialized successfully")

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration for the code generator."""
        return {
            "enabled": True,
            "version": "1.0.0",
            "model": {
                "name": "deepseek-coder:1.3b",
                "temperature": 0.3,
                "max_tokens": 2048,
                "top_p": 0.95,
            },
            "features": {
                "code_completion": {
                    "enabled": True,
                    "context_lines": 50,
                    "suggestion_count": 5,
                },
                "bug_detection": {
                    "enabled": True,
                    "severity_levels": ["error", "warning", "info"],
                    "auto_fix": True,
                },
                "optimization": {
                    "enabled": True,
                    "performance_threshold": 0.8,
                    "memory_threshold": 0.7,
                },
                "pattern_recognition": {
                    "enabled": True,
                    "learning_enabled": True,
                    "pattern_threshold": 0.6,
                },
            },
            "languages": {
                "python": {"enabled": True, "priority": "high"},
                "javascript": {"enabled": True, "priority": "high"},
                "typescript": {"enabled": True, "priority": "high"},
                "java": {"enabled": True, "priority": "medium"},
                "cpp": {"enabled": True, "priority": "medium"},
            },
            "editor_integration": {
                "enabled": True,
                "real_time_suggestions": True,
                "inline_completion": True,
                "quick_fixes": True,
            },
        }

    async def generate_code_completion(
        self, request: CodeGenerationRequest
    ) -> CodeGenerationResponse:
        """
        Generate intelligent code completion with context awareness.

        Args:
            request: Code generation request with context

        Returns:
            CodeGenerationResponse with completion suggestions
        """
        start_time = asyncio.get_event_loop().time()

        try:
            # Generate code completion using deepseek-coder
            completion_result = await self.deepseek_integration.generate_completion(
                code=request.code,
                language=request.language,
                context=request.context,
                cursor_position=request.cursor_position,
            )

            # Analyze the generated code
            analysis_results = await self.code_analyzer.analyze_code(
                code=completion_result["generated_code"], language=request.language
            )

            # Detect potential bugs
            bug_results = await self.bug_detector.detect_bugs(
                code=completion_result["generated_code"], language=request.language
            )

            # Recognize patterns
            pattern_results = await self.pattern_recognizer.recognize_patterns(
                code=completion_result["generated_code"], language=request.language
            )

            # Generate optimization suggestions
            optimization_results = await self.code_optimizer.optimize_code(
                code=completion_result["generated_code"],
                language=request.language,
                analysis_results=analysis_results,
            )

            execution_time = asyncio.get_event_loop().time() - start_time

            return CodeGenerationResponse(
                generated_code=completion_result["generated_code"],
                suggestions=completion_result["suggestions"],
                confidence=completion_result["confidence"],
                analysis_results={
                    "code_analysis": analysis_results,
                    "bug_detection": bug_results,
                    "pattern_recognition": pattern_results,
                    "optimization": optimization_results,
                },
                optimization_suggestions=optimization_results.get("suggestions", []),
                pattern_insights=pattern_results.get("insights", []),
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Error in code completion generation: {e}")
            raise

    async def detect_and_fix_bugs(
        self, request: CodeGenerationRequest
    ) -> Dict[str, Any]:
        """
        Detect and automatically fix bugs in code.

        Args:
            request: Code generation request

        Returns:
            Dictionary with bug detection and fix results
        """
        try:
            # Detect bugs
            bug_results = await self.bug_detector.detect_bugs(
                code=request.code, language=request.language
            )

            # Generate fixes for detected bugs
            fixes = []
            for bug in bug_results.get("bugs", []):
                if bug.get("severity") in ["error", "warning"]:
                    fix = await self.deepseek_integration.generate_bug_fix(
                        code=request.code,
                        bug_description=bug.get("description"),
                        language=request.language,
                    )
                    fixes.append({"bug": bug, "fix": fix})

            return {
                "bugs_detected": len(bug_results.get("bugs", [])),
                "bugs": bug_results.get("bugs", []),
                "fixes": fixes,
                "auto_fix_applied": len(fixes),
                "critical_count": len(
                    [
                        bug
                        for bug in bug_results.get("bugs", [])
                        if bug.get("severity") == "critical"
                    ]
                ),
                "high_count": len(
                    [
                        bug
                        for bug in bug_results.get("bugs", [])
                        if bug.get("severity") == "high"
                    ]
                ),
                "medium_count": len(
                    [
                        bug
                        for bug in bug_results.get("bugs", [])
                        if bug.get("severity") == "medium"
                    ]
                ),
                "low_count": len(
                    [
                        bug
                        for bug in bug_results.get("bugs", [])
                        if bug.get("severity") == "low"
                    ]
                ),
            }

        except Exception as e:
            logger.error(f"Error in bug detection and fixing: {e}")
            raise

    async def optimize_code(self, request: CodeGenerationRequest) -> Dict[str, Any]:
        """
        Optimize code for performance and best practices.

        Args:
            request: Code generation request

        Returns:
            Dictionary with optimization results
        """
        try:
            # Analyze current code
            analysis_results = await self.code_analyzer.analyze_code(
                code=request.code, language=request.language
            )

            # Generate optimizations
            optimization_results = await self.code_optimizer.optimize_code(
                code=request.code,
                language=request.language,
                analysis_results=analysis_results,
            )

            # Generate optimized code
            optimized_code = await self.deepseek_integration.generate_optimized_code(
                code=request.code,
                optimization_suggestions=optimization_results.get("suggestions", []),
                language=request.language,
            )

            return {
                "original_code": request.code,
                "optimized_code": optimized_code.get("generated_code", request.code),
                "optimization_suggestions": optimization_results.get("suggestions", []),
                "performance_improvements": optimization_results.get(
                    "improvements", {}
                ),
                "confidence": optimized_code.get("confidence", 0.0),
                "performance_score": optimization_results.get("performance_score", 0.0),
                "memory_score": optimization_results.get("memory_score", 0.0),
                "readability_score": optimization_results.get("readability_score", 0.0),
                "maintainability_score": optimization_results.get(
                    "maintainability_score", 0.0
                ),
                "overall_score": optimization_results.get("overall_score", 0.0),
                "total_suggestions": optimization_results.get("total_suggestions", 0),
                "high_priority_count": optimization_results.get(
                    "high_priority_count", 0
                ),
                "medium_priority_count": optimization_results.get(
                    "medium_priority_count", 0
                ),
                "low_priority_count": optimization_results.get("low_priority_count", 0),
            }

        except Exception as e:
            logger.error(f"Error in code optimization: {e}")
            raise

    async def recognize_patterns(
        self, request: CodeGenerationRequest
    ) -> Dict[str, Any]:
        """
        Recognize coding patterns and provide insights.

        Args:
            request: Code generation request

        Returns:
            Dictionary with pattern recognition results
        """
        try:
            # Recognize patterns in the code
            pattern_results = await self.pattern_recognizer.recognize_patterns(
                code=request.code, language=request.language
            )

            # Learn from the patterns if learning is enabled
            if (
                self.config.get("features", {})
                .get("pattern_recognition", {})
                .get("learning_enabled", False)
            ):
                await self.pattern_recognizer.learn_patterns(
                    code=request.code,
                    language=request.language,
                    project_context=request.project_context,
                )

            return {
                "patterns_found": len(pattern_results.get("patterns", [])),
                "patterns": pattern_results.get("patterns", []),
                "insights": pattern_results.get("insights", []),
                "best_practices": pattern_results.get("best_practices", []),
                "anti_patterns": pattern_results.get("anti_patterns", []),
                "design_patterns": pattern_results.get("design_patterns", []),
                "custom_patterns": pattern_results.get("custom_patterns", []),
            }

        except Exception as e:
            logger.error(f"Error in pattern recognition: {e}")
            raise

    async def get_editor_suggestions(
        self, request: CodeGenerationRequest
    ) -> Dict[str, Any]:
        """
        Get real-time editor suggestions for the current code context.

        Args:
            request: Code generation request

        Returns:
            Dictionary with editor suggestions
        """
        try:
            return await self.editor_integration.get_suggestions(
                code=request.code,
                language=request.language,
                cursor_position=request.cursor_position,
                file_path=request.file_path,
            )

        except Exception as e:
            logger.error(f"Error getting editor suggestions: {e}")
            raise

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get the health status of all code generation components.

        Returns:
            Dictionary with health status information
        """
        return {
            "status": "healthy",
            "version": self.config.get("version", "1.0.0"),
            "components": {
                "deepseek_integration": self.deepseek_integration.get_health_status(),
                "code_analyzer": self.code_analyzer.get_health_status(),
                "pattern_recognizer": self.pattern_recognizer.get_health_status(),
                "bug_detector": self.bug_detector.get_health_status(),
                "code_optimizer": self.code_optimizer.get_health_status(),
                "editor_integration": self.editor_integration.get_health_status(),
            },
            "features_enabled": {
                "code_completion": self.config.get("features", {})
                .get("code_completion", {})
                .get("enabled", False),
                "bug_detection": self.config.get("features", {})
                .get("bug_detection", {})
                .get("enabled", False),
                "optimization": self.config.get("features", {})
                .get("optimization", {})
                .get("enabled", False),
                "pattern_recognition": self.config.get("features", {})
                .get("pattern_recognition", {})
                .get("enabled", False),
            },
        }
