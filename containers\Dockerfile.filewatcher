FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
WORKDIR /app
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir watchdog docker requests fastapi uvicorn

FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
WORKDIR /app
COPY --from=builder /opt/venv /opt/venv
COPY core/file_watcher_agent.py /app/file_watcher_agent.py
COPY core/website_generator.py /app/core/website_generator.py
RUN mkdir -p /app/sites /app/core \
 && adduser --system appuser \
 && chown -R appuser:appuser /app
USER appuser
EXPOSE 8090
HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD curl --fail http://localhost:8090/health || exit 1
ENTRYPOINT ["uvicorn", "file_watcher_agent:app", "--host", "0.0.0.0", "--port", "8090"]
