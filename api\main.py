#!/usr/bin/env python3
"""
AI Coding Agent API
Main API service with comprehensive health monitoring using FastAPI
"""

import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from fastapi import Fast<PERSON>I, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="AI Coding Agent API",
    description="Main API service with comprehensive health monitoring",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for health tracking
start_time = datetime.now()
health_status = {
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": start_time.isoformat(),
    "uptime_seconds": 0,
    "services": {}
}


@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint"""
    try:
        # Calculate uptime
        uptime = (datetime.now() - start_time).total_seconds()

        # Check system resources
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")

            # Determine overall status
            status = "healthy"
            if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
                status = "degraded"
            if cpu_percent > 95 or memory.percent > 95 or disk.percent > 95:
                status = "unhealthy"

            metrics = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_mb": memory.available / (1024 * 1024),
                "disk_percent": disk.percent,
                "disk_free_gb": disk.free / (1024 * 1024 * 1024),
            }
        except ImportError:
            # psutil not available, use basic status
            status = "healthy"
            metrics = {
                "cpu_percent": 0,
                "memory_percent": 0,
                "memory_available_mb": 0,
                "disk_percent": 0,
                "disk_free_gb": 0,
            }

        health_response = {
            "status": status,
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": uptime,
            "services": {
                "api": "healthy",
                "system_resources": status,
                "database": "healthy",  # Placeholder
                "redis": "healthy",     # Placeholder
            },
            "metrics": metrics,
            "endpoints": [
                "/health",
                "/api/v1/chat/models",
                "/api/sites/list"
            ]
        }

        # Update global health status
        global health_status
        health_status.update(health_response)

        return health_response

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "ai-coding-agent-api",
        "version": "1.0.0",
        "status": "running",
        "endpoints": [
            "/health",
            "/api/v1/chat/models",
            "/api/sites/list"
        ],
        "documentation": "/docs"
    }


@app.get("/api/v1/chat/models")
async def get_models():
    """Get available AI models"""
    try:
        # Placeholder implementation
        models = [
            {
                "id": "gpt-4",
                "name": "GPT-4",
                "provider": "openai",
                "status": "available"
            },
            {
                "id": "claude-3",
                "name": "Claude-3",
                "provider": "anthropic",
                "status": "available"
            }
        ]
        return {"models": models}
    except Exception as e:
        logger.error(f"Failed to get models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/sites/list")
async def list_sites():
    """List available sites"""
    try:
        # Placeholder implementation
        sites = [
            {
                "id": "site-1",
                "name": "Test Site",
                "status": "running",
                "port": 8080
            }
        ]
        return {"sites": sites}
    except Exception as e:
        logger.error(f"Failed to list sites: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    port = int(os.getenv("API_PORT", "8000"))
    host = os.getenv("API_HOST", "0.0.0.0")

    logger.info(f"Starting AI Coding Agent API on {host}:{port}")
    uvicorn.run(app, host=host, port=port)
