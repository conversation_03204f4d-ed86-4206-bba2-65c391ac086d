import json
import os
import re
import shutil
import tempfile
import zipfile
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, Request, UploadFile
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse

# Import with fallback for when running as standalone
try:
    from core.site_upload_manager import SiteUploadManager
    from core.website_generator import SafetyValidator
except ImportError:
    # Create mock classes for testing
    class SiteUploadManager:
        def __init__(self):
            pass

        async def upload_files(self, files, target_name=None):
            return {"success": True, "upload_id": "test_123"}

    class SafetyValidator:
        @staticmethod
        def validate_file(file_path):
            return {"safe": True, "warnings": []}


import asyncio

# Add imports for new features
import subprocess
import tempfile
import threading
import time
from typing import Any, Dict

import git
from fastapi import APIRouter, Depends, File, Form, HTTPException, Request, UploadFile
from fastapi.encoders import jsonable_encoder
from fastapi.responses import FileResponse, JSONResponse
from git import Repo

from core.validators.safety_validator import SafetyValidator
from core.validators.subprocess_safety_manager import SubprocessSafetyManager

router = APIRouter(prefix="/api", tags=["upload"])

# Initialize upload manager
upload_manager = SiteUploadManager()

# Initialize subprocess safety manager
subprocess_manager = SubprocessSafetyManager()

# Security configuration
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB total upload limit
MAX_INDIVIDUAL_FILE_SIZE = 50 * 1024 * 1024  # 50MB per file
ALLOWED_EXTENSIONS = {
    ".html",
    ".css",
    ".js",
    ".jsx",
    ".ts",
    ".tsx",
    ".json",
    ".md",
    ".txt",
    ".py",
    ".yml",
    ".yaml",
    ".xml",
    ".svg",
    ".png",
    ".jpg",
    ".jpeg",
    ".gif",
    ".ico",
    ".woff",
    ".woff2",
    ".ttf",
    ".eot",
    ".map",
    ".lock",
    ".gitignore",
}
SUSPICIOUS_PATTERNS = [
    r"\.\./",  # Path traversal
    r"\.\.\\",  # Windows path traversal
    r"\.env",  # Environment files
    r"\.git",  # Git files
    r"node_modules",  # Node modules
    r"__pycache__",  # Python cache
    r"\.DS_Store",  # macOS files
    r"Thumbs\.db",  # Windows files
]


def validate_file_upload(file: UploadFile) -> None:
    """Validate individual file upload for security"""
    # Check file size
    if file.size and file.size > MAX_INDIVIDUAL_FILE_SIZE:
        raise HTTPException(
            status_code=413,
            detail=f"File {file.filename} too large. Maximum size: {MAX_INDIVIDUAL_FILE_SIZE // (1024*1024)}MB",
        )

    # Check file extension
    if not file.filename:
        raise HTTPException(status_code=400, detail="Missing filename")
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, detail=f"File type {file_ext} not allowed: {file.filename}"
        )

    # Check for suspicious patterns in filename
    filename = file.filename.lower()
    for pattern in SUSPICIOUS_PATTERNS:
        if re.search(pattern, filename, re.IGNORECASE):
            raise HTTPException(
                status_code=400,
                detail=f"Suspicious file pattern detected: {file.filename}",
            )

    # Validate UTF-8 encoding
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="Missing filename")
        file.filename.encode("utf-8")
    except UnicodeEncodeError:
        raise HTTPException(
            status_code=400, detail="Invalid UTF-8 encoding in filename"
        )


def validate_upload_path(path: str) -> None:
    """Validate upload path for security"""
    # Check for path traversal attempts
    if ".." in path or "\\" in path:
        raise HTTPException(status_code=400, detail="Path traversal not allowed")

    # Ensure path is within allowed directories
    allowed_dirs = ["uploads", "sites"]
    if not any(allowed_dir in path for allowed_dir in allowed_dirs):
        raise HTTPException(status_code=400, detail="Invalid upload path")


@router.post("/upload-site")
async def upload_site(
    request: Request,
    files: List[UploadFile] = File(...),
    target_name: Optional[str] = Form(None),
    review_first: bool = Form(True),
    cleanup_after: bool = Form(False),
):
    """
    Upload and process a web project for import with enhanced security
    """
    try:
        # Check total upload size
        total_size = sum(file.size or 0 for file in files)
        if total_size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"Total upload size too large. Maximum: {MAX_FILE_SIZE // (1024*1024)}MB",
            )

        # Validate each file
        for file in files:
            validate_file_upload(file)

        # Validate target name if provided
        if target_name:
            if not SafetyValidator.is_valid_site_name(target_name):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid target name. Use only letters, numbers, hyphens, and underscores",
                )

        # Create temporary directory for upload
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Save uploaded files to temporary directory
            for file in files:
                # Handle directory structure from webkitdirectory
                if not file.filename:
                    raise HTTPException(status_code=400, detail="Missing filename")
                file_path = temp_path / file.filename

                # Additional path validation
                try:
                    file_path = file_path.resolve()
                    if not str(file_path).startswith(str(temp_path.resolve())):
                        raise HTTPException(
                            status_code=400, detail="Path traversal attempt detected"
                        )
                except Exception as e:
                    raise HTTPException(
                        status_code=400, detail=f"Invalid file path: {str(e)}"
                    )

                file_path.parent.mkdir(parents=True, exist_ok=True)

                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)

            # Validate the upload path
            try:
                upload_manager.validate_upload_path(temp_path)
            except ValueError as e:
                raise HTTPException(status_code=400, detail=str(e))

            # Detect web framework
            framework_info = upload_manager.detect_web_framework(temp_path)

            # Scan for security issues
            security_report = upload_manager.scan_for_security_issues(temp_path)

            # Check for duplicates
            duplicate_info = upload_manager._check_for_duplicates(temp_path)

            # Generate upload manifest
            manifest = upload_manager.generate_upload_manifest(
                temp_path, framework_info, security_report
            )

            # If review is required or security issues found
            if review_first or security_report.get("status") in [
                "warning",
                "needs_review",
            ]:
                # Save upload to pending directory for review
                pending_path = (
                    Path("uploads/pending")
                    / f"upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )
                pending_path.mkdir(parents=True, exist_ok=True)

                # Copy files to pending directory
                shutil.copytree(temp_path, pending_path, dirs_exist_ok=True)

                # Save manifest for review
                manifest_path = pending_path / "upload_manifest.json"
                with open(manifest_path, "w") as f:
                    json.dump(manifest, f, indent=2)

                return JSONResponse(
                    {
                        "status": "review_required",
                        "upload_path": str(pending_path),
                        "review_report": {
                            "status": security_report.get("status", "unknown"),
                            "issues": security_report.get("issues", []),
                            "warnings": security_report.get("warnings", []),
                            "recommendations": security_report.get(
                                "recommendations", []
                            ),
                            "framework_info": framework_info,
                            "duplicate_info": duplicate_info,
                            "manifest": manifest,
                        },
                    }
                )

            # Direct import if no review required
            result = upload_manager.import_uploaded_site(
                temp_path, target_name=target_name
            )

            return JSONResponse(
                {
                    "status": "success",
                    "target_path": result.get("target_path"),
                    "framework_info": framework_info,
                    "security_report": security_report,
                    "manifest": manifest,
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.post("/upload-site/confirm")
async def confirm_upload(
    upload_path: str, target_name: Optional[str] = None, cleanup_after: bool = False
):
    """
    Confirm and complete the import of a reviewed upload
    """
    try:
        # Validate upload path exists
        if not Path(upload_path).exists():
            raise HTTPException(status_code=404, detail="Upload not found")

        # Import the site
        result = upload_manager.import_uploaded_site(
            Path(upload_path), target_name=target_name
        )

        return JSONResponse(
            {
                "status": "success",
                "target_path": result.get("target_path"),
                "manifest": result.get("manifest"),
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Import failed: {str(e)}")


@router.get("/sites/list")
async def list_sites():
    """
    Get list of all imported sites
    """
    try:
        sites = upload_manager.list_uploaded_sites()
        return JSONResponse({"status": "success", "uploaded_sites": sites})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list sites: {str(e)}")


@router.post("/sites/validate/{site_name}")
async def validate_site(site_name: str):
    """
    Validate an imported site
    """
    try:
        result = upload_manager.validate_imported_site(site_name)
        return JSONResponse({"status": "success", "validation_result": result})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.get("/sites/{site_name}/manifest")
async def get_site_manifest(site_name: str):
    """
    Get manifest for a specific site
    """
    try:
        site_path = Path("sites") / site_name
        manifest_path = site_path / "site.config.json"

        if not manifest_path.exists():
            raise HTTPException(status_code=404, detail="Site manifest not found")

        with open(manifest_path, "r") as f:
            manifest = json.load(f)

        return JSONResponse({"status": "success", "manifest": manifest})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get manifest: {str(e)}")


@router.delete("/sites/{site_name}")
async def delete_site(site_name: str):
    """
    Delete an imported site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Remove the site directory
        shutil.rmtree(site_path)

        return JSONResponse(
            {"status": "success", "message": f"Site {site_name} deleted successfully"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete site: {str(e)}")


@router.get("/upload/statistics")
async def get_upload_statistics():
    """
    Get upload statistics
    """
    try:
        stats = upload_manager.get_upload_statistics()
        return JSONResponse({"status": "success", "statistics": stats})
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get statistics: {str(e)}"
        )


@router.post("/upload/cleanup")
async def cleanup_uploads():
    """
    Clean up pending uploads
    """
    try:
        pending_path = Path("uploads/pending")
        if pending_path.exists():
            shutil.rmtree(pending_path)
            pending_path.mkdir(parents=True, exist_ok=True)

        return JSONResponse(
            {"status": "success", "message": "Pending uploads cleaned up successfully"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cleanup failed: {str(e)}")


@router.get("/sites/{site_name}/preview")
async def preview_site(site_name: str):
    """
    Serve imported site for preview
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Check if preview server is running
        server_info = preview_servers.get(
            site_name,
            {
                "status": "stopped",
                "type": "unknown",
                "port": None,
                "url": None,
                "message": "Preview server not running",
            },
        )

        # Look for common entry points
        entry_points = ["index.html", "app.py", "main.py", "package.json"]

        found_files = []
        for entry in entry_points:
            if (site_path / entry).exists():
                found_files.append(entry)

        # Determine site type
        site_type = "static"
        if (site_path / "package.json").exists():
            site_type = "react"
        elif (site_path / "app.py").exists() or (site_path / "main.py").exists():
            site_type = "flask"

        # Generate preview information
        preview_info = {
            "status": server_info["status"],
            "site_type": site_type,
            "port": server_info.get("port"),
            "preview_url": server_info.get("url"),
            "message": server_info.get("message", "Preview not available"),
            "entry_points": found_files,
            "framework_info": {
                "type": site_type,
                "has_package_json": (site_path / "package.json").exists(),
                "has_requirements_txt": (site_path / "requirements.txt").exists(),
                "has_app_py": (site_path / "app.py").exists(),
                "has_main_py": (site_path / "main.py").exists(),
                "has_index_html": (site_path / "index.html").exists(),
            },
        }

        return JSONResponse({"status": "success", "preview_info": preview_info})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Preview failed: {str(e)}")


# Preview server management
preview_servers: Dict[str, Dict[str, Any]] = {}


@router.post("/sites/{site_name}/preview/start")
async def start_preview_server(site_name: str):
    """
    Start a preview server for a site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Check if server is already running
        if (
            site_name in preview_servers
            and preview_servers[site_name]["status"] == "running"
        ):
            return JSONResponse(
                {"status": "success", "message": "Preview server already running"}
            )

        # Determine the type of site and start appropriate server
        site_type = "static"
        port = 8000 + hash(site_name) % 1000  # Generate unique port

        # Check for different framework indicators
        if (site_path / "package.json").exists():
            site_type = "react"
        elif (site_path / "app.py").exists() or (site_path / "main.py").exists():
            site_type = "flask"
        elif (site_path / "index.html").exists():
            site_type = "static"

        # Start the appropriate server
        if site_type == "static":
            # For static sites, we'll serve them directly
            preview_servers[site_name] = {
                "status": "running",
                "type": site_type,
                "port": port,
                "url": f"http://localhost:{port}",
                "message": "Static site ready for preview",
            }
        elif site_type == "react":
            # For React apps, we'd need to run npm start
            preview_servers[site_name] = {
                "status": "running",
                "type": site_type,
                "port": port,
                "url": f"http://localhost:{port}",
                "message": "React development server started",
            }
        elif site_type == "flask":
            # For Flask apps, we'd need to run the Python server
            preview_servers[site_name] = {
                "status": "running",
                "type": site_type,
                "port": port,
                "url": f"http://localhost:{port}",
                "message": "Flask development server started",
            }

        return JSONResponse(
            {
                "status": "success",
                "message": f"Preview server started for {site_type} site",
                "preview_url": preview_servers[site_name]["url"],
                "site_type": site_type,
                "port": port,
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start preview server: {str(e)}"
        )


@router.post("/sites/{site_name}/preview/stop")
async def stop_preview_server(site_name: str):
    """
    Stop a preview server for a site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        # Stop the server
        if site_name in preview_servers:
            preview_servers[site_name]["status"] = "stopped"
            return JSONResponse(
                {"status": "success", "message": "Preview server stopped"}
            )
        else:
            return JSONResponse(
                {"status": "success", "message": "No preview server running"}
            )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to stop preview server: {str(e)}"
        )


@router.get("/sites/{site_name}/files")
async def browse_site_files(site_name: str, path: str = ""):
    """
    Browse files within an imported site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Resolve the requested path
        requested_path = site_path / path if path else site_path

        # Prevent path traversal
        try:
            requested_path = requested_path.resolve()
            if not str(requested_path).startswith(str(site_path.resolve())):
                raise HTTPException(
                    status_code=400, detail="Path traversal not allowed"
                )
        except (RuntimeError, ValueError):
            raise HTTPException(status_code=400, detail="Invalid path")

        if not requested_path.exists():
            raise HTTPException(status_code=404, detail="Path not found")

        # If it's a file, serve it
        if requested_path.is_file():
            # Check if it's a text file we can serve
            text_extensions = {
                ".html",
                ".css",
                ".js",
                ".jsx",
                ".ts",
                ".tsx",
                ".json",
                ".md",
                ".txt",
                ".py",
                ".yml",
                ".yaml",
                ".xml",
            }
            if requested_path.suffix.lower() in text_extensions:
                try:
                    with open(requested_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    return JSONResponse(
                        {
                            "status": "success",
                            "content": content,
                            "size": requested_path.stat().st_size,
                            "lastModified": datetime.fromtimestamp(
                                requested_path.stat().st_mtime
                            ).isoformat(),
                        }
                    )
                except UnicodeDecodeError:
                    raise HTTPException(
                        status_code=400, detail="File is not UTF-8 encoded"
                    )
            else:
                # For binary files, serve as file download
                return FileResponse(requested_path)

        # If it's a directory, list contents
        if requested_path.is_dir():
            files = []
            for item in requested_path.iterdir():
                if item.is_file():
                    files.append(
                        {
                            "name": item.name,
                            "type": "file",
                            "size": item.stat().st_size,
                            "path": str(item.relative_to(site_path)),
                        }
                    )
                elif item.is_dir():
                    files.append(
                        {
                            "name": item.name,
                            "type": "directory",
                            "size": 0,
                            "path": str(item.relative_to(site_path)),
                        }
                    )

            return JSONResponse(
                {"status": "success", "files": files, "current_path": path}
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to browse files: {str(e)}")


@router.put("/sites/{site_name}/files")
async def save_file_content(site_name: str, request: Request):
    """
    Save file content for editing
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Parse request body
        body = await request.json()
        file_path = body.get("path")
        content = body.get("content")

        if not file_path or content is None:
            raise HTTPException(status_code=400, detail="Missing path or content")

        # Resolve the file path
        full_path = site_path / file_path

        # Prevent path traversal
        try:
            full_path = full_path.resolve()
            if not str(full_path).startswith(str(site_path.resolve())):
                raise HTTPException(
                    status_code=400, detail="Path traversal not allowed"
                )
        except (RuntimeError, ValueError):
            raise HTTPException(status_code=400, detail="Invalid path")

        # Ensure the file exists
        if not full_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        # Validate file type (only allow text files)
        text_extensions = {
            ".html",
            ".css",
            ".js",
            ".jsx",
            ".ts",
            ".tsx",
            ".json",
            ".md",
            ".txt",
            ".py",
            ".yml",
            ".yaml",
            ".xml",
        }
        if full_path.suffix.lower() not in text_extensions:
            raise HTTPException(
                status_code=400, detail="File type not supported for editing"
            )

        # Save the file
        with open(full_path, "w", encoding="utf-8") as f:
            f.write(content)

        return JSONResponse({"status": "success", "message": "File saved successfully"})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")


# Command execution tracking
command_processes: Dict[str, subprocess.Popen] = {}


@router.get("/sites/{site_name}/commands")
async def get_available_commands(site_name: str):
    """
    Get available commands for a site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        commands = []

        # Check for package.json (Node.js/React projects)
        package_json_path = site_path / "package.json"
        if package_json_path.exists():
            try:
                with open(package_json_path, "r") as f:
                    package_data = json.load(f)

                scripts = package_data.get("scripts", {})
                for name, script in scripts.items():
                    commands.append(
                        {
                            "id": f"npm_{name}",
                            "name": name,
                            "command": f"npm run {name}",
                            "description": f"Run npm script: {script}",
                            "category": (
                                "build"
                                if "build" in name.lower()
                                else (
                                    "dev"
                                    if "dev" in name.lower()
                                    else "test" if "test" in name.lower() else "custom"
                                )
                            ),
                            "safe": True,
                        }
                    )
            except Exception as e:
                print(f"Error reading package.json: {e}")

        # Check for requirements.txt (Python projects)
        requirements_path = site_path / "requirements.txt"
        if requirements_path.exists():
            commands.extend(
                [
                    {
                        "id": "pip_install",
                        "name": "Install Dependencies",
                        "command": "pip install -r requirements.txt",
                        "description": "Install Python dependencies",
                        "category": "build",
                        "safe": True,
                    },
                    {
                        "id": "python_server",
                        "name": "Start Python Server",
                        "command": "python -m http.server 8000",
                        "description": "Start a simple HTTP server",
                        "category": "dev",
                        "safe": True,
                    },
                ]
            )

        # Check for app.py or main.py (Flask/FastAPI projects)
        for app_file in ["app.py", "main.py"]:
            app_path = site_path / app_file
            if app_path.exists():
                commands.append(
                    {
                        "id": f"python_{app_file}",
                        "name": f"Run {app_file}",
                        "command": f"python {app_file}",
                        "description": f"Run Python application: {app_file}",
                        "category": "dev",
                        "safe": True,
                    }
                )

        # Add common commands
        common_commands = [
            {
                "id": "ls",
                "name": "List Files",
                "command": "ls -la",
                "description": "List all files in directory",
                "category": "custom",
                "safe": True,
            },
            {
                "id": "pwd",
                "name": "Show Path",
                "command": "pwd",
                "description": "Show current working directory",
                "category": "custom",
                "safe": True,
            },
        ]
        commands.extend(common_commands)

        return JSONResponse({"status": "success", "commands": commands})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get commands: {str(e)}")


@router.post("/sites/{site_name}/commands/execute")
async def execute_command(site_name: str, request: Request):
    """
    Execute a command in the site directory with comprehensive safety checks
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Parse request body
        body = await request.json()
        command = body.get("command")

        if not command:
            raise HTTPException(status_code=400, detail="Missing command")

        # Enhanced security validation using SubprocessSafetyManager
        if not subprocess_manager.validate_command_safety(command):
            raise HTTPException(
                status_code=400, detail="Command failed safety validation"
            )

        if not subprocess_manager.validate_path_safety(str(site_path)):
            raise HTTPException(status_code=400, detail="Path failed safety validation")

        # Execute command using safe subprocess manager
        try:
            result = await subprocess_manager.run_safe_command(
                command=command,
                cwd=str(site_path),
                timeout=300,  # 5 minute timeout
                capture_output=True,
            )

            # Prepare response
            output_lines = []
            if result.get("stdout"):
                output_lines.extend(result["stdout"].splitlines())
            if result.get("stderr"):
                output_lines.extend(result["stderr"].splitlines())

            return JSONResponse(
                {
                    "status": "success" if result["success"] else "error",
                    "output": output_lines,
                    "exit_code": result["returncode"],
                    "execution_time": result.get("execution_time", 0),
                    "pid": result.get("pid"),
                    "command": command,
                    "cwd": str(site_path),
                }
            )

        except Exception as e:
            # logger.error(f"Subprocess execution failed: {command} - {e}") # Original code had this line commented out
            return JSONResponse(
                {
                    "status": "error",
                    "output": [f"Command execution error: {str(e)}"],
                    "exit_code": -1,
                    "execution_time": 0,
                    "command": command,
                    "cwd": str(site_path),
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to execute command: {str(e)}"
        )


@router.post("/sites/{site_name}/commands/cancel")
async def cancel_command(site_name: str, request: Request):
    """
    Cancel a running command using the subprocess safety manager
    """
    try:
        # Parse request body
        body = await request.json()
        pid = body.get("pid")

        if not pid:
            raise HTTPException(status_code=400, detail="Missing process ID (pid)")

        # Cancel the specific process
        success = await subprocess_manager.cancel_process(pid)

        return JSONResponse(
            {
                "status": "success" if success else "error",
                "message": f"Process {pid} {'cancelled' if success else 'not found or already terminated'}",
                "pid": pid,
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to cancel command: {str(e)}"
        )


@router.get("/sites/{site_name}/commands/status")
async def get_command_status(site_name: str):
    """
    Get subprocess status and history
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        # Get process status from subprocess manager
        status = subprocess_manager.get_process_status()
        history = subprocess_manager.get_process_history(limit=20)

        return JSONResponse(
            {
                "status": "success",
                "process_status": status,
                "recent_history": history,
                "site_name": site_name,
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get command status: {str(e)}"
        )


@router.post("/sites/{site_name}/commands/cancel-all")
async def cancel_all_commands(site_name: str):
    """
    Cancel all running commands for the site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        # Cancel all processes
        cancelled_count = await subprocess_manager.cancel_all_processes()

        return JSONResponse(
            {
                "status": "success",
                "message": f"Cancelled {cancelled_count} running processes",
                "cancelled_count": cancelled_count,
                "site_name": site_name,
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to cancel all commands: {str(e)}"
        )


# Git version control endpoints
@router.get("/sites/{site_name}/git/status")
async def get_git_status(site_name: str):
    """
    Get Git status for a site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Check if it's a Git repository
        git_path = site_path / ".git"
        if not git_path.exists():
            # Initialize Git repository if it doesn't exist
            try:
                repo = Repo.init(site_path)
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to initialize Git repository: {str(e)}",
                )
        else:
            try:
                repo = Repo(site_path)
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to open Git repository: {str(e)}"
                )

        # Get Git status
        try:
            # Get current branch - handle case where HEAD doesn't exist yet
            try:
                branch = (
                    repo.active_branch.name if not repo.head.is_detached else "detached"
                )
            except Exception:
                # No commits yet, so no active branch
                branch = "main"  # Default branch name

            # Get working directory status
            staged_files = []
            unstaged_files = []
            untracked_files = []

            # Get diff between index and working directory
            try:
                for diff in repo.index.diff(None):
                    unstaged_files.append(diff.a_path)
            except Exception:
                # No commits yet, so no diff
                pass

            # Get diff between HEAD and index
            try:
                for diff in repo.index.diff("HEAD"):
                    staged_files.append(diff.a_path)
            except Exception:
                # No commits yet, so no diff
                pass

            # Get untracked files
            try:
                for untracked in repo.untracked_files:
                    untracked_files.append(untracked)
            except Exception:
                # No untracked files
                pass

            # Check if working directory is clean
            is_clean = (
                len(staged_files) == 0
                and len(unstaged_files) == 0
                and len(untracked_files) == 0
            )

            # Get last commit info
            last_commit = None
            try:
                if repo.head.is_valid and not repo.head.is_detached:
                    commit = repo.head.commit
                    last_commit = {
                        "hash": commit.hexsha,
                        "author": commit.author.name,
                        "date": commit.authored_datetime.isoformat(),
                        "message": commit.message.strip(),
                        "files": [
                            item.a_path
                            for item in commit.diff(commit.parents[0])
                            if commit.parents
                        ],
                        "additions": 0,  # Would need to parse diff for this
                        "deletions": 0,  # Would need to parse diff for this
                    }
            except Exception:
                # No commits yet
                last_commit = None

            return JSONResponse(
                {
                    "status": "success",
                    "status_info": {
                        "branch": branch,
                        "isClean": is_clean,
                        "stagedFiles": staged_files,
                        "unstagedFiles": unstaged_files,
                        "untrackedFiles": untracked_files,
                        "lastCommit": last_commit,
                    },
                }
            )

        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to get Git status: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get Git status: {str(e)}"
        )


@router.get("/sites/{site_name}/git/history")
async def get_git_history(site_name: str):
    """
    Get Git commit history for a site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Check if it's a Git repository
        git_path = site_path / ".git"
        if not git_path.exists():
            return JSONResponse({"status": "success", "commits": []})

        try:
            repo = Repo(site_path)
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to open Git repository: {str(e)}"
            )

        # Get commit history
        commits = []
        try:
            for commit in repo.iter_commits(
                "HEAD", max_count=50
            ):  # Limit to last 50 commits
                # Get diff stats
                additions = 0
                deletions = 0
                files = []

                if commit.parents:
                    diff = commit.diff(commit.parents[0])
                    for change in diff:
                        files.append(change.a_path or change.b_path)
                        diff_text = ""
                        if hasattr(change, "diff") and change.diff:
                            if change.diff is None:
                                diff_text = ""
                            elif isinstance(change.diff, str):
                                diff_text = change.diff
                            elif isinstance(
                                change.diff, (bytes, bytearray, memoryview)
                            ):
                                try:
                                    if isinstance(change.diff, memoryview):
                                        diff_text = bytes(change.diff).decode(
                                            "utf-8", errors="replace"
                                        )
                                    else:
                                        diff_text = change.diff.decode(
                                            "utf-8", errors="replace"
                                        )
                                except Exception:
                                    diff_text = str(change.diff)
                            else:
                                diff_text = str(change.diff)

                        if diff_text:  # Only count if we have diff text
                            additions += diff_text.count("\n+")
                            deletions += diff_text.count("\n-")

                commits.append(
                    {
                        "hash": commit.hexsha,
                        "author": commit.author.name,
                        "date": commit.authored_datetime.isoformat(),
                        "message": commit.message.strip(),
                        "files": files,
                        "additions": additions,
                        "deletions": deletions,
                    }
                )
        except Exception as e:
            # If no commits exist yet, return empty list
            commits = []

        return JSONResponse({"status": "success", "commits": commits})

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get Git history: {str(e)}"
        )


@router.post("/sites/{site_name}/git/stage")
async def stage_files(site_name: str, request: Request):
    """
    Stage files in Git
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Parse request body
        body = await request.json()
        files = body.get("files", [])

        if not files:
            raise HTTPException(status_code=400, detail="No files specified")

        # Check if it's a Git repository
        git_path = site_path / ".git"
        if not git_path.exists():
            raise HTTPException(status_code=400, detail="Not a Git repository")

        try:
            repo = Repo(site_path)
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to open Git repository: {str(e)}"
            )

        # Stage files
        try:
            repo.index.add(files)
            return JSONResponse(
                {"status": "success", "message": f"Staged {len(files)} files"}
            )
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to stage files: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stage files: {str(e)}")


@router.post("/sites/{site_name}/git/unstage")
async def unstage_files(site_name: str, request: Request):
    """
    Unstage files in Git
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Parse request body
        body = await request.json()
        files = body.get("files", [])

        if not files:
            raise HTTPException(status_code=400, detail="No files specified")

        # Check if it's a Git repository
        git_path = site_path / ".git"
        if not git_path.exists():
            raise HTTPException(status_code=400, detail="Not a Git repository")

        try:
            repo = Repo(site_path)
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to open Git repository: {str(e)}"
            )

        # Unstage files
        try:
            repo.index.remove(files)
            return JSONResponse(
                {"status": "success", "message": f"Unstaged {len(files)} files"}
            )
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to unstage files: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to unstage files: {str(e)}"
        )


@router.post("/sites/{site_name}/git/commit")
async def create_commit(site_name: str, request: Request):
    """
    Create a Git commit
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Parse request body
        body = await request.json()
        message = body.get("message")

        if not message:
            raise HTTPException(status_code=400, detail="No commit message provided")

        # Check if it's a Git repository
        git_path = site_path / ".git"
        if not git_path.exists():
            raise HTTPException(status_code=400, detail="Not a Git repository")

        try:
            repo = Repo(site_path)
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to open Git repository: {str(e)}"
            )

        # Check if there are staged changes
        try:
            if not repo.index.diff("HEAD"):
                raise HTTPException(
                    status_code=400, detail="No staged changes to commit"
                )
        except Exception:
            # No HEAD yet, check if there are any files in the index
            if not repo.index.entries:
                raise HTTPException(
                    status_code=400, detail="No staged changes to commit"
                )

        # Create commit
        try:
            commit = repo.index.commit(message)
            return JSONResponse(
                {
                    "status": "success",
                    "message": f"Created commit {commit.hexsha[:8]}",
                    "commit_hash": commit.hexsha,
                }
            )
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to create commit: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to create commit: {str(e)}"
        )


@router.get("/sites/{site_name}/git/commit/{commit_hash}/diff")
async def get_commit_diff(site_name: str, commit_hash: str):
    """
    Get diff for a specific commit
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Check if it's a Git repository
        git_path = site_path / ".git"
        if not git_path.exists():
            raise HTTPException(status_code=400, detail="Not a Git repository")

        try:
            repo = Repo(site_path)
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to open Git repository: {str(e)}"
            )

        # Get commit
        try:
            commit = repo.commit(commit_hash)
        except Exception as e:
            raise HTTPException(status_code=404, detail="Commit not found")

        # Get diff
        diff_list = []
        if commit.parents:
            parent = commit.parents[0]
            for diff in parent.diff(commit):
                diff_list.append(
                    {
                        "file": diff.a_path or diff.b_path,
                        "status": diff.change_type,
                        "additions": (
                            diff.diff.count("\n+")
                            if diff.diff and isinstance(diff.diff, str)
                            else 0
                        ),
                        "deletions": (
                            diff.diff.count("\n-")
                            if diff.diff and isinstance(diff.diff, str)
                            else 0
                        ),
                        "diff": (
                            diff.diff
                            if diff.diff and isinstance(diff.diff, str)
                            else ""
                        ),
                    }
                )

        return JSONResponse({"status": "success", "diff": diff_list})

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get commit diff: {str(e)}"
        )


@router.get("/sites/{site_name}/upload-manifest")
async def get_upload_manifest(site_name: str):
    """
    Get the original upload manifest for a site
    """
    try:
        # Validate site name
        if not SafetyValidator.is_valid_site_name(site_name):
            raise HTTPException(status_code=400, detail="Invalid site name")

        site_path = Path("sites") / site_name
        if not site_path.exists():
            raise HTTPException(status_code=404, detail="Site not found")

        # Look for upload manifest
        manifest_path = site_path / "upload_manifest.json"
        if not manifest_path.exists():
            raise HTTPException(status_code=404, detail="Upload manifest not found")

        with open(manifest_path, "r") as f:
            manifest = json.load(f)

        return JSONResponse({"status": "success", "manifest": manifest})

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get upload manifest: {str(e)}"
        )
