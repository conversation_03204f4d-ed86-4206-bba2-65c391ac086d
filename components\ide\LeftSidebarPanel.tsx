import React, { memo } from 'react';
import { IDESidebarUnified } from './IDESidebarUnified';

interface LeftSidebarPanelProps {
  onSiteSelect: (siteName: string) => void;
  onSitePreview: (siteName: string) => void;
  onSiteValidate: (siteName: string) => void;
  onSiteOpen: (siteName: string) => void;
  onSiteManifest: (siteName: string) => void;
  onSiteCommands: (siteName: string) => void;
  onSiteGit: (siteName: string) => void;
}

export const LeftSidebarPanel = memo<LeftSidebarPanelProps>((props) => {
  return (
    <div className="h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
      <IDESidebarUnified {...props} />
    </div>
  );
});
