{"search_budget": 100, "performance_threshold": 0.8, "complexity_penalty": 0.1, "task_specificity_threshold": 0.7, "architecture_constraints": {"max_layers": 50, "max_parameters": 1000000000, "max_memory": 32000}, "task_analysis": {"min_data_points": 10, "trend_analysis_window": 20, "volatility_threshold": 0.1}, "architecture_generation": {"base_layers": 10, "complexity_multiplier": 20, "layer_types": ["transformer", "attention", "convolutional", "recurrent"], "activation_functions": ["gelu", "relu", "tanh", "sigmoid"]}, "performance_evaluation": {"accuracy_weight": 0.5, "speed_weight": 0.3, "efficiency_weight": 0.2, "base_performance": {"accuracy": 0.7, "speed": 1.0, "efficiency": 1.0}}, "complexity_optimization": {"layer_complexity_weight": 0.6, "size_complexity_weight": 0.4, "max_complexity": 1.0}, "architecture_selection": {"top_k_architectures": 5, "diversity_threshold": 0.3, "performance_weight": 0.7, "complexity_weight": 0.3}, "task_specific_optimization": {"code_generation": {"preferred_layers": ["transformer", "attention"], "complexity_bonus": 0.1}, "instruction_following": {"preferred_layers": ["transformer"], "complexity_bonus": 0.05}, "conversation": {"preferred_layers": ["recurrent", "attention"], "complexity_bonus": 0.0}}}