{"root": true, "env": {"browser": true, "es2021": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "prettier"], "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended", "plugin:jsx-a11y/recommended", "plugin:prettier/recommended"], "rules": {"@typescript-eslint/explicit-module-boundary-types": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/jsx-filename-extension": [1, {"extensions": [".tsx", ".jsx"]}], "prettier/prettier": ["error"], "no-console": "warn"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["node_modules/", ".venv/", "dist/", "build/", "coverage/", "test_dist/", "*.config.js", "*.config.ts"]}