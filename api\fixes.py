"""
Utility functions for version parsing and compatibility fixes.
"""

import re
from typing import Union


def parse_version(version_string: Union[str, bytes]) -> tuple:
    """
    Parse a version string into a tuple of integers.

    This is a simplified version of packaging.version.parse that returns
    a tuple of integers for easy comparison.

    Parameters
    ----------
    version_string : str or bytes
        Version string to parse (e.g., "1.2.3", "2.0.0a1")

    Returns
    -------
    tuple
        Tuple of integers representing the version components

    Examples
    --------
    >>> parse_version("1.2.3")
    (1, 2, 3)
    >>> parse_version("2.0.0a1")
    (2, 0, 0)
    """
    if isinstance(version_string, bytes):
        version_string = version_string.decode('utf-8')

    # Remove any pre-release suffixes (alpha, beta, rc, etc.)
    version_string = re.sub(r'[a-zA-Z].*$', '', version_string)

    # Split by dots and convert to integers
    parts = version_string.split('.')
    version_tuple = []

    for part in parts:
        try:
            version_tuple.append(int(part))
        except ValueError:
            # If we can't parse a part, skip it
            continue

    return tuple(version_tuple)


def version_compare(version1: Union[str, bytes], version2: Union[str, bytes]) -> int:
    """
    Compare two version strings.

    Parameters
    ----------
    version1 : str or bytes
        First version string
    version2 : str or bytes
        Second version string

    Returns
    -------
    int
        -1 if version1 < version2, 0 if equal, 1 if version1 > version2
    """
    v1 = parse_version(version1)
    v2 = parse_version(version2)

    if v1 < v2:
        return -1
    elif v1 > v2:
        return 1
    else:
        return 0


__all__ = ["parse_version", "version_compare"]
