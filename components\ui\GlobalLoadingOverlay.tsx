import React from 'react';
import { useLoading } from '@/contexts/LoadingContext';
import { LoadingSpinner } from './LoadingSpinner';

export const GlobalLoadingOverlay: React.FC = () => {
  const { isAnyLoading, loadingStates } = useLoading();

  if (!isAnyLoading) {
    return null;
  }

  // Get the first loading message
  const firstLoadingKey = Object.keys(loadingStates).find(key => loadingStates[key].isLoading);
  const loadingMessage = firstLoadingKey ? loadingStates[firstLoadingKey].message : 'Loading...';

  return (
    <div className="loading-overlay">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl animate-scale-in">
        <LoadingSpinner
          size="lg"
          variant="primary"
          message={loadingMessage}
          showMessage={true}
        />
      </div>
    </div>
  );
};
