#!/usr/bin/env python3
"""
ASTCodeGenerator - Main AST-based code generation engine

This module provides the core AST-based code generation capabilities including:
1. AST manipulation and transformation
2. Code generation from AST nodes
3. AST merging and integration
4. Refactoring capabilities
5. Multi-language support
6. Template-based generation
"""

import ast
import copy
import json
import logging
import re
import time
import warnings
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import astor

from core.code_generation.ast_manipulator import ASTManipulator
from core.code_generation.code_analyzer import CodeAnalyzer
from core.code_generation.pattern_matcher import CodePatternMatcher

# Suppress astor deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="astor")


logger = logging.getLogger(__name__)


class GenerationStrategy(Enum):
    """Code generation strategies"""

    TEMPLATE_BASED = "template_based"
    AST_MANIPULATION = "ast_manipulation"
    PATTERN_BASED = "pattern_based"
    HYBRID = "hybrid"


class CodeLanguage(Enum):
    """Supported programming languages"""

    PYTHON = "python"
    TYPESCRIPT = "typescript"
    JAVASCRIPT = "javascript"
    JSX = "jsx"
    TSX = "tsx"


@dataclass
class GenerationContext:
    """Context for code generation"""

    language: CodeLanguage
    target_file: str
    existing_code: Optional[str] = None
    requirements: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    conventions: Dict[str, Any] = field(default_factory=dict)
    imports: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class GeneratedCode:
    """Represents generated code with metadata"""

    code: str
    language: CodeLanguage
    ast_tree: Optional[ast.AST] = None
    imports: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    complexity_score: float = 0.0
    quality_score: float = 0.0
    generation_time: float = 0.0
    strategy_used: GenerationStrategy = GenerationStrategy.HYBRID
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


class ASTCodeGenerator:
    """
    Main AST-based code generation engine that provides precise, maintainable code generation.
    """

    def __init__(self, config_path: str = "config/ast_generator_config.json"):
        self.config = self._load_config(config_path)
        self.ast_manipulator = ASTManipulator()
        self.code_analyzer = CodeAnalyzer()
        self.pattern_matcher = CodePatternMatcher()

        # Generation state
        self.generation_history: List[GeneratedCode] = []
        self.template_cache: Dict[str, ast.AST] = {}
        self.pattern_cache: Dict[str, Any] = {}

        # Performance tracking
        self.generation_metrics = {
            "total_generations": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "average_generation_time": 0.0,
            "language_usage": defaultdict(int),
        }

        logger.info("ASTCodeGenerator initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded AST generator config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "generation": {
                "default_strategy": "hybrid",
                "max_generation_time": 30,
                "quality_threshold": 0.8,
                "complexity_threshold": 0.7,
                "enable_caching": True,
                "cache_size": 1000,
            },
            "languages": {
                "python": {
                    "enabled": True,
                    "parser": "ast",
                    "formatter": "astor",
                    "conventions": {
                        "max_line_length": 88,
                        "indent_size": 4,
                        "quote_style": "double",
                    },
                },
                "typescript": {
                    "enabled": True,
                    "parser": "typescript-parser",
                    "formatter": "prettier",
                    "conventions": {
                        "indent_size": 2,
                        "quote_style": "single",
                        "semicolons": True,
                    },
                },
                "javascript": {
                    "enabled": True,
                    "parser": "esprima",
                    "formatter": "prettier",
                    "conventions": {
                        "indent_size": 2,
                        "quote_style": "single",
                        "semicolons": True,
                    },
                },
            },
            "templates": {
                "enabled": True,
                "template_directory": "templates/ast_templates",
                "auto_load_templates": True,
                "template_cache_size": 100,
            },
            "patterns": {
                "enabled": True,
                "pattern_directory": "data/code_patterns",
                "auto_load_patterns": True,
                "pattern_cache_size": 200,
            },
            "quality": {
                "enabled": True,
                "quality_checks": [
                    "syntax_validation",
                    "complexity_analysis",
                    "convention_checking",
                    "dependency_analysis",
                ],
                "quality_threshold": 0.8,
            },
            "performance": {
                "enable_caching": True,
                "cache_ttl_minutes": 60,
                "max_ast_size": 10000,
                "parallel_processing": True,
            },
        }

    async def generate_code(
        self, context: GenerationContext, strategy: GenerationStrategy = None
    ) -> GeneratedCode:
        """
        Generate code using AST-based approach
        """
        try:
            start_time = time.time()
            self.generation_metrics["total_generations"] += 1

            # Use default strategy if none specified
            if strategy is None:
                strategy = GenerationStrategy(
                    self.config.get("generation", {}).get("default_strategy", "hybrid")
                )

            # Generate code based on strategy
            if strategy == GenerationStrategy.TEMPLATE_BASED:
                result = await self._generate_from_template(context)
            elif strategy == GenerationStrategy.AST_MANIPULATION:
                result = await self._generate_from_ast_manipulation(context)
            elif strategy == GenerationStrategy.PATTERN_BASED:
                result = await self._generate_from_patterns(context)
            elif strategy == GenerationStrategy.HYBRID:
                result = await self._generate_hybrid(context)
            else:
                result = await self._generate_hybrid(context)

            # Calculate generation time
            generation_time = time.time() - start_time

            # Create GeneratedCode object
            generated_code = GeneratedCode(
                code=result["code"],
                language=context.language,
                ast_tree=result.get("ast_tree"),
                imports=result.get("imports", []),
                dependencies=result.get("dependencies", []),
                complexity_score=result.get("complexity_score", 0.0),
                quality_score=result.get("quality_score", 0.0),
                generation_time=generation_time,
                strategy_used=strategy,
                metadata=result.get("metadata", {}),
            )

            # Update metrics
            self.generation_metrics["language_usage"][context.language.value] += 1
            self.generation_metrics["successful_generations"] += 1

            # Store in history
            self.generation_history.append(generated_code)

            # Limit history size
            if len(self.generation_history) > self.config.get("generation", {}).get(
                "cache_size", 1000
            ):
                self.generation_history = self.generation_history[-1000:]

            logger.info(
                f"Generated code for {context.target_file} using {strategy.value} strategy"
            )

            return generated_code

        except Exception as e:
            self.generation_metrics["failed_generations"] += 1
            logger.error(f"Failed to generate code: {e}")
            raise

    async def _generate_from_template(
        self, context: GenerationContext
    ) -> Dict[str, Any]:
        """Generate code from AST templates"""
        try:
            # Load or create template
            template = await self._get_template(context)

            # Customize template for context
            customized_ast = await self.ast_manipulator.customize_template(
                template, context.requirements, context.constraints
            )

            # Generate code from AST
            code = await self._ast_to_code(customized_ast, context.language)

            # Analyze generated code
            analysis = await self.code_analyzer.analyze_code(code, context.language)

            return {
                "code": code,
                "ast_tree": customized_ast,
                "imports": analysis.get("imports", []),
                "dependencies": analysis.get("dependencies", []),
                "complexity_score": analysis.get("complexity_score", 0.0),
                "quality_score": analysis.get("quality_score", 0.0),
                "metadata": {"template_used": True},
            }

        except Exception as e:
            logger.error(f"Template-based generation failed: {e}")
            raise

    async def _generate_from_ast_manipulation(
        self, context: GenerationContext
    ) -> Dict[str, Any]:
        """Generate code through direct AST manipulation"""
        try:
            # Create base AST structure
            base_ast = await self.ast_manipulator.create_base_ast(context.language)

            # Add imports
            if context.imports:
                base_ast = await self.ast_manipulator.add_imports(
                    base_ast, context.imports, context.language
                )

            # Add main functionality based on requirements
            for requirement in context.requirements:
                base_ast = await self.ast_manipulator.add_functionality(
                    base_ast, requirement, context.language
                )

            # Apply constraints
            for constraint in context.constraints:
                base_ast = await self.ast_manipulator.apply_constraint(
                    base_ast, constraint, context.language
                )

            # Generate code from AST
            code = await self._ast_to_code(base_ast, context.language)

            # Analyze generated code
            analysis = await self.code_analyzer.analyze_code(code, context.language)

            return {
                "code": code,
                "ast_tree": base_ast,
                "imports": analysis.get("imports", []),
                "dependencies": analysis.get("dependencies", []),
                "complexity_score": analysis.get("complexity_score", 0.0),
                "quality_score": analysis.get("quality_score", 0.0),
                "metadata": {"ast_manipulation": True},
            }

        except Exception as e:
            logger.error(f"AST manipulation generation failed: {e}")
            raise

    async def _generate_from_patterns(
        self, context: GenerationContext
    ) -> Dict[str, Any]:
        """Generate code using pattern matching"""
        try:
            # Find applicable patterns
            patterns = await self.pattern_matcher.find_applicable_patterns(
                context.requirements, context.language
            )

            if not patterns:
                # Fallback to AST manipulation
                return await self._generate_from_ast_manipulation(context)

            # Select best pattern
            best_pattern = patterns[0]  # Assuming sorted by relevance

            # Customize pattern for context
            customized_pattern = await self.pattern_matcher.customize_pattern(
                best_pattern, context.requirements, context.constraints
            )

            # Convert pattern to AST
            pattern_ast = await self.pattern_matcher.pattern_to_ast(
                customized_pattern, context.language
            )

            # Generate code from AST
            code = await self._ast_to_code(pattern_ast, context.language)

            # Analyze generated code
            analysis = await self.code_analyzer.analyze_code(code, context.language)

            return {
                "code": code,
                "ast_tree": pattern_ast,
                "imports": analysis.get("imports", []),
                "dependencies": analysis.get("dependencies", []),
                "complexity_score": analysis.get("complexity_score", 0.0),
                "quality_score": analysis.get("quality_score", 0.0),
                "metadata": {"pattern_used": best_pattern.get("name", "unknown")},
            }

        except Exception as e:
            logger.error(f"Pattern-based generation failed: {e}")
            raise

    async def _generate_hybrid(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate code using hybrid approach combining multiple strategies"""
        try:
            # Try template-based first
            try:
                result = await self._generate_from_template(context)
                if result["quality_score"] >= self.config.get("quality", {}).get(
                    "quality_threshold", 0.8
                ):
                    return result
            except Exception as e:
                logger.debug(f"Template-based generation failed, trying patterns: {e}")

            # Try pattern-based
            try:
                result = await self._generate_from_patterns(context)
                if result["quality_score"] >= self.config.get("quality", {}).get(
                    "quality_threshold", 0.8
                ):
                    return result
            except Exception as e:
                logger.debug(
                    f"Pattern-based generation failed, trying AST manipulation: {e}"
                )

            # Fallback to AST manipulation
            return await self._generate_from_ast_manipulation(context)

        except Exception as e:
            logger.error(f"Hybrid generation failed: {e}")
            raise

    async def _get_template(self, context: GenerationContext) -> ast.AST:
        """Get or create template for the given context"""
        try:
            # Check cache first
            template_key = (
                f"{context.language.value}_{hash(tuple(context.requirements))}"
            )
            if template_key in self.template_cache:
                return self.template_cache[template_key]

            # Load template from file system
            template_path = Path(
                self.config.get("templates", {}).get(
                    "template_directory", "templates/ast_templates"
                )
            )
            template_file = template_path / f"{context.language.value}_template.py"

            if template_file.exists():
                with open(template_file, "r") as f:
                    template_code = f.read()
                template_ast = ast.parse(template_code)
            else:
                # Create default template
                template_ast = await self.ast_manipulator.create_default_template(
                    context.language
                )

            # Cache template
            if self.config.get("generation", {}).get("enable_caching", True):
                self.template_cache[template_key] = template_ast

                # Limit cache size
                if len(self.template_cache) > self.config.get("templates", {}).get(
                    "template_cache_size", 100
                ):
                    # Remove oldest entries
                    oldest_key = next(iter(self.template_cache))
                    del self.template_cache[oldest_key]

            return template_ast

        except Exception as e:
            logger.error(f"Failed to get template: {e}")
            # Return default template
            return await self.ast_manipulator.create_default_template(context.language)

    async def _ast_to_code(self, ast_tree: ast.AST, language: CodeLanguage) -> str:
        """Convert AST to code string"""
        try:
            if language == CodeLanguage.PYTHON:
                return astor.to_source(ast_tree)
            elif language in [
                CodeLanguage.TYPESCRIPT,
                CodeLanguage.JAVASCRIPT,
                CodeLanguage.JSX,
                CodeLanguage.TSX,
            ]:
                # For now, use a simple string representation
                # In a full implementation, this would use language-specific formatters
                return await self._ast_to_js_code(ast_tree, language)
            else:
                raise ValueError(f"Unsupported language: {language}")

        except Exception as e:
            logger.error(f"Failed to convert AST to code: {e}")
            raise

    async def _ast_to_js_code(self, ast_tree: ast.AST, language: CodeLanguage) -> str:
        """Convert AST to JavaScript/TypeScript code"""
        # This is a simplified implementation
        # In a full implementation, this would use proper JS/TS AST handling
        try:
            # For now, return a basic structure
            code_lines = []

            if language in [CodeLanguage.TYPESCRIPT, CodeLanguage.TSX]:
                code_lines.append("// TypeScript/TSX code generated from AST")
            else:
                code_lines.append("// JavaScript/JSX code generated from AST")

            code_lines.append("")
            code_lines.append("// Generated code would be here")
            code_lines.append(
                "// This is a placeholder for the actual AST-to-code conversion"
            )

            return "\n".join(code_lines)

        except Exception as e:
            logger.error(f"Failed to convert AST to JS code: {e}")
            raise

    async def merge_code(
        self, existing_code: str, new_code: str, language: CodeLanguage
    ) -> str:
        """Merge new code with existing code using AST manipulation"""
        try:
            # Parse existing code
            existing_ast = await self._parse_code(existing_code, language)

            # Parse new code
            new_ast = await self._parse_code(new_code, language)

            # Merge ASTs
            merged_ast = await self.ast_manipulator.merge_asts(
                existing_ast, new_ast, language
            )

            # Convert back to code
            return await self._ast_to_code(merged_ast, language)

        except Exception as e:
            logger.error(f"Failed to merge code: {e}")
            raise

    async def _parse_code(self, code: str, language: CodeLanguage) -> ast.AST:
        """Parse code string to AST"""
        try:
            if language == CodeLanguage.PYTHON:
                return ast.parse(code)
            elif language in [
                CodeLanguage.TYPESCRIPT,
                CodeLanguage.JAVASCRIPT,
                CodeLanguage.JSX,
                CodeLanguage.TSX,
            ]:
                # For now, return a basic AST structure
                # In a full implementation, this would use language-specific parsers
                return await self._parse_js_code(code, language)
            else:
                raise ValueError(f"Unsupported language: {language}")

        except Exception as e:
            logger.error(f"Failed to parse code: {e}")
            raise

    async def _parse_js_code(self, code: str, language: CodeLanguage) -> ast.AST:
        """Parse JavaScript/TypeScript code to AST"""
        # This is a simplified implementation
        # In a full implementation, this would use proper JS/TS parsers
        try:
            # Create a basic AST structure for JS/TS
            # This is a placeholder - actual implementation would parse JS/TS properly
            module_ast = ast.Module(body=[], type_ignores=[])
            return module_ast

        except Exception as e:
            logger.error(f"Failed to parse JS code: {e}")
            raise

    async def refactor_code(
        self, code: str, refactoring_rules: List[Dict[str, Any]], language: CodeLanguage
    ) -> str:
        """Refactor code using AST transformations"""
        try:
            # Parse code to AST
            ast_tree = await self._parse_code(code, language)

            # Apply refactoring rules
            for rule in refactoring_rules:
                ast_tree = await self.ast_manipulator.apply_refactoring_rule(
                    ast_tree, rule, language
                )

            # Convert back to code
            return await self._ast_to_code(ast_tree, language)

        except Exception as e:
            logger.error(f"Failed to refactor code: {e}")
            raise

    async def get_generation_metrics(self) -> Dict[str, Any]:
        """Get code generation performance metrics"""
        try:
            total_generations = self.generation_metrics["total_generations"]
            if total_generations == 0:
                return self.generation_metrics

            # Calculate average generation time
            total_time = sum(gc.generation_time for gc in self.generation_history)
            avg_time = (
                total_time / len(self.generation_history)
                if self.generation_history
                else 0.0
            )

            return {
                **self.generation_metrics,
                "average_generation_time": avg_time,
                "success_rate": (
                    self.generation_metrics["successful_generations"]
                    / total_generations
                )
                * 100,
                "failure_rate": (
                    self.generation_metrics["failed_generations"] / total_generations
                )
                * 100,
                "total_history_size": len(self.generation_history),
                "cache_size": len(self.template_cache),
            }

        except Exception as e:
            logger.error(f"Failed to get generation metrics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            # Clear caches
            self.template_cache.clear()
            self.pattern_cache.clear()

            # Clear history
            self.generation_history.clear()

            logger.info("ASTCodeGenerator cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup ASTCodeGenerator: {e}")
