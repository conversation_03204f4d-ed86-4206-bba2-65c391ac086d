"""
API Middleware for cross-cutting concerns
Provides authentication, rate limiting, logging, and other middleware functionality.
"""

import hashlib
import json
import logging
import time
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Optional

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware"""

    def __init__(self, app: ASGIApp, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests: Dict[str, list] = defaultdict(list)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()

        # Clean old requests
        self.requests[client_ip] = [
            req_time
            for req_time in self.requests[client_ip]
            if current_time - req_time < 60
        ]

        # Check rate limit
        if len(self.requests[client_ip]) >= self.requests_per_minute:
            return JSONResponse(
                status_code=429,
                content={
                    "success": False,
                    "error": {
                        "code": "rate_limit_exceeded",
                        "message": "Too many requests. Please try again later.",
                        "retry_after": 60,
                    },
                },
            )

        # Add current request
        self.requests[client_ip].append(current_time)

        return await call_next(request)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Request/response logging middleware"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # Log request
        logger.info(
            f"Request: {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "path": request.url.path,
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", ""),
                "timestamp": datetime.now().isoformat(),
            },
        )

        # Process request
        response = await call_next(request)

        # Calculate processing time
        processing_time = time.time() - start_time

        # Log response
        logger.info(
            f"Response: {response.status_code} ({processing_time:.3f}s)",
            extra={
                "status_code": response.status_code,
                "processing_time": processing_time,
                "method": request.method,
                "path": request.url.path,
                "timestamp": datetime.now().isoformat(),
            },
        )

        # Add processing time header
        response.headers["X-Processing-Time"] = str(processing_time)

        return response


class SecurityMiddleware(BaseHTTPMiddleware):
    """Security headers middleware"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"

        return response


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Global error handling middleware"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except HTTPException:
            # Re-raise HTTP exceptions as they're already handled
            raise
        except Exception as e:
            # Log unexpected errors
            logger.error(
                f"Unexpected error in {request.method} {request.url.path}: {str(e)}",
                exc_info=True,
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "client_ip": request.client.host if request.client else "unknown",
                    "timestamp": datetime.now().isoformat(),
                },
            )

            # Return standardized error response
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": {
                        "code": "internal_server_error",
                        "message": "An unexpected error occurred",
                        "type": type(e).__name__,
                        "timestamp": datetime.now().isoformat(),
                    },
                },
            )


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Request validation middleware"""

    def __init__(
        self, app: ASGIApp, max_content_length: int = 10 * 1024 * 1024
    ):  # 10MB
        super().__init__(app)
        self.max_content_length = max_content_length

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check content length
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_content_length:
            return JSONResponse(
                status_code=413,
                content={
                    "success": False,
                    "error": {
                        "code": "payload_too_large",
                        "message": f"Request body too large. Maximum size: {self.max_content_length // (1024*1024)}MB",
                        "timestamp": datetime.now().isoformat(),
                    },
                },
            )

        return await call_next(request)


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Simple authentication middleware"""

    def __init__(self, app: ASGIApp, api_key: Optional[str] = None):
        super().__init__(app)
        self.api_key = api_key or "default-api-key"

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip authentication for health checks and public endpoints
        if request.url.path in ["/health", "/docs", "/openapi.json"]:
            return await call_next(request)

        # Check API key
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=401,
                content={
                    "success": False,
                    "error": {
                        "code": "unauthorized",
                        "message": "Missing or invalid authorization header",
                        "timestamp": datetime.now().isoformat(),
                    },
                },
            )

        api_key_from_header = auth_header.split(" ")[1]
        if api_key_from_header != self.api_key:
            return JSONResponse(
                status_code=401,
                content={
                    "success": False,
                    "error": {
                        "code": "unauthorized",
                        "message": "Invalid API key",
                        "timestamp": datetime.now().isoformat(),
                    },
                },
            )

        return await call_next(request)


class MetricsMiddleware(BaseHTTPMiddleware):
    """Metrics collection middleware"""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.metrics = {
            "total_requests": 0,
            "requests_by_method": defaultdict(int),
            "requests_by_path": defaultdict(int),
            "response_codes": defaultdict(int),
            "average_response_time": 0.0,
            "start_time": datetime.now(),
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # Update request metrics
        self.metrics["total_requests"] += 1
        self.metrics["requests_by_method"][request.method] += 1
        self.metrics["requests_by_path"][request.url.path] += 1

        # Process request
        response = await call_next(request)

        # Update response metrics
        processing_time = time.time() - start_time
        self.metrics["response_codes"][response.status_code] += 1

        # Update average response time
        current_avg = self.metrics["average_response_time"]
        total_requests = self.metrics["total_requests"]
        self.metrics["average_response_time"] = (
            current_avg * (total_requests - 1) + processing_time
        ) / total_requests

        return response

    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        uptime = (datetime.now() - self.metrics["start_time"]).total_seconds()
        return {
            **self.metrics,
            "uptime_seconds": uptime,
            "requests_per_second": (
                self.metrics["total_requests"] / uptime if uptime > 0 else 0
            ),
        }


# Middleware factory functions
def create_rate_limit_middleware(requests_per_minute: int = 60) -> RateLimitMiddleware:
    """Create rate limiting middleware"""
    return lambda app: RateLimitMiddleware(app, requests_per_minute)


def create_auth_middleware(api_key: Optional[str] = None) -> AuthenticationMiddleware:
    """Create authentication middleware"""
    return lambda app: AuthenticationMiddleware(app, api_key)


def create_metrics_middleware() -> MetricsMiddleware:
    """Create metrics middleware"""
    return lambda app: MetricsMiddleware(app)
