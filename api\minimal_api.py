#!/usr/bin/env python3
"""
Minimal dashboard API using Flask without Pydantic dependencies.
This provides basic functionality for Phase 4.1 testing.
"""

import asyncio
import json
import logging
import os
import sqlite3
import sys
from datetime import datetime
from pathlib import Path

import requests
from flask import Flask, jsonify, request, session
from flask_cors import CORS
from werkzeug.security import check_password_hash, generate_password_hash

# Add project root to Python path for absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("dashboard.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.secret_key = os.getenv(
    "DASHBOARD_SECRET_KEY", "your-secret-key-here-change-in-production"
)
CORS(app)

# Database configuration
DATABASE_PATH = Path("./database/ai_coding_agent.db")


def call_ollama_model(prompt: str, model: str = "deepseek-coder:1.3b"):
    """Call Ollama model for AI responses"""
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={"model": model, "prompt": prompt, "stream": False},
            timeout=30,
        )
        if response.ok:
            return response.json()["response"]
        else:
            return f"Error: {response.status_code}"
    except Exception as e:
        return f"Model unavailable: {str(e)}"


def select_model_by_intent(intent: str) -> str:
    """Select appropriate model based on intent"""
    intent_lower = intent.lower()

    if "code" in intent_lower or "generation" in intent_lower:
        return "deepseek-coder:1.3b"
    elif "review" in intent_lower or "analysis" in intent_lower:
        return "yi-coder:1.5b"
    elif "content" in intent_lower or "documentation" in intent_lower:
        return "qwen2.5-coder:3b"
    elif "complex" in intent_lower or "advanced" in intent_lower:
        return "starcoder2:3b"
    elif "general" in intent_lower or "assistance" in intent_lower:
        return "mistral:7b-instruct-q4_0"
    else:
        return "deepseek-coder:1.3b"  # Default


def build_enhanced_prompt(
    prompt: str, context: dict, intent: str, history: list
) -> str:
    """Build an enhanced prompt with context and history"""
    enhanced_prompt = f"Task: {prompt}\n"

    if intent:
        enhanced_prompt += f"Intent: {intent}\n"

    if context:
        if "file" in context:
            enhanced_prompt += f"File Context: {context['file']}\n"
        if "code" in context:
            enhanced_prompt += f"Code Context: {context['code'][:500]}...\n"

    if history:
        # Include last 3 messages for context
        recent_history = history[-3:] if len(history) > 3 else history
        enhanced_prompt += "Recent Conversation:\n"
        for msg in recent_history:
            enhanced_prompt += (
                f"- {msg.get('role', 'user')}: {msg.get('content', '')}\n"
            )

    enhanced_prompt += "\nPlease provide a helpful and accurate response."
    return enhanced_prompt


def init_database():
    """Initialize database with basic tables"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Create users table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                hashed_password TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now'))
            )
        """
        )

        # Create projects table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                owner_id INTEGER NOT NULL,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (owner_id) REFERENCES users (id)
            )
        """
        )

        # Create code_files table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS code_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                content TEXT,
                path TEXT,
                language TEXT,
                is_directory BOOLEAN DEFAULT 0,
                project_id INTEGER NOT NULL,
                parent_id INTEGER,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (parent_id) REFERENCES code_files (id)
            )
        """
        )

        # Create deployments table
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS deployments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                status TEXT DEFAULT 'pending',
                environment TEXT DEFAULT 'development',
                url TEXT,
                commit_hash TEXT,
                logs TEXT,
                metadata TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                completed_at TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )
        """
        )

        # Check if admin user exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if cursor.fetchone()[0] == 0:
            # Create default admin user
            hashed_password = generate_password_hash("admin123")
            current_time = datetime.now().isoformat()
            cursor.execute(
                """
                INSERT INTO users (username, email, hashed_password, is_active, is_superuser, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    "admin",
                    "<EMAIL>",
                    hashed_password,
                    True,
                    True,
                    current_time,
                    current_time,
                ),
            )
            logger.info("Default admin user created: admin/admin123")

        conn.commit()
        conn.close()
        logger.info("Database initialized successfully")

        # Start monitoring agent
        try:
            from monitoring.monitoring_agent import start_monitoring_agent

            asyncio.run(start_monitoring_agent())
            logger.info("Monitoring agent started successfully")
        except ImportError:
            logger.warning("Monitoring agent not available - skipping startup")
        except Exception as e:
            logger.warning(f"Failed to start monitoring agent: {e}")

        return True
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False


def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn


def get_current_user():
    """Get current user from session"""
    if "user_id" not in session:
        return None

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE id = ?", (session["user_id"],))
    user = cursor.fetchone()
    conn.close()
    return user


def require_auth(f):
    """Decorator to require authentication"""

    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            return jsonify({"error": "Authentication required"}), 401
        return f(*args, **kwargs)

    decorated_function.__name__ = f.__name__
    return decorated_function


# Health check endpoint
@app.route("/health", methods=["GET"])
def health():
    """Health check endpoint"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        conn.close()
        return jsonify(
            {
                "status": "healthy",
                "database": "connected",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        return jsonify(
            {
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }
        )


@app.route("/monitor/health", methods=["GET"])
def monitor_health():
    """System monitoring health endpoint"""
    try:
        from monitoring.monitoring_agent import monitoring_agent

        if monitoring_agent:
            health_data = monitoring_agent.get_current_health_sync()
            return jsonify(health_data)
        else:
            return jsonify(
                {
                    "status": "monitoring_disabled",
                    "message": "Monitoring agent not available",
                    "timestamp": datetime.now().isoformat(),
                }
            )
    except ImportError:
        return jsonify(
            {
                "status": "monitoring_disabled",
                "message": "Monitoring agent not available",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        return (
            jsonify(
                {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


@app.route("/monitor/metrics", methods=["GET"])
def monitor_metrics():
    """Detailed system metrics endpoint"""
    try:
        from monitoring.monitoring_agent import monitoring_agent

        if monitoring_agent:
            # Get metrics history (last 50 entries by default)
            limit = request.args.get("limit", 50, type=int)
            metrics = monitoring_agent.get_metrics_history(limit=limit)
            return jsonify(metrics)
        else:
            return jsonify(
                {
                    "status": "monitoring_disabled",
                    "message": "Monitoring agent not available",
                    "timestamp": datetime.now().isoformat(),
                }
            )
    except ImportError:
        return jsonify(
            {
                "status": "monitoring_disabled",
                "message": "Monitoring agent not available",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        return (
            jsonify(
                {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


@app.route("/monitor/history", methods=["GET"])
def monitor_history():
    """System monitoring history endpoint"""
    try:
        from monitoring.monitoring_agent import monitoring_agent

        if monitoring_agent:
            # Get metrics history (last 100 entries by default)
            limit = request.args.get("limit", 100, type=int)
            metrics_history = monitoring_agent.get_metrics_history(limit=limit)

            # Convert to JSON-serializable format
            history_data = []
            for metric in metrics_history:
                history_data.append(
                    {
                        "timestamp": metric.timestamp,
                        "cpu_percent": metric.cpu_percent,
                        "memory_percent": metric.memory_percent,
                        "disk_percent": metric.disk_percent,
                        "process_count": metric.process_count,
                        "uptime": metric.uptime,
                    }
                )

            return jsonify(
                {
                    "status": "success",
                    "history_count": len(history_data),
                    "history": history_data,
                    "timestamp": datetime.now().isoformat(),
                }
            )
        else:
            return jsonify(
                {
                    "status": "monitoring_disabled",
                    "message": "Monitoring agent not available",
                    "timestamp": datetime.now().isoformat(),
                }
            )
    except ImportError:
        return jsonify(
            {
                "status": "monitoring_disabled",
                "message": "Monitoring agent not available",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        return (
            jsonify(
                {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


@app.route("/monitor/errors", methods=["GET"])
def monitor_errors():
    """System error monitoring endpoint"""
    try:
        from monitoring.monitoring_agent import monitoring_agent

        if monitoring_agent:
            # For now, return a placeholder since the monitoring agent doesn't have error tracking yet
            # This can be enhanced when error tracking is implemented
            return jsonify(
                {
                    "status": "success",
                    "message": "Error tracking not yet implemented",
                    "errors": [],
                    "error_count": 0,
                    "timestamp": datetime.now().isoformat(),
                }
            )
        else:
            return jsonify(
                {
                    "status": "monitoring_disabled",
                    "message": "Monitoring agent not available",
                    "timestamp": datetime.now().isoformat(),
                }
            )
    except ImportError:
        return jsonify(
            {
                "status": "monitoring_disabled",
                "message": "Monitoring agent not available",
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        return (
            jsonify(
                {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


# Root endpoint
@app.route("/", methods=["GET"])
def root():
    """Root endpoint with API information"""
    return jsonify(
        {
            "message": "AI Coding Agent Dashboard API",
            "version": "1.0.0",
            "framework": "Flask",
            "endpoints": {
                "health": "/health",
                "monitoring": {
                    "health": "/monitor/health",
                    "metrics": "/monitor/metrics",
                    "history": "/monitor/history",
                    "errors": "/monitor/errors",
                },
                "auth": "/api/v1/auth",
                "projects": "/api/v1/projects",
                "files": "/api/v1/files",
                "deployments": "/api/v1/deployments",
                "chat": {
                    "main": "/api/v1/chat",
                    "models": "/api/v1/chat/models",
                    "test": "/api/v1/chat/test",
                },
                "ai_models": "/api/v1/ai/models/health",
            },
        }
    )


# Authentication endpoints
@app.route("/api/v1/auth/login", methods=["POST"])
def login():
    """Login user"""
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return jsonify({"error": "Username and password required"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
        user = cursor.fetchone()
        conn.close()

        if not user or not check_password_hash(user["hashed_password"], password):
            return jsonify({"error": "Invalid credentials"}), 401

        # Store user in session
        session["user_id"] = user["id"]

        return jsonify(
            {
                "message": "Login successful",
                "access_token": "session_token",  # Simple token for session-based auth
                "user": {
                    "id": user["id"],
                    "username": user["username"],
                    "email": user["email"],
                    "is_active": bool(user["is_active"]),
                    "is_superuser": bool(user["is_superuser"]),
                },
            }
        )
    except Exception as e:
        logger.error(f"Login error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/auth/register", methods=["POST"])
def register():
    """Register new user"""
    try:
        data = request.get_json()
        username = data.get("username")
        email = data.get("email")
        password = data.get("password")

        if not username or not email or not password:
            return jsonify({"error": "Username, email, and password required"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if user already exists
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (username,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({"error": "Username already exists"}), 400

        cursor.execute("SELECT COUNT(*) FROM users WHERE email = ?", (email,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({"error": "Email already exists"}), 400

        # Create new user
        hashed_password = generate_password_hash(password)
        cursor.execute(
            """
            INSERT INTO users (username, email, hashed_password, is_active, is_superuser)
            VALUES (?, ?, ?, ?, ?)
        """,
            (username, email, hashed_password, True, False),
        )

        user_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify(
            {
                "message": "User registered successfully",
                "user": {"id": user_id, "username": username, "email": email},
            }
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/auth/logout", methods=["POST"])
def logout():
    """Logout user"""
    session.clear()
    return jsonify({"message": "Logout successful"})


# User endpoints
@app.route("/api/v1/users/me", methods=["GET"])
@require_auth
def get_current_user_info():
    """Get current user information"""
    user = get_current_user()
    return jsonify(
        {
            "id": user["id"],
            "username": user["username"],
            "email": user["email"],
            "is_active": bool(user["is_active"]),
            "is_superuser": bool(user["is_superuser"]),
            "created_at": user["created_at"],
            "updated_at": user["updated_at"],
        }
    )


# Project endpoints
@app.route("/api/v1/projects", methods=["GET"])
@require_auth
def get_projects():
    """Get user's projects"""
    try:
        user = get_current_user()
        skip = request.args.get("skip", 0, type=int)
        limit = request.args.get("limit", 100, type=int)

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT * FROM projects
            WHERE owner_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """,
            (user["id"], limit, skip),
        )

        projects = cursor.fetchall()
        conn.close()

        return jsonify(
            [
                {
                    "id": p["id"],
                    "name": p["name"],
                    "description": p["description"],
                    "owner_id": p["owner_id"],
                    "created_at": p["created_at"],
                    "updated_at": p["updated_at"],
                }
                for p in projects
            ]
        )
    except Exception as e:
        logger.error(f"Get projects error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/projects", methods=["POST"])
@require_auth
def create_project():
    """Create new project"""
    try:
        user = get_current_user()
        data = request.get_json()
        name = data.get("name")
        description = data.get("description", "")

        if not name:
            return jsonify({"error": "Project name required"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO projects (name, description, owner_id)
            VALUES (?, ?, ?)
        """,
            (name, description, user["id"]),
        )

        project_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify(
            {
                "id": project_id,
                "name": name,
                "description": description,
                "owner_id": user["id"],
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        logger.error(f"Create project error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/projects/<int:project_id>", methods=["GET"])
@require_auth
def get_project(project_id):
    """Get project by ID"""
    try:
        user = get_current_user()

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM projects WHERE id = ?", (project_id,))
        project = cursor.fetchone()
        conn.close()

        if not project:
            return jsonify({"error": "Project not found"}), 404

        if project["owner_id"] != user["id"] and not user["is_superuser"]:
            return jsonify({"error": "Not enough permissions"}), 403

        return jsonify(
            {
                "id": project["id"],
                "name": project["name"],
                "description": project["description"],
                "owner_id": project["owner_id"],
                "created_at": project["created_at"],
                "updated_at": project["updated_at"],
            }
        )
    except Exception as e:
        logger.error(f"Get project error: {e}")
        return jsonify({"error": "Internal server error"}), 500


# File endpoints
@app.route("/api/v1/files", methods=["GET"])
@require_auth
def get_files():
    """Get code files"""
    try:
        user = get_current_user()
        project_id = request.args.get("project_id", type=int)
        skip = request.args.get("skip", 0, type=int)
        limit = request.args.get("limit", 100, type=int)

        conn = get_db_connection()
        cursor = conn.cursor()

        if project_id:
            # Verify project ownership
            cursor.execute("SELECT owner_id FROM projects WHERE id = ?", (project_id,))
            project = cursor.fetchone()

            if not project:
                conn.close()
                return jsonify({"error": "Project not found"}), 404

            if project["owner_id"] != user["id"] and not user["is_superuser"]:
                conn.close()
                return jsonify({"error": "Not enough permissions"}), 403

            cursor.execute(
                """
                SELECT * FROM code_files
                WHERE project_id = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """,
                (project_id, limit, skip),
            )
        else:
            cursor.execute(
                """
                SELECT * FROM code_files
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """,
                (limit, skip),
            )

        files = cursor.fetchall()
        conn.close()

        return jsonify(
            [
                {
                    "id": f["id"],
                    "filename": f["filename"],
                    "content": f["content"],
                    "path": f["path"],
                    "language": f["language"],
                    "is_directory": bool(f["is_directory"]),
                    "project_id": f["project_id"],
                    "parent_id": f["parent_id"],
                    "created_at": f["created_at"],
                    "updated_at": f["updated_at"],
                }
                for f in files
            ]
        )
    except Exception as e:
        logger.error(f"Get files error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/files", methods=["POST"])
@require_auth
def create_file():
    """Create new code file"""
    try:
        user = get_current_user()
        data = request.get_json()
        filename = data.get("filename")
        content = data.get("content", "")
        language = data.get("language")
        is_directory = data.get("is_directory", False)
        project_id = data.get("project_id")

        if not filename or not project_id:
            return jsonify({"error": "Filename and project_id required"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Verify project ownership
        cursor.execute("SELECT owner_id FROM projects WHERE id = ?", (project_id,))
        project = cursor.fetchone()

        if not project:
            conn.close()
            return jsonify({"error": "Project not found"}), 404

        if project["owner_id"] != user["id"] and not user["is_superuser"]:
            conn.close()
            return jsonify({"error": "Not enough permissions"}), 403

        cursor.execute(
            """
            INSERT INTO code_files (filename, content, language, is_directory, project_id)
            VALUES (?, ?, ?, ?, ?)
        """,
            (filename, content, language, is_directory, project_id),
        )

        file_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify(
            {
                "id": file_id,
                "filename": filename,
                "content": content,
                "language": language,
                "is_directory": is_directory,
                "project_id": project_id,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        logger.error(f"Create file error: {e}")
        return jsonify({"error": "Internal server error"}), 500


# Deployment endpoints
@app.route("/api/v1/deployments", methods=["GET"])
@require_auth
def get_deployments():
    """Get deployments"""
    try:
        user = get_current_user()
        project_id = request.args.get("project_id", type=int)
        skip = request.args.get("skip", 0, type=int)
        limit = request.args.get("limit", 100, type=int)

        conn = get_db_connection()
        cursor = conn.cursor()

        if project_id:
            # Verify project ownership
            cursor.execute("SELECT owner_id FROM projects WHERE id = ?", (project_id,))
            project = cursor.fetchone()

            if not project:
                conn.close()
                return jsonify({"error": "Project not found"}), 404

            if project["owner_id"] != user["id"] and not user["is_superuser"]:
                conn.close()
                return jsonify({"error": "Not enough permissions"}), 403

            cursor.execute(
                """
                SELECT * FROM deployments
                WHERE project_id = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """,
                (project_id, limit, skip),
            )
        else:
            cursor.execute(
                """
                SELECT * FROM deployments
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """,
                (limit, skip),
            )

        deployments = cursor.fetchall()
        conn.close()

        return jsonify(
            [
                {
                    "id": d["id"],
                    "project_id": d["project_id"],
                    "status": d["status"],
                    "environment": d["environment"],
                    "url": d["url"],
                    "commit_hash": d["commit_hash"],
                    "logs": d["logs"],
                    "metadata": d["metadata"],
                    "created_at": d["created_at"],
                    "completed_at": d["completed_at"],
                }
                for d in deployments
            ]
        )
    except Exception as e:
        logger.error(f"Get deployments error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/chat", methods=["POST"])
@require_auth
def chat_endpoint():
    """AI Chat endpoint for IDE interface"""
    try:
        data = request.get_json()
        prompt = data.get("prompt")
        context = data.get("context", {})
        intent = data.get("intent", "")
        history = data.get("history", [])
        use_architect = data.get(
            "use_architect", False
        )  # New flag to use ArchitectAgent

        if not prompt:
            return jsonify({"success": False, "error": "Missing prompt"}), 400

        # Check if this is a command that should use ArchitectAgent
        if use_architect or any(
            keyword in prompt.lower()
            for keyword in [
                "create",
                "build",
                "deploy",
                "generate",
                "make",
                "set up",
                "configure",
            ]
        ):
            try:
                # Import and use ArchitectAgent
                import asyncio

                from core.agents import ArchitectAgent

                # Create event loop for async operation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Initialize ArchitectAgent
                    agent = ArchitectAgent()

                    # Process command with ArchitectAgent
                    result = loop.run_until_complete(
                        agent.process_user_command(prompt, priority="medium")
                    )

                    # Format response
                    ai_response = {
                        "content": f"Task created successfully! Task ID: {result.get('task_id', 'N/A')}. Status: {result.get('status', 'N/A')}. {result.get('message', 'Task is being processed.')}",
                        "model": "architect-agent",
                        "success": result.get("success", False),
                        "timestamp": datetime.now().isoformat(),
                        "intent": intent,
                        "context_used": bool(context),
                        "history_length": len(history),
                        "task_id": result.get("task_id"),
                        "architect_used": True,
                    }

                    return jsonify(
                        {
                            "success": True,
                            "response": ai_response,
                            "metadata": {
                                "model_selected": "architect-agent",
                                "prompt_length": len(prompt),
                                "processing_time": "async",
                                "task_created": True,
                            },
                        }
                    )

                finally:
                    loop.close()

            except ImportError:
                logger.warning("ArchitectAgent not available, using regular chat")
                # Fall back to regular chat processing
                pass
            except Exception as architect_error:
                logger.warning(
                    f"ArchitectAgent failed, falling back to regular chat: {architect_error}"
                )
                # Fall back to regular chat processing
                pass

        # Regular chat processing (existing logic)
        # Select appropriate model based on intent
        model_name = select_model_by_intent(intent)

        # Build enhanced prompt with context
        enhanced_prompt = build_enhanced_prompt(prompt, context, intent, history)

        # Call AI model with fallback logic
        try:
            ai_content = call_ollama_model(enhanced_prompt, model_name)
            model_used = model_name
            success = True
        except Exception as e:
            logger.warning(f"Primary model {model_name} failed, trying fallback: {e}")
            # Fallback to default model
            try:
                ai_content = call_ollama_model(prompt, "deepseek-coder:1.3b")
                model_used = "deepseek-coder:1.3b (fallback)"
                success = True
            except Exception as fallback_error:
                logger.error(f"All models failed: {fallback_error}")
                ai_content = f"I understand you want to: {prompt}. This is a placeholder response. The AI model integration is being implemented."
                model_used = "placeholder"
                success = False

        # Prepare enhanced response
        ai_response = {
            "content": ai_content,
            "model": model_used,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "intent": intent,
            "context_used": bool(context),
            "history_length": len(history),
            "architect_used": False,
        }

        return jsonify(
            {
                "success": True,
                "response": ai_response,
                "metadata": {
                    "model_selected": model_name,
                    "prompt_length": len(prompt),
                    "enhanced_prompt_length": len(enhanced_prompt),
                    "processing_time": "sync",
                },
            }
        )

    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/v1/ai/models/health", methods=["GET"])
def ai_models_health():
    """Check health of AI models"""
    try:
        # First check if Ollama service is available
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if not response.ok:
                return jsonify(
                    {
                        "success": False,
                        "error": "Ollama service not available",
                        "models": {},
                        "timestamp": datetime.now().isoformat(),
                    }
                )
        except Exception as e:
            return jsonify(
                {
                    "success": False,
                    "error": f"Ollama service not responding: {str(e)}",
                    "models": {},
                    "timestamp": datetime.now().isoformat(),
                }
            )

        # Get available models from Ollama
        models_response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if models_response.ok:
            available_models = [
                model["name"] for model in models_response.json().get("models", [])
            ]
        else:
            available_models = []

        # Check health of our 5 approved models
        approved_models = [
            "deepseek-coder:1.3b",
            "yi-coder:1.5b",
            "qwen2.5-coder:3b",
            "starcoder2:3b",
            "mistral:7b-instruct-q4_0",
        ]
        health_status = {}

        for model in approved_models:
            # Check if model is available in Ollama
            is_available = model in available_models
            health_status[model] = {
                "available": is_available,
                "status": "healthy" if is_available else "not_installed",
            }

        return jsonify(
            {
                "success": True,
                "models": health_status,
                "available_count": len(
                    [m for m in health_status.values() if m["available"]]
                ),
                "total_count": len(approved_models),
                "timestamp": datetime.now().isoformat(),
            }
        )
    except Exception as e:
        logger.error(f"AI models health check error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/v1/chat/models", methods=["GET"])
def get_available_models():
    """Get list of available AI models"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.ok:
            models = response.json().get("models", [])
            return jsonify(
                {
                    "success": True,
                    "models": [model["name"] for model in models],
                    "count": len(models),
                    "timestamp": datetime.now().isoformat(),
                }
            )
        else:
            return jsonify(
                {
                    "success": False,
                    "error": "Ollama service not available",
                    "models": [],
                    "timestamp": datetime.now().isoformat(),
                }
            )
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return jsonify(
            {
                "success": False,
                "error": str(e),
                "models": [],
                "timestamp": datetime.now().isoformat(),
            }
        )


@app.route("/api/v1/chat/test", methods=["POST"])
def test_chat_endpoint():
    """Test endpoint for chat functionality"""
    try:
        data = request.get_json()
        prompt = data.get("prompt", "Hello, this is a test message")

        # Simple test response
        test_response = {
            "content": f"Test response to: {prompt}",
            "model": "test-model",
            "success": True,
            "timestamp": datetime.now().isoformat(),
        }

        return jsonify(
            {
                "success": True,
                "response": test_response,
                "test": True,
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        return (
            jsonify(
                {
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            ),
            500,
        )


@app.route("/api/v1/errors/report", methods=["POST"])
def report_error():
    """Report client-side errors"""
    try:
        data = request.get_json()
        error_type = data.get("type", "unknown")
        message = data.get("message", "No message")
        stack = data.get("stack", "No stack trace")

        logger.error(f"Client error - Type: {error_type}, Message: {message}")
        logger.debug(f"Stack trace: {stack}")

        return jsonify({"message": "Error reported successfully"}), 200
    except Exception as e:
        logger.error(f"Error reporting failed: {e}")
        return jsonify({"error": "Failed to report error"}), 500


@app.route("/api/v1/deployments", methods=["POST"])
@require_auth
def create_deployment():
    """Create new deployment"""
    try:
        user = get_current_user()
        data = request.get_json()
        project_id = data.get("project_id")
        environment = data.get("environment", "development")
        url = data.get("url")
        metadata = json.dumps(data.get("metadata", {}))

        if not project_id:
            return jsonify({"error": "Project ID required"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # Verify project ownership
        cursor.execute("SELECT owner_id FROM projects WHERE id = ?", (project_id,))
        project = cursor.fetchone()

        if not project:
            conn.close()
            return jsonify({"error": "Project not found"}), 404

        if project["owner_id"] != user["id"] and not user["is_superuser"]:
            conn.close()
            return jsonify({"error": "Not enough permissions"}), 403

        cursor.execute(
            """
            INSERT INTO deployments (project_id, environment, url, metadata)
            VALUES (?, ?, ?, ?)
        """,
            (project_id, environment, url, metadata),
        )

        deployment_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify(
            {
                "id": deployment_id,
                "project_id": project_id,
                "status": "pending",
                "environment": environment,
                "url": url,
                "metadata": data.get("metadata", {}),
                "created_at": datetime.now().isoformat(),
                "completed_at": None,
            }
        )
    except Exception as e:
        logger.error(f"Create deployment error: {e}")
        return jsonify({"error": "Internal server error"}), 500


# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Not found"}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500


if __name__ == "__main__":
    # Initialize database
    if init_database():
        logger.info("Starting Flask dashboard server...")
        app.run(host="127.0.0.1", port=8000, debug=False)
    else:
        logger.error("Failed to initialize database. Exiting.")
