#!/usr/bin/env python3
"""
CLI Commands for AST-Based Code Generation

This module provides command-line interface for the AST-based code generation system.
"""

import argparse
import ast
import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.code_generation.ast_code_generator import (
    ASTCodeGenerator,
    CodeLanguage,
    GenerationContext,
    GenerationStrategy,
)
from core.code_generation.ast_manipulator import ASTManipulator
from core.code_generation.code_analyzer import CodeAnalyzer
from core.code_generation.pattern_matcher import CodePatternMatcher

logger = logging.getLogger(__name__)


class ASTGenerationCommands:
    """CLI commands for AST-based code generation"""

    def __init__(self, agent=None):
        self.agent = agent
        self.ast_generator = ASTCodeGenerator()
        self.ast_manipulator = ASTManipulator()
        self.code_analyzer = CodeAnalyzer()
        self.pattern_matcher = CodePatternMatcher()

    async def generate_code(self, **kwargs) -> Dict[str, Any]:
        """Generate code using AST-based generation"""
        try:
            language_str = kwargs.get("language", "python")
            language = CodeLanguage(language_str)

            context = GenerationContext(
                language=language,
                target_file=kwargs.get("target_file", f"generated.{language_str}"),
                requirements=kwargs.get("requirements", []),
                constraints=kwargs.get("constraints", []),
                conventions=kwargs.get("conventions", {}),
                imports=kwargs.get("imports", []),
                dependencies=kwargs.get("dependencies", []),
                metadata=kwargs.get("metadata", {}),
            )

            strategy_str = kwargs.get("strategy", "hybrid")
            strategy = GenerationStrategy(strategy_str)

            result = await self.ast_generator.generate_code(context, strategy)

            return {
                "success": True,
                "code": result.code,
                "language": result.language.value,
                "complexity_score": result.complexity_score,
                "quality_score": result.quality_score,
                "generation_time": result.generation_time,
                "strategy_used": result.strategy_used.value,
                "metadata": result.metadata,
            }

        except Exception as e:
            logger.error(f"Code generation failed: {e}")
            return {"success": False, "error": str(e)}

    async def analyze_code(self, **kwargs) -> Dict[str, Any]:
        """Analyze existing code using AST analysis"""
        try:
            code = kwargs.get("code", "")
            language = kwargs.get("language", "python")

            if not code:
                return {"success": False, "error": "No code provided for analysis"}

            analysis = await self.code_analyzer.analyze_code(code, language)

            return {
                "success": True,
                "analysis": {
                    "structure": {
                        "functions": analysis.structure.functions,
                        "classes": analysis.structure.classes,
                        "imports": analysis.structure.imports,
                        "variables": analysis.structure.variables,
                        "total_lines": analysis.structure.total_lines,
                    },
                    "complexity": {
                        "cyclomatic_complexity": analysis.complexity.cyclomatic_complexity,
                        "cognitive_complexity": analysis.complexity.cognitive_complexity,
                        "nesting_depth": analysis.complexity.nesting_depth,
                        "complexity_level": analysis.complexity.complexity_level.value,
                    },
                    "quality": {
                        "maintainability_index": analysis.quality.maintainability_index,
                        "code_smells": analysis.quality.code_smells,
                        "violations": analysis.quality.violations,
                        "overall_quality_score": analysis.quality.overall_quality_score,
                    },
                    "dependencies": analysis.dependencies,
                    "context": analysis.context,
                },
            }

        except Exception as e:
            logger.error(f"Code analysis failed: {e}")
            return {"success": False, "error": str(e)}

    async def refactor_code(self, **kwargs) -> Dict[str, Any]:
        """Refactor code using AST transformations"""
        try:
            code = kwargs.get("code", "")
            language = kwargs.get("language", "python")
            refactoring_rules = kwargs.get("refactoring_rules", [])

            if not code:
                return {"success": False, "error": "No code provided for refactoring"}

            if not refactoring_rules:
                return {"success": False, "error": "No refactoring rules provided"}

            language_enum = CodeLanguage(language)
            refactored_code = await self.ast_generator.refactor_code(
                code, refactoring_rules, language_enum
            )

            return {
                "success": True,
                "original_code": code,
                "refactored_code": refactored_code,
                "refactoring_rules": refactoring_rules,
            }

        except Exception as e:
            logger.error(f"Code refactoring failed: {e}")
            return {"success": False, "error": str(e)}

    async def merge_code(self, **kwargs) -> Dict[str, Any]:
        """Merge two code files using AST merging"""
        try:
            existing_code = kwargs.get("existing_code", "")
            new_code = kwargs.get("new_code", "")
            language = kwargs.get("language", "python")

            if not existing_code or not new_code:
                return {
                    "success": False,
                    "error": "Both existing_code and new_code must be provided",
                }

            language_enum = CodeLanguage(language)
            merged_code = await self.ast_generator.merge_code(
                existing_code, new_code, language_enum
            )

            return {
                "success": True,
                "existing_code": existing_code,
                "new_code": new_code,
                "merged_code": merged_code,
            }

        except Exception as e:
            logger.error(f"Code merging failed: {e}")
            return {"success": False, "error": str(e)}

    async def find_patterns(self, **kwargs) -> Dict[str, Any]:
        """Find applicable code patterns"""
        try:
            requirements = kwargs.get("requirements", [])
            language = kwargs.get("language", "python")

            if not requirements:
                return {
                    "success": False,
                    "error": "No requirements provided for pattern matching",
                }

            patterns = await self.pattern_matcher.find_applicable_patterns(
                requirements, language
            )

            return {
                "success": True,
                "patterns": [
                    {
                        "name": pattern.name,
                        "pattern_type": pattern.pattern_type.value,
                        "category": pattern.category.value,
                        "language": pattern.language,
                        "description": pattern.description,
                        "usage_count": pattern.usage_count,
                        "success_rate": pattern.success_rate,
                        "complexity_score": pattern.complexity_score,
                    }
                    for pattern in patterns
                ],
            }

        except Exception as e:
            logger.error(f"Pattern finding failed: {e}")
            return {"success": False, "error": str(e)}

    async def get_metrics(self, **kwargs) -> Dict[str, Any]:
        """Get generation and analysis metrics"""
        try:
            generation_metrics = await self.ast_generator.get_generation_metrics()
            analysis_metrics = await self.code_analyzer.get_analysis_metrics()
            manipulation_metrics = await self.ast_manipulator.get_manipulation_metrics()
            pattern_metrics = await self.pattern_matcher.get_matching_metrics()

            return {
                "success": True,
                "generation_metrics": generation_metrics,
                "analysis_metrics": analysis_metrics,
                "manipulation_metrics": manipulation_metrics,
                "pattern_metrics": pattern_metrics,
            }

        except Exception as e:
            logger.error(f"Metrics retrieval failed: {e}")
            return {"success": False, "error": str(e)}

    async def validate_code(self, **kwargs) -> Dict[str, Any]:
        """Validate generated code"""
        try:
            code = kwargs.get("code", "")
            language = kwargs.get("language", "python")

            if not code:
                return {"success": False, "error": "No code provided for validation"}

            language_enum = CodeLanguage(language)
            ast_tree = await self.ast_generator._parse_code(code, language_enum)
            is_valid = await self.ast_manipulator.validate_ast(ast_tree)

            return {
                "success": True,
                "is_valid": is_valid,
                "language": language,
                "ast_node_count": len(list(ast.walk(ast_tree))) if ast_tree else 0,
            }

        except Exception as e:
            logger.error(f"Code validation failed: {e}")
            return {"success": False, "error": str(e), "is_valid": False}

    async def export_patterns(self, **kwargs) -> Dict[str, Any]:
        """Export code patterns to file"""
        try:
            file_path = kwargs.get("file_path", "patterns_export.json")

            success = await self.pattern_matcher.export_patterns(file_path)

            return {
                "success": success,
                "file_path": file_path,
                "message": (
                    "Patterns exported successfully"
                    if success
                    else "Failed to export patterns"
                ),
            }

        except Exception as e:
            logger.error(f"Pattern export failed: {e}")
            return {"success": False, "error": str(e)}

    async def import_patterns(self, **kwargs) -> Dict[str, Any]:
        """Import code patterns from file"""
        try:
            file_path = kwargs.get("file_path", "patterns_import.json")

            if not Path(file_path).exists():
                return {
                    "success": False,
                    "error": f"Pattern file not found: {file_path}",
                }

            imported_count = await self.pattern_matcher.import_patterns(file_path)

            return {
                "success": True,
                "imported_count": imported_count,
                "file_path": file_path,
                "message": f"Successfully imported {imported_count} patterns",
            }

        except Exception as e:
            logger.error(f"Pattern import failed: {e}")
            return {"success": False, "error": str(e)}

    async def cleanup(self, **kwargs) -> Dict[str, Any]:
        """Cleanup resources"""
        try:
            await self.ast_generator.cleanup()
            await self.ast_manipulator.cleanup()
            await self.code_analyzer.cleanup()
            await self.pattern_matcher.cleanup()

            return {"success": True, "message": "Cleanup completed successfully"}

        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
            return {"success": False, "error": str(e)}


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="AST-Based Code Generation CLI")
    parser.add_argument(
        "command",
        choices=[
            "generate",
            "analyze",
            "refactor",
            "merge",
            "patterns",
            "metrics",
            "validate",
            "export",
            "import",
            "cleanup",
        ],
        help="Command to execute",
    )

    parser.add_argument(
        "--language",
        default="python",
        choices=["python", "typescript", "javascript", "jsx", "tsx"],
        help="Programming language",
    )
    parser.add_argument("--target-file", help="Target file path")
    parser.add_argument("--requirements", nargs="*", help="Code requirements")
    parser.add_argument("--constraints", nargs="*", help="Code constraints")
    parser.add_argument(
        "--strategy",
        default="hybrid",
        choices=["template_based", "ast_manipulation", "pattern_based", "hybrid"],
        help="Generation strategy",
    )
    parser.add_argument("--code", help="Code to analyze/refactor/merge")
    parser.add_argument("--existing-code", help="Existing code for merging")
    parser.add_argument("--new-code", help="New code for merging")
    parser.add_argument("--refactoring-rules", nargs="*", help="Refactoring rules")
    parser.add_argument("--file-path", help="File path for export/import")
    parser.add_argument("--output", help="Output file path")

    args = parser.parse_args()

    async def run_command():
        commands = ASTGenerationCommands()

        try:
            if args.command == "generate":
                result = await commands.generate_code(
                    language=args.language,
                    target_file=args.target_file,
                    requirements=args.requirements or [],
                    constraints=args.constraints or [],
                    strategy=args.strategy,
                )
            elif args.command == "analyze":
                result = await commands.analyze_code(
                    code=args.code, language=args.language
                )
            elif args.command == "refactor":
                result = await commands.refactor_code(
                    code=args.code,
                    language=args.language,
                    refactoring_rules=args.refactoring_rules or [],
                )
            elif args.command == "merge":
                result = await commands.merge_code(
                    existing_code=args.existing_code,
                    new_code=args.new_code,
                    language=args.language,
                )
            elif args.command == "patterns":
                result = await commands.find_patterns(
                    requirements=args.requirements or [], language=args.language
                )
            elif args.command == "metrics":
                result = await commands.get_metrics()
            elif args.command == "validate":
                result = await commands.validate_code(
                    code=args.code, language=args.language
                )
            elif args.command == "export":
                result = await commands.export_patterns(file_path=args.file_path)
            elif args.command == "import":
                result = await commands.import_patterns(file_path=args.file_path)
            elif args.command == "cleanup":
                result = await commands.cleanup()
            else:
                result = {"success": False, "error": f"Unknown command: {args.command}"}

            # Output result
            if args.output:
                with open(args.output, "w") as f:
                    json.dump(result, f, indent=2)
            else:
                print(json.dumps(result, indent=2))

        except Exception as e:
            error_result = {"success": False, "error": str(e)}
            if args.output:
                with open(args.output, "w") as f:
                    json.dump(error_result, f, indent=2)
            else:
                print(json.dumps(error_result, indent=2))

        finally:
            await commands.cleanup()

    asyncio.run(run_command())


if __name__ == "__main__":
    main()
