#!/usr/bin/env python3
"""
File Cleanup Manager
Provides utilities for maintaining project directory cleanliness,
detecting duplicates, and managing obsolete/legacy/temp files.
"""

import hashlib
import logging
import os
import re
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class FileCleanupManager:
    """
    Manages project directory cleanliness and file organization.

    Provides utilities for:
    - Detecting duplicate files
    - Identifying obsolete/legacy files
    - Managing temp files
    - Enforcing cleanup rules
    """

    def __init__(self, project_root: str = "."):
        """
        Initialize the FileCleanupManager.

        Args:
            project_root: Path to the project root directory
        """
        self.project_root = Path(project_root)
        self.cleanup_config = self._load_cleanup_config()

        # File patterns to check
        self.duplicate_patterns = [
            r"(.+)_v\d+\.py$",
            r"(.+)_new\.py$",
            r"(.+)_updated\.py$",
            r"(.+)_copy\.py$",
            r"(.+)_backup\.py$",
        ]

        self.obsolete_patterns = [
            r".*\.bak$",
            r".*\.backup$",
            r".*_old\.py$",
            r".*_deprecated\.py$",
            r".*_legacy\.py$",
        ]

        self.temp_patterns = [
            r".*\.tmp$",
            r".*\.temp$",
            r"test_.*\.py$",
            r".*\.log$",
            r".*\.out$",
        ]

        self.build_artifacts = [
            "__pycache__",
            "*.pyc",
            "*.pyo",
            "*.pyc",
            ".pytest_cache",
            "htmlcov",
            ".coverage",
        ]

    def _load_cleanup_config(self) -> Dict:
        """Load cleanup configuration from file or use defaults."""
        config_path = self.project_root / "config" / "file_cleanup_config.json"

        if config_path.exists():
            try:
                import json

                with open(config_path, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load cleanup config: {e}")

        # Default configuration
        return {
            "enabled": True,
            "auto_cleanup": False,
            "backup_before_delete": True,
            "exclude_patterns": [".git", ".venv", "node_modules", "dist", "build"],
            "cleanup_rules": {
                "duplicates": True,
                "obsolete": True,
                "legacy": True,
                "temp": True,
                "build_artifacts": True,
            },
        }

    def detect_duplicate_files(self) -> List[Tuple[str, List[str]]]:
        """
        Detect duplicate files in the project.

        Returns:
            List of tuples containing (base_name, [duplicate_paths])
        """
        duplicates = []
        file_groups = {}

        # Walk through all files
        for root, dirs, files in os.walk(self.project_root):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not self._is_excluded(d)]

            for file in files:
                file_path = Path(root) / file

                # Skip excluded files
                if self._is_excluded_file(file_path):
                    continue

                # Check for duplicate patterns
                base_name = self._get_base_name(file)
                if base_name:
                    if base_name not in file_groups:
                        file_groups[base_name] = []
                    file_groups[base_name].append(str(file_path))

        # Find groups with multiple files
        for base_name, file_paths in file_groups.items():
            if len(file_paths) > 1:
                duplicates.append((base_name, file_paths))

        return duplicates

    def _get_base_name(self, filename: str) -> Optional[str]:
        """Extract base name from filename, removing version/duplicate suffixes."""
        for pattern in self.duplicate_patterns:
            match = re.match(pattern, filename)
            if match:
                return match.group(1)
        return None

    def _is_excluded(self, path: str) -> bool:
        """Check if path should be excluded from cleanup."""
        exclude_patterns = self.cleanup_config.get("exclude_patterns", [])
        return any(pattern in path for pattern in exclude_patterns)

    def _is_excluded_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from cleanup."""
        # Skip hidden files
        if file_path.name.startswith("."):
            return True

        # Skip excluded patterns
        exclude_patterns = self.cleanup_config.get("exclude_patterns", [])
        return any(pattern in str(file_path) for pattern in exclude_patterns)

    def find_obsolete_files(self) -> List[str]:
        """
        Find obsolete files in the project.

        Returns:
            List of obsolete file paths
        """
        obsolete_files = []

        for root, dirs, files in os.walk(self.project_root):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not self._is_excluded(d)]

            for file in files:
                file_path = Path(root) / file

                # Skip excluded files
                if self._is_excluded_file(file_path):
                    continue

                # Check for obsolete patterns
                if self._is_obsolete_file(file):
                    obsolete_files.append(str(file_path))

        return obsolete_files

    def _is_obsolete_file(self, filename: str) -> bool:
        """Check if file matches obsolete patterns."""
        for pattern in self.obsolete_patterns:
            if re.match(pattern, filename):
                return True
        return False

    def find_temp_files(self) -> List[str]:
        """
        Find temporary files in the project.

        Returns:
            List of temporary file paths
        """
        temp_files = []

        for root, dirs, files in os.walk(self.project_root):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not self._is_excluded(d)]

            for file in files:
                file_path = Path(root) / file

                # Skip excluded files
                if self._is_excluded_file(file_path):
                    continue

                # Check for temp patterns
                if self._is_temp_file(file):
                    temp_files.append(str(file_path))

        return temp_files

    def _is_temp_file(self, filename: str) -> bool:
        """Check if file matches temp patterns."""
        for pattern in self.temp_patterns:
            if re.match(pattern, filename):
                return True
        return False

    def find_build_artifacts(self) -> List[str]:
        """
        Find build artifacts in the project.

        Returns:
            List of build artifact paths
        """
        artifacts = []

        for root, dirs, files in os.walk(self.project_root):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not self._is_excluded(d)]

            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                if dir_name in self.build_artifacts:
                    artifacts.append(str(dir_path))

            for file in files:
                file_path = Path(root) / file

                # Skip excluded files
                if self._is_excluded_file(file_path):
                    continue

                # Check for build artifact patterns
                if self._is_build_artifact(file):
                    artifacts.append(str(file_path))

        return artifacts

    def _is_build_artifact(self, filename: str) -> bool:
        """Check if file is a build artifact."""
        for pattern in self.build_artifacts:
            if pattern.startswith("*"):
                if filename.endswith(pattern[1:]):
                    return True
            elif filename == pattern:
                return True
        return False

    def cleanup_files(
        self, file_paths: List[str], backup: bool = True
    ) -> Dict[str, bool]:
        """
        Clean up specified files.

        Args:
            file_paths: List of file paths to clean up
            backup: Whether to create backups before deletion

        Returns:
            Dictionary mapping file paths to cleanup success status
        """
        results = {}

        for file_path in file_paths:
            try:
                path = Path(file_path)

                if not path.exists():
                    results[file_path] = False
                    logger.warning(f"File does not exist: {file_path}")
                    continue

                # Create backup if requested
                if backup and self.cleanup_config.get("backup_before_delete", True):
                    backup_path = self._create_backup(path)
                    logger.info(f"Created backup: {backup_path}")

                # Remove file
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path)

                results[file_path] = True
                logger.info(f"Cleaned up: {file_path}")

            except Exception as e:
                results[file_path] = False
                logger.error(f"Failed to clean up {file_path}: {e}")

        return results

    def _create_backup(self, file_path: Path) -> Path:
        """Create a backup of the file before deletion."""
        backup_dir = self.project_root / "backups" / "cleanup"
        backup_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}.backup_{timestamp}"
        backup_path = backup_dir / backup_name

        if file_path.is_file():
            shutil.copy2(file_path, backup_path)
        elif file_path.is_dir():
            shutil.copytree(file_path, backup_path)

        return backup_path

    def generate_cleanup_report(self) -> Dict:
        """
        Generate a comprehensive cleanup report.

        Returns:
            Dictionary containing cleanup report
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "duplicates": self.detect_duplicate_files(),
            "obsolete": self.find_obsolete_files(),
            "temp": self.find_temp_files(),
            "build_artifacts": self.find_build_artifacts(),
            "summary": {},
        }

        # Generate summary
        report["summary"] = {
            "total_duplicates": len(report["duplicates"]),
            "total_obsolete": len(report["obsolete"]),
            "total_temp": len(report["temp"]),
            "total_build_artifacts": len(report["build_artifacts"]),
            "total_files_to_cleanup": (
                len(report["duplicates"])
                + len(report["obsolete"])
                + len(report["temp"])
                + len(report["build_artifacts"])
            ),
        }

        return report

    def auto_cleanup(self, dry_run: bool = True) -> Dict:
        """
        Perform automatic cleanup based on detected issues.

        Args:
            dry_run: If True, only report what would be cleaned up

        Returns:
            Dictionary containing cleanup results
        """
        report = self.generate_cleanup_report()
        results = {
            "dry_run": dry_run,
            "timestamp": datetime.now().isoformat(),
            "cleaned_files": [],
            "failed_files": [],
            "summary": report["summary"],
        }

        if dry_run:
            logger.info("DRY RUN - No files will be actually deleted")

        # Clean up duplicates
        for base_name, duplicate_paths in report["duplicates"]:
            # Keep the first file, remove the rest
            for duplicate_path in duplicate_paths[1:]:
                if dry_run:
                    results["cleaned_files"].append(duplicate_path)
                    logger.info(f"Would clean up duplicate: {duplicate_path}")
                else:
                    cleanup_result = self.cleanup_files([duplicate_path])
                    if cleanup_result[duplicate_path]:
                        results["cleaned_files"].append(duplicate_path)
                    else:
                        results["failed_files"].append(duplicate_path)

        # Clean up obsolete files
        for obsolete_path in report["obsolete"]:
            if dry_run:
                results["cleaned_files"].append(obsolete_path)
                logger.info(f"Would clean up obsolete: {obsolete_path}")
            else:
                cleanup_result = self.cleanup_files([obsolete_path])
                if cleanup_result[obsolete_path]:
                    results["cleaned_files"].append(obsolete_path)
                else:
                    results["failed_files"].append(obsolete_path)

        # Clean up temp files
        for temp_path in report["temp"]:
            if dry_run:
                results["cleaned_files"].append(temp_path)
                logger.info(f"Would clean up temp: {temp_path}")
            else:
                cleanup_result = self.cleanup_files([temp_path])
                if cleanup_result[temp_path]:
                    results["cleaned_files"].append(temp_path)
                else:
                    results["failed_files"].append(temp_path)

        # Clean up build artifacts
        for artifact_path in report["build_artifacts"]:
            if dry_run:
                results["cleaned_files"].append(artifact_path)
                logger.info(f"Would clean up build artifact: {artifact_path}")
            else:
                cleanup_result = self.cleanup_files([artifact_path])
                if cleanup_result[artifact_path]:
                    results["cleaned_files"].append(artifact_path)
                else:
                    results["failed_files"].append(artifact_path)

        return results

    def validate_cleanup_rules(self) -> Dict[str, bool]:
        """
        Validate that cleanup rules are being followed.

        Returns:
            Dictionary mapping rule names to compliance status
        """
        validation_results = {}

        # Check for duplicates
        duplicates = self.detect_duplicate_files()
        validation_results["no_duplicates"] = len(duplicates) == 0

        # Check for obsolete files
        obsolete = self.find_obsolete_files()
        validation_results["no_obsolete"] = len(obsolete) == 0

        # Check for temp files
        temp = self.find_temp_files()
        validation_results["no_temp"] = len(temp) == 0

        # Check for build artifacts
        artifacts = self.find_build_artifacts()
        validation_results["no_build_artifacts"] = len(artifacts) == 0

        return validation_results


# Convenience functions
def detect_duplicates(project_root: str = ".") -> List[Tuple[str, List[str]]]:
    """Detect duplicate files in the project."""
    manager = FileCleanupManager(project_root)
    return manager.detect_duplicate_files()


def find_obsolete_files(project_root: str = ".") -> List[str]:
    """Find obsolete files in the project."""
    manager = FileCleanupManager(project_root)
    return manager.find_obsolete_files()


def cleanup_project(project_root: str = ".", dry_run: bool = True) -> Dict:
    """Perform comprehensive project cleanup."""
    manager = FileCleanupManager(project_root)
    return manager.auto_cleanup(dry_run)


def validate_cleanup_rules(project_root: str = ".") -> Dict[str, bool]:
    """Validate that cleanup rules are being followed."""
    manager = FileCleanupManager(project_root)
    return manager.validate_cleanup_rules()
