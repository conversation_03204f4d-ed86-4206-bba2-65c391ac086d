#!/usr/bin/env python3
"""
Robustness API Routes for AI Coding Agent
Provides REST API endpoints for recovery, validation, and monitoring features.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from pydantic import BaseModel, Field

from api.agent_dependency import get_agent

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/robustness", tags=["Robustness"])


# Pydantic Models
class ValidationRequest(BaseModel):
    detailed: bool = Field(
        default=False, description="Return detailed validation results"
    )


class RecoveryRequest(BaseModel):
    failure_type: str = Field(..., description="Type of failure to recover from")
    context: Dict[str, Any] = Field(
        default_factory=dict, description="Recovery context"
    )


class ComponentValidationRequest(BaseModel):
    component_name: str = Field(..., description="Name of component to validate")


class MetricsRequest(BaseModel):
    hours: int = Field(default=24, description="Number of hours to include in summary")


class HealthResponse(BaseModel):
    overall_status: str
    components: Dict[str, Any]
    timestamp: str


class ValidationResponse(BaseModel):
    success: bool
    overall_status: str
    passed_validators: int
    failed_validators: int
    critical_issues: int
    high_issues: int
    medium_issues: int
    results: Optional[Dict[str, Any]] = None


class RecoveryResponse(BaseModel):
    success: bool
    message: str
    recovery_success: Optional[bool] = None


class MonitoringResponse(BaseModel):
    success: bool
    data: Dict[str, Any]


class RobustnessTestResponse(BaseModel):
    success: bool
    robustness_score: int
    recommendations: List[str]
    test_results: Dict[str, Any]


# API Endpoints
@router.get("/health", response_model=HealthResponse)
async def get_system_health():
    """Get overall system health status"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.check_system_health()
            if result.get("success"):
                return HealthResponse(**result)
            else:
                raise HTTPException(
                    status_code=500, detail=result.get("error", "Health check failed")
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate", response_model=ValidationResponse)
async def run_system_validation(request: ValidationRequest):
    """Run comprehensive system validation"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.run_system_validation(
                detailed=request.detailed
            )
            if result.get("success"):
                if request.detailed:
                    return ValidationResponse(
                        success=True,
                        overall_status=result["results"]["overall_status"],
                        passed_validators=result["results"]["passed_validators"],
                        failed_validators=result["results"]["failed_validators"],
                        critical_issues=result["results"]["critical_issues"],
                        high_issues=result["results"]["high_issues"],
                        medium_issues=result["results"]["medium_issues"],
                        results=result["results"],
                    )
                else:
                    summary = result["summary"]
                    return ValidationResponse(
                        success=True,
                        overall_status=summary["status"],
                        passed_validators=summary["passed"],
                        failed_validators=summary["failed"],
                        critical_issues=summary["critical_issues"],
                        high_issues=summary["high_issues"],
                        medium_issues=0,
                    )
            else:
                raise HTTPException(
                    status_code=500, detail=result.get("error", "Validation failed")
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"System validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate/component")
async def validate_component(request: ComponentValidationRequest):
    """Validate a specific system component"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.validate_specific_component(
                request.component_name
            )
            if result.get("success"):
                return result
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Component validation failed"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Component validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recovery/manual", response_model=RecoveryResponse)
async def manual_recovery(request: RecoveryRequest):
    """Manually trigger recovery for a specific failure type"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.manual_recovery(
                request.failure_type, request.context
            )
            if result.get("success"):
                return RecoveryResponse(**result)
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Manual recovery failed"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Manual recovery failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recovery/history")
async def get_recovery_history(hours: int = 24):
    """Get recovery system history"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.get_recovery_history(hours)
            if result.get("success"):
                return result
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Failed to get recovery history"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Failed to get recovery history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/start")
async def start_monitoring():
    """Start the monitoring system"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.start_monitoring()
            if result.get("success"):
                return result
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Failed to start monitoring"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Failed to start monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/stop")
async def stop_monitoring():
    """Stop the monitoring system"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.stop_monitoring()
            if result.get("success"):
                return result
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Failed to stop monitoring"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Failed to stop monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/dashboard", response_model=MonitoringResponse)
async def get_monitoring_dashboard():
    """Get monitoring dashboard data"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.get_monitoring_dashboard()
            if result.get("success"):
                return MonitoringResponse(**result)
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Failed to get dashboard data"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Failed to get dashboard data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/alerts")
async def get_alerts_summary():
    """Get summary of current alerts"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.get_alerts_summary()
            if result.get("success"):
                return result
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Failed to get alerts summary"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Failed to get alerts summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/monitoring/metrics")
async def get_metrics_summary(hours: int = 24):
    """Get metrics summary for specified time period"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.get_metrics_summary(hours)
            if result.get("success"):
                return result
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Failed to get metrics summary"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Failed to get metrics summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/test", response_model=RobustnessTestResponse)
async def run_robustness_test():
    """Run a comprehensive robustness test"""
    try:
        agent = get_agent()
        if hasattr(agent, "robustness_commands"):
            result = await agent.robustness_commands.run_robustness_test()
            if result.get("success"):
                return RobustnessTestResponse(**result)
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Robustness test failed"),
                )
        else:
            raise HTTPException(
                status_code=501, detail="Robustness commands not available"
            )
    except Exception as e:
        logger.error(f"Robustness test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status")
async def get_robustness_status():
    """Get overall robustness system status"""
    try:
        # Check if robustness systems are available
        status = {
            "recovery_system": False,
            "validation_system": False,
            "monitoring_system": False,
            "timestamp": datetime.now().isoformat(),
        }

        try:
            from core.recovery_system import get_recovery_system

            recovery_system = get_recovery_system()
            status["recovery_system"] = True
        except Exception:
            pass

        try:
            from core.validation import get_validation_system

            validation_system = get_validation_system()
            status["validation_system"] = True
        except Exception:
            pass

        try:
            from core.monitoring_enhanced import get_metrics_collector

            metrics_collector = get_metrics_collector()
            status["monitoring_system"] = True
        except Exception:
            pass

        return status

    except Exception as e:
        logger.error(f"Failed to get robustness status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
