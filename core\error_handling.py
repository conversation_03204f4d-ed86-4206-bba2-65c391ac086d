"""
Centralized error handling system for the application.
Provides base classes and utilities for consistent error handling across all modules.
"""

import json
import logging
import traceback
from datetime import datetime
from functools import wraps
from typing import Any, Callable, Dict, Optional, Type, cast


class BaseError(Exception):
    """Base exception class for all application-specific errors"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "GENERIC_ERROR"
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary representation"""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
            "timestamp": self.timestamp,
            "type": self.__class__.__name__,
        }


class ValidationError(BaseError):
    """Raised when validation fails"""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Any = None,
        level: Optional[str] = None,
    ):
        details = {"field": field, "value": value} if field else {}
        super().__init__(message, "VALIDATION_ERROR", details)

        # Lazy import to avoid circular dependency
        if level is not None:
            try:
                from core.validation import ValidationLevel

                if isinstance(level, str):
                    # Convert string to ValidationLevel enum
                    level_map = {
                        "DEBUG": ValidationLevel.DEBUG,
                        "INFO": ValidationLevel.INFO,
                        "WARNING": ValidationLevel.WARNING,
                        "ERROR": ValidationLevel.ERROR,
                        "CRITICAL": ValidationLevel.CRITICAL,
                    }
                    self.level = level_map.get(level, ValidationLevel.ERROR)
                else:
                    self.level = level
            except ImportError:
                # Fallback if ValidationLevel is not available
                self.level = level
        else:
            self.level = level
        self.field = field
        self.value = value

    def __str__(self) -> str:
        return self.message


class ConfigurationError(BaseError):
    """Raised when configuration is invalid or missing"""

    def __init__(self, message: str, config_key: Optional[str] = None):
        details = {"config_key": config_key} if config_key else {}
        super().__init__(message, "CONFIGURATION_ERROR", details)


class BaseErrorHandler:
    """Universal error handling with configurable policies"""

    def __init__(
        self,
        logger: Optional[logging.Logger] = None,
        config: Optional[Dict[str, Any]] = None,
    ):
        self.logger: logging.Logger = logger or logging.getLogger(__name__)
        self.config = config or {}
        self.error_policies = self.config.get("error_policies", {})
        self.formatters = {
            "json": self._format_json,
            "text": self._format_text,
            "cli": self._format_cli,
        }

    def handle_error(
        self, error: Exception, context: str = "general", **kwargs
    ) -> Dict[str, Any]:
        """Core error handling logic with context-aware processing"""
        error_data = self._create_error_data(error, context, **kwargs)

        # Apply error policies
        policy = self._get_error_policy(type(error))
        if policy.get("suppress", False):
            return error_data

        # Log based on severity
        self._log_error(error_data, policy)

        # Format for output
        formatter = self.formatters.get(policy.get("format", "text"))
        formatted_error = formatter(error_data)

        # Store for debugging if configured
        if policy.get("store", False):
            self._store_error(error_data)

        return formatted_error

    def _create_error_data(
        self, error: Exception, context: str, **kwargs
    ) -> Dict[str, Any]:
        """Create structured error data"""
        error_data = {
            "type": type(error).__name__,
            "message": str(error),
            "context": context,
            "timestamp": datetime.now().isoformat(),
            "severity": self._determine_severity(error),
            "traceback": (
                traceback.format_exc()
                if self.config.get("include_traceback", True)
                else None
            ),
        }

        # Add custom error attributes
        if isinstance(error, BaseError):
            error_data.update(
                {
                    "error_code": error.error_code,
                    "details": error.details,
                }
            )

        # Add any additional context
        error_data.update(kwargs)

        return error_data

    def _determine_severity(self, error: Exception) -> str:
        """Determine error severity based on type"""
        severity_map = {
            ValidationError: "warning",
            ConfigurationError: "error",
            ValueError: "warning",
            TypeError: "warning",
            FileNotFoundError: "error",
            PermissionError: "error",
        }

        return severity_map.get(type(error), "critical")

    def _get_error_policy(self, error_type: Type[Exception]) -> Dict[str, Any]:
        """Get handling policy for specific error type"""
        type_name = error_type.__name__
        return self.error_policies.get(
            type_name,
            self.error_policies.get(
                "default",
                {
                    "log_level": "error",
                    "format": "text",
                    "suppress": False,
                    "store": True,
                },
            ),
        )

    def _log_error(self, error_data: Dict[str, Any], policy: Dict[str, Any]) -> None:
        """Log error based on policy"""
        log_level = policy.get("log_level", "error")
        assert log_level is not None  # Ensure non-null for type checker
        # Convert log level name to numeric level
        log_level_num = getattr(logging, log_level.upper(), logging.ERROR)
        self.logger.log(
            log_level_num, f"Error in {error_data['context']}: {error_data['message']}"
        )

    def _format_json(self, error_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format error as JSON"""
        return error_data

    def _format_text(self, error_data: Dict[str, Any]) -> str:
        """Format error as human-readable text"""
        return f"[{error_data['severity'].upper()}] {error_data['context']}: {error_data['message']}"

    def _format_cli(self, error_data: Dict[str, Any]) -> str:
        """Format error for CLI output"""
        return f"❌ Error: {error_data['message']}"

    def _store_error(self, error_data: Dict[str, Any]) -> None:
        """Store error for debugging purposes"""
        # Implementation for storing errors (file, database, etc.)
        pass


class ErrorHandlerRegistry:
    """Registry for managing error handlers across different contexts"""

    def __init__(self):
        self.handlers: Dict[str, BaseErrorHandler] = {}

    def register(self, context: str, handler: BaseErrorHandler) -> None:
        """Register an error handler for a specific context"""
        self.handlers[context] = handler

    def get_handler(self, context: str) -> BaseErrorHandler:
        """Get error handler for a context, create default if not exists"""
        if context not in self.handlers:
            self.handlers[context] = BaseErrorHandler()
        return self.handlers[context]


# Global registry instance
error_registry = ErrorHandlerRegistry()


def error_handler(context: str = "general", **handler_kwargs):
    """
    Decorator for automatic error handling in functions

    Usage:
        @error_handler("my_module")
        def my_function():
            ...
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            handler = error_registry.get_handler(context)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_result = handler.handle_error(e, context, function=func.__name__)
                # Re-raise critical errors, return formatted errors for others
                if isinstance(e, (BaseError, ValidationError, ConfigurationError)):
                    return error_result
                else:
                    raise

        return wrapper

    return decorator


class ErrorMiddleware:
    """Middleware for handling errors in web applications"""

    def __init__(self, handler: BaseErrorHandler):
        self.handler = handler

    def __call__(self, request, call_next):
        """Process request and handle any errors"""
        try:
            response = call_next(request)
            return response
        except Exception as e:
            error_result = self.handler.handle_error(e, "web_request")
            return self._create_error_response(error_result)

    def _create_error_response(self, error_data: Dict[str, Any]):
        """Create appropriate HTTP response for error"""
        from fastapi import HTTPException

        status_code = 500
        if isinstance(error_data, dict):
            if error_data.get("type") == "ValidationError":
                status_code = 422
            elif error_data.get("type") == "ConfigurationError":
                status_code = 500

        raise HTTPException(status_code=status_code, detail=error_data)


# Convenience functions for common usage
def handle_validation_error(
    message: str, field: Optional[str] = None, value: Any = None
) -> None:
    """Convenience function to raise validation errors"""
    raise ValidationError(message, field, value)


def handle_config_error(message: str, config_key: Optional[str] = None) -> None:
    """Convenience function to raise configuration errors"""
    raise ConfigurationError(message, config_key)
