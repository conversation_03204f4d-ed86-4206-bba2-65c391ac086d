"""
Phase 2.4: Maintenance Engine
Provides broken-link checking, automated dependency updates, human approval gates, and maintenance logging.
Integrates with Phase 2.3 CMS & Content for comprehensive site maintenance.
"""

import hashlib
import json
import logging
import os
import re
import smtplib
import sqlite3
import subprocess
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from pathlib import Path
from queue import Queue
from typing import Any, Dict, List, Optional, Set, Tuple
from urllib.parse import urljoin, urlparse

import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/maintenance_engine.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


@dataclass
class LinkCheckResult:
    """Result of a link check"""

    url: str
    source_content_id: Optional[str]
    source_file: Optional[str]
    status_code: Optional[int]
    is_valid: bool
    response_time: float
    error_message: Optional[str]
    checked_at: datetime
    link_text: Optional[str]
    link_type: str  # 'internal', 'external', 'media'


@dataclass
class DependencyUpdate:
    """Dependency update information"""

    package_name: str
    current_version: str
    available_version: str
    update_type: str  # 'patch', 'minor', 'major'
    changelog_url: Optional[str]
    security_impact: bool
    breaking_changes: bool
    approval_required: bool
    checked_at: datetime


@dataclass
class MaintenanceTask:
    """Maintenance task information"""

    task_id: str
    task_type: str  # 'link_check', 'dependency_update', 'content_audit', 'backup'
    status: str  # 'pending', 'running', 'completed', 'failed', 'approved', 'rejected'
    priority: str  # 'low', 'medium', 'high', 'critical'
    description: str
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    result_data: Dict[str, Any]
    approval_required: bool
    approved_by: Optional[str]
    approved_at: Optional[datetime]
    error_message: Optional[str]


@dataclass
class MaintenanceLog:
    """Maintenance log entry"""

    log_id: str
    task_id: str
    level: str  # 'info', 'warning', 'error', 'critical'
    message: str
    timestamp: datetime
    context: Dict[str, Any]
    user_id: Optional[str]


class BrokenLinkChecker:
    """Broken link checker with CMS integration"""

    def __init__(self, config: Dict[str, Any], cms_manager=None):
        self.config = config
        self.cms_manager = cms_manager
        self.link_db_path = Path("data/links.db")
        self.max_workers = config.get("link_checker", {}).get("max_workers", 10)
        self.timeout = config.get("link_checker", {}).get("timeout", 30)
        self.retry_attempts = config.get("link_checker", {}).get("retry_attempts", 3)
        self._init_link_database()

    def _init_link_database(self):
        """Initialize link database"""
        try:
            self.link_db_path.parent.mkdir(exist_ok=True)
            conn = sqlite3.connect(self.link_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS links (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    source_content_id TEXT,
                    source_file TEXT,
                    last_checked DATETIME,
                    status_code INTEGER,
                    is_valid BOOLEAN,
                    response_time REAL,
                    error_message TEXT,
                    link_text TEXT,
                    link_type TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS link_check_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT NOT NULL,
                    checked_at DATETIME,
                    status_code INTEGER,
                    is_valid BOOLEAN,
                    response_time REAL,
                    error_message TEXT
                )
            """
            )

            conn.commit()
            conn.close()
            logger.info("Link database initialized")

        except Exception as e:
            logger.error(f"Error initializing link database: {e}")

    def extract_links_from_content(
        self,
        content: str,
        content_id: Optional[str] = None,
        file_path: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """Extract links from content"""
        links = []

        # HTML link patterns
        html_patterns = [
            # <a href="...">text</a>
            r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>([^<]*)</a>',
            r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>',  # <img src="...">
            r'<link[^>]+href=["\']([^"\']+)["\'][^>]*>',  # <link href="...">
            r'<script[^>]+src=["\']([^"\']+)["\'][^>]*>',  # <script src="...">
        ]

        for pattern in html_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    url = match[0]
                    link_text = match[1] if len(match) > 1 else None
                else:
                    url = match
                    link_text = None

                if url and not url.startswith(("#", "javascript:", "mailto:")):
                    links.append(
                        {
                            "url": url,
                            "link_text": link_text,
                            "link_type": self._determine_link_type(url),
                            "source_content_id": content_id,
                            "source_file": file_path,
                        }
                    )

        return links

    def _determine_link_type(self, url: str) -> str:
        """Determine link type"""
        if url.startswith(("http://", "https://")):
            return "external"
        elif url.startswith(("/", "./", "../")):
            return "internal"
        else:
            return "media"

    def check_link(
        self,
        url: str,
        source_content_id: Optional[str] = None,
        source_file: Optional[str] = None,
    ) -> LinkCheckResult:
        """Check a single link"""
        start_time = time.time()
        error_message = None
        status_code = None
        is_valid = False  # Initialize is_valid to avoid unbound variable error

        try:
            # Handle relative URLs
            if not url.startswith(("http://", "https://")):
                if self.cms_manager and source_file:
                    # Resolve relative URLs based on content location
                    base_url = self._resolve_base_url(source_file)
                    url = urljoin(base_url, url)
                else:
                    # Skip relative URLs if we can't resolve them
                    return LinkCheckResult(
                        url=url,
                        source_content_id=source_content_id,
                        source_file=source_file,
                        status_code=None,
                        is_valid=False,
                        response_time=0,
                        error_message="Cannot resolve relative URL",
                        checked_at=datetime.now(),
                        link_text=None,
                        link_type=self._determine_link_type(url),
                    )

            # Check external links
            if url.startswith(("http://", "https://")):
                for attempt in range(self.retry_attempts):
                    try:
                        response = requests.head(
                            url,
                            timeout=self.timeout,
                            allow_redirects=True,
                            headers={
                                "User-Agent": "AI-Coding-Agent-Maintenance-Engine/1.0"
                            },
                        )
                        status_code = response.status_code
                        is_valid = 200 <= status_code < 400
                        break
                    except requests.exceptions.RequestException as e:
                        error_message = str(e)
                        if attempt == self.retry_attempts - 1:
                            is_valid = False
                        else:
                            time.sleep(1)  # Brief delay before retry
            else:
                # For internal/media links, check if file exists
                file_path = self._resolve_local_path(url)
                is_valid = bool(file_path and file_path.exists())
                status_code = 200 if is_valid else 404
                if not is_valid:
                    error_message = f"File not found: {url}"

            response_time = time.time() - start_time

            return LinkCheckResult(
                url=url,
                source_content_id=source_content_id,
                source_file=source_file,
                status_code=status_code,
                is_valid=is_valid,
                response_time=response_time,
                error_message=error_message,
                checked_at=datetime.now(),
                link_text=None,
                link_type=self._determine_link_type(url),
            )

        except Exception as e:
            response_time = time.time() - start_time
            return LinkCheckResult(
                url=url,
                source_content_id=source_content_id,
                source_file=source_file,
                status_code=None,
                is_valid=False,
                response_time=response_time,
                error_message=str(e),
                checked_at=datetime.now(),
                link_text=None,
                link_type=self._determine_link_type(url),
            )

    def _resolve_base_url(self, file_path: str) -> str:
        """Resolve base URL for relative links"""
        # This would be configured based on your hosting setup
        base_url = self.config.get("maintenance", {}).get(
            "base_url", "http://localhost:5000"
        )
        return base_url

    def _resolve_local_path(self, url: str) -> Optional[Path]:
        """Resolve local file path for internal links"""
        try:
            # Remove leading slash and resolve relative to content directory
            clean_url = url.lstrip("/")
            content_dir = Path(
                self.config.get("content", {}).get("content_dir", "content")
            )
            return content_dir / clean_url
        except Exception:
            return None

    def check_all_links(
        self, content_items: Optional[List[Any]] = None
    ) -> List[LinkCheckResult]:
        """Check all links in content"""
        all_links = []

        # Extract links from CMS content if available
        if self.cms_manager and content_items:
            for item in content_items:
                links = self.extract_links_from_content(
                    item.content, item.id, item.file_path
                )
                all_links.extend(links)

        # Extract links from static files
        static_files = self._find_static_files()
        for file_path in static_files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    links = self.extract_links_from_content(
                        content, file_path=str(file_path)
                    )
                    all_links.extend(links)
            except Exception as e:
                logger.warning(f"Error reading file {file_path}: {e}")

        # Remove duplicates
        unique_links = {}
        for link in all_links:
            key = link["url"]
            if key not in unique_links:
                unique_links[key] = link

        # Check links in parallel
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_link = {
                executor.submit(
                    self.check_link,
                    link["url"],
                    link.get("source_content_id") or "",
                    link.get("source_file") or "",
                ): link
                for link in unique_links.values()
            }

            for future in as_completed(future_to_link):
                try:
                    result = future.result()
                    results.append(result)
                    self._save_link_result(result)
                except Exception as e:
                    logger.error(f"Error checking link: {e}")

        return results

    def _find_static_files(self) -> List[Path]:
        """Find static files to check for links"""
        static_dirs = [
            Path(self.config.get("content", {}).get("content_dir", "content")),
            Path("sites"),
            Path("templates"),
        ]

        files = []
        for static_dir in static_dirs:
            if static_dir.exists():
                for file_path in static_dir.rglob("*.html"):
                    files.append(file_path)
                for file_path in static_dir.rglob("*.md"):
                    files.append(file_path)

        return files

    def _save_link_result(self, result: LinkCheckResult):
        """Save link check result to database"""
        try:
            conn = sqlite3.connect(self.link_db_path)
            cursor = conn.cursor()

            # Update or insert link record
            cursor.execute(
                """
                INSERT OR REPLACE INTO links
                (url, source_content_id, source_file, last_checked, status_code,
                 is_valid, response_time, error_message, link_text, link_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    result.url,
                    result.source_content_id,
                    result.source_file,
                    result.checked_at,
                    result.status_code,
                    result.is_valid,
                    result.response_time,
                    result.error_message,
                    result.link_text,
                    result.link_type,
                ),
            )

            # Add to history
            cursor.execute(
                """
                INSERT INTO link_check_history
                (url, checked_at, status_code, is_valid, response_time, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
            """,
                (
                    result.url,
                    result.checked_at,
                    result.status_code,
                    result.is_valid,
                    result.response_time,
                    result.error_message,
                ),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error saving link result: {e}")

    def get_broken_links_report(self) -> Dict[str, Any]:
        """Generate broken links report"""
        try:
            conn = sqlite3.connect(self.link_db_path)
            cursor = conn.cursor()

            # Get broken links
            cursor.execute(
                """
                SELECT url, source_content_id, source_file, last_checked,
                       status_code, error_message, link_type
                FROM links
                WHERE is_valid = 0
                ORDER BY last_checked DESC
            """
            )

            broken_links = []
            for row in cursor.fetchall():
                broken_links.append(
                    {
                        "url": row[0],
                        "source_content_id": row[1],
                        "source_file": row[2],
                        "last_checked": row[3],
                        "status_code": row[4],
                        "error_message": row[5],
                        "link_type": row[6],
                    }
                )

            # Get statistics
            cursor.execute("SELECT COUNT(*) FROM links")
            total_links = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM links WHERE is_valid = 1")
            valid_links = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM links WHERE is_valid = 0")
            broken_links_count = cursor.fetchone()[0]

            conn.close()

            return {
                "total_links": total_links,
                "valid_links": valid_links,
                "broken_links": broken_links_count,
                "broken_links_list": broken_links,
                "health_percentage": (
                    (valid_links / total_links * 100) if total_links > 0 else 0
                ),
            }

        except Exception as e:
            logger.error(f"Error generating broken links report: {e}")
            return {}


class DependencyManager:
    """Dependency update manager"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.requirements_file = Path("requirements.txt")
        self.dev_requirements_file = Path("requirements-dev.txt")
        self.package_lock_file = Path("package-lock.json")
        self.update_db_path = Path("data/dependency_updates.db")
        self._init_update_database()

    def _init_update_database(self):
        """Initialize dependency update database"""
        try:
            self.update_db_path.parent.mkdir(exist_ok=True)
            conn = sqlite3.connect(self.update_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS dependency_updates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    package_name TEXT NOT NULL,
                    current_version TEXT NOT NULL,
                    available_version TEXT NOT NULL,
                    update_type TEXT NOT NULL,
                    changelog_url TEXT,
                    security_impact BOOLEAN,
                    breaking_changes BOOLEAN,
                    approval_required BOOLEAN,
                    checked_at DATETIME,
                    applied_at DATETIME,
                    applied_by TEXT,
                    status TEXT DEFAULT 'pending'
                )
            """
            )

            conn.commit()
            conn.close()
            logger.info("Dependency update database initialized")

        except Exception as e:
            logger.error(f"Error initializing dependency update database: {e}")

    def check_python_dependencies(self) -> List[DependencyUpdate]:
        """Check for Python dependency updates"""
        updates: List[DependencyUpdate] = []

        try:
            # Read current requirements
            if not self.requirements_file.exists():
                logger.warning("requirements.txt not found")
                return updates

            current_packages = self._parse_requirements_file(self.requirements_file)

            # Check each package for updates
            for package_name, current_version in current_packages.items():
                try:
                    update_info = self._check_package_update(
                        package_name, current_version
                    )
                    if update_info:
                        updates.append(update_info)
                        self._save_dependency_update(update_info)
                except Exception as e:
                    logger.warning(f"Error checking package {package_name}: {e}")

            return updates

        except Exception as e:
            logger.error(f"Error checking Python dependencies: {e}")
            return updates

    def _parse_requirements_file(self, file_path: Path) -> Dict[str, str]:
        """Parse requirements file"""
        packages = {}
        try:
            with open(file_path, "r") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#"):
                        # Handle different requirement formats
                        if "==" in line:
                            package, version = line.split("==", 1)
                        elif ">=" in line:
                            package, version = line.split(">=", 1)
                        elif "<=" in line:
                            package, version = line.split("<=", 1)
                        else:
                            package = line
                            version = "unknown"

                        packages[package.strip()] = version.strip()
        except Exception as e:
            logger.error(f"Error parsing requirements file: {e}")

        return packages

    def _check_package_update(
        self, package_name: str, current_version: str
    ) -> Optional[DependencyUpdate]:
        """Check if a package has updates available"""
        try:
            # Use pip to check for updates
            result = subprocess.run(
                [sys.executable, "-m", "pip", "index", "versions", package_name],
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                # Parse the output to get latest version
                output = result.stdout
                # This is a simplified parser - in production you'd want a more robust approach
                if "LATEST:" in output:
                    latest_version = output.split("LATEST:")[1].split()[0].strip()

                    if latest_version != current_version:
                        update_type = self._determine_update_type(
                            current_version, latest_version
                        )
                        security_impact = self._check_security_impact(
                            package_name, latest_version
                        )
                        breaking_changes = self._check_breaking_changes(
                            package_name, current_version, latest_version
                        )

                        return DependencyUpdate(
                            package_name=package_name,
                            current_version=current_version,
                            available_version=latest_version,
                            update_type=update_type,
                            changelog_url=self._get_changelog_url(
                                package_name, latest_version
                            ),
                            security_impact=security_impact,
                            breaking_changes=breaking_changes,
                            approval_required=update_type == "major"
                            or breaking_changes,
                            checked_at=datetime.now(),
                        )

            return None

        except Exception as e:
            logger.error(f"Error checking package {package_name}: {e}")
            return None

    def _determine_update_type(self, current: str, latest: str) -> str:
        """Determine update type (patch, minor, major)"""
        try:
            current_parts = current.split(".")
            latest_parts = latest.split(".")

            if len(current_parts) >= 3 and len(latest_parts) >= 3:
                if latest_parts[0] > current_parts[0]:
                    return "major"
                elif latest_parts[1] > current_parts[1]:
                    return "minor"
                elif latest_parts[2] > current_parts[2]:
                    return "patch"

            return "patch"
        except Exception:
            return "patch"

    def _check_security_impact(self, package_name: str, version: str) -> bool:
        """Check if update has security implications"""
        # This would integrate with security databases like NVD
        # For now, return False as a placeholder
        return False

    def _check_breaking_changes(
        self, package_name: str, current_version: str, latest_version: str
    ) -> bool:
        """Check if update has breaking changes"""
        # This would check changelogs or use semantic versioning rules
        # For now, assume major version updates have breaking changes
        return self._determine_update_type(current_version, latest_version) == "major"

    def _get_changelog_url(self, package_name: str, version: str) -> Optional[str]:
        """Get changelog URL for package"""
        # This would construct URLs to package changelogs
        # For now, return None as a placeholder
        return None

    def _save_dependency_update(self, update: DependencyUpdate):
        """Save dependency update to database"""
        try:
            conn = sqlite3.connect(self.update_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT OR REPLACE INTO dependency_updates
                (package_name, current_version, available_version, update_type,
                 changelog_url, security_impact, breaking_changes, approval_required, checked_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    update.package_name,
                    update.current_version,
                    update.available_version,
                    update.update_type,
                    update.changelog_url,
                    update.security_impact,
                    update.breaking_changes,
                    update.approval_required,
                    update.checked_at,
                ),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error saving dependency update: {e}")

    def apply_dependency_update(
        self, package_name: str, user_id: Optional[str] = None
    ) -> bool:
        """Apply a dependency update"""
        try:
            # Get update information
            conn = sqlite3.connect(self.update_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT available_version FROM dependency_updates
                WHERE package_name = ? AND status = 'pending'
            """,
                (package_name,),
            )

            result = cursor.fetchone()
            if not result:
                logger.error(f"No pending update found for {package_name}")
                return False

            available_version = result[0]

            # Update requirements.txt
            self._update_requirements_file(package_name, available_version)

            # Install the update
            subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "pip",
                    "install",
                    f"{package_name}=={available_version}",
                ],
                check=True,
            )

            # Update database
            cursor.execute(
                """
                UPDATE dependency_updates
                SET status = 'applied', applied_at = ?, applied_by = ?
                WHERE package_name = ? AND status = 'pending'
            """,
                (datetime.now(), user_id, package_name),
            )

            conn.commit()
            conn.close()

            logger.info(f"Successfully applied update for {package_name}")
            return True

        except Exception as e:
            logger.error(f"Error applying dependency update: {e}")
            return False

    def _update_requirements_file(self, package_name: str, new_version: str):
        """Update requirements.txt with new version"""
        try:
            with open(self.requirements_file, "r") as f:
                lines = f.readlines()

            with open(self.requirements_file, "w") as f:
                for line in lines:
                    if line.startswith(package_name):
                        f.write(f"{package_name}=={new_version}\n")
                    else:
                        f.write(line)

        except Exception as e:
            logger.error(f"Error updating requirements file: {e}")


class MaintenanceEngine:
    """Main maintenance engine coordinating all maintenance tasks"""

    def __init__(self, config_path: str = "config/maintenance_config.json"):
        from utils.config_loader import ConfigLoader

        self.config = ConfigLoader.load_config_with_defaults(
            config_path, self._get_default_config()
        )
        self.task_queue: Queue = Queue()
        self.approval_queue: Queue = Queue()
        self.maintenance_db_path = Path("data/maintenance.db")
        self._init_maintenance_database()

        # Initialize components
        self.link_checker = BrokenLinkChecker(self.config)
        self.dependency_manager = DependencyManager(self.config)

        # Start maintenance worker
        self.worker_thread = threading.Thread(
            target=self._maintenance_worker, daemon=True
        )
        self.worker_thread.start()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default maintenance configuration"""
        return {
            "maintenance": {
                "base_url": "http://localhost:5000",
                "check_interval_hours": 24,
                "approval_required": True,
                "notification_email": None,
            },
            "link_checker": {
                "max_workers": 10,
                "timeout": 30,
                "retry_attempts": 3,
                "check_interval_hours": 6,
            },
            "dependency_manager": {
                "check_interval_hours": 24,
                "auto_update_minor": False,
                "auto_update_patch": True,
                "security_updates_only": False,
            },
            "logging": {"retention_days": 30, "log_level": "INFO"},
        }

    def _init_maintenance_database(self):
        """Initialize maintenance database"""
        try:
            self.maintenance_db_path.parent.mkdir(exist_ok=True)
            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS maintenance_tasks (
                    task_id TEXT PRIMARY KEY,
                    task_type TEXT NOT NULL,
                    status TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    description TEXT NOT NULL,
                    created_at DATETIME NOT NULL,
                    started_at DATETIME,
                    completed_at DATETIME,
                    result_data TEXT,
                    approval_required BOOLEAN,
                    approved_by TEXT,
                    approved_at DATETIME,
                    error_message TEXT
                )
            """
            )

            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS maintenance_logs (
                    log_id TEXT PRIMARY KEY,
                    task_id TEXT NOT NULL,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    context TEXT,
                    user_id TEXT,
                    FOREIGN KEY (task_id) REFERENCES maintenance_tasks (task_id)
                )
            """
            )

            conn.commit()
            conn.close()
            logger.info("Maintenance database initialized")

        except Exception as e:
            logger.error(f"Error initializing maintenance database: {e}")

    def _maintenance_worker(self):
        """Background maintenance worker"""
        while True:
            try:
                # Check for scheduled tasks
                self._check_scheduled_tasks()

                # Process task queue
                while not self.task_queue.empty():
                    task = self.task_queue.get()
                    self._execute_task(task)

                # Sleep for a while
                time.sleep(300)  # 5 minutes

            except Exception as e:
                logger.error(f"Error in maintenance worker: {e}")
                time.sleep(60)  # Wait a minute before retrying

    def _check_scheduled_tasks(self):
        """Check for scheduled maintenance tasks"""
        try:
            # Check if it's time for link checking
            if self._should_run_link_check():
                self.schedule_link_check()

            # Check if it's time for dependency updates
            if self._should_run_dependency_check():
                self.schedule_dependency_check()

        except Exception as e:
            logger.error(f"Error checking scheduled tasks: {e}")

    def _should_run_link_check(self) -> bool:
        """Check if link check should run"""
        try:
            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT MAX(created_at) FROM maintenance_tasks
                WHERE task_type = 'link_check' AND status = 'completed'
            """
            )

            result = cursor.fetchone()
            conn.close()

            if not result[0]:
                return True

            last_run = datetime.fromisoformat(result[0])
            interval_hours = self.config.get("link_checker", {}).get(
                "check_interval_hours", 6
            )

            return datetime.now() - last_run > timedelta(hours=interval_hours)

        except Exception as e:
            logger.error(f"Error checking link check schedule: {e}")
            return False

    def _should_run_dependency_check(self) -> bool:
        """Check if dependency check should run"""
        try:
            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT MAX(created_at) FROM maintenance_tasks
                WHERE task_type = 'dependency_check' AND status = 'completed'
            """
            )

            result = cursor.fetchone()
            conn.close()

            if not result[0]:
                return True

            last_run = datetime.fromisoformat(result[0])
            interval_hours = self.config.get("dependency_manager", {}).get(
                "check_interval_hours", 24
            )

            return datetime.now() - last_run > timedelta(hours=interval_hours)

        except Exception as e:
            logger.error(f"Error checking dependency check schedule: {e}")
            return False

    def schedule_link_check(self, priority: str = "medium") -> str:
        """Schedule a link check task"""
        task_id = f"link_check_{int(time.time())}"
        task = MaintenanceTask(
            task_id=task_id,
            task_type="link_check",
            status="pending",
            priority=priority,
            description="Check all links for broken URLs",
            created_at=datetime.now(),
            started_at=None,
            completed_at=None,
            result_data={},
            approval_required=False,
            approved_by=None,
            approved_at=None,
            error_message=None,
        )

        self.task_queue.put(task)
        self._save_task(task)
        self._log_task(task_id, "info", "Link check task scheduled")

        return task_id

    def schedule_dependency_check(self, priority: str = "medium") -> str:
        """Schedule a dependency check task"""
        task_id = f"dependency_check_{int(time.time())}"
        task = MaintenanceTask(
            task_id=task_id,
            task_type="dependency_check",
            status="pending",
            priority=priority,
            description="Check for dependency updates",
            created_at=datetime.now(),
            started_at=None,
            completed_at=None,
            result_data={},
            approval_required=False,
            approved_by=None,
            approved_at=None,
            error_message=None,
        )

        self.task_queue.put(task)
        self._save_task(task)
        self._log_task(task_id, "info", "Dependency check task scheduled")

        return task_id

    def _execute_task(self, task: MaintenanceTask):
        """Execute a maintenance task"""
        try:
            # Update task status
            task.status = "running"
            task.started_at = datetime.now()
            self._update_task(task)

            self._log_task(task.task_id, "info", f"Starting {task.task_type} task")

            if task.task_type == "link_check":
                self._execute_link_check(task)
            elif task.task_type == "dependency_check":
                self._execute_dependency_check(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")

            # Mark task as completed
            task.status = "completed"
            task.completed_at = datetime.now()
            self._update_task(task)

            self._log_task(task.task_id, "info", f"Completed {task.task_type} task")

        except Exception as e:
            task.status = "failed"
            task.completed_at = datetime.now()
            task.error_message = str(e)
            self._update_task(task)

            self._log_task(task.task_id, "error", f"Task failed: {e}")

    def _execute_link_check(self, task: MaintenanceTask):
        """Execute link check task"""
        try:
            # Get content items from CMS if available
            content_items = []
            if (
                hasattr(self.link_checker, "cms_manager")
                and self.link_checker.cms_manager
            ):
                content_items = self.link_checker.cms_manager.list_content()

            # Perform link check
            results = self.link_checker.check_all_links(content_items)

            # Generate report
            report = self.link_checker.get_broken_links_report()

            # Update task result
            task.result_data = {
                "total_links_checked": len(results),
                "broken_links_count": len([r for r in results if not r.is_valid]),
                "report": {
                    "total_links": report.get("total_links", 0),
                    "valid_links": report.get("valid_links", 0),
                    "broken_links": report.get("broken_links", 0),
                    "health_percentage": report.get("health_percentage", 0),
                },
            }

            # Send notification if broken links found
            if report.get("broken_links_count", 0) > 0:
                self._send_notification(
                    f"Found {report['broken_links_count']} broken links",
                    f"Link check completed. {report['broken_links_count']} broken links found out of {report['total_links']} total links.",
                )

        except Exception as e:
            logger.error(f"Error executing link check: {e}")
            raise

    def _execute_dependency_check(self, task: MaintenanceTask):
        """Execute dependency check task"""
        try:
            # Check for updates
            updates = self.dependency_manager.check_python_dependencies()

            # Filter updates based on configuration
            auto_update_patch = self.config.get("dependency_manager", {}).get(
                "auto_update_patch", True
            )
            auto_update_minor = self.config.get("dependency_manager", {}).get(
                "auto_update_minor", False
            )

            pending_updates = []
            auto_applied = []

            for update in updates:
                if update.update_type == "patch" and auto_update_patch:
                    # Auto-apply patch updates
                    if self.dependency_manager.apply_dependency_update(
                        update.package_name
                    ):
                        auto_applied.append(update)
                elif update.update_type == "minor" and auto_update_minor:
                    # Auto-apply minor updates
                    if self.dependency_manager.apply_dependency_update(
                        update.package_name
                    ):
                        auto_applied.append(update)
                else:
                    # Require approval for major updates or when auto-update is disabled
                    pending_updates.append(update)

            # Update task result
            task.result_data = {
                "total_updates_found": len(updates),
                "auto_applied": len(auto_applied),
                "pending_approval": len(pending_updates),
                "updates": [
                    {
                        "package_name": update.package_name,
                        "current_version": update.current_version,
                        "available_version": update.available_version,
                        "update_type": update.update_type,
                        "security_impact": update.security_impact,
                        "breaking_changes": update.breaking_changes,
                        "approval_required": update.approval_required,
                    }
                    for update in updates
                ],
            }

            # Send notification if updates found
            if updates:
                self._send_notification(
                    f"Found {len(updates)} dependency updates",
                    f"Dependency check completed. {len(auto_applied)} auto-applied, {len(pending_updates)} pending approval.",
                )

        except Exception as e:
            logger.error(f"Error executing dependency check: {e}")
            raise

    def _save_task(self, task: MaintenanceTask):
        """Save task to database"""
        try:
            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT OR REPLACE INTO maintenance_tasks
                (task_id, task_type, status, priority, description, created_at,
                 started_at, completed_at, result_data, approval_required,
                 approved_by, approved_at, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    task.task_id,
                    task.task_type,
                    task.status,
                    task.priority,
                    task.description,
                    task.created_at,
                    task.started_at,
                    task.completed_at,
                    json.dumps(task.result_data),
                    task.approval_required,
                    task.approved_by,
                    task.approved_at,
                    task.error_message,
                ),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error saving task: {e}")

    def _update_task(self, task: MaintenanceTask):
        """Update task in database"""
        self._save_task(task)

    def _log_task(
        self,
        task_id: str,
        level: str,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
    ):
        """Log maintenance task activity"""
        try:
            log_id = f"log_{int(time.time())}_{hashlib.md5(message.encode()).hexdigest()[:8]}"

            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT INTO maintenance_logs
                (log_id, task_id, level, message, timestamp, context, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    log_id,
                    task_id,
                    level,
                    message,
                    datetime.now(),
                    json.dumps(context or {}),
                    user_id,
                ),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error logging task: {e}")

    def _send_notification(self, subject: str, message: str):
        """Send maintenance notification"""
        try:
            notification_email = self.config.get("maintenance", {}).get(
                "notification_email"
            )
            if not notification_email:
                logger.info(f"Notification: {subject} - {message}")
                return

            # This would implement email sending
            # For now, just log the notification
            logger.info(
                f"Would send notification to {notification_email}: {subject} - {message}"
            )

        except Exception as e:
            logger.error(f"Error sending notification: {e}")

    def get_maintenance_status(self) -> Dict[str, Any]:
        """Get overall maintenance status"""
        try:
            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            # Get task statistics
            cursor.execute(
                """
                SELECT task_type, status, COUNT(*)
                FROM maintenance_tasks
                GROUP BY task_type, status
            """
            )

            task_stats: Dict[str, Dict[str, int]] = {}
            for row in cursor.fetchall():
                task_type, status, count = row
                if task_type not in task_stats:
                    task_stats[task_type] = {}
                task_stats[task_type][status] = count

            # Get recent tasks
            cursor.execute(
                """
                SELECT task_id, task_type, status, priority, created_at, completed_at
                FROM maintenance_tasks
                ORDER BY created_at DESC
                LIMIT 10
            """
            )

            recent_tasks = []
            for row in cursor.fetchall():
                recent_tasks.append(
                    {
                        "task_id": row[0],
                        "task_type": row[1],
                        "status": row[2],
                        "priority": row[3],
                        "created_at": row[4],
                        "completed_at": row[5],
                    }
                )

            # Get link health
            link_report = self.link_checker.get_broken_links_report()

            # Get pending dependency updates
            cursor.execute(
                """
                SELECT COUNT(*) FROM dependency_updates
                WHERE status = 'pending'
            """
            )
            pending_updates = cursor.fetchone()[0]

            conn.close()

            return {
                "task_statistics": task_stats,
                "recent_tasks": recent_tasks,
                "link_health": link_report,
                "pending_dependency_updates": pending_updates,
                "last_updated": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error getting maintenance status: {e}")
            return {}

    def approve_task(self, task_id: str, user_id: str) -> bool:
        """Approve a maintenance task"""
        try:
            conn = sqlite3.connect(self.maintenance_db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                UPDATE maintenance_tasks
                SET approval_required = 0, approved_by = ?, approved_at = ?
                WHERE task_id = ?
            """,
                (user_id, datetime.now(), task_id),
            )

            conn.commit()
            conn.close()

            self._log_task(task_id, "info", f"Task approved by {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error approving task: {e}")
            return False


# Example usage
if __name__ == "__main__":
    engine = MaintenanceEngine()

    # Schedule maintenance tasks
    link_task_id = engine.schedule_link_check()
    dep_task_id = engine.schedule_dependency_check()

    print(f"Scheduled link check: {link_task_id}")
    print(f"Scheduled dependency check: {dep_task_id}")

    # Get status
    status = engine.get_maintenance_status()
    print(f"Maintenance status: {status}")
