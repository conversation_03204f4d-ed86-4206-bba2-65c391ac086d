"""
Core functionality for AI Coding Agent
Contains the main agent, maintenance engine, and core system components.
"""


# Lazy imports to avoid circular dependencies
def _import_cursor_rules_enforcer():
    """Import CursorRulesEnforcer lazily"""
    from core.cursor_rules_enforcer import CursorRulesEnforcer

    return CursorRulesEnforcer


def _import_agent():
    """Import AIAgent lazily"""
    from core.agent import AIAgent

    return AIAgent


def _import_maintenance_engine():
    """Import MaintenanceEngine lazily"""
    from core.maintenance_engine import MaintenanceEngine

    return MaintenanceEngine


def _import_home_server():
    """Import HomeServer lazily"""
    from core.home_server import HomeServer

    return HomeServer


def _import_website_generator():
    """Import WebsiteGenerator lazily"""
    from core.website_generator import WebsiteGenerator

    return WebsiteGenerator


def _import_site_upload_manager():
    """Import SiteUploadManager lazily"""
    from core.site_upload_manager import SiteUploadManager

    return SiteUploadManager


def _import_context_manager():
    """Import ContextManager lazily"""
    from core.context_manager import ContextManager

    return ContextManager


def _import_site_editor():
    """Import SiteEditor lazily"""
    from core.site_editor import SiteEditor

    return SiteEditor


def _import_site_container_manager():
    """Import SiteContainerManager lazily"""
    from core.site_container_manager import SiteContainerManager

    return SiteContainerManager


# Export CursorRulesEnforcer directly since it's needed for the monitor
from core.cursor_rules_enforcer import CursorRulesEnforcer

__all__ = [
    "CursorRulesEnforcer",  # Export this directly
]
