#!/usr/bin/env python3
"""
FastAPI Startup Script for AI Coding Agent
Runs the FastAPI server on port 8001 to avoid conflicts with Flask server on port 8000.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def main():
    parser = argparse.ArgumentParser(description="AI Coding Agent FastAPI Server")
    parser.add_argument(
        "--host", default="127.0.0.1", help="Host to bind to (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", type=int, default=8001, help="Port to bind to (default: 8001)"
    )
    parser.add_argument(
        "--reload", action="store_true", help="Enable auto-reload for development"
    )
    args = parser.parse_args()

    # Get project root
    project_root = Path(__file__).parent.absolute()

    # Check if virtual environment is activated
    if not hasattr(sys, "real_prefix") and not (
        hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
    ):
        print(
            "⚠️  Warning: Virtual environment not detected. Please activate your venv first."
        )
        print("   Run: .\\.venv\\Scripts\\Activate.ps1")
        return

    # Set environment variables
    env = os.environ.copy()
    env["PYTHONPATH"] = str(project_root)

    # Build uvicorn command
    uvicorn_cmd = [
        sys.executable,
        "-m",
        "uvicorn",
        "api.main:app",
        "--host",
        args.host,
        "--port",
        str(args.port),
        "--log-level",
        "info",
    ]

    if args.reload:
        uvicorn_cmd.append("--reload")

    print(f"🚀 Starting FastAPI server on {args.host}:{args.port}")
    print(f"📁 Project root: {project_root}")
    print(f"🔧 Command: {' '.join(uvicorn_cmd)}")
    print("=" * 60)

    try:
        # Run the FastAPI server
        subprocess.run(uvicorn_cmd, env=env, cwd=project_root)
    except KeyboardInterrupt:
        print("\n🛑 FastAPI server stopped by user")
    except Exception as e:
        print(f"❌ Error starting FastAPI server: {e}")
        print("\n💡 Make sure you have uvicorn installed:")
        print("   pip install uvicorn")


if __name__ == "__main__":
    main()
