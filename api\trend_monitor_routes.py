#!/usr/bin/env python3
"""
Trend Monitor API Routes

Provides REST API endpoints for managing the Trend Monitor container
and interacting with the Trend Monitor service.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from trend_monitoring.trend_monitor_container_manager import (
    TrendMonitorContainerManager,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/trend-monitor", tags=["trend-monitor"])


# Pydantic models
class TrendMonitorConfig(BaseModel):
    """Trend Monitor configuration"""

    base_dir: str = Field(default=".", description="Base directory for the project")
    containers_dir: str = Field(
        default="containers", description="Containers directory"
    )


class TrendMonitorResponse(BaseModel):
    """Standard response model"""

    success: bool
    data: Dict[str, Any]
    message: str
    timestamp: datetime


class TrendMonitorStatusResponse(BaseModel):
    """Status response model"""

    container_name: str
    status: str
    port: int
    health_status: str
    created_at: Optional[str]
    last_started: Optional[str]
    last_health_check: Optional[str]


# Dependency to get Trend Monitor Container Manager
def get_trend_monitor_manager(
    config: TrendMonitorConfig = Depends(),
) -> TrendMonitorContainerManager:
    """Get Trend Monitor Container Manager instance"""
    return TrendMonitorContainerManager(config.base_dir, config.containers_dir)


@router.get("/", response_model=Dict[str, Any])
async def root():
    """Root endpoint with service information"""
    return {
        "service": "Trend Monitor Container Management",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "create": "POST /api/v1/trend-monitor/create",
            "start": "POST /api/v1/trend-monitor/start",
            "stop": "POST /api/v1/trend-monitor/stop",
            "restart": "POST /api/v1/trend-monitor/restart",
            "status": "GET /api/v1/trend-monitor/status",
            "logs": "GET /api/v1/trend-monitor/logs",
            "health": "GET /api/v1/trend-monitor/health",
            "metrics": "GET /api/v1/trend-monitor/metrics",
            "delete": "DELETE /api/v1/trend-monitor/delete",
            "info": "GET /api/v1/trend-monitor/info",
        },
    }


@router.post("/create", response_model=TrendMonitorResponse)
async def create_trend_monitor_container(
    config: TrendMonitorConfig,
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Create the Trend Monitor container"""
    try:
        result = await manager.create_trend_monitor_container()

        if result["success"]:
            return TrendMonitorResponse(
                success=True,
                data=result["container"],
                message="Trend Monitor container created successfully",
                timestamp=datetime.now(),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error creating Trend Monitor container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start", response_model=TrendMonitorResponse)
async def start_trend_monitor_container(
    config: TrendMonitorConfig,
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Start the Trend Monitor container"""
    try:
        result = await manager.start_trend_monitor_container()

        if result["success"]:
            return TrendMonitorResponse(
                success=True,
                data=result["container"],
                message="Trend Monitor container started successfully",
                timestamp=datetime.now(),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error starting Trend Monitor container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop", response_model=TrendMonitorResponse)
async def stop_trend_monitor_container(
    config: TrendMonitorConfig,
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Stop the Trend Monitor container"""
    try:
        result = await manager.stop_trend_monitor_container()

        if result["success"]:
            return TrendMonitorResponse(
                success=True,
                data=result["container"],
                message="Trend Monitor container stopped successfully",
                timestamp=datetime.now(),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error stopping Trend Monitor container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/restart", response_model=TrendMonitorResponse)
async def restart_trend_monitor_container(
    config: TrendMonitorConfig,
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Restart the Trend Monitor container"""
    try:
        result = await manager.restart_trend_monitor_container()

        if result["success"]:
            return TrendMonitorResponse(
                success=True,
                data=result["container"],
                message="Trend Monitor container restarted successfully",
                timestamp=datetime.now(),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error restarting Trend Monitor container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=TrendMonitorStatusResponse)
async def get_trend_monitor_status(
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Get the status of the Trend Monitor container"""
    try:
        result = await manager.get_trend_monitor_status()

        if result["success"]:
            container = result["container"]
            return TrendMonitorStatusResponse(
                container_name=container["container_name"],
                status=container["status"],
                port=container["port"],
                health_status=container["health_status"],
                created_at=container.get("created_at"),
                last_started=container.get("last_started"),
                last_health_check=container.get("last_health_check"),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error getting Trend Monitor status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs")
async def get_trend_monitor_logs(
    lines: int = 100,
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Get Trend Monitor container logs"""
    try:
        result = await manager.get_trend_monitor_logs(lines)

        if result["success"]:
            return {
                "success": True,
                "logs": result["logs"],
                "total_lines": result["total_lines"],
                "timestamp": datetime.now().isoformat(),
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error getting Trend Monitor logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check(
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Perform a health check on the Trend Monitor container"""
    try:
        result = await manager.health_check()

        if result["success"]:
            return {
                "success": True,
                "health": result.get("health", {}),
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
            }
        else:
            return {
                "success": False,
                "error": result["error"],
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
            }

    except Exception as e:
        logger.error(f"Error performing health check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_trend_monitor_metrics(
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Get Trend Monitor metrics and statistics"""
    try:
        result = await manager.get_trend_monitor_metrics()

        if result["success"]:
            return {
                "success": True,
                "metrics": result["metrics"],
                "health_check": result.get("health_check", {}),
                "timestamp": datetime.now().isoformat(),
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error getting Trend Monitor metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete", response_model=TrendMonitorResponse)
async def delete_trend_monitor_container(
    config: TrendMonitorConfig,
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Delete the Trend Monitor container and its resources"""
    try:
        result = await manager.delete_trend_monitor_container()

        if result["success"]:
            return TrendMonitorResponse(
                success=True,
                data={},
                message="Trend Monitor container deleted successfully",
                timestamp=datetime.now(),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        logger.error(f"Error deleting Trend Monitor container: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/info")
async def get_trend_monitor_info(
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Get detailed information about the Trend Monitor container"""
    try:
        # Get status
        status_result = await manager.get_trend_monitor_status()
        if not status_result["success"]:
            raise HTTPException(status_code=400, detail=status_result["error"])

        # Get health
        health_result = await manager.health_check()

        # Get metrics
        metrics_result = await manager.get_trend_monitor_metrics()

        info = {
            "container": status_result["container"],
            "health": health_result,
            "metrics": (
                metrics_result.get("metrics", {}) if metrics_result["success"] else {}
            ),
            "api_endpoints": {
                "health_check": f"http://localhost:{status_result['container']['port']}/health",
                "api_docs": f"http://localhost:{status_result['container']['port']}/docs",
                "trends_api": f"http://localhost:{status_result['container']['port']}/api/v1/trends",
                "insights_api": f"http://localhost:{status_result['container']['port']}/api/v1/insights",
                "predictions_api": f"http://localhost:{status_result['container']['port']}/api/v1/predictions",
                "summary_api": f"http://localhost:{status_result['container']['port']}/api/v1/summary",
            },
            "timestamp": datetime.now().isoformat(),
        }

        return {"success": True, "info": info}

    except Exception as e:
        logger.error(f"Error getting Trend Monitor info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/start")
async def start_monitoring(
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Start trend monitoring via API"""
    try:
        # This would typically call the Trend Monitor service API
        # For now, we'll just return a success response
        return {
            "success": True,
            "message": "Trend monitoring started",
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error starting monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/monitoring/stop")
async def stop_monitoring(
    config: TrendMonitorConfig = Depends(),
    manager: TrendMonitorContainerManager = Depends(get_trend_monitor_manager),
):
    """Stop trend monitoring via API"""
    try:
        # This would typically call the Trend Monitor service API
        # For now, we'll just return a success response
        return {
            "success": True,
            "message": "Trend monitoring stopped",
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error stopping monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))
