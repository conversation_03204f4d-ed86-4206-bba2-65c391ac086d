"""
Simple Dashboard API using Flask
Alternative implementation using Flask instead of FastAPI.
This provides the same functionality but with Flask framework.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import jwt
from flask import Flask, jsonify, request, session
from flask_cors import CORS
from werkzeug.security import check_password_hash, generate_password_hash

from config.security import secure_config
from dashboard.database import create_tables, get_db_context, health_check, init_db
from db.database_manager import (
    APITokenManager,
    CodeFileManager,
    DeploymentManager,
    ModelRunManager,
    ProjectManager,
    SettingsManager,
    UserManager,
)
from db.models import User

# Configure logging
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
app.secret_key = secure_config.secret_key or "dev-secret-key-change-in-production"
CORS(app, supports_credentials=True)


# Initialize database
def init_database():
    """Initialize database tables and default data"""
    try:
        create_tables()
        init_db()
        logger.info("Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False


# Authentication helpers
def get_current_user():
    """Get current user from session"""
    if "user_id" not in session:
        return None

    with get_db_context() as db:
        user_manager = UserManager()
        return user_manager.get(db, session["user_id"])


def require_auth(f):
    """Decorator to require authentication"""

    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            return jsonify({"error": "Authentication required"}), 401
        return f(*args, **kwargs)

    decorated_function.__name__ = f.__name__
    return decorated_function


# Health check endpoint
@app.route("/health", methods=["GET"])
def health():
    """Health check endpoint"""
    db_healthy = health_check()
    return jsonify(
        {
            "status": "healthy" if db_healthy else "unhealthy",
            "database": "connected" if db_healthy else "disconnected",
            "timestamp": datetime.now().isoformat(),
        }
    )


# Root endpoint
@app.route("/", methods=["GET"])
def root():
    """Root endpoint with API information"""
    return jsonify(
        {
            "message": "AI Coding Agent Dashboard API (Flask)",
            "version": "1.0.0",
            "framework": "Flask",
            "endpoints": {
                "health": "/health",
                "auth": "/api/v1/auth",
                "projects": "/api/v1/projects",
                "files": "/api/v1/files",
                "deployments": "/api/v1/deployments",
            },
        }
    )


# Authentication endpoints
@app.route("/api/v1/auth/login", methods=["POST"])
def login():
    """Login user with HttpOnly cookie"""
    try:
        data = request.get_json()
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return jsonify({"error": "Username and password required"}), 400

        with get_db_context() as db:
            user_manager = UserManager()
            user = user_manager.get_by_username(db, username)

            if not user or not check_password_hash(user.hashed_password, password):
                return jsonify({"error": "Invalid credentials"}), 401

            # Create JWT token
            token_payload = {
                "user_id": user.id,
                "username": user.username,
                "exp": datetime.utcnow() + timedelta(days=1),
            }
            token = jwt.encode(token_payload, str(app.secret_key), algorithm="HS256")

            # Set HttpOnly cookie
            response = jsonify(
                {
                    "message": "Login successful",
                    "user": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "is_active": user.is_active,
                        "is_superuser": user.is_superuser,
                    },
                }
            )

            response.set_cookie(
                "token",
                token,
                httponly=True,
                secure=True,  # Enable in production with HTTPS
                samesite="Strict",
                max_age=86400,  # 24 hours
            )

            return response
    except Exception as e:
        logger.error(f"Login error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/auth/register", methods=["POST"])
def register():
    """Register new user"""
    try:
        data = request.get_json()
        username = data.get("username")
        email = data.get("email")
        password = data.get("password")

        if not username or not email or not password:
            return jsonify({"error": "Username, email, and password required"}), 400

        with get_db_context() as db:
            user_manager = UserManager()

            # Check if user already exists
            if user_manager.get_by_username(db, username):
                return jsonify({"error": "Username already exists"}), 400

            if user_manager.get_by_email(db, email):
                return jsonify({"error": "Email already exists"}), 400

            # Create new user
            hashed_password = generate_password_hash(password)
            user_data = {
                "username": username,
                "email": email,
                "hashed_password": hashed_password,
                "is_active": True,
                "is_superuser": False,
            }

            user = user_manager.create(db, obj_in=user_data)

            return jsonify(
                {
                    "message": "User registered successfully",
                    "user": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                    },
                }
            )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/auth/logout", methods=["POST"])
def logout():
    """Logout user"""
    session.clear()
    return jsonify({"message": "Logout successful"})


# User endpoints
@app.route("/api/v1/users/me", methods=["GET"])
@require_auth
def get_current_user_info():
    """Get current user information"""
    user = get_current_user()
    if user is None:  # This check is for type checker, decorator should prevent None
        return jsonify({"error": "Authentication required"}), 401
    return jsonify(
        {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "created_at": user.created_at.isoformat(),
            "updated_at": user.updated_at.isoformat(),
        }
    )


# Project endpoints
@app.route("/api/v1/projects", methods=["GET"])
@require_auth
def get_projects():
    """Get user's projects"""
    try:
        user = get_current_user()
        if (
            user is None
        ):  # This check is for type checker, decorator should prevent None
            return jsonify({"error": "Authentication required"}), 401
        skip = request.args.get("skip", 0, type=int)
        limit = request.args.get("limit", 100, type=int)

        with get_db_context() as db:
            project_manager = ProjectManager()
            projects = project_manager.get_multi_by_owner(
                db, user.id, skip=skip, limit=limit
            )

            return jsonify(
                [
                    {
                        "id": p.id,
                        "name": p.name,
                        "description": p.description,
                        "owner_id": p.owner_id,
                        "created_at": p.created_at.isoformat(),
                        "updated_at": p.updated_at.isoformat(),
                    }
                    for p in projects
                ]
            )
    except Exception as e:
        logger.error(f"Get projects error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/projects", methods=["POST"])
@require_auth
def create_project():
    """Create new project"""
    try:
        user = get_current_user()
        if (
            user is None
        ):  # This check is for type checker, decorator should prevent None
            return jsonify({"error": "Authentication required"}), 401
        data = request.get_json()
        name = data.get("name")
        description = data.get("description", "")

        if not name:
            return jsonify({"error": "Project name required"}), 400

        with get_db_context() as db:
            project_manager = ProjectManager()
            project_data = {
                "name": name,
                "description": description,
                "owner_id": user.id,
            }

            project = project_manager.create(db, obj_in=project_data)

            return jsonify(
                {
                    "id": project.id,
                    "name": project.name,
                    "description": project.description,
                    "owner_id": project.owner_id,
                    "created_at": project.created_at.isoformat(),
                    "updated_at": project.updated_at.isoformat(),
                }
            )
    except Exception as e:
        logger.error(f"Create project error: {e}")
        return jsonify({"error": "Internal server error"}), 500


# File endpoints
@app.route("/api/v1/files", methods=["GET"])
@require_auth
def get_files():
    """Get code files"""
    try:
        user = get_current_user()
        if (
            user is None
        ):  # This check is for type checker, decorator should prevent None
            return jsonify({"error": "Authentication required"}), 401
        project_id = request.args.get("project_id", type=int)
        skip = request.args.get("skip", 0, type=int)
        limit = request.args.get("limit", 100, type=int)

        with get_db_context() as db:
            file_manager = CodeFileManager()

            if project_id:
                # Verify project ownership
                project_manager = ProjectManager()
                project = project_manager.get(db, project_id)

                if not project:
                    return jsonify({"error": "Project not found"}), 404

                if project.owner_id != user.id and not user.is_superuser:
                    return jsonify({"error": "Not enough permissions"}), 403

                files = file_manager.get_by_project(
                    db, project_id, skip=skip, limit=limit
                )
            else:
                files = file_manager.get_multi(db, skip=skip, limit=limit)

            return jsonify(
                [
                    {
                        "id": f.id,
                        "filename": f.filename,
                        "content": f.content,
                        "path": f.path,
                        "language": f.language,
                        "is_directory": f.is_directory,
                        "project_id": f.project_id,
                        "parent_id": f.parent_id,
                        "created_at": f.created_at.isoformat(),
                        "updated_at": f.updated_at.isoformat(),
                    }
                    for f in files
                ]
            )
    except Exception as e:
        logger.error(f"Get files error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/files", methods=["POST"])
@require_auth
def create_file():
    """Create new code file"""
    try:
        user = get_current_user()
        if (
            user is None
        ):  # This check is for type checker, decorator should prevent None
            return jsonify({"error": "Authentication required"}), 401
        data = request.get_json()
        filename = data.get("filename")
        content = data.get("content", "")
        language = data.get("language")
        is_directory = data.get("is_directory", False)
        project_id = data.get("project_id")

        if not filename or not project_id:
            return jsonify({"error": "Filename and project_id required"}), 400

        with get_db_context() as db:
            # Verify project ownership
            project_manager = ProjectManager()
            project = project_manager.get(db, project_id)

            if not project:
                return jsonify({"error": "Project not found"}), 404

            if project.owner_id != user.id and not user.is_superuser:
                return jsonify({"error": "Not enough permissions"}), 403

            file_manager = CodeFileManager()
            file_data = {
                "filename": filename,
                "content": content,
                "language": language,
                "is_directory": is_directory,
                "project_id": project_id,
            }

            file_obj = file_manager.create(db, obj_in=file_data)

            return jsonify(
                {
                    "id": file_obj.id,
                    "filename": file_obj.filename,
                    "content": file_obj.content,
                    "path": file_obj.path,
                    "language": file_obj.language,
                    "is_directory": file_obj.is_directory,
                    "project_id": file_obj.project_id,
                    "parent_id": file_obj.parent_id,
                    "created_at": file_obj.created_at.isoformat(),
                    "updated_at": file_obj.updated_at.isoformat(),
                }
            )
    except Exception as e:
        logger.error(f"Create file error: {e}")
        return jsonify({"error": "Internal server error"}), 500


# Deployment endpoints
@app.route("/api/v1/deployments", methods=["GET"])
@require_auth
def get_deployments():
    """Get deployments"""
    try:
        user = get_current_user()
        if (
            user is None
        ):  # This check is for type checker, decorator should prevent None
            return jsonify({"error": "Authentication required"}), 401
        project_id = request.args.get("project_id", type=int)
        skip = request.args.get("skip", 0, type=int)
        limit = request.args.get("limit", 100, type=int)

        with get_db_context() as db:
            deployment_manager = DeploymentManager()

            if project_id:
                # Verify project ownership
                project_manager = ProjectManager()
                project = project_manager.get(db, project_id)

                if not project:
                    return jsonify({"error": "Project not found"}), 404

                if project.owner_id != user.id and not user.is_superuser:
                    return jsonify({"error": "Not enough permissions"}), 403

                deployments = deployment_manager.get_multi_by_project(
                    db, project_id, skip=skip, limit=limit
                )
            else:
                deployments = deployment_manager.get_multi(db, skip=skip, limit=limit)

            return jsonify(
                [
                    {
                        "id": d.id,
                        "project_id": d.project_id,
                        "status": d.status,
                        "environment": d.environment,
                        "url": d.url,
                        "commit_hash": d.commit_hash,
                        "logs": d.logs,
                        "metadata": d.metadata_,
                        "created_at": d.created_at.isoformat(),
                        "completed_at": (
                            d.completed_at.isoformat() if d.completed_at else None
                        ),
                    }
                    for d in deployments
                ]
            )
    except Exception as e:
        logger.error(f"Get deployments error: {e}")
        return jsonify({"error": "Internal server error"}), 500


@app.route("/api/v1/deployments", methods=["POST"])
@require_auth
def create_deployment():
    """Create new deployment"""
    try:
        user = get_current_user()
        if (
            user is None
        ):  # This check is for type checker, decorator should prevent None
            return jsonify({"error": "Authentication required"}), 401
        data = request.get_json()
        project_id = data.get("project_id")
        environment = data.get("environment", "development")
        url = data.get("url")
        metadata = data.get("metadata", {})

        if not project_id:
            return jsonify({"error": "Project ID required"}), 400

        with get_db_context() as db:
            # Verify project ownership
            project_manager = ProjectManager()
            project = project_manager.get(db, project_id)

            if not project:
                return jsonify({"error": "Project not found"}), 404

            if project.owner_id != user.id and not user.is_superuser:
                return jsonify({"error": "Not enough permissions"}), 403

            deployment_manager = DeploymentManager()
            deployment_data = {
                "project_id": project_id,
                "environment": environment,
                "url": url,
                "metadata": metadata,
            }

            deployment = deployment_manager.create(db, obj_in=deployment_data)

            return jsonify(
                {
                    "id": deployment.id,
                    "project_id": deployment.project_id,
                    "status": deployment.status,
                    "environment": deployment.environment,
                    "url": deployment.url,
                    "commit_hash": deployment.commit_hash,
                    "logs": deployment.logs,
                    "metadata": deployment.metadata_,
                    "created_at": deployment.created_at.isoformat(),
                    "completed_at": (
                        deployment.completed_at.isoformat()
                        if deployment.completed_at
                        else None
                    ),
                }
            )
    except Exception as e:
        logger.error(f"Create deployment error: {e}")
        return jsonify({"error": "Internal server error"}), 500


# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Not found"}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500


if __name__ == "__main__":
    # Initialize database
    if init_database():
        logger.info("Starting Flask dashboard server...")
        app.run(host="127.0.0.1", port=8000, debug=False)
    else:
        logger.error("Failed to initialize database. Exiting.")
