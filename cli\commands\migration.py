"""
Database migration commands.

This module provides CLI commands for creating, managing, and deploying
database migrations for Supabase projects.
"""

import logging
import os
from typing import Optional

import click

from cli.error_handler import error_handler
from cli.state_tracker import track_event
from database.migration_manager import MigrationDeployment<PERSON>anager, MigrationFileManager
from database.supabase_cli import Supabase<PERSON><PERSON>
from db import database_migration_manager, supabase_config_manager
from db.models import Project
from utils.decorators import (
    CliError,
    require_project_access,
    validate_migration_sql,
    with_db,
)
from utils.tasks import add_db_task

logger = logging.getLogger(__name__)


@click.group()
def migration() -> None:
    """Database migration commands"""
    pass


@migration.command()
@click.argument("project_id", type=int)
@click.argument("name")
@click.option("--description", help="Migration description")
@click.option("--template", help="Template to use for migration")
@click.option("--sql", help="Custom SQL content", callback=validate_migration_sql)
@click.pass_context
@error_handler
@with_db
@require_project_access
def create(
    ctx,
    project_id: int,
    name: str,
    description: Optional[str],
    template: Optional[str],
    sql: Optional[str],
) -> None:
    """Create a new migration for a project"""
    track_event(
        "migration", "Creating migration", {"project_id": project_id, "name": name}
    )

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Creating migration '{name}' for project {project_id}")

    # Get project path
    project_path = project.path or os.getcwd()

    # Initialize migration manager
    migration_manager = MigrationFileManager(project_path)

    if template:
        # Create migration from template
        if not sql:
            raise CliError("SQL content is required when using a template")

        # Parse template variables from SQL (simple approach)
        variables = {"sql_content": sql}
        success, result = migration_manager.create_migration_from_template(
            template, variables, name
        )

        if not success:
            raise CliError(f"Failed to create migration from template: {result}")
    else:
        # Create custom migration
        if not sql:
            raise CliError("SQL content is required")

        success, result = migration_manager.create_custom_migration(name, sql)

        if not success:
            raise CliError(f"Failed to create migration: {result}")

    # Create migration record in database
    migration_data = {
        "project_id": project_id,
        "name": name,
        "description": description,
        "migration_type": "both",
        "status": "pending",
        "file_path": result,
        "sql_content": sql,
    }

    database_migration_manager.create(db, obj_in=migration_data)
    logger.info(f"Created migration record in database: {name}")

    # Add task for review
    add_db_task(f"Review migration '{name}' for project {project_id}")

    click.echo(f"✅ Migration '{name}' created successfully!")


@migration.command()
@click.argument("project_id", type=int)
@click.option("--skip", type=int, default=0, help="Number of migrations to skip")
@click.option(
    "--limit", type=int, default=100, help="Maximum number of migrations to show"
)
@click.pass_context
@error_handler
@with_db
@require_project_access
def list(ctx, project_id: int, skip: int, limit: int) -> None:
    """List migrations for a project"""
    track_event("migration", "Listing migrations", {"project_id": project_id})

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Listing migrations for project {project_id}")

    # Get migrations from database
    migrations = database_migration_manager.get_by_project(
        db, project_id, skip=skip, limit=limit
    )

    if not migrations:
        click.echo("No migrations found for this project.")
        return

    click.echo(f"Migrations for Project {project_id}:")
    click.echo("-" * 80)

    for migration in migrations:
        status_icon = {
            "pending": "⏳",
            "applied": "✅",
            "failed": "❌",
            "rolled_back": "↩️",
        }.get(migration.status, "❓")

        click.echo(f"{status_icon} {migration.name}")
        if migration.description:
            click.echo(f"    Description: {migration.description}")
        click.echo(f"    Status: {migration.status}")
        click.echo(f"    Type: {migration.migration_type}")
        click.echo(f"    Created: {migration.created_at}")
        if migration.applied_at:
            click.echo(f"    Applied: {migration.applied_at}")
        click.echo()


@migration.command()
@click.argument("project_id", type=int)
@click.argument("migration_id", type=int)
@click.option("--sql", help="New SQL content")
@click.option("--description", help="New description")
@click.pass_context
@error_handler
@with_db
@require_project_access
def edit(
    ctx,
    project_id: int,
    migration_id: int,
    sql: Optional[str],
    description: Optional[str],
) -> None:
    """Edit an existing migration"""
    track_event(
        "migration",
        "Editing migration",
        {"project_id": project_id, "migration_id": migration_id},
    )

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Editing migration {migration_id} for project {project_id}")

    # Get migration
    migration = database_migration_manager.get(db, migration_id)
    if not migration:
        raise CliError(f"Migration {migration_id} not found")

    if migration.project_id != project_id:
        raise CliError("Migration does not belong to this project")

    # Update migration
    update_data = {}
    if sql:
        update_data["sql_content"] = sql
    if description:
        update_data["description"] = description

    if update_data:
        database_migration_manager.update(db, db_obj=migration, obj_in=update_data)
        logger.info(f"Updated migration {migration_id}")
        click.echo(f"✅ Migration '{migration.name}' updated successfully!")
    else:
        click.echo("No changes specified.")


@migration.command()
@click.argument("project_id", type=int)
@click.argument("migration_id", type=int)
@click.option(
    "--force", is_flag=True, help="Force deployment even if there are conflicts"
)
@click.option(
    "--dry-run", is_flag=True, help="Perform a dry run without applying changes"
)
@click.pass_context
@error_handler
@with_db
@require_project_access
def deploy(ctx, project_id: int, migration_id: int, force: bool, dry_run: bool) -> None:
    """Deploy a migration to Supabase"""
    track_event(
        "migration",
        "Deploying migration",
        {
            "project_id": project_id,
            "migration_id": migration_id,
            "force": force,
            "dry_run": dry_run,
        },
    )

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Deploying migration {migration_id} for project {project_id}")

    # Get migration
    migration = database_migration_manager.get(db, migration_id)
    if not migration:
        raise CliError(f"Migration {migration_id} not found")

    if migration.project_id != project_id:
        raise CliError("Migration does not belong to this project")

    # Get Supabase config
    config = supabase_config_manager.get_by_project(db, project_id)
    if not config:
        raise CliError("Project is not linked to Supabase")

    # Get project path
    project_path = project.path or os.getcwd()

    # Initialize Supabase CLI and deployment manager
    supabase_cli = SupabaseCLI(project_path)
    deployment_manager = MigrationDeploymentManager(project_path, supabase_cli)

    # Deploy migration
    if dry_run:
        logger.info("Performing dry run deployment")
        click.echo("🔍 Performing dry run...")
    else:
        logger.info(f"Deploying migration '{migration.name}'")
        click.echo(f"🚀 Deploying migration '{migration.name}'...")

    result = deployment_manager.deploy_migration(
        migration.file_path or f"{migration_id}_{migration.name}.sql",
        force=force,
        dry_run=dry_run,
    )

    if result.success:
        if dry_run:
            click.echo("✅ Dry run completed successfully!")
        else:
            click.echo("✅ Migration deployed successfully!")
            # Update migration status
            database_migration_manager.update(
                db,
                db_obj=migration,
                obj_in={"status": "applied", "applied_at": "now()"},
            )
            logger.info(f"Migration {migration_id} deployed successfully")
    else:
        error_msg = f"Migration deployment failed: {result.error}"
        logger.error(error_msg)
        click.echo(f"❌ {error_msg}")


@migration.command()
@click.argument("project_id", type=int)
@click.argument("migration_id", type=int)
@click.option(
    "--force", is_flag=True, help="Force rollback even if there are conflicts"
)
@click.option(
    "--dry-run", is_flag=True, help="Perform a dry run without applying changes"
)
@click.pass_context
@error_handler
@with_db
@require_project_access
def rollback(
    ctx, project_id: int, migration_id: int, force: bool, dry_run: bool
) -> None:
    """Rollback a migration from Supabase"""
    track_event(
        "migration",
        "Rolling back migration",
        {
            "project_id": project_id,
            "migration_id": migration_id,
            "force": force,
            "dry_run": dry_run,
        },
    )

    # Get db and project from context
    db = ctx.obj["db"]
    project = ctx.obj["project"]

    logger.info(f"Rolling back migration {migration_id} for project {project_id}")

    # Get migration
    migration = database_migration_manager.get(db, migration_id)
    if not migration:
        raise CliError(f"Migration {migration_id} not found")

    if migration.project_id != project_id:
        raise CliError("Migration does not belong to this project")

    # Get Supabase config
    config = supabase_config_manager.get_by_project(db, project_id)
    if not config:
        raise CliError("Project is not linked to Supabase")

    # Get project path
    project_path = project.path or os.getcwd()

    # Initialize Supabase CLI and deployment manager
    supabase_cli = SupabaseCLI(project_path)
    deployment_manager = MigrationDeploymentManager(project_path, supabase_cli)

    # Rollback migration
    if dry_run:
        logger.info("Performing dry run rollback")
        click.echo("🔍 Performing dry run rollback...")
    else:
        logger.info(f"Rolling back migration '{migration.name}'")
        click.echo(f"↩️ Rolling back migration '{migration.name}'...")

    result = deployment_manager.rollback_migration(
        migration.name, force=force, dry_run=dry_run
    )

    if result.success:
        if dry_run:
            click.echo("✅ Dry run rollback completed successfully!")
        else:
            click.echo("✅ Migration rolled back successfully!")
            # Update migration status
            database_migration_manager.update(
                db,
                db_obj=migration,
                obj_in={"status": "rolled_back", "rolled_back_at": "now()"},
            )
            logger.info(f"Migration {migration_id} rolled back successfully")
    else:
        error_msg = f"Migration rollback failed: {result.error}"
        logger.error(error_msg)
        click.echo(f"❌ {error_msg}")
