{"detection_threshold": 0.7, "pattern_window": 100, "feedback_anomaly_threshold": 0.8, "request_anomaly_threshold": 0.8, "mitigation_enabled": true, "quarantine_duration": 3600, "anomaly_detection": {"feedback_patterns": {"min_data_points": 10, "variance_threshold": 0.01, "consistency_threshold": 0.95, "change_threshold": 0.5}, "request_patterns": {"min_data_points": 10, "size_thresholds": {"min_size": 10, "max_size": 10000}, "repetition_threshold": 0.3, "timing_thresholds": {"min_timing": 0.1, "max_timing": 10.0}}}, "attack_classification": {"coordinated_attack": {"feedback_score_threshold": 0.7, "request_score_threshold": 0.7}, "feedback_manipulation": {"feedback_score_threshold": 0.7, "request_score_threshold": 0.0}, "request_flooding": {"feedback_score_threshold": 0.0, "request_score_threshold": 0.7}, "suspicious_activity": {"feedback_score_threshold": 0.5, "request_score_threshold": 0.5}}, "threat_levels": {"critical": {"confidence_threshold": 0.9, "attack_types": ["coordinated_attack"]}, "high": {"confidence_threshold": 0.8, "attack_types": ["coordinated_attack", "feedback_manipulation"]}, "medium": {"confidence_threshold": 0.6, "attack_types": ["request_flooding"]}, "low": {"confidence_threshold": 0.0, "attack_types": ["suspicious_activity"]}}, "mitigation_strategies": {"coordinated_attack": {"quarantine_duration": 7200, "monitoring_interval": 10, "aggressive_mitigation": true}, "feedback_manipulation": {"quarantine_duration": 3600, "enable_filtering": true, "monitoring_interval": 30}, "request_flooding": {"quarantine_duration": 1800, "enable_rate_limiting": true, "monitoring_interval": 30}, "suspicious_activity": {"quarantine_duration": 900, "monitoring_interval": 60}}}