{"llm_security": {"enabled": true, "models": {"threat_analysis": "deepseek-coder:1.3b", "code_review": "deepseek-coder:1.3b", "compliance": "mistral:7b-instruct-q4_0", "general": "qwen2.5-coder:3b", "fallback": "yi-coder:1.5b"}, "settings": {"max_tokens": 2048, "temperature": 0.3, "confidence_threshold": 0.7, "max_processing_time": 30.0}, "tasks": {"threat_analysis": {"enabled": true, "auto_trigger": true, "priority": "high"}, "anomaly_detection": {"enabled": true, "auto_trigger": true, "priority": "medium"}, "code_security_review": {"enabled": true, "auto_trigger": false, "priority": "high"}, "compliance_audit": {"enabled": true, "auto_trigger": false, "priority": "medium"}, "security_recommendations": {"enabled": true, "auto_trigger": false, "priority": "low"}}, "integrations": {"security_manager": true, "threat_detector": true, "audit_logger": true, "compliance_checker": true}, "notifications": {"high_severity_incidents": true, "critical_threats": true, "compliance_violations": true, "code_vulnerabilities": true}}}