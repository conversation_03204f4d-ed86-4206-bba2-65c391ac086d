"""
Bug Detector Module

Identifies common programming errors, security vulnerabilities, and provides
automated fix suggestions for detected issues.

Phase 19 Implementation - Enhanced Code Generation
"""

import ast
import logging
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class Bug:
    """Represents a detected bug or issue."""

    id: str
    type: str  # "error", "warning", "info", "security"
    severity: str  # "critical", "high", "medium", "low"
    message: str
    description: str
    line_number: int
    column: int
    code_snippet: str
    language: str
    category: str
    fix_suggestion: Optional[str] = None
    confidence: float = 0.8


@dataclass
class BugDetectionResult:
    """Result of bug detection analysis."""

    bugs: List[Bug]
    total_bugs: int
    critical_count: int
    high_count: int
    medium_count: int
    low_count: int
    security_issues: List[Bug]
    performance_issues: List[Bug]
    code_quality_issues: List[Bug]


class BugDetector:
    """
    Advanced bug detection system for multiple programming languages.

    Provides:
    - Static analysis for common errors
    - Security vulnerability detection
    - Performance issue identification
    - Code quality problem detection
    - Automated fix suggestions
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the bug detector.

        Args:
            config: Configuration dictionary for bug detection
        """
        self.config = config or {}
        self.supported_languages = ["python", "javascript", "typescript", "java", "cpp"]
        self.health_status = "healthy"

        # Load bug patterns
        self.bug_patterns = self._load_bug_patterns()
        self.security_patterns = self._load_security_patterns()
        self.performance_patterns = self._load_performance_patterns()

        logger.info("Bug Detector initialized successfully")

    async def detect_bugs(
        self, code: str, language: str, file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Detect bugs and issues in the given code.

        Args:
            code: Source code to analyze
            language: Programming language
            file_path: Optional file path for context

        Returns:
            Dictionary with bug detection results
        """
        try:
            if language not in self.supported_languages:
                raise ValueError(f"Unsupported language: {language}")

            # Perform language-specific bug detection
            if language == "python":
                bugs = await self._detect_python_bugs(code, file_path)
            elif language in ["javascript", "typescript"]:
                bugs = await self._detect_javascript_bugs(code, language, file_path)
            elif language == "java":
                bugs = await self._detect_java_bugs(code, file_path)
            elif language == "cpp":
                bugs = await self._detect_cpp_bugs(code, file_path)
            else:
                bugs = await self._detect_generic_bugs(code, language, file_path)

            # Categorize bugs
            security_issues = [bug for bug in bugs if bug.category == "security"]
            performance_issues = [bug for bug in bugs if bug.category == "performance"]
            code_quality_issues = [bug for bug in bugs if bug.category == "quality"]

            # Count bugs by severity
            critical_count = len([bug for bug in bugs if bug.severity == "critical"])
            high_count = len([bug for bug in bugs if bug.severity == "high"])
            medium_count = len([bug for bug in bugs if bug.severity == "medium"])
            low_count = len([bug for bug in bugs if bug.severity == "low"])

            result = BugDetectionResult(
                bugs=bugs,
                total_bugs=len(bugs),
                critical_count=critical_count,
                high_count=high_count,
                medium_count=medium_count,
                low_count=low_count,
                security_issues=security_issues,
                performance_issues=performance_issues,
                code_quality_issues=code_quality_issues,
            )

            return {
                "bugs": [self._bug_to_dict(bug) for bug in bugs],
                "total_bugs": result.total_bugs,
                "critical_count": result.critical_count,
                "high_count": result.high_count,
                "medium_count": result.medium_count,
                "low_count": result.low_count,
                "security_issues": len(security_issues),
                "performance_issues": len(performance_issues),
                "code_quality_issues": len(code_quality_issues),
                "language": language,
                "file_path": file_path,
            }

        except Exception as e:
            logger.error(f"Error detecting bugs: {e}")
            raise

    async def _detect_python_bugs(
        self, code: str, file_path: Optional[str]
    ) -> List[Bug]:
        """Detect bugs in Python code using AST analysis."""
        bugs = []
        lines = code.split("\n")

        try:
            # Parse the code
            tree = ast.parse(code)

            # Check for syntax errors and other issues
            for node in ast.walk(tree):
                bugs.extend(self._check_python_node(node, lines))

            # Check for common Python bugs
            bugs.extend(self._check_python_common_bugs(code, lines))

            # Check for security vulnerabilities
            bugs.extend(self._check_python_security_bugs(code, lines))

            # Check for performance issues
            bugs.extend(self._check_python_performance_bugs(code, lines))

        except SyntaxError as e:
            # Handle syntax errors
            bug = Bug(
                id="syntax_error",
                type="error",
                severity="critical",
                message=f"Syntax error: {e.msg}",
                description="The code contains a syntax error that prevents execution",
                line_number=e.lineno or 1,
                column=e.offset or 1,
                code_snippet=(
                    lines[e.lineno - 1] if e.lineno and e.lineno <= len(lines) else ""
                ),
                language="python",
                category="syntax",
                fix_suggestion="Fix the syntax error to make the code valid Python",
            )
            bugs.append(bug)

        return bugs

    async def _detect_javascript_bugs(
        self, code: str, language: str, file_path: Optional[str]
    ) -> List[Bug]:
        """Detect bugs in JavaScript/TypeScript code."""
        bugs = []
        lines = code.split("\n")

        # Check for common JavaScript bugs
        bugs.extend(self._check_javascript_common_bugs(code, lines, language))

        # Check for security vulnerabilities
        bugs.extend(self._check_javascript_security_bugs(code, lines))

        # Check for performance issues
        bugs.extend(self._check_javascript_performance_bugs(code, lines))

        return bugs

    async def _detect_java_bugs(self, code: str, file_path: Optional[str]) -> List[Bug]:
        """Detect bugs in Java code."""
        bugs = []
        lines = code.split("\n")

        # Check for common Java bugs
        bugs.extend(self._check_java_common_bugs(code, lines))

        # Check for security vulnerabilities
        bugs.extend(self._check_java_security_bugs(code, lines))

        # Check for performance issues
        bugs.extend(self._check_java_performance_bugs(code, lines))

        return bugs

    async def _detect_cpp_bugs(self, code: str, file_path: Optional[str]) -> List[Bug]:
        """Detect bugs in C++ code."""
        bugs = []
        lines = code.split("\n")

        # Check for common C++ bugs
        bugs.extend(self._check_cpp_common_bugs(code, lines))

        # Check for security vulnerabilities
        bugs.extend(self._check_cpp_security_bugs(code, lines))

        # Check for performance issues
        bugs.extend(self._check_cpp_performance_bugs(code, lines))

        return bugs

    async def _detect_generic_bugs(
        self, code: str, language: str, file_path: Optional[str]
    ) -> List[Bug]:
        """Detect generic bugs for any language."""
        bugs = []
        lines = code.split("\n")

        # Check for generic issues
        bugs.extend(self._check_generic_bugs(code, lines, language))

        return bugs

    def _check_python_node(self, node: ast.AST, lines: List[str]) -> List[Bug]:
        """Check individual Python AST nodes for bugs."""
        bugs = []

        # Check for bare except clauses
        if isinstance(node, ast.ExceptHandler) and node.type is None:
            bug = Bug(
                id="bare_except",
                type="warning",
                severity="medium",
                message="Bare except clause catches all exceptions",
                description="Using bare except clauses can mask important errors",
                line_number=getattr(node, "lineno", 1),
                column=getattr(node, "col_offset", 1),
                code_snippet=(
                    lines[getattr(node, "lineno", 1) - 1]
                    if getattr(node, "lineno", 1) <= len(lines)
                    else ""
                ),
                language="python",
                category="quality",
                fix_suggestion="Specify the exception type(s) to catch",
            )
            bugs.append(bug)

        # Check for unused variables
        if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
            # This is a simplified check - in real implementation would track variable usage
            pass

        return bugs

    def _check_python_common_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for common Python bugs."""
        bugs = []

        # Check for magic numbers
        magic_number_matches = re.finditer(r"\b\d{3,}\b", code)
        for match in magic_number_matches:
            line_number = code[: match.start()].count("\n") + 1
            bug = Bug(
                id="magic_number",
                type="warning",
                severity="low",
                message="Magic number detected",
                description="Magic numbers make code harder to understand and maintain",
                line_number=line_number,
                column=(
                    match.start() - code.rfind("\n", 0, match.start())
                    if "\n" in code[: match.start()]
                    else match.start()
                ),
                code_snippet=match.group(),
                language="python",
                category="quality",
                fix_suggestion="Define a named constant for this value",
            )
            bugs.append(bug)

        # Check for unused imports (simplified)
        if "import " in code and "unused_import" in self.config.get("checks", []):
            bug = Bug(
                id="unused_import",
                type="info",
                severity="low",
                message="Potential unused import",
                description="This import may not be used in the code",
                line_number=1,
                column=1,
                code_snippet="import statement",
                language="python",
                category="quality",
                fix_suggestion="Remove unused imports to clean up the code",
            )
            bugs.append(bug)

        return bugs

    def _check_python_security_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for Python security vulnerabilities."""
        bugs = []

        # Check for eval usage
        if "eval(" in code:
            line_number = code.find("eval(") + 1
            bug = Bug(
                id="eval_usage",
                type="error",
                severity="critical",
                message="Use of eval() function",
                description="eval() can execute arbitrary code and is a security risk",
                line_number=line_number,
                column=1,
                code_snippet="eval()",
                language="python",
                category="security",
                fix_suggestion="Use safer alternatives like ast.literal_eval() or json.loads()",
            )
            bugs.append(bug)

        # Check for exec usage
        if "exec(" in code:
            line_number = code.find("exec(") + 1
            bug = Bug(
                id="exec_usage",
                type="error",
                severity="critical",
                message="Use of exec() function",
                description="exec() can execute arbitrary code and is a security risk",
                line_number=line_number,
                column=1,
                code_snippet="exec()",
                language="python",
                category="security",
                fix_suggestion="Avoid using exec() for security reasons",
            )
            bugs.append(bug)

        return bugs

    def _check_python_performance_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for Python performance issues."""
        bugs = []

        # Check for inefficient string concatenation
        if code.count("+") > 10 and "str" in code:
            bug = Bug(
                id="inefficient_string_concat",
                type="warning",
                severity="medium",
                message="Inefficient string concatenation",
                description="Using + for string concatenation in loops is inefficient",
                line_number=1,
                column=1,
                code_snippet="string concatenation",
                language="python",
                category="performance",
                fix_suggestion="Use str.join() or f-strings for better performance",
            )
            bugs.append(bug)

        return bugs

    def _check_javascript_common_bugs(
        self, code: str, lines: List[str], language: str
    ) -> List[Bug]:
        """Check for common JavaScript bugs."""
        bugs = []

        # Check for var usage
        var_matches = re.finditer(r"\bvar\s+", code)
        for match in var_matches:
            line_number = code[: match.start()].count("\n") + 1
            bug = Bug(
                id="var_usage",
                type="warning",
                severity="medium",
                message="Use of var keyword",
                description="var has function scope and can lead to unexpected behavior",
                line_number=line_number,
                column=(
                    match.start() - code.rfind("\n", 0, match.start())
                    if "\n" in code[: match.start()]
                    else match.start()
                ),
                code_snippet=match.group(),
                language=language,
                category="quality",
                fix_suggestion="Use const or let instead of var",
            )
            bugs.append(bug)

        # Check for == instead of ===
        loose_equals_matches = re.finditer(r"\s==\s", code)
        for match in loose_equals_matches:
            line_number = code[: match.start()].count("\n") + 1
            bug = Bug(
                id="loose_equality",
                type="warning",
                severity="medium",
                message="Use of loose equality (==)",
                description="== performs type coercion which can lead to unexpected results",
                line_number=line_number,
                column=(
                    match.start() - code.rfind("\n", 0, match.start())
                    if "\n" in code[: match.start()]
                    else match.start()
                ),
                code_snippet=match.group(),
                language=language,
                category="quality",
                fix_suggestion="Use strict equality (===) instead",
            )
            bugs.append(bug)

        return bugs

    def _check_javascript_security_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for JavaScript security vulnerabilities."""
        bugs = []

        # Check for eval usage
        if "eval(" in code:
            line_number = code.find("eval(") + 1
            bug = Bug(
                id="eval_usage",
                type="error",
                severity="critical",
                message="Use of eval() function",
                description="eval() can execute arbitrary code and is a security risk",
                line_number=line_number,
                column=1,
                code_snippet="eval()",
                language="javascript",
                category="security",
                fix_suggestion="Avoid using eval() for security reasons",
            )
            bugs.append(bug)

        return bugs

    def _check_javascript_performance_bugs(
        self, code: str, lines: List[str]
    ) -> List[Bug]:
        """Check for JavaScript performance issues."""
        bugs = []

        # Check for DOM manipulation in loops
        if "document." in code and "for" in code:
            bug = Bug(
                id="dom_in_loop",
                type="warning",
                severity="medium",
                message="DOM manipulation in loop",
                description="DOM manipulation in loops can cause performance issues",
                line_number=1,
                column=1,
                code_snippet="DOM manipulation",
                language="javascript",
                category="performance",
                fix_suggestion="Batch DOM operations or use DocumentFragment",
            )
            bugs.append(bug)

        return bugs

    def _check_java_common_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for common Java bugs."""
        bugs = []

        # Check for public fields
        public_field_matches = re.finditer(r"public\s+\w+\s+\w+\s*;", code)
        for match in public_field_matches:
            line_number = code[: match.start()].count("\n") + 1
            bug = Bug(
                id="public_field",
                type="warning",
                severity="medium",
                message="Public field detected",
                description="Public fields break encapsulation",
                line_number=line_number,
                column=(
                    match.start() - code.rfind("\n", 0, match.start())
                    if "\n" in code[: match.start()]
                    else match.start()
                ),
                code_snippet=match.group(),
                language="java",
                category="quality",
                fix_suggestion="Make fields private and provide getters/setters",
            )
            bugs.append(bug)

        return bugs

    def _check_java_security_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for Java security vulnerabilities."""
        bugs = []

        # Check for SQL injection
        if "Statement" in code and "execute" in code:
            bug = Bug(
                id="sql_injection",
                type="error",
                severity="high",
                message="Potential SQL injection",
                description="Using Statement instead of PreparedStatement can lead to SQL injection",
                line_number=1,
                column=1,
                code_snippet="Statement usage",
                language="java",
                category="security",
                fix_suggestion="Use PreparedStatement with parameterized queries",
            )
            bugs.append(bug)

        return bugs

    def _check_java_performance_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for Java performance issues."""
        bugs = []

        # Check for string concatenation in loops
        if "String" in code and "for" in code and "+" in code:
            bug = Bug(
                id="string_concat_in_loop",
                type="warning",
                severity="medium",
                message="String concatenation in loop",
                description="String concatenation in loops is inefficient",
                line_number=1,
                column=1,
                code_snippet="String concatenation",
                language="java",
                category="performance",
                fix_suggestion="Use StringBuilder for string concatenation in loops",
            )
            bugs.append(bug)

        return bugs

    def _check_cpp_common_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for common C++ bugs."""
        bugs = []

        # Check for raw pointers
        pointer_matches = re.finditer(r"\w+\s*\*\s*\w+", code)
        for match in pointer_matches:
            line_number = code[: match.start()].count("\n") + 1
            bug = Bug(
                id="raw_pointer",
                type="warning",
                severity="medium",
                message="Raw pointer usage",
                description="Raw pointers can lead to memory leaks and dangling pointers",
                line_number=line_number,
                column=(
                    match.start() - code.rfind("\n", 0, match.start())
                    if "\n" in code[: match.start()]
                    else match.start()
                ),
                code_snippet=match.group(),
                language="cpp",
                category="quality",
                fix_suggestion="Consider using smart pointers (unique_ptr, shared_ptr)",
            )
            bugs.append(bug)

        return bugs

    def _check_cpp_security_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for C++ security vulnerabilities."""
        bugs = []

        # Check for buffer overflow potential
        if "strcpy" in code or "strcat" in code:
            bug = Bug(
                id="buffer_overflow",
                type="error",
                severity="high",
                message="Potential buffer overflow",
                description="strcpy and strcat can cause buffer overflows",
                line_number=1,
                column=1,
                code_snippet="strcpy/strcat",
                language="cpp",
                category="security",
                fix_suggestion="Use strncpy, strncat, or std::string instead",
            )
            bugs.append(bug)

        return bugs

    def _check_cpp_performance_bugs(self, code: str, lines: List[str]) -> List[Bug]:
        """Check for C++ performance issues."""
        bugs = []

        # Check for unnecessary copying
        if "std::string" in code and "=" in code:
            bug = Bug(
                id="unnecessary_copy",
                type="warning",
                severity="low",
                message="Potential unnecessary copying",
                description="Consider using references to avoid copying",
                line_number=1,
                column=1,
                code_snippet="string copy",
                language="cpp",
                category="performance",
                fix_suggestion="Use const references to avoid copying",
            )
            bugs.append(bug)

        return bugs

    def _check_generic_bugs(
        self, code: str, lines: List[str], language: str
    ) -> List[Bug]:
        """Check for generic bugs in any language."""
        bugs = []

        # Check for long lines
        for i, line in enumerate(lines, 1):
            if len(line) > 120:
                bug = Bug(
                    id="long_line",
                    type="info",
                    severity="low",
                    message=f"Line {i} is too long",
                    description="Long lines can make code harder to read",
                    line_number=i,
                    column=1,
                    code_snippet=line[:50] + "...",
                    language=language,
                    category="quality",
                    fix_suggestion="Break long lines for better readability",
                )
                bugs.append(bug)

        return bugs

    def _bug_to_dict(self, bug: Bug) -> Dict[str, Any]:
        """Convert Bug object to dictionary."""
        return {
            "id": bug.id,
            "type": bug.type,
            "severity": bug.severity,
            "message": bug.message,
            "description": bug.description,
            "line_number": bug.line_number,
            "column": bug.column,
            "code_snippet": bug.code_snippet,
            "language": bug.language,
            "category": bug.category,
            "fix_suggestion": bug.fix_suggestion,
            "confidence": bug.confidence,
        }

    def _load_bug_patterns(self) -> Dict[str, List[str]]:
        """Load predefined bug patterns."""
        return {
            "python": [
                "syntax_error",
                "bare_except",
                "magic_number",
                "unused_import",
                "eval_usage",
            ],
            "javascript": [
                "var_usage",
                "loose_equality",
                "eval_usage",
                "callback_hell",
            ],
            "java": ["public_field", "sql_injection", "string_concat_in_loop"],
            "cpp": ["raw_pointer", "buffer_overflow", "memory_leak"],
        }

    def _load_security_patterns(self) -> Dict[str, List[str]]:
        """Load security vulnerability patterns."""
        return {
            "python": ["eval_usage", "exec_usage", "sql_injection"],
            "javascript": ["eval_usage", "xss", "csrf"],
            "java": ["sql_injection", "path_traversal"],
            "cpp": ["buffer_overflow", "format_string"],
        }

    def _load_performance_patterns(self) -> Dict[str, List[str]]:
        """Load performance issue patterns."""
        return {
            "python": ["inefficient_string_concat", "list_comprehension_vs_loop"],
            "javascript": ["dom_in_loop", "memory_leak"],
            "java": ["string_concat_in_loop", "boxing"],
            "cpp": ["unnecessary_copy", "virtual_function_call"],
        }

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get the health status of the bug detector.

        Returns:
            Dictionary with health status information
        """
        return {
            "status": self.health_status,
            "supported_languages": self.supported_languages,
            "bug_patterns_count": sum(
                len(patterns) for patterns in self.bug_patterns.values()
            ),
            "security_patterns_count": sum(
                len(patterns) for patterns in self.security_patterns.values()
            ),
            "performance_patterns_count": sum(
                len(patterns) for patterns in self.performance_patterns.values()
            ),
            "config": self.config,
        }
