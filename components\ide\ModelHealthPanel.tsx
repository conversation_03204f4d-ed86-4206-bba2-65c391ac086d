import React, { useState, useEffect } from 'react';
import { Activity, AlertTriangle, CheckCircle, Clock, Zap, RefreshCw, Settings } from 'lucide-react';
import { modelHealthMonitor } from '@/services/ModelHealthMonitor';
import toast from 'react-hot-toast';

interface ModelHealthPanelProps {
  className?: string;
}

interface ModelStatus {
  name: string;
  health: 'healthy' | 'degraded' | 'unhealthy' | 'offline';
  avgResponseTime: number;
  successRate: number;
  totalRequests: number;
  lastUsed: string | null;
  performanceScore: number;
}

export const ModelHealthPanel: React.FC<ModelHealthPanelProps> = ({ className = '' }) => {
  const [models, setModels] = useState<ModelStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadModelHealth();
    const interval = setInterval(loadModelHealth, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadModelHealth = async () => {
    try {
      setRefreshing(true);
      const response = await fetch('/api/v1/models/performance');
      if (response.ok) {
        const data = await response.json();
        const modelList = Object.entries(data.models).map(([name, info]: [string, any]) => ({
          name,
          health: info.health.health,
          avgResponseTime: info.health.avg_response_time,
          successRate: info.health.success_rate,
          totalRequests: info.health.total_requests,
          lastUsed: info.health.last_used,
          performanceScore: info.performance_score
        }));
        setModels(modelList);
      }
    } catch (error) {
      toast.error('Failed to load model health.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'unhealthy':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'offline':
        return <Activity className="w-4 h-4 text-gray-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'unhealthy':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'offline':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-blue-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatResponseTime = (time: number) => {
    if (time < 1) return `${(time * 1000).toFixed(0)}ms`;
    return `${time.toFixed(2)}s`;
  };

  const formatLastUsed = (timestamp: string | null) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  const testModel = async (modelName: string) => {
    try {
      const response = await fetch(`/api/v1/models/${modelName}/test`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: 'Hello, this is a test message.' })
      });

      if (response.ok) {
        const result = await response.json();
        // console.log(`Model ${modelName} test result:`, result);
        // Refresh health data
        loadModelHealth();
        toast.success(`Model ${modelName} test successful.`);
      }
    } catch (error) {
      toast.error(`Failed to test model ${modelName}.`);
    }
  };

  const clearCache = async () => {
    try {
      const response = await fetch('/api/v1/models/cache/clear', { method: 'POST' });
      if (response.ok) {
        // console.log('Model cache cleared');
        loadModelHealth();
        toast.success('Model cache cleared.');
      }
    } catch (error) {
      toast.error('Failed to clear cache.');
    }
  };

  if (loading) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="w-6 h-6 animate-spin text-gray-500" />
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-500" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            AI Model Health
          </h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={loadModelHealth}
            disabled={refreshing}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50"
            title="Refresh"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            title="Toggle details"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Model List */}
      <div className="p-4 space-y-3">
        {models.map((model) => (
          <div
            key={model.name}
            className={`p-3 rounded-lg border ${getHealthColor(model.health)} dark:bg-gray-700 dark:border-gray-600`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getHealthIcon(model.health)}
                <div>
                  <div className="text-sm font-medium dark:text-gray-100">
                    {model.name.split(':')[0]}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {model.name.split(':')[1]}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <div className={`text-sm font-semibold ${getPerformanceColor(model.performanceScore)}`}>
                    {model.performanceScore.toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Performance
                  </div>
                </div>

                <button
                  onClick={() => testModel(model.name)}
                  className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  Test
                </button>
              </div>
            </div>

            {showDetails && (
              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600 space-y-2">
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      {formatResponseTime(model.avgResponseTime)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Zap className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      {(model.successRate * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Activity className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      {model.totalRequests} requests
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      {formatLastUsed(model.lastUsed)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {models.filter(m => m.health === 'healthy').length} of {models.length} models healthy
          </div>
          <button
            onClick={clearCache}
            className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Clear Cache
          </button>
        </div>
      </div>
    </div>
  );
};
