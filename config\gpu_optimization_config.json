{"gpu_configuration": {"hardware": {"model": "NVIDIA Quadro P1000", "memory": "4GB GDDR5", "cuda_cores": 512, "memory_bandwidth": "32 GB/s", "compute_capability": "6.1"}, "allocation_strategy": {"primary_service": "ollama", "secondary_services": ["fine_tuner", "model_optimizer"], "memory_allocation": {"ollama": "4GB", "fine_tuner": "4GB (when active)", "model_optimizer": "4GB (when active)"}, "scheduling": {"priority": {"ollama": 1, "fine_tuner": 2, "model_optimizer": 3}, "conflict_resolution": "stop_lower_priority_service"}}, "optimization_settings": {"cuda_visible_devices": "0", "gpu_memory_fraction": 0.95, "allow_growth": true, "per_process_gpu_memory_fraction": 0.9, "mixed_precision": true, "tensor_cores": false}, "model_support": {"ollama_models": ["deepseek-coder:1.3b", "yi-coder:1.5b", "qwen2.5-coder:3b", "starcoder2:3b", "mistral:7b-instruct-q4_0"], "max_model_size": "3.5GB", "recommended_batch_size": 1, "quantization": {"enabled": true, "methods": ["q4_0", "q4_1", "q5_0", "q5_1"], "default": "q4_0"}}, "performance_tuning": {"memory_management": {"preload_models": false, "unload_idle_models": true, "idle_timeout": 300}, "inference_optimization": {"use_fast_attention": true, "use_flash_attention": false, "use_xformers": false, "max_sequence_length": 4096}, "training_optimization": {"gradient_accumulation_steps": 4, "mixed_precision_training": true, "gradient_checkpointing": true, "max_grad_norm": 1.0}}, "monitoring": {"gpu_utilization_threshold": 90, "memory_utilization_threshold": 95, "temperature_threshold": 85, "power_monitoring": true, "metrics_collection_interval": 30}, "docker_configuration": {"runtime": "nvidia", "environment_variables": {"NVIDIA_VISIBLE_DEVICES": "0", "NVIDIA_DRIVER_CAPABILITIES": "compute,utility", "CUDA_VISIBLE_DEVICES": "0"}, "device_requests": {"driver": "nvidia", "count": 1, "capabilities": ["gpu"], "options": {"memory": "4GB"}}}, "fallback_strategy": {"cpu_fallback": true, "memory_optimization": true, "model_quantization": true, "batch_size_reduction": true}, "maintenance": {"gpu_cleanup_interval": 3600, "memory_defragmentation": true, "cache_clear_interval": 7200, "driver_update_check": true}}, "service_specific_configs": {"ollama": {"gpu_memory_fraction": 1.0, "model_loading_strategy": "on_demand", "concurrent_requests": 1, "timeout": 300}, "fine_tuner": {"gpu_memory_fraction": 1.0, "training_batch_size": 1, "gradient_accumulation": 4, "checkpoint_interval": 100}, "model_optimizer": {"gpu_memory_fraction": 1.0, "optimization_batch_size": 1, "pruning_enabled": true, "quantization_enabled": true}}, "health_checks": {"gpu_health_check_interval": 60, "memory_health_check_interval": 30, "temperature_health_check_interval": 60, "performance_health_check_interval": 300}}