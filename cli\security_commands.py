"""
Security Management CLI Commands
Provides command-line interface for security operations
"""

import json
import sys
from pathlib import Path
from typing import Any, Dict

import click

from security.security_manager import SecurityManager


@click.group()
def security():
    """Security Management Commands"""
    pass


@security.command()
@click.argument("ip_address")
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def check_rate_limit(ip_address: str, config: str):
    """Check rate limit status for an IP address"""
    try:
        security_manager = SecurityManager(config)
        result = security_manager.check_rate_limit(ip_address)

        if result["allowed"]:
            click.echo(f"✅ IP {ip_address} is allowed")
            if result["remaining"] >= 0:
                click.echo(f"Remaining requests: {result['remaining']}")
        else:
            click.echo(f"❌ IP {ip_address} is blocked")
            click.echo(f"Reason: {result['reason']}")
            if "blocked_until" in result:
                click.echo(f"Blocked until: {result['blocked_until']}")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.argument("ip_address")
@click.argument("reason")
@click.option(
    "--duration", type=int, help="Block duration in minutes (default: permanent)"
)
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def block_ip(ip_address: str, reason: str, duration: int, config: str):
    """Block an IP address"""
    try:
        security_manager = SecurityManager(config)
        success = security_manager.block_ip(ip_address, reason, duration)

        if success:
            click.echo(f"✅ IP {ip_address} blocked successfully")
            if duration:
                click.echo(f"Blocked for {duration} minutes")
            else:
                click.echo("Blocked permanently")
        else:
            click.echo(f"❌ Failed to block IP {ip_address}", err=True)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.argument("ip_address")
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def unblock_ip(ip_address: str, config: str):
    """Unblock an IP address"""
    try:
        security_manager = SecurityManager(config)
        success = security_manager.unblock_ip(ip_address)

        if success:
            click.echo(f"✅ IP {ip_address} unblocked successfully")
        else:
            click.echo(f"❌ Failed to unblock IP {ip_address}", err=True)
            sys.exit(1)

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.option("--hours", type=int, default=24, help="Hours to look back for statistics")
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def stats(hours: int, config: str):
    """Show security statistics"""
    try:
        security_manager = SecurityManager(config)
        stats = security_manager.get_security_stats(hours)

        click.echo(f"Security Statistics (Last {hours} hours):")
        click.echo("-" * 50)
        click.echo(f"Total Events: {stats['total_events']}")

        if stats["events_by_type"]:
            click.echo("\nEvents by Type:")
            for event_type, count in stats["events_by_type"].items():
                click.echo(f"  {event_type}: {count}")

        if stats["top_blocked_ips"]:
            click.echo("\nTop Blocked IPs:")
            for ip, count in stats["top_blocked_ips"]:
                click.echo(f"  {ip}: {count} events")

        if stats["currently_blocked"]:
            click.echo("\nCurrently Blocked IPs:")
            for ip, reason, blocked_at in stats["currently_blocked"]:
                click.echo(f"  {ip}: {reason} (since {blocked_at})")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.option("--days", type=int, default=30, help="Days of data to keep")
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def cleanup(days: int, config: str):
    """Clean up old security data"""
    try:
        security_manager = SecurityManager(config)
        deleted_count = security_manager.cleanup_old_data(days)

        click.echo(f"✅ Cleaned up {deleted_count} old security records")
        click.echo(f"Kept data from last {days} days")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def headers(config: str):
    """Show security headers configuration"""
    try:
        security_manager = SecurityManager(config)
        headers = security_manager.get_security_headers()

        click.echo("Security Headers:")
        click.echo("-" * 50)

        if headers:
            for header, value in headers.items():
                click.echo(f"{header}: {value}")
        else:
            click.echo("Security headers are disabled")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.argument("ip_address")
@click.argument("user_agent")
@click.argument("path")
@click.argument("method")
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def validate_request(
    ip_address: str, user_agent: str, path: str, method: str, config: str
):
    """Validate a request for security"""
    try:
        security_manager = SecurityManager(config)
        result = security_manager.validate_request(ip_address, user_agent, path, method)

        if result["allowed"]:
            click.echo(f"✅ Request is allowed")
            if result["warnings"]:
                click.echo("Warnings:")
                for warning in result["warnings"]:
                    click.echo(f"  - {warning}")
        else:
            click.echo(f"❌ Request is blocked")
            click.echo(f"Reason: {result['blocked_reason']}")
            if result["warnings"]:
                click.echo("Warnings:")
                for warning in result["warnings"]:
                    click.echo(f"  - {warning}")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


@security.command()
@click.option(
    "--config",
    default="config/security_config.json",
    help="Security configuration file path",
)
def config_show(config: str):
    """Show security configuration"""
    try:
        security_manager = SecurityManager(config)
        click.echo("Security Configuration:")
        click.echo(json.dumps(security_manager.config, indent=2))

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)
        sys.exit(1)


if __name__ == "__main__":
    security()
