#!/usr/bin/env python3
"""
Feedback Manager Module - Command and Feedback Tracking
Extracted from core/agent.py for modular organization
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

from core.agents.base_agent import AgentLogger


class FeedbackManager:
    """Manages command history and user feedback"""

    def __init__(self, logger: AgentLogger):
        self.logger = logger
        self._ensure_data_directory()

    def _ensure_data_directory(self):
        """Ensure data directory exists for storing feedback"""
        Path("data/feedback").mkdir(parents=True, exist_ok=True)

    def record_command(
        self, command: str, args: Dict[str, Any], result: Dict[str, Any]
    ):
        """Record a command execution with its arguments and result"""
        try:
            command_record = {
                "command": command,
                "args": args,
                "result": result,
                "timestamp": datetime.now().isoformat(),
                "success": result.get("success", False),
            }

            # Load existing history
            history_file = "data/feedback/command_history.json"
            history = self._load_json_file(history_file, [])

            # Add new record
            history.append(command_record)

            # Keep only last 1000 records
            if len(history) > 1000:
                history = history[-1000:]

            # Save updated history
            self._save_json_file(history_file, history)

            self.logger.info(
                f"Recorded command: {command}", command_record=command_record
            )

        except Exception as e:
            self.logger.error(f"Failed to record command: {e}", error=e)

    def record_feedback(self, command: str, feedback: Dict[str, Any]):
        """Record user feedback for a command"""
        try:
            feedback_record = {
                "command": command,
                "feedback": feedback,
                "timestamp": datetime.now().isoformat(),
            }

            # Load existing feedback
            feedback_file = "data/feedback/user_feedback.json"
            feedback_history = self._load_json_file(feedback_file, [])

            # Add new feedback
            feedback_history.append(feedback_record)

            # Keep only last 500 feedback records
            if len(feedback_history) > 500:
                feedback_history = feedback_history[-500:]

            # Save updated feedback
            self._save_json_file(feedback_file, feedback_history)

            self.logger.info(
                f"Recorded feedback for command: {command}",
                feedback_record=feedback_record,
            )

        except Exception as e:
            self.logger.error(f"Failed to record feedback: {e}", error=e)

    def get_command_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent command history"""
        try:
            history_file = "data/feedback/command_history.json"
            history = self._load_json_file(history_file, [])
            return history[-limit:] if limit > 0 else history
        except Exception as e:
            self.logger.error(f"Failed to get command history: {e}", error=e)
            return []

    def get_feedback(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent user feedback"""
        try:
            feedback_file = "data/feedback/user_feedback.json"
            feedback_history = self._load_json_file(feedback_file, [])
            return feedback_history[-limit:] if limit > 0 else feedback_history
        except Exception as e:
            self.logger.error(f"Failed to get feedback: {e}", error=e)
            return []

    def _load_json_file(self, file_path: str, default: Any) -> Any:
        """Load JSON file with error handling"""
        try:
            if os.path.exists(file_path):
                with open(file_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            return default
        except Exception as e:
            self.logger.error(f"Failed to load JSON file {file_path}: {e}", error=e)
            return default

    def _save_json_file(self, file_path: str, data: Any):
        """Save JSON file with error handling"""
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save JSON file {file_path}: {e}", error=e)
