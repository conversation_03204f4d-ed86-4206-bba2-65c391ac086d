import asyncio

import click

from core.agents import ArchitectAgent


@click.group()
def architect():
    """ArchitectAgent CLI commands"""
    pass


@architect.command()
@click.argument("user_command")
def process(user_command):
    """Process a high-level user command (natural language)"""
    agent = ArchitectAgent()
    result = asyncio.run(agent.process_user_command(user_command))
    click.echo(result)


@architect.command()
@click.argument("task_id")
def status(task_id):
    """Get the status of a specific task"""
    agent = ArchitectAgent()
    result = asyncio.run(agent.get_task_status(task_id))
    click.echo(result)


@architect.command()
def all():
    """List all tasks (active and completed)"""
    agent = ArchitectAgent()
    result = asyncio.run(agent.get_all_tasks())
    click.echo(result)
