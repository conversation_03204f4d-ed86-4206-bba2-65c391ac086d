import React from 'react';
import { render, screen } from '@testing-library/react';
import { IDELayout } from '../IDELayout';

jest.mock('../ChatPanel', () => ({
  ChatPanel: () => <div data-testid="chat-panel">Chat Panel</div>,
}));

jest.mock('../PreviewPanel', () => ({
  PreviewPanel: () => <div data-testid="preview-panel">Preview Panel</div>,
}));

jest.mock('../Toolbar', () => ({
  Toolbar: () => <div data-testid="toolbar">Toolbar</div>,
}));

jest.mock('../StatusBar', () => ({
  StatusBar: () => <div data-testid="status-bar">Status Bar</div>,
}));

describe('IDELayout', () => {
  it('renders all IDE components', () => {
    render(<IDELayout />);

    expect(screen.getByTestId('toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('preview-panel')).toBeInTheDocument();
    expect(screen.getByTestId('chat-panel')).toBeInTheDocument();
    expect(screen.getByTestId('status-bar')).toBeInTheDocument();
  });

    it('applies custom className', () => {
    render(<IDELayout className="custom-class" />);

    // The className is applied to the main container div
    const container = screen.getByTestId('toolbar').closest('.custom-class');
    expect(container).toBeInTheDocument();
  });
});
