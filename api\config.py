"""
API Configuration Management
Centralized configuration for all API components including settings, limits, and feature flags.
"""

import json
import os
from dataclasses import dataclass, field
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional


@dataclass
class APIConfig:
    """API configuration settings"""

    # API metadata
    title: str = "AI Coding Agent API"
    description: str = (
        "RESTful API for AI-powered coding assistance and project management"
    )
    version: str = "1.0.0"

    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    reload: bool = False

    # Security settings
    api_key: Optional[str] = None
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    rate_limit_requests_per_minute: int = 60
    max_content_length_mb: int = 10

    # Model settings
    default_model: str = "deepseek-coder:1.3b"
    available_models: List[str] = field(
        default_factory=lambda: [
            "deepseek-coder:1.3b",
            "yi-coder:1.5b",
            "qwen2.5-coder:3b",
            "starcoder2:3b",
            "mistral:7b-instruct-q4_0",
        ]
    )
    model_timeout_seconds: int = 30

    # Upload settings
    max_upload_size_mb: int = 100
    max_individual_file_size_mb: int = 50
    allowed_extensions: List[str] = field(
        default_factory=lambda: [
            ".html",
            ".css",
            ".js",
            ".jsx",
            ".ts",
            ".tsx",
            ".json",
            ".md",
            ".txt",
            ".py",
            ".yml",
            ".yaml",
            ".xml",
            ".svg",
            ".png",
            ".jpg",
            ".jpeg",
            ".gif",
            ".ico",
            ".woff",
            ".woff2",
            ".ttf",
            ".eot",
            ".map",
            ".lock",
            ".gitignore",
        ]
    )

    # File operations
    max_file_size_mb: int = 5
    supported_encodings: List[str] = field(
        default_factory=lambda: ["utf-8", "ascii", "latin-1", "cp1252"]
    )

    # Command execution
    command_timeout_seconds: int = 300
    max_command_output_size: int = 1024 * 1024  # 1MB

    # Git operations
    git_timeout_seconds: int = 60
    max_commit_message_length: int = 500

    # Preview server
    preview_server_port_range: tuple = (8001, 8010)
    preview_server_timeout_seconds: int = 30

    # Logging
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file: Optional[str] = None

    # Monitoring
    enable_metrics: bool = True
    metrics_retention_hours: int = 24

    # Error handling
    include_stack_traces: bool = False
    error_reporting_enabled: bool = True

    # Feature flags
    enable_websockets: bool = True
    enable_file_editing: bool = True
    enable_git_operations: bool = True
    enable_preview_server: bool = True
    enable_command_execution: bool = True

    @classmethod
    def from_env(cls) -> "APIConfig":
        """Create configuration from environment variables"""
        config = cls()

        # Server settings
        config.host = os.getenv("API_HOST", config.host)
        config.port = int(os.getenv("API_PORT", str(config.port)))
        config.debug = os.getenv("API_DEBUG", "false").lower() == "true"
        config.reload = os.getenv("API_RELOAD", "false").lower() == "true"

        # Security settings
        config.api_key = os.getenv("API_KEY", config.api_key)
        cors_origins = os.getenv("CORS_ORIGINS")
        if cors_origins:
            config.cors_origins = [origin.strip() for origin in cors_origins.split(",")]
        config.rate_limit_requests_per_minute = int(
            os.getenv("RATE_LIMIT_RPM", str(config.rate_limit_requests_per_minute))
        )
        config.max_content_length_mb = int(
            os.getenv("MAX_CONTENT_LENGTH_MB", str(config.max_content_length_mb))
        )

        # Model settings
        config.default_model = os.getenv("DEFAULT_MODEL", config.default_model)
        available_models = os.getenv("AVAILABLE_MODELS")
        if available_models:
            config.available_models = [
                model.strip() for model in available_models.split(",")
            ]
        config.model_timeout_seconds = int(
            os.getenv("MODEL_TIMEOUT_SECONDS", str(config.model_timeout_seconds))
        )

        # Upload settings
        config.max_upload_size_mb = int(
            os.getenv("MAX_UPLOAD_SIZE_MB", str(config.max_upload_size_mb))
        )
        config.max_individual_file_size_mb = int(
            os.getenv(
                "MAX_INDIVIDUAL_FILE_SIZE_MB", str(config.max_individual_file_size_mb)
            )
        )

        # Logging
        config.log_level = os.getenv("LOG_LEVEL", config.log_level)
        config.log_file = os.getenv("LOG_FILE", config.log_file)

        # Feature flags
        config.enable_websockets = (
            os.getenv("ENABLE_WEBSOCKETS", "true").lower() == "true"
        )
        config.enable_file_editing = (
            os.getenv("ENABLE_FILE_EDITING", "true").lower() == "true"
        )
        config.enable_git_operations = (
            os.getenv("ENABLE_GIT_OPERATIONS", "true").lower() == "true"
        )
        config.enable_preview_server = (
            os.getenv("ENABLE_PREVIEW_SERVER", "true").lower() == "true"
        )
        config.enable_command_execution = (
            os.getenv("ENABLE_COMMAND_EXECUTION", "true").lower() == "true"
        )

        return config

    @classmethod
    def from_file(cls, config_path: str) -> "APIConfig":
        """Create configuration from JSON file"""
        config = cls()

        if Path(config_path).exists():
            with open(config_path, "r") as f:
                file_config = json.load(f)

            # Update config with file values
            for key, value in file_config.items():
                if hasattr(config, key):
                    setattr(config, key, value)

        return config

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "host": self.host,
            "port": self.port,
            "debug": self.debug,
            "reload": self.reload,
            "api_key": self.api_key,
            "cors_origins": self.cors_origins,
            "rate_limit_requests_per_minute": self.rate_limit_requests_per_minute,
            "max_content_length_mb": self.max_content_length_mb,
            "default_model": self.default_model,
            "available_models": self.available_models,
            "model_timeout_seconds": self.model_timeout_seconds,
            "max_upload_size_mb": self.max_upload_size_mb,
            "max_individual_file_size_mb": self.max_individual_file_size_mb,
            "allowed_extensions": self.allowed_extensions,
            "max_file_size_mb": self.max_file_size_mb,
            "supported_encodings": self.supported_encodings,
            "command_timeout_seconds": self.command_timeout_seconds,
            "max_command_output_size": self.max_command_output_size,
            "git_timeout_seconds": self.git_timeout_seconds,
            "max_commit_message_length": self.max_commit_message_length,
            "preview_server_port_range": self.preview_server_port_range,
            "preview_server_timeout_seconds": self.preview_server_timeout_seconds,
            "log_level": self.log_level,
            "log_format": self.log_format,
            "log_file": self.log_file,
            "enable_metrics": self.enable_metrics,
            "metrics_retention_hours": self.metrics_retention_hours,
            "include_stack_traces": self.include_stack_traces,
            "error_reporting_enabled": self.error_reporting_enabled,
            "enable_websockets": self.enable_websockets,
            "enable_file_editing": self.enable_file_editing,
            "enable_git_operations": self.enable_git_operations,
            "enable_preview_server": self.enable_preview_server,
            "enable_command_execution": self.enable_command_execution,
        }

    def save_to_file(self, config_path: str) -> None:
        """Save configuration to JSON file"""
        with open(config_path, "w") as f:
            json.dump(self.to_dict(), f, indent=2)

    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []

        if self.port < 1 or self.port > 65535:
            errors.append("Port must be between 1 and 65535")

        if self.rate_limit_requests_per_minute < 1:
            errors.append("Rate limit must be at least 1 request per minute")

        if self.max_content_length_mb < 1:
            errors.append("Max content length must be at least 1MB")

        if self.max_upload_size_mb < 1:
            errors.append("Max upload size must be at least 1MB")

        if self.model_timeout_seconds < 1:
            errors.append("Model timeout must be at least 1 second")

        if self.command_timeout_seconds < 1:
            errors.append("Command timeout must be at least 1 second")

        if self.git_timeout_seconds < 1:
            errors.append("Git timeout must be at least 1 second")

        if self.max_commit_message_length < 1:
            errors.append("Max commit message length must be at least 1 character")

        return errors


# Global configuration instance
_config: Optional[APIConfig] = None


def get_config() -> APIConfig:
    """Get the global API configuration"""
    global _config

    if _config is None:
        # Try to load from config file first
        config_path = os.getenv("API_CONFIG_PATH", "config/api_config.json")
        if Path(config_path).exists():
            _config = APIConfig.from_file(config_path)
        else:
            # Fall back to environment variables
            _config = APIConfig.from_env()

        # Validate configuration
        errors = _config.validate()
        if errors:
            raise ValueError(f"Invalid API configuration: {', '.join(errors)}")

    return _config


def set_config(config: APIConfig) -> None:
    """Set the global API configuration"""
    global _config
    _config = config


def reload_config() -> None:
    """Reload configuration from environment or file"""
    global _config
    _config = None
    get_config()
