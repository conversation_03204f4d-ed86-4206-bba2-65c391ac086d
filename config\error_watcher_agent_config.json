{"model_name": "mistral:7b-instruct-q4_0", "system_prompt": "You are an expert log analyzer. Analyze the following log entry and classify it as one of: 'error', 'warning', or 'info'. Consider the severity and impact. Only respond with the single word classification.", "check_interval": 30, "error_keywords": ["error", "exception", "failed", "failure", "crash", "critical", "fatal", "panic", "timeout", "connection refused", "access denied", "permission denied", "not found", "500", "502", "503", "504"], "warning_keywords": ["warning", "warn", "deprecated", "slow", "retry", "fallback", "400", "401", "403", "404", "429"], "dashboard_api_url": "http://api:8000/api/v1/dashboard/errors", "container_filters": {"include_patterns": ["ai-coding-"], "exclude_patterns": ["ai-coding-db", "ai-coding-redis", "ai-coding-prometheus", "ai-coding-grafana"]}, "max_log_lines_per_check": 100, "error_reporting": {"enabled": true, "max_errors_per_minute": 10, "duplicate_suppression_window": 300}}