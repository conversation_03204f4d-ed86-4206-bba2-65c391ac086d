{"models": {"deepseek-coder:6.7b-instruct": {"name": "deepseek-coder:6.7b-instruct", "type": "local", "endpoint": "http://localhost:11434/api/generate", "use_cases": ["code_generation", "code_analysis", "bug_fixing", "complex_problem_solving"], "performance": {"max_response_time": 15.0, "target_response_time": 8.0, "min_success_rate": 0.95, "max_concurrent_requests": 3, "timeout": 60, "memory_usage_mb": 2048, "gpu_layers": 24, "gpu_memory_mb": 1536}, "optimization": {"temperature": 0.3, "top_p": 0.9, "top_k": 40, "max_tokens": 4096, "frequency_penalty": 0.1, "presence_penalty": 0.1, "repeat_penalty": 1.1, "mirostat": 2, "mirostat_tau": 5.0, "mirostat_eta": 0.1}, "fallback_models": ["yi-coder:1.5b", "starcoder2:3b"], "prompt_engineering": {"system_prompt": "You are an expert software developer. Provide clear, efficient, and well-documented code solutions.", "context_window": 8192, "max_context_tokens": 6144}}, "yi-coder:1.5b": {"name": "yi-coder:1.5b", "type": "local", "endpoint": "http://localhost:11434/api/generate", "use_cases": ["code_review", "intent_parsing", "natural_language_understanding", "quick_analysis"], "performance": {"max_response_time": 6.0, "target_response_time": 3.0, "min_success_rate": 0.92, "max_concurrent_requests": 4, "timeout": 20, "memory_usage_mb": 1024, "gpu_layers": 24, "gpu_memory_mb": 1536}, "optimization": {"temperature": 0.4, "top_p": 0.85, "top_k": 30, "max_tokens": 2048, "frequency_penalty": 0.15, "presence_penalty": 0.15, "repeat_penalty": 1.05, "mirostat": 1, "mirostat_tau": 3.0, "mirostat_eta": 0.2}, "fallback_models": ["deepseek-coder:1.3b", "qwen2.5-coder:3b"], "prompt_engineering": {"system_prompt": "You are a code review expert. Analyze code for quality, security, and best practices.", "context_window": 4096, "max_context_tokens": 3072}}, "qwen2.5:3b": {"name": "qwen2.5:3b", "type": "local", "endpoint": "http://localhost:11434/api/generate", "use_cases": ["content_creation", "documentation_generation", "blog_writing", "explanation"], "performance": {"max_response_time": 12.0, "target_response_time": 6.0, "min_success_rate": 0.9, "max_concurrent_requests": 2, "timeout": 35, "memory_usage_mb": 1536, "gpu_layers": 28, "gpu_memory_mb": 2688}, "optimization": {"temperature": 0.7, "top_p": 0.95, "top_k": 50, "max_tokens": 3072, "frequency_penalty": 0.05, "presence_penalty": 0.05, "repeat_penalty": 1.08, "mirostat": 2, "mirostat_tau": 4.0, "mirostat_eta": 0.15}, "fallback_models": ["deepseek-coder:1.3b", "yi-coder:1.5b"], "prompt_engineering": {"system_prompt": "You are a technical writer and content creator. Generate clear, engaging, and informative content.", "context_window": 6144, "max_context_tokens": 4608}}, "starcoder2:3b": {"name": "starcoder2:3b", "type": "local", "endpoint": "http://localhost:11434/api/generate", "use_cases": ["advanced_code_generation", "architecture_design", "complex_problem_solving", "refactoring"], "performance": {"max_response_time": 15.0, "target_response_time": 8.0, "min_success_rate": 0.88, "max_concurrent_requests": 2, "timeout": 45, "memory_usage_mb": 2048, "gpu_layers": 28, "gpu_memory_mb": 2688}, "optimization": {"temperature": 0.5, "top_p": 0.9, "top_k": 40, "max_tokens": 4096, "frequency_penalty": 0.2, "presence_penalty": 0.2, "repeat_penalty": 1.15, "mirostat": 2, "mirostat_tau": 4.5, "mirostat_eta": 0.12}, "fallback_models": ["deepseek-coder:1.3b", "yi-coder:1.5b"], "prompt_engineering": {"system_prompt": "You are an expert software architect. Design scalable, maintainable, and efficient solutions.", "context_window": 8192, "max_context_tokens": 6144}}, "mistral:7b-instruct-q4_0": {"name": "mistral:7b-instruct-q4_0", "type": "local", "endpoint": "http://localhost:11434/api/generate", "use_cases": ["general_assistance", "explanation", "teaching", "conversation", "problem_solving"], "performance": {"max_response_time": 10.0, "target_response_time": 5.0, "min_success_rate": 0.92, "max_concurrent_requests": 2, "timeout": 30, "memory_usage_mb": 2048, "gpu_layers": 32, "gpu_memory_mb": 3129}, "optimization": {"temperature": 0.6, "top_p": 0.9, "top_k": 40, "max_tokens": 3072, "frequency_penalty": 0.1, "presence_penalty": 0.1, "repeat_penalty": 1.1, "mirostat": 2, "mirostat_tau": 4.0, "mirostat_eta": 0.15}, "fallback_models": ["deepseek-coder:1.3b", "yi-coder:1.5b"], "prompt_engineering": {"system_prompt": "You are a helpful AI assistant. Provide clear, accurate, and educational responses.", "context_window": 8192, "max_context_tokens": 6144}}}, "gpu_configuration": {"gpu_name": "NVIDIA Quadro P1000", "total_vram_mb": 4096, "available_vram_mb": 3682, "optimization_strategy": "maximize_gpu_utilization", "gpu_layers_strategy": {"small_models": 30, "medium_models": 35, "large_models": 35}, "memory_allocation": {"max_gpu_memory_per_model_mb": 3200, "reserve_system_memory_mb": 896, "dynamic_allocation": true}, "performance_targets": {"target_gpu_utilization": 85, "max_gpu_memory_usage": 90, "min_response_time_improvement": 30}}, "switching_rules": {"health_thresholds": {"healthy": {"success_rate": 0.95, "avg_response_time": 6.0, "memory_usage_percent": 80, "gpu_utilization_percent": 70}, "degraded": {"success_rate": 0.8, "avg_response_time": 12.0, "memory_usage_percent": 90, "gpu_utilization_percent": 95}, "unhealthy": {"success_rate": 0.7, "avg_response_time": 20.0, "memory_usage_percent": 95, "gpu_utilization_percent": 98}}, "performance_weights": {"success_rate": 0.4, "response_time": 0.3, "memory_usage": 0.15, "gpu_utilization": 0.15}, "fallback_strategy": {"max_fallback_attempts": 3, "fallback_delay": 1.0, "prefer_faster_models": true, "load_balancing": true, "gpu_aware_fallback": true}}, "caching_strategy": {"enabled": true, "max_cache_size": 200, "ttl_seconds": 7200, "cache_by": ["model_name", "prompt_hash", "use_case", "temperature"], "exclude_patterns": ["time_sensitive", "user_specific", "dynamic_content", "session_data"], "compression": true, "persistence": {"enabled": true, "file_path": "cache/model_responses.json", "max_file_size_mb": 100}}, "prompt_optimization": {"enabled": true, "techniques": ["few_shot_learning", "chain_of_thought", "role_playing", "context_compression", "prompt_chunking"], "max_prompt_length": 6000, "context_compression_ratio": 0.8, "dynamic_context": true}, "resource_management": {"memory_monitoring": true, "gpu_utilization": true, "auto_cleanup": true, "cleanup_interval_seconds": 300, "max_memory_usage_percent": 85, "max_gpu_memory_usage_percent": 90, "model_unloading": {"enabled": true, "idle_timeout_seconds": 1800, "memory_threshold_percent": 90, "gpu_memory_threshold_percent": 95}, "gpu_optimization": {"enabled": true, "dynamic_gpu_layers": true, "gpu_memory_monitoring": true, "auto_gpu_layer_adjustment": true}}, "monitoring": {"metrics_collection": true, "performance_tracking": true, "error_logging": true, "gpu_monitoring": true, "health_checks": {"interval_seconds": 60, "timeout_seconds": 10, "retry_attempts": 3}, "alerts": {"enabled": true, "response_time_threshold": 15.0, "error_rate_threshold": 0.1, "memory_usage_threshold": 90, "gpu_memory_threshold": 95, "gpu_utilization_threshold": 98}}}