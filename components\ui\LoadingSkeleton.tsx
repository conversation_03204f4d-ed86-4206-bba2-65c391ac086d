import React from 'react';

export interface LoadingSkeletonProps {
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
  width?: string | number;
  height?: string | number;
  className?: string;
  lines?: number;
  spacing?: 'sm' | 'md' | 'lg';
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  variant = 'text',
  width,
  height,
  className = '',
  lines = 1,
  spacing = 'md',
}) => {
  const baseClasses = 'animate-pulse bg-gray-200 dark:bg-gray-700';

  const variantClasses = {
    text: 'rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-none',
    rounded: 'rounded-lg',
  };

  const spacingClasses = {
    sm: 'space-y-2',
    md: 'space-y-3',
    lg: 'space-y-4',
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  if (lines > 1) {
    return (
      <div className={`${spacingClasses[spacing]} ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`${baseClasses} ${variantClasses[variant]}`}
            style={{
              ...style,
              height: height ? (typeof height === 'number' ? `${height}px` : height) : '1rem',
              width: index === lines - 1 && width ? '60%' : '100%',
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      style={style}
    />
  );
};

// Predefined skeleton components for common use cases
export const TextSkeleton: React.FC<{ lines?: number; className?: string }> = ({
  lines = 1,
  className = ''
}) => (
  <LoadingSkeleton variant="text" lines={lines} className={className} />
);

export const AvatarSkeleton: React.FC<{ size?: number; className?: string }> = ({
  size = 40,
  className = ''
}) => (
  <LoadingSkeleton
    variant="circular"
    width={size}
    height={size}
    className={className}
  />
);

export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`p-4 space-y-3 ${className}`}>
    <LoadingSkeleton variant="rounded" height={20} width="60%" />
    <LoadingSkeleton variant="text" lines={3} />
    <div className="flex space-x-2">
      <LoadingSkeleton variant="rounded" height={32} width={80} />
      <LoadingSkeleton variant="rounded" height={32} width={80} />
    </div>
  </div>
);
