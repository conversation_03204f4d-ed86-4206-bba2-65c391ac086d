import React from 'react';
import {
  Save, Play, Square, UploadCloud, Loader2, CheckCircle, XCircle, Shield, Activity, Database, Zap, TestTube, Brain, HelpCircle
} from 'lucide-react';

interface ToolbarMainActionsProps {
  isRunning: boolean;
  deploying: boolean;
  deployStatus: 'idle' | 'deploying' | 'success' | 'error';
  sslGenerating: boolean;
  maintenanceRunning: boolean;
  performanceAnalyzing: boolean;
  testingRunning: boolean;
  showModelHealth: boolean;
  showHelp: boolean;
  handleSave: () => void;
  handleRun: () => void;
  handleStop: () => void;
  handleDeploy: () => void;
  handleSSLGenerate: () => void;
  handleHealthCheck: () => void;
  handleBackup: () => void;
  handlePerformanceAnalysis: () => void;
  handleRunTests: () => void;
  handleShowModelHealth: () => void;
  handleShowHelp: () => void;
}

export const ToolbarMainActions: React.FC<ToolbarMainActionsProps> = ({
  isRunning,
  deploying,
  deployStatus,
  sslGenerating,
  maintenanceRunning,
  performanceAnalyzing,
  testingRunning,
  showModelHealth,
  showHelp,
  handleSave,
  handleRun,
  handleStop,
  handleDeploy,
  handleSSLGenerate,
  handleHealthCheck,
  handleBackup,
  handlePerformanceAnalysis,
  handleRunTests,
  handleShowModelHealth,
  handleShowHelp
}) => (
  <div className="flex items-center space-x-2" role="group" aria-label="Main toolbar actions">
    <button
      onClick={handleSave}
      className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
      title="Save Project (Ctrl/Cmd + S)"
      aria-label="Save project"
    >
      <Save className="w-4 h-4" aria-hidden="true" />
      <span>Save</span>
    </button>
    <div className="flex items-center space-x-1" role="group" aria-label="Run controls">
      <button
        onClick={handleRun}
        disabled={isRunning}
        className="flex items-center space-x-1 px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
        title="Run Project (Ctrl/Cmd + R)"
        aria-label="Run project"
        aria-describedby={isRunning ? 'run-button-error' : undefined}
      >
        <Play className="w-4 h-4" aria-hidden="true" />
        <span>Run</span>
      </button>
      {isRunning && (
        <button
          onClick={handleStop}
          className="flex items-center space-x-1 px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1"
          title="Stop Project"
          aria-label="Stop project"
        >
          <Square className="w-4 h-4" aria-hidden="true" />
          <span>Stop</span>
        </button>
      )}
      {isRunning && (
        <span id="run-button-error" className="sr-only">Project is currently running</span>
      )}
    </div>
    <button
      onClick={handleDeploy}
      disabled={deploying}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
      title="Deploy Project (Ctrl/Cmd + D)"
      aria-label="Deploy project"
      aria-describedby={deploying ? 'deploy-button-error' : undefined}
    >
      {deploying ? <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" /> : deployStatus === 'success' ? <CheckCircle className="w-4 h-4 text-green-400" aria-hidden="true" /> : deployStatus === 'error' ? <XCircle className="w-4 h-4 text-red-400" aria-hidden="true" /> : <UploadCloud className="w-4 h-4" aria-hidden="true" />}
      Deploy
    </button>
    {deploying && (
      <span id="deploy-button-error" className="sr-only">Deployment in progress</span>
    )}
    <button
      onClick={handleSSLGenerate}
      disabled={sslGenerating}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
      title="Generate SSL Certificate"
      aria-label="Generate SSL certificate"
    >
      {sslGenerating ? <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" /> : <Shield className="w-4 h-4" aria-hidden="true" />}
      SSL
    </button>
    <button
      onClick={handleHealthCheck}
      disabled={maintenanceRunning}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:opacity-50 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1"
      title="Run Health Check"
      aria-label="Run health check"
    >
      {maintenanceRunning ? <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" /> : <Activity className="w-4 h-4" aria-hidden="true" />}
      Health
    </button>
    <button
      onClick={handleBackup}
      disabled={maintenanceRunning}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1"
      title="Create Backup"
      aria-label="Create backup"
    >
      {maintenanceRunning ? <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" /> : <Database className="w-4 h-4" aria-hidden="true" />}
      Backup
    </button>
    <button
      onClick={handlePerformanceAnalysis}
      disabled={performanceAnalyzing}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:opacity-50 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1"
      title="Performance Analysis"
      aria-label="Run performance analysis"
    >
      {performanceAnalyzing ? <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" /> : <Zap className="w-4 h-4" aria-hidden="true" />}
      Perf
    </button>
    <button
      onClick={handleRunTests}
      disabled={testingRunning}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1"
      title="Run All Tests (Ctrl/Cmd + T)"
      aria-label="Run all tests"
    >
      {testingRunning ? <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" /> : <TestTube className="w-4 h-4" aria-hidden="true" />}
      Tests
    </button>
    <button
      onClick={handleShowModelHealth}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1"
      title="AI Model Health"
      aria-label="Show AI model health"
    >
      <Brain className="w-4 h-4" aria-hidden="true" />
      AI Health
    </button>
    <button
      onClick={handleShowHelp}
      className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
      title="Help & Documentation"
      aria-label="Show help and documentation"
    >
      <HelpCircle className="w-4 h-4" aria-hidden="true" />
      Help
    </button>
  </div>
);
