import React, { useState, useEffect, memo, useCallback } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { ChatPanel } from './ChatPanel';
import { PreviewPanel } from './PreviewPanel';
import { Toolbar } from './Toolbar';
import { StatusBar } from './StatusBar';
import { CodeEditor, EditorFile } from './CodeEditor';
import { ModelHealthPanel } from './ModelHealthPanel';
import { DocumentationPanel } from './DocumentationPanel';
import { fileManager } from '@/services/FileManager';
import { ErrorDetectionPanel } from './ErrorDetectionPanel';
import { IDESidebarUnified } from './IDESidebarUnified';
import FileEditor from '@/frontend/components/FileEditor';
import LivePreview from '@/frontend/components/LivePreview';
import CommandRunner from '@/frontend/components/CommandRunner';
import GitVersionControl from '@/frontend/components/GitVersionControl';
import { useFileStore } from '@/store/fileStore';
import { LeftSidebarPanel } from './LeftSidebarPanel';
import { MainContentPanel } from './MainContentPanel';
import { RightPanel } from './RightPanel';
import { IDEStatusBar } from './IDEStatusBar';
import { useResponsive } from '@/hooks/useResponsive';
import toast from 'react-hot-toast';
import FileTree from './FileTree';

interface IDELayoutProps {
  className?: string;
}

type ActivePanel = 'editor' | 'preview' | 'commands' | 'git' | 'none';

export const IDELayout: React.FC<IDELayoutProps> = ({ className = '' }) => {
  const [currentProject, setCurrentProject] = useState<string>('Untitled Project');
  const [showEditor, setShowEditor] = useState(false);
  const [showModelHealth, setShowModelHealth] = useState(false);
  const [showDocumentationPanel, setShowDocumentationPanel] = useState(false);
  const [showErrorDetection, setShowErrorDetection] = useState(false);
  const [showBonusFeatures, setShowBonusFeatures] = useState(false);
  const [selectedSite, setSelectedSite] = useState<string | null>(null);
  const [activePanel, setActivePanel] = useState<ActivePanel>('none');
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);

  // Responsive hook
  const { isMobile, isTablet } = useResponsive();

  // Zustand file store
  const files = useFileStore(state => state.files);
  const activeFileId = useFileStore(state => state.activeFileId);
  const setActiveFile = useFileStore(state => state.setActiveFile);
  const updateFile = useFileStore(state => state.updateFile);
  const saveFile = useFileStore(state => state.saveFile);
  const createFile = useFileStore(state => state.createFile);
  const deleteFile = useFileStore(state => state.deleteFile);

  // Remove useEffect polling for files

  // Close mobile sidebar when screen size changes to desktop
  useEffect(() => {
    if (!isMobile && showMobileSidebar) {
      setShowMobileSidebar(false);
    }
  }, [isMobile, showMobileSidebar]);

  const handleFileSelect = useCallback((fileId: string) => {
    setActiveFile(fileId);
    setShowEditor(true);
  }, [setActiveFile, setShowEditor]);

  const handleFileChange = useCallback((fileId: string, content: string) => {
    updateFile(fileId, content);
  }, [updateFile]);

  const handleFileSave = useCallback((fileId: string) => {
    saveFile(fileId);
  }, [saveFile]);

  const handleFileClose = useCallback((fileId: string) => {
    // Deactivate the file
    if (activeFileId === fileId) {
      const nextFile = files.find(f => f.id !== fileId);
      if (nextFile) {
        setActiveFile(nextFile.id);
      } else {
        setShowEditor(false);
      }
    }
  }, [activeFileId, files, setActiveFile, setShowEditor]);

  const handleErrorSelect = useCallback((error: any) => {
    // Handle error selection - could open the file and highlight the error
    console.log('Selected error:', error);
    if (error.file_path) {
      // Open the file in the editor
      // This would integrate with the file manager
    }
  }, []);

  // IDESidebar handlers
  const handleSiteSelect = useCallback(async (siteName: string) => {
    console.log('Opening site in editor:', siteName);
    setSelectedSite(siteName);
    setCurrentProject(siteName);
    setActivePanel('none'); // Reset to default view

    // Load site files into the editor
    try {
      const response = await fetch(`/api/sites/${siteName}/files`);
      const result = await response.json();

      if (result.status === 'success' && result.files) {
        // Load files into the file store
        const fileStore = useFileStore.getState();
        const files = result.files.map((file: any) => ({
          id: file.id || `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name: file.name,
          path: file.path,
          content: file.content || '',
          language: file.language || '',
          isModified: false,
          isActive: false,
          type: file.type || 'file'
        }));
        fileStore.setFiles(files);
        toast.success(`Loaded ${files.length} files from ${siteName}`);
      }
    } catch (error) {
      console.error('Failed to load site files:', error);
      toast.error('Failed to load site files');
    }
  }, [setSelectedSite, setCurrentProject, setActivePanel]);

  const handleSitePreview = useCallback((siteName: string) => {
    setSelectedSite(siteName);
    setActivePanel('preview');
  }, [setSelectedSite, setActivePanel]);

  const handleSiteValidate = useCallback(async (siteName: string) => {
    try {
      console.log('Validating site:', siteName);
      const response = await fetch(`/api/sites/validate/${siteName}`, {
        method: 'POST'
      });
      const result = await response.json();
      console.log('Validation result:', result);

      // Show validation result in a toast or modal
      if (result.status === 'success') {
        // Show detailed validation results
        const validationDetails = result.validation || {};
        const issues = validationDetails.issues || [];
        const warnings = validationDetails.warnings || [];

        if (issues.length === 0 && warnings.length === 0) {
          toast.success('Site validated successfully - No issues found');
        } else {
          const issueCount = issues.length;
          const warningCount = warnings.length;
          toast.success(`Site validated - ${issueCount} issues, ${warningCount} warnings found`);

          // Show detailed results in console for now
          console.log('Validation Issues:', issues);
          console.log('Validation Warnings:', warnings);
        }
      } else {
        toast.error(`Validation failed: ${result.message || 'Unknown error'}`);
      }
    } catch (error) {
      toast.error('Validation failed.');
    }
  }, []);

  const handleSiteOpen = useCallback((siteName: string) => {
    setSelectedSite(siteName);
    setActivePanel('editor');
  }, [setSelectedSite, setActivePanel]);

  const handleSiteManifest = useCallback(async (siteName: string) => {
    try {
      console.log('Loading manifest for site:', siteName);
      const response = await fetch(`/api/sites/${siteName}/upload-manifest`);
      const result = await response.json();

      if (result.status === 'success') {
        // Show manifest in a modal or panel
        const manifest = result.manifest;

        // Create a detailed manifest summary
        const framework = manifest.framework_info?.framework || 'Unknown';
        const fileCount = manifest.file_count || 0;
        const securityStatus = manifest.security_report?.status || 'Unknown';
        const totalSize = manifest.total_size_mb || 0;

        // Show summary in toast
        toast.success(`Manifest loaded: ${framework} project with ${fileCount} files (${totalSize}MB)`);

        // Show detailed manifest in console for now
        console.log('=== Site Manifest ===');
        console.log('Framework:', framework);
        console.log('File count:', fileCount);
        console.log('Total size:', totalSize, 'MB');
        console.log('Security status:', securityStatus);
        console.log('Full manifest:', manifest);

        // Note: Future enhancement could include a modal with detailed manifest information
        // including file structure, dependencies, security report, etc.
      }
    } catch (error) {
      toast.error('Failed to load manifest.');
    }
  }, []);

  // New component handlers
  const handleOpenFileEditor = useCallback((siteName: string, filePath: string) => {
    setSelectedSite(siteName);
    setSelectedFile(filePath);
    setActivePanel('editor');
  }, [setSelectedSite, setSelectedFile, setActivePanel]);

  const handleOpenCommandRunner = useCallback((siteName: string) => {
    setSelectedSite(siteName);
    setActivePanel('commands');
  }, [setSelectedSite, setActivePanel]);

  const handleOpenGitControl = useCallback((siteName: string) => {
    setSelectedSite(siteName);
    setActivePanel('git');
  }, [setSelectedSite, setActivePanel]);

  const handleClosePanel = useCallback(() => {
    setActivePanel('none');
    setSelectedFile('');
  }, [setActivePanel, setSelectedFile]);

  // Render the active panel content
  const renderActivePanel = () => {
    if (!selectedSite) {
      return <PreviewPanel />;
    }

    switch (activePanel) {
      case 'editor':
        return selectedFile ? (
          <FileEditor
            siteName={selectedSite}
            filePath={selectedFile}
            onClose={handleClosePanel}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            Select a file to edit
          </div>
        );

      case 'preview':
        return (
          <LivePreview
            siteName={selectedSite}
            onClose={handleClosePanel}
          />
        );

      case 'commands':
        return (
          <CommandRunner
            siteName={selectedSite}
            onClose={handleClosePanel}
          />
        );

      case 'git':
        return (
          <GitVersionControl
            siteName={selectedSite}
            onClose={handleClosePanel}
          />
        );

      default:
        return <PreviewPanel />;
    }
  };

  return (
    <div className={`flex flex-col h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
      {/* Top Toolbar */}
      <Toolbar
        currentProject={currentProject}
        onProjectChange={setCurrentProject}
        onModelHealthToggle={setShowModelHealth}
        // Add Documentation toggle button to Toolbar
        documentationPanelToggle={() => setShowDocumentationPanel((prev) => !prev)}
        showDocumentationPanel={showDocumentationPanel}
        onToggleErrorDetection={() => setShowErrorDetection((prev) => !prev)}
        showErrorDetection={showErrorDetection}
        onToggleBonusFeatures={() => setShowBonusFeatures((prev) => !prev)}
        showBonusFeatures={showBonusFeatures}
        onMobileMenuToggle={() => setShowMobileSidebar((prev) => !prev)}
      />

      {/* Main IDE Content */}
      <div className="flex-1 flex overflow-hidden">
        <PanelGroup direction="horizontal" className="w-full h-full">
          {/* Left Panel - Unified Sidebar (25% default, responsive) */}
          <Panel defaultSize={25} minSize={15} maxSize={40} className="hidden md:block">
            <LeftSidebarPanel
              onSiteSelect={handleSiteSelect}
              onSitePreview={handleSitePreview}
              onSiteValidate={handleSiteValidate}
              onSiteOpen={handleSiteOpen}
              onSiteManifest={handleSiteManifest}
              onSiteCommands={handleOpenCommandRunner}
              onSiteGit={handleOpenGitControl}
            />
          </Panel>

                    {/* Mobile sidebar overlay */}
          <div
            className="md:hidden fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out"
            style={{ opacity: showMobileSidebar ? 1 : 0, pointerEvents: showMobileSidebar ? 'auto' : 'none' }}
            onClick={() => setShowMobileSidebar(false)}
          >
            <div
              className="fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 transform transition-transform duration-300 ease-in-out"
              style={{ transform: showMobileSidebar ? 'translateX(0)' : 'translateX(-100%)' }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Menu</h3>
                <button
                  onClick={() => setShowMobileSidebar(false)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                  aria-label="Close menu"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="flex-1 overflow-y-auto">
                <LeftSidebarPanel
                  onSiteSelect={handleSiteSelect}
                  onSitePreview={handleSitePreview}
                  onSiteValidate={handleSiteValidate}
                  onSiteOpen={handleSiteOpen}
                  onSiteManifest={handleSiteManifest}
                  onSiteCommands={handleOpenCommandRunner}
                  onSiteGit={handleOpenGitControl}
                />
              </div>
            </div>
          </div>

          <PanelResizeHandle className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors hidden md:block" />

          {/* Center Panel - Dynamic Content (responsive) */}
          <Panel defaultSize={50} minSize={30} className="w-full md:w-auto">
            <div className="h-full bg-white dark:bg-gray-800">
              <MainContentPanel
                selectedSite={selectedSite}
                activePanel={activePanel}
                selectedFile={selectedFile}
                handleClosePanel={handleClosePanel}
              />
            </div>
          </Panel>

          {/* Right Panel - Documentation/AI Chat/Model Health (25% default) */}
          <Panel defaultSize={25} minSize={20} maxSize={45}>
            <RightPanel
              showDocumentationPanel={showDocumentationPanel}
              showModelHealth={showModelHealth}
              showErrorDetection={showErrorDetection}
              showBonusFeatures={showBonusFeatures}
              handleErrorSelect={handleErrorSelect}
            />
          </Panel>
        </PanelGroup>
      </div>

      {/* Bottom Status Bar */}
      <IDEStatusBar />
    </div>
  );
};
