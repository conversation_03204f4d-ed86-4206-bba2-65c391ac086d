#!/usr/bin/env python3
"""
BackendAgent - Specialized agent for backend development tasks

Handles:
- API endpoint generation
- Database schema design
- Authentication and authorization
- Data validation and serialization
- Backend optimization
- API documentation
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.agents.enhanced_base_agent import EnhancedBaseAgent
from core.agents.enhanced_base_agent import TaskStatus as EnhancedTaskStatus
from core.agents.enhanced_base_agent import VerificationLevel
from core.code_generation.ast_code_generator import (
    ASTCodeGenerator,
    CodeLanguage,
    GenerationContext,
    GenerationStrategy,
)
from models.ollama_manager import OllamaModelManager
from utils.code_generator import CodeGenerator
from utils.database_schema_generator import DatabaseSchemaGenerator

logger = logging.getLogger(__name__)


class BackendAgent(EnhancedBaseAgent):
    """Specialized agent for backend development tasks"""

    def __init__(self, config_path: str = "config/backend_agent_config.json"):
        """Initialize the BackendAgent"""
        default_config = {
            "memory_tracking": {
                "enabled": True,
                "max_attempts_per_task": 3,
                "cooldown_seconds": 300,
                "verify_tasks": True,
                "reset_on_success": True,
                "verification_level": "comprehensive",
                "persistence": True,
                "cleanup_interval": 86400,
            },
            "model_settings": {
                "model_name": "deepseek-coder:1.3b",
                "system_prompt": "You are a backend AI assistant specialized in API development and database design.",
            },
            "framework": "fastapi",
            "database": "postgresql",
            "orm": "sqlalchemy",
            "authentication": "jwt",
            "validation": "pydantic",
            "documentation": "openapi",
            "testing": "pytest",
            "security": True,
            "rate_limiting": True,
            "caching": True,
        }
        super().__init__(config_path, default_config)

        self.code_generator = CodeGenerator()
        self.schema_generator = DatabaseSchemaGenerator()
        self.ast_generator = ASTCodeGenerator()

        logger.info(
            "BackendAgent initialized successfully with enhanced memory tracking"
        )

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description for backend agent

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        try:
            prompt = f"""
            {self.system_prompt}

            Parse the following backend development task and extract requirements:

            Task: {task_description}

            Extract:
            1. Required API endpoints
            2. Database requirements
            3. Authentication requirements
            4. Data models needed
            5. Integration requirements

            Return as JSON.
            """

            response_dict = await self.ollama_manager.generate_response(
                prompt, model_name=self.model_name
            )
            response = response_dict.get("response", "")

            try:
                # Try to parse JSON from response
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_str = response[json_start:json_end].strip()
                    requirements = json.loads(json_str)
                    requirements["parsed"] = True
                    return requirements
                else:
                    # Fallback parsing
                    requirements = self._fallback_parse_requirements(task_description)
                    requirements["parsed"] = True
                    return requirements
            except Exception as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                requirements = self._fallback_parse_requirements(task_description)
                requirements["parsed"] = True
                return requirements

        except Exception as e:
            logger.error(f"Error parsing backend task requirements: {e}")
            return {"description": task_description, "parsed": False, "error": str(e)}

    def _fallback_parse_requirements(self, task_description: str) -> Dict[str, Any]:
        """Fallback parsing for requirements"""
        requirements = {
            "endpoints": [],
            "database": "postgresql",
            "models": [],
            "authentication": "jwt",
            "integrations": [],
        }

        # Simple keyword-based parsing
        description_lower = task_description.lower()

        if "api" in description_lower:
            requirements["endpoints"].extend(["GET", "POST", "PUT", "DELETE"])

        if "user" in description_lower:
            requirements["models"].append("user")
            requirements["authentication"] = "jwt"

        if "database" in description_lower:
            if "postgres" in description_lower:
                requirements["database"] = "postgresql"
            elif "mysql" in description_lower:
                requirements["database"] = "mysql"
            elif "sqlite" in description_lower:
                requirements["database"] = "sqlite"

        return requirements

    async def _generate_api_endpoints(
        self, requirements: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate API endpoints based on requirements"""
        endpoints = []

        for endpoint in requirements.get("endpoints", []):
            try:
                endpoint_code = await self._generate_endpoint_code(
                    endpoint, requirements
                )
                endpoints.append(
                    {
                        "endpoint": endpoint,
                        "code": endpoint_code,
                        "type": "api_endpoint",
                    }
                )
            except Exception as e:
                logger.error(f"Error generating endpoint {endpoint}: {e}")

        return endpoints

    async def _generate_endpoint_code(
        self, endpoint: str, requirements: Dict[str, Any]
    ) -> str:
        """Generate endpoint code using AST-based generation"""
        try:
            # Create generation context
            context = GenerationContext(
                language=CodeLanguage.PYTHON,
                target_file=f"api/endpoints/{endpoint.lower()}.py",
                requirements=[
                    f"Create FastAPI endpoint: {endpoint}",
                    f"Framework: {self.config.get('framework', 'fastapi')}",
                    f"Database: {self.config.get('database', 'postgresql')}",
                    f"ORM: {self.config.get('orm', 'sqlalchemy')}",
                    f"Validation: {self.config.get('validation', 'pydantic')}",
                    f"Authentication: {self.config.get('authentication', 'jwt')}",
                    f"Security: {self.config.get('security', True)}",
                ]
                + [str(req) for req in requirements.get("additional_requirements", [])],
                constraints=[
                    "Use async/await",
                    "Include proper error handling",
                    "Add input validation",
                    "Include authentication/authorization",
                    "Add comprehensive documentation",
                    "Follow FastAPI best practices",
                ],
                conventions={
                    "naming": "snake_case",
                    "max_line_length": 88,
                    "import_style": "absolute",
                },
                imports=[
                    "from fastapi import APIRouter, HTTPException, Depends",
                    "from sqlalchemy.orm import Session",
                    "from typing import List, Optional",
                    "from pydantic import BaseModel",
                ],
            )

            # Generate code using AST generator
            result = await self.ast_generator.generate_code(
                context, GenerationStrategy.HYBRID
            )

            if result and result.code:
                return result.code
            else:
                # Fallback to traditional generation
                return await self._fallback_endpoint_generation(endpoint, requirements)

        except Exception as e:
            logger.warning(f"AST generation failed for endpoint {endpoint}: {e}")
            # Fallback to traditional generation
            return await self._fallback_endpoint_generation(endpoint, requirements)

    async def _fallback_endpoint_generation(
        self, endpoint: str, requirements: Dict[str, Any]
    ) -> str:
        """Fallback endpoint generation using traditional LLM approach"""
        prompt = f"""
        Generate a FastAPI endpoint for: {endpoint}

        Requirements:
        - Framework: {self.config['framework']}
        - Database: {self.config['database']}
        - ORM: {self.config['orm']}
        - Validation: {self.config['validation']}
        - Authentication: {self.config['authentication']}
        - Security: {self.config['security']}

        Additional context: {requirements}

        Generate a complete, production-ready endpoint with:
        1. Proper HTTP method and path
        2. Request/response models
        3. Database operations
        4. Error handling
        5. Authentication/authorization
        6. Input validation
        7. Documentation
        """

        response_dict = await self.ollama_manager.generate_response(
            prompt, model_name=self.model_name
        )
        return response_dict.get("response", "")

    async def _generate_database_schema(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate database schema based on requirements"""
        schema = {"tables": [], "relationships": [], "indexes": [], "migrations": []}

        for entity in requirements.get("models", []):
            try:
                table_schema = await self._generate_table_schema(entity, requirements)
                schema["tables"].append(table_schema)
            except Exception as e:
                logger.error(f"Error generating schema for {entity}: {e}")

        return schema

    async def _generate_table_schema(
        self, entity: str, requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate schema for a specific database table"""
        prompt = f"""
        Generate a database table schema for: {entity}

        Requirements:
        - Database: {self.config['database']}
        - ORM: {self.config['orm']}

        Additional context: {requirements}

        Generate a complete table schema with:
        1. Table name and columns
        2. Data types and constraints
        3. Primary and foreign keys
        4. Indexes
        5. SQLAlchemy model
        6. Migration script
        """

        response_dict = await self.ollama_manager.generate_response(
            prompt, model_name=self.model_name
        )
        response = response_dict.get("response", "")

        # Parse the response to extract structured data
        return {
            "entity": entity,
            "sqlalchemy_model": response,
            "migration": f"create_table_{entity}.py",
        }

    async def _generate_models(
        self, requirements: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate Pydantic models for request/response validation"""
        models = []

        for entity in requirements.get("models", []):
            try:
                model_code = await self._generate_model_code(entity, requirements)
                models.append(
                    {"entity": entity, "code": model_code, "type": "pydantic_model"}
                )
            except Exception as e:
                logger.error(f"Error generating model for {entity}: {e}")

        return models

    async def _generate_model_code(
        self, entity: str, requirements: Dict[str, Any]
    ) -> str:
        """Generate Pydantic model code for a specific entity"""
        prompt = f"""
        Generate Pydantic models for: {entity}

        Requirements:
        - Validation: {self.config['validation']}
        - Framework: {self.config['framework']}

        Generate:
        1. Base model for the entity
        2. Create model (for POST requests)
        3. Update model (for PUT requests)
        4. Response model (for GET requests)
        5. List response model

        Include proper validation rules and documentation.
        """

        response_dict = await self.ollama_manager.generate_response(
            prompt, model_name=self.model_name
        )
        return response_dict.get("response", "")

    async def _generate_auth_system(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate authentication system"""
        auth_type = requirements.get("authentication", self.config["authentication"])

        if auth_type == "jwt":
            return await self._generate_jwt_auth()
        elif auth_type == "oauth":
            return await self._generate_oauth_auth()
        else:
            return await self._generate_basic_auth()

    async def _generate_jwt_auth(self) -> Dict[str, Any]:
        """Generate JWT authentication system"""
        prompt = """
        Generate a complete JWT authentication system for FastAPI with:

        1. User model with password hashing
        2. JWT token generation and validation
        3. Login endpoint
        4. Register endpoint
        5. Logout endpoint
        6. Password reset functionality
        7. Role-based access control
        8. Security middleware

        Include proper error handling and security best practices.
        """

        response_dict = await self.ollama_manager.generate_response(
            prompt, model_name=self.model_name
        )
        response = response_dict.get("response", "")

        return {
            "type": "jwt",
            "code": response,
            "endpoints": [
                "POST /api/auth/login",
                "POST /api/auth/register",
                "POST /api/auth/logout",
                "POST /api/auth/refresh",
                "POST /api/auth/reset-password",
            ],
        }

    async def _generate_oauth_auth(self) -> Dict[str, Any]:
        """Generate OAuth authentication system"""
        return {
            "type": "oauth",
            "code": "# OAuth implementation placeholder",
            "endpoints": [
                "GET /api/auth/oauth/{provider}",
                "GET /api/auth/oauth/{provider}/callback",
            ],
        }

    async def _generate_basic_auth(self) -> Dict[str, Any]:
        """Generate basic authentication system"""
        return {
            "type": "basic",
            "code": "# Basic auth implementation placeholder",
            "endpoints": ["POST /api/auth/login"],
        }

    async def _generate_config_files(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, str]:
        """Generate configuration files for the backend project"""
        config_files = {}

        # requirements.txt
        config_files["requirements.txt"] = self._generate_requirements_txt(requirements)

        # main.py
        config_files["main.py"] = self._generate_main_py(requirements)

        # database.py
        config_files["database.py"] = self._generate_database_py(requirements)

        # config.py
        config_files["config.py"] = self._generate_config_py(requirements)

        return config_files

    def _generate_requirements_txt(self, requirements: Dict[str, Any]) -> str:
        """Generate requirements.txt content"""
        dependencies = [
            "fastapi==0.116.1",
            "uvicorn==0.35.0",
            "sqlalchemy==2.0.41",
            "pydantic==2.10.4",
            "python-jose==3.4.0",
            "passlib==1.7.4",
            "python-multipart==0.0.18",
            "alembic==1.13.1",
        ]

        if self.config["database"] == "postgresql":
            dependencies.append("psycopg2-binary==2.9.10")
        elif self.config["database"] == "mysql":
            dependencies.append("mysqlclient==2.2.0")

        if self.config["caching"]:
            dependencies.append("redis==5.0.1")

        if self.config["testing"] == "pytest":
            dependencies.extend(
                ["pytest==8.0.0", "pytest-asyncio==0.23.5", "httpx==0.27.0"]
            )

        return "\n".join(dependencies)

    def _generate_main_py(self, requirements: Dict[str, Any]) -> str:
        """Generate main.py content"""
        return """
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from database import engine, Base
from routers import auth, projects, posts, contact

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Backend API",
    description="Generated backend API",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(projects.router, prefix="/api/projects", tags=["Projects"])
app.include_router(posts.router, prefix="/api/posts", tags=["Posts"])
app.include_router(contact.router, prefix="/api/contact", tags=["Contact"])

@app.get("/")
async def root():
    return {"message": "Backend API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
"""

    def _generate_database_py(self, requirements: Dict[str, Any]) -> str:
        """Generate database.py content"""
        return """
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config import settings

SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL

engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
"""

    def _generate_config_py(self, requirements: Dict[str, Any]) -> str:
        """Generate config.py content"""
        return """
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    DATABASE_URL: str = "postgresql://user:password@localhost/dbname"
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    class Config:
        env_file = ".env"

settings = Settings()
"""

    async def _create_docker_config(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, str]:
        """Create Docker configuration for the backend"""
        docker_files = {}

        # Dockerfile
        docker_files[
            "Dockerfile"
        ] = """
# Backend Dockerfile
FROM python:3.11-slim AS builder

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim AS production

WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

COPY . .

RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
"""

        # .dockerignore
        docker_files[
            ".dockerignore"
        ] = """
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis
"""

        return docker_files

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation for backend agent

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        try:
            if not requirements.get("parsed", False):
                return {
                    "success": False,
                    "error": "Failed to parse task requirements",
                    "task_id": task_id,
                }

            logger.info(
                f"Executing backend task: {requirements.get('description', task_id)}"
            )

            # Execute backend tasks based on requirements
            results = {}

            # Generate API endpoints
            if requirements.get("api_endpoints"):
                results["api_endpoints"] = await self._generate_api_endpoints(
                    requirements
                )

            # Generate database schema
            if requirements.get("database_requirements"):
                results["database_schema"] = await self._generate_database_schema(
                    requirements
                )

            # Generate data models
            if requirements.get("data_models"):
                results["data_models"] = await self._generate_models(requirements)

            # Generate authentication system
            if requirements.get("authentication_requirements"):
                results["authentication"] = await self._generate_auth_system(
                    requirements
                )

            # Generate configuration files
            results["config_files"] = await self._generate_config_files(requirements)

            # Generate Docker configuration
            results["docker_config"] = await self._create_docker_config(requirements)

            # Determine overall success
            all_successful = all(
                result.get("success", False) if isinstance(result, dict) else True
                for result in results.values()
            )

            return {
                "success": all_successful,
                "task_id": task_id,
                "results": results,
                "requirements": requirements,
            }

        except Exception as e:
            logger.error(f"Error executing backend task {task_id}: {e}")
            return {"success": False, "error": str(e), "task_id": task_id}

    async def _verify_task_specific(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Backend-specific task verification

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        try:
            # Check if task was successful
            if not result.get("success", False):
                return False

            # Check if all backend components were generated successfully
            results = result.get("results", {})
            if not results:
                return False

            # Verify each backend component
            for component, component_result in results.items():
                if isinstance(component_result, dict) and not component_result.get(
                    "success", True
                ):
                    logger.warning(f"Backend component {component} failed verification")
                    return False

            # Additional verification for backend-specific tasks
            if (
                "api" in task_description.lower()
                or "backend" in task_description.lower()
            ):
                # Check if API endpoints were generated
                if "api_endpoints" not in results:
                    logger.warning("API endpoints not generated")
                    return False

                # Check if database schema was generated
                if "database_schema" not in results:
                    logger.warning("Database schema not generated")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error during backend task verification: {e}")
            return False
