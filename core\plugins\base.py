"""
Base Plugin Classes and Plugin Management System
Provides the foundation for the extensible plugin architecture.
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Protocol, Set, Type

logger = logging.getLogger(__name__)


class PluginPriority(Enum):
    """Plugin execution priority levels"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class PluginConfig:
    """Plugin configuration"""

    name: str
    version: str
    description: str
    author: str
    priority: PluginPriority = PluginPriority.NORMAL
    enabled: bool = True
    dependencies: List[str] = field(default_factory=list)
    settings: Dict[str, Any] = field(default_factory=dict)


class PluginBase(ABC):
    """Base class for all plugins"""

    def __init__(self, config: PluginConfig):
        self.config = config
        self.logger = logging.getLogger(f"plugin.{config.name}")
        self._initialized = False
        self._error_count = 0
        self._last_error: Optional[str] = None

    @property
    def name(self) -> str:
        """Plugin name"""
        return self.config.name

    @property
    def version(self) -> str:
        """Plugin version"""
        return self.config.version

    @property
    def enabled(self) -> bool:
        """Whether plugin is enabled"""
        return self.config.enabled

    @property
    def priority(self) -> PluginPriority:
        """Plugin priority"""
        return self.config.priority

    async def initialize(self) -> bool:
        """Initialize the plugin"""
        try:
            if self._initialized:
                return True

            await self._initialize_plugin()
            self._initialized = True
            self.logger.info(f"Plugin {self.name} initialized successfully")
            return True

        except Exception as e:
            self._error_count += 1
            self._last_error = str(e)
            self.logger.error(f"Failed to initialize plugin {self.name}: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup plugin resources"""
        try:
            await self._cleanup_plugin()
            self._initialized = False
            self.logger.info(f"Plugin {self.name} cleaned up successfully")
        except Exception as e:
            self.logger.error(f"Error cleaning up plugin {self.name}: {e}")

    async def pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Called before website build starts"""
        try:
            if not self.enabled or not self._initialized:
                return {}

            result = await self._pre_build(site_config, site_dir)
            self.logger.debug(f"Plugin {self.name} pre_build completed")
            return result

        except Exception as e:
            self._error_count += 1
            self._last_error = str(e)
            self.logger.error(f"Plugin {self.name} pre_build failed: {e}")
            return {"error": str(e)}

    async def post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Called after website build completes"""
        try:
            if not self.enabled or not self._initialized:
                return {}

            result = await self._post_build(site_config, site_dir)
            self.logger.debug(f"Plugin {self.name} post_build completed")
            return result

        except Exception as e:
            self._error_count += 1
            self._last_error = str(e)
            self.logger.error(f"Plugin {self.name} post_build failed: {e}")
            return {"error": str(e)}

    async def on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Called when an error occurs during build"""
        try:
            if not self.enabled or not self._initialized:
                return

            await self._on_error(error, site_config)
            self.logger.debug(f"Plugin {self.name} on_error completed")

        except Exception as e:
            self._error_count += 1
            self._last_error = str(e)
            self.logger.error(f"Plugin {self.name} on_error failed: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get plugin status"""
        return {
            "name": self.name,
            "version": self.version,
            "enabled": self.enabled,
            "initialized": self._initialized,
            "priority": self.priority.value,
            "error_count": self._error_count,
            "last_error": self._last_error,
        }

    @abstractmethod
    async def _initialize_plugin(self) -> None:
        """Initialize plugin-specific resources"""
        pass

    @abstractmethod
    async def _cleanup_plugin(self) -> None:
        """Cleanup plugin-specific resources"""
        pass

    @abstractmethod
    async def _pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Plugin-specific pre-build logic"""
        pass

    @abstractmethod
    async def _post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Plugin-specific post-build logic"""
        pass

    @abstractmethod
    async def _on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Plugin-specific error handling"""
        pass


class PluginRegistry:
    """Plugin registry for managing available plugins"""

    def __init__(self):
        self._plugins: Dict[str, Type[PluginBase]] = {}
        self._configs: Dict[str, PluginConfig] = {}

    def register(self, plugin_class: Type[PluginBase], config: PluginConfig) -> None:
        """Register a plugin class"""
        self._plugins[config.name] = plugin_class
        self._configs[config.name] = config
        logger.info(f"Registered plugin: {config.name} v{config.version}")

    def unregister(self, plugin_name: str) -> None:
        """Unregister a plugin"""
        if plugin_name in self._plugins:
            del self._plugins[plugin_name]
            del self._configs[plugin_name]
            logger.info(f"Unregistered plugin: {plugin_name}")

    def get_plugin_class(self, plugin_name: str) -> Optional[Type[PluginBase]]:
        """Get plugin class by name"""
        return self._plugins.get(plugin_name)

    def get_config(self, plugin_name: str) -> Optional[PluginConfig]:
        """Get plugin config by name"""
        return self._configs.get(plugin_name)

    def list_plugins(self) -> List[str]:
        """List all registered plugin names"""
        return list(self._plugins.keys())

    def get_plugin_info(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """Get plugin information"""
        config = self.get_config(plugin_name)
        if not config:
            return None

        return {
            "name": config.name,
            "version": config.version,
            "description": config.description,
            "author": config.author,
            "priority": config.priority.value,
            "enabled": config.enabled,
            "dependencies": config.dependencies,
            "settings": config.settings,
        }


class PluginManager:
    """Manages plugin lifecycle and execution"""

    def __init__(self, registry: Optional[PluginRegistry] = None):
        self.registry = registry or PluginRegistry()
        self.active_plugins: Dict[str, PluginBase] = {}
        self.plugin_order: List[str] = []
        self._initialized = False

    async def initialize(self) -> bool:
        """Initialize the plugin manager"""
        try:
            if self._initialized:
                return True

            # Load and initialize all enabled plugins
            for plugin_name in self.registry.list_plugins():
                config = self.registry.get_config(plugin_name)
                if config and config.enabled:
                    await self._load_plugin(plugin_name, config)

            # Sort plugins by priority
            self._sort_plugins()

            self._initialized = True
            logger.info(
                f"Plugin manager initialized with {len(self.active_plugins)} plugins"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to initialize plugin manager: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup all plugins"""
        try:
            for plugin in self.active_plugins.values():
                await plugin.cleanup()

            self.active_plugins.clear()
            self.plugin_order.clear()
            self._initialized = False
            logger.info("Plugin manager cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up plugin manager: {e}")

    async def run_pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Run pre-build hooks for all plugins"""
        results = {}

        for plugin_name in self.plugin_order:
            plugin = self.active_plugins.get(plugin_name)
            if plugin and plugin.enabled:
                try:
                    result = await plugin.pre_build(site_config, site_dir)
                    results[plugin_name] = result
                except Exception as e:
                    logger.error(f"Plugin {plugin_name} pre_build failed: {e}")
                    results[plugin_name] = {"error": str(e)}

        return results

    async def run_post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Run post-build hooks for all plugins"""
        results = {}

        for plugin_name in self.plugin_order:
            plugin = self.active_plugins.get(plugin_name)
            if plugin and plugin.enabled:
                try:
                    result = await plugin.post_build(site_config, site_dir)
                    results[plugin_name] = result
                except Exception as e:
                    logger.error(f"Plugin {plugin_name} post_build failed: {e}")
                    results[plugin_name] = {"error": str(e)}

        return results

    async def run_on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Run error hooks for all plugins"""
        for plugin_name in self.plugin_order:
            plugin = self.active_plugins.get(plugin_name)
            if plugin and plugin.enabled:
                try:
                    await plugin.on_error(error, site_config)
                except Exception as e:
                    logger.error(f"Plugin {plugin_name} on_error failed: {e}")

    def get_plugin_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all plugins"""
        return {
            name: plugin.get_status() for name, plugin in self.active_plugins.items()
        }

    async def _load_plugin(self, plugin_name: str, config: PluginConfig) -> bool:
        """Load and initialize a plugin"""
        try:
            # Check dependencies
            if not await self._check_dependencies(config.dependencies):
                logger.warning(f"Plugin {plugin_name} dependencies not met")
                return False

            # Create plugin instance
            plugin_class = self.registry.get_plugin_class(plugin_name)
            if not plugin_class:
                logger.error(f"Plugin class not found: {plugin_name}")
                return False

            plugin = plugin_class(config)

            # Initialize plugin
            if await plugin.initialize():
                self.active_plugins[plugin_name] = plugin
                return True
            else:
                logger.error(f"Failed to initialize plugin: {plugin_name}")
                return False

        except Exception as e:
            logger.error(f"Error loading plugin {plugin_name}: {e}")
            return False

    async def _check_dependencies(self, dependencies: List[str]) -> bool:
        """Check if plugin dependencies are satisfied"""
        for dep in dependencies:
            if dep not in self.active_plugins:
                return False
        return True

    def _sort_plugins(self) -> None:
        """Sort plugins by priority (highest first)"""
        self.plugin_order = sorted(
            self.active_plugins.keys(),
            key=lambda name: self.active_plugins[name].priority.value,
            reverse=True,
        )


# Global plugin registry instance
plugin_registry = PluginRegistry()
