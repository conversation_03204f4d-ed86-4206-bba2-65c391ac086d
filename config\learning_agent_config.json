{"agent_name": "LearningAgent", "version": "1.0.0", "description": "Specialized agent for learning and model training tasks", "model_settings": {"primary_models": ["deepseek-coder:1.3b", "yi-coder:1.5b", "qwen2.5-coder:3b", "starcoder2:3b", "mistral:7b-instruct-q4_0"], "model_switching": true, "fallback_model": "mistral:7b-instruct-q4_0", "performance_tracking": true, "model_optimization": true}, "training_settings": {"fine_tuning_enabled": true, "training_data_collection": true, "data_preprocessing": true, "model_evaluation": true, "hyperparameter_optimization": true, "automated_training": true}, "learning_systems": {"automated_learner": {"enabled": true, "config_file": "config/automated_learner_config.json", "learning_rate": 0.001, "batch_size": 32, "epochs": 10}, "best_practices_learner": {"enabled": true, "config_file": "config/best_practices_config.json", "pattern_recognition": true, "rule_extraction": true, "knowledge_base": true}, "framework_monitor": {"enabled": true, "config_file": "config/framework_monitor_config.json", "trend_analysis": true, "update_detection": true, "recommendation_engine": true}}, "data_management": {"data_collection": {"enabled": true, "sources": ["user_interactions", "code_reviews", "performance_metrics"], "storage": "data/learning", "backup_strategy": "automated", "data_validation": true}, "data_processing": {"cleaning": true, "normalization": true, "augmentation": true, "feature_extraction": true, "quality_assurance": true}}, "performance_monitoring": {"model_performance": {"accuracy_tracking": true, "response_time_monitoring": true, "error_rate_analysis": true, "user_satisfaction": true, "resource_usage": true}, "learning_metrics": {"improvement_tracking": true, "knowledge_gap_analysis": true, "adaptation_rate": true, "retention_analysis": true}}, "optimization": {"model_optimization": {"quantization": true, "pruning": true, "distillation": true, "compression": true}, "resource_optimization": {"memory_usage": true, "cpu_utilization": true, "gpu_optimization": true, "network_efficiency": true}}, "integration": {"ollama_integration": {"enabled": true, "config_file": "config/ollama_config.json", "model_management": true, "performance_tracking": true}, "api_integration": {"endpoints": true, "webhooks": true, "real_time_updates": true, "status_monitoring": true}}, "project_structure": {"data_directory": "data/learning", "models_directory": "models", "training_directory": "training", "evaluation_directory": "evaluation", "logs_directory": "logs/learning"}, "dependencies": {"core_dependencies": ["torch", "transformers", "datasets", "accelerate"], "monitoring_dependencies": ["wandb", "tensorboard", "mlflow"], "optimization_dependencies": ["optuna", "hyperopt", "ray[tune]"]}, "logging": {"level": "INFO", "format": "json", "file_logging": true, "console_logging": true, "log_rotation": true, "structured_logging": true}, "development": {"debug_mode": false, "development_features": true, "test_mode": false, "experimental_features": false}}