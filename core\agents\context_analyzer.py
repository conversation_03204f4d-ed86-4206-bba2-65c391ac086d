#!/usr/bin/env python3
"""
ContextA<PERSON>yzer - Analyzes task complexity and context for intelligent routing

This module provides comprehensive task analysis including:
1. Task complexity scoring
2. Requirement analysis
3. Constraint identification
4. Dependency mapping
5. Resource requirements estimation
6. Risk assessment
"""

import asyncio
import json
import logging
import re
import statistics
import time
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from models.ollama_manager import OllamaModelManager

logger = logging.getLogger(__name__)


class ComplexityLevel(Enum):
    """Task complexity levels"""

    TRIVIAL = "trivial"
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"
    EXTREME = "extreme"


class TaskCategory(Enum):
    """Task categories"""

    FRONTEND = "frontend"
    BACKEND = "backend"
    DATABASE = "database"
    INFRASTRUCTURE = "infrastructure"
    SECURITY = "security"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    INTEGRATION = "integration"
    OPTIMIZATION = "optimization"
    MAINTENANCE = "maintenance"
    GENERAL = "general"


@dataclass
class ComplexityFactors:
    """Factors that contribute to task complexity"""

    lines_of_code_estimate: int = 0
    number_of_files: int = 0
    dependencies_count: int = 0
    integrations_count: int = 0
    security_requirements: int = 0
    testing_requirements: int = 0
    documentation_requirements: int = 0
    performance_requirements: int = 0
    scalability_requirements: int = 0
    time_constraints: int = 0
    skill_requirements: int = 0


@dataclass
class TaskAnalysis:
    """Comprehensive task analysis result"""

    task_id: str
    complexity_score: float
    complexity_level: ComplexityLevel
    category: TaskCategory
    estimated_duration: int  # minutes
    risk_level: str
    skill_requirements: List[str]
    dependencies: List[str]
    constraints: List[str]
    resource_requirements: Dict[str, Any]
    analysis_confidence: float
    factors: ComplexityFactors
    created_at: datetime = field(default_factory=datetime.now)


class ContextAnalyzer:
    """
    Analyzes task context and complexity for intelligent routing decisions.
    """

    def __init__(self, config_path: str = "config/context_analyzer_config.json"):
        self.config = self._load_config(config_path)
        self.ollama_manager = OllamaModelManager()

        # Complexity patterns and keywords
        self.complexity_patterns = self._load_complexity_patterns()
        self.category_keywords = self._load_category_keywords()

        logger.info("ContextAnalyzer initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded context analyzer config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "analysis": {
                "enabled": True,
                "ai_analysis": True,
                "confidence_threshold": 0.7,
                "max_analysis_time": 30,
            },
            "complexity": {
                "factors": [
                    "lines_of_code",
                    "dependencies",
                    "integrations",
                    "security_requirements",
                    "testing_requirements",
                    "documentation_requirements",
                    "performance_requirements",
                    "scalability_requirements",
                    "time_constraints",
                    "skill_requirements",
                ],
                "weights": {
                    "lines_of_code": 0.15,
                    "dependencies": 0.12,
                    "integrations": 0.12,
                    "security_requirements": 0.10,
                    "testing_requirements": 0.08,
                    "documentation_requirements": 0.05,
                    "performance_requirements": 0.08,
                    "scalability_requirements": 0.08,
                    "time_constraints": 0.07,
                    "skill_requirements": 0.15,
                },
            },
            "categories": {
                "frontend": [
                    "ui",
                    "ux",
                    "react",
                    "vue",
                    "angular",
                    "css",
                    "html",
                    "javascript",
                ],
                "backend": [
                    "api",
                    "server",
                    "database",
                    "authentication",
                    "authorization",
                ],
                "database": ["sql", "nosql", "migration", "schema", "query", "index"],
                "infrastructure": [
                    "docker",
                    "kubernetes",
                    "deployment",
                    "ci/cd",
                    "monitoring",
                ],
                "security": [
                    "security",
                    "vulnerability",
                    "encryption",
                    "authentication",
                    "authorization",
                ],
                "testing": ["test", "unit", "integration", "e2e", "coverage"],
                "documentation": ["docs", "readme", "api", "user", "technical"],
                "integration": ["api", "third-party", "webhook", "oauth", "sso"],
                "optimization": [
                    "performance",
                    "speed",
                    "efficiency",
                    "optimization",
                    "caching",
                ],
                "maintenance": ["bug", "fix", "update", "upgrade", "refactor"],
            },
        }

    def _load_complexity_patterns(self) -> Dict[str, List[str]]:
        """Load complexity patterns and keywords"""
        return {
            "trivial": [
                "simple",
                "basic",
                "minor",
                "quick",
                "small",
                "easy",
                "straightforward",
            ],
            "simple": ["standard", "routine", "common", "typical", "basic", "simple"],
            "moderate": ["medium", "moderate", "standard", "typical", "common"],
            "complex": [
                "complex",
                "advanced",
                "sophisticated",
                "intricate",
                "detailed",
            ],
            "very_complex": [
                "very complex",
                "highly sophisticated",
                "advanced",
                "intricate",
                "detailed",
            ],
            "extreme": [
                "extreme",
                "critical",
                "mission-critical",
                "high-risk",
                "complex system",
            ],
        }

    def _load_category_keywords(self) -> Dict[str, List[str]]:
        """Load category keywords"""
        return self.config.get("categories", {})

    async def analyze_complexity(self, task_context) -> float:
        """
        Analyze task complexity and return a score between 0.0 and 1.0
        """
        try:
            # Extract complexity factors
            factors = await self._extract_complexity_factors(task_context)

            # Calculate weighted complexity score
            complexity_score = self._calculate_complexity_score(factors)

            # Apply AI analysis if enabled
            if self.config.get("analysis", {}).get("ai_analysis", True):
                ai_score = await self._ai_complexity_analysis(task_context)
                if ai_score is not None:
                    # Combine rule-based and AI scores
                    complexity_score = (complexity_score * 0.6) + (ai_score * 0.4)

            return max(0.0, min(1.0, complexity_score))

        except Exception as e:
            logger.error(f"Failed to analyze complexity: {e}")
            return 0.5  # Default moderate complexity

    async def analyze_task(self, task_context) -> TaskAnalysis:
        """
        Perform comprehensive task analysis
        """
        try:
            # Analyze complexity
            complexity_score = await self.analyze_complexity(task_context)
            complexity_level = self._map_complexity_level(complexity_score)

            # Analyze category
            category = await self._analyze_category(task_context)

            # Estimate duration
            estimated_duration = await self._estimate_duration(
                task_context, complexity_score
            )

            # Analyze risk
            risk_level = await self._analyze_risk(task_context, complexity_score)

            # Extract requirements
            skill_requirements = await self._extract_skill_requirements(task_context)
            dependencies = await self._extract_dependencies(task_context)
            constraints = await self._extract_constraints(task_context)

            # Analyze resource requirements
            resource_requirements = await self._analyze_resource_requirements(
                task_context, complexity_score
            )

            # Calculate analysis confidence
            analysis_confidence = self._calculate_analysis_confidence(task_context)

            # Create complexity factors
            factors = await self._extract_complexity_factors(task_context)

            return TaskAnalysis(
                task_id=task_context.task_id,
                complexity_score=complexity_score,
                complexity_level=complexity_level,
                category=category,
                estimated_duration=estimated_duration,
                risk_level=risk_level,
                skill_requirements=skill_requirements,
                dependencies=dependencies,
                constraints=constraints,
                resource_requirements=resource_requirements,
                analysis_confidence=analysis_confidence,
                factors=factors,
            )

        except Exception as e:
            logger.error(f"Failed to analyze task: {e}")
            return self._create_default_analysis(task_context)

    async def _extract_complexity_factors(self, task_context) -> ComplexityFactors:
        """Extract complexity factors from task context"""
        try:
            factors = ComplexityFactors()

            # Analyze text content for complexity indicators
            text_content = f"{task_context.task_type} {' '.join(task_context.requirements)} {' '.join(task_context.constraints)}"
            text_lower = text_content.lower()

            # Lines of code estimate
            factors.lines_of_code_estimate = self._estimate_lines_of_code(text_content)

            # Number of files
            factors.number_of_files = self._estimate_number_of_files(text_content)

            # Dependencies count
            factors.dependencies_count = self._count_dependencies(text_content)

            # Integrations count
            factors.integrations_count = self._count_integrations(text_content)

            # Security requirements
            factors.security_requirements = self._count_security_requirements(
                text_lower
            )

            # Testing requirements
            factors.testing_requirements = self._count_testing_requirements(text_lower)

            # Documentation requirements
            factors.documentation_requirements = self._count_documentation_requirements(
                text_lower
            )

            # Performance requirements
            factors.performance_requirements = self._count_performance_requirements(
                text_lower
            )

            # Scalability requirements
            factors.scalability_requirements = self._count_scalability_requirements(
                text_lower
            )

            # Time constraints
            factors.time_constraints = self._count_time_constraints(text_lower)

            # Skill requirements
            factors.skill_requirements = self._count_skill_requirements(text_lower)

            return factors

        except Exception as e:
            logger.error(f"Failed to extract complexity factors: {e}")
            return ComplexityFactors()

    def _calculate_complexity_score(self, factors: ComplexityFactors) -> float:
        """Calculate weighted complexity score"""
        try:
            weights = self.config.get("complexity", {}).get("weights", {})

            # Normalize factors to 0-1 scale
            normalized_factors = {
                "lines_of_code": min(factors.lines_of_code_estimate / 1000, 1.0),
                "dependencies": min(factors.dependencies_count / 10, 1.0),
                "integrations": min(factors.integrations_count / 5, 1.0),
                "security_requirements": min(factors.security_requirements / 3, 1.0),
                "testing_requirements": min(factors.testing_requirements / 3, 1.0),
                "documentation_requirements": min(
                    factors.documentation_requirements / 2, 1.0
                ),
                "performance_requirements": min(
                    factors.performance_requirements / 2, 1.0
                ),
                "scalability_requirements": min(
                    factors.scalability_requirements / 2, 1.0
                ),
                "time_constraints": min(factors.time_constraints / 2, 1.0),
                "skill_requirements": min(factors.skill_requirements / 5, 1.0),
            }

            # Calculate weighted score
            total_score = 0.0
            total_weight = 0.0

            for factor, weight in weights.items():
                if factor in normalized_factors:
                    total_score += normalized_factors[factor] * weight
                    total_weight += weight

            if total_weight > 0:
                return total_score / total_weight
            else:
                return 0.5

        except Exception as e:
            logger.error(f"Failed to calculate complexity score: {e}")
            return 0.5

    async def _ai_complexity_analysis(self, task_context) -> Optional[float]:
        """Use AI to analyze task complexity"""
        try:
            prompt = f"""
Analyze the complexity of the following task and provide a complexity score between 0.0 and 1.0.

Task Type: {task_context.task_type}
Requirements: {', '.join(task_context.requirements)}
Constraints: {', '.join(task_context.constraints)}
Priority: {task_context.priority}

Consider the following factors:
1. Technical complexity (number of components, integrations, dependencies)
2. Skill requirements (specialized knowledge needed)
3. Time requirements (estimated duration)
4. Risk factors (potential issues, dependencies)
5. Resource requirements (tools, infrastructure, team size)

Provide only a single number between 0.0 and 1.0 representing the overall complexity score.
0.0 = Trivial task (simple, quick, basic)
0.2 = Simple task (routine, standard)
0.4 = Moderate task (some complexity, standard approach)
0.6 = Complex task (advanced, multiple components)
0.8 = Very complex task (sophisticated, high risk)
1.0 = Extreme task (mission-critical, highly complex system)

Complexity score:
"""

            response = await self.ollama_manager.generate_response(
                model_name="qwen2.5-coder:3b",
                prompt=prompt,
                max_tokens=50,
                temperature=0.1,
            )

            # Extract numeric score from response
            score_match = re.search(r"0\.\d+|1\.0", response.strip())
            if score_match:
                return float(score_match.group())

            return None

        except Exception as e:
            logger.error(f"AI complexity analysis failed: {e}")
            return None

    def _map_complexity_level(self, complexity_score: float) -> ComplexityLevel:
        """Map complexity score to complexity level"""
        if complexity_score < 0.1:
            return ComplexityLevel.TRIVIAL
        elif complexity_score < 0.3:
            return ComplexityLevel.SIMPLE
        elif complexity_score < 0.5:
            return ComplexityLevel.MODERATE
        elif complexity_score < 0.7:
            return ComplexityLevel.COMPLEX
        elif complexity_score < 0.9:
            return ComplexityLevel.VERY_COMPLEX
        else:
            return ComplexityLevel.EXTREME

    async def _analyze_category(self, task_context) -> TaskCategory:
        """Analyze task category based on content"""
        try:
            text_content = f"{task_context.task_type} {' '.join(task_context.requirements)} {' '.join(task_context.constraints)}"
            text_lower = text_content.lower()

            # Count keyword matches for each category
            category_scores = {}
            for category, keywords in self.category_keywords.items():
                score = sum(1 for keyword in keywords if keyword.lower() in text_lower)
                category_scores[category] = score

            # Find category with highest score
            if category_scores:
                best_category = max(category_scores.items(), key=lambda x: x[1])
                if best_category[1] > 0:
                    return TaskCategory(best_category[0])

            return TaskCategory.GENERAL

        except Exception as e:
            logger.error(f"Failed to analyze category: {e}")
            return TaskCategory.GENERAL

    async def _estimate_duration(self, task_context, complexity_score: float) -> int:
        """Estimate task duration in minutes"""
        try:
            # Base duration based on complexity
            base_duration = {
                ComplexityLevel.TRIVIAL: 15,
                ComplexityLevel.SIMPLE: 60,
                ComplexityLevel.MODERATE: 240,
                ComplexityLevel.COMPLEX: 480,
                ComplexityLevel.VERY_COMPLEX: 960,
                ComplexityLevel.EXTREME: 1920,
            }

            complexity_level = self._map_complexity_level(complexity_score)
            duration = base_duration.get(complexity_level, 240)

            # Adjust based on requirements count
            requirements_count = len(task_context.requirements)
            if requirements_count > 5:
                duration *= 1.5
            elif requirements_count > 10:
                duration *= 2.0

            # Adjust based on constraints
            constraints_count = len(task_context.constraints)
            if constraints_count > 3:
                duration *= 1.3

            return int(duration)

        except Exception as e:
            logger.error(f"Failed to estimate duration: {e}")
            return 240  # Default 4 hours

    async def _analyze_risk(self, task_context, complexity_score: float) -> str:
        """Analyze task risk level"""
        try:
            if complexity_score < 0.3:
                return "low"
            elif complexity_score < 0.6:
                return "medium"
            elif complexity_score < 0.8:
                return "high"
            else:
                return "critical"

        except Exception as e:
            logger.error(f"Failed to analyze risk: {e}")
            return "medium"

    async def _extract_skill_requirements(self, task_context) -> List[str]:
        """Extract required skills from task context"""
        try:
            skills = set()
            text_content = (
                f"{task_context.task_type} {' '.join(task_context.requirements)}"
            )
            text_lower = text_content.lower()

            # Common skill keywords
            skill_keywords = {
                "python": ["python", "django", "flask", "fastapi"],
                "javascript": ["javascript", "js", "node", "react", "vue", "angular"],
                "database": ["sql", "postgresql", "mysql", "mongodb", "redis"],
                "devops": ["docker", "kubernetes", "ci/cd", "deployment"],
                "security": [
                    "security",
                    "authentication",
                    "authorization",
                    "encryption",
                ],
                "testing": ["testing", "unit", "integration", "e2e"],
                "frontend": ["frontend", "ui", "ux", "css", "html"],
                "backend": ["backend", "api", "server", "microservices"],
            }

            for skill, keywords in skill_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    skills.add(skill)

            return list(skills)

        except Exception as e:
            logger.error(f"Failed to extract skill requirements: {e}")
            return []

    async def _extract_dependencies(self, task_context) -> List[str]:
        """Extract dependencies from task context"""
        try:
            dependencies = []
            text_content = (
                f"{task_context.task_type} {' '.join(task_context.requirements)}"
            )

            # Look for dependency patterns
            dependency_patterns = [
                r"depends on (\w+)",
                r"requires (\w+)",
                r"needs (\w+)",
                r"integration with (\w+)",
                r"uses (\w+)",
            ]

            for pattern in dependency_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                dependencies.extend(matches)

            return list(set(dependencies))

        except Exception as e:
            logger.error(f"Failed to extract dependencies: {e}")
            return []

    async def _extract_constraints(self, task_context) -> List[str]:
        """Extract constraints from task context"""
        try:
            constraints = []
            text_content = (
                f"{task_context.task_type} {' '.join(task_context.constraints)}"
            )

            # Look for constraint patterns
            constraint_patterns = [
                r"must (\w+)",
                r"should (\w+)",
                r"cannot (\w+)",
                r"limited to (\w+)",
                r"within (\w+)",
            ]

            for pattern in constraint_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                constraints.extend(matches)

            return list(set(constraints))

        except Exception as e:
            logger.error(f"Failed to extract constraints: {e}")
            return []

    async def _analyze_resource_requirements(
        self, task_context, complexity_score: float
    ) -> Dict[str, Any]:
        """Analyze resource requirements"""
        try:
            resources = {
                "estimated_time_minutes": await self._estimate_duration(
                    task_context, complexity_score
                ),
                "skill_level": (
                    "expert"
                    if complexity_score > 0.7
                    else "intermediate" if complexity_score > 0.4 else "beginner"
                ),
                "team_size": (
                    1 if complexity_score < 0.5 else 2 if complexity_score < 0.8 else 3
                ),
                "tools_required": [],
                "infrastructure_required": [],
            }

            # Add tools based on category
            category = await self._analyze_category(task_context)
            if category == TaskCategory.FRONTEND:
                resources["tools_required"].extend(["code_editor", "browser_dev_tools"])
            elif category == TaskCategory.BACKEND:
                resources["tools_required"].extend(["code_editor", "api_testing_tool"])
            elif category == TaskCategory.DATABASE:
                resources["tools_required"].extend(
                    ["database_client", "migration_tool"]
                )

            return resources

        except Exception as e:
            logger.error(f"Failed to analyze resource requirements: {e}")
            return {}

    def _calculate_analysis_confidence(self, task_context) -> float:
        """Calculate confidence in the analysis"""
        try:
            confidence = 0.5  # Base confidence

            # Increase confidence based on available information
            if task_context.requirements:
                confidence += 0.2
            if task_context.constraints:
                confidence += 0.1
            if task_context.estimated_duration:
                confidence += 0.1
            if len(task_context.requirements) > 3:
                confidence += 0.1

            return min(1.0, confidence)

        except Exception as e:
            logger.error(f"Failed to calculate analysis confidence: {e}")
            return 0.5

    def _create_default_analysis(self, task_context) -> TaskAnalysis:
        """Create default analysis when analysis fails"""
        return TaskAnalysis(
            task_id=task_context.task_id,
            complexity_score=0.5,
            complexity_level=ComplexityLevel.MODERATE,
            category=TaskCategory.GENERAL,
            estimated_duration=240,
            risk_level="medium",
            skill_requirements=[],
            dependencies=[],
            constraints=[],
            resource_requirements={},
            analysis_confidence=0.3,
            factors=ComplexityFactors(),
        )

    # Helper methods for complexity factor extraction
    def _estimate_lines_of_code(self, text_content: str) -> int:
        """Estimate lines of code based on task description"""
        # Simple heuristic based on keywords and complexity indicators
        complexity_indicators = [
            "complex",
            "advanced",
            "sophisticated",
            "multiple",
            "integration",
        ]
        base_estimate = 100

        for indicator in complexity_indicators:
            if indicator.lower() in text_content.lower():
                base_estimate *= 1.5

        return min(base_estimate, 5000)  # Cap at 5000 lines

    def _estimate_number_of_files(self, text_content: str) -> int:
        """Estimate number of files based on task description"""
        file_indicators = [
            "multiple files",
            "several files",
            "file structure",
            "components",
        ]
        base_estimate = 1

        for indicator in file_indicators:
            if indicator.lower() in text_content.lower():
                base_estimate += 2

        return min(base_estimate, 20)  # Cap at 20 files

    def _count_dependencies(self, text_content: str) -> int:
        """Count dependencies mentioned in text"""
        dependency_keywords = ["depends", "requires", "needs", "uses", "imports"]
        count = 0

        for keyword in dependency_keywords:
            count += text_content.lower().count(keyword)

        return min(count, 10)  # Cap at 10 dependencies

    def _count_integrations(self, text_content: str) -> int:
        """Count integrations mentioned in text"""
        integration_keywords = ["integration", "api", "webhook", "oauth", "sso"]
        count = 0

        for keyword in integration_keywords:
            count += text_content.lower().count(keyword)

        return min(count, 5)  # Cap at 5 integrations

    def _count_security_requirements(self, text_lower: str) -> int:
        """Count security requirements"""
        security_keywords = [
            "security",
            "authentication",
            "authorization",
            "encryption",
            "vulnerability",
        ]
        return sum(1 for keyword in security_keywords if keyword in text_lower)

    def _count_testing_requirements(self, text_lower: str) -> int:
        """Count testing requirements"""
        testing_keywords = ["test", "testing", "unit", "integration", "e2e", "coverage"]
        return sum(1 for keyword in testing_keywords if keyword in text_lower)

    def _count_documentation_requirements(self, text_lower: str) -> int:
        """Count documentation requirements"""
        doc_keywords = ["documentation", "docs", "readme", "api", "user guide"]
        return sum(1 for keyword in doc_keywords if keyword in text_lower)

    def _count_performance_requirements(self, text_lower: str) -> int:
        """Count performance requirements"""
        perf_keywords = [
            "performance",
            "speed",
            "efficiency",
            "optimization",
            "caching",
        ]
        return sum(1 for keyword in perf_keywords if keyword in text_lower)

    def _count_scalability_requirements(self, text_lower: str) -> int:
        """Count scalability requirements"""
        scale_keywords = ["scalability", "scale", "load", "concurrent", "distributed"]
        return sum(1 for keyword in scale_keywords if keyword in text_lower)

    def _count_time_constraints(self, text_lower: str) -> int:
        """Count time constraints"""
        time_keywords = ["urgent", "deadline", "time", "quick", "fast", "immediate"]
        return sum(1 for keyword in time_keywords if keyword in text_lower)

    def _count_skill_requirements(self, text_lower: str) -> int:
        """Count skill requirements"""
        skill_keywords = [
            "expert",
            "advanced",
            "specialized",
            "experience",
            "knowledge",
        ]
        return sum(1 for keyword in skill_keywords if keyword in text_lower)
