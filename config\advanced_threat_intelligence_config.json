{"threat_feeds": {"enabled": true, "update_interval_minutes": 15, "feeds": {"abuseipdb": {"enabled": true, "api_key": "", "url": "https://api.abuseipdb.com/api/v2/blacklist", "format": "json", "confidence_threshold": 0.8}, "virustotal": {"enabled": true, "api_key": "", "url": "https://www.virustotal.com/vtapi/v2/ip-address/report", "format": "json", "confidence_threshold": 0.7}, "alienvault_otx": {"enabled": true, "api_key": "", "url": "https://otx.alienvault.com/api/v1/indicators", "format": "json", "confidence_threshold": 0.6}, "threatfox": {"enabled": true, "url": "https://threatfox-api.abuse.ch/api/v1/", "format": "json", "confidence_threshold": 0.9}, "urlhaus": {"enabled": true, "url": "https://urlhaus.abuse.ch/downloads/csv_recent/", "format": "csv", "confidence_threshold": 0.8}, "phishtank": {"enabled": true, "url": "https://data.phishtank.com/data/online-valid.json", "format": "json", "confidence_threshold": 0.9}, "emerging_threats": {"enabled": false, "url": "https://rules.emergingthreats.net/open/suricata/rules/", "format": "text", "confidence_threshold": 0.7}, "maltiverse": {"enabled": false, "api_key": "", "url": "https://api.maltiverse.com/", "format": "json", "confidence_threshold": 0.6}}, "custom_feeds": {"enabled": true, "feeds": []}}, "machine_learning": {"enabled": true, "models": {"anomaly_detection": {"enabled": true, "algorithm": "isolation_forest", "threshold": 0.8, "training_frequency": "daily", "min_samples": 1000}, "threat_classification": {"enabled": true, "algorithm": "random_forest", "confidence_threshold": 0.7, "training_frequency": "weekly", "features": ["indicator_type", "source", "frequency", "geographic_location"]}, "behavior_analysis": {"enabled": true, "algorithm": "lstm", "sequence_length": 100, "training_frequency": "daily", "prediction_window": 24}, "clustering": {"enabled": true, "algorithm": "dbscan", "eps": 0.5, "min_samples": 5}}, "training": {"enabled": true, "frequency": "weekly", "data_retention_days": 90, "validation_split": 0.2, "cross_validation": true}, "feature_engineering": {"enabled": true, "features": {"temporal": ["hour", "day_of_week", "month", "season"], "geographic": ["country", "region", "city", "isp"], "behavioral": ["frequency", "patterns", "associations"], "technical": ["indicator_type", "protocol", "port", "user_agent"]}}}, "correlation": {"enabled": true, "rules": {"multiple_indicators": {"enabled": true, "threshold": 3, "time_window_hours": 24, "confidence_boost": 0.2}, "temporal_correlation": {"enabled": true, "time_window_minutes": 60, "pattern_detection": true}, "spatial_correlation": {"enabled": true, "geographic_radius_km": 100, "network_proximity": true}, "behavioral_correlation": {"enabled": true, "similarity_threshold": 0.8, "pattern_matching": true}, "threat_actor_correlation": {"enabled": true, "actor_attribution": true, "campaign_linking": true}}, "scoring": {"base_score": 0.5, "correlation_multiplier": 1.5, "time_decay": 0.1, "confidence_weight": 0.3}}, "threat_hunting": {"enabled": true, "techniques": {"ioc_hunting": {"enabled": true, "scan_frequency": "daily", "scan_depth": "comprehensive", "retrospective_analysis": true}, "behavior_hunting": {"enabled": true, "baseline_days": 30, "anomaly_threshold": 0.7, "pattern_detection": true}, "anomaly_hunting": {"enabled": true, "sensitivity": 0.7, "false_positive_reduction": true}, "threat_hunting": {"enabled": true, "hypothesis_based": true, "intelligence_driven": true, "adversary_emulation": false}}, "hunting_playbooks": {"enabled": true, "playbooks": [{"name": "apt_detection", "description": "Advanced Persistent Threat detection", "techniques": ["ioc_hunting", "behavior_hunting", "anomaly_hunting"], "enabled": true}, {"name": "ransomware_detection", "description": "Ransomware activity detection", "techniques": ["ioc_hunting", "behavior_hunting"], "enabled": true}, {"name": "data_exfiltration", "description": "Data exfiltration detection", "techniques": ["behavior_hunting", "anomaly_hunting"], "enabled": true}]}}, "response_automation": {"enabled": true, "actions": {"block_ip": {"enabled": true, "severity_threshold": "medium", "duration_hours": 24, "auto_expire": true}, "block_domain": {"enabled": true, "severity_threshold": "medium", "duration_hours": 24, "auto_expire": true}, "quarantine_file": {"enabled": true, "severity_threshold": "high", "scan_before_quarantine": true}, "alert_security_team": {"enabled": true, "severity_threshold": "high", "notification_channels": ["email", "slack"]}, "isolate_system": {"enabled": true, "severity_threshold": "critical", "duration_hours": 2, "manual_approval": true}, "update_firewall": {"enabled": true, "severity_threshold": "high", "rule_expiry_hours": 48}}, "workflows": {"enabled": true, "workflows": [{"name": "malware_response", "description": "Automated malware response workflow", "triggers": ["malware_detection"], "actions": ["quarantine_file", "alert_security_team", "update_firewall"], "enabled": true}, {"name": "phishing_response", "description": "Automated phishing response workflow", "triggers": ["phishing_detection"], "actions": ["block_domain", "alert_security_team"], "enabled": true}]}}, "mitre_attck": {"enabled": true, "mapping": {"enabled": true, "update_frequency": "monthly", "version": "14.1", "auto_mapping": true}, "tactics": {"reconnaissance": true, "resource_development": true, "initial_access": true, "execution": true, "persistence": true, "privilege_escalation": true, "defense_evasion": true, "credential_access": true, "discovery": true, "lateral_movement": true, "collection": true, "command_and_control": true, "exfiltration": true, "impact": true}}, "intelligence_sharing": {"enabled": true, "platforms": {"misp": {"enabled": false, "url": "", "api_key": "", "sync_frequency": "hourly"}, "opencti": {"enabled": false, "url": "", "api_key": "", "sync_frequency": "daily"}, "threatfox": {"enabled": true, "url": "https://threatfox-api.abuse.ch/api/v1/", "api_key": "", "sync_frequency": "hourly"}}, "stix_taxii": {"enabled": false, "servers": []}}, "reporting": {"enabled": true, "reports": {"daily_summary": {"enabled": true, "recipients": [], "format": "html"}, "weekly_analysis": {"enabled": true, "recipients": [], "format": "pdf"}, "monthly_trends": {"enabled": true, "recipients": [], "format": "pdf"}}, "dashboards": {"enabled": true, "real_time": true, "historical": true, "custom_views": true}}, "data_retention": {"indicators": {"retention_days": 365, "archive_after_days": 90, "compression": true}, "reports": {"retention_days": 2555, "archive_after_days": 365, "compression": true}, "alerts": {"retention_days": 180, "archive_after_days": 30, "compression": true}}, "performance": {"cache": {"enabled": true, "max_size_mb": 1000, "ttl_seconds": 3600}, "batch_processing": {"enabled": true, "batch_size": 1000, "processing_interval_seconds": 60}, "parallel_processing": {"enabled": true, "max_workers": 4}}}