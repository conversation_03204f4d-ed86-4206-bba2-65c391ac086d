import asyncio
import json
import logging
import threading
import time
import weakref
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set

logger = logging.getLogger(__name__)


class ContextEventType(Enum):
    """Types of context events."""

    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    ACCESSED = "accessed"
    LOCKED = "locked"
    UNLOCKED = "unlocked"


@dataclass
class ContextEvent:
    """Represents a context change event."""

    event_type: ContextEventType
    key: str
    value: Any = None
    previous_value: Any = None
    agent_id: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ContextLock:
    """Thread-safe lock for context keys."""

    def __init__(self, key: str, agent_id: str, timeout: float = 30.0):
        self.key = key
        self.agent_id = agent_id
        self.timeout = timeout
        self.acquired_at = time.time()
        self._lock = threading.RLock()

    def is_expired(self) -> bool:
        """Check if lock has expired."""
        return time.time() - self.acquired_at > self.timeout

    def extend(self, additional_time: float = 30.0) -> None:
        """Extend lock timeout."""
        with self._lock:
            self.timeout += additional_time


class SharedContext:
    """Thread-safe shared context store for agent collaboration."""

    def __init__(self, context_id: str, persistence_path: Optional[str] = None):
        self.context_id = context_id
        self.persistence_path = persistence_path
        self._data: Dict[str, Any] = {}
        self._metadata: Dict[str, Dict[str, Any]] = {}
        self._locks: Dict[str, ContextLock] = {}
        self._subscribers: Dict[str, List[Callable]] = {}
        self._access_log: List[ContextEvent] = []
        self._lock = threading.RLock()
        self._event_handlers: List[Callable[[ContextEvent], None]] = []

        # Load from persistence if available
        if self.persistence_path:
            self._load_from_persistence()

        logger.info(f"SharedContext {context_id} initialized")

    def subscribe(self, key: str, callback: Callable[[ContextEvent], None]) -> None:
        """Subscribe to changes on a specific key."""
        with self._lock:
            if key not in self._subscribers:
                self._subscribers[key] = []
            self._subscribers[key].append(callback)

        logger.debug(f"Subscribed to context key: {key}")

    def unsubscribe(self, key: str, callback: Callable[[ContextEvent], None]) -> None:
        """Unsubscribe from changes on a specific key."""
        with self._lock:
            if key in self._subscribers and callback in self._subscribers[key]:
                self._subscribers[key].remove(callback)

        logger.debug(f"Unsubscribed from context key: {key}")

    def add_event_handler(self, handler: Callable[[ContextEvent], None]) -> None:
        """Add global event handler for all context changes."""
        with self._lock:
            self._event_handlers.append(handler)

    def set(
        self,
        key: str,
        value: Any,
        agent_id: Optional[str] = None,
        metadata: Dict[str, Any] = None,
    ) -> None:
        """Set a value in the shared context."""
        with self._lock:
            # Check if key is locked by another agent
            if key in self._locks:
                lock = self._locks[key]
                if lock.is_expired():
                    del self._locks[key]
                elif lock.agent_id != agent_id:
                    raise ValueError(f"Key '{key}' is locked by agent {lock.agent_id}")

            previous_value = self._data.get(key)
            self._data[key] = value

            # Update metadata
            if metadata:
                if key not in self._metadata:
                    self._metadata[key] = {}
                self._metadata[key].update(metadata)

            # Create event
            event_type = (
                ContextEventType.UPDATED
                if key in self._data
                else ContextEventType.CREATED
            )
            event = ContextEvent(
                event_type=event_type,
                key=key,
                value=value,
                previous_value=previous_value,
                agent_id=agent_id,
                metadata=metadata or {},
            )

            self._log_event(event)
            self._notify_subscribers(key, event)
            self._notify_global_handlers(event)

        logger.debug(f"Set context key '{key}' = {value} by agent {agent_id}")

    def get(self, key: str, default: Any = None, agent_id: Optional[str] = None) -> Any:
        """Get a value from the shared context."""
        with self._lock:
            value = self._data.get(key, default)

            # Log access
            event = ContextEvent(
                event_type=ContextEventType.ACCESSED,
                key=key,
                value=value,
                agent_id=agent_id,
            )
            self._log_event(event)

            return value

    def get_with_metadata(
        self, key: str, default: Any = None, agent_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get a value with its metadata from the shared context."""
        with self._lock:
            value = self._data.get(key, default)
            metadata = self._metadata.get(key, {})

            # Log access
            event = ContextEvent(
                event_type=ContextEventType.ACCESSED,
                key=key,
                value=value,
                agent_id=agent_id,
            )
            self._log_event(event)

            return {"value": value, "metadata": metadata, "exists": key in self._data}

    def delete(self, key: str, agent_id: Optional[str] = None) -> bool:
        """Delete a key from the shared context."""
        with self._lock:
            # Check if key is locked by another agent
            if key in self._locks:
                lock = self._locks[key]
                if lock.is_expired():
                    del self._locks[key]
                elif lock.agent_id != agent_id:
                    raise ValueError(f"Key '{key}' is locked by agent {lock.agent_id}")

            if key not in self._data:
                return False

            previous_value = self._data[key]
            del self._data[key]

            # Clean up metadata
            if key in self._metadata:
                del self._metadata[key]

            # Create event
            event = ContextEvent(
                event_type=ContextEventType.DELETED,
                key=key,
                previous_value=previous_value,
                agent_id=agent_id,
            )

            self._log_event(event)
            self._notify_subscribers(key, event)
            self._notify_global_handlers(event)

            return True

    def lock(self, key: str, agent_id: str, timeout: float = 30.0) -> bool:
        """Lock a key for exclusive access by an agent."""
        with self._lock:
            # Check if already locked
            if key in self._locks:
                lock = self._locks[key]
                if lock.is_expired():
                    del self._locks[key]
                elif lock.agent_id != agent_id:
                    return False

            # Create lock
            self._locks[key] = ContextLock(key, agent_id, timeout)

            # Create event
            event = ContextEvent(
                event_type=ContextEventType.LOCKED,
                key=key,
                agent_id=agent_id,
                metadata={"timeout": timeout},
            )
            self._log_event(event)
            self._notify_global_handlers(event)

            logger.debug(f"Locked key '{key}' for agent {agent_id}")
            return True

    def unlock(self, key: str, agent_id: str) -> bool:
        """Unlock a key."""
        with self._lock:
            if key not in self._locks:
                return False

            lock = self._locks[key]
            if lock.agent_id != agent_id:
                return False

            del self._locks[key]

            # Create event
            event = ContextEvent(
                event_type=ContextEventType.UNLOCKED, key=key, agent_id=agent_id
            )
            self._log_event(event)
            self._notify_global_handlers(event)

            logger.debug(f"Unlocked key '{key}' by agent {agent_id}")
            return True

    def extend_lock(
        self, key: str, agent_id: str, additional_time: float = 30.0
    ) -> bool:
        """Extend a lock timeout."""
        with self._lock:
            if key not in self._locks:
                return False

            lock = self._locks[key]
            if lock.agent_id != agent_id:
                return False

            lock.extend(additional_time)
            logger.debug(f"Extended lock on key '{key}' for agent {agent_id}")
            return True

    def is_locked(self, key: str, agent_id: Optional[str] = None) -> bool:
        """Check if a key is locked."""
        with self._lock:
            if key not in self._locks:
                return False

            lock = self._locks[key]
            if lock.is_expired():
                del self._locks[key]
                return False

            if agent_id and lock.agent_id == agent_id:
                return False  # Not locked for the lock owner

            return True

    def update(
        self,
        updates: Dict[str, Any],
        agent_id: Optional[str] = None,
        metadata: Dict[str, Any] = None,
    ) -> None:
        """Update multiple keys atomically."""
        with self._lock:
            # Check locks for all keys first
            for key in updates:
                if key in self._locks:
                    lock = self._locks[key]
                    if lock.is_expired():
                        del self._locks[key]
                    elif lock.agent_id != agent_id:
                        raise ValueError(
                            f"Key '{key}' is locked by agent {lock.agent_id}"
                        )

            # Apply all updates
            for key, value in updates.items():
                self.set(key, value, agent_id, metadata)

    def get_all(self, agent_id: Optional[str] = None) -> Dict[str, Any]:
        """Get all data in the context."""
        with self._lock:
            # Log access to all keys
            event = ContextEvent(
                event_type=ContextEventType.ACCESSED,
                key="*",
                value=None,
                agent_id=agent_id,
                metadata={"access_type": "get_all"},
            )
            self._log_event(event)

            return self._data.copy()

    def keys(self) -> List[str]:
        """Get all keys in the context."""
        with self._lock:
            return list(self._data.keys())

    def has_key(self, key: str) -> bool:
        """Check if a key exists in the context."""
        with self._lock:
            return key in self._data

    def clear(self, agent_id: Optional[str] = None) -> None:
        """Clear all data in the context."""
        with self._lock:
            # Check locks
            for key in self._locks:
                lock = self._locks[key]
                if not lock.is_expired() and lock.agent_id != agent_id:
                    raise ValueError(
                        f"Cannot clear context: key '{key}' is locked by agent {lock.agent_id}"
                    )

            self._data.clear()
            self._metadata.clear()
            self._locks.clear()

            logger.info(f"Cleared context {self.context_id} by agent {agent_id}")

    def get_locked_keys(self) -> Dict[str, str]:
        """Get all locked keys and their owners."""
        with self._lock:
            # Clean expired locks
            expired_keys = []
            for key, lock in self._locks.items():
                if lock.is_expired():
                    expired_keys.append(key)

            for key in expired_keys:
                del self._locks[key]

            return {key: lock.agent_id for key, lock in self._locks.items()}

    def get_access_log(self, limit: Optional[int] = None) -> List[ContextEvent]:
        """Get access log."""
        with self._lock:
            if limit:
                return self._access_log[-limit:]
            return self._access_log.copy()

    def get_statistics(self) -> Dict[str, Any]:
        """Get context usage statistics."""
        with self._lock:
            total_events = len(self._access_log)
            event_types = {}
            agent_activity = {}

            for event in self._access_log:
                # Count event types
                event_type = event.event_type.value
                event_types[event_type] = event_types.get(event_type, 0) + 1

                # Count agent activity
                if event.agent_id:
                    agent_activity[event.agent_id] = (
                        agent_activity.get(event.agent_id, 0) + 1
                    )

            return {
                "context_id": self.context_id,
                "total_keys": len(self._data),
                "locked_keys": len(self._locks),
                "total_events": total_events,
                "event_types": event_types,
                "agent_activity": agent_activity,
                "active_subscribers": sum(
                    len(subs) for subs in self._subscribers.values()
                ),
                "global_handlers": len(self._event_handlers),
            }

    def _log_event(self, event: ContextEvent) -> None:
        """Log an event to the access log."""
        self._access_log.append(event)

        # Limit log size to prevent memory issues
        if len(self._access_log) > 10000:
            # Keep latest 5000 events
            self._access_log = self._access_log[-5000:]

    def _notify_subscribers(self, key: str, event: ContextEvent) -> None:
        """Notify subscribers of a key change."""
        if key in self._subscribers:
            for callback in self._subscribers[key]:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Error in context subscriber callback: {e}")

    def _notify_global_handlers(self, event: ContextEvent) -> None:
        """Notify global event handlers."""
        for handler in self._event_handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"Error in context event handler: {e}")

    def _load_from_persistence(self) -> None:
        """Load context from persistent storage."""
        if not self.persistence_path:
            return

        try:
            persistence_file = Path(self.persistence_path)
            if persistence_file.exists():
                with open(persistence_file, "r") as f:
                    data = json.load(f)
                    self._data = data.get("data", {})
                    self._metadata = data.get("metadata", {})
                logger.info(
                    f"Loaded context {self.context_id} from {self.persistence_path}"
                )
        except Exception as e:
            logger.error(f"Failed to load context from persistence: {e}")

    def save_to_persistence(self) -> None:
        """Save context to persistent storage."""
        if not self.persistence_path:
            return

        try:
            with self._lock:
                persistence_file = Path(self.persistence_path)
                persistence_file.parent.mkdir(parents=True, exist_ok=True)

                data = {
                    "context_id": self.context_id,
                    "data": self._data,
                    "metadata": self._metadata,
                    "saved_at": time.time(),
                }

                with open(persistence_file, "w") as f:
                    json.dump(data, f, indent=2, default=str)

                logger.info(
                    f"Saved context {self.context_id} to {self.persistence_path}"
                )
        except Exception as e:
            logger.error(f"Failed to save context to persistence: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary representation."""
        with self._lock:
            return {
                "context_id": self.context_id,
                "data": self._data.copy(),
                "metadata": self._metadata.copy(),
                "locked_keys": self.get_locked_keys(),
                "statistics": self.get_statistics(),
            }


class SharedContextManager:
    """Manages multiple shared context instances."""

    def __init__(self, base_persistence_path: Optional[str] = None):
        self.base_persistence_path = base_persistence_path
        self._contexts: Dict[str, SharedContext] = {}
        self._lock = threading.RLock()

        logger.info("SharedContextManager initialized")

    def create_context(
        self, context_id: str, persistence: bool = True
    ) -> SharedContext:
        """Create a new shared context."""
        with self._lock:
            if context_id in self._contexts:
                return self._contexts[context_id]

            persistence_path = None
            if persistence and self.base_persistence_path:
                persistence_path = f"{self.base_persistence_path}/{context_id}.json"

            context = SharedContext(context_id, persistence_path)
            self._contexts[context_id] = context

            logger.info(f"Created shared context: {context_id}")
            return context

    def get_context(self, context_id: str) -> Optional[SharedContext]:
        """Get an existing shared context."""
        with self._lock:
            return self._contexts.get(context_id)

    def delete_context(self, context_id: str) -> bool:
        """Delete a shared context."""
        with self._lock:
            if context_id not in self._contexts:
                return False

            context = self._contexts[context_id]
            if context.persistence_path:
                try:
                    Path(context.persistence_path).unlink(missing_ok=True)
                except Exception as e:
                    logger.error(f"Failed to delete persistence file: {e}")

            del self._contexts[context_id]
            logger.info(f"Deleted shared context: {context_id}")
            return True

    def list_contexts(self) -> List[str]:
        """List all context IDs."""
        with self._lock:
            return list(self._contexts.keys())

    def save_all_contexts(self) -> None:
        """Save all contexts to persistence."""
        with self._lock:
            for context in self._contexts.values():
                context.save_to_persistence()

    def get_global_statistics(self) -> Dict[str, Any]:
        """Get statistics for all contexts."""
        with self._lock:
            total_contexts = len(self._contexts)
            total_keys = sum(len(ctx._data) for ctx in self._contexts.values())
            total_locks = sum(len(ctx._locks) for ctx in self._contexts.values())

            return {
                "total_contexts": total_contexts,
                "total_keys": total_keys,
                "total_locks": total_locks,
                "contexts": {
                    ctx_id: ctx.get_statistics()
                    for ctx_id, ctx in self._contexts.items()
                },
            }
