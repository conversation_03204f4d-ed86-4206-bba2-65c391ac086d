"""
Supabase CLI commands for AI Coding Agent.

This module provides the main entry point for Supabase CLI commands,
importing and registering the modular command groups.
"""

from cli.commands.migration import migration
from cli.commands.schema import schema
from cli.commands.supabase import supabase


def register_commands(cli_group) -> None:
    """Register all Supabase CLI command groups with the main CLI."""
    cli_group.add_command(supabase)
    cli_group.add_command(migration)
    cli_group.add_command(schema)
