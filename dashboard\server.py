"""
Server launcher for dashboard backend.
Provides command-line interface to start the FastAPI server.
"""

import argparse
import logging
import os
import sys
from pathlib import Path

import uvicorn
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from dashboard.api import app
from dashboard.database import create_tables, health_check, init_db

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


# Mount static files
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def setup_database():
    """Setup database tables and initial data"""
    try:
        logger.info("Setting up database...")
        create_tables()
        init_db()

        if health_check():
            logger.info("Database setup completed successfully")
            return True
        else:
            logger.error("Database health check failed after setup")
            return False
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False


def main():
    """Main function to run the dashboard server"""
    parser = argparse.ArgumentParser(description="AI Coding Agent Dashboard Server")
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind the server to (default: 127.0.0.1)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)",
    )
    parser.add_argument(
        "--reload", action="store_true", help="Enable auto-reload on code changes"
    )
    parser.add_argument(
        "--workers", type=int, default=1, help="Number of worker processes (default: 1)"
    )
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="Log level (default: info)",
    )
    parser.add_argument(
        "--setup-db", action="store_true", help="Setup database before starting server"
    )
    parser.add_argument(
        "--check-db", action="store_true", help="Check database health and exit"
    )

    args = parser.parse_args()

    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))

    # Check database health if requested
    if args.check_db:
        logger.info("Checking database health...")
        if health_check():
            logger.info("Database health check passed")
            sys.exit(0)
        else:
            logger.error("Database health check failed")
            sys.exit(1)

    # Setup database if requested
    if args.setup_db:
        if not setup_database():
            logger.error("Failed to setup database. Exiting.")
            sys.exit(1)

    # Start the server
    logger.info(f"Starting AI Coding Agent Dashboard on {args.host}:{args.port}")

    try:
        uvicorn.run(
            "dashboard.api:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers,
            log_level=args.log_level,
            access_log=True,
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
