import React, { useState } from 'react';
import { Monitor, Tablet, Smartphone, ZoomIn, ZoomOut, RotateCcw, Download } from 'lucide-react';

interface PreviewPanelProps {
  className?: string;
}

type DeviceMode = 'desktop' | 'tablet' | 'mobile';

export const PreviewPanel: React.FC<PreviewPanelProps> = ({ className = '' }) => {
  const [deviceMode, setDeviceMode] = useState<DeviceMode>('desktop');
  const [zoom, setZoom] = useState(100);
  const [previewUrl, setPreviewUrl] = useState('');

  const deviceSizes = {
    desktop: 'w-full',
    tablet: 'w-96 mx-auto',
    mobile: 'w-80 mx-auto',
  };

  const sampleHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Website</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        .cta-button {
            display: inline-block;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Your Website</h1>
        <p>This is a beautiful, responsive website created with AI assistance. You can customize this content and styling to match your needs.</p>
        <a href="#" class="cta-button">Get Started</a>
    </div>
</body>
</html>
  `;

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 50));
  };

  const handleReset = () => {
    setZoom(100);
  };

  const handleRefresh = () => {
    // Refresh the preview
    const iframe = document.getElementById('preview-iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.src = iframe.src;
    }
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
          Preview
        </h3>

        <div className="flex items-center space-x-2">
          {/* Device Mode Selector */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-md p-1">
            <button
              onClick={() => setDeviceMode('desktop')}
              className={`p-1 rounded ${deviceMode === 'desktop' ? 'bg-white dark:bg-gray-600 shadow' : ''}`}
              title="Desktop"
            >
              <Monitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => setDeviceMode('tablet')}
              className={`p-1 rounded ${deviceMode === 'tablet' ? 'bg-white dark:bg-gray-600 shadow' : ''}`}
              title="Tablet"
            >
              <Tablet className="w-4 h-4" />
            </button>
            <button
              onClick={() => setDeviceMode('mobile')}
              className={`p-1 rounded ${deviceMode === 'mobile' ? 'bg-white dark:bg-gray-600 shadow' : ''}`}
              title="Mobile"
            >
              <Smartphone className="w-4 h-4" />
            </button>
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center space-x-1">
            <button
              onClick={handleZoomOut}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              title="Zoom Out"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[3rem] text-center">
              {zoom}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              title="Zoom In"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
            <button
              onClick={handleReset}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              title="Reset Zoom"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1">
            <button
              onClick={handleRefresh}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              title="Refresh Preview"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            <button
              onClick={() => {/* Handle download */}}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              title="Download"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-auto bg-gray-100 dark:bg-gray-900 p-4">
        <div
          className={`${deviceSizes[deviceMode]} transition-all duration-300`}
          style={{ transform: `scale(${zoom / 100})` }}
        >
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            {/* Browser Chrome */}
            <div className="flex items-center justify-between bg-gray-200 dark:bg-gray-800 px-4 py-2">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-xs text-gray-600 dark:text-gray-400">
                  localhost:3000
                </span>
              </div>
            </div>

            {/* Preview Frame */}
            <div className="relative">
              <iframe
                id="preview-iframe"
                srcDoc={sampleHtml}
                className="w-full border-0"
                style={{
                  height: deviceMode === 'mobile' ? '600px' :
                          deviceMode === 'tablet' ? '800px' : '600px'
                }}
                title="Website Preview"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-400">
        <span>Ready</span>
        <span>{deviceMode} • {zoom}%</span>
      </div>
    </div>
  );
};
