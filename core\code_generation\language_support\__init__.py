#!/usr/bin/env python3
"""
Language Support Package for AST Code Generation

This package provides language-specific AST manipulation and code generation support
for Python, TypeScript, JavaScript, JSX, and TSX.
"""

from core.code_generation.language_support.base_language_support import (
    BaseLanguageSupport,
)
from core.code_generation.language_support.javascript_support import (
    JavaScriptASTSupport,
)
from core.code_generation.language_support.jsx_support import JSXASTSupport
from core.code_generation.language_support.python_support import PythonASTSupport
from core.code_generation.language_support.tsx_support import TSXASTSupport
from core.code_generation.language_support.typescript_support import (
    TypeScriptASTSupport,
)

__all__ = [
    "PythonASTSupport",
    "TypeScriptASTSupport",
    "JavaScriptASTSupport",
    "JSXASTSupport",
    "TSXASTSupport",
    "BaseLanguageSupport",
]
