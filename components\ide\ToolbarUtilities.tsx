import React, { useState, useRef, useEffect, RefObject } from 'react';
import {
  Upload,
  Download,
  Share2,
  Settings,
  User,
  ChevronDown,
  LogOut,
  Loader2,
  Check,
  Bug,
  Zap
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { UploadProgress } from '@/components/ui/UploadProgress';
import { useTheme } from '@/contexts/ThemeContext';

interface ToolbarUtilitiesProps {
  documentationPanelToggle?: () => void;
  showDocumentationPanel?: boolean;
  onUpload: () => void;
  uploading: boolean;
  onDownload: () => void;
  downloading: boolean;
  onShare: () => void;
  sharing: boolean;
  copied: boolean;
  onSettings: () => void;
  onToggleErrorDetection?: () => void;
  showErrorDetection: boolean;
  onToggleBonusFeatures?: () => void;
  showBonusFeatures: boolean;
  fileInputRef: RefObject<HTMLInputElement>;
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  uploadFiles: any[];
  handleRemoveUpload: (fileId: string) => void;
  handleRetryUpload: (fileId: string) => void;
}

export const ToolbarUtilities: React.FC<ToolbarUtilitiesProps> = ({
  documentationPanelToggle,
  showDocumentationPanel,
  onUpload,
  uploading,
  onDownload,
  downloading,
  onShare,
  sharing,
  copied,
  onSettings,
  onToggleErrorDetection,
  showErrorDetection,
  onToggleBonusFeatures,
  showBonusFeatures,
  fileInputRef,
  handleFileUpload,
  uploadFiles,
  handleRemoveUpload,
  handleRetryUpload
}) => {
  const { toggleTheme, isDark } = useTheme();
  const { user, isAuthenticated, logout } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Close user menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
    };

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu]);

  return (
  <div className="flex items-center space-x-2">
    <button
      onClick={documentationPanelToggle}
      className={`p-1 rounded ${showDocumentationPanel ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'} hover:bg-blue-600 dark:hover:bg-blue-600 transition-colors`}
      title="Toggle Documentation Panel (Ctrl/Cmd + /)"
    >
      <FileText className="w-5 h-5" />
    </button>
    <button
      onClick={toggleTheme}
      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
      title={`Switch to ${isDark ? 'light' : 'dark'} theme`}
    >
      {isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
    </button>
    <button
      onClick={onUpload}
      disabled={uploading}
      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
      title="Upload Files (Ctrl/Cmd + U)"
    >
      {uploading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Upload className="w-4 h-4" />}
    </button>
    <button
      onClick={onDownload}
      disabled={downloading}
      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
      title="Download Project"
    >
      {downloading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Download className="w-4 h-4" />}
    </button>
    <button
      onClick={onShare}
      disabled={sharing}
      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors disabled:opacity-50"
      title="Share Project"
    >
      {sharing ? <Loader2 className="w-4 h-4 animate-spin" /> : copied ? <Check className="w-4 h-4 text-green-500" /> : <Share2 className="w-4 h-4" />}
    </button>
    <button
      onClick={onSettings}
      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
      title="Settings"
    >
      <Settings className="w-4 h-4" />
    </button>
    <button
      onClick={onToggleErrorDetection || (() => {})}
      className={`p-2 rounded hover:bg-gray-100 ${showErrorDetection ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}
      title="Error Detection (Ctrl+E)"
    >
      <Bug className="w-5 h-5" />
    </button>
    <button
      onClick={onToggleBonusFeatures || (() => {})}
      className={`p-2 rounded hover:bg-gray-100 ${showBonusFeatures ? 'bg-purple-100 text-purple-600' : 'text-gray-600'}`}
      title="Bonus Features (Ctrl+B)"
    >
      <Zap className="w-5 h-5" />
    </button>

    {/* User Menu */}
    <div className="relative user-menu-container">
      <button
        onClick={() => setShowUserMenu(!showUserMenu)}
        className="flex items-center space-x-1 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
        title={isAuthenticated ? `Logged in as ${user?.username}` : 'Login'}
      >
        <User className="w-4 h-4" />
        {isAuthenticated && (
          <>
            <span className="text-sm font-medium">{user?.username}</span>
            <ChevronDown className="w-3 h-3" />
          </>
        )}
      </button>

      {showUserMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-50">
          {isAuthenticated ? (
            <div className="py-1">
              <div className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
                <div className="font-medium">{user?.username}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">{user?.email}</div>
              </div>
              <button
                onClick={() => {
                  logout();
                  setShowUserMenu(false);
                }}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </button>
            </div>
          ) : (
            <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
              Please log in to access features
            </div>
          )}
        </div>
      )}
    </div>
    {/* Hidden file input for uploads */}
    <input
      ref={fileInputRef}
      type="file"
      multiple
      accept=".html,.css,.js,.jsx,.ts,.tsx,.json,.md,.txt"
      onChange={handleFileUpload}
      className="hidden"
    />
    {/* Upload Progress */}
    <UploadProgress
      files={uploadFiles}
      onRemove={handleRemoveUpload}
      onRetry={handleRetryUpload}
    />
  </div>
);
}
