#!/usr/bin/env python3
"""
Base Language Support for AST Code Generation

This module provides the base class for language-specific AST support implementations.
"""

import ast
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class LanguageType(Enum):
    """Supported language types"""

    PYTHON = "python"
    TYPESCRIPT = "typescript"
    JAVASCRIPT = "javascript"
    JSX = "jsx"
    TSX = "tsx"


@dataclass
class ASTNodeInfo:
    """Information about an AST node"""

    node_type: str
    name: Optional[str] = None
    line_number: Optional[int] = None
    column: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CodeGenerationContext:
    """Context for code generation"""

    language: LanguageType
    requirements: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    conventions: Dict[str, Any] = field(default_factory=dict)
    imports: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseLanguageSupport(ABC):
    """
    Base class for language-specific AST support implementations.
    """

    def __init__(self, language: LanguageType, config: Dict[str, Any]):
        self.language = language
        self.config = config
        self.node_cache: Dict[str, ast.AST] = {}
        self.template_cache: Dict[str, ast.AST] = {}

        logger.info(f"Initialized {language.value} AST support")

    @abstractmethod
    async def create_base_ast(self) -> ast.AST:
        """Create a base AST for the language"""
        pass

    @abstractmethod
    async def parse_code(self, code: str) -> ast.AST:
        """Parse code string into AST"""
        pass

    @abstractmethod
    async def ast_to_code(self, ast_tree: ast.AST) -> str:
        """Convert AST back to code string"""
        pass

    @abstractmethod
    async def add_import(self, ast_tree: ast.AST, import_statement: str) -> ast.AST:
        """Add import statement to AST"""
        pass

    @abstractmethod
    async def add_function(
        self, ast_tree: ast.AST, function_name: str, parameters: List[str], body: str
    ) -> ast.AST:
        """Add function to AST"""
        pass

    @abstractmethod
    async def add_class(
        self, ast_tree: ast.AST, class_name: str, methods: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add class to AST"""
        pass

    @abstractmethod
    async def add_variable(
        self,
        ast_tree: ast.AST,
        variable_name: str,
        value: str,
        var_type: Optional[str] = None,
    ) -> ast.AST:
        """Add variable declaration to AST"""
        pass

    @abstractmethod
    async def merge_asts(self, existing_ast: ast.AST, new_ast: ast.AST) -> ast.AST:
        """Merge two ASTs"""
        pass

    @abstractmethod
    async def validate_ast(self, ast_tree: ast.AST) -> bool:
        """Validate AST structure"""
        pass

    @abstractmethod
    async def extract_node_info(self, ast_tree: ast.AST) -> List[ASTNodeInfo]:
        """Extract information about AST nodes"""
        pass

    async def get_language_features(self) -> Dict[str, Any]:
        """Get supported language features"""
        return self.config.get("features", {})

    async def get_language_conventions(self) -> Dict[str, Any]:
        """Get language conventions"""
        return self.config.get("conventions", {})

    async def apply_conventions(self, ast_tree: ast.AST) -> ast.AST:
        """Apply language conventions to AST"""
        conventions = await self.get_language_conventions()

        # Apply naming conventions
        if "naming" in conventions:
            ast_tree = await self._apply_naming_conventions(
                ast_tree, conventions["naming"]
            )

        # Apply formatting conventions
        if "max_line_length" in conventions:
            ast_tree = await self._apply_line_length_conventions(
                ast_tree, conventions["max_line_length"]
            )

        return ast_tree

    async def _apply_naming_conventions(
        self, ast_tree: ast.AST, naming_style: str
    ) -> ast.AST:
        """Apply naming conventions to AST"""
        # This is a base implementation - subclasses should override
        return ast_tree

    async def _apply_line_length_conventions(
        self, ast_tree: ast.AST, max_length: int
    ) -> ast.AST:
        """Apply line length conventions to AST"""
        # This is a base implementation - subclasses should override
        return ast_tree

    async def cache_node(self, key: str, node: ast.AST) -> None:
        """Cache an AST node"""
        self.node_cache[key] = node

    async def get_cached_node(self, key: str) -> Optional[ast.AST]:
        """Get cached AST node"""
        return self.node_cache.get(key)

    async def cache_template(self, key: str, template: ast.AST) -> None:
        """Cache an AST template"""
        self.template_cache[key] = template

    async def get_cached_template(self, key: str) -> Optional[ast.AST]:
        """Get cached AST template"""
        return self.template_cache.get(key)

    async def clear_cache(self) -> None:
        """Clear all caches"""
        self.node_cache.clear()
        self.template_cache.clear()

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "node_cache_size": len(self.node_cache),
            "template_cache_size": len(self.template_cache),
            "language": self.language.value,
        }

    async def cleanup(self) -> None:
        """Cleanup resources"""
        await self.clear_cache()
        logger.info(f"Cleaned up {self.language.value} AST support")
