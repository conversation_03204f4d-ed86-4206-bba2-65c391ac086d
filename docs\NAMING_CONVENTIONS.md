# Naming Conventions for AI Coding Agent

## Overview
This document establishes consistent naming standards across the AI Coding Agent codebase to improve readability, maintainability, and reduce confusion.

## General Principles
- **Consistency**: Use the same naming pattern throughout the codebase
- **Clarity**: Names should clearly indicate purpose and usage
- **Brevity**: Keep names concise but not cryptic
- **Predictability**: Follow established conventions so developers can guess names

## Naming Rules

### 1. Functions and Methods
- **Format**: `snake_case`
- **Examples**: `get_user_data()`, `validate_config()`, `process_file()`
- **Avoid**: camelCase, PascalCase, or mixed styles

### 2. Classes
- **Format**: `PascalCase`
- **Examples**: `Config<PERSON>oader`, `FileUtils`, `<PERSON><PERSON>r<PERSON>andler`
- **Avoid**: snake_case, camelCase, or abbreviations

### 3. Variables and Parameters
- **Format**: `snake_case`
- **Examples**: `config_path`, `user_data`, `file_size`
- **Avoid**: camelCase, PascalCase, or single-letter names (except for loop counters)

### 4. Constants
- **Format**: `UPPER_SNAKE_CASE`
- **Examples**: `MAX_FILE_SIZE`, `DEFAULT_TIMEOUT`, `CONFIG_FILE_PATH`
- **Avoid**: lowercase, camelCase, or PascalCase

### 5. File Names
- **Format**: `snake_case.py`
- **Examples**: `config_loader.py`, `file_utils.py`, `error_handler.py`
- **Avoid**: camelCase, PascalCase, or special characters

### 6. Directories
- **Format**: `snake_case`
- **Examples**: `src/utils`, `config/testing`, `scripts/demo`
- **Avoid**: camelCase, PascalCase, or spaces

### 7. Private Methods and Variables
- **Format**: Prefix with single underscore `_`
- **Examples**: `_validate_input()`, `_internal_cache`
- **Usage**: For internal implementation details not part of public API

### 8. Protected Methods and Variables
- **Format**: Prefix with single underscore `_`
- **Examples**: `_setup_logging()`, `_config_data`
- **Usage**: For subclassing and inheritance scenarios

## Specific Guidelines

### Function Names
- Use action verbs: `get`, `set`, `validate`, `process`, `create`, `delete`
- Be specific: `get_user_by_id()` instead of `get()`
- Avoid abbreviations: `calculate_directory_size()` instead of `calc_dir_size()`

### Variable Names
- Use nouns or noun phrases: `user_data`, `config_path`
- Include units when relevant: `timeout_seconds`, `file_size_mb`
- Avoid generic names: `data`, `temp`, `value`

### Class Names
- Use nouns: `ConfigLoader`, `FileProcessor`
- Avoid verbs in class names
- Use descriptive names that indicate responsibility

### Boolean Variables
- Use prefixes: `is_`, `has_`, `can_`, `should_`
- Examples: `is_valid`, `has_permission`, `can_write`

### Collection Variables
- Use plural nouns: `users`, `config_files`, `error_messages`
- Avoid suffixes like `_list`, `_array`, `_dict` unless necessary

## Examples

### ✅ Good Examples
```python
# Functions
def validate_configuration(config_path: str) -> bool:
    pass

def get_user_data(user_id: int) -> dict:
    pass

def process_file_contents(file_path: str) -> str:
    pass

# Classes
class ConfigurationManager:
    pass

class FileProcessor:
    pass

# Variables
max_file_size_mb = 100
user_session_timeout = 3600
config_file_path = "/path/to/config.json"

# Constants
MAX_RETRY_ATTEMPTS = 3
DEFAULT_BUFFER_SIZE = 8192
CONFIG_FILE_NAME = "app_config.json"
```

### ❌ Bad Examples
```python
# Functions
def validateConfiguration(configPath: str) -> bool:  # camelCase
    pass

def getuserdata(userid: int) -> dict:  # no underscores
    pass

def processFileContents(filepath: str) -> str:  # camelCase
    pass

# Classes
class configurationManager:  # camelCase
    pass

class file_processor:  # snake_case
    pass

# Variables
maxFileSizeMB = 100  # camelCase
userSessionTimeout = 3600  # camelCase
filepath = "/path/to/config.json"  # unclear

# Constants
max_retry_attempts = 3  # lowercase
defaultBufferSize = 8192  # camelCase
configfilename = "app_config.json"  # no underscores
```

## Migration Strategy

### Phase 1: Document Current Inconsistencies
1. Identify all naming inconsistencies
2. Create a mapping of old → new names
3. Document breaking changes

### Phase 2: Update Non-Breaking Changes
1. Update internal variables and private methods
2. Update file names and directory names
3. Update documentation

### Phase 3: Update Public APIs
1. Update public method names
2. Update class names
3. Update import statements
4. Update all references

### Phase 4: Validation
1. Run all tests
2. Verify no broken imports
3. Check documentation accuracy

## Tools and Automation

### Naming Convention Checkers
- Use `pylint` with naming convention rules
- Use `flake8` with naming plugins
- Consider `black` for automatic formatting

### Search and Replace Strategy
1. Use IDE search/replace with regex
2. Use `grep` to find patterns
3. Use `sed` for batch replacements (with caution)

## Common Patterns to Check

### File Patterns
- `get_` prefix for retrieval functions
- `set_` prefix for setter functions
- `is_` prefix for boolean checks
- `validate_` prefix for validation functions
- `_` prefix for private methods

### Directory Patterns
- `utils/` for utility functions
- `models/` for data models
- `services/` for business logic
- `config/` for configuration
- `tests/` for test files

## Review Checklist

Before committing changes:
- [ ] All functions use snake_case
- [ ] All classes use PascalCase
- [ ] All variables use snake_case
- [ ] All constants use UPPER_SNAKE_CASE
- [ ] All file names use snake_case.py
- [ ] All imports are updated
- [ ] All tests pass
- [ ] Documentation is updated
- [ ] No breaking changes in public APIs (or properly documented)

## Exceptions

The following exceptions are allowed:
- Third-party library names (keep original)
- External API parameter names (keep original)
- Legacy compatibility shims (document clearly)
- Test fixtures and mock objects (can use descriptive names)
