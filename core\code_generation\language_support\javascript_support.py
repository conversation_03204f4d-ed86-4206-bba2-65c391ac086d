#!/usr/bin/env python3
"""
JavaScript AST Support for Code Generation

This module provides JavaScript-specific AST manipulation and code generation support.
"""

import ast
import logging
import re
import warnings
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

import astor

from core.code_generation.language_support.base_language_support import (
    ASTNodeInfo,
    BaseLanguageSupport,
    LanguageType,
)

# Suppress astor deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="astor")


logger = logging.getLogger(__name__)


@dataclass
class JavaScriptFunctionInfo:
    """Information about a JavaScript function"""

    name: str
    parameters: List[str]
    is_async: bool = False
    is_export: bool = False
    is_arrow: bool = False
    decorators: List[str] = None


@dataclass
class JavaScriptClassInfo:
    """Information about a JavaScript class"""

    name: str
    extends: Optional[str] = None
    methods: List[JavaScriptFunctionInfo] = None
    properties: List[Dict[str, Any]] = None
    is_export: bool = False
    decorators: List[str] = None


class JavaScriptASTSupport(BaseLanguageSupport):
    """
    JavaScript-specific AST support implementation.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(LanguageType.JAVASCRIPT, config)

    async def create_base_ast(self) -> ast.AST:
        """Create a base JavaScript AST"""
        # For JavaScript, we'll use a simplified AST structure
        # In practice, you'd use a proper JavaScript parser
        return ast.Module(body=[], type_ignores=[])

    async def parse_code(self, code: str) -> ast.AST:
        """Parse JavaScript code string into AST"""
        try:
            # Convert JavaScript syntax to Python-compatible syntax for parsing
            python_code = self._convert_js_to_python(code)
            return ast.parse(python_code)
        except SyntaxError as e:
            logger.error(f"Failed to parse JavaScript code: {e}")
            raise

    async def ast_to_code(self, ast_tree: ast.AST) -> str:
        """Convert JavaScript AST back to code string"""
        try:
            # For now, we'll use astor as a fallback
            # In practice, you'd use a JavaScript code generator
            code = astor.to_source(ast_tree)
            # Convert Python-style code to JavaScript
            return self._convert_to_javascript(code)
        except Exception as e:
            logger.error(f"Failed to convert AST to JavaScript code: {e}")
            raise

    async def add_import(self, ast_tree: ast.AST, import_statement: str) -> ast.AST:
        """Add import statement to JavaScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse the import statement
        try:
            import_ast = ast.parse(import_statement)
            if isinstance(import_ast, ast.Module) and import_ast.body:
                import_node = import_ast.body[0]

                # Check if import already exists
                for existing_node in ast_tree.body:
                    if isinstance(existing_node, type(import_node)):
                        if self._imports_are_equivalent(existing_node, import_node):
                            return ast_tree

                # Add import at the beginning
                ast_tree.body.insert(0, import_node)

        except SyntaxError as e:
            logger.error(f"Invalid import statement: {import_statement}, error: {e}")
            raise

        return ast_tree

    async def add_function(
        self, ast_tree: ast.AST, function_name: str, parameters: List[str], body: str
    ) -> ast.AST:
        """Add function to JavaScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse function body
        try:
            body_ast = ast.parse(body)
            if isinstance(body_ast, ast.Module):
                function_body = body_ast.body
            else:
                function_body = [body_ast]
        except SyntaxError:
            # If body parsing fails, create a simple pass statement
            function_body = [ast.Pass()]

        # Create function arguments
        args = []
        for param in parameters:
            args.append(ast.arg(arg=param.strip()))

        # Create function definition
        function_def = ast.FunctionDef(
            name=function_name,
            args=ast.arguments(
                posonlyargs=[], args=args, kwonlyargs=[], defaults=[], kw_defaults=[]
            ),
            body=function_body,
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(function_def)
        return ast_tree

    async def add_class(
        self, ast_tree: ast.AST, class_name: str, methods: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add class to JavaScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create class body
        class_body = []

        # Add methods
        for method_info in methods:
            method_name = method_info.get("name", "")
            parameters = method_info.get("parameters", [])
            body = method_info.get("body", "pass")

            # Parse method body
            try:
                body_ast = ast.parse(body)
                if isinstance(body_ast, ast.Module):
                    method_body = body_ast.body
                else:
                    method_body = [body_ast]
            except SyntaxError:
                method_body = [ast.Pass()]

            # Create method arguments
            # JavaScript methods have 'this' context
            args = [ast.arg(arg="this")]
            for param in parameters:
                args.append(ast.arg(arg=param.strip()))

            # Create method definition
            method_def = ast.FunctionDef(
                name=method_name,
                args=ast.arguments(
                    posonlyargs=[],
                    args=args,
                    kwonlyargs=[],
                    defaults=[],
                    kw_defaults=[],
                ),
                body=method_body,
                decorator_list=[],
                returns=None,
            )

            class_body.append(method_def)

        # Create class definition
        class_def = ast.ClassDef(
            name=class_name, bases=[], keywords=[], body=class_body, decorator_list=[]
        )

        ast_tree.body.append(class_def)
        return ast_tree

    async def add_variable(
        self,
        ast_tree: ast.AST,
        variable_name: str,
        value: str,
        var_type: Optional[str] = None,
    ) -> ast.AST:
        """Add variable declaration to JavaScript AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse the value
        try:
            value_ast = ast.parse(value, mode="eval")
        except SyntaxError:
            # If parsing fails, use the string as-is
            value_ast = ast.Constant(value=value)

        # Create assignment (JavaScript uses const/let/var)
        assign = ast.Assign(targets=[ast.Name(id=variable_name)], value=value_ast)

        ast_tree.body.append(assign)
        return ast_tree

    async def merge_asts(self, existing_ast: ast.AST, new_ast: ast.AST) -> ast.AST:
        """Merge two JavaScript ASTs"""
        if not isinstance(existing_ast, ast.Module) or not isinstance(
            new_ast, ast.Module
        ):
            raise ValueError("Both ASTs must be Modules")

        # Merge imports first
        existing_imports = []
        existing_others = []

        for node in existing_ast.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                existing_imports.append(node)
            else:
                existing_others.append(node)

        new_imports = []
        new_others = []

        for node in new_ast.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                new_imports.append(node)
            else:
                new_others.append(node)

        # Merge imports, avoiding duplicates
        merged_imports = existing_imports.copy()
        for new_import in new_imports:
            if not any(
                self._imports_are_equivalent(existing, new_import)
                for existing in merged_imports
            ):
                merged_imports.append(new_import)

        # Create merged AST
        merged_ast = ast.Module(
            body=merged_imports + existing_others + new_others, type_ignores=[]
        )

        return merged_ast

    async def validate_ast(self, ast_tree: ast.AST) -> bool:
        """Validate JavaScript AST structure"""
        try:
            # Try to convert back to code to validate
            await self.ast_to_code(ast_tree)
            return True
        except Exception as e:
            logger.error(f"AST validation failed: {e}")
            return False

    async def extract_node_info(self, ast_tree: ast.AST) -> List[ASTNodeInfo]:
        """Extract information about JavaScript AST nodes"""
        node_info_list = []

        for node in ast.walk(ast_tree):
            info = ASTNodeInfo(
                node_type=type(node).__name__,
                line_number=getattr(node, "lineno", None),
                column=getattr(node, "col_offset", None),
            )

            # Extract name for named nodes
            if hasattr(node, "name"):
                info.name = node.name
            elif hasattr(node, "id"):
                info.name = node.id
            elif hasattr(node, "attr"):
                info.name = node.attr

            # Add specific metadata
            if isinstance(node, ast.FunctionDef):
                info.metadata = {
                    "is_async": isinstance(node, ast.AsyncFunctionDef),
                    "decorator_count": len(node.decorator_list),
                    "parameter_count": len(node.args.args),
                }
            elif isinstance(node, ast.ClassDef):
                info.metadata = {
                    "base_count": len(node.bases),
                    "decorator_count": len(node.decorator_list),
                    "method_count": len(
                        [n for n in node.body if isinstance(n, ast.FunctionDef)]
                    ),
                }
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                info.metadata = {
                    "import_type": (
                        "import" if isinstance(node, ast.Import) else "from_import"
                    )
                }

            node_info_list.append(info)

        return node_info_list

    def _imports_are_equivalent(self, import1: ast.AST, import2: ast.AST) -> bool:
        """Check if two import statements are equivalent"""
        if type(import1) != type(import2):
            return False

        if isinstance(import1, ast.Import):
            return import1.names == import2.names
        elif isinstance(import1, ast.ImportFrom):
            return (
                import1.module == import2.module
                and import1.names == import2.names
                and import1.level == import2.level
            )

        return False

    async def _apply_naming_conventions(
        self, ast_tree: ast.AST, naming_style: str
    ) -> ast.AST:
        """Apply JavaScript naming conventions to AST"""
        if naming_style == "camelCase":
            # Convert function and variable names to camelCase
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef) and not self._is_camel_case(
                    node.name
                ):
                    node.name = self._to_camel_case(node.name)
                elif isinstance(node, ast.Name) and not self._is_camel_case(node.id):
                    node.id = self._to_camel_case(node.id)

        return ast_tree

    async def _apply_line_length_conventions(
        self, ast_tree: ast.AST, max_length: int
    ) -> ast.AST:
        """Apply line length conventions to JavaScript AST"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated line breaking logic
        return ast_tree

    def _is_camel_case(self, name: str) -> bool:
        """Check if a name follows camelCase convention"""
        return bool(re.match(r"^[a-z][a-zA-Z0-9]*$", name))

    def _to_camel_case(self, name: str) -> str:
        """Convert a name to camelCase"""
        # Convert snake_case or kebab-case to camelCase
        name = re.sub(r"[-_]", " ", name)
        words = name.split()
        if not words:
            return name
        return words[0].lower() + "".join(word.capitalize() for word in words[1:])

    def _convert_to_javascript(self, python_code: str) -> str:
        """Convert Python-style code to JavaScript"""
        # This is a simplified conversion
        # In practice, you'd need a more sophisticated converter

        # Replace Python-specific syntax
        javascript_code = python_code

        # Convert function definitions
        javascript_code = re.sub(r"def (\w+)\(", r"function \1(", javascript_code)

        # Convert class definitions
        javascript_code = re.sub(r"class (\w+):", r"class \1 {", javascript_code)

        # Convert imports
        javascript_code = re.sub(r"from (\w+) import", r"import {", javascript_code)
        javascript_code = re.sub(r"import (\w+)", r"import \1", javascript_code)

        # Convert variable declarations
        javascript_code = re.sub(r"(\w+): (\w+) =", r"const \1 =", javascript_code)

        # Convert None to null
        javascript_code = javascript_code.replace("None", "null")

        # Convert True/False to true/false
        javascript_code = javascript_code.replace("True", "true")
        javascript_code = javascript_code.replace("False", "false")

        return javascript_code

    async def add_arrow_function(
        self, ast_tree: ast.AST, function_name: str, parameters: List[str], body: str
    ) -> ast.AST:
        """Add arrow function to JavaScript AST"""
        # This is a simplified implementation
        # In practice, you'd need proper JavaScript AST support
        return await self.add_function(ast_tree, function_name, parameters, body)

    async def add_destructuring(
        self, ast_tree: ast.AST, pattern: str, source: str
    ) -> ast.AST:
        """Add destructuring assignment to JavaScript AST"""
        # This is a simplified implementation
        # In practice, you'd need proper JavaScript AST support
        return ast_tree

    async def add_spread_operator(
        self, ast_tree: ast.AST, target: str, source: str
    ) -> ast.AST:
        """Add spread operator to JavaScript AST"""
        # This is a simplified implementation
        # In practice, you'd need proper JavaScript AST support
        return ast_tree

    def _convert_js_to_python(self, js_code: str) -> str:
        """Convert JavaScript code to Python-compatible syntax for parsing"""
        # Basic conversion for parsing purposes
        python_code = js_code

        # Convert JavaScript function declarations to Python
        python_code = re.sub(
            r"function (\w+)\(([^)]*)\)\s*{", r"def \1(\2):", python_code
        )

        # Convert JavaScript imports to Python imports
        python_code = re.sub(
            r'import\s*{\s*(\w+)\s*}\s*from\s*[\'"]([^\'"]+)[\'"]',
            r"from \2 import \1",
            python_code,
        )
        python_code = re.sub(
            r'import\s+(\w+)\s+from\s+[\'"]([^\'"]+)[\'"]',
            r"from \2 import \1",
            python_code,
        )

        # Convert JavaScript null to Python None
        python_code = python_code.replace("null", "None")

        # Convert JavaScript true/false to Python True/False
        python_code = python_code.replace("true", "True")
        python_code = python_code.replace("false", "False")

        # Remove JavaScript-specific syntax
        python_code = python_code.replace("{", ":")
        python_code = python_code.replace("}", "")

        return python_code
