"""
CMS & Content Management System - Phase 2.3
Provides AI-powered content creation, image optimization, and local content management.
"""

import hashlib
import json
import logging
import mimetypes
import os
import re
import shutil
import time
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from PIL import Image, ImageOps

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ImageMetadata:
    """Metadata for image files"""

    width: int
    height: int
    format: str
    size: int
    optimized_size: Optional[int] = None
    thumbnail_path: Optional[str] = None
    alt_text: Optional[str] = None
    caption: Optional[str] = None


@dataclass
class ContentItem:
    """Represents a content item in the CMS"""

    id: str
    title: str
    content: str
    content_type: str
    author: str
    status: str  # published, draft, archived
    tags: List[str]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    version: int = 1
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    image_metadata: Optional[ImageMetadata] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "content_type": self.content_type,
            "author": self.author,
            "status": self.status,
            "tags": self.tags,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "image_metadata": (
                asdict(self.image_metadata) if self.image_metadata else None
            ),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ContentItem":
        """Create from dictionary"""
        return cls(
            id=data["id"],
            title=data["title"],
            content=data["content"],
            content_type=data["content_type"],
            author=data["author"],
            status=data["status"],
            tags=data["tags"],
            metadata=data["metadata"],
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            version=data.get("version", 1),
            file_path=data.get("file_path"),
            file_size=data.get("file_size"),
            image_metadata=(
                ImageMetadata(**data["image_metadata"])
                if data.get("image_metadata")
                else None
            ),
        )


class ContentManager:
    """Main CMS content management class"""

    def __init__(self, config_path: str = "config/cms_config.json"):
        """Initialize the content manager"""
        self.config_path = config_path
        self.config = self._load_config()
        self._ensure_directories()
        self.content_index = {}
        self._load_content_index()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(self.config_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Config file not found: {self.config_path}, using defaults")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "content_directory": "content",
            "media_directory": "content/media",
            "versions_directory": "content/versions",
            "cache_directory": "content/content_cache",
            "max_file_size": 10485760,
            "allowed_image_types": ["jpg", "jpeg", "png", "gif", "webp", "svg"],
            "image_optimization": {
                "max_width": 1920,
                "max_height": 1080,
                "quality": 85,
                "formats": ["webp", "jpeg", "png"],
                "generate_thumbnails": True,
                "thumbnail_size": [300, 200],
            },
            "ai_generation": {
                "enabled": True,
                "provider": "openai",
                "model": "gpt-3.5-turbo",
                "max_tokens": 2000,
                "temperature": 0.7,
            },
            "search": {
                "enabled": True,
                "index_file": "content/search_index.json",
                "update_interval": 300,
            },
            "backup": {
                "enabled": True,
                "directory": "content/backups",
                "interval": 86400,
                "retention_days": 30,
            },
        }

    def _ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.config["content_directory"],
            self.config["media_directory"],
            self.config["versions_directory"],
            self.config["cache_directory"],
        ]

        # Add backup directory if it exists in config
        if "backup" in self.config and "directory" in self.config["backup"]:
            directories.append(self.config["backup"]["directory"])
        else:
            # Use default backup directory
            directories.append("content/backups")

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    def _load_content_index(self):
        """Load content index from file"""
        index_file = Path(self.config["content_directory"]) / "content_index.json"
        if index_file.exists():
            try:
                with open(index_file, "r") as f:
                    data = json.load(f)
                    self.content_index = {
                        content_id: ContentItem.from_dict(item_data)
                        for content_id, item_data in data.items()
                    }
            except Exception as e:
                logger.error(f"Error loading content index: {e}")
                self.content_index = {}
        else:
            self.content_index = {}

    def _save_content_index(self):
        """Save content index to file"""
        index_file = Path(self.config["content_directory"]) / "content_index.json"
        try:
            with open(index_file, "w") as f:
                json.dump(
                    {cid: item.to_dict() for cid, item in self.content_index.items()},
                    f,
                    indent=2,
                )
        except Exception as e:
            logger.error(f"Error saving content index: {e}")

    def _generate_id(self) -> str:
        """Generate unique content ID"""
        return str(uuid.uuid4())[:8]

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        return re.sub(r"[^\w\-_\.]", "_", filename)

    def create_content(
        self,
        title: str,
        content: str,
        content_type: str = "article",
        author: str = "AI Coding Agent",
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> ContentItem:
        """Create new content"""
        content_id = self._generate_id()
        now = datetime.now()

        content_item = ContentItem(
            id=content_id,
            title=title,
            content=content,
            content_type=content_type,
            author=author,
            status="draft",
            tags=tags or [],
            metadata=metadata or {},
            created_at=now,
            updated_at=now,
        )

        # Save content to file
        content_file = Path(self.config["content_directory"]) / f"{content_id}.json"
        with open(content_file, "w") as f:
            json.dump(content_item.to_dict(), f, indent=2)

        # Update index
        self.content_index[content_id] = content_item
        self._save_content_index()

        logger.info(f"Created content: {content_id}")
        return content_item

    def generate_ai_content(
        self,
        topic: str,
        content_type: str = "article",
        length: str = "medium",
        style: str = "informative",
        author: str = "AI Coding Agent",
        tags: Optional[List[str]] = None,
    ) -> ContentItem:
        """Generate content using AI (placeholder implementation)"""
        # This is a placeholder - in a real implementation, this would use an AI API
        content_lengths = {"short": 200, "medium": 500, "long": 1000}

        target_length = content_lengths.get(length, 500)

        # Generate placeholder content
        if content_type == "article":
            content = f"<article><h1>{topic}</h1><p>Test content</p></article>"
        else:
            content = (
                f"<p>AI-generated {content_type} about {topic} in {style} style.</p>"
            )

        metadata = {
            "ai_generated": True,
            "generation_params": {
                "topic": topic,
                "content_type": content_type,
                "length": length,
                "style": style,
            },
        }

        return self.create_content(
            title=f"AI: {topic}",
            content=content,
            content_type=content_type,
            author=author,
            tags=tags or [topic.lower()],
            metadata=metadata,
        )

    def update_content(
        self,
        content_id: str,
        title: Optional[str] = None,
        content: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        author: Optional[str] = None,
    ) -> ContentItem:
        """Update existing content"""
        if content_id not in self.content_index:
            raise ValueError(f"Content not found: {content_id}")

        content_item = self.content_index[content_id]

        # Create version backup
        self._create_version_backup(content_item)

        # Update fields
        if title is not None:
            content_item.title = title
        if content is not None:
            content_item.content = content
        if tags is not None:
            content_item.tags = tags
        if metadata is not None:
            content_item.metadata.update(metadata)
        if author is not None:
            content_item.author = author

        content_item.updated_at = datetime.now()
        content_item.version += 1

        # Save updated content
        content_file = Path(self.config["content_directory"]) / f"{content_id}.json"
        with open(content_file, "w") as f:
            json.dump(content_item.to_dict(), f, indent=2)

        self._save_content_index()
        logger.info(f"Updated content: {content_id}")
        return content_item

    def _create_version_backup(self, content_item: ContentItem):
        """Create a version backup of content"""
        backup_dir = Path(self.config["versions_directory"]) / content_item.id
        backup_dir.mkdir(parents=True, exist_ok=True)

        backup_file = backup_dir / f"v{content_item.version}.json"
        with open(backup_file, "w") as f:
            json.dump(content_item.to_dict(), f, indent=2)

    def publish_content(self, content_id: str) -> ContentItem:
        """Publish content"""
        return self.update_content(content_id, metadata={"status": "published"})

    def archive_content(self, content_id: str) -> ContentItem:
        """Archive content"""
        return self.update_content(content_id, metadata={"status": "archived"})

    def delete_content(self, content_id: str) -> bool:
        """Delete content"""
        if content_id not in self.content_index:
            return False

        # Move to backup instead of deleting
        content_item = self.content_index[content_id]
        backup_dir = Path(self.config["backup"]["directory"])
        backup_file = backup_dir / f"deleted_{content_id}_{int(time.time())}.json"

        with open(backup_file, "w") as f:
            json.dump(content_item.to_dict(), f, indent=2)

        # Remove from index and file system
        del self.content_index[content_id]
        content_file = Path(self.config["content_directory"]) / f"{content_id}.json"
        if content_file.exists():
            content_file.unlink()

        self._save_content_index()
        logger.info(f"Deleted content: {content_id}")
        return True

    def get_content(self, content_id: str) -> Optional[ContentItem]:
        """Get content by ID"""
        return self.content_index.get(content_id)

    def list_content(
        self,
        content_type: Optional[str] = None,
        status: Optional[str] = None,
        author: Optional[str] = None,
        tags: Optional[List[str]] = None,
    ) -> List[ContentItem]:
        """List content with filters"""
        items = list(self.content_index.values())

        if content_type:
            items = [item for item in items if item.content_type == content_type]

        if status:
            items = [item for item in items if item.status == status]

        if author:
            items = [item for item in items if item.author == author]

        if tags:
            items = [item for item in items if any(tag in item.tags for tag in tags)]

        # Sort by updated_at descending
        items.sort(key=lambda x: x.updated_at, reverse=True)
        return items

    def search_content(self, query: str) -> List[ContentItem]:
        """Search content by title, content, or tags"""
        query = query.lower()
        results = []

        for content_item in self.content_index.values():
            if (
                query in content_item.title.lower()
                or query in content_item.content.lower()
                or any(query in tag.lower() for tag in content_item.tags)
            ):
                results.append(content_item)

        return results

    def upload_media(
        self,
        file_path: Union[str, Path],
        title: Optional[str] = None,
        tags: Optional[List[str]] = None,
        optimize: bool = True,
    ) -> ContentItem:
        """Upload and process media file"""
        file_path = Path(file_path) if isinstance(file_path, str) else file_path
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Check file size
        file_size = file_path.stat().st_size
        if file_size > self.config["max_file_size"]:
            raise ValueError(f"File too large: {file_size} bytes")

        # Determine content type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type and mime_type.startswith("image/"):
            content_type = "image"
        else:
            content_type = "file"

        # Generate content ID
        content_id = self._generate_id()

        # Process image if it's an image file
        image_metadata = None
        if content_type == "image":
            image_metadata = self._process_image(file_path, optimize)

        # Copy file to media directory
        dest_filename = f"{content_id}_{self._sanitize_filename(file_path.name)}"
        dest_path = Path(self.config["media_directory"]) / dest_filename
        shutil.copy2(file_path, dest_path)

        # Create content item
        content_item = ContentItem(
            id=content_id,
            title=title or file_path.stem,
            content=f"Media file: {file_path.name}",
            content_type=content_type,
            author="System",
            status="published",
            tags=tags or [],
            metadata={
                "original_filename": file_path.name,
                "mime_type": mime_type,
                "file_extension": file_path.suffix,
                "original_size": file_size,
                "optimized": optimize,
            },
            created_at=datetime.now(),
            updated_at=datetime.now(),
            file_path=str(dest_path),
            file_size=file_size,
            image_metadata=image_metadata,
        )

        # Add to index
        self.content_index[content_id] = content_item
        self._save_content_index()

        logger.info(f"Uploaded media: {content_id}")
        return content_item

    def _process_image(self, file_path: Path, optimize: bool = True) -> ImageMetadata:
        """Process and optimize image"""
        with Image.open(file_path) as img:
            width, height = img.size
            original_size = file_path.stat().st_size

            metadata = ImageMetadata(
                width=width,
                height=height,
                format=img.format or "unknown",
                size=original_size,
            )

            if optimize:
                # Create optimized version
                optimized_path = (
                    Path(self.config["cache_directory"]) / f"opt_{file_path.name}"
                )

                # Resize if too large
                max_width = self.config["image_optimization"]["max_width"]
                max_height = self.config["image_optimization"]["max_height"]

                if width > max_width or height > max_height:
                    img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

                # Save optimized version
                quality = self.config["image_optimization"]["quality"]
                img.save(optimized_path, optimize=True, quality=quality)

                metadata.optimized_size = optimized_path.stat().st_size

                # Create thumbnail
                if self.config["image_optimization"]["generate_thumbnails"]:
                    thumb_width, thumb_height = self.config["image_optimization"][
                        "thumbnail_size"
                    ]
                    thumb = img.copy()
                    thumb.thumbnail(
                        (thumb_width, thumb_height), Image.Resampling.LANCZOS
                    )

                    thumb_path = (
                        Path(self.config["cache_directory"]) / f"thumb_{file_path.name}"
                    )
                    thumb.save(thumb_path, optimize=True, quality=85)
                    metadata.thumbnail_path = str(thumb_path)

            return metadata

    def get_content_statistics(self) -> Dict[str, Any]:
        """Get content statistics"""
        items = list(self.content_index.values())

        total_size = sum(item.file_size or 0 for item in items)

        type_counts = {}
        author_counts = {}
        status_counts = {"published": 0, "draft": 0, "archived": 0}

        for item in items:
            type_counts[item.content_type] = type_counts.get(item.content_type, 0) + 1
            author_counts[item.author] = author_counts.get(item.author, 0) + 1
            status_counts[item.status] = status_counts.get(item.status, 0) + 1

        return {
            "total_items": len(items),
            "total_size_mb": total_size / (1024 * 1024),
            "type_counts": type_counts,
            "author_counts": author_counts,
            "published_items": status_counts.get("published", 0),
            "draft_items": status_counts.get("draft", 0),
            "archived_items": status_counts.get("archived", 0),
        }


# Example usage
if __name__ == "__main__":
    # Initialize content manager
    cms = ContentManager()

    # Create sample content
    content = cms.create_content(
        title="Welcome to AI Coding Agent CMS",
        content="<h1>Welcome</h1><p>This is a sample article.</p>",
        content_type="article",
        tags=["welcome", "sample"],
    )

    print(f"Created content: {content.id}")

    # List all content
    all_content = cms.list_content()
    print(f"Total content items: {len(all_content)}")

    # Get statistics
    stats = cms.get_content_statistics()
    print("Content statistics:", stats)
