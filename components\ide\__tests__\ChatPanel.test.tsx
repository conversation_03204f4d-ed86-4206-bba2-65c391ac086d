/**
 * ChatPanel Component Tests
 * Comprehensive unit tests for ChatPanel component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ChatPanel } from '../ChatPanel';
import mockUseAuth from './mockUseAuth.mock';

// Mock dependencies

jest.mock('@/services/AIService', () => ({
  aiService: {
    generateResponse: jest.fn(),
    updateContext: jest.fn(),
  },
}));

jest.mock('@/services/IntentRecognition', () => ({
  intentRecognition: {
    parseCommand: jest.fn(),
  },
}));

jest.mock('@/services/ConversationManager', () => ({
  conversationManager: {
    startSession: jest.fn(),
    addTurn: jest.fn(),
    getContextForAI: jest.fn(() => ({ conversationHistory: [] })),
    suggestNextActions: jest.fn(() => ['Suggestion 1', 'Suggestion 2']),
  },
}));

jest.mock('@/services/PromptEnhancer', () => ({
  promptEnhancer: {
    detectEnhancementMode: jest.fn(() => 'code_generation'),
    enhancePrompt: jest.fn(() => ({ enhanced: 'Enhanced prompt', confidence: 0.8 })),
  },
}));

jest.mock('@/services/FileManager', () => ({
  getFileManager: jest.fn(() => ({
    getAllFiles: jest.fn(() => [
      { id: '1', name: 'test.js', content: 'console.log("test");', isActive: true },
    ]),
  })),
}));

let mockUseAuthImpl = () => ({ isAuthenticated: true, isLoading: false });

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: (...args: any[]) => mockUseAuthImpl(...(args as [])),
  __esModule: true,
}));

jest.mock('@/components/auth/LoginModal', () => ({
  LoginModal: ({ isOpen, onClose, onSuccess }: any) =>
    isOpen ? <div data-testid="login-modal">Login Modal</div> : null,
}));

jest.mock('@/components/ide/EnhanceButton', () => ({
  EnhanceButtonWithPrompt: ({ onEnhance, disabled }: any) => (
    <button
      data-testid="enhance-button"
      onClick={() => onEnhance('Enhanced prompt')}
      disabled={disabled}
    >
      Enhance
    </button>
  ),
}));

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe('ChatPanel Component', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuthImpl = () => ({ isAuthenticated: true, isLoading: false });
  });

  // In tests that require unauthenticated state, set:
  // mockUseAuthImpl = () => ({ isAuthenticated: false, isLoading: false });

  describe('Component Rendering', () => {
    it('should render the chat panel with initial AI message', () => {
      render(<ChatPanel />);

      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
      expect(screen.getByText(/Hello! I'm your AI coding assistant/)).toBeInTheDocument();
      expect(screen.getByText('Create a photography portfolio website')).toBeInTheDocument();
    });

    it('should render with custom className', () => {
      render(<ChatPanel className="custom-class" />);

      const container = screen.getByRole('region', { name: 'AI Chat Assistant' });
      expect(container).toHaveClass('custom-class');
    });

    it('should render settings button', () => {
      render(<ChatPanel />);

      expect(screen.getByRole('button', { name: 'Toggle chat settings' })).toBeInTheDocument();
    });

    it('should render chat input area', () => {
      render(<ChatPanel />);

      expect(screen.getByRole('textbox', { name: 'Chat message input' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Send message' })).toBeInTheDocument();
    });
  });

  describe('User Message Input Handling', () => {
    it('should update input value when typing', async () => {
      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');

      expect(input).toHaveValue('Hello AI');
    });

    it('should send message when Enter is pressed', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByText('Hello AI')).toBeInTheDocument();
      });
    });

    it('should not send empty messages', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      render(<ChatPanel />);
      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, '   '); // Empty message
      fireEvent.keyPress(input, { key: 'Enter', code: 13, charCode: 13 });

      await waitFor(() => {
        expect(mockGenerateResponse).not.toHaveBeenCalled();
      });
    });

    it('should clear input after sending message', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(input).toHaveValue('');
      });
    });
  });

  describe('Loading State Management', () => {
    it('should show loading state while AI is responding', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ content: 'AI response' }), 100)));

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      expect(screen.getByText('AI is thinking...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Send message' })).toBeDisabled();
    });

    it('should disable input while loading', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ content: 'AI response' }), 100)));

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      expect(input).toBeDisabled();
    });
  });

  describe('Error Message Display', () => {
    it('should show error message when AI service fails', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockRejectedValue(new Error('AI service error'));

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(screen.getByText('AI is unavailable. Please try again later.')).toBeInTheDocument();
      });
    });

    it('should show authentication error for 401 responses', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockRejectedValue(new Error('401 Unauthorized'));

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(screen.getByText('Please log in to use the AI chat feature.')).toBeInTheDocument();
      });
    });
  });

  describe('Auto-scroll Behavior', () => {
    it('should scroll to bottom when new messages are added', async () => {
      const mockScrollIntoView = jest.fn();
      Element.prototype.scrollIntoView = mockScrollIntoView;

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });
      });
    });
  });

  describe('Suggestion Handling', () => {
    it('should display AI message suggestions', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(screen.getByText('Suggestion 1')).toBeInTheDocument();
        expect(screen.getByText('Suggestion 2')).toBeInTheDocument();
      });
    });

    it('should fill input when suggestion is clicked', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        const suggestion = screen.getByText('Suggestion 1');
        user.click(suggestion);
      });

      await waitFor(() => {
        expect(screen.getByRole('textbox', { name: 'Chat message input' })).toHaveValue('Suggestion 1');
      });
    });
  });

  describe('Prompt Enhancement Integration', () => {
    it('should enhance prompt when auto-enhance is enabled', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      const mockEnhancePrompt = require('@/services/PromptEnhancer').promptEnhancer.enhancePrompt;
      mockEnhancePrompt.mockResolvedValue({ enhanced: 'Enhanced prompt', confidence: 0.8 });

      render(<ChatPanel />);

      // Enable auto-enhance
      const settingsButton = screen.getByRole('button', { name: 'Toggle chat settings' });
      await user.click(settingsButton);

      const autoEnhanceCheckbox = screen.getByRole('checkbox', { name: /Auto-enhance prompts/ });
      await user.click(autoEnhanceCheckbox);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(mockEnhancePrompt).toHaveBeenCalledWith('Hello AI', 'code_generation');
      });
    });

    it('should use enhanced prompt when confidence is high', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      const mockEnhancePrompt = require('@/services/PromptEnhancer').promptEnhancer.enhancePrompt;
      mockEnhancePrompt.mockResolvedValue({ enhanced: 'Enhanced prompt', confidence: 0.8 });

      render(<ChatPanel />);

      // Enable auto-enhance
      const settingsButton = screen.getByRole('button', { name: 'Toggle chat settings' });
      await user.click(settingsButton);

      const autoEnhanceCheckbox = screen.getByRole('checkbox', { name: /Auto-enhance prompts/ });
      await user.click(autoEnhanceCheckbox);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(mockGenerateResponse).toHaveBeenCalledWith(
          'Enhanced prompt',
          expect.any(String),
          undefined,
          expect.any(String),
          expect.any(Object),
          expect.any(Array)
        );
      });
    });
  });

  describe('Code Context Injection', () => {
    it('should inject active file content when intent is code-related', async () => {
      const mockGenerateResponse = require('@/services/AIService').aiService.generateResponse;
      mockGenerateResponse.mockResolvedValue({ content: 'AI response' });

      const mockParseCommand = require('@/services/IntentRecognition').intentRecognition.parseCommand;
      mockParseCommand.mockReturnValue({ intent: { type: 'create' } });

      const mockDetectEnhancementMode = require('@/services/PromptEnhancer').promptEnhancer.detectEnhancementMode;
      mockDetectEnhancementMode.mockReturnValue('code_generation');

      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');
      await user.click(screen.getByRole('button', { name: 'Send message' }));

      await waitFor(() => {
        expect(mockGenerateResponse).toHaveBeenCalledWith(
          expect.any(String),
          expect.any(String),
          undefined,
          'console.log("test");',
          expect.any(Object),
          expect.any(Array)
        );
      });
    });
  });

  describe('Authentication Integration', () => {
    it('should show login modal when user is not authenticated', () => {
      mockUseAuthImpl = () => ({ isAuthenticated: false, isLoading: false });

      render(<ChatPanel />);

      expect(screen.getByText('Login Required')).toBeInTheDocument();
      expect(screen.getByText('Please log in to use the AI chat feature and access your projects.')).toBeInTheDocument();
    });

    describe('Message Sending', () => {
      it('should show login modal when trying to send message while not authenticated', async () => {
        mockUseAuthImpl = () => ({ isAuthenticated: false, isLoading: false });

        render(<ChatPanel />);

        const loginButton = screen.getByRole('button', { name: 'Login' });
        expect(loginButton).toBeInTheDocument();
      });
    });
  });

  describe('Settings Panel', () => {
    it('should toggle settings panel when settings button is clicked', async () => {
      render(<ChatPanel />);

      const settingsButton = screen.getByRole('button', { name: 'Toggle chat settings' });
      await user.click(settingsButton);

      expect(screen.getByText('Auto-enhance prompts')).toBeInTheDocument();

      await user.click(settingsButton);

      expect(screen.queryByText('Auto-enhance prompts')).not.toBeInTheDocument();
    });

    it('should toggle auto-enhance setting', async () => {
      render(<ChatPanel />);

      const settingsButton = screen.getByRole('button', { name: 'Toggle chat settings' });
      await user.click(settingsButton);

      const autoEnhanceCheckbox = screen.getByRole('checkbox', { name: /Auto-enhance prompts/ });
      expect(autoEnhanceCheckbox).not.toBeChecked();

      await user.click(autoEnhanceCheckbox);
      expect(autoEnhanceCheckbox).toBeChecked();
    });
  });

  describe('Enhance Button Integration', () => {
    it('should enhance prompt when enhance button is clicked', async () => {
      render(<ChatPanel />);

      const input = screen.getByRole('textbox', { name: 'Chat message input' });
      await user.type(input, 'Hello AI');

      const enhanceButton = screen.getByTestId('enhance-button');
      await user.click(enhanceButton);

      expect(input).toHaveValue('Enhanced prompt');
    });

    it('should disable enhance button when input is empty', () => {
      render(<ChatPanel />);

      const enhanceButton = screen.getByTestId('enhance-button');
      expect(enhanceButton).toBeDisabled();
    });
  });
});
