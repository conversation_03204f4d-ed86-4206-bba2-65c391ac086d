{"version": "1.0.0", "deployments": [{"deployment_id": "integration-test_20250804_174125", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T17:41:25.719359", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T17:41:25.719941", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T17:41:25.721144", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T17:41:25.721780"}, {"deployment_id": "integration-test_20250804_174337", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T17:43:37.179713", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T17:43:37.181450", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T17:43:37.183929", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T17:43:37.184642"}, {"deployment_id": "integration-test_20250804_175054", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T17:50:54.846403", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T17:50:54.847185", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T17:50:54.848512", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T17:50:54.849218"}, {"deployment_id": "integration-test_20250804_175519", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T17:55:19.156729", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T17:55:19.157372", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T17:55:19.158577", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T17:55:19.159385"}, {"deployment_id": "integration-test_20250804_175858", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T17:58:58.151393", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T17:58:58.152150", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T17:58:58.153591", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T17:58:58.154447"}, {"deployment_id": "integration-test_20250804_181210", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T18:12:10.979854", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T18:12:10.981631", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T18:12:10.983976", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T18:12:10.985643"}, {"deployment_id": "integration-test_20250804_223120", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-04T22:31:20.350877", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-04T22:31:20.351940", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-04T22:31:20.354087", "details": {}}], "rollback_available": true, "end_time": "2025-08-04T22:31:20.355114"}, {"deployment_id": "integration-test_20250805_010626", "site_name": "integration-test", "config": {}, "status": "failed", "start_time": "2025-08-05T01:06:26.982070", "steps": [{"name": "pre_validation", "status": "running", "timestamp": "2025-08-05T01:06:26.983527", "details": {}}, {"name": "pre_validation", "status": "completed", "timestamp": "2025-08-05T01:06:26.985268", "details": {}}], "rollback_available": true, "end_time": "2025-08-05T01:06:26.988090"}], "rollbacks": [], "current_deployment": null, "pipeline_status": "idle", "last_updated": "2025-08-05T01:06:26.988102"}