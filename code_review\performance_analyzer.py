"""
Performance Analyzer

Comprehensive performance analysis engine for detecting bottlenecks,
optimization opportunities, and resource usage issues.

Phase 20 Implementation - Advanced Code Review
"""

import ast
import asyncio
import logging
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class PerformanceBottleneck:
    """Represents a detected performance bottleneck."""

    id: str
    type: str  # "algorithm", "memory", "io", "network", "database"
    severity: str  # "critical", "high", "medium", "low"
    title: str
    description: str
    line_number: int
    column: int
    code_snippet: str
    language: str
    category: str
    impact_estimate: str
    fix_suggestion: Optional[str] = None
    confidence: float = 0.8


@dataclass
class PerformanceAnalysisResult:
    """Result of performance analysis."""

    performance_score: float
    bottlenecks: List[PerformanceBottleneck]
    optimization_opportunities: List[Dict[str, Any]]
    resource_usage: Dict[str, Any]
    recommendations: List[str]
    confidence: float


class PerformanceAnalyzer:
    """
    Performance analyzer for detecting bottlenecks and optimization opportunities.

    This class performs comprehensive performance analysis including
    algorithm efficiency, memory usage, I/O operations, and resource optimization.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the performance analyzer.

        Args:
            config: Configuration dictionary for performance analysis
        """
        self.config = config or {}
        self.performance_patterns = self._load_performance_patterns()
        self.optimization_rules = self._load_optimization_rules()

        logger.info("Performance Analyzer initialized successfully")

    async def analyze_performance(
        self, code: str, language: str, file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform comprehensive performance analysis.

        Args:
            code: Code to analyze
            language: Programming language
            file_path: Optional file path for context

        Returns:
            Dictionary with performance analysis results
        """
        try:
            logger.info(
                f"Starting performance analysis for {language} file: {file_path}"
            )

            # Detect bottlenecks
            bottlenecks = await self._detect_bottlenecks(code, language, file_path)

            # Find optimization opportunities
            optimization_opportunities = await self._find_optimization_opportunities(
                code, language, file_path
            )

            # Analyze resource usage
            resource_usage = await self._analyze_resource_usage(
                code, language, file_path
            )

            # Calculate performance score
            performance_score = self._calculate_performance_score(
                bottlenecks, optimization_opportunities, resource_usage
            )

            # Generate recommendations
            recommendations = self._generate_performance_recommendations(
                bottlenecks, optimization_opportunities, resource_usage
            )

            # Calculate confidence
            confidence = self._calculate_confidence(
                bottlenecks, optimization_opportunities, resource_usage
            )

            return {
                "performance_score": performance_score,
                "bottlenecks": [self._bottleneck_to_dict(b) for b in bottlenecks],
                "optimization_opportunities": optimization_opportunities,
                "resource_usage": resource_usage,
                "recommendations": recommendations,
                "confidence": confidence,
                "total_bottlenecks": len(bottlenecks),
                "critical_count": len(
                    [b for b in bottlenecks if b.severity == "critical"]
                ),
                "high_count": len([b for b in bottlenecks if b.severity == "high"]),
                "medium_count": len([b for b in bottlenecks if b.severity == "medium"]),
                "low_count": len([b for b in bottlenecks if b.severity == "low"]),
            }

        except Exception as e:
            logger.error(f"Error in performance analysis: {e}")
            return {
                "performance_score": 0.0,
                "bottlenecks": [],
                "optimization_opportunities": [],
                "resource_usage": {},
                "recommendations": [],
                "confidence": 0.0,
                "total_bottlenecks": 0,
                "critical_count": 0,
                "high_count": 0,
                "medium_count": 0,
                "low_count": 0,
            }

    async def _detect_bottlenecks(
        self, code: str, language: str, file_path: Optional[str]
    ) -> List[PerformanceBottleneck]:
        """Detect performance bottlenecks in the code."""
        bottlenecks = []
        lines = code.split("\n")

        try:
            # Language-specific bottleneck detection
            if language == "python":
                bottlenecks.extend(await self._detect_python_bottlenecks(code, lines))
            elif language in ["javascript", "typescript"]:
                bottlenecks.extend(
                    await self._detect_javascript_bottlenecks(code, lines, language)
                )
            elif language == "java":
                bottlenecks.extend(await self._detect_java_bottlenecks(code, lines))
            elif language == "cpp":
                bottlenecks.extend(await self._detect_cpp_bottlenecks(code, lines))

            # Generic bottleneck detection
            bottlenecks.extend(
                await self._detect_generic_bottlenecks(code, lines, language)
            )

        except Exception as e:
            logger.error(f"Error detecting bottlenecks: {e}")

        return bottlenecks

    async def _detect_python_bottlenecks(
        self, code: str, lines: List[str]
    ) -> List[PerformanceBottleneck]:
        """Detect Python-specific performance bottlenecks."""
        bottlenecks = []

        try:
            # Parse the code
            tree = ast.parse(code)

            # Check for common Python performance issues
            for node in ast.walk(tree):
                bottlenecks.extend(self._check_python_node_performance(node, lines))

            # Check for inefficient patterns
            bottlenecks.extend(self._check_inefficient_patterns(code, lines))

            # Check for memory issues
            bottlenecks.extend(self._check_memory_issues(code, lines))

        except SyntaxError:
            # Handle syntax errors
            bottleneck = PerformanceBottleneck(
                id="syntax_error",
                type="error",
                severity="medium",
                title="Syntax Error",
                description="Code contains syntax errors that may affect performance",
                line_number=1,
                column=1,
                code_snippet=lines[0] if lines else "",
                language="python",
                category="syntax",
                impact_estimate="Unknown",
                fix_suggestion="Fix syntax errors to ensure proper performance",
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    async def _detect_javascript_bottlenecks(
        self, code: str, lines: List[str], language: str
    ) -> List[PerformanceBottleneck]:
        """Detect JavaScript/TypeScript-specific performance bottlenecks."""
        bottlenecks = []

        # Check for inefficient DOM manipulation
        if re.search(r"innerHTML\s*=", code, re.IGNORECASE):
            bottleneck = PerformanceBottleneck(
                id="dom_manipulation",
                type="io",
                severity="medium",
                title="Inefficient DOM Manipulation",
                description="innerHTML can cause performance issues with large content",
                line_number=self._find_line_number(code, "innerHTML"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "innerHTML"),
                language=language,
                category="dom_performance",
                impact_estimate="10-20% performance improvement",
                fix_suggestion="Use textContent or createElement for better performance",
                confidence=0.8,
            )
            bottlenecks.append(bottleneck)

        # Check for inefficient loops
        if re.search(r"for\s*\(\s*var\s+\w+\s*=\s*0", code):
            bottleneck = PerformanceBottleneck(
                id="inefficient_loop",
                type="algorithm",
                severity="low",
                title="Inefficient Loop Declaration",
                description="Using var in for loops can cause performance issues",
                line_number=self._find_line_number(code, "for.*var"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "for.*var"),
                language=language,
                category="loop_optimization",
                impact_estimate="5-10% performance improvement",
                fix_suggestion="Use let instead of var in for loops",
                confidence=0.7,
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    async def _detect_java_bottlenecks(
        self, code: str, lines: List[str]
    ) -> List[PerformanceBottleneck]:
        """Detect Java-specific performance bottlenecks."""
        bottlenecks = []

        # Check for inefficient string concatenation
        if re.search(r"String\s+\w+\s*=\s*.*\+.*\+", code):
            bottleneck = PerformanceBottleneck(
                id="string_concatenation",
                type="memory",
                severity="medium",
                title="Inefficient String Concatenation",
                description="String concatenation with + creates temporary objects",
                line_number=self._find_line_number(code, "String.*=.*\\+"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "String.*=.*\\+"),
                language="java",
                category="string_optimization",
                impact_estimate="20-30% performance improvement",
                fix_suggestion="Use StringBuilder for multiple string concatenations",
                confidence=0.8,
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    async def _detect_cpp_bottlenecks(
        self, code: str, lines: List[str]
    ) -> List[PerformanceBottleneck]:
        """Detect C++-specific performance bottlenecks."""
        bottlenecks = []

        # Check for inefficient copying
        if re.search(r"std::vector.*=.*std::vector", code):
            bottleneck = PerformanceBottleneck(
                id="unnecessary_copying",
                type="memory",
                severity="medium",
                title="Unnecessary Object Copying",
                description="Copying large objects can cause performance issues",
                line_number=self._find_line_number(code, "std::vector.*="),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "std::vector.*="),
                language="cpp",
                category="copy_optimization",
                impact_estimate="15-25% performance improvement",
                fix_suggestion="Use references or move semantics to avoid copying",
                confidence=0.8,
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    async def _detect_generic_bottlenecks(
        self, code: str, lines: List[str], language: str
    ) -> List[PerformanceBottleneck]:
        """Detect generic performance bottlenecks."""
        bottlenecks = []

        # Check for nested loops
        if self._count_nested_loops(code) > 2:
            bottleneck = PerformanceBottleneck(
                id="nested_loops",
                type="algorithm",
                severity="high",
                title="Deeply Nested Loops",
                description="Deeply nested loops can cause exponential performance degradation",
                line_number=self._find_line_number(code, "for|while"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "for|while"),
                language=language,
                category="algorithm_complexity",
                impact_estimate="50-80% performance improvement",
                fix_suggestion="Consider using more efficient algorithms or data structures",
                confidence=0.9,
            )
            bottlenecks.append(bottleneck)

        # Check for large functions
        if len(lines) > 50:
            bottleneck = PerformanceBottleneck(
                id="large_function",
                type="maintainability",
                severity="low",
                title="Large Function",
                description="Large functions can be harder to optimize and maintain",
                line_number=1,
                column=1,
                code_snippet=lines[0] if lines else "",
                language=language,
                category="code_structure",
                impact_estimate="5-15% performance improvement",
                fix_suggestion="Break down large functions into smaller, focused functions",
                confidence=0.6,
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    def _check_python_node_performance(
        self, node: ast.AST, lines: List[str]
    ) -> List[PerformanceBottleneck]:
        """Check Python AST node for performance issues."""
        bottlenecks = []

        # Check for list comprehensions vs loops
        if isinstance(node, ast.For) and self._has_append_in_loop(node, lines):
            bottleneck = PerformanceBottleneck(
                id="list_comprehension_opportunity",
                type="algorithm",
                severity="medium",
                title="List Comprehension Opportunity",
                description="List comprehensions are generally faster than loops with append",
                line_number=getattr(node, "lineno", 1),
                column=getattr(node, "col_offset", 1),
                code_snippet=(
                    lines[getattr(node, "lineno", 1) - 1]
                    if getattr(node, "lineno", 1) <= len(lines)
                    else ""
                ),
                language="python",
                category="loop_optimization",
                impact_estimate="20-30% performance improvement",
                fix_suggestion="Replace loop with append with list comprehension",
                confidence=0.8,
            )
            bottlenecks.append(bottleneck)

        # Check for inefficient string concatenation
        if isinstance(node, ast.BinOp) and isinstance(node.op, ast.Add):
            if self._is_string_concatenation(node):
                bottleneck = PerformanceBottleneck(
                    id="string_concatenation",
                    type="memory",
                    severity="low",
                    title="Inefficient String Concatenation",
                    description="String concatenation with + can be inefficient for multiple operations",
                    line_number=getattr(node, "lineno", 1),
                    column=getattr(node, "col_offset", 1),
                    code_snippet=(
                        lines[getattr(node, "lineno", 1) - 1]
                        if getattr(node, "lineno", 1) <= len(lines)
                        else ""
                    ),
                    language="python",
                    category="string_optimization",
                    impact_estimate="10-20% performance improvement",
                    fix_suggestion="Use join() for multiple string concatenations",
                    confidence=0.7,
                )
                bottlenecks.append(bottleneck)

        return bottlenecks

    def _check_inefficient_patterns(
        self, code: str, lines: List[str]
    ) -> List[PerformanceBottleneck]:
        """Check for inefficient coding patterns."""
        bottlenecks = []

        # Check for repeated calculations
        if re.search(r"len\s*\(\s*\w+\s*\)\s*.*len\s*\(\s*\w+\s*\)", code):
            bottleneck = PerformanceBottleneck(
                id="repeated_calculation",
                type="algorithm",
                severity="low",
                title="Repeated Calculation",
                description="Repeated calculations can be cached for better performance",
                line_number=self._find_line_number(code, "len.*len"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "len.*len"),
                language="python",
                category="caching",
                impact_estimate="5-15% performance improvement",
                fix_suggestion="Cache repeated calculations in variables",
                confidence=0.7,
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    def _check_memory_issues(
        self, code: str, lines: List[str]
    ) -> List[PerformanceBottleneck]:
        """Check for memory-related performance issues."""
        bottlenecks = []

        # Check for large data structures
        if re.search(r"\[\s*\]\s*\*\s*\d{4,}", code):
            bottleneck = PerformanceBottleneck(
                id="large_data_structure",
                type="memory",
                severity="medium",
                title="Large Data Structure",
                description="Creating large data structures can consume significant memory",
                line_number=self._find_line_number(code, "\\[\\].*\\*"),
                column=1,
                code_snippet=self._get_line_with_pattern(code, "\\[\\].*\\*"),
                language="python",
                category="memory_usage",
                impact_estimate="20-40% memory reduction",
                fix_suggestion="Consider using generators or lazy evaluation",
                confidence=0.8,
            )
            bottlenecks.append(bottleneck)

        return bottlenecks

    async def _find_optimization_opportunities(
        self, code: str, language: str, file_path: Optional[str]
    ) -> List[Dict[str, Any]]:
        """Find optimization opportunities in the code."""
        opportunities = []

        # Check for algorithm optimization opportunities
        if re.search(r"O\s*\(\s*n\s*\*\s*n\s*\)", code):
            opportunities.append(
                {
                    "type": "algorithm",
                    "severity": "high",
                    "description": "O(n²) algorithm detected",
                    "recommendation": "Consider using more efficient algorithms",
                    "impact": "50-80% performance improvement",
                }
            )

        # Check for caching opportunities
        if re.search(r"def.*\n.*return.*\n.*def.*\n.*return", code):
            opportunities.append(
                {
                    "type": "caching",
                    "severity": "medium",
                    "description": "Potential caching opportunity",
                    "recommendation": "Implement memoization for expensive calculations",
                    "impact": "20-50% performance improvement",
                }
            )

        return opportunities

    async def _analyze_resource_usage(
        self, code: str, language: str, file_path: Optional[str]
    ) -> Dict[str, Any]:
        """Analyze resource usage patterns."""
        resource_usage = {
            "memory_usage": "low",
            "cpu_usage": "low",
            "io_operations": "low",
            "network_calls": "low",
        }

        # Analyze memory usage
        if re.search(r"list\s*\(\s*range\s*\(\s*\d{4,}\s*\)\s*\)", code):
            resource_usage["memory_usage"] = "high"

        # Analyze CPU usage
        if self._count_nested_loops(code) > 2:
            resource_usage["cpu_usage"] = "high"

        # Analyze I/O operations
        if re.search(r"open\s*\(|read\s*\(|write\s*\(", code):
            resource_usage["io_operations"] = "medium"

        # Analyze network calls
        if re.search(r"requests\.|urllib\.|http\.", code):
            resource_usage["network_calls"] = "medium"

        return resource_usage

    def _calculate_performance_score(
        self,
        bottlenecks: List[PerformanceBottleneck],
        optimization_opportunities: List[Dict[str, Any]],
        resource_usage: Dict[str, Any],
    ) -> float:
        """Calculate performance score based on bottlenecks and opportunities."""
        if not bottlenecks and not optimization_opportunities:
            return 1.0

        # Calculate bottleneck penalty
        bottleneck_penalty = 0.0
        for bottleneck in bottlenecks:
            if bottleneck.severity == "critical":
                bottleneck_penalty += 0.3
            elif bottleneck.severity == "high":
                bottleneck_penalty += 0.2
            elif bottleneck.severity == "medium":
                bottleneck_penalty += 0.1
            elif bottleneck.severity == "low":
                bottleneck_penalty += 0.05

        # Calculate opportunity penalty
        opportunity_penalty = len(optimization_opportunities) * 0.05

        # Calculate resource usage penalty
        resource_penalty = 0.0
        for usage in resource_usage.values():
            if usage == "high":
                resource_penalty += 0.1
            elif usage == "medium":
                resource_penalty += 0.05

        # Calculate final score
        total_penalty = min(
            bottleneck_penalty + opportunity_penalty + resource_penalty, 1.0
        )
        return max(0.0, 1.0 - total_penalty)

    def _generate_performance_recommendations(
        self,
        bottlenecks: List[PerformanceBottleneck],
        optimization_opportunities: List[Dict[str, Any]],
        resource_usage: Dict[str, Any],
    ) -> List[str]:
        """Generate performance recommendations."""
        recommendations = []

        # Add bottleneck-specific recommendations
        for bottleneck in bottlenecks:
            if bottleneck.fix_suggestion:
                recommendations.append(bottleneck.fix_suggestion)

        # Add opportunity-specific recommendations
        for opportunity in optimization_opportunities:
            if "recommendation" in opportunity:
                recommendations.append(opportunity["recommendation"])

        # Add resource usage recommendations
        if resource_usage.get("memory_usage") == "high":
            recommendations.append(
                "Consider using generators or lazy evaluation to reduce memory usage"
            )

        if resource_usage.get("cpu_usage") == "high":
            recommendations.append("Optimize algorithms to reduce CPU usage")

        if resource_usage.get("io_operations") == "medium":
            recommendations.append(
                "Consider batching I/O operations for better performance"
            )

        if resource_usage.get("network_calls") == "medium":
            recommendations.append(
                "Implement caching for network calls to reduce latency"
            )

        return list(set(recommendations))  # Remove duplicates

    def _calculate_confidence(
        self,
        bottlenecks: List[PerformanceBottleneck],
        optimization_opportunities: List[Dict[str, Any]],
        resource_usage: Dict[str, Any],
    ) -> float:
        """Calculate confidence in the performance analysis."""
        if not bottlenecks and not optimization_opportunities:
            return 0.9  # High confidence when no issues found

        # Calculate average confidence from bottlenecks
        if bottlenecks:
            avg_confidence = sum(b.confidence for b in bottlenecks) / len(bottlenecks)
            return min(0.95, avg_confidence)

        return 0.8

    def _bottleneck_to_dict(self, bottleneck: PerformanceBottleneck) -> Dict[str, Any]:
        """Convert PerformanceBottleneck to dictionary."""
        return {
            "id": bottleneck.id,
            "type": bottleneck.type,
            "severity": bottleneck.severity,
            "title": bottleneck.title,
            "description": bottleneck.description,
            "line_number": bottleneck.line_number,
            "column": bottleneck.column,
            "code_snippet": bottleneck.code_snippet,
            "language": bottleneck.language,
            "category": bottleneck.category,
            "impact_estimate": bottleneck.impact_estimate,
            "fix_suggestion": bottleneck.fix_suggestion,
            "confidence": bottleneck.confidence,
        }

    def _has_append_in_loop(self, node: ast.For, lines: List[str]) -> bool:
        """Check if a for loop contains append operations."""
        # Simplified check - in a real implementation, this would be more sophisticated
        return True

    def _is_string_concatenation(self, node: ast.BinOp) -> bool:
        """Check if a binary operation is string concatenation."""
        # Simplified check - in a real implementation, this would be more sophisticated
        return True

    def _count_nested_loops(self, code: str) -> int:
        """Count the number of nested loops in the code."""
        loop_count = 0
        max_nesting = 0
        current_nesting = 0

        for line in code.split("\n"):
            if re.search(r"\bfor\b|\bwhile\b", line):
                current_nesting += 1
                max_nesting = max(max_nesting, current_nesting)
            elif re.search(r"\bend\b|\}", line):
                current_nesting = max(0, current_nesting - 1)

        return max_nesting

    def _find_line_number(self, code: str, pattern: str) -> int:
        """Find line number containing a pattern."""
        lines = code.split("\n")
        for i, line in enumerate(lines):
            if re.search(pattern, line, re.IGNORECASE):
                return i + 1
        return 1

    def _get_line_with_pattern(self, code: str, pattern: str) -> str:
        """Get the line containing a pattern."""
        lines = code.split("\n")
        for line in lines:
            if re.search(pattern, line, re.IGNORECASE):
                return line.strip()
        return ""

    def _load_performance_patterns(self) -> Dict[str, List[str]]:
        """Load performance detection patterns."""
        return {
            "inefficient_loops": [
                r"for.*in.*range.*len",
                r"while.*True",
                r"for.*var.*=.*0",
            ],
            "memory_issues": [
                r"\[\s*\]\s*\*",
                r"list\s*\(\s*range",
                r"dict\s*\(\s*\[\s*\]\s*\)",
            ],
            "string_operations": [
                r".*\s*\+\s*.*\s*\+\s*.*",
                r"join\s*\(\s*\[\s*\]\s*\)",
            ],
        }

    def _load_optimization_rules(self) -> Dict[str, List[str]]:
        """Load optimization rules."""
        return {
            "algorithm_optimization": [
                r"O\s*\(\s*n\s*\*\s*n\s*\)",
                r"O\s*\(\s*n\s*\*\s*n\s*\*\s*n\s*\)",
            ],
            "caching_opportunities": [
                r"def.*\n.*return.*\n.*def.*\n.*return",
                r"calculate.*\n.*calculate",
            ],
        }

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the performance analyzer."""
        return {
            "status": "healthy",
            "performance_patterns_loaded": len(self.performance_patterns),
            "optimization_rules_loaded": len(self.optimization_rules),
            "supported_languages": [
                "python",
                "javascript",
                "typescript",
                "java",
                "cpp",
            ],
        }
