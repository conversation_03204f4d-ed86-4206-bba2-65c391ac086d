# Container-specific requirements (excludes Windows-specific packages)
fastapi==0.116.1
uvicorn==0.35.0
aiohttp==3.12.15
torch==2.7.1
torchvision
torchaudio
pydantic==2.11.7
pydantic-settings==2.8.0
psycopg2-binary==2.9.10
redis==5.2.1
sqlalchemy==2.0.41
alembic==1.15.1
python-multipart==0.0.18
python-jose==3.4.0
passlib==1.7.4
bcrypt==4.3.0
cryptography==45.0.5
python-dotenv==1.0.0
requests==2.32.4
httpx==0.27.0
websockets==15.0
watchfiles==1.1.0
pytest==8.0.0
pytest-asyncio==0.23.5
pytest-cov==4.1.0
coverage==7.9.2
black==24.3.0
flake8==7.0.0
mypy==1.8.0
isort==5.13.2
pre-commit==3.6.0
docker==7.1.0
gitpython==3.1.44
numpy==2.3.2
pandas==2.3.1
scikit-learn==1.7.1
transformers==4.54.1
datasets==4.0.0
accelerate==1.9.0
peft==0.16.0
bitsandbytes==0.46.1
tokenizers==0.21.4
huggingface-hub==0.34.3
safetensors==0.5.3
wandb==0.21.0
tqdm==4.67.1
rich==14.0.0
click==8.2.1
typer==0.16.0
jinja2==3.1.6
markupsafe==3.0.2
pyyaml==6.0.2
toml==0.10.2
tomlkit==0.13.3
packaging==25.0
setuptools==80.9.0
wheel==0.45.0
pip==25.2
