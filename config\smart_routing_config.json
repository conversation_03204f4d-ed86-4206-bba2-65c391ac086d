{"smart_routing": {"enabled": true, "default_strategy": "hybrid", "confidence_threshold": 0.7, "max_alternatives": 3, "fallback_strategy": "load_balanced", "learning_enabled": true, "performance_tracking": true}, "ai_routing": {"enabled": true, "model_name": "qwen2.5-coder:3b", "max_tokens": 1000, "temperature": 0.3, "timeout_seconds": 30, "retry_attempts": 3}, "performance_tracking": {"enabled": true, "history_size": 1000, "cleanup_interval_hours": 24, "metrics_retention_days": 30, "track_success_rate": true, "track_duration": true, "track_error_patterns": true, "track_trends": true}, "load_balancing": {"enabled": true, "max_load_threshold": 0.8, "min_availability_threshold": 0.3, "load_check_interval_seconds": 60, "health_check_interval_seconds": 30, "capacity_scaling": true, "auto_scaling_threshold": 0.9}, "context_analysis": {"enabled": true, "ai_analysis": true, "confidence_threshold": 0.7, "max_analysis_time": 30, "complexity_factors": ["lines_of_code", "dependencies", "integrations", "security_requirements", "testing_requirements", "documentation_requirements", "performance_requirements", "scalability_requirements", "time_constraints", "skill_requirements"], "complexity_weights": {"lines_of_code": 0.15, "dependencies": 0.12, "integrations": 0.12, "security_requirements": 0.1, "testing_requirements": 0.08, "documentation_requirements": 0.05, "performance_requirements": 0.08, "scalability_requirements": 0.08, "time_constraints": 0.07, "skill_requirements": 0.15}}, "task_distribution": {"enabled": true, "default_strategy": "hybrid", "confidence_threshold": 0.7, "max_alternatives": 3, "fallback_strategy": "round_robin", "strategies": {"round_robin": {"enabled": true, "weight": 1.0}, "weighted": {"enabled": true, "weight": 1.0, "factors": ["performance", "availability", "load"]}, "priority_based": {"enabled": true, "weight": 1.0, "priority_weights": {"critical": 4.0, "high": 2.0, "medium": 1.0, "low": 0.5}}, "load_aware": {"enabled": true, "weight": 1.0, "load_threshold": 0.8}, "capability_based": {"enabled": true, "weight": 1.0, "capability_match_weight": 0.6}, "hybrid": {"enabled": true, "weight": 1.0, "strategy_weights": {"capability_based": 0.4, "load_aware": 0.3, "performance_based": 0.3}}, "adaptive": {"enabled": true, "weight": 1.0, "learning_rate": 0.1, "performance_window": 100}}}, "agent_capabilities": {"frontend_agent": {"capabilities": ["frontend", "ui", "ux", "react", "vue", "angular", "css", "html", "javascript"], "specializations": ["user_interface", "user_experience", "responsive_design", "frontend_frameworks"], "max_capacity": 8, "priority_weight": 1.0}, "backend_agent": {"capabilities": ["backend", "api", "server", "database", "authentication", "authorization"], "specializations": ["api_development", "server_architecture", "database_design", "security"], "max_capacity": 10, "priority_weight": 1.0}, "container_agent": {"capabilities": ["containerization", "docker", "kubernetes", "deployment", "infrastructure"], "specializations": ["container_orchestration", "deployment_automation", "infrastructure_as_code"], "max_capacity": 6, "priority_weight": 0.8}, "shell_ops_agent": {"capabilities": ["shell", "operations", "system", "automation", "scripting"], "specializations": ["system_administration", "automation_scripts", "operational_tasks"], "max_capacity": 5, "priority_weight": 0.7}, "learning_agent": {"capabilities": ["learning", "optimization", "improvement", "analysis"], "specializations": ["performance_optimization", "learning_algorithms", "data_analysis"], "max_capacity": 4, "priority_weight": 0.6}, "security_agent": {"capabilities": ["security", "vulnerability", "encryption", "authentication", "authorization"], "specializations": ["security_analysis", "vulnerability_assessment", "security_implementation"], "max_capacity": 3, "priority_weight": 1.2}, "monitoring_agent": {"capabilities": ["monitoring", "logging", "metrics", "alerting", "observability"], "specializations": ["system_monitoring", "log_analysis", "performance_metrics"], "max_capacity": 4, "priority_weight": 0.5}}, "learning": {"enabled": true, "learning_rate": 0.1, "update_interval_minutes": 30, "trend_analysis": true, "performance_window": 100, "success_threshold": 0.8, "improvement_threshold": 0.05}, "monitoring": {"enabled": true, "metrics_collection": true, "performance_tracking": true, "alerting": true, "dashboard_integration": true, "metrics_retention_days": 30, "alert_thresholds": {"success_rate_minimum": 0.8, "average_duration_maximum": 600, "failure_rate_maximum": 0.2, "load_threshold": 0.9}}, "security": {"enabled": true, "input_validation": true, "output_sanitization": true, "access_control": true, "audit_logging": true, "rate_limiting": true, "max_requests_per_minute": 100}, "optimization": {"enabled": true, "task_batching": true, "batch_size": 5, "smart_scheduling": true, "load_balancing_strategy": "adaptive", "cache_task_results": true, "cache_ttl_minutes": 60, "performance_optimization": true}, "logging": {"log_level": "INFO", "log_routing_decisions": true, "log_performance_metrics": true, "log_errors": true, "log_to_file": true, "log_file_path": "logs/smart_routing.log", "log_rotation": {"enabled": true, "max_size_mb": 100, "backup_count": 5}}, "debugging": {"enabled": false, "detailed_tracing": false, "capture_stack_traces": false, "enable_profiling": false, "profile_output_path": "profiles/smart_routing"}, "experimental": {"enable_ai_task_routing": true, "enable_predictive_scheduling": false, "enable_auto_strategy_selection": true, "enable_cross_agent_learning": false}}