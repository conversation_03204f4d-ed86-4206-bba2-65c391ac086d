# AI Coding Agent - Centralized Model Configuration
# This file defines all AI models, their providers, routing rules, and agent assignments

version: "2.0"
description: "Centralized model configuration for AI Coding Agent"

# Global settings
global:
  default_provider: "ollama"
  enable_cloud_models: false  # Set to true to enable cloud models
  fallback_strategy: "performance_based"
  health_check_interval: 60
  cache_enabled: true
  cache_ttl: 7200

# Provider configurations
providers:
  ollama:
    type: "local"
    base_url: "http://localhost:11434"  # Use host's Ollama service
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    health_check_endpoint: "/api/tags"
    api_key_env: "OLLAMA_API_KEY"  # Environment variable for Ollama API key
    auth_required: false  # Set to true if API key is required

  openai:
    type: "cloud"
    base_url: "https://api.openai.com/v1"
    timeout: 60
    max_retries: 3
    retry_delay: 2.0
    api_key_env: "OPENAI_API_KEY"
    models_endpoint: "/models"

  anthropic:
    type: "cloud"
    base_url: "https://api.anthropic.com"
    timeout: 60
    max_retries: 3
    retry_delay: 2.0
    api_key_env: "ANTHROPIC_API_KEY"
    models_endpoint: "/v1/models"

# Model definitions
models:
  # Local Ollama Models
  deepseek-coder:6.7b-instruct:
    provider: "ollama"
    type: "local"
    name: "deepseek-coder:6.7b-instruct"
    description: "Fast code generation model with good performance"
    use_cases:
      - "code_generation"
      - "code_completion"
      - "simple_code_analysis"
    performance:
      max_response_time: 7.0
      target_response_time: 4.0
      min_success_rate: 0.90
      max_concurrent_requests: 5
      timeout: 25
      memory_usage_mb: 1024
      gpu_layers: 24
      gpu_memory_mb: 1536
    optimization:
      temperature: 0.5
      top_p: 0.9
      top_k: 35
      max_tokens: 2048
      frequency_penalty: 0.1
      presence_penalty: 0.1
      repeat_penalty: 1.1
      mirostat: 1
      mirostat_tau: 4.0
      mirostat_eta: 0.2
    fallback_models:
      - "yi-coder:1.5b"
      - "mistral:7b-instruct-q4_0"
    prompt_engineering:
      system_prompt: "You are a helpful coding assistant, skilled in generating and completing code snippets."
      context_window: 4096
      max_context_tokens: 3072

  yi-coder:1.5b:
    provider: "ollama"
    type: "local"
    name: "yi-coder:1.5b"
    description: "Balanced code generation model"
    use_cases:
      - "code_review"
      - "intent_parsing"
      - "natural_language_understanding"
      - "quick_analysis"
    performance:
      max_response_time: 6.0
      target_response_time: 3.0
      min_success_rate: 0.92
      max_concurrent_requests: 4
      timeout: 20
      memory_usage_mb: 1024
      gpu_layers: 24
      gpu_memory_mb: 1536
    optimization:
      temperature: 0.4
      top_p: 0.85
      top_k: 30
      max_tokens: 2048
      frequency_penalty: 0.15
      presence_penalty: 0.15
      repeat_penalty: 1.05
      mirostat: 1
      mirostat_tau: 3.0
      mirostat_eta: 0.2
    fallback_models:
      - "deepseek-coder:6.7b-instruct"
      - "qwen2.5:3b"
    prompt_engineering:
      system_prompt: "You are a code review expert. Analyze code for quality, security, and best practices."
      context_window: 4096
      max_context_tokens: 3072

  qwen2.5:3b:
    provider: "ollama"
    type: "local"
    name: "qwen2.5:3b"
    description: "High-quality code generation with larger context"
    use_cases:
      - "content_creation"
      - "documentation_generation"
      - "blog_writing"
      - "explanation"
    performance:
      max_response_time: 12.0
      target_response_time: 6.0
      min_success_rate: 0.9
      max_concurrent_requests: 2
      timeout: 35
      memory_usage_mb: 1536
      gpu_layers: 28
      gpu_memory_mb: 2688
    optimization:
      temperature: 0.7
      top_p: 0.95
      top_k: 50
      max_tokens: 3072
      frequency_penalty: 0.05
      presence_penalty: 0.05
      repeat_penalty: 1.08
      mirostat: 2
      mirostat_tau: 4.0
      mirostat_eta: 0.15
    fallback_models:
      - "deepseek-coder:6.7b-instruct"
      - "yi-coder:1.5b"
    prompt_engineering:
      system_prompt: "You are a technical writer and content creator. Generate clear, engaging, and informative content."
      context_window: 6144
      max_context_tokens: 4608

  starcoder2:3b:
    provider: "ollama"
    type: "local"
    name: "starcoder2:3b"
    description: "Specialized code generation model"
    use_cases:
      - "advanced_code_generation"
      - "architecture_design"
      - "complex_problem_solving"
      - "refactoring"
    performance:
      max_response_time: 15.0
      target_response_time: 8.0
      min_success_rate: 0.88
      max_concurrent_requests: 2
      timeout: 45
      memory_usage_mb: 2048
      gpu_layers: 28
      gpu_memory_mb: 2688
    optimization:
      temperature: 0.5
      top_p: 0.9
      top_k: 40
      max_tokens: 4096
      frequency_penalty: 0.2
      presence_penalty: 0.2
      repeat_penalty: 1.15
      mirostat: 2
      mirostat_tau: 4.5
      mirostat_eta: 0.12
    fallback_models:
      - "deepseek-coder:6.7b-instruct"
      - "yi-coder:1.5b"
    prompt_engineering:
      system_prompt: "You are an expert software architect. Design scalable, maintainable, and efficient solutions."
      context_window: 8192
      max_context_tokens: 6144

  mistral:7b-instruct-q4_0:
    provider: "ollama"
    type: "local"
    name: "mistral:7b-instruct-q4_0"
    description: "Default model with good general capabilities"
    use_cases:
      - "general_assistance"
      - "explanation"
      - "teaching"
      - "conversation"
      - "problem_solving"
    performance:
      max_response_time: 10.0
      target_response_time: 5.0
      min_success_rate: 0.92
      max_concurrent_requests: 2
      timeout: 30
      memory_usage_mb: 2048
      gpu_layers: 32
      gpu_memory_mb: 3129
    optimization:
      temperature: 0.6
      top_p: 0.9
      top_k: 40
      max_tokens: 3072
      frequency_penalty: 0.1
      presence_penalty: 0.1
      repeat_penalty: 1.1
      mirostat: 2
      mirostat_tau: 4.0
      mirostat_eta: 0.15
    fallback_models:
      - "deepseek-coder:6.7b-instruct"
      - "yi-coder:1.5b"
    prompt_engineering:
      system_prompt: "You are a helpful AI assistant. Provide clear, accurate, and educational responses."
      context_window: 8192
      max_context_tokens: 6144

  # Cloud Models (optional)
  gpt-4o:
    provider: "openai"
    type: "cloud"
    name: "gpt-4o"
    description: "OpenAI's latest GPT-4 model"
    use_cases:
      - "complex_code_generation"
      - "advanced_analysis"
      - "creative_problem_solving"
      - "multimodal_tasks"
    performance:
      max_response_time: 30.0
      target_response_time: 15.0
      min_success_rate: 0.95
      max_concurrent_requests: 10
      timeout: 120
    optimization:
      temperature: 0.3
      top_p: 0.9
      max_tokens: 4096
    fallback_models:
      - "deepseek-coder:6.7b-instruct"
      - "starcoder2:3b"
    prompt_engineering:
      system_prompt: "You are an expert AI coding assistant with deep knowledge of software development."
      context_window: 128000
      max_context_tokens: 128000

  claude-3-5-sonnet:
    provider: "anthropic"
    type: "cloud"
    name: "claude-3-5-sonnet"
    description: "Anthropic's Claude 3.5 Sonnet model"
    use_cases:
      - "code_review"
      - "documentation"
      - "explanation"
      - "analysis"
    performance:
      max_response_time: 25.0
      target_response_time: 12.0
      min_success_rate: 0.94
      max_concurrent_requests: 8
      timeout: 90
    optimization:
      temperature: 0.4
      top_p: 0.9
      max_tokens: 4096
    fallback_models:
      - "yi-coder:1.5b"
      - "qwen2.5:3b"
    prompt_engineering:
      system_prompt: "You are a helpful AI assistant specializing in code analysis and documentation."
      context_window: 200000
      max_context_tokens: 200000

# Agent model assignments
agents:
  frontend_agent:
    primary_model: "starcoder2:3b"
    fallback_models:
      - "qwen2.5:3b"
      - "deepseek-coder:6.7b-instruct"
    system_prompt: |
      You are an expert frontend developer specializing in Next.js, React, and Tailwind CSS.
      Your primary responsibilities are to create, modify, and analyze frontend components.
      Adhere to the following principles:
      1. **Component Structure:** Generate components using the atomic design methodology (atoms, molecules, organisms).
      2. **Styling:** Use Tailwind CSS for all styling. Do not use plain CSS, SCSS, or styled-components.
      3. **State Management:** Use Zustand for global state management. Do not use React Context or Redux.
      4. **Code Quality:** Write clean, maintainable, and well-documented TypeScript code. Ensure all components are strictly typed.
      5. **Testing:** Always generate unit tests for components using Jest and React Testing Library.

  backend_agent:
    primary_model: "deepseek-coder:6.7b-instruct"
    fallback_models:
      - "yi-coder:1.5b"
      - "starcoder2:3b"
    system_prompt: |
      You are an expert backend developer specializing in Python and FastAPI.
      Your primary responsibilities are to design, build, and maintain robust and scalable server-side applications.
      Adhere to the following principles:
      1. **Framework:** Use FastAPI for all API development. Do not use Flask or Django unless explicitly instructed.
      2. **Database:** Use PostgreSQL with SQLAlchemy for all database interactions. Use Alembic for migrations.
      3. **Authentication:** Implement JWT-based authentication for securing endpoints.
      4. **Code Quality:** Write clean, asynchronous, and fully type-hinted Python 3.11 code. Follow PEP 8 standards.
      5. **Testing:** Always generate unit and integration tests for your code using pytest.

  container_agent:
    primary_model: "deepseek-coder:6.7b-instruct"
    fallback_models:
      - "yi-coder:1.5b"
      - "starcoder2:3b"
    system_prompt: |
      You are a container AI assistant specialized in Docker and container orchestration.
      Your primary responsibilities are to create, manage, and optimize containerized applications.

  learning_agent:
    primary_model: "deepseek-coder:6.7b-instruct"
    fallback_models:
      - "yi-coder:1.5b"
      - "mistral:7b-instruct-q4_0"
    system_prompt: |
      You are a learning AI assistant specialized in LLM training and optimization.
      Your primary responsibilities are to improve model performance and learning capabilities.

  architect_agent:
    primary_model: "deepseek-coder:6.7b-instruct"
    fallback_models:
      - "yi-coder:1.5b"
      - "starcoder2:3b"
    system_prompt: |
      You are an AI coding architect that coordinates multiple specialized agents.
      Your primary responsibilities are to orchestrate tasks and manage agent collaboration.

  cursor_agent:
    primary_model: "yi-coder:1.5b"
    fallback_models:
      - "deepseek-coder:6.7b-instruct"
      - "mistral:7b-instruct-q4_0"
    system_prompt: |
      You are a helpful AI assistant specializing in code generation and development tasks.
      You MUST follow all cursor rules provided in the system prompt for every task.

# Routing rules
routing:
  task_type_mapping:
    code_generation:
      primary: "deepseek-coder:6.7b-instruct"
      alternatives: ["starcoder2:3b", "gpt-4o"]
    code_review:
      primary: "yi-coder:1.5b"
      alternatives: ["claude-3-5-sonnet", "deepseek-coder:6.7b-instruct"]
    content_creation:
      primary: "qwen2.5:3b"
      alternatives: ["gpt-4o", "claude-3-5-sonnet"]
    architecture_design:
      primary: "starcoder2:3b"
      alternatives: ["gpt-4o", "deepseek-coder:6.7b-instruct"]
    general_assistance:
      primary: "mistral:7b-instruct-q4_0"
      alternatives: ["gpt-4o", "claude-3-5-sonnet"]

  complexity_mapping:
    low:
      primary: "yi-coder:1.5b"
      alternatives: ["mistral:7b-instruct-q4_0"]
    medium:
      primary: "deepseek-coder:6.7b-instruct"
      alternatives: ["qwen2.5:3b"]
    high:
      primary: "starcoder2:3b"
      alternatives: ["gpt-4o", "deepseek-coder:6.7b-instruct"]
    very_high:
      primary: "gpt-4o"
      alternatives: ["starcoder2:3b", "claude-3-5-sonnet"]

# Performance monitoring
monitoring:
  enabled: true
  metrics:
    - "response_time"
    - "success_rate"
    - "error_count"
    - "token_usage"
    - "cost_per_request"
  health_checks:
    interval: 60
    timeout: 10
    retries: 3
  alerts:
    response_time_threshold: 15.0
    error_rate_threshold: 0.1
    success_rate_threshold: 0.9

# Caching configuration
caching:
  enabled: true
  max_size: 200
  ttl_seconds: 7200
  cache_by:
    - "model_name"
    - "prompt_hash"
    - "use_case"
    - "temperature"
  exclude_patterns:
    - "time_sensitive"
    - "user_specific"
    - "dynamic_content"
    - "session_data"
