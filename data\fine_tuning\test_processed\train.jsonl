{"instruction": "Generate code for: Show me how to define a class in Python", "input": "", "output": "class MyClass: def __init__(self): self.value = 42"}
{"instruction": "Generate code for: How do I handle exceptions in Python?", "input": "", "output": "try: # code that might raise an exception pass except Exception as e: print(f'Error: {e}')"}
{"instruction": "Generate code for: Write a Python function to add two numbers", "input": "", "output": "def add_numbers(a, b): return a + b"}
