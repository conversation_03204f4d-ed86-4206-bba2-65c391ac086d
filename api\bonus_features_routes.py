from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from core.agents import ArchitectAgent

router = APIRouter(prefix="/bonus", tags=["Bonus Features"])


# Request/Response models
class ScheduleTaskRequest(BaseModel):
    command: str
    scheduled_at: datetime
    priority: str = "medium"
    max_retries: int = 3


class ClarificationRequest(BaseModel):
    request_id: str
    response: Dict[str, Any]


class FeedbackRequest(BaseModel):
    task_id: str
    satisfaction: int
    feedback: str = ""
    improvements: Optional[list] = None


class RecommendationRequest(BaseModel):
    task_description: str


# Dependency provider
def get_architect_agent():
    from api.architect_routes import get_architect_agent

    return get_architect_agent()


@router.post("/schedule")
async def schedule_deferred_task(
    request: ScheduleTaskRequest, agent: ArchitectAgent = Depends(get_architect_agent)
):
    """Schedule a task for deferred execution"""
    try:
        from core.agents import TaskPriority

        priority_enum = TaskPriority(request.priority.lower())

        result = await agent.schedule_deferred_task(
            user_command=request.command,
            scheduled_at=request.scheduled_at,
            priority=priority_enum,
            max_retries=request.max_retries,
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/queue-status")
async def get_queue_status(agent: ArchitectAgent = Depends(get_architect_agent)):
    """Get task queue status"""
    try:
        return await agent.get_queue_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/clarifications")
async def get_pending_clarifications(
    agent: ArchitectAgent = Depends(get_architect_agent),
):
    """Get pending clarification requests"""
    try:
        return await agent.get_pending_clarifications()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clarify")
async def provide_clarification(
    request: ClarificationRequest, agent: ArchitectAgent = Depends(get_architect_agent)
):
    """Provide clarification for a request"""
    try:
        result = await agent.provide_clarification(request.request_id, request.response)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/insights")
async def get_learning_insights(agent: ArchitectAgent = Depends(get_architect_agent)):
    """Get learning insights and patterns"""
    try:
        return await agent.get_learning_insights()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recommendations")
async def get_pattern_recommendations(
    request: RecommendationRequest, agent: ArchitectAgent = Depends(get_architect_agent)
):
    """Get pattern recommendations for a task"""
    try:
        return await agent.get_pattern_recommendations(request.task_description)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/feedback")
async def learn_from_feedback(
    request: FeedbackRequest, agent: ArchitectAgent = Depends(get_architect_agent)
):
    """Learn from user feedback"""
    try:
        feedback = {
            "satisfaction": request.satisfaction,
            "feedback": request.feedback,
            "improvements": request.improvements or [],
        }
        return await agent.learn_from_feedback(request.task_id, feedback)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
