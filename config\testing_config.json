{"test_environment": {"timeout": 300, "headless": true, "slow_mo": 100, "retry_count": 3, "parallel_tests": 4, "screenshot_on_failure": true, "video_recording": false, "trace_recording": false, "browser": "chromium", "viewport": {"width": 1280, "height": 720}}, "validation_rules": {"html": {"required_tags": ["html", "head", "body", "title"], "required_meta": ["viewport", "charset"], "forbidden_tags": ["marquee", "blink", "font"], "accessibility": {"require_alt": true, "require_aria": false, "require_semantic": true, "require_landmarks": true, "require_skip_links": false}, "seo": {"require_title": true, "require_description": true, "require_keywords": false, "require_robots": false, "max_title_length": 60, "max_description_length": 160}, "performance": {"max_file_size": 1024000, "require_compression": false, "require_minification": false}}, "css": {"forbidden_properties": ["!important"], "max_selectors_per_rule": 10, "max_rules_per_file": 1000, "require_vendor_prefixes": false, "performance": {"max_file_size": 512000, "require_compression": false, "require_minification": false}}, "javascript": {"forbidden_functions": ["eval", "innerHTML"], "max_file_size": 512000, "require_strict_mode": true, "performance": {"require_compression": false, "require_minification": false}}}, "rollback_conditions": {"max_load_time": 5.0, "max_error_rate": 0.05, "required_status_codes": [200, 201, 202], "health_check_endpoints": ["/", "/health", "/api/status", "/ping"], "rollback_threshold": 3, "monitoring_duration": 300, "performance_thresholds": {"first_contentful_paint": 2.0, "largest_contentful_paint": 4.0, "cumulative_layout_shift": 0.1, "first_input_delay": 0.1}, "error_patterns": ["500 Internal Server Error", "502 Bad Gateway", "503 Service Unavailable", "504 Gateway Timeout"]}, "test_data_management": {"test_data_directory": "test_data", "fixtures_directory": "test_fixtures", "screenshots_directory": "test_screenshots", "reports_directory": "test_reports", "logs_directory": "test_logs", "cleanup_after_tests": true, "retain_failed_screenshots": true, "retain_logs_days": 7, "max_screenshot_age_days": 30, "compression": {"enabled": true, "format": "zip", "retain_original": false}}, "test_suites": {"html_validation": {"enabled": true, "directories": ["sites", "templates", "deployments"], "file_patterns": ["*.html", "*.htm"], "exclude_patterns": ["node_modules", ".git", "dist"], "parallel_processing": true}, "css_validation": {"enabled": true, "directories": ["sites", "templates", "static"], "file_patterns": ["*.css"], "exclude_patterns": ["node_modules", ".git", "dist"], "parallel_processing": true}, "javascript_validation": {"enabled": true, "directories": ["sites", "templates", "static"], "file_patterns": ["*.js"], "exclude_patterns": ["node_modules", ".git", "dist"], "parallel_processing": true}, "playwright_tests": {"enabled": true, "urls": ["http://localhost:5000", "http://localhost:8000", "http://localhost:8080"], "test_scenarios": [{"name": "homepage_load", "url": "/", "checks": ["title_exists", "no_console_errors", "responsive_design"]}, {"name": "api_endpoints", "url": "/api/status", "checks": ["json_response", "status_200", "response_time"]}, {"name": "cms_interface", "url": "/cms", "checks": ["login_form", "content_editor", "media_upload"]}], "mobile_testing": {"enabled": true, "devices": ["iPhone 12", "Samsung Galaxy S20", "iPad"]}, "accessibility_testing": {"enabled": true, "tools": ["axe-core", "lighthouse"]}}, "deployment_tests": {"enabled": true, "pre_deployment": {"health_check": true, "performance_baseline": true, "security_scan": true}, "post_deployment": {"health_check": true, "performance_comparison": true, "functional_testing": true, "rollback_readiness": true}, "monitoring": {"duration": 300, "interval": 30, "alerts": {"error_rate_threshold": 0.05, "response_time_threshold": 5.0, "availability_threshold": 0.99}}}, "integration_tests": {"enabled": true, "test_cases": [{"name": "cms_content_creation", "steps": ["login_to_cms", "create_new_content", "add_media", "publish_content", "verify_publication"]}, {"name": "maintenance_engine", "steps": ["trigger_link_check", "verify_broken_links", "approve_fixes", "apply_changes", "verify_changes"]}, {"name": "deployment_workflow", "steps": ["create_deployment", "run_pre_deployment_tests", "deploy_site", "run_post_deployment_tests", "verify_deployment"]}]}}, "reporting": {"formats": ["html", "json", "junit"], "output_directory": "test_reports", "include_screenshots": true, "include_logs": true, "include_performance_metrics": true, "email_notifications": {"enabled": false, "recipients": [], "conditions": ["test_failure", "deployment_rollback"]}, "slack_notifications": {"enabled": false, "webhook_url": "", "channel": "#testing", "conditions": ["test_failure", "deployment_rollback"]}}, "performance_testing": {"enabled": true, "lighthouse": {"enabled": true, "categories": ["performance", "accessibility", "best-practices", "seo"], "thresholds": {"performance": 0.8, "accessibility": 0.9, "best-practices": 0.8, "seo": 0.8}}, "load_testing": {"enabled": false, "concurrent_users": 10, "duration": 60, "ramp_up_time": 10}, "stress_testing": {"enabled": false, "max_concurrent_users": 100, "duration": 300}}, "security_testing": {"enabled": true, "vulnerability_scanning": {"enabled": true, "tools": ["bandit", "safety"], "severity_levels": ["high", "medium", "low"]}, "dependency_checking": {"enabled": true, "check_frequency": "daily", "auto_update": false}, "ssl_testing": {"enabled": true, "check_expiry": true, "check_strength": true}}, "ci_cd_integration": {"github_actions": {"enabled": true, "workflow_file": ".github/workflows/test.yml", "triggers": ["push", "pull_request"], "artifacts": {"screenshots": true, "reports": true, "logs": true}}, "jenkins": {"enabled": false, "pipeline_file": "Jenkins<PERSON><PERSON>", "triggers": ["push", "pull_request"]}, "gitlab_ci": {"enabled": false, "pipeline_file": ".gitlab-ci.yml", "triggers": ["push", "merge_request"]}}, "notifications": {"email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "from_address": "", "to_addresses": []}, "slack": {"enabled": false, "webhook_url": "", "channel": "#testing", "username": "Test Bot"}, "discord": {"enabled": false, "webhook_url": "", "channel": "testing"}}}