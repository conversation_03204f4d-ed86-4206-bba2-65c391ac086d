"""
Centralized validation utilities to reduce code duplication
"""

import re
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


@dataclass
class ValidationResult:
    """Standard validation result"""

    is_valid: bool
    errors: List[str]
    warnings: List[str]


class BaseValidator:
    """Base validation class with common patterns"""

    @staticmethod
    def validate_string_length(
        value: str,
        min_length: int = 0,
        max_length: Optional[int] = None,
        field_name: str = "field",
    ) -> ValidationResult:
        """Validate string length constraints"""
        errors = []

        if len(value) < min_length:
            errors.append(f"{field_name} must be at least {min_length} characters")

        if max_length and len(value) > max_length:
            errors.append(f"{field_name} must be at most {max_length} characters")

        return ValidationResult(is_valid=len(errors) == 0, errors=errors, warnings=[])

    @staticmethod
    def validate_numeric_range(
        value: Union[int, float],
        min_val: Optional[Union[int, float]] = None,
        max_val: Optional[Union[int, float]] = None,
        field_name: str = "field",
    ) -> ValidationResult:
        """Validate numeric range constraints"""
        errors = []

        if min_val is not None and value < min_val:
            errors.append(f"{field_name} must be at least {min_val}")

        if max_val is not None and value > max_val:
            errors.append(f"{field_name} must be at most {max_val}")

        return ValidationResult(is_valid=len(errors) == 0, errors=errors, warnings=[])

    @staticmethod
    def validate_path_safety(path: str, allowed_dirs: List[str]) -> ValidationResult:
        """Validate path is within allowed directories"""
        try:
            target_path = Path(path).resolve()
            for allowed_dir in allowed_dirs:
                if str(target_path).startswith(str(Path(allowed_dir).resolve())):
                    return ValidationResult(True, [], [])
            return ValidationResult(
                False, [f"Path {path} is outside allowed directories"], []
            )
        except Exception as e:
            return ValidationResult(False, [f"Invalid path: {e}"], [])

    @staticmethod
    def validate_email(email: str) -> ValidationResult:
        """Validate email format"""
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if re.match(pattern, email):
            return ValidationResult(True, [], [])
        return ValidationResult(False, ["Invalid email format"], [])

    @staticmethod
    def validate_url(url: str) -> ValidationResult:
        """Validate URL format"""
        pattern = r"^https?://[^\s/$.?#].[^\s]*$"
        if re.match(pattern, url):
            return ValidationResult(True, [], [])
        return ValidationResult(False, ["Invalid URL format"], [])

    @staticmethod
    def validate_enum(
        value: str, allowed_values: List[str], field_name: str = "field"
    ) -> ValidationResult:
        """Validate value is in allowed set"""
        if value in allowed_values:
            return ValidationResult(True, [], [])
        return ValidationResult(
            False, [f"{field_name} must be one of: {', '.join(allowed_values)}"], []
        )


class ValidationRegistry:
    """Registry for validation functions"""

    _validators: Dict[str, Any] = {}

    @classmethod
    def register(cls, name: str, validator: Any):
        """Register a validation function"""
        cls._validators[name] = validator

    @classmethod
    def validate(cls, name: str, value: Any, **kwargs) -> ValidationResult:
        """Run registered validation"""
        if name not in cls._validators:
            return ValidationResult(False, [f"Unknown validator: {name}"], [])
        return cls._validators[name](value, **kwargs)


# Register common validators
ValidationRegistry.register("string_length", BaseValidator.validate_string_length)
ValidationRegistry.register("numeric_range", BaseValidator.validate_numeric_range)
ValidationRegistry.register("path_safety", BaseValidator.validate_path_safety)
ValidationRegistry.register("email", BaseValidator.validate_email)
ValidationRegistry.register("url", BaseValidator.validate_url)
ValidationRegistry.register("enum", BaseValidator.validate_enum)
