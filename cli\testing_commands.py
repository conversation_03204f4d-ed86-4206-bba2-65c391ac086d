#!/usr/bin/env python3
"""
Testing Commands - Phase 2.5
CLI commands for running tests and generating reports.
"""

import argparse
import asyncio
import json
import logging
import sys
from argparse import Namespace
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from config import get_config
from tests.testing_harness import TestConfig, TestResult

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


# Local classes to avoid circular imports

# Import TestResult from testing_harness to avoid duplication

# Import TestConfig from testing_harness to avoid duplication


class TestingHarness:
    """Simplified testing harness for CLI commands"""

    def __init__(self, config: TestConfig):
        self.config = config
        # Import validators here to avoid circular imports
        from core.validators.css_validator import CSSValidator
        from core.validators.html_validator import HTMLValidator

        self.html_validator = HTMLValidator()
        self.css_validator = CSSValidator()

    async def run_html_validation(self, directory: str) -> List[TestResult]:
        """Run HTML validation tests"""
        results = []
        html_files = list(Path(directory).rglob("*.html"))

        for html_file in html_files:
            try:
                result = self.html_validator.validate_html_file(str(html_file))
                results.append(
                    TestResult(
                        test_name=result.get(
                            "test_name", f"HTML_Validation_{html_file.stem}"
                        ),
                        test_type="html_validation",
                        status=result.get("status", "passed"),
                        duration=result.get("duration", 0.0),
                        timestamp=result.get("timestamp", datetime.now()),
                        details=result.get("details", {}),
                        error_message=result.get("error_message"),
                    )
                )
            except Exception as e:
                results.append(
                    TestResult(
                        test_name=f"HTML_Validation_{html_file.stem}",
                        test_type="html_validation",
                        status="error",
                        duration=0.0,
                        timestamp=datetime.now(),
                        details={"file_path": str(html_file)},
                        error_message=str(e),
                    )
                )

        return results

    async def run_css_validation(self, directory: str) -> List[TestResult]:
        """Run CSS validation tests"""
        results = []
        css_files = list(Path(directory).rglob("*.css"))

        for css_file in css_files:
            try:
                result = self.css_validator.validate_css_file(str(css_file))
                results.append(
                    TestResult(
                        test_name=result.get(
                            "test_name", f"CSS_Validation_{css_file.stem}"
                        ),
                        test_type="css_validation",
                        status=result.get("status", "passed"),
                        duration=result.get("duration", 0.0),
                        timestamp=result.get("timestamp", datetime.now()),
                        details=result.get("details", {}),
                        error_message=result.get("error_message"),
                    )
                )
            except Exception as e:
                results.append(
                    TestResult(
                        test_name=f"CSS_Validation_{css_file.stem}",
                        test_type="css_validation",
                        status="error",
                        duration=0.0,
                        timestamp=datetime.now(),
                        details={"file_path": str(css_file)},
                        error_message=str(e),
                    )
                )

        return results

    def generate_test_report(self, results: List[TestResult], output_file: str) -> str:
        """Generate HTML test report"""
        # Create simple HTML report
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Harness Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .summary {{ background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .test-result {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
        .passed {{ border-left-color: #4CAF50; background: #f1f8e9; }}
        .failed {{ border-left-color: #f44336; background: #ffebee; }}
        .error {{ border-left-color: #9c27b0; background: #f3e5f5; }}
    </style>
</head>
<body>
    <h1>Testing Harness Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Tests:</strong> {len(results)}</p>
        <p><strong>Passed:</strong> {len([r for r in results if r.status == 'passed'])}</p>
        <p><strong>Failed:</strong> {len([r for r in results if r.status == 'failed'])}</p>
        <p><strong>Errors:</strong> {len([r for r in results if r.status == 'error'])}</p>
    </div>

    <h2>Test Results</h2>"""

        for result in results:
            status_class = result.status
            html_content += f"""
    <div class="test-result {status_class}">
        <h3>{result.test_name}</h3>
        <p><strong>Type:</strong> {result.test_type}</p>
        <p><strong>Status:</strong> {result.status}</p>
        <p><strong>Duration:</strong> {result.duration:.2f}s</p>
        <p><strong>Timestamp:</strong> {result.timestamp}</p>"""

            if result.error_message:
                html_content += f"<p><strong>Error:</strong> {result.error_message}</p>"

            html_content += """
    </div>"""

        html_content += """
</body>
</html>"""

        # Ensure directory exists
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)

        # Write report
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(html_content)

        return output_file


def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("test_logs/testing_harness.log"),
        ],
    )


async def run_html_validation(args: Namespace) -> List[TestResult]:
    """Run HTML validation tests"""
    print("🔍 Running HTML Validation Tests...")

    config = get_config("config/testing_config.json")
    test_config = TestConfig(**config.get("test_environment", {}))

    harness = TestingHarness(test_config)

    directory = args.directory or "sites"
    results = await harness.run_html_validation(directory)

    # Print results
    passed = len([r for r in results if r.status == "passed"])
    failed = len([r for r in results if r.status == "failed"])
    errors = len([r for r in results if r.status == "error"])

    print(f"\n📊 HTML Validation Results:")
    print(f"   Total files: {len(results)}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {failed}")
    print(f"   Errors: {errors}")

    # Show failed tests
    if failed > 0 or errors > 0:
        print(f"\n❌ Failed/Error Tests:")
        for result in results:
            if result.status in ["failed", "error"]:
                print(f"   {result.test_name}: {result.error_message}")

    # Generate report
    if args.report:
        report_file = harness.generate_test_report(
            results, "test_reports/html_validation_report.html"
        )
        print(f"\n📄 Report generated: {report_file}")

    return results


async def run_css_validation(args: Namespace) -> List[TestResult]:
    """Run CSS validation tests"""
    print("🎨 Running CSS Validation Tests...")

    config = get_config("config/testing_config.json")
    test_config = TestConfig(**config.get("test_environment", {}))

    harness = TestingHarness(test_config)

    directory = args.directory or "sites"
    results = await harness.run_css_validation(directory)

    # Print results
    passed = len([r for r in results if r.status == "passed"])
    failed = len([r for r in results if r.status == "failed"])
    errors = len([r for r in results if r.status == "error"])

    print(f"\n📊 CSS Validation Results:")
    print(f"   Total files: {len(results)}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {failed}")
    print(f"   Errors: {errors}")

    # Show failed tests
    if failed > 0 or errors > 0:
        print(f"\n❌ Failed/Error Tests:")
        for result in results:
            if result.status in ["failed", "error"]:
                print(f"   {result.test_name}: {result.error_message}")

    # Generate report
    if args.report:
        report_file = harness.generate_test_report(
            results, "test_reports/css_validation_report.html"
        )
        print(f"\n📄 Report generated: {report_file}")

    return results


async def run_playwright_tests(args: Namespace) -> List[TestResult]:
    """Run Playwright end-to-end tests"""
    print("🌐 Running Playwright Tests...")

    config = get_config("config/testing_config.json")
    test_config = TestConfig(**config.get("test_environment", {}))

    harness = TestingHarness(test_config)

    urls = args.urls or ["http://localhost:5000"]
    results = await harness.run_playwright_tests(urls)

    # Print results
    passed = len([r for r in results if r.status == "passed"])
    failed = len([r for r in results if r.status == "failed"])
    skipped = len([r for r in results if r.status == "skipped"])

    print(f"\n📊 Playwright Test Results:")
    print(f"   Total tests: {len(results)}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {failed}")
    print(f"   Skipped: {skipped}")

    # Show failed tests
    if failed > 0:
        print(f"\n❌ Failed Tests:")
        for result in results:
            if result.status == "failed":
                print(f"   {result.test_name}: {result.error_message}")

    # Generate report
    if args.report:
        report_file = harness.generate_test_report(
            results, "test_reports/playwright_report.html"
        )
        print(f"\n📄 Report generated: {report_file}")

    return results


async def run_deployment_tests(args: Namespace) -> List[TestResult]:
    """Run deployment health and rollback tests"""
    print("🚀 Running Deployment Tests...")

    config = get_config("config/testing_config.json")
    test_config = TestConfig(**config.get("test_environment", {}))

    harness = TestingHarness(test_config)

    deployment_url = args.url or "http://localhost:5000"
    deployment_id = args.deployment_id

    results = await harness.run_deployment_tests(deployment_url, deployment_id)

    # Print results
    passed = len([r for r in results if r.status == "passed"])
    failed = len([r for r in results if r.status == "failed"])

    print(f"\n📊 Deployment Test Results:")
    print(f"   Total tests: {len(results)}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {failed}")

    # Show failed tests
    if failed > 0:
        print(f"\n❌ Failed Tests:")
        for result in results:
            if result.status == "failed":
                print(f"   {result.test_name}: {result.error_message}")

    # Check for rollback recommendations
    rollback_tests = [r for r in results if r.test_type == "deployment_health"]
    for test in rollback_tests:
        if test.details.get("should_rollback", False):
            print(f"\n⚠️  ROLLBACK RECOMMENDED for {test.details.get('deployment_url')}")
            print(f"   Error rate: {test.details.get('error_rate', 0):.2%}")
            print(
                f"   Failed checks: {test.details.get('error_count', 0)}/{test.details.get('total_checks', 0)}"
            )

    # Generate report
    if args.report:
        report_file = harness.generate_test_report(
            results, "test_reports/deployment_report.html"
        )
        print(f"\n📄 Report generated: {report_file}")

    return results


async def run_comprehensive_suite(args: Namespace) -> Dict[str, Any]:
    """Run comprehensive test suite"""
    print("🧪 Running Comprehensive Test Suite...")

    config = get_config("config/testing_config.json")
    test_config = TestConfig(**config.get("test_environment", {}))

    harness = TestingHarness(test_config)

    # Build test configuration
    test_config_dict = {
        "html_validation": {
            "enabled": not args.skip_html,
            "directory": args.html_directory or "sites",
        },
        "css_validation": {
            "enabled": not args.skip_css,
            "directory": args.css_directory or "sites",
        },
        "playwright_tests": {
            "enabled": not args.skip_playwright,
            "urls": args.urls or ["http://localhost:5000"],
        },
        "deployment_tests": {
            "enabled": not args.skip_deployment,
            "url": args.deployment_url or "http://localhost:5000",
            "deployment_id": args.deployment_id,
        },
    }

    results = await harness.run_comprehensive_test_suite(test_config_dict)

    # Print summary
    summary = results["summary"]
    print(f"\n📊 Comprehensive Test Suite Results:")
    print(f"   Total tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed_tests']}")
    print(f"   Failed: {summary['failed_tests']}")
    print(f"   Skipped: {summary['skipped_tests']}")
    print(f"   Errors: {summary['error_tests']}")
    print(f"   Success rate: {summary['success_rate']:.1f}%")
    print(f"   Total duration: {summary['total_duration']:.2f}s")

    # Show failed tests
    failed_tests = [r for r in results["results"] if r.status in ["failed", "error"]]
    if failed_tests:
        print(f"\n❌ Failed/Error Tests:")
        for result in failed_tests:
            print(f"   {result.test_name} ({result.test_type}): {result.error_message}")

    # Generate report
    if args.report:
        report_file = harness.generate_test_report(
            results["results"], "test_reports/comprehensive_report.html"
        )
        print(f"\n📄 Report generated: {report_file}")

    return results


def show_test_results(args):
    """Show recent test results"""
    print("📋 Recent Test Results...")

    try:
        from tests.testing_harness import TestDatabase

        db = TestDatabase()
        results = db.get_test_results(limit=args.limit or 50)

        if not results:
            print("No test results found.")
            return

        print(f"\n📊 Recent Test Results (showing {len(results)}):")
        print("-" * 80)

        for result in results:
            status_icon = {
                "passed": "✅",
                "failed": "❌",
                "skipped": "⏭️",
                "error": "💥",
            }.get(result["status"], "❓")

            print(f"{status_icon} {result['test_name']} ({result['test_type']})")
            print(
                f"   Status: {result['status']} | Duration: {result['duration']:.2f}s | Time: {result['timestamp']}"
            )

            if result["error_message"]:
                print(f"   Error: {result['error_message']}")
            print()
    except ImportError:
        print(
            "❌ Testing harness not available. Please install with: pip install testing-harness"
        )
    except Exception as e:
        print(f"❌ Error accessing test results: {e}")


def create_test_data(args):
    """Create test data and fixtures"""
    print("📝 Creating Test Data...")

    # Create test directories
    test_dirs = [
        "test_data",
        "test_fixtures",
        "test_screenshots",
        "test_reports",
        "test_logs",
    ]

    for dir_name in test_dirs:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"   Created directory: {dir_name}")

    # Create sample test fixtures
    fixtures = {
        "sample_site": {
            "name": "test-site",
            "template": "basic",
            "content": {
                "title": "Test Site",
                "description": "A test site for validation",
                "pages": ["home", "about", "contact"],
            },
        },
        "sample_content": {
            "title": "Test Content",
            "body": "<p>This is test content for validation.</p>",
            "metadata": {
                "author": "Test Author",
                "date": "2024-01-01",
                "tags": ["test", "validation"],
            },
        },
    }

    fixtures_file = Path("test_fixtures/sample_data.json")
    with open(fixtures_file, "w") as f:
        json.dump(fixtures, f, indent=2)

    print(f"   Created sample fixtures: {fixtures_file}")

    # Create sample HTML file for testing
    sample_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <meta name="description" content="A test page for validation">
</head>
<body>
    <header>
        <h1>Test Page</h1>
    </header>
    <main>
        <p>This is a test page for HTML validation.</p>
        <img src="test.jpg" alt="Test image">
    </main>
    <footer>
        <p>&copy; 2024 Test Site</p>
    </footer>
</body>
</html>"""

    sample_html_file = Path("test_data/sample.html")
    with open(sample_html_file, "w") as f:
        f.write(sample_html)

    print(f"   Created sample HTML: {sample_html_file}")

    # Create sample CSS file for testing
    sample_css = """/* Test CSS file */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

header {
    background-color: #333;
    color: white;
    padding: 1rem;
    text-align: center;
}

main {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

footer {
    text-align: center;
    padding: 1rem;
    color: #666;
}"""

    sample_css_file = Path("test_data/sample.css")
    with open(sample_css_file, "w") as f:
        f.write(sample_css)

    print(f"   Created sample CSS: {sample_css_file}")

    print("\n✅ Test data creation completed!")


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="AI Coding Agent Testing Harness")
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # HTML validation command
    html_parser = subparsers.add_parser("html", help="Run HTML validation tests")
    html_parser.add_argument(
        "--directory", "-d", help="Directory to scan for HTML files"
    )
    html_parser.add_argument(
        "--report", "-r", action="store_true", help="Generate HTML report"
    )

    # CSS validation command
    css_parser = subparsers.add_parser("css", help="Run CSS validation tests")
    css_parser.add_argument("--directory", "-d", help="Directory to scan for CSS files")
    css_parser.add_argument(
        "--report", "-r", action="store_true", help="Generate HTML report"
    )

    # Playwright tests command
    playwright_parser = subparsers.add_parser(
        "playwright", help="Run Playwright end-to-end tests"
    )
    playwright_parser.add_argument("--urls", "-u", nargs="+", help="URLs to test")
    playwright_parser.add_argument(
        "--report", "-r", action="store_true", help="Generate HTML report"
    )

    # Deployment tests command
    deployment_parser = subparsers.add_parser("deployment", help="Run deployment tests")
    deployment_parser.add_argument("--url", "-u", help="Deployment URL to test")
    deployment_parser.add_argument(
        "--deployment-id", "-i", help="Deployment ID for rollback testing"
    )
    deployment_parser.add_argument(
        "--report", "-r", action="store_true", help="Generate HTML report"
    )

    # Comprehensive suite command
    suite_parser = subparsers.add_parser("suite", help="Run comprehensive test suite")
    suite_parser.add_argument(
        "--skip-html", action="store_true", help="Skip HTML validation"
    )
    suite_parser.add_argument(
        "--skip-css", action="store_true", help="Skip CSS validation"
    )
    suite_parser.add_argument(
        "--skip-playwright", action="store_true", help="Skip Playwright tests"
    )
    suite_parser.add_argument(
        "--skip-deployment", action="store_true", help="Skip deployment tests"
    )
    suite_parser.add_argument("--html-directory", help="Directory for HTML validation")
    suite_parser.add_argument("--css-directory", help="Directory for CSS validation")
    suite_parser.add_argument("--urls", nargs="+", help="URLs for Playwright tests")
    suite_parser.add_argument("--deployment-url", help="Deployment URL for testing")
    suite_parser.add_argument(
        "--deployment-id", help="Deployment ID for rollback testing"
    )
    suite_parser.add_argument(
        "--report", "-r", action="store_true", help="Generate HTML report"
    )

    # Show results command
    results_parser = subparsers.add_parser("results", help="Show recent test results")
    results_parser.add_argument(
        "--limit", "-l", type=int, help="Number of results to show"
    )

    # Create test data command
    data_parser = subparsers.add_parser(
        "create-data", help="Create test data and fixtures"
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.verbose)

    # Create test directories
    for dir_name in ["test_logs", "test_reports", "test_screenshots"]:
        Path(dir_name).mkdir(exist_ok=True)

    # Execute command with proper async/sync separation
    if args.command in ["html", "css", "playwright", "deployment", "suite"]:

        async def _run_async_command() -> None:
            """Wrapper to pass args to async commands"""
            if args.command == "html":
                await run_html_validation(args)
            elif args.command == "css":
                await run_css_validation(args)
            elif args.command == "playwright":
                await run_playwright_tests(args)
            elif args.command == "deployment":
                await run_deployment_tests(args)
            elif args.command == "suite":
                await run_comprehensive_suite(args)

        asyncio.run(_run_async_command())
    elif args.command == "results":
        show_test_results(args)
    elif args.command == "create-data":
        create_test_data(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
