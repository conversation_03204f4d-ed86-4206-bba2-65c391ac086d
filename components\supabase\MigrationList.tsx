import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Migration } from '@/types';

interface DatabaseMigration {
  id: string;
  project_id: string;
  name: string;
  description: string;
  sql_content: string;
  status: 'pending' | 'applied' | 'failed' | 'rolled_back';
  applied_at?: string;
  created_at: string;
  updated_at: string;
  version: string;
  checksum: string;
}

interface MigrationListProps {
  projectId: string;
  onMigrationSelect?: (migration: DatabaseMigration) => void;
  onMigrationDeploy?: (migrationId: string) => Promise<void>;
  onMigrationRollback?: (migrationId: string) => Promise<void>;
  onError?: (error: string) => void;
}

export const MigrationList: React.FC<MigrationListProps> = ({
  projectId,
  onMigrationSelect,
  onMigrationDeploy,
  onMigrationRollback,
  onError
}) => {
  const [migrations, setMigrations] = useState<DatabaseMigration[]>([]);
  const [loading, setLoading] = useState(true);
  const [deploying, setDeploying] = useState<string | null>(null);
  const [rollingBack, setRollingBack] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'applied' | 'failed'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadMigrations();
  }, [projectId]);

  const loadMigrations = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/migrations`);
      if (response.ok) {
        const data = await response.json();
        setMigrations(data);
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to load migrations');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load migrations';
      onError?.(errorMessage);
      console.error('Error loading migrations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeploy = async (migrationId: string) => {
    setDeploying(migrationId);
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/migrations/${migrationId}/deploy`, {
        method: 'POST',
      });

      if (response.ok) {
        const result = await response.json();
        await loadMigrations(); // Refresh the list
        onMigrationDeploy?.(migrationId);
        alert(`Migration "${result.name}" deployed successfully!`);
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to deploy migration');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to deploy migration';
      onError?.(errorMessage);
      alert(`Error deploying migration: ${errorMessage}`);
    } finally {
      setDeploying(null);
    }
  };

  const handleRollback = async (migrationId: string) => {
    if (!confirm('Are you sure you want to rollback this migration? This will undo the database changes.')) {
      return;
    }

    setRollingBack(migrationId);
    try {
      const response = await fetch(`/api/v1/projects/${projectId}/migrations/${migrationId}/rollback`, {
        method: 'POST',
      });

      if (response.ok) {
        const result = await response.json();
        await loadMigrations(); // Refresh the list
        onMigrationRollback?.(migrationId);
        alert(`Migration "${result.name}" rolled back successfully!`);
      } else {
        const error = await response.json();
        throw new Error(error.detail || 'Failed to rollback migration');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to rollback migration';
      onError?.(errorMessage);
      alert(`Error rolling back migration: ${errorMessage}`);
    } finally {
      setRollingBack(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'applied':
        return <Badge variant="success">Applied</Badge>;
      case 'failed':
        return <Badge variant="error">Failed</Badge>;
      case 'rolled_back':
        return <Badge variant="secondary">Rolled Back</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredMigrations = migrations.filter(migration => {
    const matchesFilter = filter === 'all' || migration.status === filter;
    const matchesSearch = migration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         migration.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Database Migrations</h3>
        <Button
          onClick={loadMigrations}
          variant="outline"
          size="sm"
        >
          Refresh
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex gap-4 mb-6">
        <div className="flex gap-2">
          {(['all', 'pending', 'applied', 'failed'] as const).map(status => (
            <Button
              key={status}
              variant={filter === status ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setFilter(status)}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Button>
          ))}
        </div>
        <input
          type="text"
          placeholder="Search migrations..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Migrations List */}
      <div className="space-y-4">
        {filteredMigrations.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {migrations.length === 0 ? 'No migrations found' : 'No migrations match your filters'}
          </div>
        ) : (
          filteredMigrations.map(migration => (
            <div
              key={migration.id}
              className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="font-medium text-gray-900">{migration.name}</h4>
                    {getStatusBadge(migration.status)}
                    <span className="text-sm text-gray-500">v{migration.version}</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{migration.description}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>Created: {new Date(migration.created_at).toLocaleDateString()}</span>
                    {migration.applied_at && (
                      <span>Applied: {new Date(migration.applied_at).toLocaleDateString()}</span>
                    )}
                    <span>Checksum: {migration.checksum.substring(0, 8)}...</span>
                  </div>
                </div>
                <div className="flex gap-2 ml-4">
                  <Button
                    onClick={() => onMigrationSelect?.(migration)}
                    variant="outline"
                    size="sm"
                  >
                    View
                  </Button>
                  {migration.status === 'pending' && (
                    <Button
                      onClick={() => handleDeploy(migration.id)}
                      disabled={deploying === migration.id}
                      variant="primary"
                      size="sm"
                    >
                      {deploying === migration.id ? 'Deploying...' : 'Deploy'}
                    </Button>
                  )}
                  {migration.status === 'applied' && (
                    <Button
                      onClick={() => handleRollback(migration.id)}
                      disabled={rollingBack === migration.id}
                      variant="outline"
                      size="sm"
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      {rollingBack === migration.id ? 'Rolling Back...' : 'Rollback'}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex justify-between text-sm text-gray-600">
          <span>Total: {migrations.length} migrations</span>
          <span>Showing: {filteredMigrations.length} migrations</span>
        </div>
      </div>
    </div>
  );
};
