"""
Fine-Tuning CLI Commands
========================

This module provides CLI commands for the fine-tuning pipeline, including
training, evaluation, deployment, and automated triggers.
"""

import json
import logging
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class FineTuningCommands:
    """CLI commands for fine-tuning pipeline"""

    def __init__(self, agent):
        self.agent = agent
        self.setup_components()

    def setup_components(self):
        """Setup fine-tuning components"""
        try:
            from fine_tuning.automated_triggers import AutomatedFineTuningTrigger
            from fine_tuning.model_evaluator import ModelEvaluationWorkflow
            from fine_tuning.performance_monitor import FineTuningPerformanceMonitor
            from models.fine_tuning_integration import FineTuningIntegration
            from scripts.train_model import ModelTrainer

            self.trainer = ModelTrainer()
            self.evaluator = ModelEvaluationWorkflow()
            self.integration = FineTuningIntegration()
            self.performance_monitor = FineTuningPerformanceMonitor()
            self.automated_triggers = AutomatedFineTuningTrigger()

        except ImportError as e:
            logger.warning(f"Could not import fine-tuning components: {e}")
            self.trainer = None
            self.evaluator = None
            self.integration = None
            self.performance_monitor = None
            self.automated_triggers = None

    async def train_model(self, **kwargs) -> Dict[str, Any]:
        """Train a model using the fine-tuning pipeline"""
        try:
            if not self.trainer:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_name = kwargs.get("model_name", "default_model")
            dataset_type = kwargs.get("dataset_type", "all")

            logger.info(f"Starting model training for {model_name}")

            # Run complete training pipeline
            result = self.trainer.run_complete_pipeline(model_name, dataset_type)

            if result.get("overall_success"):
                # Register model in integration system
                if result["steps"].get("training", {}).get("success"):
                    model_path = result["steps"]["training"].get("output_dir")
                    if model_path:
                        self.integration.register_fine_tuned_model(
                            model_name=model_name,
                            model_path=model_path,
                            evaluation_results=result["steps"]
                            .get("evaluation", {})
                            .get("evaluation_result"),
                        )

                logger.info(f"Model training completed successfully for {model_name}")
                return {"success": True, "model_name": model_name, "result": result}
            else:
                logger.error(f"Model training failed for {model_name}")
                return {
                    "success": False,
                    "model_name": model_name,
                    "error": "Training pipeline failed",
                    "result": result,
                }

        except Exception as e:
            logger.error(f"Error in train_model: {e}")
            return {"success": False, "error": str(e)}

    async def evaluate_model(self, **kwargs) -> Dict[str, Any]:
        """Evaluate a trained model"""
        try:
            if not self.evaluator:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_path = kwargs.get("model_path")
            model_name = kwargs.get("model_name", "unknown")
            test_file = kwargs.get("test_file", "data/fine_tuning/test_dataset.jsonl")

            if not model_path:
                return {"success": False, "error": "model_path is required"}

            logger.info(f"Evaluating model: {model_path}")

            # Run evaluation
            result = self.evaluator.evaluate_model(model_path, test_file, model_name)

            if result.get("success"):
                logger.info(f"Model evaluation completed for {model_name}")
                return {"success": True, "model_name": model_name, "result": result}
            else:
                logger.error(f"Model evaluation failed for {model_name}")
                return {
                    "success": False,
                    "model_name": model_name,
                    "error": result.get("error", "Evaluation failed"),
                }

        except Exception as e:
            logger.error(f"Error in evaluate_model: {e}")
            return {"success": False, "error": str(e)}

    async def deploy_model(self, **kwargs) -> Dict[str, Any]:
        """Deploy a trained model"""
        try:
            if not self.integration:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_name = kwargs.get("model_name")
            auto_switch = kwargs.get("auto_switch", True)

            if not model_name:
                return {"success": False, "error": "model_name is required"}

            logger.info(f"Deploying model: {model_name}")

            # Deploy model
            result = self.integration.deploy_fine_tuned_model(model_name, auto_switch)

            if result.get("success"):
                logger.info(f"Model deployed successfully: {model_name}")
                return {"success": True, "model_name": model_name, "result": result}
            else:
                logger.error(f"Model deployment failed: {model_name}")
                return {
                    "success": False,
                    "model_name": model_name,
                    "error": result.get("error", "Deployment failed"),
                }

        except Exception as e:
            logger.error(f"Error in deploy_model: {e}")
            return {"success": False, "error": str(e)}

    async def list_models(self, **kwargs) -> Dict[str, Any]:
        """List available models"""
        try:
            if not self.integration:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            models = self.integration.list_available_models()
            active_model = self.integration.get_active_model()

            return {
                "success": True,
                "models": models,
                "active_model": active_model,
                "total_models": len(models),
            }

        except Exception as e:
            logger.error(f"Error in list_models: {e}")
            return {"success": False, "error": str(e)}

    async def get_model_status(self, **kwargs) -> Dict[str, Any]:
        """Get status of a specific model"""
        try:
            if not self.integration:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_name = kwargs.get("model_name")

            if not model_name:
                return {"success": False, "error": "model_name is required"}

            models = self.integration.list_available_models()
            model_info = next((m for m in models if m["name"] == model_name), None)

            if model_info:
                # Get performance history
                history = self.integration.get_model_performance_history(model_name)

                return {
                    "success": True,
                    "model_info": model_info,
                    "performance_history": history,
                }
            else:
                return {"success": False, "error": f"Model {model_name} not found"}

        except Exception as e:
            logger.error(f"Error in get_model_status: {e}")
            return {"success": False, "error": str(e)}

    async def create_trigger(self, **kwargs) -> Dict[str, Any]:
        """Create an automated fine-tuning trigger"""
        try:
            if not self.automated_triggers:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            trigger_type = kwargs.get("trigger_type")
            model_name = kwargs.get("model_name")

            if not trigger_type or not model_name:
                return {
                    "success": False,
                    "error": "trigger_type and model_name are required",
                }

            logger.info(f"Creating {trigger_type} trigger for {model_name}")

            if trigger_type == "performance":
                threshold = kwargs.get("performance_threshold", 0.7)
                interval = kwargs.get("check_interval_hours", 24)
                result = self.automated_triggers.create_performance_trigger(
                    model_name, threshold, interval
                )
            elif trigger_type == "scheduled":
                schedule_type = kwargs.get("schedule_type", "weekly")
                schedule_value = kwargs.get("schedule_value", "monday")
                result = self.automated_triggers.create_scheduled_trigger(
                    model_name, schedule_type, schedule_value
                )
            elif trigger_type == "data_drift":
                threshold = kwargs.get("drift_threshold", 0.1)
                interval = kwargs.get("check_interval_hours", 12)
                result = self.automated_triggers.create_data_drift_trigger(
                    model_name, threshold, interval
                )
            elif trigger_type == "usage":
                threshold = kwargs.get("usage_threshold", 1000)
                window = kwargs.get("time_window_hours", 24)
                result = self.automated_triggers.create_usage_trigger(
                    model_name, threshold, window
                )
            else:
                return {
                    "success": False,
                    "error": f"Unknown trigger type: {trigger_type}",
                }

            if result.get("id"):
                logger.info(f"Trigger created successfully: {result['id']}")
                return {"success": True, "trigger": result}
            else:
                return {"success": False, "error": "Failed to create trigger"}

        except Exception as e:
            logger.error(f"Error in create_trigger: {e}")
            return {"success": False, "error": str(e)}

    async def list_triggers(self, **kwargs) -> Dict[str, Any]:
        """List automated triggers"""
        try:
            if not self.automated_triggers:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            status = self.automated_triggers.get_trigger_status()

            return {
                "success": True,
                "triggers": status.get("triggers", {}),
                "scheduler_active": status.get("scheduler_active", False),
                "total_triggers": status.get("total_triggers", 0),
                "active_triggers": status.get("active_triggers", 0),
            }

        except Exception as e:
            logger.error(f"Error in list_triggers: {e}")
            return {"success": False, "error": str(e)}

    async def start_trigger_scheduler(self, **kwargs) -> Dict[str, Any]:
        """Start the automated trigger scheduler"""
        try:
            if not self.automated_triggers:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            self.automated_triggers.start_scheduler()

            return {
                "success": True,
                "message": "Trigger scheduler started successfully",
            }

        except Exception as e:
            logger.error(f"Error in start_trigger_scheduler: {e}")
            return {"success": False, "error": str(e)}

    async def stop_trigger_scheduler(self, **kwargs) -> Dict[str, Any]:
        """Stop the automated trigger scheduler"""
        try:
            if not self.automated_triggers:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            self.automated_triggers.stop_scheduler()

            return {
                "success": True,
                "message": "Trigger scheduler stopped successfully",
            }

        except Exception as e:
            logger.error(f"Error in stop_trigger_scheduler: {e}")
            return {"success": False, "error": str(e)}

    async def get_performance_summary(self, **kwargs) -> Dict[str, Any]:
        """Get performance monitoring summary"""
        try:
            if not self.performance_monitor:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_name = kwargs.get("model_name")
            summary = self.performance_monitor.get_performance_summary(model_name)

            return {"success": True, "summary": summary}

        except Exception as e:
            logger.error(f"Error in get_performance_summary: {e}")
            return {"success": False, "error": str(e)}

    async def generate_performance_report(self, **kwargs) -> Dict[str, Any]:
        """Generate performance report"""
        try:
            if not self.performance_monitor:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_name = kwargs.get("model_name")
            report_format = kwargs.get("format", "json")

            report = self.performance_monitor.generate_performance_report(
                model_name, report_format
            )

            return {"success": True, "report": report}

        except Exception as e:
            logger.error(f"Error in generate_performance_report: {e}")
            return {"success": False, "error": str(e)}

    async def run_complete_pipeline(self, **kwargs) -> Dict[str, Any]:
        """Run the complete fine-tuning pipeline"""
        try:
            if not self.trainer:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            model_name = kwargs.get("model_name", "default_model")
            dataset_type = kwargs.get("dataset_type", "all")
            auto_deploy = kwargs.get("auto_deploy", True)

            logger.info(f"Running complete pipeline for {model_name}")

            # Step 1: Train model
            training_result = await self.train_model(
                model_name=model_name, dataset_type=dataset_type
            )

            if not training_result.get("success"):
                return training_result

            # Step 2: Deploy model if requested
            if auto_deploy:
                deployment_result = await self.deploy_model(model_name=model_name)
                if not deployment_result.get("success"):
                    logger.warning(
                        f"Auto-deployment failed: {deployment_result.get('error')}"
                    )

            # Step 3: Get final status
            status_result = await self.get_model_status(model_name=model_name)

            return {
                "success": True,
                "model_name": model_name,
                "training_result": training_result,
                "deployment_result": deployment_result if auto_deploy else None,
                "model_status": status_result,
            }

        except Exception as e:
            logger.error(f"Error in run_complete_pipeline: {e}")
            return {"success": False, "error": str(e)}

    async def cleanup_old_models(self, **kwargs) -> Dict[str, Any]:
        """Clean up old models and data"""
        try:
            if not self.integration:
                return {
                    "success": False,
                    "error": "Fine-tuning components not available",
                }

            days_old = kwargs.get("days_old", 30)
            backup = kwargs.get("backup", True)

            logger.info(f"Cleaning up models older than {days_old} days")

            models = self.integration.list_available_models()
            cutoff_date = datetime.now() - timedelta(days=days_old)
            removed_models = []

            for model in models:
                # Check if model is old enough to remove
                # This would need to be implemented based on actual model metadata
                if (
                    model.get("performance_score", 0) < 0.5
                ):  # Remove low-performing models
                    result = self.integration.remove_model(model["name"], backup)
                    if result.get("success"):
                        removed_models.append(model["name"])

            return {
                "success": True,
                "removed_models": removed_models,
                "total_removed": len(removed_models),
            }

        except Exception as e:
            logger.error(f"Error in cleanup_old_models: {e}")
            return {"success": False, "error": str(e)}
