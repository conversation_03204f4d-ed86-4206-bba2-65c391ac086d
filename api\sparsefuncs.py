"""
Sparse array utility functions.
"""

import numpy as np
from typing import Any, Optional, Union


def count_nonzero(X: Any, axis: Optional[int] = None, sample_weight: Optional[Any] = None) -> Union[int, np.ndarray]:
    """
    Count non-zero elements in an array.

    Parameters
    ----------
    X : array-like
        Input array
    axis : int, optional
        Axis along which to count non-zeros
    sample_weight : array-like, optional
        Sample weights

    Returns
    -------
    int or ndarray
        Number of non-zero elements
    """
    if hasattr(X, 'count_nonzero'):
        # For sparse matrices
        return X.count_nonzero(axis=axis)
    else:
        # For dense arrays
        if sample_weight is not None:
            # Apply sample weights
            weighted_X = X * sample_weight
            return np.count_nonzero(weighted_X, axis=axis)
        else:
            return np.count_nonzero(X, axis=axis)


__all__ = ["count_nonzero"]
