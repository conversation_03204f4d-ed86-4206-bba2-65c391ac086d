"""
Dashboard Authentication Module
JWT-based authentication for the dashboard API.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Optional, Union

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from passlib.context import CryptContext

from config.security import secure_config
from dashboard.database import get_db_context
from db.database_manager import User<PERSON>anager
from db.models import User

# Configure logging
logger = logging.getLogger(__name__)

# Security configuration
SECRET_KEY = secure_config.secret_key
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """Verify JWT token and return username"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if not isinstance(username, str) or not username:
            return None
        return username
    except JWTError:
        return None


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> User:
    """Get current user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        token = credentials.credentials
        username = verify_token(token)
        if username is None:
            raise credentials_exception
    except Exception:
        raise credentials_exception

    try:
        with get_db_context() as db:
            user_manager = UserManager()
            user = user_manager.get_by_username(db, username=username)
            if user is None:
                raise credentials_exception
            return user
    except Exception as e:
        logger.error(f"Error getting current user: {e}")
        raise credentials_exception


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """Get current superuser"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    return current_user


def authenticate_user(db: Any, username: str, password: str) -> Optional[User]:
    """Authenticate user with username and password"""
    try:
        user_manager = UserManager()
        user = user_manager.get_by_username(db, username=username)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        return None


def create_user(
    db: Any, username: str, email: str, password: str, is_superuser: bool = False
) -> Optional[User]:
    """Create a new user"""
    try:
        user_manager = UserManager()

        # Check if user already exists
        if user_manager.get_by_username(db, username=username):
            return None

        if user_manager.get_by_email(db, email=email):
            return None

        # Create user
        user_data = {
            "username": username,
            "email": email,
            "hashed_password": get_password_hash(password),
            "is_active": True,
            "is_superuser": is_superuser,
        }
        user = user_manager.create(db, obj_in=user_data)
        return user
    except Exception as e:
        logger.error(f"User creation error: {e}")
        return None


def update_user_password(db: Any, user: User, new_password: str) -> bool:
    """Update user password"""
    try:
        user_manager = UserManager()
        user.hashed_password = get_password_hash(new_password)
        user_manager.update(
            db, db_obj=user, obj_in={"hashed_password": user.hashed_password}
        )
        return True
    except Exception as e:
        logger.error(f"Password update error: {e}")
        return False


def deactivate_user(db: Any, user: User) -> bool:
    """Deactivate a user"""
    try:
        user_manager = UserManager()
        user.is_active = False
        user_manager.update(db, db_obj=user, obj_in={"is_active": False})
        return True
    except Exception as e:
        logger.error(f"User deactivation error: {e}")
        return False


def activate_user(db: Any, user: User) -> bool:
    """Activate a user"""
    try:
        user_manager = UserManager()
        user.is_active = True
        user_manager.update(db, db_obj=user, obj_in={"is_active": True})
        return True
    except Exception as e:
        logger.error(f"User activation error: {e}")
        return False


def grant_superuser(db: Any, user: User) -> bool:
    """Grant superuser privileges to a user"""
    try:
        user_manager = UserManager()
        user.is_superuser = True
        user_manager.update(db, db_obj=user, obj_in={"is_superuser": True})
        return True
    except Exception as e:
        logger.error(f"Superuser grant error: {e}")
        return False


def revoke_superuser(db: Any, user: User) -> bool:
    """Revoke superuser privileges from a user"""
    try:
        user_manager = UserManager()
        user.is_superuser = False
        user_manager.update(db, db_obj=user, obj_in={"is_superuser": False})
        return True
    except Exception as e:
        logger.error(f"Superuser revocation error: {e}")
        return False


# Utility functions for token management
def get_token_expiration(token: str) -> Optional[datetime]:
    """Get token expiration time"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        exp = payload.get("exp")
        if exp:
            return datetime.fromtimestamp(exp)
        return None
    except JWTError:
        return None


def is_token_expired(token: str) -> bool:
    """Check if token is expired"""
    exp = get_token_expiration(token)
    if exp is None:
        return True
    return datetime.utcnow() > exp


def refresh_token(token: str) -> Optional[str]:
    """Refresh an access token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if not isinstance(username, str) or not username:
            return None

        # Create new token with same data but new expiration
        return create_access_token(data={"sub": username})
    except JWTError:
        return None
