module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../$1',
  },
  testMatch: [
    '<rootDir>/../**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/../**/*.{spec,test}.{js,jsx,ts,tsx}',
  ],
  collectCoverageFrom: [
    '<rootDir>/../**/*.{js,jsx,ts,tsx}',
    '!<rootDir>/../**/*.d.ts',
    '!<rootDir>/../**/*.stories.{js,jsx,ts,tsx}',
    '!<rootDir>/../**/__tests__/**',
    '!<rootDir>/../**/node_modules/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { configFile: './config/babel.config.js' }],
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  testPathIgnorePatterns: ['<rootDir>/../node_modules/', '<rootDir>/../.next/'],
  transformIgnorePatterns: [
    '/node_modules/',
    '^.+\\.module\\.(css|sass|scss)$',
  ],
  roots: ['<rootDir>/..'],
};
