#!/usr/bin/env python3
"""
Robustness CLI Commands for AI Coding Agent
Provides command-line interface for recovery, validation, and monitoring features.
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class RobustnessCommands:
    """CLI commands for robustness features"""

    def __init__(self, agent):
        self.agent = agent

    async def run_system_validation(self, detailed: bool = False) -> Dict[str, Any]:
        """Run comprehensive system validation"""
        try:
            from core.validation import run_system_validation

            logger.info("Starting comprehensive system validation...")
            results = await run_system_validation()

            if detailed:
                return {
                    "success": True,
                    "message": "System validation completed",
                    "results": results,
                }
            else:
                # Return simplified results
                return {
                    "success": True,
                    "message": f"System validation: {results['overall_status'].upper()}",
                    "summary": {
                        "status": results["overall_status"],
                        "passed": results["passed_validators"],
                        "failed": results["failed_validators"],
                        "critical_issues": results["critical_issues"],
                        "high_issues": results["high_issues"],
                    },
                }

        except Exception as e:
            logger.error(f"System validation failed: {e}")
            return {"success": False, "error": str(e)}

    async def check_system_health(self) -> Dict[str, Any]:
        """Check overall system health"""
        try:
            from core.monitoring_enhanced import get_metrics_collector
            from core.recovery_system import get_recovery_system
            from core.validation import get_validation_system

            validation_system = get_validation_system()
            recovery_system = get_recovery_system()
            metrics_collector = get_metrics_collector()

            # Get health status from all systems
            validation_health = validation_system.get_system_health()
            recovery_health = recovery_system.get_system_health()
            current_metrics = metrics_collector.get_current_metrics()

            # Determine overall health
            health_statuses = [
                validation_health.get("status", "unknown"),
                recovery_health.get("status", "unknown"),
            ]

            if "critical" in health_statuses:
                overall_status = "critical"
            elif "degraded" in health_statuses:
                overall_status = "degraded"
            elif "warning" in health_statuses:
                overall_status = "warning"
            elif all(status == "healthy" for status in health_statuses):
                overall_status = "healthy"
            else:
                overall_status = "unknown"

            return {
                "success": True,
                "overall_status": overall_status,
                "components": {
                    "validation": validation_health,
                    "recovery": recovery_health,
                    "monitoring": {
                        "active": current_metrics.get("monitoring_active", False),
                        "latest_metrics": current_metrics.get("system") is not None,
                    },
                },
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"success": False, "error": str(e)}

    async def start_monitoring(self) -> Dict[str, Any]:
        """Start the monitoring system"""
        try:
            from core.monitoring_enhanced import start_monitoring

            start_monitoring()

            return {
                "success": True,
                "message": "Monitoring system started successfully",
            }

        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
            return {"success": False, "error": str(e)}

    async def stop_monitoring(self) -> Dict[str, Any]:
        """Stop the monitoring system"""
        try:
            from core.monitoring_enhanced import stop_monitoring

            stop_monitoring()

            return {
                "success": True,
                "message": "Monitoring system stopped successfully",
            }

        except Exception as e:
            logger.error(f"Failed to stop monitoring: {e}")
            return {"success": False, "error": str(e)}

    async def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get monitoring dashboard data"""
        try:
            from core.monitoring_enhanced import get_monitoring_dashboard

            dashboard = get_monitoring_dashboard()
            dashboard_data = dashboard.get_dashboard_data()

            return {
                "success": True,
                "message": "Dashboard data retrieved successfully",
                "data": dashboard_data,
            }

        except Exception as e:
            logger.error(f"Failed to get dashboard data: {e}")
            return {"success": False, "error": str(e)}

    async def get_alerts_summary(self) -> Dict[str, Any]:
        """Get summary of current alerts"""
        try:
            from core.monitoring_enhanced import get_monitoring_dashboard

            dashboard = get_monitoring_dashboard()
            alerts_summary = dashboard.get_alerts_summary()

            return {
                "success": True,
                "message": "Alerts summary retrieved successfully",
                "summary": alerts_summary,
            }

        except Exception as e:
            logger.error(f"Failed to get alerts summary: {e}")
            return {"success": False, "error": str(e)}

    async def manual_recovery(
        self, failure_type: str, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Manually trigger recovery for a specific failure type"""
        try:
            from core.recovery_system import FailureType, get_recovery_system

            recovery_system = get_recovery_system()

            # Map string to FailureType enum
            failure_type_map = {
                "file_corruption": FailureType.FILE_CORRUPTION,
                "database_corruption": FailureType.DATABASE_CORRUPTION,
                "configuration_error": FailureType.CONFIGURATION_ERROR,
                "dependency_missing": FailureType.DEPENDENCY_MISSING,
                "permission_error": FailureType.PERMISSION_ERROR,
                "disk_space_low": FailureType.DISK_SPACE_LOW,
                "memory_low": FailureType.MEMORY_LOW,
                "network_error": FailureType.NETWORK_ERROR,
                "process_hang": FailureType.PROCESS_HANG,
                "import_error": FailureType.IMPORT_ERROR,
                "validation_error": FailureType.VALIDATION_ERROR,
                "backup_failure": FailureType.BACKUP_FAILURE,
                "deployment_failure": FailureType.DEPLOYMENT_FAILURE,
            }

            if failure_type not in failure_type_map:
                return {
                    "success": False,
                    "error": f"Unknown failure type: {failure_type}",
                }

            failure_enum = failure_type_map[failure_type]
            success = await recovery_system.manual_recovery(failure_enum, context)

            return {
                "success": True,
                "message": f"Manual recovery for {failure_type} {'succeeded' if success else 'failed'}",
                "recovery_success": success,
            }

        except Exception as e:
            logger.error(f"Manual recovery failed: {e}")
            return {"success": False, "error": str(e)}

    async def get_recovery_history(self, hours: int = 24) -> Dict[str, Any]:
        """Get recovery system history"""
        try:
            from core.recovery_system import get_recovery_system

            recovery_system = get_recovery_system()
            failure_history = recovery_system.get_failure_history(hours)

            return {
                "success": True,
                "message": f"Recovery history for last {hours} hours",
                "history": [
                    {
                        "failure_type": failure.failure_type.value,
                        "severity": failure.severity,
                        "description": failure.description,
                        "timestamp": failure.timestamp.isoformat(),
                        "recovery_attempts": failure.recovery_attempts,
                    }
                    for failure in failure_history
                ],
                "total_failures": len(failure_history),
            }

        except Exception as e:
            logger.error(f"Failed to get recovery history: {e}")
            return {"success": False, "error": str(e)}

    async def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get metrics summary for specified time period"""
        try:
            from core.monitoring_enhanced import get_metrics_collector

            metrics_collector = get_metrics_collector()
            summary = metrics_collector.get_metrics_summary(hours)

            return {
                "success": True,
                "message": f"Metrics summary for last {hours} hours",
                "summary": summary,
            }

        except Exception as e:
            logger.error(f"Failed to get metrics summary: {e}")
            return {"success": False, "error": str(e)}

    async def validate_specific_component(self, component_name: str) -> Dict[str, Any]:
        """Validate a specific system component"""
        try:
            from core.validation import get_validation_system

            validation_system = get_validation_system()
            result = await validation_system.validate_specific_component(component_name)

            return {
                "success": True,
                "message": f"Component validation: {result.message}",
                "result": {
                    "success": result.success,
                    "level": result.level.value,
                    "message": result.message,
                    "details": result.details,
                },
            }

        except Exception as e:
            logger.error(f"Component validation failed: {e}")
            return {"success": False, "error": str(e)}

    async def run_robustness_test(self) -> Dict[str, Any]:
        """Run a comprehensive robustness test"""
        try:
            logger.info("Starting comprehensive robustness test...")

            # Run system validation
            validation_result = await self.run_system_validation(detailed=True)

            # Check system health
            health_result = await self.check_system_health()

            # Get monitoring status
            monitoring_result = await self.get_monitoring_dashboard()

            # Determine overall robustness score
            score = 100

            if not validation_result.get("success", False):
                score -= 30

            if health_result.get("overall_status") != "healthy":
                score -= 20

            if not monitoring_result.get("success", False):
                score -= 10

            # Check for critical issues
            if validation_result.get("results", {}).get("critical_issues", 0) > 0:
                score -= 40

            score = max(0, score)

            return {
                "success": True,
                "message": "Robustness test completed",
                "robustness_score": score,
                "test_results": {
                    "validation": validation_result,
                    "health": health_result,
                    "monitoring": monitoring_result,
                },
                "recommendations": self._generate_recommendations(
                    validation_result, health_result, score
                ),
            }

        except Exception as e:
            logger.error(f"Robustness test failed: {e}")
            return {"success": False, "error": str(e)}

    def _generate_recommendations(
        self,
        validation_result: Dict[str, Any],
        health_result: Dict[str, Any],
        score: int,
    ) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []

        if score < 50:
            recommendations.append("CRITICAL: System needs immediate attention")

        if score < 70:
            recommendations.append(
                "HIGH: Address critical issues before production use"
            )

        if score < 90:
            recommendations.append(
                "MEDIUM: Consider improvements for better reliability"
            )

        # Specific recommendations based on validation results
        if validation_result.get("results", {}).get("critical_issues", 0) > 0:
            recommendations.append("Fix critical validation issues immediately")

        if validation_result.get("results", {}).get("high_issues", 0) > 0:
            recommendations.append("Address high-priority validation issues")

        if health_result.get("overall_status") != "healthy":
            recommendations.append("Resolve system health issues")

        if not validation_result.get("success", False):
            recommendations.append("Ensure validation system is working properly")

        if not health_result.get("success", False):
            recommendations.append("Check health monitoring system")

        return recommendations
