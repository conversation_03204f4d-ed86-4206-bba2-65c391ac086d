import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert<PERSON>riangle, RefreshCw, X, Info } from 'lucide-react';
import { errorHandlingService } from '@/services/ErrorHandlingService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  isRetrying: boolean;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    showDetails: false,
    isRetrying: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      showDetails: false,
      isRetrying: false
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to service
    errorHandlingService.handleError(error, 'react-error-boundary', 'component-error');

    this.setState({ errorInfo });

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = async () => {
    this.setState({ isRetrying: true });

    try {
      // Wait a moment before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset error state
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        showDetails: false,
        isRetrying: false
      });
    } catch (error) {
      this.setState({ isRetrying: false });
      errorHandlingService.handleError(error as Error, 'retry-failed');
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  private dismissError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      isRetrying: false
    });
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-6 h-6 text-red-500" />
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Something went wrong
                </h2>
              </div>
              <button
                onClick={this.dismissError}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <p className="text-gray-600 dark:text-gray-400 mb-4">
              We encountered an unexpected error. Don't worry, your work is safe.
            </p>

            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                disabled={this.state.isRetrying}
                className="w-full flex items-center justify-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {this.state.isRetrying ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                <span>{this.state.isRetrying ? 'Retrying...' : 'Try Again'}</span>
              </button>

              <button
                onClick={this.handleReload}
                className="w-full bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
              >
                Reload Page
              </button>

              <button
                onClick={this.toggleDetails}
                className="w-full flex items-center justify-center space-x-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                <Info className="w-4 h-4" />
                <span>{this.state.showDetails ? 'Hide Details' : 'Show Details'}</span>
              </button>
            </div>

            {this.state.showDetails && (
              <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Error Details
                </h3>
                <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                  <p><strong>Message:</strong> {this.state.error?.message}</p>
                  <p><strong>Component:</strong> {this.state.errorInfo?.componentStack?.split('\n')[1]?.trim()}</p>
                  <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
                </div>
                {this.state.error?.stack && (
                  <details className="mt-2">
                    <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                      Stack Trace
                    </summary>
                    <pre className="text-xs text-gray-600 dark:text-gray-400 mt-1 whitespace-pre-wrap overflow-auto max-h-32">
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </div>
            )}

            <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 text-center">
              Error ID: {errorHandlingService.getErrors().slice(-1)[0]?.id || 'unknown'}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
