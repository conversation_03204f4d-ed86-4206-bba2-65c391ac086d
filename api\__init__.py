"""
API Package for AI Coding Agent
Provides RESTful API endpoints for chat, file uploads, and error handling.
"""

# FastAPI imports for API routes
from api.chat_routes import router as chat_router
from api.error_routes import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from api.error_routes import router as error_router
from api.upload_routes import router as upload_router

__all__ = ["chat_router", "upload_router", "error_router", "ErrorHandler"]

# API version
__version__ = "1.0.0"

# API metadata
API_METADATA = {
    "title": "AI Coding Agent API",
    "description": "RESTful API for AI-powered coding assistance and project management",
    "version": __version__,
    "contact": {"name": "AI Coding Agent Team", "email": "<EMAIL>"},
    "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"},
}
