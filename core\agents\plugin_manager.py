#!/usr/bin/env python3
"""
Plugin Manager Module - Plugin System Management
Extracted from core/agent.py for modular organization
"""

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional

from core.agents.base_agent import AgentLogger

if TYPE_CHECKING:
    from core.agents.agent_main import AIAgent


class PluginInterface(ABC):
    """Base interface for all plugins"""

    @abstractmethod
    def initialize(self, agent: "AIAgent") -> bool:
        """Initialize the plugin with the agent"""
        pass

    @abstractmethod
    def execute(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the plugin functionality"""
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """Cleanup plugin resources"""
        pass


class PluginManager:
    """Manages plugin loading, execution, and lifecycle"""

    def __init__(self, agent: "AIAgent"):
        self.agent = agent
        self.logger = agent.logger
        self.plugins: Dict[str, PluginInterface] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}

    def register_plugin(
        self, name: str, plugin: PluginInterface, config: Dict[str, Any] = None
    ) -> bool:
        """Register a new plugin"""
        try:
            if name in self.plugins:
                self.logger.warning(f"Plugin {name} already registered, overwriting")

            # Initialize the plugin
            if plugin.initialize(self.agent):
                self.plugins[name] = plugin
                self.plugin_configs[name] = config or {}
                self.logger.info(f"Plugin {name} registered successfully")
                return True
            else:
                self.logger.error(f"Failed to initialize plugin {name}")
                return False

        except Exception as e:
            self.logger.error(f"Error registering plugin {name}: {e}", error=e)
            return False

    def unregister_plugin(self, name: str) -> bool:
        """Unregister a plugin"""
        try:
            if name in self.plugins:
                plugin = self.plugins[name]
                if plugin.cleanup():
                    del self.plugins[name]
                    del self.plugin_configs[name]
                    self.logger.info(f"Plugin {name} unregistered successfully")
                    return True
                else:
                    self.logger.error(f"Failed to cleanup plugin {name}")
                    return False
            else:
                self.logger.warning(f"Plugin {name} not found")
                return False

        except Exception as e:
            self.logger.error(f"Error unregistering plugin {name}: {e}", error=e)
            return False

    async def execute_plugin(self, name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a specific plugin"""
        try:
            if name not in self.plugins:
                return {
                    "success": False,
                    "error": f"Plugin {name} not found",
                    "available_plugins": list(self.plugins.keys()),
                }

            plugin = self.plugins[name]
            config = self.plugin_configs[name]

            # Merge config with data
            execution_data = {**config, **data}

            result = plugin.execute(execution_data)
            self.logger.info(f"Plugin {name} executed successfully", result=result)

            return {"success": True, "plugin": name, "result": result}

        except Exception as e:
            self.logger.error(f"Error executing plugin {name}: {e}", error=e)
            return {"success": False, "error": str(e), "plugin": name}

    async def execute_plugin_chain(
        self, plugin_names: List[str], data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Execute multiple plugins in sequence"""
        results = []
        current_data = data.copy()

        for plugin_name in plugin_names:
            result = await self.execute_plugin(plugin_name, current_data)
            results.append(result)

            # Pass result to next plugin if successful
            if result.get("success"):
                current_data = result.get("result", {})
            else:
                # Stop chain on failure
                break

        return results

    def get_plugin_info(self, name: str) -> Dict[str, Any]:
        """Get information about a plugin"""
        if name in self.plugins:
            plugin = self.plugins[name]
            return {
                "name": name,
                "type": type(plugin).__name__,
                "config": self.plugin_configs[name],
                "active": True,
            }
        else:
            return {"name": name, "active": False, "error": "Plugin not found"}

    def list_plugins(self) -> List[Dict[str, Any]]:
        """List all registered plugins"""
        return [self.get_plugin_info(name) for name in self.plugins.keys()]

    def get_plugin_config(self, name: str) -> Dict[str, Any]:
        """Get configuration for a plugin"""
        return self.plugin_configs.get(name, {})

    def update_plugin_config(self, name: str, config: Dict[str, Any]) -> bool:
        """Update configuration for a plugin"""
        try:
            if name in self.plugins:
                self.plugin_configs[name].update(config)
                self.logger.info(f"Updated config for plugin {name}")
                return True
            else:
                self.logger.warning(f"Plugin {name} not found for config update")
                return False
        except Exception as e:
            self.logger.error(f"Error updating plugin config {name}: {e}", error=e)
            return False
