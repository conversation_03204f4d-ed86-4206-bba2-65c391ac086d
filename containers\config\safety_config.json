{"safety_boundaries": {"description": "Safety configuration for AI Coding Agent website generation", "version": "1.3.0", "enforced": true, "last_updated": "2025-07-23T18:30:00Z"}, "path_validation": {"allowed_site_paths": ["sites/"], "forbidden_paths": ["src/", "scripts/", "config/", "tests/", "docs/", ".venv/", "node_modules/", ".git/", "logs/", "data/", "backups/", "deployments/", "themes/", "templates/"], "forbidden_characters": ["..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|"], "max_path_length": 255, "require_absolute_paths": false}, "subprocess_safety": {"allowed_commands": ["npm", "yarn", "node", "python", "pip", "git", "openssl", "certbot", "nginx"], "forbidden_commands": ["rm", "del", "format", "fdisk", "mkfs", "dd", "shutdown", "reboot", "halt"], "require_cwd_validation": true, "max_execution_time": 300, "allowed_working_directories": ["sites/*"], "forbidden_working_directories": ["src/", "scripts/", "config/", ".", "/", "C:/", "D:/", "E:/", "F:/", "G:/"]}, "file_operations": {"require_backup_before_modification": true, "backup_formats": ["zip"], "max_file_size_mb": 100, "allowed_file_extensions": [".html", ".css", ".js", ".ts", ".tsx", ".jsx", ".json", ".md", ".txt", ".jpg", ".jpeg", ".png", ".gif", ".svg", ".webp", ".ico", ".woff", ".woff2", ".ttf", ".otf"], "forbidden_file_extensions": [".exe", ".bat", ".cmd", ".ps1", ".sh", ".py", ".php", ".rb", ".pl", ".sql", ".db", ".sqlite"]}, "site_manifest": {"required_fields": ["name", "created_at", "generator", "template", "status"], "optional_fields": ["theme", "framework", "live_url", "author", "version", "description"], "safety_checks": ["path_validated", "backup_created", "read_only"], "manifest_filename": "site.config.json"}, "build_isolation": {"enforce_site_specific_builds": true, "build_directories": ["dist/", ".next/", "build/", "out/", "public/", "node_modules/"], "prevent_root_builds": true, "require_site_manifest": true}, "backup_management": {"auto_backup_enabled": true, "backup_before_operations": ["create_website", "delete_site", "deploy_site", "modify_site"], "backup_retention_days": 30, "max_backups_per_site": 10, "backup_compression": true, "backup_verification": true}, "validation_rules": {"site_name_validation": {"min_length": 1, "max_length": 50, "allowed_pattern": "^[a-zA-Z0-9_-]+$", "forbidden_words": ["admin", "root", "system", "config", "backup", "temp", "tmp"]}, "template_validation": {"require_template_exists": true, "validate_template_structure": true, "check_template_permissions": true}, "content_validation": {"max_title_length": 100, "max_description_length": 500, "sanitize_html": true, "prevent_script_injection": true}}, "security_checks": {"scan_for_secrets": true, "validate_file_permissions": true, "check_for_malicious_content": true, "verify_ssl_certificates": true, "audit_dependencies": true}, "monitoring": {"log_all_operations": true, "track_file_changes": true, "monitor_subprocess_execution": true, "alert_on_safety_violations": true, "audit_trail_retention_days": 90}, "emergency_protocols": {"emergency_stop_enabled": true, "auto_rollback_on_failure": true, "isolate_compromised_sites": true, "notify_on_critical_errors": true, "emergency_contact": "<EMAIL>"}}