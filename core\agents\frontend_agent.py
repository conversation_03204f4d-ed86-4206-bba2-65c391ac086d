#!/usr/bin/env python3
"""
FrontendAgent - Specialized agent for frontend development tasks

Handles:
- UI/UX component generation
- React/Next.js application creation
- Styling and theming
- Frontend optimization
- Component library management
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.agents.enhanced_base_agent import EnhancedBaseAgent
from core.agents.enhanced_base_agent import TaskStatus as EnhancedTaskStatus
from core.agents.enhanced_base_agent import VerificationLevel
from core.code_generation.ast_code_generator import (
    ASTCodeGenerator,
    CodeLanguage,
    GenerationContext,
    GenerationStrategy,
)
from models.model_router import get_model_router, ModernModelRouter
from templates.template_manager import TemplateManager
from utils.code_generator import CodeGenerator

logger = logging.getLogger(__name__)


class FrontendAgent(EnhancedBaseAgent):
    """Specialized agent for frontend development tasks"""

    def __init__(self, config_path: str = "config/frontend_agent_config.json"):
        """Initialize the FrontendAgent"""
        default_config = {
            "memory_tracking": {
                "enabled": True,
                "max_attempts_per_task": 3,
                "cooldown_seconds": 300,
                "verify_tasks": True,
                "reset_on_success": True,
                "verification_level": "comprehensive",
                "persistence": True,
                "cleanup_interval": 86400,
            },
            "model_settings": {
                "model_name": "starcoder2:3b",  # Updated to use YAML config
                "system_prompt": "You are a frontend AI assistant specialized in React/Next.js development and UI/UX design.",
            },
            "framework": "nextjs",
            "styling": "tailwind",
            "components_library": "shadcn/ui",
            "default_theme": "light",
            "responsive_design": True,
            "accessibility": True,
            "performance_optimization": True,
        }
        super().__init__(config_path, default_config)

        # Initialize modern model router
        self.model_router = get_model_router()

        # Get agent-specific model and system prompt from YAML config
        self.agent_name = "frontend_agent"
        self.model_name = self.model_router.get_agent_model(self.agent_name) or "starcoder2:3b"
        self.system_prompt = self.model_router.get_agent_system_prompt(self.agent_name) or self.system_prompt

        self.code_generator = CodeGenerator()
        self.template_manager = TemplateManager()
        self.ast_generator = ASTCodeGenerator()

        logger.info(
            f"FrontendAgent initialized successfully with model {self.model_name}"
        )

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description for frontend agent

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        try:
            prompt = f"""
            {self.system_prompt}

            Parse the following frontend development task and extract requirements:

            Task: {task_description}

            Extract:
            1. Required pages/components
            2. Styling requirements
            3. Functionality requirements
            4. Data requirements
            5. Integration requirements

            Return as JSON.
            """

            # Use the new model router
            response_dict = await self.model_router.generate_response(
                prompt=prompt,
                model_name=self.model_name,
                agent_name=self.agent_name,
                task_type="code_generation"
            )

            if response_dict.get("success", False):
                response = response_dict.get("content", "")
            else:
                logger.warning(f"Model response failed: {response_dict.get('error', 'Unknown error')}")
                return self._fallback_parse_requirements(task_description)

            try:
                # Try to parse JSON from response
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_str = response[json_start:json_end].strip()
                    requirements = json.loads(json_str)
                    requirements["parsed"] = True
                    return requirements
                else:
                    # Fallback parsing
                    requirements = self._fallback_parse_requirements(task_description)
                    requirements["parsed"] = True
                    return requirements
            except Exception as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                requirements = self._fallback_parse_requirements(task_description)
                requirements["parsed"] = True
                return requirements

        except Exception as e:
            logger.error(f"Error parsing frontend task requirements: {e}")
            return {"description": task_description, "parsed": False, "error": str(e)}

    def _fallback_parse_requirements(self, task_description: str) -> Dict[str, Any]:
        """Fallback parsing for requirements"""
        requirements = {
            "pages": [],
            "components": [],
            "styling": "modern",
            "functionality": [],
            "data_requirements": [],
            "integrations": [],
        }

        # Simple keyword-based parsing
        description_lower = task_description.lower()

        if "portfolio" in description_lower:
            requirements["pages"].extend(["home", "about", "projects", "contact"])
            requirements["components"].extend(
                ["header", "footer", "project-card", "contact-form"]
            )

        if "blog" in description_lower:
            requirements["pages"].extend(["blog", "blog-post"])
            requirements["components"].extend(["blog-list", "blog-post", "sidebar"])

        if "contact" in description_lower:
            requirements["components"].append("contact-form")

        if "responsive" in description_lower or "mobile" in description_lower:
            requirements["styling"] = "responsive"

        return requirements

    async def _generate_components(
        self, requirements: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate React components based on requirements"""
        components = []

        for component_name in requirements.get("components", []):
            try:
                component_code = await self._generate_component_code(
                    component_name, requirements
                )
                components.append(
                    {
                        "name": component_name,
                        "code": component_code,
                        "type": "react_component",
                    }
                )
            except Exception as e:
                logger.error(f"Error generating component {component_name}: {e}")

        return components

    async def _generate_component_code(
        self, component_name: str, requirements: Dict[str, Any]
    ) -> str:
        """Generate component code using AST-based generation"""
        try:
            # Determine language based on framework
            language = (
                CodeLanguage.TSX
                if self.config.get("framework") == "nextjs"
                else CodeLanguage.JSX
            )

            # Create generation context
            context = GenerationContext(
                language=language,
                target_file=f"components/{component_name}.{language.value}",
                requirements=[
                    f"Create React component: {component_name}",
                    f"Framework: {self.config.get('framework', 'nextjs')}",
                    f"Styling: {self.config.get('styling', 'tailwind')}",
                    f"Components Library: {self.config.get('components_library', 'shadcn/ui')}",
                    f"Responsive Design: {self.config.get('responsive_design', True)}",
                    f"Accessibility: {self.config.get('accessibility', True)}",
                ]
                + [str(req) for req in requirements.get("additional_requirements", [])],
                constraints=[
                    "Use functional components",
                    (
                        "Include proper TypeScript types"
                        if language == CodeLanguage.TSX
                        else "Use proper JavaScript"
                    ),
                    "Follow React best practices",
                    "Include proper error handling",
                ],
                conventions={
                    "naming": "PascalCase",
                    "max_line_length": 100,
                    "import_style": "relative",
                },
            )

            # Generate code using AST generator
            result = await self.ast_generator.generate_code(
                context, GenerationStrategy.HYBRID
            )

            if result and result.code:
                return result.code
            else:
                # Fallback to traditional generation
                return await self._fallback_component_generation(
                    component_name, requirements
                )

        except Exception as e:
            logger.warning(f"AST generation failed for {component_name}: {e}")
            # Fallback to traditional generation
            return await self._fallback_component_generation(
                component_name, requirements
            )

    async def _fallback_component_generation(
        self, component_name: str, requirements: Dict[str, Any]
    ) -> str:
        """Fallback component generation using traditional LLM approach"""
        prompt = f"""
        {self.system_prompt}

        Generate a React component for: {component_name}

        Requirements:
        - Framework: {self.config['framework']}
        - Styling: {self.config['styling']}
        - Component library: {self.config['components_library']}
        - Responsive: {self.config['responsive_design']}
        - Accessibility: {self.config['accessibility']}

        Additional context: {requirements}

        Generate a complete, production-ready component with:
        1. TypeScript interfaces
        2. Proper error handling
        3. Accessibility features
        4. Responsive design
        5. Modern styling
        """

        # Use the new model router
        response_dict = await self.model_router.generate_response(
            prompt=prompt,
            model_name=self.model_name,
            agent_name=self.agent_name,
            task_type="code_generation"
        )

        if response_dict.get("success", False):
            return response_dict.get("content", "")
        else:
            logger.warning(f"Component generation failed: {response_dict.get('error', 'Unknown error')}")
            return f"// Failed to generate component {component_name}"

    async def _create_project_structure(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create the project directory structure"""
        structure = {
            "pages": requirements.get("pages", []),
            "components": requirements.get("components", []),
            "directories": [
                "components",
                "pages",
                "styles",
                "utils",
                "hooks",
                "types",
                "public",
            ],
        }

        return structure

    async def _generate_config_files(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, str]:
        """Generate configuration files for the frontend project"""
        config_files = {}

        # package.json
        config_files["package.json"] = self._generate_package_json(requirements)

        # tsconfig.json
        config_files["tsconfig.json"] = self._generate_tsconfig_json()

        # tailwind.config.js
        if self.config["styling"] == "tailwind":
            config_files["tailwind.config.js"] = self._generate_tailwind_config()

        # next.config.js
        if self.config["framework"] == "nextjs":
            config_files["next.config.js"] = self._generate_next_config()

        return config_files

    def _generate_package_json(self, requirements: Dict[str, Any]) -> str:
        """Generate package.json content"""
        dependencies = {
            "react": "^18.2.0",
            "react-dom": "^18.2.0",
            "next": "^14.0.0",
            "typescript": "^5.0.0",
            "@types/node": "^20.0.0",
            "@types/react": "^18.2.0",
            "@types/react-dom": "^18.2.0",
        }

        if self.config["styling"] == "tailwind":
            dependencies.update(
                {
                    "tailwindcss": "^3.3.0",
                    "autoprefixer": "^10.4.0",
                    "postcss": "^8.4.0",
                }
            )

        if self.config["components_library"] == "shadcn/ui":
            dependencies.update(
                {
                    "@radix-ui/react-slot": "^1.0.0",
                    "class-variance-authority": "^0.7.0",
                    "clsx": "^2.0.0",
                    "tailwind-merge": "^2.0.0",
                }
            )

        return json.dumps(
            {
                "name": "frontend-project",
                "version": "1.0.0",
                "private": True,
                "scripts": {
                    "dev": "next dev",
                    "build": "next build",
                    "start": "next start",
                    "lint": "next lint",
                },
                "dependencies": dependencies,
                "devDependencies": {
                    "eslint": "^8.0.0",
                    "eslint-config-next": "^14.0.0",
                },
            },
            indent=2,
        )

    def _generate_tsconfig_json(self) -> str:
        """Generate tsconfig.json content"""
        return json.dumps(
            {
                "compilerOptions": {
                    "target": "es5",
                    "lib": ["dom", "dom.iterable", "es6"],
                    "allowJs": True,
                    "skipLibCheck": True,
                    "strict": True,
                    "forceConsistentCasingInFileNames": True,
                    "noEmit": True,
                    "esModuleInterop": True,
                    "module": "esnext",
                    "moduleResolution": "node",
                    "resolveJsonModule": True,
                    "isolatedModules": True,
                    "jsx": "preserve",
                    "incremental": True,
                    "plugins": [{"name": "next"}],
                    "paths": {"@/*": ["./*"]},
                },
                "include": [
                    "next-env.d.ts",
                    "**/*.ts",
                    "**/*.tsx",
                    ".next/types/**/*.ts",
                ],
                "exclude": ["node_modules"],
            },
            indent=2,
        )

    def _generate_tailwind_config(self) -> str:
        """Generate tailwind.config.js content"""
        return """
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
      },
    },
  },
  plugins: [],
}
"""

    def _generate_next_config(self) -> str:
        """Generate next.config.js content"""
        return """
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: [],
  },
}

module.exports = nextConfig
"""

    async def _create_docker_config(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, str]:
        """Create Docker configuration for the frontend"""
        docker_files = {}

        # Dockerfile
        docker_files[
            "Dockerfile"
        ] = """
# Frontend Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
"""

        # .dockerignore
        docker_files[
            ".dockerignore"
        ] = """
node_modules
.next
.git
.env
.env.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
"""

        return docker_files

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation for frontend agent

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        try:
            if not requirements.get("parsed", False):
                return {
                    "success": False,
                    "error": "Failed to parse task requirements",
                    "task_id": task_id,
                }

            logger.info(
                f"Executing frontend task: {requirements.get('description', task_id)}"
            )

            # Execute frontend tasks based on requirements
            results = {}

            # Generate components
            if requirements.get("components"):
                results["components"] = await self._generate_components(requirements)

            # Create project structure
            results["project_structure"] = await self._create_project_structure(
                requirements
            )

            # Generate configuration files
            results["config_files"] = await self._generate_config_files(requirements)

            # Create Docker configuration
            results["docker_config"] = await self._create_docker_config(requirements)

            # Determine overall success
            all_successful = all(
                result.get("success", False) if isinstance(result, dict) else True
                for result in results.values()
            )

            return {
                "success": all_successful,
                "task_id": task_id,
                "results": results,
                "requirements": requirements,
            }

        except Exception as e:
            logger.error(f"Error executing frontend task {task_id}: {e}")
            return {"success": False, "error": str(e), "task_id": task_id}

    async def _verify_task_specific(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Frontend-specific task verification

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        try:
            # Check if task was successful
            if not result.get("success", False):
                return False

            # Check if all frontend components were generated successfully
            results = result.get("results", {})
            if not results:
                return False

            # Verify each frontend component
            for component, component_result in results.items():
                if isinstance(component_result, dict) and not component_result.get(
                    "success", True
                ):
                    logger.warning(
                        f"Frontend component {component} failed verification"
                    )
                    return False

            # Additional verification for frontend-specific tasks
            if (
                "frontend" in task_description.lower()
                or "ui" in task_description.lower()
            ):
                # Check if components were generated
                if "components" not in results:
                    logger.warning("Components not generated")
                    return False

                # Check if project structure was created
                if "project_structure" not in results:
                    logger.warning("Project structure not created")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error during frontend task verification: {e}")
            return False
