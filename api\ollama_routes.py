#!/usr/bin/env python3
"""
Ollama API Routes
Provides REST API endpoints for Ollama model management

Endpoints:
- GET /api/ollama/status - Get comprehensive status
- GET /api/ollama/models - List available models
- POST /api/ollama/switch - Switch to different model
- POST /api/ollama/pull - Pull new model
- POST /api/ollama/generate - Generate response
- GET /api/ollama/performance - Get performance stats
- POST /api/ollama/optimize - Optimize model selection
- POST /api/ollama/test - Test specific model
- POST /api/ollama/compare - Compare multiple models
- GET /api/ollama/approved - Get approved models list
- POST /api/ollama/validate - Validate model
"""

import asyncio

# Add project root to path for imports
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from api.agent_dependency import get_agent
from models.ollama_manager import get_ollama_manager

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


router = APIRouter(prefix="/api/ollama", tags=["Ollama Model Management"])


# Pydantic models for request/response
class ModelSwitchRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to switch to")


class ModelPullRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to pull")


class GenerateRequest(BaseModel):
    prompt: str = Field(..., description="Input prompt for generation")
    model_name: Optional[str] = Field(
        None, description="Specific model to use (optional)"
    )
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(None, description="Temperature for generation")
    top_p: Optional[float] = Field(None, description="Top-p sampling parameter")


class TestModelRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to test")
    test_prompt: str = Field(
        "Write a simple Python function to add two numbers",
        description="Test prompt to use",
    )


class CompareModelsRequest(BaseModel):
    prompt: str = Field(..., description="Prompt to use for comparison")
    models: Optional[List[str]] = Field(None, description="List of models to compare")


class ValidateModelRequest(BaseModel):
    model_name: str = Field(..., description="Name of the model to validate")


class StatusResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any]


class ModelsResponse(BaseModel):
    success: bool
    message: str
    models: Dict[str, Any]
    approved_models: List[str]
    current_model: str


class GenerateResponse(BaseModel):
    success: bool
    message: str
    response: Optional[str] = None
    model: str
    response_time: float
    tokens_used: int
    prompt_tokens: int


class PerformanceResponse(BaseModel):
    success: bool
    message: str
    performance: Dict[str, Any]


class TestResponse(BaseModel):
    success: bool
    message: str
    model: str
    test_prompt: str
    response: str
    response_time: float
    tokens_used: int


class CompareResponse(BaseModel):
    success: bool
    message: str
    prompt: str
    models: Dict[str, Any]


class ValidationResponse(BaseModel):
    success: bool
    message: str
    model_name: str
    is_approved: bool
    is_available: bool
    can_use: bool
    recommendation: str


@router.get("/status", response_model=StatusResponse)
async def get_ollama_status(agent=Depends(get_agent)):
    """Get comprehensive status of Ollama manager"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.get_status()

        return StatusResponse(
            success=True, message="Ollama status retrieved successfully", data=result
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get Ollama status: {str(e)}"
        )


@router.get("/models", response_model=ModelsResponse)
async def list_ollama_models(agent=Depends(get_agent)):
    """List all available models and their status"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.list_available_models()

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=result.get("error", "Failed to list models")
            )

        return ModelsResponse(
            success=True,
            message="Models list retrieved successfully",
            models=result["models"],
            approved_models=result["approved_models"],
            current_model=result["current_model"],
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list models: {str(e)}")


@router.post("/switch", response_model=StatusResponse)
async def switch_ollama_model(request: ModelSwitchRequest, agent=Depends(get_agent)):
    """Switch to a different Ollama model"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.switch_model(request.model_name)

        if not result["success"]:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to switch model")
            )

        return StatusResponse(
            success=True,
            message=f"Successfully switched to {request.model_name}",
            data=result,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to switch model: {str(e)}")


@router.post("/pull", response_model=StatusResponse)
async def pull_ollama_model(request: ModelPullRequest, agent=Depends(get_agent)):
    """Pull a new Ollama model"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.pull_model(request.model_name)

        if not result["success"]:
            raise HTTPException(
                status_code=400, detail=result.get("error", "Failed to pull model")
            )

        return StatusResponse(
            success=True,
            message=f"Successfully pulled {request.model_name}",
            data=result,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to pull model: {str(e)}")


@router.post("/generate", response_model=GenerateResponse)
async def generate_ollama_response(request: GenerateRequest, agent=Depends(get_agent)):
    """Generate response using Ollama model"""
    try:
        ollama_manager = get_ollama_manager()

        # Prepare kwargs for generation
        kwargs = {}
        if request.max_tokens is not None:
            kwargs["max_tokens"] = request.max_tokens
        if request.temperature is not None:
            kwargs["temperature"] = request.temperature
        if request.top_p is not None:
            kwargs["top_p"] = request.top_p

        result = await ollama_manager.generate_response(
            request.prompt, request.model_name, **kwargs
        )

        if not result["success"]:
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Failed to generate response"),
            )

        return GenerateResponse(
            success=True,
            message="Response generated successfully",
            response=result["response"],
            model=result["model"],
            response_time=result["response_time"],
            tokens_used=result["tokens_used"],
            prompt_tokens=result["prompt_tokens"],
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate response: {str(e)}"
        )


@router.get("/performance", response_model=PerformanceResponse)
async def get_ollama_performance(
    model_name: Optional[str] = None, agent=Depends(get_agent)
):
    """Get performance statistics for models"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.get_performance_stats(model_name)

        if not result["success"]:
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Failed to get performance stats"),
            )

        return PerformanceResponse(
            success=True,
            message="Performance statistics retrieved successfully",
            performance=result["performance"],
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get performance stats: {str(e)}"
        )


@router.post("/optimize", response_model=StatusResponse)
async def optimize_ollama_model_selection(agent=Depends(get_agent)):
    """Optimize model selection based on performance"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.optimize_model_selection()

        if not result["success"]:
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Failed to optimize model selection"),
            )

        return StatusResponse(
            success=True,
            message="Model optimization completed successfully",
            data=result,
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to optimize model selection: {str(e)}"
        )


@router.post("/test", response_model=TestResponse)
async def test_ollama_model(request: TestModelRequest, agent=Depends(get_agent)):
    """Test a specific Ollama model"""
    try:
        ollama_manager = get_ollama_manager()

        # Switch to the model first
        switch_result = await ollama_manager.switch_model(request.model_name)
        if not switch_result["success"]:
            raise HTTPException(
                status_code=400,
                detail=switch_result.get("error", "Failed to switch model"),
            )

        # Generate test response
        result = await ollama_manager.generate_response(
            request.test_prompt, request.model_name
        )

        if not result["success"]:
            raise HTTPException(
                status_code=400,
                detail=result.get("error", "Failed to generate test response"),
            )

        return TestResponse(
            success=True,
            message=f"Model {request.model_name} test completed successfully",
            model=request.model_name,
            test_prompt=request.test_prompt,
            response=result["response"],
            response_time=result["response_time"],
            tokens_used=result["tokens_used"],
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to test model: {str(e)}")


@router.post("/compare", response_model=CompareResponse)
async def compare_ollama_models(
    request: CompareModelsRequest, agent=Depends(get_agent)
):
    """Compare multiple Ollama models with the same prompt"""
    try:
        ollama_manager = get_ollama_manager()

        if request.models is None:
            request.models = [
                "mistral:7b-instruct-q4_0",
                "qwen2.5-coder:3b",
                "starcoder2:3b",
            ]

        results = {}
        for model_name in request.models:
            if model_name in ollama_manager.approved_models:
                result = await ollama_manager.generate_response(
                    request.prompt, model_name
                )
                results[model_name] = {
                    "success": result["success"],
                    "response": result.get("response", ""),
                    "response_time": result.get("response_time", 0),
                    "tokens_used": result.get("tokens_used", 0),
                }
            else:
                results[model_name] = {
                    "success": False,
                    "error": f"Model {model_name} is not approved",
                }

        return CompareResponse(
            success=True,
            message=f"Model comparison completed for {len(request.models)} models",
            prompt=request.prompt,
            models=results,
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to compare models: {str(e)}"
        )


@router.get("/approved", response_model=StatusResponse)
async def get_approved_ollama_models(agent=Depends(get_agent)):
    """Get list of approved models for this project"""
    try:
        ollama_manager = get_ollama_manager()

        return StatusResponse(
            success=True,
            message="Approved models list retrieved successfully",
            data={
                "approved_models": ollama_manager.approved_models,
                "current_model": ollama_manager.current_model,
                "total_approved": len(ollama_manager.approved_models),
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get approved models: {str(e)}"
        )


@router.post("/validate", response_model=ValidationResponse)
async def validate_ollama_model(
    request: ValidateModelRequest, agent=Depends(get_agent)
):
    """Validate if a model is approved and available"""
    try:
        ollama_manager = get_ollama_manager()

        is_approved = request.model_name in ollama_manager.approved_models
        available_models = await ollama_manager.list_available_models()
        is_available = available_models["success"] and available_models["models"].get(
            request.model_name, {}
        ).get("available", False)

        recommendation = (
            "pull_model" if is_approved and not is_available else "ready_to_use"
        )

        return ValidationResponse(
            success=True,
            message=f"Model {request.model_name} validation completed",
            model_name=request.model_name,
            is_approved=is_approved,
            is_available=is_available,
            can_use=is_approved and is_available,
            recommendation=recommendation,
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate model: {str(e)}"
        )


@router.get("/connection", response_model=StatusResponse)
async def check_ollama_connection(agent=Depends(get_agent)):
    """Check Ollama connection status"""
    try:
        ollama_manager = get_ollama_manager()
        result = await ollama_manager.check_ollama_connection()

        return StatusResponse(
            success=True, message="Ollama connection check completed", data=result
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to check Ollama connection: {str(e)}"
        )


@router.post("/setup-learning", response_model=StatusResponse)
async def setup_ollama_learning_integration(agent=Depends(get_agent)):
    """Setup learning integration with the advanced learning system"""
    try:
        ollama_manager = get_ollama_manager()

        # Add learning callback to capture patterns
        if agent and hasattr(agent, "record_learning_event"):
            ollama_manager.add_learning_callback(agent.record_learning_event)

        return StatusResponse(
            success=True,
            message="Learning integration setup completed",
            data={
                "learning_callbacks": len(ollama_manager.learning_callbacks),
                "integration_enabled": True,
            },
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to setup learning integration: {str(e)}"
        )


# Health check endpoint
@router.get("/health")
async def ollama_health_check():
    """Health check for Ollama API"""
    try:
        ollama_manager = get_ollama_manager()
        connection = await ollama_manager.check_ollama_connection()

        return {
            "status": "healthy" if connection["success"] else "unhealthy",
            "ollama_connection": connection["success"],
            "timestamp": asyncio.get_event_loop().time(),
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": asyncio.get_event_loop().time(),
        }
