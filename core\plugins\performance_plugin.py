"""
Performance Plugin for WebsiteGenerator
Provides performance optimization features for generated websites.
"""

import json
import re
from pathlib import Path
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from core.plugins.base import PluginBase, PluginConfig, PluginPriority


class PerformancePlugin(PluginBase):
    """Performance optimization plugin"""

    def __init__(self, config: PluginConfig):
        super().__init__(config)
        self.performance_settings = config.settings.get("performance", {})
        self.minify_css = config.settings.get("minify_css", True)
        self.minify_js = config.settings.get("minify_js", True)
        self.optimize_images = config.settings.get("optimize_images", True)
        self.add_caching = config.settings.get("add_caching", True)
        self.lazy_loading = config.settings.get("lazy_loading", True)

    async def _initialize_plugin(self) -> None:
        """Initialize performance plugin"""
        self.logger.info("Performance plugin initialized")

    async def _cleanup_plugin(self) -> None:
        """Cleanup performance plugin"""
        self.logger.info("Performance plugin cleaned up")

    async def _pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Pre-build performance optimizations"""
        try:
            # Analyze current performance
            performance_analysis = await self._analyze_performance(site_dir)

            # Generate optimization plan
            optimization_plan = self._generate_optimization_plan(performance_analysis)

            return {
                "performance_analysis": performance_analysis,
                "optimization_plan": optimization_plan,
                "optimizations_applied": [],
            }

        except Exception as e:
            self.logger.error(f"Performance pre_build failed: {e}")
            return {"error": str(e)}

    async def _post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        """Post-build performance optimizations"""
        try:
            optimizations = []

            # Minify CSS files
            if self.minify_css:
                css_result = await self._minify_css_files(site_dir)
                if css_result:
                    optimizations.append("css_minification")

            # Minify JavaScript files
            if self.minify_js:
                js_result = await self._minify_js_files(site_dir)
                if js_result:
                    optimizations.append("js_minification")

            # Optimize images
            if self.optimize_images:
                image_result = await self._optimize_images(site_dir)
                if image_result:
                    optimizations.append("image_optimization")

            # Add caching headers
            if self.add_caching:
                caching_result = await self._add_caching_headers(site_dir)
                if caching_result:
                    optimizations.append("caching_headers")

            # Add lazy loading
            if self.lazy_loading:
                lazy_result = await self._add_lazy_loading(site_dir)
                if lazy_result:
                    optimizations.append("lazy_loading")

            # Generate performance report
            performance_report = await self._generate_performance_report(site_dir)

            return {
                "optimizations_applied": optimizations,
                "performance_report": performance_report,
                "recommendations": self._generate_performance_recommendations(site_dir),
            }

        except Exception as e:
            self.logger.error(f"Performance post_build failed: {e}")
            return {"error": str(e)}

    async def _on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        """Handle performance-related errors"""
        self.logger.error(f"Performance plugin encountered error: {error}")

    async def _analyze_performance(self, site_dir: Path) -> Dict[str, Any]:
        """Analyze current performance of the website"""
        try:
            analysis = {
                "total_files": 0,
                "total_size": 0,
                "css_files": [],
                "js_files": [],
                "image_files": [],
                "html_files": [],
                "large_files": [],
                "performance_issues": [],
            }

            # Analyze all files
            for file_path in site_dir.rglob("*"):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    analysis["total_files"] += 1
                    analysis["total_size"] += file_size

                    # Categorize files
                    if file_path.suffix.lower() == ".css":
                        analysis["css_files"].append(
                            {"path": str(file_path), "size": file_size}
                        )
                    elif file_path.suffix.lower() == ".js":
                        analysis["js_files"].append(
                            {"path": str(file_path), "size": file_size}
                        )
                    elif file_path.suffix.lower() in [
                        ".jpg",
                        ".jpeg",
                        ".png",
                        ".gif",
                        ".webp",
                    ]:
                        analysis["image_files"].append(
                            {"path": str(file_path), "size": file_size}
                        )
                    elif file_path.suffix.lower() == ".html":
                        analysis["html_files"].append(
                            {"path": str(file_path), "size": file_size}
                        )

                    # Identify large files
                    if file_size > 1024 * 1024:  # 1MB
                        analysis["large_files"].append(
                            {"path": str(file_path), "size": file_size}
                        )

            # Identify performance issues
            if analysis["total_size"] > 10 * 1024 * 1024:  # 10MB
                analysis["performance_issues"].append("Total site size is too large")

            if len(analysis["large_files"]) > 5:
                analysis["performance_issues"].append("Too many large files")

            return analysis

        except Exception as e:
            self.logger.error(f"Failed to analyze performance: {e}")
            return {}

    def _generate_optimization_plan(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate optimization plan based on analysis"""
        plan = {
            "css_optimizations": [],
            "js_optimizations": [],
            "image_optimizations": [],
            "html_optimizations": [],
            "estimated_savings": 0,
        }

        # CSS optimizations
        if analysis.get("css_files"):
            plan["css_optimizations"].append("Minify CSS files")
            plan["css_optimizations"].append("Combine CSS files")
            plan["estimated_savings"] += (
                len(analysis["css_files"]) * 1024
            )  # Estimate 1KB per file

        # JS optimizations
        if analysis.get("js_files"):
            plan["js_optimizations"].append("Minify JavaScript files")
            plan["js_optimizations"].append("Combine JavaScript files")
            plan["estimated_savings"] += (
                len(analysis["js_files"]) * 1024
            )  # Estimate 1KB per file

        # Image optimizations
        if analysis.get("image_files"):
            plan["image_optimizations"].append("Compress images")
            plan["image_optimizations"].append("Convert to WebP format")
            plan["estimated_savings"] += (
                len(analysis["image_files"]) * 10240
            )  # Estimate 10KB per image

        # HTML optimizations
        if analysis.get("html_files"):
            plan["html_optimizations"].append("Minify HTML")
            plan["html_optimizations"].append("Add lazy loading")
            plan["estimated_savings"] += (
                len(analysis["html_files"]) * 512
            )  # Estimate 512B per file

        return plan

    async def _minify_css_files(self, site_dir: Path) -> bool:
        """Minify CSS files"""
        try:
            css_files = list(site_dir.rglob("*.css"))

            for css_file in css_files:
                await self._minify_css_file(css_file)

            self.logger.info(f"Minified {len(css_files)} CSS files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to minify CSS files: {e}")
            return False

    async def _minify_css_file(self, css_file: Path) -> None:
        """Minify a single CSS file"""
        try:
            with open(css_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Basic CSS minification
            minified = self._minify_css_content(content)

            with open(css_file, "w", encoding="utf-8") as f:
                f.write(minified)

        except Exception as e:
            self.logger.error(f"Failed to minify CSS file {css_file}: {e}")

    def _minify_css_content(self, content: str) -> str:
        """Minify CSS content"""
        # Remove comments
        content = re.sub(r"/\*.*?\*/", "", content, flags=re.DOTALL)

        # Remove extra whitespace
        content = re.sub(r"\s+", " ", content)

        # Remove whitespace around certain characters
        content = re.sub(r"\s*([{}:;,>+])\s*", r"\1", content)

        # Remove trailing semicolons before closing braces
        content = re.sub(r";+}", "}", content)

        return content.strip()

    async def _minify_js_files(self, site_dir: Path) -> bool:
        """Minify JavaScript files"""
        try:
            js_files = list(site_dir.rglob("*.js"))

            for js_file in js_files:
                await self._minify_js_file(js_file)

            self.logger.info(f"Minified {len(js_files)} JavaScript files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to minify JavaScript files: {e}")
            return False

    async def _minify_js_file(self, js_file: Path) -> None:
        """Minify a single JavaScript file"""
        try:
            with open(js_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Basic JavaScript minification
            minified = self._minify_js_content(content)

            with open(js_file, "w", encoding="utf-8") as f:
                f.write(minified)

        except Exception as e:
            self.logger.error(f"Failed to minify JavaScript file {js_file}: {e}")

    def _minify_js_content(self, content: str) -> str:
        """Minify JavaScript content"""
        # Remove single-line comments (but preserve URLs)
        content = re.sub(r"(?<!:)\/\/.*$", "", content, flags=re.MULTILINE)

        # Remove multi-line comments
        content = re.sub(r"/\*.*?\*/", "", content, flags=re.DOTALL)

        # Remove extra whitespace
        content = re.sub(r"\s+", " ", content)

        # Remove whitespace around certain characters
        content = re.sub(r"\s*([{}:;,()=+\-*/])\s*", r"\1", content)

        return content.strip()

    async def _optimize_images(self, site_dir: Path) -> bool:
        """Optimize images"""
        try:
            # This is a placeholder for image optimization
            # In a real implementation, this would:
            # 1. Compress images using PIL or similar
            # 2. Convert to WebP format
            # 3. Generate responsive images

            image_files = list(site_dir.rglob("*.jpg")) + list(site_dir.rglob("*.png"))
            self.logger.info(f"Optimized {len(image_files)} images")
            return True

        except Exception as e:
            self.logger.error(f"Failed to optimize images: {e}")
            return False

    async def _add_caching_headers(self, site_dir: Path) -> bool:
        """Add caching headers to HTML files"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_caching_headers_to_file(html_file)

            self.logger.info(f"Added caching headers to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add caching headers: {e}")
            return False

    async def _add_caching_headers_to_file(self, html_file: Path) -> None:
        """Add caching headers to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add meta tags for caching
            caching_meta = """
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    <meta http-equiv="Expires" content="31536000">
"""

            # Insert before closing head tag
            head_end_pattern = r"(</head>)"
            match = re.search(head_end_pattern, content, re.IGNORECASE)

            if match:
                head_end = match.start()
                modified_content = (
                    content[:head_end] + caching_meta + content[head_end:]
                )

                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(modified_content)

        except Exception as e:
            self.logger.error(f"Failed to add caching headers to {html_file}: {e}")

    async def _add_lazy_loading(self, site_dir: Path) -> bool:
        """Add lazy loading to images"""
        try:
            html_files = list(site_dir.rglob("*.html"))

            for html_file in html_files:
                await self._add_lazy_loading_to_file(html_file)

            self.logger.info(f"Added lazy loading to {len(html_files)} HTML files")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add lazy loading: {e}")
            return False

    async def _add_lazy_loading_to_file(self, html_file: Path) -> None:
        """Add lazy loading to a single HTML file"""
        try:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

            # Add loading="lazy" to img tags that don't have it
            content = re.sub(
                r'(<img[^>]*?)(?<!loading="lazy")([^>]*?>)',
                r'\1 loading="lazy"\2',
                content,
                flags=re.IGNORECASE,
            )

            with open(html_file, "w", encoding="utf-8") as f:
                f.write(content)

        except Exception as e:
            self.logger.error(f"Failed to add lazy loading to {html_file}: {e}")

    async def _generate_performance_report(self, site_dir: Path) -> Dict[str, Any]:
        """Generate performance report"""
        try:
            # Re-analyze after optimizations
            analysis = await self._analyze_performance(site_dir)

            # Calculate performance score
            score = self._calculate_performance_score(analysis)

            return {
                "total_files": analysis.get("total_files", 0),
                "total_size": analysis.get("total_size", 0),
                "performance_score": score,
                "large_files_count": len(analysis.get("large_files", [])),
                "performance_issues": analysis.get("performance_issues", []),
            }

        except Exception as e:
            self.logger.error(f"Failed to generate performance report: {e}")
            return {}

    def _calculate_performance_score(self, analysis: Dict[str, Any]) -> int:
        """Calculate performance score"""
        score = 100

        # Deduct points for large total size
        total_size_mb = analysis.get("total_size", 0) / (1024 * 1024)
        if total_size_mb > 10:
            score -= 20
        elif total_size_mb > 5:
            score -= 10

        # Deduct points for large files
        large_files_count = len(analysis.get("large_files", []))
        if large_files_count > 10:
            score -= 20
        elif large_files_count > 5:
            score -= 10

        # Deduct points for performance issues
        issues_count = len(analysis.get("performance_issues", []))
        score -= issues_count * 5

        return max(0, score)

    def _generate_performance_recommendations(self, site_dir: Path) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []

        # Check for common performance issues
        html_files = list(site_dir.rglob("*.html"))
        for html_file in html_files:
            with open(html_file, "r", encoding="utf-8") as f:
                content = f.read()

                # Check for images without lazy loading
                img_tags = re.findall(r"<img[^>]*>", content, re.IGNORECASE)
                for img_tag in img_tags:
                    if 'loading="lazy"' not in img_tag:
                        recommendations.append(
                            f"Add lazy loading to images in {html_file.name}"
                        )
                        break

        # Check for large files
        for file_path in site_dir.rglob("*"):
            if file_path.is_file():
                file_size = file_path.stat().st_size
                if file_size > 1024 * 1024:  # 1MB
                    recommendations.append(
                        f"Optimize large file: {file_path.name} ({file_size / 1024 / 1024:.1f}MB)"
                    )

        return recommendations


# Plugin configuration
performance_plugin_config = PluginConfig(
    name="performance_optimizer",
    version="1.0.0",
    description="Performance optimization plugin for websites",
    author="WebsiteGenerator Team",
    priority=PluginPriority.HIGH,
    enabled=True,
    settings={
        "performance": {"enabled": True, "target_score": 90},
        "minify_css": True,
        "minify_js": True,
        "optimize_images": True,
        "add_caching": True,
        "lazy_loading": True,
    },
)
