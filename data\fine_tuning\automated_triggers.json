{"triggers": {"performance_test_model_20250804_134228": {"id": "performance_test_model_20250804_134228", "type": "performance", "model_name": "test_model", "conditions": {"performance_threshold": 0.7, "check_interval_hours": 24}, "status": "active", "created_time": "2025-08-04T13:42:28.898837", "last_checked": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "scheduled_test_model_20250804_134228": {"id": "scheduled_test_model_20250804_134228", "type": "scheduled", "model_name": "test_model", "conditions": {"schedule_type": "weekly", "schedule_value": "monday"}, "status": "active", "created_time": "2025-08-04T13:42:28.900863", "last_triggered": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "performance_test_model_20250804_134919": {"id": "performance_test_model_20250804_134919", "type": "performance", "model_name": "test_model", "conditions": {"performance_threshold": 0.7, "check_interval_hours": 24}, "status": "active", "created_time": "2025-08-04T13:49:19.234152", "last_checked": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "scheduled_test_model_20250804_134919": {"id": "scheduled_test_model_20250804_134919", "type": "scheduled", "model_name": "test_model", "conditions": {"schedule_type": "weekly", "schedule_value": "monday"}, "status": "active", "created_time": "2025-08-04T13:49:19.235978", "last_triggered": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "performance_test_model_20250804_135745": {"id": "performance_test_model_20250804_135745", "type": "performance", "model_name": "test_model", "conditions": {"performance_threshold": 0.7, "check_interval_hours": 24}, "status": "active", "created_time": "2025-08-04T13:57:45.083303", "last_checked": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "scheduled_test_model_20250804_135745": {"id": "scheduled_test_model_20250804_135745", "type": "scheduled", "model_name": "test_model", "conditions": {"schedule_type": "weekly", "schedule_value": "monday"}, "status": "active", "created_time": "2025-08-04T13:57:45.084617", "last_triggered": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "performance_test_model_20250804_135856": {"id": "performance_test_model_20250804_135856", "type": "performance", "model_name": "test_model", "conditions": {"performance_threshold": 0.7, "check_interval_hours": 24}, "status": "active", "created_time": "2025-08-04T13:58:56.874240", "last_checked": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "scheduled_test_model_20250804_135856": {"id": "scheduled_test_model_20250804_135856", "type": "scheduled", "model_name": "test_model", "conditions": {"schedule_type": "weekly", "schedule_value": "monday"}, "status": "active", "created_time": "2025-08-04T13:58:56.876313", "last_triggered": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "performance_test_model_20250804_211622": {"id": "performance_test_model_20250804_211622", "type": "performance", "model_name": "test_model", "conditions": {"performance_threshold": 0.7, "check_interval_hours": 24}, "status": "active", "created_time": "2025-08-04T21:16:22.633575", "last_checked": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}, "scheduled_test_model_20250804_211622": {"id": "scheduled_test_model_20250804_211622", "type": "scheduled", "model_name": "test_model", "conditions": {"schedule_type": "weekly", "schedule_value": "monday"}, "status": "active", "created_time": "2025-08-04T21:16:22.635229", "last_triggered": null, "trigger_count": 0, "training_config": {"num_epochs": 3, "batch_size": 4, "learning_rate": 0.0002}}}, "trigger_history": [], "last_updated": "2025-08-04T21:16:22.635651"}