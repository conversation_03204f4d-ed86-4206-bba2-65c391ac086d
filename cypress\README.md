# Cypress E2E Testing for IDE Feedback

This directory contains automated end-to-end tests for the IDE interface, specifically focused on testing error and success feedback scenarios.

## Setup

1. **Install dependencies** (already done):
   ```bash
   npm install
   ```

2. **Start the development server**:
   ```bash
   npm run dev
   ```

3. **Run tests**:
   ```bash
   # Open Cypress Test Runner (interactive)
   npm run cypress:open

   # Run tests in headless mode
   npm run cypress:run

   # Run only feedback tests
   npm run test:feedback
   ```

## Test Structure

### `cypress/support/commands.js`
Custom Cypress commands for common operations:
- `waitForToast()` - Wait for toast notifications
- `expectErrorToast()` - Check for error toasts
- `expectSuccessToast()` - Check for success toasts
- `sendChatMessage()` - Send chat messages
- `enhancePrompt()` - Test prompt enhancement
- `runErrorScan()` - Run error detection scans
- `uploadFile()` - Test file uploads
- `simulateNetworkError()` - Simulate network failures
- `simulateServerError()` - Simulate server errors

### `cypress/e2e/feedback_spec.cy.js`
Comprehensive test suite covering:
- **Site Validation Feedback** - Error/success toasts for site validation
- **Error Detection Feedback** - Scan and auto-fix feedback
- **Chat & AI Feedback** - AI response and prompt enhancement feedback
- **File Operations Feedback** - Upload and save feedback
- **Model Health Feedback** - Model testing and cache operations
- **Performance & GPU Feedback** - Performance monitoring feedback
- **Documentation Feedback** - Documentation generation feedback
- **Accessibility & UX** - Keyboard navigation and ARIA labels
- **Error Recovery** - Network error handling and resilience
- **Toast Management** - Auto-dismissal and manual dismissal

## Test Scenarios Covered

### Error Scenarios
- Network failures
- Server errors (500, 404, 413)
- Timeout scenarios
- File upload failures
- AI model unavailability
- Component load failures

### Success Scenarios
- Successful operations
- Proper loading states
- Correct toast messages
- Accessibility compliance

### User Experience
- Keyboard navigation
- ARIA labels
- Loading indicators
- Toast stacking
- Auto-dismissal

## Configuration

### `cypress.config.js`
- Base URL: `http://localhost:3000`
- Viewport: 1280x720
- Timeouts: 10 seconds
- Screenshots on failure: enabled
- Videos: disabled

### Environment Variables
You can set these in `cypress.env.json`:
```json
{
  "baseUrl": "http://localhost:3000",
  "apiUrl": "http://localhost:8000"
}
```

## Running Specific Tests

```bash
# Run only site validation tests
npx cypress run --spec "cypress/e2e/feedback_spec.cy.js" --grep "Site Validation"

# Run only error detection tests
npx cypress run --spec "cypress/e2e/feedback_spec.cy.js" --grep "Error Detection"

# Run only chat tests
npx cypress run --spec "cypress/e2e/feedback_spec.cy.js" --grep "Chat"
```

## Debugging

### View Test Results
- Screenshots are saved to `cypress/screenshots/` on failure
- Videos are saved to `cypress/videos/` (if enabled)

### Debug Mode
```bash
# Run with debug logging
DEBUG=cypress:* npm run test:feedback
```

### Interactive Debugging
```bash
# Open Cypress and run specific test
npm run cypress:open
# Then click on the test you want to debug
```

## Adding New Tests

1. **Add custom commands** in `cypress/support/commands.js`
2. **Create test fixtures** in `cypress/fixtures/`
3. **Write test cases** in `cypress/e2e/feedback_spec.cy.js`
4. **Update selectors** to match your actual UI elements

## Best Practices

1. **Use data-testid attributes** for reliable element selection
2. **Mock API responses** for consistent testing
3. **Test both success and failure scenarios**
4. **Include accessibility testing**
5. **Test error recovery and resilience**
6. **Use descriptive test names**

## Troubleshooting

### Common Issues

1. **Tests fail because app isn't running**
   - Ensure `npm run dev` is running on port 3000

2. **Element not found**
   - Check that `data-testid` attributes are present
   - Verify element selectors match actual UI

3. **Network timeouts**
   - Increase timeout values in `cypress.config.js`
   - Check if backend API is running

4. **Toast not found**
   - Verify toast CSS classes match actual implementation
   - Check toast timing and auto-dismissal

### Debug Commands

```bash
# Check if Cypress is properly installed
npx cypress verify

# Check Cypress version
npx cypress version

# Clear Cypress cache
npx cypress cache clear
```
