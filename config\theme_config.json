{"theme_structure": {"naming_conventions": {"theme_names": "kebab-case", "css_variables": "kebab-case", "asset_files": "snake_case"}, "required_files": ["index.html", "style.css", "script.js"], "optional_files": ["README.md", "preview.png", "theme.json"], "directory_structure": {"base": "Base template files", "assets": "Theme-specific assets", "variants": "Theme variants", "docs": "Theme documentation"}}, "asset_optimization": {"image_formats": {"supported": ["jpg", "jpeg", "png", "gif", "svg", "webp"], "preferred": ["webp", "svg"], "compression": {"jpg": {"quality": 85, "progressive": true}, "png": {"compression": 9, "optimize": true}, "webp": {"quality": 80, "lossless": false}}}, "css_optimization": {"minify": true, "autoprefixer": true, "purge_unused": true, "source_maps": false}, "js_optimization": {"minify": true, "source_maps": false, "bundle": false}, "font_optimization": {"formats": ["woff2", "woff"], "subset": true, "display": "swap"}}, "content_metadata": {"schema": {"required_fields": ["title", "description", "author"], "optional_fields": ["keywords", "tags", "category", "publish_date"], "custom_fields": []}, "validation_rules": {"title": {"max_length": 100, "min_length": 1}, "description": {"max_length": 500, "min_length": 10}, "keywords": {"max_count": 10, "max_length_per_keyword": 50}}}, "template_inheritance": {"base_template": "modern", "inheritance_chain": ["base", "variants", "custom"], "override_rules": {"css": "merge_and_override", "html": "template_override", "js": "append_and_override"}}, "performance_settings": {"cache_strategy": {"assets": "1_week", "templates": "1_day", "metadata": "1_hour"}, "compression": {"gzip": true, "brotli": true}, "cdn_integration": {"enabled": false, "provider": "cloudflare"}}, "security_settings": {"content_security_policy": {"enabled": true, "default_src": ["'self'"], "script_src": ["'self'", "'unsafe-inline'"], "style_src": ["'self'", "'unsafe-inline'"], "img_src": ["'self'", "data:", "https:"]}, "file_validation": {"allowed_extensions": [".html", ".css", ".js", ".json", ".md"], "max_file_size": "10MB", "scan_for_malware": false}}, "development_settings": {"hot_reload": {"enabled": true, "watch_patterns": ["**/*.html", "**/*.css", "**/*.js"], "ignore_patterns": ["**/node_modules/**", "**/.git/**"]}, "debug_mode": {"enabled": false, "log_level": "INFO", "show_source_maps": true}}}