# api/site_container_routes.py
"""
Site Container Management API Routes
Provides REST API endpoints for managing site containers.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from api.agent_dependency import get_agent
from core.site_container_manager import SiteContainerManager

router = APIRouter(prefix="/api/site-containers", tags=["Site Containers"])


class SiteContainerCreate(BaseModel):
    site_name: str
    port: Optional[int] = None
    environment: str = "production"


class SiteContainerResponse(BaseModel):
    success: bool
    message: str
    container: Optional[Dict[str, Any]] = None
    port: Optional[int] = None
    url: Optional[str] = None
    error: Optional[str] = None


class ContainerListResponse(BaseModel):
    success: bool
    containers: List[Dict[str, Any]]
    total: int
    error: Optional[str] = None


@router.post("/create", response_model=SiteContainerResponse)
async def create_site_container(request: SiteContainerCreate, agent=Depends(get_agent)):
    """Create a Docker container for a website"""
    try:
        container_manager = SiteContainerManager()
        site_config = {
            "name": request.site_name,
            "port": request.port,
            "environment": request.environment,
        }

        result = await container_manager.create_site_container(
            request.site_name, site_config
        )

        if result["success"]:
            return SiteContainerResponse(
                success=True,
                message=f"Container created for site {request.site_name}",
                container=result["container"],
                port=result["port"],
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{site_name}/start", response_model=SiteContainerResponse)
async def start_site_container(site_name: str, agent=Depends(get_agent)):
    """Start a site container"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.start_site_container(site_name)

        if result["success"]:
            return SiteContainerResponse(
                success=True,
                message=f"Container started for site {site_name}",
                url=result["url"],
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{site_name}/stop", response_model=SiteContainerResponse)
async def stop_site_container(site_name: str, agent=Depends(get_agent)):
    """Stop a site container"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.stop_site_container(site_name)

        if result["success"]:
            return SiteContainerResponse(
                success=True, message=f"Container stopped for site {site_name}"
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{site_name}", response_model=SiteContainerResponse)
async def delete_site_container(site_name: str, agent=Depends(get_agent)):
    """Delete a site container"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.delete_site_container(site_name)

        if result["success"]:
            return SiteContainerResponse(
                success=True, message=f"Container deleted for site {site_name}"
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=ContainerListResponse)
async def list_site_containers(agent=Depends(get_agent)):
    """List all site containers"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.list_containers()

        if result["success"]:
            containers = result["containers"]
            container_list = []

            for container in containers:
                container_list.append(
                    {
                        "site_name": container["site_name"],
                        "status": container["status"],
                        "port": container["port"],
                        "url": f"http://localhost:{container['port']}",
                        "health": container["health_status"],
                    }
                )

            return ContainerListResponse(
                success=True, containers=container_list, total=len(container_list)
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{site_name}/status", response_model=SiteContainerResponse)
async def get_container_status(site_name: str, agent=Depends(get_agent)):
    """Get status of a site container"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.get_container_status(site_name)

        if result["success"]:
            container = result["container"]
            return SiteContainerResponse(
                success=True,
                message=f"Container status for {site_name}",
                container={
                    "site_name": container["site_name"],
                    "status": container["status"],
                    "port": container["port"],
                    "url": f"http://localhost:{container['port']}",
                    "health": container["health_status"],
                    "resource_usage": container["resource_usage"],
                },
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{site_name}/rebuild", response_model=SiteContainerResponse)
async def rebuild_site_container(site_name: str, agent=Depends(get_agent)):
    """Rebuild a site container"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.rebuild_site_container(site_name)

        if result["success"]:
            return SiteContainerResponse(
                success=True,
                message=f"Container rebuilt for site {site_name}",
                url=result.get("url"),
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{site_name}/logs")
async def get_container_logs(
    site_name: str, lines: int = 100, agent=Depends(get_agent)
):
    """Get logs from a site container"""
    try:
        container_manager = SiteContainerManager()
        result = await container_manager.get_container_logs(site_name, lines)

        if result["success"]:
            return {
                "success": True,
                "logs": result["logs"],
                "container_name": result["container_name"],
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
