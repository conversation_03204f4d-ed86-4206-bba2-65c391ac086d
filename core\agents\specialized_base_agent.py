#!/usr/bin/env python3
"""
BaseAgent - Base class for all specialized agents

Provides common functionality for:
- Configuration loading
- Default configuration
- Task execution patterns
- Logging setup
- Error handling
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from models.ollama_manager import OllamaModelManager

logger = logging.getLogger(__name__)


class BaseAgent:
    """Base class for all specialized agents"""

    def __init__(self, config_path: str, default_config: Dict[str, Any]):
        """
        Initialize the base agent

        Args:
            config_path: Path to configuration file
            default_config: Default configuration dictionary
        """
        self.config = self._load_config(config_path, default_config)
        self.task_history: List[Dict[str, Any]] = []

        # Initialize Ollama manager
        self.ollama_manager = OllamaModelManager()

        # Load model settings
        model_settings = self.config.get("model_settings", {})
        self.model_name = model_settings.get(
            "model_name", self.ollama_manager.current_model
        )
        self.system_prompt = model_settings.get(
            "system_prompt", "You are a helpful AI assistant."
        )

        logger.info(
            f"{self.__class__.__name__} initialized successfully with model {self.model_name}"
        )

    def _load_config(
        self, config_path: str, default_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Load configuration from JSON file

        Args:
            config_path: Path to configuration file
            default_config: Default configuration to use if file not found

        Returns:
            Configuration dictionary
        """
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(
                f"Configuration file {config_path} not found, using defaults"
            )
            return default_config
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return default_config

    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration - to be overridden by subclasses

        Returns:
            Default configuration dictionary
        """
        return {}

    async def execute_task(self, task_description: str, task_id: str) -> Dict[str, Any]:
        """
        Common task execution pattern for all agents

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        start_time = datetime.now()

        try:
            logger.info(f"Executing {self.__class__.__name__} task: {task_description}")

            # Parse task requirements
            requirements = await self._parse_task_requirements(task_description)

            # Execute the specific task implementation
            result = await self._execute_specific_task(requirements, task_id)

            # Add metadata to result
            execution_time = (datetime.now() - start_time).total_seconds()
            result.update(
                {
                    "task_id": task_id,
                    "agent_type": self.__class__.__name__,
                    "execution_time": execution_time,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            # Store in task history
            self.task_history.append(
                {
                    "task_id": task_id,
                    "description": task_description,
                    "result": result,
                    "execution_time": execution_time,
                    "timestamp": datetime.now().isoformat(),
                }
            )

            logger.info(
                f"Task {task_id} completed successfully in {execution_time:.2f}s"
            )
            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_result = {
                "success": False,
                "task_id": task_id,
                "agent_type": self.__class__.__name__,
                "error": str(e),
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat(),
                "message": f"Task failed: {str(e)}",
            }

            logger.error(f"Task {task_id} failed after {execution_time:.2f}s: {e}")
            return error_result

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description - to be overridden by subclasses

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        # Default implementation - subclasses should override
        return {"description": task_description, "parsed": False}

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation - to be overridden by subclasses

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        raise NotImplementedError("Subclasses must implement _execute_specific_task")

    def get_task_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent task history

        Args:
            limit: Maximum number of tasks to return

        Returns:
            List of recent task results
        """
        return self.task_history[-limit:] if self.task_history else []

    def get_agent_stats(self) -> Dict[str, Any]:
        """
        Get agent statistics

        Returns:
            Dictionary with agent statistics
        """
        total_tasks = len(self.task_history)
        successful_tasks = len(
            [t for t in self.task_history if t.get("result", {}).get("success", False)]
        )
        failed_tasks = total_tasks - successful_tasks

        total_execution_time = sum(
            t.get("execution_time", 0) for t in self.task_history
        )
        avg_execution_time = (
            total_execution_time / total_tasks if total_tasks > 0 else 0
        )

        return {
            "agent_type": self.__class__.__name__,
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": (
                (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0
            ),
            "total_execution_time": total_execution_time,
            "average_execution_time": avg_execution_time,
            "last_task": self.task_history[-1] if self.task_history else None,
        }

    async def shutdown(self):
        """Shutdown the agent - to be overridden by subclasses if needed"""
        logger.info(f"Shutting down {self.__class__.__name__}")

        # Clear task history to free memory
        self.task_history.clear()
