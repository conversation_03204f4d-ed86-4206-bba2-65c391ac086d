"""
API Documentation Generator

This module provides comprehensive API documentation generation capabilities
including endpoint documentation, request/response schemas, authentication
documentation, and error handling documentation.
"""

import asyncio
import re
from typing import Any, Dict, List, Optional


class APIDocumentationGenerator:
    """
    Generator for comprehensive API documentation.

    Creates detailed API documentation including endpoint descriptions,
    request/response schemas, authentication information, and error handling.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize API documentation generator with configuration."""
        self.config = config
        self.supported_formats = config.get("output_formats", ["markdown", "html"])
        self.style_guides = config.get("style_guides", {})

    async def generate_api_documentation(
        self,
        source_code: str,
        context: Dict[str, Any],
        style_preferences: Dict[str, Any],
        output_format: str,
    ) -> str:
        """
        Generate comprehensive API documentation.

        Args:
            source_code: Source code to analyze
            context: Additional context for documentation
            style_preferences: Style preferences for documentation
            output_format: Output format (markdown, html, etc.)

        Returns:
            str: Generated API documentation
        """
        try:
            # Parse source code to extract API information
            api_info = await self._parse_api_code(source_code)

            # Generate documentation based on output format
            if output_format == "markdown":
                return await self._generate_markdown_documentation(
                    api_info, context, style_preferences
                )
            elif output_format == "html":
                return await self._generate_html_documentation(
                    api_info, context, style_preferences
                )
            else:
                return await self._generate_markdown_documentation(
                    api_info, context, style_preferences
                )

        except Exception as e:
            return f"Error generating API documentation: {str(e)}"

    async def _parse_api_code(self, source_code: str) -> Dict[str, Any]:
        """Parse source code to extract API information."""
        api_info: Dict[str, Any] = {
            "endpoints": [],
            "models": [],
            "functions": [],
            "classes": [],
            "imports": [],
            "dependencies": [],
        }

        lines = source_code.split("\n")

        for i, line in enumerate(lines):
            line = line.strip()

            # Extract function definitions
            if line.startswith("def ") or line.startswith("async def "):
                func_info = self._extract_function_info(line, lines, i)
                if func_info:
                    api_info["functions"].append(func_info)

            # Extract class definitions
            elif line.startswith("class "):
                class_info = self._extract_class_info(line, lines, i)
                if class_info:
                    api_info["classes"].append(class_info)

            # Extract imports
            elif line.startswith("import ") or line.startswith("from "):
                api_info["imports"].append(line)

        return api_info

    def _extract_function_info(
        self, line: str, lines: List[str], line_index: int
    ) -> Optional[Dict[str, Any]]:
        """Extract information about a function."""
        # Extract function name and parameters
        match = re.match(r"(?:async\s+)?def\s+(\w+)\s*\((.*?)\)", line)
        if not match:
            return None

        func_name = match.group(1)
        params_str = match.group(2)

        # Parse parameters
        params = []
        if params_str.strip():
            param_parts = params_str.split(",")
            for param in param_parts:
                param = param.strip()
                if "=" in param:
                    name, default = param.split("=", 1)
                    params.append(
                        {
                            "name": name.strip(),
                            "default": default.strip(),
                            "required": False,
                        }
                    )
                else:
                    params.append(
                        {"name": param.strip(), "default": None, "required": True}
                    )

        # Extract docstring
        docstring = ""
        for i in range(line_index + 1, min(line_index + 10, len(lines))):
            next_line = lines[i].strip()
            if next_line.startswith('"""') or next_line.startswith("'''"):
                docstring = next_line.strip("\"'")
                break
            elif next_line.startswith("#"):
                docstring += next_line[1:].strip() + " "

        return {
            "name": func_name,
            "parameters": params,
            "docstring": docstring,
            "async": "async" in line,
            "line_number": line_index + 1,
        }

    def _extract_class_info(
        self, line: str, lines: List[str], line_index: int
    ) -> Optional[Dict[str, Any]]:
        """Extract information about a class."""
        # Extract class name
        match = re.match(r"class\s+(\w+)", line)
        if not match:
            return None

        class_name = match.group(1)

        # Extract docstring
        docstring = ""
        for i in range(line_index + 1, min(line_index + 10, len(lines))):
            next_line = lines[i].strip()
            if next_line.startswith('"""') or next_line.startswith("'''"):
                docstring = next_line.strip("\"'")
                break
            elif next_line.startswith("#"):
                docstring += next_line[1:].strip() + " "

        return {
            "name": class_name,
            "docstring": docstring,
            "line_number": line_index + 1,
        }

    async def _generate_markdown_documentation(
        self,
        api_info: Dict[str, Any],
        context: Dict[str, Any],
        style_preferences: Dict[str, Any],
    ) -> str:
        """Generate API documentation in Markdown format."""
        doc = []

        # Title and overview
        project_name = context.get("project_name", "API Documentation")
        version = context.get("version", "1.0.0")

        doc.append(f"# {project_name} API Documentation")
        doc.append(f"**Version:** {version}")
        doc.append("")

        # Table of contents
        doc.append("## Table of Contents")
        doc.append("- [Overview](#overview)")
        doc.append("- [Authentication](#authentication)")
        doc.append("- [Endpoints](#endpoints)")
        doc.append("- [Models](#models)")
        doc.append("- [Error Handling](#error-handling)")
        doc.append("- [Examples](#examples)")
        doc.append("")

        # Overview
        doc.append("## Overview")
        doc.append(
            "This API provides comprehensive functionality for data processing and analysis."
        )
        doc.append("")

        # Authentication
        doc.append("## Authentication")
        doc.append(
            "The API uses token-based authentication. Include your API key in the request headers:"
        )
        doc.append("")
        doc.append("```http")
        doc.append("Authorization: Bearer YOUR_API_KEY")
        doc.append("```")
        doc.append("")

        # Endpoints
        if api_info["functions"]:
            doc.append("## Endpoints")
            doc.append("")

            for func in api_info["functions"]:
                doc.append(f"### `{func['name']}`")
                doc.append("")

                if func["docstring"]:
                    doc.append(func["docstring"])
                    doc.append("")

                # Parameters
                if func["parameters"]:
                    doc.append("**Parameters:**")
                    doc.append("")
                    for param in func["parameters"]:
                        required = "Required" if param["required"] else "Optional"
                        default = (
                            f" (default: {param['default']})"
                            if param["default"]
                            else ""
                        )
                        doc.append(f"- `{param['name']}` - {required}{default}")
                    doc.append("")

                # Example
                doc.append("**Example:**")
                doc.append("```python")
                param_str = ", ".join([p["name"] for p in func["parameters"]])
                doc.append(f"result = {func['name']}({param_str})")
                doc.append("```")
                doc.append("")

        # Models/Classes
        if api_info["classes"]:
            doc.append("## Models")
            doc.append("")

            for cls in api_info["classes"]:
                doc.append(f"### `{cls['name']}`")
                doc.append("")

                if cls["docstring"]:
                    doc.append(cls["docstring"])
                    doc.append("")

                doc.append("**Properties:**")
                doc.append("- Add properties here")
                doc.append("")

        # Error Handling
        doc.append("## Error Handling")
        doc.append("")
        doc.append("The API uses standard HTTP status codes:")
        doc.append("")
        doc.append("| Status Code | Description |")
        doc.append("|-------------|-------------|")
        doc.append("| 200 | Success |")
        doc.append("| 400 | Bad Request |")
        doc.append("| 401 | Unauthorized |")
        doc.append("| 403 | Forbidden |")
        doc.append("| 404 | Not Found |")
        doc.append("| 500 | Internal Server Error |")
        doc.append("")

        # Examples
        doc.append("## Examples")
        doc.append("")
        doc.append("### Basic Usage")
        doc.append("```python")
        doc.append("import requests")
        doc.append("")
        doc.append("url = 'https://api.example.com/v1/data'")
        doc.append("headers = {'Authorization': 'Bearer YOUR_API_KEY'}")
        doc.append("")
        doc.append("response = requests.get(url, headers=headers)")
        doc.append("data = response.json()")
        doc.append("```")
        doc.append("")

        doc.append("### Error Handling")
        doc.append("```python")
        doc.append("try:")
        doc.append("    response = requests.get(url, headers=headers)")
        doc.append("    response.raise_for_status()")
        doc.append("    data = response.json()")
        doc.append("except requests.exceptions.RequestException as e:")
        doc.append("    print(f'Error: {e}')")
        doc.append("```")
        doc.append("")

        return "\n".join(doc)

    async def _generate_html_documentation(
        self,
        api_info: Dict[str, Any],
        context: Dict[str, Any],
        style_preferences: Dict[str, Any],
    ) -> str:
        """Generate API documentation in HTML format."""
        # Convert markdown to HTML
        markdown_doc = await self._generate_markdown_documentation(
            api_info, context, style_preferences
        )

        # Simple markdown to HTML conversion
        html = markdown_doc.replace("# ", "<h1>").replace("\n# ", "</h1>\n<h1>")
        html = html.replace("## ", "<h2>").replace("\n## ", "</h2>\n<h2>")
        html = html.replace("### ", "<h3>").replace("\n### ", "</h3>\n<h3>")
        html = html.replace("**", "<strong>").replace("**", "</strong>")
        html = html.replace("`", "<code>").replace("`", "</code>")
        html = html.replace("\n\n", "</p>\n<p>")

        # Add HTML structure
        full_html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{context.get('project_name', 'API Documentation')}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        code {{ background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }}
        pre {{ background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
{html}
</body>
</html>
"""
        return full_html

    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the API documentation generator."""
        return {
            "status": "healthy",
            "supported_formats": self.supported_formats,
            "style_guides": list(self.style_guides.keys()),
            "version": "1.0.0",
        }
