"""
Web Search and Update Monitoring CLI Commands

Provides CLI interface for web search capabilities and update monitoring
for local Ollama LLM integration.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import click

from trend_monitoring.update_monitor import (
    BestPracticeChange,
    SecurityVulnerability,
    UpdateInfo,
    UpdateMonitor,
)
from trend_monitoring.web_search import SearchResult, WebSearchEngine

logger = logging.getLogger(__name__)


class WebSearchCommands:
    """CLI commands for web search and update monitoring"""

    def __init__(self, agent):
        self.agent = agent
        self.web_search_engine = None
        self.update_monitor = None

    async def initialize_components(self):
        """Initialize web search and update monitoring components"""
        if self.web_search_engine is None:
            config = {
                "trusted_domains": [
                    "developer.mozilla.org",
                    "docs.python.org",
                    "owasp.org",
                    "stackoverflow.com",
                    "github.com",
                    "dev.to",
                ],
                "blocked_domains": [],
                "user_agent": "AICodingAgent/1.0",
                "rate_limit_delay": 1.0,
                "max_retries": 3,
                "timeout": 30,
                "cache_ttl": 3600,
                "max_workers": 5,
                "rss_feeds": {
                    "mdn": {
                        "name": "MDN Web Docs",
                        "url": "https://developer.mozilla.org/en-US/sitemap.xml",
                        "category": "documentation",
                        "check_interval": 3600,
                        "priority": "high",
                    },
                    "python_docs": {
                        "name": "Python Documentation",
                        "url": "https://docs.python.org/3/feeds/news.rss",
                        "category": "documentation",
                        "check_interval": 7200,
                        "priority": "high",
                    },
                    "owasp": {
                        "name": "OWASP Security",
                        "url": "https://owasp.org/feeds/",
                        "category": "security",
                        "check_interval": 1800,
                        "priority": "critical",
                    },
                },
            }
            self.web_search_engine = WebSearchEngine(config)
            await self.web_search_engine.start()

        if self.update_monitor is None:
            config = {
                "check_intervals": {
                    "framework_updates": 3600,
                    "security_vulnerabilities": 1800,
                    "best_practices": 7200,
                    "dependency_updates": 3600,
                },
                "api_endpoints": {
                    "github": "https://api.github.com",
                    "pypi": "https://pypi.org/pypi",
                    "npm": "https://registry.npmjs.org",
                    "nvd": "https://services.nvd.nist.gov/rest/json/cves/2.0",
                },
                "max_workers": 5,
                "frameworks": {
                    "python": {
                        "django": {"current_version": "4.2.0"},
                        "flask": {"current_version": "2.3.0"},
                        "fastapi": {"current_version": "0.100.0"},
                    },
                    "javascript": {
                        "react": {"current_version": "18.2.0"},
                        "vue": {"current_version": "3.3.0"},
                        "angular": {"current_version": "16.0.0"},
                    },
                },
                "requirements_file": "requirements.txt",
            }
            self.update_monitor = UpdateMonitor(config)
            await self.update_monitor.start_monitoring()

    async def search_best_practices(
        self,
        query: str,
        language: Optional[str] = None,
        framework: Optional[str] = None,
        max_results: int = 20,
    ) -> Dict[str, Any]:
        """Search for coding best practices"""
        try:
            await self.initialize_components()

            start_time = datetime.now()
            results = await self.web_search_engine.search_best_practices(
                query=query, language=language, framework=framework
            )

            # Limit results
            limited_results = results[:max_results]

            # Convert to dictionaries
            result_dicts = [result.to_dict() for result in limited_results]

            search_time = (datetime.now() - start_time).total_seconds()

            return {
                "success": True,
                "results": result_dicts,
                "total_results": len(results),
                "query": query,
                "search_time": search_time,
                "language": language,
                "framework": framework,
            }

        except Exception as e:
            logger.error(f"Error in web search: {e}")
            return {"success": False, "error": str(e), "query": query}

    async def get_update_summary(self) -> Dict[str, Any]:
        """Get summary of all updates and monitoring status"""
        try:
            await self.initialize_components()

            summary = await self.update_monitor.get_update_summary()

            return {
                "success": True,
                "summary": summary,
                "last_check": summary.get("last_check"),
                "total_updates": summary.get("total_updates", 0),
                "total_vulnerabilities": summary.get("total_vulnerabilities", 0),
                "total_best_practice_changes": summary.get(
                    "total_best_practice_changes", 0
                ),
                "critical_updates": summary.get("critical_updates", 0),
                "high_severity_vulnerabilities": summary.get(
                    "high_severity_vulnerabilities", 0
                ),
                "check_errors": summary.get("check_errors", 0),
            }

        except Exception as e:
            logger.error(f"Error getting update summary: {e}")
            return {"success": False, "error": str(e)}

    async def get_update_recommendations(self) -> Dict[str, Any]:
        """Get recommended updates based on priority"""
        try:
            await self.initialize_components()

            recommendations = await self.update_monitor.get_recommended_updates()

            # Convert to dictionaries
            rec_dicts = [rec.to_dict() for rec in recommendations]

            return {
                "success": True,
                "recommendations": rec_dicts,
                "total_recommendations": len(recommendations),
                "critical_count": len(
                    [r for r in recommendations if r.severity == "critical"]
                ),
                "high_count": len([r for r in recommendations if r.severity == "high"]),
                "medium_count": len(
                    [r for r in recommendations if r.severity == "medium"]
                ),
                "low_count": len([r for r in recommendations if r.severity == "low"]),
            }

        except Exception as e:
            logger.error(f"Error getting update recommendations: {e}")
            return {"success": False, "error": str(e)}

    async def get_critical_vulnerabilities(self) -> Dict[str, Any]:
        """Get critical security vulnerabilities"""
        try:
            await self.initialize_components()

            vulnerabilities = await self.update_monitor.get_critical_vulnerabilities()

            # Convert to dictionaries
            vuln_dicts = [vuln.to_dict() for vuln in vulnerabilities]

            critical_count = len(
                [v for v in vulnerabilities if v.severity == "critical"]
            )
            high_count = len([v for v in vulnerabilities if v.severity == "high"])

            return {
                "success": True,
                "vulnerabilities": vuln_dicts,
                "total_vulnerabilities": len(vulnerabilities),
                "critical_count": critical_count,
                "high_count": high_count,
                "medium_count": len(
                    [v for v in vulnerabilities if v.severity == "medium"]
                ),
                "low_count": len([v for v in vulnerabilities if v.severity == "low"]),
            }

        except Exception as e:
            logger.error(f"Error getting vulnerabilities: {e}")
            return {"success": False, "error": str(e)}

    async def get_recent_best_practice_changes(self) -> Dict[str, Any]:
        """Get recent best practice changes"""
        try:
            await self.initialize_components()

            changes = await self.update_monitor.get_recent_best_practice_changes()

            # Convert to dictionaries
            change_dicts = [change.to_dict() for change in changes]

            return {
                "success": True,
                "changes": change_dicts,
                "total_changes": len(changes),
                "security_count": len([c for c in changes if c.category == "security"]),
                "performance_count": len(
                    [c for c in changes if c.category == "performance"]
                ),
                "maintainability_count": len(
                    [c for c in changes if c.category == "maintainability"]
                ),
                "accessibility_count": len(
                    [c for c in changes if c.category == "accessibility"]
                ),
            }

        except Exception as e:
            logger.error(f"Error getting best practice changes: {e}")
            return {"success": False, "error": str(e)}

    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring system status"""
        try:
            await self.initialize_components()

            # Get update summary for metrics
            summary = await self.update_monitor.get_update_summary()

            return {
                "success": True,
                "is_monitoring": self.update_monitor.is_monitoring,
                "last_check": summary.get("last_check"),
                "check_errors": summary.get("check_errors", 0),
                "metrics": summary,
                "web_search_engine": "available",
                "update_monitor": "available",
            }

        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {"success": False, "error": str(e)}

    async def start_monitoring(self) -> Dict[str, Any]:
        """Start the monitoring system"""
        try:
            await self.initialize_components()

            if not self.update_monitor.is_monitoring:
                await self.update_monitor.start_monitoring()

            return {
                "success": True,
                "message": "Monitoring started successfully",
                "is_monitoring": self.update_monitor.is_monitoring,
            }

        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
            return {"success": False, "error": str(e)}

    async def stop_monitoring(self) -> Dict[str, Any]:
        """Stop the monitoring system"""
        try:
            await self.initialize_components()

            if self.update_monitor.is_monitoring:
                await self.update_monitor.stop_monitoring()

            return {
                "success": True,
                "message": "Monitoring stopped successfully",
                "is_monitoring": self.update_monitor.is_monitoring,
            }

        except Exception as e:
            logger.error(f"Error stopping monitoring: {e}")
            return {"success": False, "error": str(e)}

    async def manual_check_updates(self) -> Dict[str, Any]:
        """Manually trigger update checks"""
        try:
            await self.initialize_components()

            # Run all update checks
            await self.update_monitor._check_framework_updates()
            await self.update_monitor._check_security_vulnerabilities()
            await self.update_monitor._check_best_practices()
            await self.update_monitor._check_dependency_updates()

            return {
                "success": True,
                "message": "Update checks completed successfully",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error in manual update check: {e}")
            return {"success": False, "error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """Health check for web search and update monitoring"""
        try:
            await self.initialize_components()

            return {
                "success": True,
                "status": "healthy",
                "web_search_engine": "available",
                "update_monitor": "available",
                "monitoring_active": self.update_monitor.is_monitoring,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"success": False, "error": str(e), "status": "unhealthy"}

    async def get_available_sources(self) -> Dict[str, Any]:
        """Get list of available search and monitoring sources"""
        try:
            await self.initialize_components()

            sources = {
                "web_search_sources": {
                    "duckduckgo": "DuckDuckGo Instant Answer API",
                    "rss_feeds": [
                        feed.name for feed in self.web_search_engine.rss_feeds
                    ],
                    "trusted_domains": list(self.web_search_engine.trusted_domains),
                },
                "update_monitoring_sources": {
                    "frameworks": {
                        "python": list(
                            self.update_monitor.config.get("frameworks", {})
                            .get("python", {})
                            .keys()
                        ),
                        "javascript": list(
                            self.update_monitor.config.get("frameworks", {})
                            .get("javascript", {})
                            .keys()
                        ),
                    },
                    "security_sources": [
                        "NVD (National Vulnerability Database)",
                        "GitHub Security Advisories",
                    ],
                    "documentation_sources": [
                        "MDN Web Docs",
                        "Python Documentation",
                        "OWASP Security Guidelines",
                    ],
                },
            }

            return {"success": True, "sources": sources}

        except Exception as e:
            logger.error(f"Error getting sources: {e}")
            return {"success": False, "error": str(e)}

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.web_search_engine:
                await self.web_search_engine.stop()
            if self.update_monitor:
                await self.update_monitor.stop_monitoring()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# CLI Commands using Click
@click.group()
def web_search_cli():
    """Web Search and Update Monitoring CLI"""
    pass


@web_search_cli.command()
@click.option("--query", "-q", required=True, help="Search query")
@click.option("--language", "-l", help="Programming language filter")
@click.option("--framework", "-f", help="Framework filter")
@click.option("--max-results", "-m", default=20, help="Maximum number of results")
@click.option("--output", "-o", help="Output file for results")
def search(query, language, framework, max_results, output):
    """Search for coding best practices"""

    async def run_search():
        commands = WebSearchCommands(None)
        try:
            result = await commands.search_best_practices(
                query=query,
                language=language,
                framework=framework,
                max_results=max_results,
            )

            if output:
                with open(output, "w") as f:
                    json.dump(result, f, indent=2)
                click.echo(f"Results saved to {output}")
            else:
                click.echo(json.dumps(result, indent=2))

        finally:
            await commands.cleanup()

    asyncio.run(run_search())


@web_search_cli.command()
@click.option("--output", "-o", help="Output file for summary")
def summary(output):
    """Get update summary"""

    async def run_summary():
        commands = WebSearchCommands(None)
        try:
            result = await commands.get_update_summary()

            if output:
                with open(output, "w") as f:
                    json.dump(result, f, indent=2)
                click.echo(f"Summary saved to {output}")
            else:
                click.echo(json.dumps(result, indent=2))

        finally:
            await commands.cleanup()

    asyncio.run(run_summary())


@web_search_cli.command()
@click.option("--output", "-o", help="Output file for recommendations")
def recommendations(output):
    """Get update recommendations"""

    async def run_recommendations():
        commands = WebSearchCommands(None)
        try:
            result = await commands.get_update_recommendations()

            if output:
                with open(output, "w") as f:
                    json.dump(result, f, indent=2)
                click.echo(f"Recommendations saved to {output}")
            else:
                click.echo(json.dumps(result, indent=2))

        finally:
            await commands.cleanup()

    asyncio.run(run_recommendations())


@web_search_cli.command()
@click.option("--output", "-o", help="Output file for vulnerabilities")
def vulnerabilities(output):
    """Get critical vulnerabilities"""

    async def run_vulnerabilities():
        commands = WebSearchCommands(None)
        try:
            result = await commands.get_critical_vulnerabilities()

            if output:
                with open(output, "w") as f:
                    json.dump(result, f, indent=2)
                click.echo(f"Vulnerabilities saved to {output}")
            else:
                click.echo(json.dumps(result, indent=2))

        finally:
            await commands.cleanup()

    asyncio.run(run_vulnerabilities())


@web_search_cli.command()
@click.option("--output", "-o", help="Output file for best practices")
def best_practices(output):
    """Get recent best practice changes"""

    async def run_best_practices():
        commands = WebSearchCommands(None)
        try:
            result = await commands.get_recent_best_practice_changes()

            if output:
                with open(output, "w") as f:
                    json.dump(result, f, indent=2)
                click.echo(f"Best practices saved to {output}")
            else:
                click.echo(json.dumps(result, indent=2))

        finally:
            await commands.cleanup()

    asyncio.run(run_best_practices())


@web_search_cli.command()
def status():
    """Get monitoring status"""

    async def run_status():
        commands = WebSearchCommands(None)
        try:
            result = await commands.get_monitoring_status()
            click.echo(json.dumps(result, indent=2))
        finally:
            await commands.cleanup()

    asyncio.run(run_status())


@web_search_cli.command()
def start():
    """Start monitoring"""

    async def run_start():
        commands = WebSearchCommands(None)
        try:
            result = await commands.start_monitoring()
            click.echo(json.dumps(result, indent=2))
        finally:
            await commands.cleanup()

    asyncio.run(run_start())


@web_search_cli.command()
def stop():
    """Stop monitoring"""

    async def run_stop():
        commands = WebSearchCommands(None)
        try:
            result = await commands.stop_monitoring()
            click.echo(json.dumps(result, indent=2))
        finally:
            await commands.cleanup()

    asyncio.run(run_stop())


@web_search_cli.command()
def check():
    """Manually check for updates"""

    async def run_check():
        commands = WebSearchCommands(None)
        try:
            result = await commands.manual_check_updates()
            click.echo(json.dumps(result, indent=2))
        finally:
            await commands.cleanup()

    asyncio.run(run_check())


@web_search_cli.command()
def health():
    """Health check"""

    async def run_health():
        commands = WebSearchCommands(None)
        try:
            result = await commands.health_check()
            click.echo(json.dumps(result, indent=2))
        finally:
            await commands.cleanup()

    asyncio.run(run_health())


@web_search_cli.command()
def sources():
    """Get available sources"""

    async def run_sources():
        commands = WebSearchCommands(None)
        try:
            result = await commands.get_available_sources()
            click.echo(json.dumps(result, indent=2))
        finally:
            await commands.cleanup()

    asyncio.run(run_sources())


if __name__ == "__main__":
    web_search_cli()
