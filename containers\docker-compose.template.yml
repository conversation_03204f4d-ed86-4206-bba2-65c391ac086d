# Standardized Docker Compose Template
# Follows cursorrules.md Docker-First Policy requirements
# Includes secrets, health checks, logging, and resource limits

version: '3.8'

# Define networks
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Define secrets (externalize sensitive data)
secrets:
  db_password:
    file: ./secrets/db_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  api_key:
    file: ./secrets/api_key.txt

# Define volumes for persistent data
volumes:
  app_data:
    driver: local
  app_logs:
    driver: local
  app_backups:
    driver: local
  db_data:
    driver: local

services:
  # Example Python API Service
  api:
    build:
      context: ..
      dockerfile: containers/Dockerfile.api
    container_name: app-api
    restart: unless-stopped
    
    # Environment configuration
    env_file: ../.env
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - API_PORT=8000
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 15s
      start_period: 30s
      retries: 3
    
    # Logging configuration
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    read_only: false
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Network configuration
    networks:
      - app-network
    
    # Port configuration (use external port mapping file)
    ports: []
    
    # Volume mounts
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - app_backups:/app/backups:ro
    
    # Secrets
    secrets:
      - jwt_secret
      - api_key
    
    # Dependencies
    depends_on:
      db:
        condition: service_healthy

  # Example Database Service
  db:
    image: postgres:15-alpine
    container_name: app-db
    restart: unless-stopped
    
    # Environment configuration
    environment:
      - POSTGRES_DB=${DB_NAME:-app_db}
      - POSTGRES_USER=${DB_USER:-app_user}
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
    
    # Health check
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-app_user} -d ${DB_NAME:-app_db}"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3
    
    # Logging configuration
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    
    # Network configuration
    networks:
      - app-network
    
    # Volume mounts
    volumes:
      - db_data:/var/lib/postgresql/data
    
    # Secrets
    secrets:
      - db_password

  # Example Frontend Service
  frontend:
    build:
      context: ..
      dockerfile: containers/Dockerfile.frontend
    container_name: app-frontend
    restart: unless-stopped
    
    # Environment configuration
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      start_period: 30s
      retries: 3
    
    # Logging configuration
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security options
    security_opt:
      - no-new-privileges:true
    read_only: false
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    
    # Network configuration
    networks:
      - app-network
    
    # Dependencies
    depends_on:
      api:
        condition: service_healthy
