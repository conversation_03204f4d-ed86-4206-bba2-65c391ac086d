# AI Coding Agent Docker Compose Environment Configuration
# Copy this file to .env and update the values as needed

# Database Configuration
DB_NAME=ai_coding_agent
DB_USER=ai_coding_user
# DB_PASSWORD is managed via Docker secrets (see containers/secrets/db_password.txt)

# Redis Configuration
# REDIS_PASSWORD is managed via Docker secrets (see containers/secrets/redis_password.txt)

# Application Secrets (managed via Docker secrets)
# SECRET_KEY - see containers/secrets/secret_key.txt
# JWT_SECRET - see containers/secrets/jwt_secret.txt
# GRAFANA_PASSWORD - see containers/secrets/grafana_password.txt

# External Services
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Service Ports (optional - defaults are used if not specified)
FINE_TUNER_PORT=8002
VALIDATION_PORT=8004

# Logging Configuration
LOG_LEVEL=INFO

# Cache Configuration
CACHE_TTL=3600

# Monitoring Configuration
PROMETHEUS_RETENTION_DAYS=15
GRAFANA_ADMIN_EMAIL=<EMAIL>

# Security Configuration
ENABLE_SSL=true
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Resource Limits (optional - defaults are used if not specified)
# API_CPU_LIMIT=2.0
# API_MEMORY_LIMIT=4G
# OLLAMA_CPU_LIMIT=1.5
# OLLAMA_MEMORY_LIMIT=4G

# Development Configuration
DEBUG=false
RELOAD_ON_CHANGE=false

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
