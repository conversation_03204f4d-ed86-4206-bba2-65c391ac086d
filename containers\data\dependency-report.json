{"generated_at": "2025-07-23T15:13:42.572163", "python": {"current": [{"name": "alabaster", "version": "0.7.16"}, {"name": "annotated-types", "version": "0.7.0"}, {"name": "anyio", "version": "4.9.0"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.6.0"}, {"name": "babel", "version": "2.17.0"}, {"name": "bandit", "version": "1.7.5"}, {"name": "bcrypt", "version": "4.3.0"}, {"name": "bidict", "version": "0.23.1"}, {"name": "black", "version": "24.3.0"}, {"name": "blinker", "version": "1.9.0"}, {"name": "boolean.py", "version": "5.0"}, {"name": "CacheControl", "version": "0.14.3"}, {"name": "certifi", "version": "2025.7.14"}, {"name": "cffi", "version": "1.17.1"}, {"name": "cfgv", "version": "3.4.0"}, {"name": "charset-normalizer", "version": "3.4.2"}, {"name": "click", "version": "8.2.1"}, {"name": "colorama", "version": "0.4.6"}, {"name": "coverage", "version": "7.9.2"}, {"name": "cryptography", "version": "45.0.5"}, {"name": "cyclonedx-python-lib", "version": "9.1.0"}, {"name": "defusedxml", "version": "0.7.1"}, {"name": "deprecation", "version": "2.1.0"}, {"name": "distlib", "version": "0.4.0"}, {"name": "docutils", "version": "0.20.1"}, {"name": "dparse", "version": "0.6.4"}, {"name": "ecdsa", "version": "0.19.1"}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.116.1"}, {"name": "filelock", "version": "3.16.1"}, {"name": "flake8", "version": "7.0.0"}, {"name": "Flask", "version": "3.1.1"}, {"name": "flask-cors", "version": "6.0.1"}, {"name": "Flask-SocketIO", "version": "5.5.1"}, {"name": "gitdb", "version": "4.0.12"}, {"name": "GitPython", "version": "3.1.44"}, {"name": "gotrue", "version": "2.12.3"}, {"name": "greenlet", "version": "3.2.3"}, {"name": "h11", "version": "0.16.0"}, {"name": "h2", "version": "4.2.0"}, {"name": "hpack", "version": "4.1.0"}, {"name": "httpcore", "version": "1.0.9"}, {"name": "httptools", "version": "0.6.4"}, {"name": "httpx", "version": "0.28.1"}, {"name": "hyperframe", "version": "6.1.0"}, {"name": "identify", "version": "2.6.12"}, {"name": "idna", "version": "3.10"}, {"name": "imagesize", "version": "1.4.1"}, {"name": "iniconfig", "version": "2.1.0"}, {"name": "isort", "version": "5.13.2"}, {"name": "itsdangerous", "version": "2.2.0"}, {"name": "Jinja2", "version": "3.1.6"}, {"name": "joblib", "version": "1.5.1"}, {"name": "license-expression", "version": "30.4.4"}, {"name": "markdown-it-py", "version": "3.0.0"}, {"name": "MarkupSafe", "version": "3.0.2"}, {"name": "marshmallow", "version": "4.0.0"}, {"name": "mccabe", "version": "0.7.0"}, {"name": "mdurl", "version": "0.1.2"}, {"name": "msgpack", "version": "1.1.1"}, {"name": "mypy", "version": "1.8.0"}, {"name": "mypy_extensions", "version": "1.1.0"}, {"name": "nltk", "version": "3.9.1"}, {"name": "nodeenv", "version": "1.9.1"}, {"name": "packageurl-python", "version": "0.17.1"}, {"name": "packaging", "version": "25.0"}, {"name": "passlib", "version": "1.7.4"}, {"name": "pathspec", "version": "0.12.1"}, {"name": "pbr", "version": "6.1.1"}, {"name": "pillow", "version": "11.3.0"}, {"name": "pip", "version": "25.1.1"}, {"name": "pip-api", "version": "0.0.34"}, {"name": "pip_audit", "version": "2.9.0"}, {"name": "pip-requirements-parser", "version": "32.0.1"}, {"name": "platformdirs", "version": "4.3.8"}, {"name": "pluggy", "version": "1.6.0"}, {"name": "postgrest", "version": "1.1.1"}, {"name": "pre-commit", "version": "3.6.0"}, {"name": "psutil", "version": "6.1.1"}, {"name": "py-serializable", "version": "2.1.0"}, {"name": "pyasn1", "version": "0.4.8"}, {"name": "pycodestyle", "version": "2.11.1"}, {"name": "pyc<PERSON><PERSON>", "version": "2.22"}, {"name": "pydantic", "version": "2.11.7"}, {"name": "pydantic_core", "version": "2.33.2"}, {"name": "pydantic-settings", "version": "2.10.1"}, {"name": "pyflakes", "version": "3.2.0"}, {"name": "Pygments", "version": "2.19.2"}, {"name": "PyJWT", "version": "2.10.1"}, {"name": "pyparsing", "version": "3.2.3"}, {"name": "pytest", "version": "8.0.0"}, {"name": "pytest-asyncio", "version": "0.23.5"}, {"name": "pytest-cov", "version": "4.1.0"}, {"name": "pytest-mock", "version": "3.12.0"}, {"name": "python-dateutil", "version": "2.9.0.post0"}, {"name": "python-dotenv", "version": "1.0.0"}, {"name": "python-engineio", "version": "4.12.2"}, {"name": "python-jose", "version": "3.4.0"}, {"name": "python-multipart", "version": "0.0.18"}, {"name": "python-socketio", "version": "5.13.0"}, {"name": "PyYAML", "version": "6.0.2"}, {"name": "realtime", "version": "2.6.0"}, {"name": "regex", "version": "2024.11.6"}, {"name": "requests", "version": "2.32.4"}, {"name": "rich", "version": "14.0.0"}, {"name": "rsa", "version": "4.9.1"}, {"name": "ruamel.yaml", "version": "0.18.14"}, {"name": "ruamel.yaml.clib", "version": "0.2.12"}, {"name": "safety", "version": "3.6.0"}, {"name": "safety-schemas", "version": "0.0.14"}, {"name": "setuptools", "version": "80.9.0"}, {"name": "shellingham", "version": "1.5.4"}, {"name": "simple-websocket", "version": "1.1.0"}, {"name": "six", "version": "1.17.0"}, {"name": "smmap", "version": "5.0.2"}, {"name": "sniffio", "version": "1.3.1"}, {"name": "snowballstemmer", "version": "3.0.1"}, {"name": "sortedcontainers", "version": "2.4.0"}, {"name": "Sphinx", "version": "7.2.6"}, {"name": "sphinx-rtd-theme", "version": "2.0.0"}, {"name": "sphinxcontrib-applehelp", "version": "2.0.0"}, {"name": "sphinxcontrib-devhelp", "version": "2.0.0"}, {"name": "sphinxcontrib-htmlhelp", "version": "2.1.0"}, {"name": "sphinxcontrib-j<PERSON>y", "version": "4.1"}, {"name": "sphinxcontrib-j<PERSON>th", "version": "1.0.1"}, {"name": "sphinxcontrib-qthelp", "version": "2.0.0"}, {"name": "sphinxcontrib-serializinghtml", "version": "2.0.0"}, {"name": "SQLAlchemy", "version": "2.0.41"}, {"name": "starlette", "version": "0.47.2"}, {"name": "s<PERSON><PERSON><PERSON>", "version": "5.4.1"}, {"name": "storage3", "version": "0.12.0"}, {"name": "StrEnum", "version": "0.4.15"}, {"name": "supabase", "version": "2.17.0"}, {"name": "supafunc", "version": "0.10.1"}, {"name": "tenacity", "version": "9.1.2"}, {"name": "toml", "version": "0.10.2"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.13.3"}, {"name": "tqdm", "version": "4.67.1"}, {"name": "typer", "version": "0.16.0"}, {"name": "types-passlib", "version": "1.7.7.20250602"}, {"name": "types-psutil", "version": "7.0.0.20250601"}, {"name": "types-pyasn1", "version": "0.6.0.20250516"}, {"name": "types-python-jose", "version": "3.5.0.20250531"}, {"name": "types-requests", "version": "2.32.4.20250611"}, {"name": "typing_extensions", "version": "4.14.1"}, {"name": "typing-inspection", "version": "0.4.1"}, {"name": "urllib3", "version": "2.5.0"}, {"name": "u<PERSON><PERSON>", "version": "0.35.0"}, {"name": "virtualenv", "version": "20.31.2"}, {"name": "watchdog", "version": "3.0.0"}, {"name": "watchfiles", "version": "1.1.0"}, {"name": "websockets", "version": "15.0"}, {"name": "Werkzeug", "version": "3.1.3"}, {"name": "wsproto", "version": "1.2.0"}], "outdated": [{"name": "alabaster", "version": "0.7.16", "latest_version": "1.0.0", "latest_filetype": "wheel"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.6.0", "latest_version": "1.6.1", "latest_filetype": "wheel"}, {"name": "bandit", "version": "1.7.5", "latest_version": "1.8.6", "latest_filetype": "wheel"}, {"name": "black", "version": "24.3.0", "latest_version": "25.1.0", "latest_filetype": "wheel"}, {"name": "cyclonedx-python-lib", "version": "9.1.0", "latest_version": "11.0.0", "latest_filetype": "wheel"}, {"name": "docutils", "version": "0.20.1", "latest_version": "0.21.2", "latest_filetype": "wheel"}, {"name": "filelock", "version": "3.16.1", "latest_version": "3.18.0", "latest_filetype": "wheel"}, {"name": "flake8", "version": "7.0.0", "latest_version": "7.3.0", "latest_filetype": "wheel"}, {"name": "isort", "version": "5.13.2", "latest_version": "6.0.1", "latest_filetype": "wheel"}, {"name": "mypy", "version": "1.8.0", "latest_version": "1.17.0", "latest_filetype": "wheel"}, {"name": "pre-commit", "version": "3.6.0", "latest_version": "4.2.0", "latest_filetype": "wheel"}, {"name": "psutil", "version": "6.1.1", "latest_version": "7.0.0", "latest_filetype": "wheel"}, {"name": "pyasn1", "version": "0.4.8", "latest_version": "0.6.1", "latest_filetype": "wheel"}, {"name": "pycodestyle", "version": "2.11.1", "latest_version": "2.14.0", "latest_filetype": "wheel"}, {"name": "pydantic_core", "version": "2.33.2", "latest_version": "2.35.2", "latest_filetype": "wheel"}, {"name": "pyflakes", "version": "3.2.0", "latest_version": "3.4.0", "latest_filetype": "wheel"}, {"name": "pytest", "version": "8.0.0", "latest_version": "8.4.1", "latest_filetype": "wheel"}, {"name": "pytest-asyncio", "version": "0.23.5", "latest_version": "1.1.0", "latest_filetype": "wheel"}, {"name": "pytest-cov", "version": "4.1.0", "latest_version": "6.2.1", "latest_filetype": "wheel"}, {"name": "pytest-mock", "version": "3.12.0", "latest_version": "3.14.1", "latest_filetype": "wheel"}, {"name": "python-dotenv", "version": "1.0.0", "latest_version": "1.1.1", "latest_filetype": "wheel"}, {"name": "python-jose", "version": "3.4.0", "latest_version": "3.5.0", "latest_filetype": "wheel"}, {"name": "python-multipart", "version": "0.0.18", "latest_version": "0.0.20", "latest_filetype": "wheel"}, {"name": "Sphinx", "version": "7.2.6", "latest_version": "8.2.3", "latest_filetype": "wheel"}, {"name": "sphinx-rtd-theme", "version": "2.0.0", "latest_version": "3.0.2", "latest_filetype": "wheel"}, {"name": "virtualenv", "version": "20.31.2", "latest_version": "20.32.0", "latest_filetype": "wheel"}, {"name": "watchdog", "version": "3.0.0", "latest_version": "6.0.0", "latest_filetype": "wheel"}, {"name": "websockets", "version": "15.0", "latest_version": "15.0.1", "latest_filetype": "wheel"}], "vulnerabilities": []}, "nodejs": {"current": {}, "outdated": {}, "vulnerabilities": []}, "summary": {"python": {"total_packages": 153, "outdated_packages": 28, "vulnerabilities": 0, "update_percentage": 18.30065359477124}, "nodejs": {"total_packages": 0, "outdated_packages": 0, "vulnerabilities": 0, "update_percentage": 0}, "total_vulnerabilities": 0, "total_outdated": 28}}