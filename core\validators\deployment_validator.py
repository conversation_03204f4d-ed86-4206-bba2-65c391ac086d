"""
DeploymentValidator - Unified deployment validation.
"""

from pathlib import Path
from typing import Any, Dict, List


class DeploymentValidator:
    """Deployment validation and safety checking"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

    def validate_deployment(self, deployment_path: str) -> Dict[str, Any]:
        """Validate deployment configuration"""
        issues = []

        path = Path(deployment_path)
        if not path.exists():
            issues.append(
                {
                    "type": "path",
                    "severity": "error",
                    "message": f"Deployment path does not exist: {deployment_path}",
                }
            )

        return {"valid": len(issues) == 0, "issues": issues, "path": deployment_path}

    def validate_site_structure(self, site_path: str) -> Dict[str, Any]:
        """Validate site structure for deployment"""
        issues = []
        site_dir = Path(site_path)

        # Check for index file
        index_files = ["index.html", "index.htm", "default.html"]
        has_index = any((site_dir / index_file).exists() for index_file in index_files)

        if not has_index:
            issues.append(
                {
                    "type": "structure",
                    "severity": "warning",
                    "message": "No index file found",
                }
            )

        return {"valid": len(issues) == 0, "issues": issues, "site_path": site_path}
