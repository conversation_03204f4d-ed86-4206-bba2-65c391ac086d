#!/usr/bin/env python3
"""
CodeAnalyzer - Context extraction from existing code

This module provides comprehensive code analysis capabilities including:
1. Code structure analysis
2. Dependency extraction
3. Complexity analysis
4. Quality assessment
5. Context extraction
6. Multi-language support
"""

import ast
import json
import logging
import re
import statistics
import time
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

import astor

logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """Types of code analysis"""

    STRUCTURE = "structure"
    COMPLEXITY = "complexity"
    QUALITY = "quality"
    DEPENDENCIES = "dependencies"
    CONTEXT = "context"
    SECURITY = "security"


class ComplexityLevel(Enum):
    """Code complexity levels"""

    TRIVIAL = "trivial"
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"


@dataclass
class CodeStructure:
    """Represents code structure analysis"""

    functions: List[Dict[str, Any]] = field(default_factory=list)
    classes: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)
    total_lines: int = 0
    code_lines: int = 0
    comment_lines: int = 0
    blank_lines: int = 0


@dataclass
class ComplexityMetrics:
    """Represents complexity analysis metrics"""

    cyclomatic_complexity: float = 0.0
    cognitive_complexity: float = 0.0
    nesting_depth: int = 0
    function_length: int = 0
    class_length: int = 0
    parameter_count: int = 0
    variable_count: int = 0
    complexity_level: ComplexityLevel = ComplexityLevel.SIMPLE


@dataclass
class QualityMetrics:
    """Represents code quality metrics"""

    maintainability_index: float = 0.0
    code_smells: List[str] = field(default_factory=list)
    violations: List[str] = field(default_factory=list)
    test_coverage: float = 0.0
    documentation_score: float = 0.0
    naming_convention_score: float = 0.0
    overall_quality_score: float = 0.0


@dataclass
class CodeAnalysis:
    """Comprehensive code analysis result"""

    structure: CodeStructure
    complexity: ComplexityMetrics
    quality: QualityMetrics
    dependencies: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    analysis_time: float = 0.0
    language: str = "python"
    created_at: datetime = field(default_factory=datetime.now)


class CodeAnalyzer:
    """
    Provides comprehensive code analysis capabilities for context extraction.
    """

    def __init__(self, config_path: str = "config/code_analyzer_config.json"):
        self.config = self._load_config(config_path)

        # Analysis state
        self.analysis_cache: Dict[str, CodeAnalysis] = {}
        self.pattern_cache: Dict[str, Any] = {}

        # Performance tracking
        self.analysis_metrics = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "average_analysis_time": 0.0,
            "language_usage": defaultdict(int),
        }

        logger.info("CodeAnalyzer initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded code analyzer config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "analysis": {
                "enable_caching": True,
                "cache_size": 500,
                "max_file_size_mb": 10,
                "enable_parallel_analysis": True,
                "analysis_timeout_seconds": 60,
            },
            "complexity": {
                "enabled": True,
                "cyclomatic_threshold": 10,
                "cognitive_threshold": 15,
                "nesting_threshold": 5,
                "function_length_threshold": 50,
                "class_length_threshold": 200,
            },
            "quality": {
                "enabled": True,
                "maintainability_threshold": 65,
                "test_coverage_threshold": 80,
                "documentation_threshold": 70,
                "naming_convention_threshold": 80,
            },
            "dependencies": {
                "enabled": True,
                "extract_imports": True,
                "extract_external_dependencies": True,
                "extract_internal_dependencies": True,
                "dependency_depth": 3,
            },
            "context": {
                "enabled": True,
                "extract_patterns": True,
                "extract_conventions": True,
                "extract_architecture": True,
                "extract_style": True,
            },
            "languages": {
                "python": {"enabled": True, "parser": "ast", "extensions": [".py"]},
                "typescript": {
                    "enabled": True,
                    "parser": "typescript-parser",
                    "extensions": [".ts", ".tsx"],
                },
                "javascript": {
                    "enabled": True,
                    "parser": "esprima",
                    "extensions": [".js", ".jsx"],
                },
            },
        }

    async def analyze_code(self, code: str, language: str = "python") -> Dict[str, Any]:
        """
        Analyze code and return comprehensive analysis results
        """
        try:
            start_time = time.time()
            self.analysis_metrics["total_analyses"] += 1

            # Check cache first
            cache_key = f"{hash(code)}_{language}"
            if cache_key in self.analysis_cache:
                cached_analysis = self.analysis_cache[cache_key]
                return {
                    "structure": asdict(cached_analysis.structure),
                    "complexity": asdict(cached_analysis.complexity),
                    "quality": asdict(cached_analysis.quality),
                    "dependencies": cached_analysis.dependencies,
                    "context": cached_analysis.context,
                    "analysis_time": cached_analysis.analysis_time,
                    "language": cached_analysis.language,
                }

            # Handle both string and enum values
            language_str = (
                language.value if hasattr(language, "value") else str(language)
            )

            # Perform analysis based on language
            if language_str == "python":
                analysis = await self._analyze_python_code(code)
            elif language_str in ["typescript", "javascript", "jsx", "tsx"]:
                analysis = await self._analyze_js_code(code, language_str)
            else:
                raise ValueError(f"Unsupported language: {language}")

            # Calculate analysis time
            analysis_time = time.time() - start_time
            analysis.analysis_time = analysis_time
            analysis.language = language

            # Cache analysis result
            if self.config.get("analysis", {}).get("enable_caching", True):
                self.analysis_cache[cache_key] = analysis

                # Limit cache size
                if len(self.analysis_cache) > self.config.get("analysis", {}).get(
                    "cache_size", 500
                ):
                    # Remove oldest entries
                    oldest_key = next(iter(self.analysis_cache))
                    del self.analysis_cache[oldest_key]

            # Update metrics
            self.analysis_metrics["successful_analyses"] += 1
            self.analysis_metrics["language_usage"][language] += 1

            logger.info(f"Analyzed {language} code in {analysis_time:.2f}s")

            return {
                "structure": asdict(analysis.structure),
                "complexity": asdict(analysis.complexity),
                "quality": asdict(analysis.quality),
                "dependencies": analysis.dependencies,
                "context": analysis.context,
                "analysis_time": analysis_time,
                "language": language,
            }

        except Exception as e:
            self.analysis_metrics["failed_analyses"] += 1
            logger.error(f"Failed to analyze code: {e}")
            raise

    async def _analyze_python_code(self, code: str) -> CodeAnalysis:
        """Analyze Python code"""
        try:
            # Parse code to AST
            ast_tree = ast.parse(code)

            # Analyze structure
            structure = await self._analyze_python_structure(ast_tree, code)

            # Analyze complexity
            complexity = await self._analyze_python_complexity(ast_tree)

            # Analyze quality
            quality = await self._analyze_python_quality(ast_tree, code)

            # Extract dependencies
            dependencies = await self._extract_python_dependencies(ast_tree)

            # Extract context
            context = await self._extract_python_context(ast_tree, code)

            return CodeAnalysis(
                structure=structure,
                complexity=complexity,
                quality=quality,
                dependencies=dependencies,
                context=context,
            )

        except Exception as e:
            logger.error(f"Failed to analyze Python code: {e}")
            raise

    async def _analyze_js_code(self, code: str, language: str) -> CodeAnalysis:
        """Analyze JavaScript/TypeScript code"""
        try:
            # This is a simplified implementation
            # In a full implementation, this would use proper JS/TS parsers

            # Create basic structure
            structure = CodeStructure(
                total_lines=len(code.split("\n")),
                code_lines=len(
                    [
                        line
                        for line in code.split("\n")
                        if line.strip() and not line.strip().startswith("//")
                    ]
                ),
                comment_lines=len(
                    [line for line in code.split("\n") if line.strip().startswith("//")]
                ),
                blank_lines=len(
                    [line for line in code.split("\n") if not line.strip()]
                ),
            )

            # Create basic complexity metrics
            complexity = ComplexityMetrics(
                cyclomatic_complexity=1.0,
                cognitive_complexity=1.0,
                nesting_depth=1,
                complexity_level=ComplexityLevel.SIMPLE,
            )

            # Create basic quality metrics
            quality = QualityMetrics(
                maintainability_index=80.0, overall_quality_score=75.0
            )

            # Extract basic dependencies
            dependencies = await self._extract_js_dependencies(code, language)

            # Extract basic context
            context = await self._extract_js_context(code, language)

            return CodeAnalysis(
                structure=structure,
                complexity=complexity,
                quality=quality,
                dependencies=dependencies,
                context=context,
            )

        except Exception as e:
            logger.error(f"Failed to analyze JS code: {e}")
            raise

    async def _analyze_python_structure(
        self, ast_tree: ast.AST, code: str
    ) -> CodeStructure:
        """Analyze Python code structure"""
        try:
            structure = CodeStructure()

            # Count lines
            lines = code.split("\n")
            structure.total_lines = len(lines)
            structure.code_lines = len(
                [
                    line
                    for line in lines
                    if line.strip() and not line.strip().startswith("#")
                ]
            )
            structure.comment_lines = len(
                [line for line in lines if line.strip().startswith("#")]
            )
            structure.blank_lines = len([line for line in lines if not line.strip()])

            # Extract functions
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    function_info = {
                        "name": node.name,
                        "line_number": node.lineno if hasattr(node, "lineno") else None,
                        "args": [arg.arg for arg in node.args.args],
                        "decorators": [
                            d.id if hasattr(d, "id") else str(d)
                            for d in node.decorator_list
                        ],
                        "has_docstring": self._has_docstring(node),
                        "is_async": isinstance(node, ast.AsyncFunctionDef),
                    }
                    structure.functions.append(function_info)

            # Extract classes
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "line_number": node.lineno if hasattr(node, "lineno") else None,
                        "bases": [
                            base.id if hasattr(base, "id") else str(base)
                            for base in node.bases
                        ],
                        "methods": [
                            n.name for n in node.body if isinstance(n, ast.FunctionDef)
                        ],
                        "has_docstring": self._has_docstring(node),
                    }
                    structure.classes.append(class_info)

            # Extract imports
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        structure.imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        structure.imports.append(f"{module}.{alias.name}")

            # Extract variables
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            structure.variables.append(target.id)

            return structure

        except Exception as e:
            logger.error(f"Failed to analyze Python structure: {e}")
            return CodeStructure()

    async def _analyze_python_complexity(self, ast_tree: ast.AST) -> ComplexityMetrics:
        """Analyze Python code complexity"""
        try:
            complexity = ComplexityMetrics()

            # Calculate cyclomatic complexity
            complexity.cyclomatic_complexity = self._calculate_cyclomatic_complexity(
                ast_tree
            )

            # Calculate cognitive complexity
            complexity.cognitive_complexity = self._calculate_cognitive_complexity(
                ast_tree
            )

            # Calculate nesting depth
            complexity.nesting_depth = self._calculate_nesting_depth(ast_tree)

            # Analyze function lengths
            function_lengths = []
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    function_lengths.append(self._calculate_function_length(node))

            if function_lengths:
                complexity.function_length = max(function_lengths)

            # Analyze class lengths
            class_lengths = []
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.ClassDef):
                    class_lengths.append(len(node.body))

            if class_lengths:
                complexity.class_length = max(class_lengths)

            # Count parameters
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    complexity.parameter_count = max(
                        complexity.parameter_count, len(node.args.args)
                    )

            # Count variables
            variable_names = set()
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            variable_names.add(target.id)

            complexity.variable_count = len(variable_names)

            # Determine complexity level
            complexity.complexity_level = self._determine_complexity_level(complexity)

            return complexity

        except Exception as e:
            logger.error(f"Failed to analyze Python complexity: {e}")
            return ComplexityMetrics()

    async def _analyze_python_quality(
        self, ast_tree: ast.AST, code: str
    ) -> QualityMetrics:
        """Analyze Python code quality"""
        try:
            quality = QualityMetrics()

            # Calculate maintainability index
            quality.maintainability_index = self._calculate_maintainability_index(
                ast_tree, code
            )

            # Detect code smells
            quality.code_smells = self._detect_code_smells(ast_tree)

            # Detect violations
            quality.violations = self._detect_violations(ast_tree, code)

            # Calculate documentation score
            quality.documentation_score = self._calculate_documentation_score(ast_tree)

            # Calculate naming convention score
            quality.naming_convention_score = self._calculate_naming_convention_score(
                ast_tree
            )

            # Calculate overall quality score
            quality.overall_quality_score = self._calculate_overall_quality_score(
                quality
            )

            return quality

        except Exception as e:
            logger.error(f"Failed to analyze Python quality: {e}")
            return QualityMetrics()

    async def _extract_python_dependencies(self, ast_tree: ast.AST) -> List[str]:
        """Extract Python dependencies"""
        try:
            dependencies = []

            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dependencies.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        dependencies.append(f"{module}.{alias.name}")

            return dependencies

        except Exception as e:
            logger.error(f"Failed to extract Python dependencies: {e}")
            return []

    async def _extract_python_context(
        self, ast_tree: ast.AST, code: str
    ) -> Dict[str, Any]:
        """Extract Python code context"""
        try:
            context = {
                "patterns": [],
                "conventions": {},
                "architecture": {},
                "style": {},
            }

            # Extract patterns
            context["patterns"] = self._extract_patterns(ast_tree)

            # Extract conventions
            context["conventions"] = self._extract_conventions(code)

            # Extract architecture
            context["architecture"] = self._extract_architecture(ast_tree)

            # Extract style
            context["style"] = self._extract_style(code)

            return context

        except Exception as e:
            logger.error(f"Failed to extract Python context: {e}")
            return {}

    async def _extract_js_dependencies(self, code: str, language: str) -> List[str]:
        """Extract JavaScript/TypeScript dependencies"""
        try:
            dependencies = []

            # Extract import statements
            import_patterns = [
                r"import\s+.*?from\s+['\"]([^'\"]+)['\"]",
                r"require\s*\(\s*['\"]([^'\"]+)['\"]\s*\)",
                r"import\s+['\"]([^'\"]+)['\"]",
            ]

            for pattern in import_patterns:
                matches = re.findall(pattern, code)
                dependencies.extend(matches)

            return list(set(dependencies))

        except Exception as e:
            logger.error(f"Failed to extract JS dependencies: {e}")
            return []

    async def _extract_js_context(self, code: str, language: str) -> Dict[str, Any]:
        """Extract JavaScript/TypeScript code context"""
        try:
            context = {
                "patterns": [],
                "conventions": {},
                "architecture": {},
                "style": {},
            }

            # Extract basic patterns
            context["patterns"] = self._extract_js_patterns(code)

            # Extract conventions
            context["conventions"] = self._extract_js_conventions(code)

            return context

        except Exception as e:
            logger.error(f"Failed to extract JS context: {e}")
            return {}

    # Helper methods for complexity analysis
    def _calculate_cyclomatic_complexity(self, ast_tree: ast.AST) -> float:
        """Calculate cyclomatic complexity"""
        try:
            complexity = 1  # Base complexity

            for node in ast.walk(ast_tree):
                if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                    complexity += 1
                elif isinstance(node, ast.ExceptHandler):
                    complexity += 1
                elif isinstance(node, ast.BoolOp):
                    complexity += len(node.values) - 1

            return complexity

        except Exception as e:
            logger.error(f"Failed to calculate cyclomatic complexity: {e}")
            return 1.0

    def _calculate_cognitive_complexity(self, ast_tree: ast.AST) -> float:
        """Calculate cognitive complexity"""
        try:
            complexity = 0

            for node in ast.walk(ast_tree):
                if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                    complexity += 1
                elif isinstance(node, ast.ExceptHandler):
                    complexity += 1
                elif isinstance(node, ast.BoolOp):
                    complexity += len(node.values) - 1
                elif isinstance(node, ast.FunctionDef):
                    complexity += 1  # Function definition adds complexity

            return complexity

        except Exception as e:
            logger.error(f"Failed to calculate cognitive complexity: {e}")
            return 1.0

    def _calculate_nesting_depth(self, ast_tree: ast.AST) -> int:
        """Calculate maximum nesting depth"""
        try:
            max_depth = 0
            current_depth = 0

            for node in ast.walk(ast_tree):
                if isinstance(
                    node, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.Try, ast.With)
                ):
                    current_depth += 1
                    max_depth = max(max_depth, current_depth)
                elif isinstance(node, ast.FunctionDef):
                    # Reset depth for new function
                    current_depth = 0

            return max_depth

        except Exception as e:
            logger.error(f"Failed to calculate nesting depth: {e}")
            return 1

    def _calculate_function_length(self, function_node: ast.FunctionDef) -> int:
        """Calculate function length in lines"""
        try:
            if not function_node.body:
                return 0

            # Count lines in function body
            lines = 0
            for node in function_node.body:
                if hasattr(node, "lineno"):
                    lines += 1
                else:
                    lines += 1  # Fallback

            return lines

        except Exception as e:
            logger.error(f"Failed to calculate function length: {e}")
            return 0

    def _determine_complexity_level(
        self, complexity: ComplexityMetrics
    ) -> ComplexityLevel:
        """Determine complexity level based on metrics"""
        try:
            if (
                complexity.cyclomatic_complexity <= 3
                and complexity.cognitive_complexity <= 5
                and complexity.nesting_depth <= 2
            ):
                return ComplexityLevel.TRIVIAL
            elif (
                complexity.cyclomatic_complexity <= 5
                and complexity.cognitive_complexity <= 8
                and complexity.nesting_depth <= 3
            ):
                return ComplexityLevel.SIMPLE
            elif (
                complexity.cyclomatic_complexity <= 8
                and complexity.cognitive_complexity <= 12
                and complexity.nesting_depth <= 4
            ):
                return ComplexityLevel.MODERATE
            elif (
                complexity.cyclomatic_complexity <= 12
                and complexity.cognitive_complexity <= 18
                and complexity.nesting_depth <= 5
            ):
                return ComplexityLevel.COMPLEX
            else:
                return ComplexityLevel.VERY_COMPLEX

        except Exception as e:
            logger.error(f"Failed to determine complexity level: {e}")
            return ComplexityLevel.MODERATE

    # Helper methods for quality analysis
    def _calculate_maintainability_index(self, ast_tree: ast.AST, code: str) -> float:
        """Calculate maintainability index"""
        try:
            # Simplified maintainability index calculation
            # In a full implementation, this would use the standard formula

            # Factors that improve maintainability
            positive_factors = 0

            # Check for documentation
            if self._has_docstrings(ast_tree):
                positive_factors += 20

            # Check for good naming
            if self._has_good_naming(ast_tree):
                positive_factors += 15

            # Check for reasonable complexity
            complexity = self._calculate_cyclomatic_complexity(ast_tree)
            if complexity <= 5:
                positive_factors += 25
            elif complexity <= 10:
                positive_factors += 15
            else:
                positive_factors += 5

            # Base score
            base_score = 50

            return min(100, base_score + positive_factors)

        except Exception as e:
            logger.error(f"Failed to calculate maintainability index: {e}")
            return 50.0

    def _detect_code_smells(self, ast_tree: ast.AST) -> List[str]:
        """Detect code smells"""
        try:
            smells = []

            # Check for long functions
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    if self._calculate_function_length(node) > 50:
                        smells.append("long_function")

            # Check for too many parameters
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef):
                    if len(node.args.args) > 5:
                        smells.append("too_many_parameters")

            # Check for deep nesting
            if self._calculate_nesting_depth(ast_tree) > 4:
                smells.append("deep_nesting")

            return smells

        except Exception as e:
            logger.error(f"Failed to detect code smells: {e}")
            return []

    def _detect_violations(self, ast_tree: ast.AST, code: str) -> List[str]:
        """Detect code violations"""
        try:
            violations = []

            # Check for unused imports
            imports = set()
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        imports.add(f"{module}.{alias.name}")

            # Check for unused variables
            variables = set()
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            variables.add(target.id)

            # This is a simplified check - in a full implementation,
            # you would track variable usage throughout the AST

            return violations

        except Exception as e:
            logger.error(f"Failed to detect violations: {e}")
            return []

    def _calculate_documentation_score(self, ast_tree: ast.AST) -> float:
        """Calculate documentation score"""
        try:
            total_items = 0
            documented_items = 0

            for node in ast.walk(ast_tree):
                if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.Module)):
                    total_items += 1
                    if self._has_docstring(node):
                        documented_items += 1

            if total_items == 0:
                return 100.0

            return (documented_items / total_items) * 100

        except Exception as e:
            logger.error(f"Failed to calculate documentation score: {e}")
            return 0.0

    def _calculate_naming_convention_score(self, ast_tree: ast.AST) -> float:
        """Calculate naming convention score"""
        try:
            total_names = 0
            good_names = 0

            for node in ast.walk(ast_tree):
                if isinstance(node, ast.Name):
                    total_names += 1
                    if self._is_good_name(node.id):
                        good_names += 1
                elif isinstance(node, ast.FunctionDef):
                    total_names += 1
                    if self._is_good_function_name(node.name):
                        good_names += 1
                elif isinstance(node, ast.ClassDef):
                    total_names += 1
                    if self._is_good_class_name(node.name):
                        good_names += 1

            if total_names == 0:
                return 100.0

            return (good_names / total_names) * 100

        except Exception as e:
            logger.error(f"Failed to calculate naming convention score: {e}")
            return 0.0

    def _calculate_overall_quality_score(self, quality: QualityMetrics) -> float:
        """Calculate overall quality score"""
        try:
            # Weighted average of quality metrics
            weights = {
                "maintainability": 0.3,
                "documentation": 0.2,
                "naming": 0.2,
                "code_smells": 0.15,
                "violations": 0.15,
            }

            score = 0.0

            # Maintainability score
            score += (quality.maintainability_index / 100) * weights["maintainability"]

            # Documentation score
            score += (quality.documentation_score / 100) * weights["documentation"]

            # Naming convention score
            score += (quality.naming_convention_score / 100) * weights["naming"]

            # Code smells penalty
            smell_penalty = min(0.2, len(quality.code_smells) * 0.05)
            score += (1 - smell_penalty) * weights["code_smells"]

            # Violations penalty
            violation_penalty = min(0.2, len(quality.violations) * 0.05)
            score += (1 - violation_penalty) * weights["violations"]

            return score * 100

        except Exception as e:
            logger.error(f"Failed to calculate overall quality score: {e}")
            return 0.0

    # Helper methods for context extraction
    def _extract_patterns(self, ast_tree: ast.AST) -> List[str]:
        """Extract code patterns"""
        try:
            patterns = []

            # Check for common patterns
            if self._has_singleton_pattern(ast_tree):
                patterns.append("singleton")

            if self._has_factory_pattern(ast_tree):
                patterns.append("factory")

            if self._has_observer_pattern(ast_tree):
                patterns.append("observer")

            return patterns

        except Exception as e:
            logger.error(f"Failed to extract patterns: {e}")
            return []

    def _extract_conventions(self, code: str) -> Dict[str, Any]:
        """Extract code conventions"""
        try:
            conventions = {
                "indentation": "spaces",
                "line_length": 88,
                "quote_style": "double",
                "naming_style": "snake_case",
            }

            # Analyze indentation
            lines = code.split("\n")
            if lines:
                first_line = lines[0]
                if first_line.startswith("\t"):
                    conventions["indentation"] = "tabs"
                else:
                    # Count leading spaces
                    spaces = len(first_line) - len(first_line.lstrip())
                    conventions["indent_size"] = spaces

            # Analyze quote style
            single_quotes = code.count("'")
            double_quotes = code.count('"')
            if single_quotes > double_quotes:
                conventions["quote_style"] = "single"

            return conventions

        except Exception as e:
            logger.error(f"Failed to extract conventions: {e}")
            return {}

    def _extract_architecture(self, ast_tree: ast.AST) -> Dict[str, Any]:
        """Extract architecture information"""
        try:
            architecture = {"type": "procedural", "layers": [], "components": []}

            # Check for object-oriented patterns
            classes = [
                node for node in ast.walk(ast_tree) if isinstance(node, ast.ClassDef)
            ]
            if classes:
                architecture["type"] = "object_oriented"
                architecture["components"] = [cls.name for cls in classes]

            return architecture

        except Exception as e:
            logger.error(f"Failed to extract architecture: {e}")
            return {}

    def _extract_style(self, code: str) -> Dict[str, Any]:
        """Extract code style information"""
        try:
            style = {
                "formatting": "consistent",
                "comments": "present",
                "structure": "clear",
            }

            # Check for comments
            comment_lines = len(
                [line for line in code.split("\n") if line.strip().startswith("#")]
            )
            if comment_lines == 0:
                style["comments"] = "absent"

            return style

        except Exception as e:
            logger.error(f"Failed to extract style: {e}")
            return {}

    def _extract_js_patterns(self, code: str) -> List[str]:
        """Extract JavaScript/TypeScript patterns"""
        try:
            patterns = []

            # Check for common JS patterns
            if "function(" in code or "=>" in code:
                patterns.append("functional")

            if "class" in code:
                patterns.append("class_based")

            if "import" in code or "require" in code:
                patterns.append("modular")

            return patterns

        except Exception as e:
            logger.error(f"Failed to extract JS patterns: {e}")
            return []

    def _extract_js_conventions(self, code: str) -> Dict[str, Any]:
        """Extract JavaScript/TypeScript conventions"""
        try:
            conventions = {"semicolons": True, "quotes": "single", "indentation": 2}

            # Check for semicolons
            if code.count(";") < len(code.split("\n")) * 0.5:
                conventions["semicolons"] = False

            # Check quote style
            if code.count('"') > code.count("'"):
                conventions["quotes"] = "double"

            return conventions

        except Exception as e:
            logger.error(f"Failed to extract JS conventions: {e}")
            return {}

    # Utility methods
    def _has_docstring(self, node: ast.AST) -> bool:
        """Check if node has a docstring"""
        try:
            if not hasattr(node, "body") or not node.body:
                return False

            first_node = node.body[0]
            if isinstance(first_node, ast.Expr) and isinstance(
                first_node.value, ast.Constant
            ):
                return isinstance(first_node.value.value, str)

            return False

        except Exception as e:
            logger.error(f"Failed to check docstring: {e}")
            return False

    def _has_good_naming(self, ast_tree: ast.AST) -> bool:
        """Check if code has good naming conventions"""
        try:
            # Simplified check - in a full implementation, this would be more comprehensive
            return True

        except Exception as e:
            logger.error(f"Failed to check naming: {e}")
            return False

    def _is_good_name(self, name: str) -> bool:
        """Check if a name follows good naming conventions"""
        try:
            # Check for snake_case (Python convention)
            if re.match(r"^[a-z][a-z0-9_]*$", name):
                return True

            # Check for camelCase
            if re.match(r"^[a-z][a-zA-Z0-9]*$", name):
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to check name: {e}")
            return False

    def _is_good_function_name(self, name: str) -> bool:
        """Check if a function name follows conventions"""
        try:
            # Python functions should be snake_case
            return re.match(r"^[a-z][a-z0-9_]*$", name) is not None

        except Exception as e:
            logger.error(f"Failed to check function name: {e}")
            return False

    def _is_good_class_name(self, name: str) -> bool:
        """Check if a class name follows conventions"""
        try:
            # Python classes should be PascalCase
            return re.match(r"^[A-Z][a-zA-Z0-9]*$", name) is not None

        except Exception as e:
            logger.error(f"Failed to check class name: {e}")
            return False

    def _has_singleton_pattern(self, ast_tree: ast.AST) -> bool:
        """Check for singleton pattern"""
        try:
            # Simplified check for singleton pattern
            return False

        except Exception as e:
            logger.error(f"Failed to check singleton pattern: {e}")
            return False

    def _has_factory_pattern(self, ast_tree: ast.AST) -> bool:
        """Check for factory pattern"""
        try:
            # Simplified check for factory pattern
            return False

        except Exception as e:
            logger.error(f"Failed to check factory pattern: {e}")
            return False

    def _has_observer_pattern(self, ast_tree: ast.AST) -> bool:
        """Check for observer pattern"""
        try:
            # Simplified check for observer pattern
            return False

        except Exception as e:
            logger.error(f"Failed to check observer pattern: {e}")
            return False

    async def get_analysis_metrics(self) -> Dict[str, Any]:
        """Get code analysis performance metrics"""
        try:
            total_analyses = self.analysis_metrics["total_analyses"]
            if total_analyses == 0:
                return self.analysis_metrics

            return {
                **self.analysis_metrics,
                "success_rate": (
                    self.analysis_metrics["successful_analyses"] / total_analyses
                )
                * 100,
                "failure_rate": (
                    self.analysis_metrics["failed_analyses"] / total_analyses
                )
                * 100,
                "cache_size": len(self.analysis_cache),
            }

        except Exception as e:
            logger.error(f"Failed to get analysis metrics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            # Clear caches
            self.analysis_cache.clear()
            self.pattern_cache.clear()

            logger.info("CodeAnalyzer cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup CodeAnalyzer: {e}")
