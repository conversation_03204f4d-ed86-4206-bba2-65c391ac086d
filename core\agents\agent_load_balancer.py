#!/usr/bin/env python3
"""
AgentLoadBalancer - Manages agent load and availability for optimal resource utilization

This module provides load balancing capabilities including:
1. Real-time load tracking
2. Availability monitoring
3. Capacity management
4. Load distribution strategies
5. Health checks
6. Resource optimization
"""

import asyncio
import json
import logging
import statistics
import time
from collections import defaultdict, deque
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class LoadStatus(Enum):
    """Agent load status"""

    IDLE = "idle"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    OVERLOADED = "overloaded"
    UNAVAILABLE = "unavailable"


class LoadBalancingStrategy(Enum):
    """Load balancing strategies"""

    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_RESPONSE_TIME = "least_response_time"
    ADAPTIVE = "adaptive"


@dataclass
class AgentLoad:
    """Represents current load for an agent"""

    agent_id: str
    agent_type: str
    current_load: int = 0
    max_capacity: int = 10
    active_tasks: int = 0
    queued_tasks: int = 0
    response_time_avg: float = 0.0
    availability_score: float = 1.0
    health_status: str = "healthy"
    last_updated: datetime = field(default_factory=datetime.now)
    load_history: deque = field(default_factory=lambda: deque(maxlen=100))


@dataclass
class LoadBalancingDecision:
    """Represents a load balancing decision"""

    selected_agent_id: str
    strategy_used: LoadBalancingStrategy
    load_score: float
    availability_score: float
    reasoning: str
    alternative_agents: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)


class AgentLoadBalancer:
    """
    Manages agent load and availability for optimal resource utilization.
    """

    def __init__(self, config_path: str = "config/load_balancer_config.json"):
        self.config = self._load_config(config_path)
        self.agent_loads: Dict[str, AgentLoad] = {}
        self.load_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.health_checks: Dict[str, datetime] = {}

        # Load balancing state
        self.current_strategy = LoadBalancingStrategy(
            self.config.get("default_strategy", "adaptive")
        )
        self.round_robin_index = 0

        # Performance tracking
        self.balancing_metrics = {
            "total_decisions": 0,
            "successful_balances": 0,
            "failed_balances": 0,
            "average_load": 0.0,
            "strategy_usage": defaultdict(int),
        }

        logger.info("AgentLoadBalancer initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded load balancer config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "load_balancing": {
                "default_strategy": "adaptive",
                "max_load_threshold": 0.8,
                "min_availability_threshold": 0.3,
                "health_check_interval_seconds": 60,
                "load_update_interval_seconds": 30,
            },
            "capacity": {
                "default_max_capacity": 10,
                "capacity_scaling": True,
                "auto_scaling_threshold": 0.9,
                "scale_up_factor": 1.5,
                "scale_down_factor": 0.8,
            },
            "health_checks": {
                "enabled": True,
                "timeout_seconds": 10,
                "retry_attempts": 3,
                "unhealthy_threshold": 3,
            },
            "monitoring": {
                "track_load_history": True,
                "track_response_times": True,
                "track_availability": True,
                "metrics_retention_days": 7,
            },
            "strategies": {
                "round_robin": {"enabled": True, "weight": 1.0},
                "least_connections": {"enabled": True, "weight": 1.0},
                "weighted_round_robin": {"enabled": True, "weight": 1.0},
                "least_response_time": {"enabled": True, "weight": 1.0},
                "adaptive": {"enabled": True, "weight": 1.0, "learning_rate": 0.1},
            },
        }

    async def register_agent(
        self, agent_id: str, agent_type: str, max_capacity: int = None
    ) -> bool:
        """Register a new agent for load balancing"""
        try:
            if agent_id not in self.agent_loads:
                max_capacity = max_capacity or self.config.get("capacity", {}).get(
                    "default_max_capacity", 10
                )

                self.agent_loads[agent_id] = AgentLoad(
                    agent_id=agent_id, agent_type=agent_type, max_capacity=max_capacity
                )

                logger.info(
                    f"Registered agent {agent_id} ({agent_type}) for load balancing with capacity {max_capacity}"
                )
            return True
        except Exception as e:
            logger.error(f"Failed to register agent {agent_id}: {e}")
            return False

    async def update_agent_load(
        self,
        agent_id: str,
        current_load: int = None,
        active_tasks: int = None,
        queued_tasks: int = None,
        response_time: float = None,
    ) -> bool:
        """Update agent load information"""
        try:
            if agent_id not in self.agent_loads:
                await self.register_agent(agent_id, "unknown")

            agent_load = self.agent_loads[agent_id]

            # Update load information
            if current_load is not None:
                agent_load.current_load = current_load
                agent_load.load_history.append(current_load)

            if active_tasks is not None:
                agent_load.active_tasks = active_tasks

            if queued_tasks is not None:
                agent_load.queued_tasks = queued_tasks

            if response_time is not None:
                # Update average response time
                if agent_load.response_time_avg == 0:
                    agent_load.response_time_avg = response_time
                else:
                    agent_load.response_time_avg = (
                        agent_load.response_time_avg * 0.9
                    ) + (response_time * 0.1)

            # Update availability score
            agent_load.availability_score = self._calculate_availability_score(
                agent_load
            )

            # Update timestamp
            agent_load.last_updated = datetime.now()

            logger.debug(
                f"Updated load for {agent_id}: load={agent_load.current_load}, "
                f"active={agent_load.active_tasks}, queued={agent_load.queued_tasks}"
            )

            return True

        except Exception as e:
            logger.error(f"Failed to update load for {agent_id}: {e}")
            return False

    def _calculate_availability_score(self, agent_load: AgentLoad) -> float:
        """Calculate availability score for an agent"""
        try:
            # Base availability from load
            load_ratio = agent_load.current_load / agent_load.max_capacity
            base_availability = max(0.0, 1.0 - load_ratio)

            # Health factor
            health_factor = 1.0 if agent_load.health_status == "healthy" else 0.3

            # Response time factor
            response_time_factor = 1.0
            if agent_load.response_time_avg > 0:
                # Normalize response time (lower is better)
                normalized_response_time = min(agent_load.response_time_avg / 60.0, 1.0)
                response_time_factor = 1.0 - normalized_response_time

            # Calculate final availability score
            availability_score = (
                base_availability * health_factor * response_time_factor
            )

            return max(0.0, min(1.0, availability_score))

        except Exception as e:
            logger.error(f"Failed to calculate availability score: {e}")
            return 0.5

    async def get_agent_load(self, agent_id: str) -> Dict[str, Any]:
        """Get comprehensive load information for an agent"""
        try:
            agent_load = self.agent_loads.get(agent_id)
            if not agent_load:
                return {}

            # Calculate load status
            load_status = self._get_load_status(agent_load)

            return {
                "agent_id": agent_id,
                "agent_type": agent_load.agent_type,
                "current_load": agent_load.current_load,
                "max_capacity": agent_load.max_capacity,
                "load_ratio": agent_load.current_load / agent_load.max_capacity,
                "active_tasks": agent_load.active_tasks,
                "queued_tasks": agent_load.queued_tasks,
                "response_time_avg": agent_load.response_time_avg,
                "availability_score": agent_load.availability_score,
                "health_status": agent_load.health_status,
                "load_status": load_status.value,
                "last_updated": agent_load.last_updated.isoformat(),
                # Last 20 points
                "load_history": list(agent_load.load_history)[-20:],
            }

        except Exception as e:
            logger.error(f"Failed to get load for {agent_id}: {e}")
            return {}

    def _get_load_status(self, agent_load: AgentLoad) -> LoadStatus:
        """Get load status for an agent"""
        try:
            load_ratio = agent_load.current_load / agent_load.max_capacity

            if agent_load.health_status != "healthy":
                return LoadStatus.UNAVAILABLE
            elif load_ratio == 0:
                return LoadStatus.IDLE
            elif load_ratio < 0.3:
                return LoadStatus.LOW
            elif load_ratio < 0.6:
                return LoadStatus.MEDIUM
            elif load_ratio < 0.9:
                return LoadStatus.HIGH
            else:
                return LoadStatus.OVERLOADED

        except Exception as e:
            logger.error(f"Failed to get load status: {e}")
            return LoadStatus.MEDIUM

    async def select_agent(
        self, available_agents: List[str] = None, strategy: LoadBalancingStrategy = None
    ) -> LoadBalancingDecision:
        """Select the best agent using the specified load balancing strategy"""
        try:
            self.balancing_metrics["total_decisions"] += 1

            # Use default strategy if none specified
            if strategy is None:
                strategy = self.current_strategy

            # Filter available agents
            if available_agents is None:
                available_agents = list(self.agent_loads.keys())

            available_agents = await self._filter_available_agents(available_agents)

            if not available_agents:
                raise ValueError("No available agents for load balancing")

            # Apply load balancing strategy
            if strategy == LoadBalancingStrategy.ROUND_ROBIN:
                decision = await self._round_robin_selection(available_agents)
            elif strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
                decision = await self._least_connections_selection(available_agents)
            elif strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
                decision = await self._weighted_round_robin_selection(available_agents)
            elif strategy == LoadBalancingStrategy.LEAST_RESPONSE_TIME:
                decision = await self._least_response_time_selection(available_agents)
            elif strategy == LoadBalancingStrategy.ADAPTIVE:
                decision = await self._adaptive_selection(available_agents)
            else:
                decision = await self._round_robin_selection(available_agents)

            # Update metrics
            self.balancing_metrics["strategy_usage"][strategy.value] += 1

            logger.info(
                f"Selected agent {decision.selected_agent_id} using {strategy.value} strategy"
            )

            return decision

        except Exception as e:
            logger.error(f"Failed to select agent: {e}")
            return await self._fallback_selection(available_agents or [])

    async def _filter_available_agents(self, agent_ids: List[str]) -> List[str]:
        """Filter agents based on availability and load"""
        available_agents = []

        for agent_id in agent_ids:
            agent_load = self.agent_loads.get(agent_id)
            if not agent_load:
                continue

            # Check health status
            if agent_load.health_status != "healthy":
                continue

            # Check load threshold
            load_ratio = agent_load.current_load / agent_load.max_capacity
            if load_ratio >= self.config.get("load_balancing", {}).get(
                "max_load_threshold", 0.8
            ):
                continue

            # Check availability threshold
            if agent_load.availability_score < self.config.get(
                "load_balancing", {}
            ).get("min_availability_threshold", 0.3):
                continue

            available_agents.append(agent_id)

        return available_agents

    async def _round_robin_selection(
        self, available_agents: List[str]
    ) -> LoadBalancingDecision:
        """Round-robin selection strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Get next agent in round-robin order
            selected_agent_id = available_agents[
                self.round_robin_index % len(available_agents)
            ]
            self.round_robin_index += 1

            agent_load = self.agent_loads[selected_agent_id]

            return LoadBalancingDecision(
                selected_agent_id=selected_agent_id,
                strategy_used=LoadBalancingStrategy.ROUND_ROBIN,
                load_score=1.0 - (agent_load.current_load / agent_load.max_capacity),
                availability_score=agent_load.availability_score,
                reasoning="Round-robin selection",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
            )

        except Exception as e:
            logger.error(f"Round-robin selection failed: {e}")
            raise

    async def _least_connections_selection(
        self, available_agents: List[str]
    ) -> LoadBalancingDecision:
        """Least connections selection strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Find agent with least active connections
            agent_loads = []
            for agent_id in available_agents:
                agent_load = self.agent_loads[agent_id]
                agent_loads.append((agent_id, agent_load.active_tasks))

            # Sort by active tasks (ascending)
            agent_loads.sort(key=lambda x: x[1])
            selected_agent_id = agent_loads[0][0]

            agent_load = self.agent_loads[selected_agent_id]

            return LoadBalancingDecision(
                selected_agent_id=selected_agent_id,
                strategy_used=LoadBalancingStrategy.LEAST_CONNECTIONS,
                load_score=1.0 - (agent_load.current_load / agent_load.max_capacity),
                availability_score=agent_load.availability_score,
                reasoning=f"Least connections selection (active_tasks={agent_load.active_tasks})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
            )

        except Exception as e:
            logger.error(f"Least connections selection failed: {e}")
            raise

    async def _weighted_round_robin_selection(
        self, available_agents: List[str]
    ) -> LoadBalancingDecision:
        """Weighted round-robin selection strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Calculate weights based on availability scores
            agent_weights = []
            total_weight = 0

            for agent_id in available_agents:
                agent_load = self.agent_loads[agent_id]
                weight = agent_load.availability_score
                agent_weights.append((agent_id, weight))
                total_weight += weight

            if total_weight == 0:
                return await self._round_robin_selection(available_agents)

            # Select agent based on weights
            import random

            random_value = random.uniform(0, total_weight)
            current_weight = 0

            for agent_id, weight in agent_weights:
                current_weight += weight
                if random_value <= current_weight:
                    selected_agent_id = agent_id
                    break
            else:
                selected_agent_id = agent_weights[-1][0]

            agent_load = self.agent_loads[selected_agent_id]

            return LoadBalancingDecision(
                selected_agent_id=selected_agent_id,
                strategy_used=LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN,
                load_score=1.0 - (agent_load.current_load / agent_load.max_capacity),
                availability_score=agent_load.availability_score,
                reasoning=f"Weighted round-robin selection (weight={agent_load.availability_score:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
            )

        except Exception as e:
            logger.error(f"Weighted round-robin selection failed: {e}")
            raise

    async def _least_response_time_selection(
        self, available_agents: List[str]
    ) -> LoadBalancingDecision:
        """Least response time selection strategy"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Find agent with lowest average response time
            agent_response_times = []
            for agent_id in available_agents:
                agent_load = self.agent_loads[agent_id]
                response_time = (
                    agent_load.response_time_avg
                    if agent_load.response_time_avg > 0
                    else float("inf")
                )
                agent_response_times.append((agent_id, response_time))

            # Sort by response time (ascending)
            agent_response_times.sort(key=lambda x: x[1])
            selected_agent_id = agent_response_times[0][0]

            agent_load = self.agent_loads[selected_agent_id]

            return LoadBalancingDecision(
                selected_agent_id=selected_agent_id,
                strategy_used=LoadBalancingStrategy.LEAST_RESPONSE_TIME,
                load_score=1.0 - (agent_load.current_load / agent_load.max_capacity),
                availability_score=agent_load.availability_score,
                reasoning=f"Least response time selection (avg_response_time={agent_load.response_time_avg:.2f}s)",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
            )

        except Exception as e:
            logger.error(f"Least response time selection failed: {e}")
            raise

    async def _adaptive_selection(
        self, available_agents: List[str]
    ) -> LoadBalancingDecision:
        """Adaptive selection strategy that learns from previous decisions"""
        try:
            if not available_agents:
                raise ValueError("No available agents")

            # Calculate adaptive scores based on multiple factors
            agent_scores = []
            for agent_id in available_agents:
                agent_load = self.agent_loads[agent_id]

                # Load factor (lower is better)
                load_factor = 1.0 - (agent_load.current_load / agent_load.max_capacity)

                # Availability factor
                availability_factor = agent_load.availability_score

                # Response time factor (lower is better)
                response_time_factor = 1.0
                if agent_load.response_time_avg > 0:
                    normalized_response_time = min(
                        agent_load.response_time_avg / 60.0, 1.0
                    )
                    response_time_factor = 1.0 - normalized_response_time

                # Calculate adaptive score
                adaptive_score = (
                    load_factor * 0.4
                    + availability_factor * 0.4
                    + response_time_factor * 0.2
                )
                agent_scores.append((agent_id, adaptive_score))

            # Sort by adaptive score (descending)
            agent_scores.sort(key=lambda x: x[1], reverse=True)
            selected_agent_id = agent_scores[0][0]

            agent_load = self.agent_loads[selected_agent_id]

            return LoadBalancingDecision(
                selected_agent_id=selected_agent_id,
                strategy_used=LoadBalancingStrategy.ADAPTIVE,
                load_score=1.0 - (agent_load.current_load / agent_load.max_capacity),
                availability_score=agent_load.availability_score,
                reasoning=f"Adaptive selection (score={agent_scores[0][1]:.2f})",
                alternative_agents=[
                    a for a in available_agents if a != selected_agent_id
                ],
            )

        except Exception as e:
            logger.error(f"Adaptive selection failed: {e}")
            raise

    async def _fallback_selection(
        self, available_agents: List[str]
    ) -> LoadBalancingDecision:
        """Fallback selection when other strategies fail"""
        if not available_agents:
            raise ValueError("No agents available for selection")

        selected_agent_id = available_agents[0]
        agent_load = self.agent_loads.get(selected_agent_id)

        return LoadBalancingDecision(
            selected_agent_id=selected_agent_id,
            strategy_used=LoadBalancingStrategy.ROUND_ROBIN,
            load_score=0.5,
            availability_score=agent_load.availability_score if agent_load else 0.5,
            reasoning="Fallback selection",
            alternative_agents=available_agents[1:],
        )

    async def get_agent_score(self, agent_id: str) -> float:
        """Get a normalized load score (0.0 to 1.0) for an agent"""
        try:
            agent_load = self.agent_loads.get(agent_id)
            if not agent_load:
                return 0.5  # Default score for unknown agents

            # Calculate load score (lower load = higher score)
            load_score = 1.0 - (agent_load.current_load / agent_load.max_capacity)

            # Combine with availability score
            final_score = (load_score * 0.6) + (agent_load.availability_score * 0.4)

            return max(0.0, min(1.0, final_score))

        except Exception as e:
            logger.error(f"Failed to get agent score for {agent_id}: {e}")
            return 0.5

    async def record_task_completion(self, agent_id: str, duration: int = None) -> None:
        """Record task completion for load balancing"""
        try:
            if agent_id in self.agent_loads:
                agent_load = self.agent_loads[agent_id]

                # Decrease active tasks
                if agent_load.active_tasks > 0:
                    agent_load.active_tasks -= 1

                # Update load
                if agent_load.current_load > 0:
                    agent_load.current_load -= 1

                # Update response time if duration provided
                if duration is not None:
                    if agent_load.response_time_avg == 0:
                        agent_load.response_time_avg = duration
                    else:
                        agent_load.response_time_avg = (
                            agent_load.response_time_avg * 0.9
                        ) + (duration * 0.1)

                # Update availability score
                agent_load.availability_score = self._calculate_availability_score(
                    agent_load
                )
                agent_load.last_updated = datetime.now()

                logger.debug(f"Recorded task completion for {agent_id}")

        except Exception as e:
            logger.error(f"Failed to record task completion for {agent_id}: {e}")

    async def get_load_balancing_metrics(self) -> Dict[str, Any]:
        """Get load balancing performance metrics"""
        try:
            total_agents = len(self.agent_loads)
            if total_agents == 0:
                return {}

            # Calculate average load
            total_load = sum(
                agent_load.current_load for agent_load in self.agent_loads.values()
            )
            average_load = total_load / total_agents

            # Calculate load distribution
            load_distribution = defaultdict(int)
            for agent_load in self.agent_loads.values():
                load_status = self._get_load_status(agent_load)
                load_distribution[load_status.value] += 1

            return {
                "total_agents": total_agents,
                "average_load": average_load,
                "load_distribution": dict(load_distribution),
                "total_decisions": self.balancing_metrics["total_decisions"],
                "successful_balances": self.balancing_metrics["successful_balances"],
                "failed_balances": self.balancing_metrics["failed_balances"],
                "strategy_usage": dict(self.balancing_metrics["strategy_usage"]),
                "current_strategy": self.current_strategy.value,
            }

        except Exception as e:
            logger.error(f"Failed to get load balancing metrics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup old load data"""
        try:
            cutoff_time = datetime.now() - timedelta(
                days=self.config.get("monitoring", {}).get("metrics_retention_days", 7)
            )

            # Cleanup load history
            for agent_id in list(self.load_history.keys()):
                self.load_history[agent_id] = deque(
                    [
                        load
                        for load in self.load_history[agent_id]
                        if load > cutoff_time
                    ],
                    maxlen=1000,
                )

            logger.info("Load balancer cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup load balancer: {e}")
