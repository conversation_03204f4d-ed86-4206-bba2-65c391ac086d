"""
CLI Decorators for AI Coding Agent.

This module provides reusable decorators for CLI commands including:
- Database context management
- Project access verification
- Error handling
"""

import functools
import logging
from contextlib import contextmanager
from typing import Any, Callable, Optional

import click

from cli.error_handler import C<PERSON><PERSON><PERSON>r
from db import get_db, project_manager
from db.models import User

logger = logging.getLogger(__name__)


def get_user_context(ctx) -> Optional[User]:
    """Get current user from context with basic implementation"""
    # Basic implementation - in production this would get user from authentication context
    # For now, implement basic user context retrieval

    # In production, you would:
    # 1. Get user from authentication token
    # 2. Verify user permissions
    # 3. Check project ownership

    # Example implementation:
    # current_user = ctx.obj.get("current_user")
    # if not current_user:
    #     raise CliError("Authentication required. Please login first.")
    # return current_user

    # For now, skip user verification (development mode)
    # In production, uncomment the above code and implement proper authentication
    return None


def with_db(func: Callable) -> Callable:
    """
    Decorator that injects a database session into the function.

    Args:
        func: The function to decorate

    Returns:
        Decorated function with db parameter injected
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with get_db() as db:
            # Inject db as the first parameter after ctx
            if args and hasattr(args[0], "obj"):  # ctx is first arg
                new_args = (args[0], db) + args[1:]
            else:
                new_args = (db,) + args
            return func(*new_args, **kwargs)

    return wrapper


def require_project_access(func: Callable) -> Callable:
    """
    Decorator that verifies project access and injects the project object.

    Args:
        func: The function to decorate

    Returns:
        Decorated function with project parameter injected
    """

    @functools.wraps(func)
    def wrapper(ctx, project_id: int, db, *args, **kwargs):
        # Verify project exists
        project = project_manager.get(db, project_id)
        if not project:
            raise CliError(f"Project {project_id} not found")

        # Verify user has access to project
        current_user = get_user_context(ctx)
        if (
            current_user
            and project.owner_id != current_user.id
            and not current_user.is_superuser
        ):
            raise CliError("Access denied: You don't own this project")

        # Inject project as parameter
        return func(ctx, project_id, db, project, *args, **kwargs)

    return wrapper


def validate_migration_sql(ctx, param, value) -> Optional[str]:
    """
    Click callback to validate that SQL is provided when using a template.

    Args:
        ctx: Click context
        param: Click parameter
        value: Parameter value

    Returns:
        The SQL value if valid

    Raises:
        click.BadParameter: If SQL is missing when template is provided
    """
    if ctx.params.get("template") and not value:
        raise click.BadParameter(
            "SQL content is required when using a template. "
            "Use --sql option to provide the SQL content."
        )
    return value
