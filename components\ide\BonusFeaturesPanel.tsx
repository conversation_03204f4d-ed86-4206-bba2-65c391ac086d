import React, { useState, useEffect } from 'react';
import { Clock, MessageSquare, Lightbulb, TrendingUp, Star, Calendar, CheckCircle, AlertCircle } from 'lucide-react';
import { bonusFeaturesService } from '@/services/BonusFeaturesService';
import toast from 'react-hot-toast';

interface BonusFeaturesPanelProps {
  className?: string;
}

export const BonusFeaturesPanel: React.FC<BonusFeaturesPanelProps> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<'queue' | 'clarifications' | 'insights' | 'schedule'>('queue');
  const [queueStatus, setQueueStatus] = useState<any>(null);
  const [clarifications, setClarifications] = useState<any[]>([]);
  const [insights, setInsights] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [scheduleForm, setScheduleForm] = useState({
    command: '',
    delayMinutes: 0,
    priority: 'medium'
  });

  // Load data based on active tab
  useEffect(() => {
    loadTabData();
  }, [activeTab]);

  const loadTabData = async () => {
    setLoading(true);
    try {
      switch (activeTab) {
        case 'queue':
          const queueData = await bonusFeaturesService.getQueueStatus();
          setQueueStatus(queueData);
          break;
        case 'clarifications':
          const clarData = await bonusFeaturesService.getPendingClarifications();
          setClarifications(clarData.clarifications || []);
          break;
        case 'insights':
          const insightsData = await bonusFeaturesService.getLearningInsights();
          setInsights(insightsData);
          break;
      }
    } catch (error) {
      console.error('Error loading tab data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleTask = async () => {
    if (!scheduleForm.command.trim()) {
      toast.error('Please enter a command');
      return;
    }

    try {
      const scheduledAt = new Date();
      scheduledAt.setMinutes(scheduledAt.getMinutes() + scheduleForm.delayMinutes);

      const result = await bonusFeaturesService.scheduleTask({
        command: scheduleForm.command,
        scheduled_at: scheduledAt.toISOString(),
        priority: scheduleForm.priority,
        max_retries: 3
      });

      if (result.success) {
        toast.success('Task scheduled successfully!');
        setScheduleForm({ command: '', delayMinutes: 0, priority: 'medium' });
        loadTabData(); // Refresh queue status
      } else {
        toast.error(result.error || 'Failed to schedule task');
      }
    } catch (error) {
      console.error('Error scheduling task:', error);
      toast.error('Failed to schedule task');
    }
  };

  const handleProvideClarification = async (requestId: string, response: string) => {
    try {
      const result = await bonusFeaturesService.provideClarification({
        request_id: requestId,
        response: { response }
      });

      if (result.success) {
        toast.success('Clarification provided successfully!');
        loadTabData(); // Refresh clarifications
      } else {
        toast.error(result.error || 'Failed to provide clarification');
      }
    } catch (error) {
      console.error('Error providing clarification:', error);
      toast.error('Failed to provide clarification');
    }
  };

  const renderQueueTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Task Queue Status</h3>
        <button
          onClick={loadTabData}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : queueStatus ? (
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-100 dark:bg-green-900 p-3 rounded">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {queueStatus.queued_tasks || 0}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">Queued</div>
            </div>
            <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {queueStatus.running_tasks || 0}
              </div>
              <div className="text-sm text-blue-600 dark:text-blue-400">Running</div>
            </div>
          </div>

          {queueStatus.recent_tasks && queueStatus.recent_tasks.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Recent Tasks</h4>
              <div className="space-y-2">
                {queueStatus.recent_tasks.slice(0, 5).map((task: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <span className="text-sm truncate">{task.name}</span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      task.status === 'completed' ? 'bg-green-100 text-green-800' :
                      task.status === 'running' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {task.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">No queue data available</div>
      )}
    </div>
  );

  const renderClarificationsTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Pending Clarifications</h3>
        <button
          onClick={loadTabData}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : clarifications.length > 0 ? (
        <div className="space-y-3">
          {clarifications.map((clarification, index) => (
            <div key={index} className="border border-gray-200 dark:border-gray-700 rounded p-3">
              <div className="flex items-start justify-between mb-2">
                <span className="text-sm font-medium">{clarification.type}</span>
                <span className="text-xs text-gray-500">{clarification.created_at}</span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">{clarification.message}</p>

              {clarification.options && clarification.options.length > 0 && (
                <div className="space-y-2">
                  <label className="text-xs font-medium">Quick Response:</label>
                  <div className="flex flex-wrap gap-2">
                    {clarification.options.map((option: string, optIndex: number) => (
                      <button
                        key={optIndex}
                        onClick={() => handleProvideClarification(clarification.id, option)}
                        className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                      >
                        {option}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <div className="mt-3">
                <input
                  type="text"
                  placeholder="Type your response..."
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const target = e.target as HTMLInputElement;
                      handleProvideClarification(clarification.id, target.value);
                      target.value = '';
                    }
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">No pending clarifications</div>
      )}
    </div>
  );

  const renderInsightsTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Learning Insights</h3>
        <button
          onClick={loadTabData}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : insights ? (
        <div className="space-y-4">
          {insights.patterns && insights.patterns.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Common Patterns</h4>
              <div className="space-y-2">
                {insights.patterns.slice(0, 5).map((pattern: any, index: number) => (
                  <div key={index} className="p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div className="text-sm font-medium">{pattern.pattern}</div>
                    <div className="text-xs text-gray-500">Success rate: {pattern.success_rate}%</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {insights.recommendations && insights.recommendations.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Recommendations</h4>
              <div className="space-y-2">
                {insights.recommendations.slice(0, 3).map((rec: any, index: number) => (
                  <div key={index} className="p-2 bg-blue-50 dark:bg-blue-900 rounded">
                    <div className="text-sm">{rec.recommendation}</div>
                    <div className="text-xs text-blue-600 dark:text-blue-400">Confidence: {rec.confidence}%</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">No insights available</div>
      )}
    </div>
  );

  const renderScheduleTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Schedule Deferred Task</h3>

      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium mb-1">Command</label>
          <textarea
            value={scheduleForm.command}
            onChange={(e) => setScheduleForm(prev => ({ ...prev, command: e.target.value }))}
            placeholder="Enter the command to schedule..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 resize-none"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-sm font-medium mb-1">Delay (minutes)</label>
            <input
              type="number"
              value={scheduleForm.delayMinutes}
              onChange={(e) => setScheduleForm(prev => ({ ...prev, delayMinutes: parseInt(e.target.value) || 0 }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800"
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Priority</label>
            <select
              value={scheduleForm.priority}
              onChange={(e) => setScheduleForm(prev => ({ ...prev, priority: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
        </div>

        <button
          onClick={handleScheduleTask}
          disabled={!scheduleForm.command.trim()}
          className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Schedule Task
        </button>
      </div>
    </div>
  );

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setActiveTab('queue')}
          className={`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'queue'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
        >
          <Clock className="w-4 h-4 mr-2" />
          Queue
        </button>
        <button
          onClick={() => setActiveTab('clarifications')}
          className={`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'clarifications'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
        >
          <MessageSquare className="w-4 h-4 mr-2" />
          Clarifications
        </button>
        <button
          onClick={() => setActiveTab('insights')}
          className={`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'insights'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
        >
          <Lightbulb className="w-4 h-4 mr-2" />
          Insights
        </button>
        <button
          onClick={() => setActiveTab('schedule')}
          className={`flex items-center px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'schedule'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
        >
          <Calendar className="w-4 h-4 mr-2" />
          Schedule
        </button>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'queue' && renderQueueTab()}
        {activeTab === 'clarifications' && renderClarificationsTab()}
        {activeTab === 'insights' && renderInsightsTab()}
        {activeTab === 'schedule' && renderScheduleTab()}
      </div>
    </div>
  );
};
