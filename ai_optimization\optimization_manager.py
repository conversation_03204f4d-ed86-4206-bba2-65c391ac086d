"""
AI Optimization Manager

High-level manager that coordinates all AI model optimization activities,
including performance monitoring, resource management, and automatic tuning.
"""

import asyncio
import json
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from ai_optimization.model_optimizer import (
    ModelOptimizer,
    ModelPerformanceMetrics,
    OptimizationConfig,
)
from ai_optimization.performance_monitor import PerformanceAlert, PerformanceMonitor

logger = logging.getLogger(__name__)


class AIOptimizationManager:
    """
    High-level AI optimization manager that coordinates all optimization activities.

    Features:
    - Unified optimization interface
    - Coordinated performance monitoring
    - Automatic optimization scheduling
    - Optimization reporting and analytics
    - Integration with existing AI services
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the AI optimization manager"""
        self.config = config
        self.optimizer = ModelOptimizer(
            OptimizationConfig(**config.get("optimization", {}))
        )
        self.monitor = PerformanceMonitor(config.get("monitoring", {}))

        # Manager state
        self.optimization_schedule: Dict[str, Any] = {}
        self.optimization_history: List[Dict[str, Any]] = []
        self.active_models: Dict[str, bool] = {}
        self.optimization_stats: Dict[str, Any] = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "total_performance_improvements": 0.0,
            "last_optimization": None,
        }

        # Threading
        self.manager_thread = None
        self.manager_active = False
        self.manager_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(
            max_workers=5, thread_name_prefix="AIOptimizer"
        )

        # Data storage
        self.data_dir = Path(config.get("data_dir", "data/ai_optimization"))
        self.data_dir.mkdir(parents=True, exist_ok=True)

        logger.info("AI Optimization Manager initialized")

    def start(self) -> None:
        """Start the optimization manager"""
        with self.manager_lock:
            if not self.manager_active:
                self.manager_active = True

                # Start sub-components
                self.optimizer.start_monitoring()
                self.monitor.start_monitoring()

                # Start manager thread
                self.manager_thread = threading.Thread(
                    target=self._manager_loop, daemon=True
                )
                self.manager_thread.start()

                logger.info("AI Optimization Manager started")

    def stop(self) -> None:
        """Stop the optimization manager"""
        with self.manager_lock:
            if self.manager_active:
                self.manager_active = False

                # Stop sub-components
                self.optimizer.stop_monitoring()
                self.monitor.stop_monitoring()

                # Wait for manager thread
                if self.manager_thread:
                    self.manager_thread.join()
                    self.manager_thread = None

                # Shutdown executor
                self.executor.shutdown(wait=True)

                # Save final state
                self.save_state()

                logger.info("AI Optimization Manager stopped")

    def register_model(self, model_name: str, model_config: Dict[str, Any]) -> None:
        """Register a model for optimization"""
        with self.manager_lock:
            self.active_models[model_name] = True
            self.optimizer.model_configs[model_name] = model_config

            # Set up optimization schedule
            self.optimization_schedule[model_name] = {
                "last_optimization": None,
                "next_optimization": datetime.now() + timedelta(hours=1),
                "optimization_interval": timedelta(
                    hours=self.config.get("default_optimization_interval", 6)
                ),
                "enabled": True,
            }

            logger.info(f"Registered model for optimization: {model_name}")

    def unregister_model(self, model_name: str) -> None:
        """Unregister a model from optimization"""
        with self.manager_lock:
            if model_name in self.active_models:
                del self.active_models[model_name]
                if model_name in self.optimizer.model_configs:
                    del self.optimizer.model_configs[model_name]
                if model_name in self.optimization_schedule:
                    del self.optimization_schedule[model_name]

                logger.info(f"Unregistered model from optimization: {model_name}")

    def record_model_performance(self, model_name: str, **performance_data) -> None:
        """Record performance data for a model"""
        # Convert success boolean to success_rate and error_rate
        if "success" in performance_data:
            success = performance_data.pop("success")
            performance_data["success_rate"] = 1.0 if success else 0.0
            performance_data["error_rate"] = 0.0 if success else 1.0

        # Record in optimizer
        metrics = ModelPerformanceMetrics(model_name=model_name, **performance_data)
        self.optimizer.add_performance_metrics(metrics)

        # Convert back to boolean success for monitor
        monitor_data = performance_data.copy()
        if "success_rate" in monitor_data:
            monitor_data["success"] = monitor_data.pop("success_rate") > 0.5
            if "error_rate" in monitor_data:
                monitor_data.pop("error_rate")

        # Record in monitor
        self.monitor.record_model_performance(model_name, **monitor_data)

    def optimize_model(self, model_name: str, force: bool = False) -> Dict[str, Any]:
        """Optimize a specific model"""
        with self.manager_lock:
            if model_name not in self.active_models:
                return {
                    "status": "error",
                    "message": f"Model {model_name} not registered",
                }

            # Check if optimization is needed
            if not force:
                schedule = self.optimization_schedule[model_name]
                if schedule["next_optimization"] > datetime.now():
                    return {"status": "skipped", "message": "Optimization not due yet"}

            # Perform optimization
            try:
                result = self.optimizer.optimize_model(model_name)

                # Update statistics
                self.optimization_stats["total_optimizations"] += 1
                if result["status"] == "completed":
                    self.optimization_stats["successful_optimizations"] += 1
                else:
                    self.optimization_stats["failed_optimizations"] += 1

                self.optimization_stats["last_optimization"] = (
                    datetime.now().isoformat()
                )

                # Update schedule
                self.optimization_schedule[model_name][
                    "last_optimization"
                ] = datetime.now()
                self.optimization_schedule[model_name]["next_optimization"] = (
                    datetime.now()
                    + self.optimization_schedule[model_name]["optimization_interval"]
                )

                # Record in history
                history_entry = {
                    "model_name": model_name,
                    "timestamp": datetime.now().isoformat(),
                    "result": result,
                    "force": force,
                }
                self.optimization_history.append(history_entry)

                logger.info(f"Optimization completed for {model_name}: {result}")
                return result

            except Exception as e:
                logger.error(f"Optimization failed for {model_name}: {e}")
                self.optimization_stats["failed_optimizations"] += 1
                return {"status": "error", "message": str(e)}

    def get_optimization_recommendations(self, model_name: str) -> List[Dict[str, Any]]:
        """Get optimization recommendations for a model"""
        return self.optimizer.get_optimization_recommendations(model_name)

    def get_model_performance_summary(
        self, model_name: str, hours: int = 24
    ) -> Optional[Dict[str, Any]]:
        """Get performance summary for a model"""
        return self.monitor.get_model_performance_summary(model_name, hours)

    def get_active_alerts(
        self, severity: Optional[str] = None
    ) -> List[PerformanceAlert]:
        """Get active performance alerts"""
        return self.monitor.get_active_alerts(severity)

    def resolve_alert(self, alert_id: str) -> bool:
        """Resolve a performance alert"""
        return self.monitor.resolve_alert(alert_id)

    def get_optimization_schedule(self) -> Dict[str, Any]:
        """Get optimization schedule for all models"""
        with self.manager_lock:
            return {
                model: {
                    "enabled": schedule["enabled"],
                    "last_optimization": (
                        schedule["last_optimization"].isoformat()
                        if schedule["last_optimization"]
                        else None
                    ),
                    "next_optimization": schedule["next_optimization"].isoformat(),
                    "interval_hours": schedule["optimization_interval"].total_seconds()
                    / 3600,
                }
                for model, schedule in self.optimization_schedule.items()
            }

    def update_optimization_schedule(
        self,
        model_name: str,
        enabled: Optional[bool] = None,
        interval_hours: Optional[float] = None,
    ) -> bool:
        """Update optimization schedule for a model"""
        with self.manager_lock:
            if model_name not in self.optimization_schedule:
                return False

            schedule = self.optimization_schedule[model_name]

            if enabled is not None:
                schedule["enabled"] = enabled

            if interval_hours is not None:
                schedule["optimization_interval"] = timedelta(hours=interval_hours)
                # Recalculate next optimization
                if schedule["last_optimization"]:
                    schedule["next_optimization"] = (
                        schedule["last_optimization"]
                        + schedule["optimization_interval"]
                    )

            logger.info(f"Updated optimization schedule for {model_name}")
            return True

    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get optimization statistics"""
        with self.manager_lock:
            stats = self.optimization_stats.copy()

            # Add current state
            stats.update(
                {
                    "active_models": len(self.active_models),
                    "total_alerts": len(self.monitor.alerts),
                    "active_alerts": len(self.monitor.get_active_alerts()),
                    "manager_active": self.manager_active,
                }
            )

            return stats

    def get_optimization_history(
        self, model_name: Optional[str] = None, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get optimization history"""
        with self.manager_lock:
            history = self.optimization_history

            if model_name:
                history = [
                    entry for entry in history if entry["model_name"] == model_name
                ]

            # Sort by timestamp (newest first) and limit
            history.sort(key=lambda x: x["timestamp"], reverse=True)
            return history[:limit]

    def _manager_loop(self) -> None:
        """Main manager loop"""
        while self.manager_active:
            try:
                with self.manager_lock:
                    # Check for scheduled optimizations
                    current_time = datetime.now()

                    # Create a copy to avoid issues with concurrent modification
                    schedule_copy = self.optimization_schedule.copy()

                    for model_name, schedule in schedule_copy.items():
                        if (
                            schedule["enabled"]
                            and schedule["next_optimization"] <= current_time
                        ):

                            # Perform optimization in a thread pool to avoid blocking the loop
                            self.executor.submit(self.optimize_model, model_name)

                # Save state periodically
                if current_time.minute % 10 == 0:  # Every 10 minutes
                    self.save_state()

                # Sleep for a short interval
                time.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error in manager loop: {e}")
                time.sleep(300)  # Wait 5 minutes on error

    async def _async_optimize_model(self, model_name: str) -> None:
        """Asynchronously optimize a model"""
        try:
            result = self.optimize_model(model_name)
            logger.info(f"Async optimization for {model_name}: {result['status']}")
        except Exception as e:
            logger.error(f"Async optimization failed for {model_name}: {e}")

    def save_state(self) -> None:
        """Save manager state to file"""
        with self.manager_lock:
            state = {
                "optimization_schedule": {
                    model: {
                        "enabled": schedule["enabled"],
                        "last_optimization": (
                            schedule["last_optimization"].isoformat()
                            if schedule["last_optimization"]
                            else None
                        ),
                        "next_optimization": schedule["next_optimization"].isoformat(),
                        "optimization_interval": schedule[
                            "optimization_interval"
                        ].total_seconds(),
                    }
                    for model, schedule in self.optimization_schedule.items()
                },
                "optimization_stats": self.optimization_stats,
                "optimization_history": self.optimization_history[
                    -1000:
                ],  # Keep last 1000 entries
                "active_models": list(self.active_models.keys()),
                "model_configs": self.optimizer.model_configs,
                "saved_at": datetime.now().isoformat(),
            }

            state_file = self.data_dir / "optimization_state.json"
            with open(state_file, "w") as f:
                json.dump(state, f, indent=2)

            # Save sub-component data
            self.optimizer.save_metrics(self.data_dir / "optimizer_metrics.json")
            self.monitor.save_monitoring_data(str(self.data_dir / "monitor_data.json"))

            logger.debug("Optimization manager state saved")

    def load_state(self) -> None:
        """Load manager state from file"""
        state_file = self.data_dir / "optimization_state.json"
        if not state_file.exists():
            return

        try:
            with open(state_file, "r") as f:
                state = json.load(f)

            # Load optimization schedule
            self.optimization_schedule.clear()
            for model, schedule_data in state.get("optimization_schedule", {}).items():
                self.optimization_schedule[model] = {
                    "enabled": schedule_data["enabled"],
                    "last_optimization": (
                        datetime.fromisoformat(schedule_data["last_optimization"])
                        if schedule_data["last_optimization"]
                        else None
                    ),
                    "next_optimization": datetime.fromisoformat(
                        schedule_data["next_optimization"]
                    ),
                    "optimization_interval": timedelta(
                        seconds=schedule_data["optimization_interval"]
                    ),
                }

            # Load other state
            self.optimization_stats = state.get(
                "optimization_stats", self.optimization_stats
            )
            self.optimization_history = state.get("optimization_history", [])

            # Load active models
            self.active_models.clear()
            for model in state.get("active_models", []):
                self.active_models[model] = model

            # Load model configs
            self.optimizer.model_configs = state.get("model_configs", {})

            # Load sub-component data
            self.optimizer.load_metrics(self.data_dir / "optimizer_metrics.json")
            self.monitor.load_monitoring_data(str(self.data_dir / "monitor_data.json"))

            logger.info("Optimization manager state loaded")

        except Exception as e:
            logger.error(f"Failed to load optimization state: {e}")

    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Get a comprehensive optimization report"""
        with self.manager_lock:
            report = {
                "summary": {
                    "active_models": len(self.active_models),
                    "total_optimizations": self.optimization_stats[
                        "total_optimizations"
                    ],
                    "success_rate": (
                        self.optimization_stats["successful_optimizations"]
                        / max(self.optimization_stats["total_optimizations"], 1)
                    ),
                    "active_alerts": len(self.monitor.get_active_alerts()),
                    "manager_status": "active" if self.manager_active else "inactive",
                },
                "model_performance": {},
                "recent_optimizations": self.optimization_history[-10:],
                "active_alerts": [
                    alert.to_dict() for alert in self.monitor.get_active_alerts()
                ],
                "optimization_schedule": self.get_optimization_schedule(),
                "resource_usage": self.monitor.get_resource_usage().to_dict(),
                "resource_trends": self.monitor.get_resource_trends(),
                "generated_at": datetime.now().isoformat(),
            }

            # Add performance summaries for each model
            for model_name in self.active_models:
                summary = self.get_model_performance_summary(model_name)
                if summary:
                    report["model_performance"][model_name] = summary

            return report

    def export_report(self, file_path: Path, format: str = "json") -> None:
        """Export optimization report to file"""
        report = self.get_comprehensive_report()

        if format.lower() == "json":
            with open(str(file_path), "w") as f:
                json.dump(report, f, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")

        logger.info(f"Optimization report exported to {file_path}")
