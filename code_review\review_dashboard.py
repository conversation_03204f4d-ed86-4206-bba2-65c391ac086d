"""
Review Dashboard

Dashboard and reporting system for code review analytics,
trends, and project-wide quality metrics.

Phase 20 Implementation - Advanced Code Review
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class ReviewMetrics:
    """Represents review metrics for a time period."""

    period: str
    total_reviews: int
    average_quality_score: float
    average_security_score: float
    average_performance_score: float
    total_issues: int
    critical_issues: int
    high_issues: int
    medium_issues: int
    low_issues: int


@dataclass
class ProjectMetrics:
    """Represents project-wide metrics."""

    project_id: str
    project_name: str
    total_files: int
    total_lines: int
    overall_quality_score: float
    overall_security_score: float
    overall_performance_score: float
    last_review_date: datetime
    trend: str  # "improving", "declining", "stable"


class ReviewDashboard:
    """
    Review dashboard for analytics and reporting.

    This class provides comprehensive analytics and reporting
    capabilities for code review data across projects and time periods.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the review dashboard.

        Args:
            config: Configuration dictionary for dashboard
        """
        self.config = config or {}
        self.review_history: List[Dict[str, Any]] = []
        self.project_data: Dict[str, Any] = {}

        logger.info("Review Dashboard initialized successfully")

    async def get_dashboard_data(self, project_id: str) -> Dict[str, Any]:
        """
        Get comprehensive dashboard data for a project.

        Args:
            project_id: Project identifier

        Returns:
            Dictionary with dashboard data
        """
        try:
            logger.info(f"Generating dashboard data for project: {project_id}")

            # Get project metrics
            project_metrics = await self._get_project_metrics(project_id)

            # Get review trends
            review_trends = await self._get_review_trends(project_id)

            # Get quality metrics
            quality_metrics = await self._get_quality_metrics(project_id)

            # Get security metrics
            security_metrics = await self._get_security_metrics(project_id)

            # Get performance metrics
            performance_metrics = await self._get_performance_metrics(project_id)

            # Get recent reviews
            recent_reviews = await self._get_recent_reviews(project_id)

            # Get top issues
            top_issues = await self._get_top_issues(project_id)

            # Get recommendations
            recommendations = await self._get_recommendations(project_id)

            return {
                "project_metrics": project_metrics,
                "review_trends": review_trends,
                "quality_metrics": quality_metrics,
                "security_metrics": security_metrics,
                "performance_metrics": performance_metrics,
                "recent_reviews": recent_reviews,
                "top_issues": top_issues,
                "recommendations": recommendations,
                "generated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error generating dashboard data: {e}")
            return {"error": str(e), "generated_at": datetime.now().isoformat()}

    async def add_review_result(
        self, project_id: str, review_data: Dict[str, Any]
    ) -> bool:
        """
        Add a review result to the dashboard history.

        Args:
            project_id: Project identifier
            review_data: Review result data

        Returns:
            True if successfully added, False otherwise
        """
        try:
            review_entry = {
                "project_id": project_id,
                "timestamp": datetime.now().isoformat(),
                "review_data": review_data,
            }

            self.review_history.append(review_entry)

            # Update project data
            if project_id not in self.project_data:
                self.project_data[project_id] = {"reviews": [], "metrics": {}}

            self.project_data[project_id]["reviews"].append(review_entry)

            logger.info(f"Added review result for project: {project_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding review result: {e}")
            return False

    async def get_project_summary(self, project_id: str) -> Dict[str, Any]:
        """
        Get a summary of project review data.

        Args:
            project_id: Project identifier

        Returns:
            Dictionary with project summary
        """
        try:
            project_reviews = [
                r for r in self.review_history if r["project_id"] == project_id
            ]

            if not project_reviews:
                return {
                    "project_id": project_id,
                    "total_reviews": 0,
                    "message": "No reviews found for this project",
                }

            # Calculate summary metrics
            total_reviews = len(project_reviews)
            quality_scores = [
                r["review_data"].get("quality_score", 0) for r in project_reviews
            ]
            security_scores = [
                r["review_data"].get("security_score", 0) for r in project_reviews
            ]
            performance_scores = [
                r["review_data"].get("performance_score", 0) for r in project_reviews
            ]

            avg_quality = (
                sum(quality_scores) / len(quality_scores) if quality_scores else 0
            )
            avg_security = (
                sum(security_scores) / len(security_scores) if security_scores else 0
            )
            avg_performance = (
                sum(performance_scores) / len(performance_scores)
                if performance_scores
                else 0
            )

            return {
                "project_id": project_id,
                "total_reviews": total_reviews,
                "average_quality_score": avg_quality,
                "average_security_score": avg_security,
                "average_performance_score": avg_performance,
                "last_review": (
                    project_reviews[-1]["timestamp"] if project_reviews else None
                ),
            }

        except Exception as e:
            logger.error(f"Error getting project summary: {e}")
            return {"error": str(e)}

    async def get_trend_analysis(
        self, project_id: str, days: int = 30
    ) -> Dict[str, Any]:
        """
        Get trend analysis for a project over a specified period.

        Args:
            project_id: Project identifier
            days: Number of days to analyze

        Returns:
            Dictionary with trend analysis
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            project_reviews = [
                r
                for r in self.review_history
                if r["project_id"] == project_id
                and datetime.fromisoformat(r["timestamp"]) >= cutoff_date
            ]

            if not project_reviews:
                return {
                    "project_id": project_id,
                    "period_days": days,
                    "message": "No reviews found in the specified period",
                }

            # Group reviews by week
            weekly_data: Dict[str, Any] = {}
            for review in project_reviews:
                review_date = datetime.fromisoformat(review["timestamp"])
                week_key = review_date.strftime("%Y-%W")

                if week_key not in weekly_data:
                    weekly_data[week_key] = []
                weekly_data[week_key].append(review)

            # Calculate weekly averages
            weekly_metrics = []
            for week, reviews in weekly_data.items():
                quality_scores = [
                    r["review_data"].get("quality_score", 0) for r in reviews
                ]
                security_scores = [
                    r["review_data"].get("security_score", 0) for r in reviews
                ]
                performance_scores = [
                    r["review_data"].get("performance_score", 0) for r in reviews
                ]

                weekly_metrics.append(
                    {
                        "week": week,
                        "reviews_count": len(reviews),
                        "avg_quality": sum(quality_scores) / len(quality_scores),
                        "avg_security": sum(security_scores) / len(security_scores),
                        "avg_performance": sum(performance_scores)
                        / len(performance_scores),
                    }
                )

            # Determine trend
            if len(weekly_metrics) >= 2:
                recent_avg = weekly_metrics[-1]["avg_quality"]
                previous_avg = weekly_metrics[-2]["avg_quality"]

                if recent_avg > previous_avg + 0.05:
                    trend = "improving"
                elif recent_avg < previous_avg - 0.05:
                    trend = "declining"
                else:
                    trend = "stable"
            else:
                trend = "insufficient_data"

            return {
                "project_id": project_id,
                "period_days": days,
                "total_reviews": len(project_reviews),
                "weekly_metrics": weekly_metrics,
                "trend": trend,
            }

        except Exception as e:
            logger.error(f"Error getting trend analysis: {e}")
            return {"error": str(e)}

    async def _get_project_metrics(self, project_id: str) -> Dict[str, Any]:
        """Get project-wide metrics."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]

        if not project_reviews:
            return {
                "project_id": project_id,
                "total_files": 0,
                "total_lines": 0,
                "overall_quality_score": 0.0,
                "overall_security_score": 0.0,
                "overall_performance_score": 0.0,
                "last_review_date": None,
                "trend": "no_data",
            }

        # Calculate metrics
        quality_scores = [
            r["review_data"].get("quality_score", 0) for r in project_reviews
        ]
        security_scores = [
            r["review_data"].get("security_score", 0) for r in project_reviews
        ]
        performance_scores = [
            r["review_data"].get("performance_score", 0) for r in project_reviews
        ]

        return {
            "project_id": project_id,
            "total_files": len(project_reviews),
            "total_lines": sum(
                r["review_data"].get("total_lines", 0) for r in project_reviews
            ),
            "overall_quality_score": (
                sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            ),
            "overall_security_score": (
                sum(security_scores) / len(security_scores) if security_scores else 0.0
            ),
            "overall_performance_score": (
                sum(performance_scores) / len(performance_scores)
                if performance_scores
                else 0.0
            ),
            "last_review_date": (
                project_reviews[-1]["timestamp"] if project_reviews else None
            ),
            "trend": "stable",  # Would be calculated from trend analysis
        }

    async def _get_review_trends(self, project_id: str) -> Dict[str, Any]:
        """Get review trends over time."""
        return await self.get_trend_analysis(project_id, 30)

    async def _get_quality_metrics(self, project_id: str) -> Dict[str, Any]:
        """Get quality-specific metrics."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]

        if not project_reviews:
            return {"message": "No quality data available"}

        quality_scores = [
            r["review_data"].get("quality_score", 0) for r in project_reviews
        ]
        quality_issues = []

        for review in project_reviews:
            issues = review["review_data"].get("issues", [])
            quality_issues.extend([i for i in issues if i.get("type") == "quality"])

        return {
            "average_score": (
                sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            ),
            "total_issues": len(quality_issues),
            "critical_issues": len(
                [i for i in quality_issues if i.get("severity") == "critical"]
            ),
            "high_issues": len(
                [i for i in quality_issues if i.get("severity") == "high"]
            ),
            "medium_issues": len(
                [i for i in quality_issues if i.get("severity") == "medium"]
            ),
            "low_issues": len(
                [i for i in quality_issues if i.get("severity") == "low"]
            ),
        }

    async def _get_security_metrics(self, project_id: str) -> Dict[str, Any]:
        """Get security-specific metrics."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]

        if not project_reviews:
            return {"message": "No security data available"}

        security_scores = [
            r["review_data"].get("security_score", 0) for r in project_reviews
        ]
        security_issues = []

        for review in project_reviews:
            issues = review["review_data"].get("issues", [])
            security_issues.extend([i for i in issues if i.get("type") == "security"])

        return {
            "average_score": (
                sum(security_scores) / len(security_scores) if security_scores else 0.0
            ),
            "total_vulnerabilities": len(security_issues),
            "critical_vulnerabilities": len(
                [i for i in security_issues if i.get("severity") == "critical"]
            ),
            "high_vulnerabilities": len(
                [i for i in security_issues if i.get("severity") == "high"]
            ),
            "medium_vulnerabilities": len(
                [i for i in security_issues if i.get("severity") == "medium"]
            ),
            "low_vulnerabilities": len(
                [i for i in security_issues if i.get("severity") == "low"]
            ),
        }

    async def _get_performance_metrics(self, project_id: str) -> Dict[str, Any]:
        """Get performance-specific metrics."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]

        if not project_reviews:
            return {"message": "No performance data available"}

        performance_scores = [
            r["review_data"].get("performance_score", 0) for r in project_reviews
        ]
        performance_issues = []

        for review in project_reviews:
            issues = review["review_data"].get("issues", [])
            performance_issues.extend(
                [i for i in issues if i.get("type") == "performance"]
            )

        return {
            "average_score": (
                sum(performance_scores) / len(performance_scores)
                if performance_scores
                else 0.0
            ),
            "total_bottlenecks": len(performance_issues),
            "critical_bottlenecks": len(
                [i for i in performance_issues if i.get("severity") == "critical"]
            ),
            "high_bottlenecks": len(
                [i for i in performance_issues if i.get("severity") == "high"]
            ),
            "medium_bottlenecks": len(
                [i for i in performance_issues if i.get("severity") == "medium"]
            ),
            "low_bottlenecks": len(
                [i for i in performance_issues if i.get("severity") == "low"]
            ),
        }

    async def _get_recent_reviews(
        self, project_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get recent reviews for the project."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]
        project_reviews.sort(key=lambda x: x["timestamp"], reverse=True)

        recent_reviews = []
        for review in project_reviews[:limit]:
            recent_reviews.append(
                {
                    "timestamp": review["timestamp"],
                    "quality_score": review["review_data"].get("quality_score", 0),
                    "security_score": review["review_data"].get("security_score", 0),
                    "performance_score": review["review_data"].get(
                        "performance_score", 0
                    ),
                    "total_issues": len(review["review_data"].get("issues", [])),
                    "file_path": review["review_data"].get("file_path", "unknown"),
                }
            )

        return recent_reviews

    async def _get_top_issues(
        self, project_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get top issues across the project."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]

        issue_counts = {}
        for review in project_reviews:
            issues = review["review_data"].get("issues", [])
            for issue in issues:
                issue_type = issue.get("type", "unknown")
                if issue_type not in issue_counts:
                    issue_counts[issue_type] = 0
                issue_counts[issue_type] += 1

        # Sort by count and return top issues
        sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)

        top_issues = []
        for issue_type, count in sorted_issues[:limit]:
            top_issues.append(
                {
                    "type": issue_type,
                    "count": count,
                    "percentage": (
                        (count / sum(issue_counts.values())) * 100
                        if issue_counts
                        else 0
                    ),
                }
            )

        return top_issues

    async def _get_recommendations(self, project_id: str) -> List[str]:
        """Get recommendations based on project data."""
        project_reviews = [
            r for r in self.review_history if r["project_id"] == project_id
        ]

        if not project_reviews:
            return ["Start conducting regular code reviews to improve code quality"]

        recommendations = []

        # Analyze quality scores
        quality_scores = [
            r["review_data"].get("quality_score", 0) for r in project_reviews
        ]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0

        if avg_quality < 0.7:
            recommendations.append(
                "Focus on improving code quality through better documentation and testing"
            )

        # Analyze security scores
        security_scores = [
            r["review_data"].get("security_score", 0) for r in project_reviews
        ]
        avg_security = (
            sum(security_scores) / len(security_scores) if security_scores else 0
        )

        if avg_security < 0.8:
            recommendations.append(
                "Implement security best practices and conduct security training"
            )

        # Analyze performance scores
        performance_scores = [
            r["review_data"].get("performance_score", 0) for r in project_reviews
        ]
        avg_performance = (
            sum(performance_scores) / len(performance_scores)
            if performance_scores
            else 0
        )

        if avg_performance < 0.7:
            recommendations.append(
                "Focus on performance optimization and code efficiency"
            )

        # General recommendations
        if len(project_reviews) < 5:
            recommendations.append("Increase the frequency of code reviews")

        recommendations.append("Consider implementing automated code quality checks")
        recommendations.append("Establish coding standards and best practices")

        return recommendations[:5]  # Limit to top 5 recommendations

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the review dashboard."""
        return {
            "status": "healthy",
            "total_reviews": len(self.review_history),
            "total_projects": len(set(r["project_id"] for r in self.review_history)),
            "features": [
                "project_metrics",
                "trend_analysis",
                "quality_metrics",
                "security_metrics",
                "performance_metrics",
                "recent_reviews",
                "top_issues",
                "recommendations",
            ],
        }
