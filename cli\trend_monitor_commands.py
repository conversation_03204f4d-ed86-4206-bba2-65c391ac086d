#!/usr/bin/env python3
"""
Trend Monitor CLI Commands

Provides command-line interface for managing the Trend Monitor container
and interacting with the Trend Monitor service.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional

import click

from trend_monitoring.trend_monitor_container_manager import (
    TrendMonitorContainerManager,
)

logger = logging.getLogger(__name__)


@click.group()
def trend_monitor():
    """Trend Monitor container management commands"""
    pass


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def create(base_dir: str, containers_dir: str):
    """Create the Trend Monitor container"""

    async def _create():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.create_trend_monitor_container()

        if result["success"]:
            click.echo(f"✅ Trend Monitor container created successfully")
            click.echo(f"   Container: {result['container']['container_name']}")
            click.echo(f"   Port: {result['container']['port']}")
            click.echo(f"   Status: {result['container']['status']}")
        else:
            click.echo(
                f"❌ Failed to create Trend Monitor container: {result['error']}"
            )
            return 1

    asyncio.run(_create())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def start(base_dir: str, containers_dir: str):
    """Start the Trend Monitor container"""

    async def _start():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.start_trend_monitor_container()

        if result["success"]:
            click.echo(f"✅ Trend Monitor container started successfully")
            click.echo(f"   URL: {result['url']}")
            click.echo(f"   Status: {result['container']['status']}")
        else:
            click.echo(f"❌ Failed to start Trend Monitor container: {result['error']}")
            return 1

    asyncio.run(_start())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def stop(base_dir: str, containers_dir: str):
    """Stop the Trend Monitor container"""

    async def _stop():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.stop_trend_monitor_container()

        if result["success"]:
            click.echo(f"✅ Trend Monitor container stopped successfully")
        else:
            click.echo(f"❌ Failed to stop Trend Monitor container: {result['error']}")
            return 1

    asyncio.run(_stop())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def restart(base_dir: str, containers_dir: str):
    """Restart the Trend Monitor container"""

    async def _restart():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.restart_trend_monitor_container()

        if result["success"]:
            click.echo(f"✅ Trend Monitor container restarted successfully")
            click.echo(f"   URL: {result['url']}")
        else:
            click.echo(
                f"❌ Failed to restart Trend Monitor container: {result['error']}"
            )
            return 1

    asyncio.run(_restart())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def status(base_dir: str, containers_dir: str):
    """Get the status of the Trend Monitor container"""

    async def _status():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.get_trend_monitor_status()

        if result["success"]:
            container = result["container"]
            click.echo(f"📊 Trend Monitor Container Status")
            click.echo(f"   Container: {container['container_name']}")
            click.echo(f"   Status: {container['status']}")
            click.echo(f"   Port: {container['port']}")
            click.echo(f"   Created: {container['created_at']}")
            if container["last_started"]:
                click.echo(f"   Last Started: {container['last_started']}")
            if container["last_health_check"]:
                click.echo(f"   Last Health Check: {container['last_health_check']}")
            click.echo(f"   Health Status: {container['health_status']}")
        else:
            click.echo(f"❌ Failed to get Trend Monitor status: {result['error']}")
            return 1

    asyncio.run(_status())


@trend_monitor.command()
@click.option("--lines", default=100, help="Number of log lines to show")
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def logs(lines: int, base_dir: str, containers_dir: str):
    """Get Trend Monitor container logs"""

    async def _logs():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.get_trend_monitor_logs(lines)

        if result["success"]:
            click.echo(
                f"📋 Trend Monitor Container Logs (last {result['total_lines']} lines)"
            )
            click.echo("=" * 80)
            for log_line in result["logs"]:
                if log_line.strip():
                    click.echo(log_line)
        else:
            click.echo(f"❌ Failed to get Trend Monitor logs: {result['error']}")
            return 1

    asyncio.run(_logs())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def health(base_dir: str, containers_dir: str):
    """Perform a health check on the Trend Monitor container"""

    async def _health():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.health_check()

        if result["success"]:
            click.echo(f"✅ Trend Monitor container is healthy")
            if "health" in result:
                health_data = result["health"]
                click.echo(f"   Status: {health_data.get('status', 'unknown')}")
                click.echo(f"   Version: {health_data.get('version', 'unknown')}")
                click.echo(f"   Uptime: {health_data.get('uptime', 0):.2f} seconds")
                click.echo(
                    f"   Monitoring Active: {health_data.get('monitoring_active', False)}"
                )
                click.echo(f"   Trends Count: {health_data.get('trends_count', 0)}")
                click.echo(f"   Insights Count: {health_data.get('insights_count', 0)}")
        else:
            click.echo(
                f"❌ Trend Monitor container health check failed: {result['error']}"
            )
            return 1

    asyncio.run(_health())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def metrics(base_dir: str, containers_dir: str):
    """Get Trend Monitor metrics and statistics"""

    async def _metrics():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.get_trend_monitor_metrics()

        if result["success"]:
            metrics = result["metrics"]
            click.echo(f"📊 Trend Monitor Metrics")
            click.echo(f"   Container Status: {metrics['container_status']}")
            click.echo(f"   Health Status: {metrics['health_status']}")
            if metrics["uptime_seconds"]:
                click.echo(f"   Uptime: {metrics['uptime_seconds']:.2f} seconds")
            click.echo(f"   Port: {metrics['port']}")
            click.echo(f"   Data Directory: {metrics['data_dir']}")
            click.echo(f"   Update Interval: {metrics['update_interval']} seconds")
            click.echo(f"   Analysis Window: {metrics['analysis_window']} days")
            click.echo(f"   Monitoring Enabled: {metrics['monitoring_enabled']}")

            if metrics["resource_usage"]:
                click.echo(
                    f"   Resource Usage: {json.dumps(metrics['resource_usage'], indent=2)}"
                )
        else:
            click.echo(f"❌ Failed to get Trend Monitor metrics: {result['error']}")
            return 1

    asyncio.run(_metrics())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
@click.confirmation_option(
    prompt="Are you sure you want to delete the Trend Monitor container?"
)
def delete(base_dir: str, containers_dir: str):
    """Delete the Trend Monitor container and its resources"""

    async def _delete():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)
        result = await manager.delete_trend_monitor_container()

        if result["success"]:
            click.echo(f"✅ Trend Monitor container deleted successfully")
        else:
            click.echo(
                f"❌ Failed to delete Trend Monitor container: {result['error']}"
            )
            return 1

    asyncio.run(_delete())


@trend_monitor.command()
@click.option("--base-dir", default=".", help="Base directory for the project")
@click.option("--containers-dir", default="containers", help="Containers directory")
def info(base_dir: str, containers_dir: str):
    """Show detailed information about the Trend Monitor container"""

    async def _info():
        manager = TrendMonitorContainerManager(base_dir, containers_dir)

        # Get status
        status_result = await manager.get_trend_monitor_status()
        if not status_result["success"]:
            click.echo(f"❌ Failed to get container status: {status_result['error']}")
            return 1

        # Get health
        health_result = await manager.health_check()

        # Get metrics
        metrics_result = await manager.get_trend_monitor_metrics()

        click.echo(f"🔍 Trend Monitor Container Information")
        click.echo("=" * 80)

        if status_result["success"]:
            container = status_result["container"]
            click.echo(f"Container Name: {container['container_name']}")
            click.echo(f"Image Name: {container['image_name']}")
            click.echo(f"Port: {container['port']}")
            click.echo(f"Status: {container['status']}")
            click.echo(f"Created: {container['created_at']}")
            if container["last_started"]:
                click.echo(f"Last Started: {container['last_started']}")
            if container["last_health_check"]:
                click.echo(f"Last Health Check: {container['last_health_check']}")
            click.echo(f"Health Status: {container['health_status']}")

        click.echo()
        click.echo("Configuration:")
        if metrics_result["success"]:
            metrics = metrics_result["metrics"]
            click.echo(f"  Data Directory: {metrics['data_dir']}")
            click.echo(f"  Update Interval: {metrics['update_interval']} seconds")
            click.echo(f"  Analysis Window: {metrics['analysis_window']} days")
            click.echo(f"  Monitoring Enabled: {metrics['monitoring_enabled']}")

        click.echo()
        click.echo("Health Check:")
        if health_result["success"]:
            click.echo(f"  Status: ✅ Healthy")
            if "health" in health_result:
                health_data = health_result["health"]
                click.echo(f"  Version: {health_data.get('version', 'unknown')}")
                click.echo(f"  Uptime: {health_data.get('uptime', 0):.2f} seconds")
                click.echo(
                    f"  Monitoring Active: {health_data.get('monitoring_active', False)}"
                )
                click.echo(f"  Trends Count: {health_data.get('trends_count', 0)}")
                click.echo(f"  Insights Count: {health_data.get('insights_count', 0)}")
        else:
            click.echo(f"  Status: ❌ Unhealthy - {health_result['error']}")

        click.echo()
        click.echo("API Endpoints:")
        click.echo(f"  Health Check: http://localhost:{container['port']}/health")
        click.echo(f"  API Documentation: http://localhost:{container['port']}/docs")
        click.echo(f"  Trends API: http://localhost:{container['port']}/api/v1/trends")
        click.echo(
            f"  Insights API: http://localhost:{container['port']}/api/v1/insights"
        )
        click.echo(
            f"  Predictions API: http://localhost:{container['port']}/api/v1/predictions"
        )
        click.echo(
            f"  Summary API: http://localhost:{container['port']}/api/v1/summary"
        )

    asyncio.run(_info())


if __name__ == "__main__":
    trend_monitor()
