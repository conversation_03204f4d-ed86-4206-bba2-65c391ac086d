{"last_check": "2025-08-03T14:20:13.997490", "violations": [], "compliance_score": 100.0, "rule_checks": {"file_organization": {"status": "passed", "violations": [], "warnings": []}, "todo_completion": {"status": "passed", "violations": [], "warnings": [], "incomplete_todos": []}, "test_success": {"status": "passed", "violations": [], "warnings": ["Test execution timed out"], "test_results": {}}, "dependency_management": {"status": "passed", "violations": [], "warnings": []}, "cli_api_compliance": {"status": "passed", "violations": [], "warnings": []}, "ai_model_compliance": {"status": "passed", "violations": [], "warnings": ["Cloud model usage detected in learning\\best_practices_learner.py (future mode will allow this)", "Cloud model usage detected in content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\evaluator.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\pipeline.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\trainer.py (future mode will allow this)", "Cloud model usage detected in scripts\\test_fine_tuning.py (future mode will allow this)"], "current_mode": "local_only"}, "git_workflow": {"status": "passed", "violations": [], "warnings": ["Repository should be in G:\\AICodingAgent, found in F:/NasShare/AICodingAgent", "There are uncommitted changes"]}, "security_compliance": {"status": "passed", "violations": [], "warnings": ["Potential hardcoded secret in scripts\\demo_phase_20.py", "Potential hardcoded secret in scripts\\demo_phase_20.py", "Potential hardcoded secret in scripts\\phase_16_security_implementation.py", "Potential hardcoded secret in scripts\\test_automated_security_system.py", "Potential hardcoded secret in scripts\\test_automated_security_system.py", "Potential hardcoded secret in scripts\\test_llm_security_agent.py", "Potential hardcoded secret in tests\\test_advanced_code_reviewer.py", "Potential hardcoded secret in tests\\test_advanced_code_reviewer.py", "Potential hardcoded secret in tests\\test_error_detection.py", "Potential hardcoded secret in tests\\test_error_detection.py", "Potential hardcoded secret in tests\\test_vulnerability.py", "Potential hardcoded secret in tests\\test_vulnerability.py"]}, "file_cleanup": {"status": "passed", "violations": [], "warnings": [], "duplicates": 0, "obsolete_files": 0}, "mock_data_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 298 potential mock data files"], "mock_files": 298}, "virtual_environment": {"status": "passed", "violations": [], "warnings": []}}, "warnings": []}