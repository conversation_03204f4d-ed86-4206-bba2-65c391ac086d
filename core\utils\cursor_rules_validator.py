#!/usr/bin/env python3
"""
Cursor Rules Validator
Provides functions for validating cursor rules loading and compliance
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


def assert_rules_loaded() -> bool:
    """
    Assert that cursor rules are properly loaded and accessible.

    Returns:
        True if rules are loaded successfully

    Raises:
        AssertionError: If rules are not loaded or accessible
    """
    try:
        # Check for cursor rules file in common locations
        project_root = Path(__file__).parent.parent.parent
        rules_paths = [
            project_root / "config" / "cursorrules.md",
            project_root / ".cursor" / "rules" / "cursorrules.md",
            project_root / "docs" / "cursorrules.md",
            project_root / "cursorrules.md",
        ]

        rules_found = False
        rules_content = ""

        for rules_path in rules_paths:
            if rules_path.exists():
                with open(rules_path, "r", encoding="utf-8") as f:
                    rules_content = f.read()
                rules_found = True
                logger.info(f"Cursor rules found at: {rules_path}")
                break

        if not rules_found:
            raise AssertionError("Cursor rules file not found in any expected location")

        if not rules_content.strip():
            raise AssertionError("Cursor rules file is empty")

        # Validate basic structure
        if not _validate_rules_structure(rules_content):
            raise AssertionError("Cursor rules file does not have expected structure")

        logger.info("✅ Cursor rules are properly loaded and accessible")
        return True

    except Exception as e:
        logger.error(f"❌ Cursor rules validation failed: {e}")
        raise AssertionError(f"Cursor rules validation failed: {e}")


def _validate_rules_structure(content: str) -> bool:
    """
    Validate that cursor rules content has the expected structure.

    Args:
        content: Rules content to validate

    Returns:
        True if structure is valid
    """
    required_sections = [
        "CORE NON-NEGOTIABLES",
        "BEFORE ANY CODE CHANGE",
        "AFTER ANY CODE CHANGE",
    ]

    content_lower = content.lower()

    for section in required_sections:
        if section.lower() not in content_lower:
            logger.warning(f"Required section not found: {section}")
            return False

    return True


def validate_cursor_agent_integration() -> Dict[str, Any]:
    """
    Validate that CursorAgent is properly integrated with cursor rules.

    Returns:
        Dictionary containing validation results
    """
    try:
        # Import CursorAgent
        from core.agents.cursor_agent import CursorAgent

        # Create agent instance
        agent = CursorAgent()

        # Check if rules are loaded
        rules_status = agent.get_rules_status()

        if not rules_status.get("rules_loaded", False):
            raise AssertionError("CursorAgent does not have rules loaded")

        # Check if system prompt includes rules
        system_prompt = agent.system_prompt
        if "CURSOR RULES" not in system_prompt:
            raise AssertionError("System prompt does not include cursor rules")

        logger.info("✅ CursorAgent integration validation successful")
        return {
            "success": True,
            "rules_loaded": True,
            "system_prompt_includes_rules": True,
            "agent_initialized": True,
        }

    except Exception as e:
        logger.error(f"❌ CursorAgent integration validation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "rules_loaded": False,
            "system_prompt_includes_rules": False,
            "agent_initialized": False,
        }


def validate_cleanup_rules() -> Dict[str, Any]:
    """
    Validate that cleanup rules are being followed.

    Returns:
        Dictionary containing cleanup validation results
    """
    try:
        # Import FileCleanupManager
        from core.utils.file_cleanup_manager import FileCleanupManager

        # Create cleanup manager
        manager = FileCleanupManager()

        # Validate cleanup rules
        validation_results = manager.validate_cleanup_rules()

        # Check if any rules are violated
        violations = [rule for rule, passed in validation_results.items() if not passed]

        if violations:
            logger.warning(f"⚠️ Cleanup rule violations found: {violations}")
            return {
                "success": False,
                "violations": violations,
                "validation_results": validation_results,
            }
        else:
            logger.info("✅ All cleanup rules are being followed")
            return {
                "success": True,
                "violations": [],
                "validation_results": validation_results,
            }

    except Exception as e:
        logger.error(f"❌ Cleanup rules validation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "violations": [],
            "validation_results": {},
        }


def check_rules_compliance(task_description: str) -> Dict[str, Any]:
    """
    Check if a task description complies with cursor rules.

    Args:
        task_description: Description of the task to check

    Returns:
        Dictionary containing compliance status and issues
    """
    issues = []
    compliant = True

    # Check for TODO-related tasks
    if "todo" in task_description.lower():
        if (
            "complete" not in task_description.lower()
            and "finish" not in task_description.lower()
        ):
            issues.append("TODO tasks should include completion criteria")
            compliant = False

    # Check for testing requirements
    if any(
        keyword in task_description.lower()
        for keyword in ["code", "feature", "function", "class"]
    ):
        if (
            "test" not in task_description.lower()
            and "testing" not in task_description.lower()
        ):
            issues.append("Code changes should include testing considerations")

    # Check for Docker requirements
    if any(
        keyword in task_description.lower()
        for keyword in ["website", "service", "deploy", "container"]
    ):
        if (
            "docker" not in task_description.lower()
            and "container" not in task_description.lower()
        ):
            issues.append(
                "Website/service tasks should consider Docker containerization"
            )

    # Check for cleanup requirements
    if any(
        keyword in task_description.lower()
        for keyword in ["file", "cleanup", "delete", "remove"]
    ):
        if (
            "duplicate" not in task_description.lower()
            and "obsolete" not in task_description.lower()
        ):
            issues.append(
                "File operations should consider cleanup and duplication prevention"
            )

    return {
        "compliant": compliant,
        "issues": issues,
        "task_description": task_description,
    }


def generate_compliance_report() -> Dict[str, Any]:
    """
    Generate a comprehensive compliance report for cursor rules.

    Returns:
        Dictionary containing compliance report
    """
    try:
        # Check rules loading
        rules_loaded = assert_rules_loaded()

        # Check agent integration
        agent_integration = validate_cursor_agent_integration()

        # Check cleanup rules
        cleanup_validation = validate_cleanup_rules()

        # Check file structure
        project_root = Path(__file__).parent.parent.parent
        rules_file_exists = (project_root / "config" / "cursorrules.md").exists()

        # Overall compliance
        overall_compliance = (
            rules_loaded
            and agent_integration["success"]
            and cleanup_validation["success"]
        )

        return {
            "timestamp": "2025-01-27T12:00:00Z",
            "overall_compliance": overall_compliance,
            "rules_loading": {
                "status": "✅ PASS" if rules_loaded else "❌ FAIL",
                "details": "Cursor rules are properly loaded and accessible",
            },
            "agent_integration": {
                "status": "✅ PASS" if agent_integration["success"] else "❌ FAIL",
                "details": agent_integration,
            },
            "cleanup_rules": {
                "status": "✅ PASS" if cleanup_validation["success"] else "❌ FAIL",
                "details": cleanup_validation,
            },
            "file_structure": {
                "status": "✅ PASS" if rules_file_exists else "❌ FAIL",
                "details": "Cursor rules file exists in expected location",
            },
            "recommendations": generate_recommendations(
                rules_loaded,
                agent_integration["success"],
                cleanup_validation["success"],
            ),
        }

    except Exception as e:
        logger.error(f"Failed to generate compliance report: {e}")
        return {
            "timestamp": "2025-01-27T12:00:00Z",
            "overall_compliance": False,
            "error": str(e),
            "recommendations": [
                "Fix cursor rules loading issues",
                "Check agent integration",
                "Validate cleanup rules",
                "Check file structure",
            ],
        }


def generate_recommendations(
    rules_loaded: bool, agent_integration: bool, cleanup_rules: bool
) -> List[str]:
    """
    Generate recommendations based on compliance status.

    Args:
        rules_loaded: Whether cursor rules are loaded
        agent_integration: Whether agent integration is working
        cleanup_rules: Whether cleanup rules are being followed

    Returns:
        List of recommendations
    """
    recommendations = []

    if not rules_loaded:
        recommendations.append("Fix cursor rules loading issues")

    if not agent_integration:
        recommendations.append("Check CursorAgent integration")

    if not cleanup_rules:
        recommendations.append("Review and fix cleanup rule violations")

    if rules_loaded and agent_integration and cleanup_rules:
        recommendations.append(
            "All systems are working correctly - maintain current practices"
        )

    return recommendations
