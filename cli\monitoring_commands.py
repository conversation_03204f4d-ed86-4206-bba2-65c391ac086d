#!/usr/bin/env python3
"""
Monitoring Agent CLI Commands
Provides command-line interface for managing the monitoring agent with PID file functionality.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Any, Dict, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from monitoring.monitoring_agent import MonitoringAgent
except ImportError as e:
    print(f"❌ Failed to import MonitoringAgent: {e}")
    sys.exit(1)

logger = logging.getLogger(__name__)


class MonitoringCommands:
    """CLI commands for monitoring agent management"""

    def __init__(self, agent=None):
        self.agent = agent
        self.monitoring_agent = MonitoringAgent()

    async def start_monitoring(self, **kwargs) -> Dict[str, Any]:
        """Start the monitoring agent with PID file management"""
        try:
            logger.info("🚀 Starting monitoring agent...")

            # Check if already running
            status = self.monitoring_agent.get_status()
            if status["status"] == "running":
                return {
                    "success": True,
                    "message": f"Monitoring already running (PID {status['pid']})",
                    "pid": status["pid"],
                    "status": status,
                }

            # Start monitoring
            await self.monitoring_agent.start()

            if self.monitoring_agent.is_running:
                return {
                    "success": True,
                    "message": f"Monitoring started successfully (PID {self.monitoring_agent.pid})",
                    "pid": self.monitoring_agent.pid,
                    "status": self.monitoring_agent.get_status(),
                }
            else:
                return {"success": False, "error": "Failed to start monitoring agent"}

        except Exception as e:
            logger.error(f"Error starting monitoring agent: {e}")
            return {"success": False, "error": str(e)}

    async def stop_monitoring(self, **kwargs) -> Dict[str, Any]:
        """Stop the monitoring agent"""
        try:
            logger.info("🛑 Stopping monitoring agent...")

            if not self.monitoring_agent.is_running:
                return {"success": True, "message": "Monitoring agent is not running"}

            await self.monitoring_agent.stop()

            return {"success": True, "message": "Monitoring agent stopped successfully"}

        except Exception as e:
            logger.error(f"Error stopping monitoring agent: {e}")
            return {"success": False, "error": str(e)}

    async def get_status(self, **kwargs) -> Dict[str, Any]:
        """Get monitoring agent status with PID file recovery"""
        try:
            status = self.monitoring_agent.get_status()

            return {"success": True, "status": status, "message": status["message"]}

        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {"success": False, "error": str(e)}

    async def restart_monitoring(self, **kwargs) -> Dict[str, Any]:
        """Restart the monitoring agent"""
        try:
            logger.info("🔄 Restarting monitoring agent...")

            # Stop if running
            if self.monitoring_agent.is_running:
                await self.monitoring_agent.stop()

            # Start again
            await self.monitoring_agent.start()

            if self.monitoring_agent.is_running:
                return {
                    "success": True,
                    "message": f"Monitoring agent restarted successfully (PID {self.monitoring_agent.pid})",
                    "pid": self.monitoring_agent.pid,
                    "status": self.monitoring_agent.get_status(),
                }
            else:
                return {"success": False, "error": "Failed to restart monitoring agent"}

        except Exception as e:
            logger.error(f"Error restarting monitoring agent: {e}")
            return {"success": False, "error": str(e)}

    async def check_health(self, **kwargs) -> Dict[str, Any]:
        """Check monitoring agent health"""
        try:
            status = self.monitoring_agent.get_status()

            if status["status"] == "running":
                # Get current health metrics
                try:
                    health = await self.monitoring_agent.get_current_health()
                    return {
                        "success": True,
                        "status": "healthy",
                        "message": "Monitoring agent is healthy",
                        "health": health.dict() if hasattr(health, "dict") else health,
                        "pid": status["pid"],
                    }
                except Exception as e:
                    return {
                        "success": True,
                        "status": "running",
                        "message": f"Monitoring agent is running but health check failed: {e}",
                        "pid": status["pid"],
                    }
            else:
                return {
                    "success": False,
                    "status": "not_running",
                    "message": "Monitoring agent is not running",
                    "error": status["message"],
                }

        except Exception as e:
            logger.error(f"Error checking monitoring health: {e}")
            return {"success": False, "error": str(e)}


def main():
    """Main CLI function"""
    import argparse

    parser = argparse.ArgumentParser(description="Monitoring Agent CLI")
    parser.add_argument(
        "command",
        choices=["start", "stop", "status", "restart", "health"],
        help="Command to execute",
    )
    parser.add_argument("--daemon", action="store_true", help="Run in daemon mode")
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Log level",
    )

    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(
        level=getattr(logging, args.log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Create monitoring commands instance
    commands = MonitoringCommands()

    async def run_command():
        if args.command == "start":
            result = await commands.start_monitoring()
        elif args.command == "stop":
            result = await commands.stop_monitoring()
        elif args.command == "status":
            result = await commands.get_status()
        elif args.command == "restart":
            result = await commands.restart_monitoring()
        elif args.command == "health":
            result = await commands.check_health()
        else:
            result = {"success": False, "error": f"Unknown command: {args.command}"}

        # Print result
        if result["success"]:
            print(f"✅ {result['message']}")
            if "pid" in result:
                print(f"   PID: {result['pid']}")
            if "status" in result:
                print(f"   Status: {result['status']}")
        else:
            print(f"❌ {result.get('error', 'Unknown error')}")
            sys.exit(1)

    # Run the command
    asyncio.run(run_command())


if __name__ == "__main__":
    main()
