# 🚀 Frontend Upload Integration - Complete Setup Guide

## Overview
This integration adds a complete upload system to your IDE-style web interface, allowing users to import external web projects with automatic framework detection, security scanning, and seamless integration with your existing IDE.

## ✨ Features Implemented

### 🎯 Core Components
- **UploadZone**: Drag-and-drop file upload with progress tracking
- **UploadForm**: Import options and configuration
- **SecurityReport**: Security analysis display with action buttons
- **SiteList**: Grid view of all imported sites with actions
- **UploadPage**: Complete upload workflow with step-by-step process

### 🔧 Backend Integration
- **FastAPI Routes**: Complete API endpoints for upload management
- **File Processing**: Automatic framework detection and security scanning
- **Site Management**: List, validate, and manage imported sites
- **Error Handling**: Comprehensive error handling and user feedback

### 🎨 UI/UX Features
- **Modern Design**: Beautiful gradient backgrounds and card-based layout
- **Responsive**: Mobile-friendly design with adaptive layouts
- **Progress Tracking**: Real-time upload progress with visual feedback
- **Status Indicators**: Clear status badges for security and import status
- **Action Buttons**: Quick access to preview, edit, and validate sites

## 📦 Installation & Setup

### 1. Install Frontend Dependencies
```bash
# Install Node.js dependencies
npm install

# Install additional dependencies if needed
npm install react-dropzone@14.2.3
```

### 2. Backend Integration
The backend routes are already created in `src/api/upload_routes.py`. Make sure to:

1. **Add the routes to your FastAPI app**:
```python
from api.upload_routes import router as upload_router
app.include_router(upload_router)
```

2. **Ensure the upload directories exist**:
```bash
mkdir -p uploads/pending uploads/imported uploads/validated
```

### 3. Start the Development Servers
```bash
# Terminal 1: Start the FastAPI backend
python -m uvicorn src.dashboard.minimal_api:app --host 127.0.0.1 --port 8000 --reload

# Terminal 2: Start the Next.js frontend
npm run dev
```

## 🎯 Usage Guide

### For Users

#### 1. **Access the Upload Interface**
- Navigate to `http://localhost:3000/upload`
- Or click "📤 Import Project" from the main navigation

#### 2. **Upload a Web Project**
- **Drag & Drop**: Simply drag a project folder onto the upload zone
- **Click to Select**: Click the upload zone to browse and select files
- **Directory Upload**: The system supports uploading entire project directories

#### 3. **Review & Configure**
- **Security Analysis**: Review the automatic security scan results
- **Framework Detection**: See which web framework was detected
- **Import Options**: Set target name and cleanup preferences

#### 4. **Complete Import**
- **Proceed**: Click "✅ Proceed with Import" to complete the process
- **Cancel**: Click "❌ Cancel Import" to abort if issues are found

#### 5. **Manage Imported Sites**
- **View All Sites**: See all imported projects in the site list
- **Actions Available**:
  - 📝 **Open in Editor**: Launch the site in your IDE
  - 🔍 **Preview**: View the site in a new tab
  - ✅ **Validate**: Run additional validation checks

### For Developers

#### API Endpoints Available
```bash
# Upload a site
POST /api/upload-site

# Confirm upload after review
POST /api/upload-site/confirm

# List all sites
GET /api/sites/list

# Validate a site
POST /api/sites/validate/{site_name}

# Get site manifest
GET /api/sites/{site_name}/manifest

# Delete a site
DELETE /api/sites/{site_name}

# Get upload statistics
GET /api/upload/statistics

# Clean up pending uploads
POST /api/upload/cleanup
```

#### Component Integration
```tsx
// Import components
import { UploadZone } from '@/components/UploadZone';
import { UploadForm } from '@/components/UploadForm';
import { SecurityReport } from '@/components/SecurityReport';
import { SiteList } from '@/components/SiteList';

// Use in your pages
<UploadZone
  onUpload={handleUpload}
  onUploadComplete={handleUploadComplete}
/>
```

## 🔧 Configuration

### Environment Variables
```bash
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000

# Backend (config/upload_config.json)
{
  "max_file_size_mb": 100,
  "allowed_extensions": [".html", ".js", ".ts", ".py", ".json"],
  "security_scan_enabled": true,
  "auto_cleanup": false
}
```

### Customization Options

#### 1. **Styling**
- Modify CSS in each component's `<style jsx>` section
- Update color schemes and layouts as needed
- Add your brand colors and fonts

#### 2. **Upload Behavior**
- Adjust file size limits in the backend
- Modify allowed file extensions
- Configure security scanning sensitivity

#### 3. **Integration Points**
- Update navigation links to match your app structure
- Modify the editor integration URLs
- Customize the site preview functionality

## 🧪 Testing

### Frontend Testing
```bash
# Run type checking
npm run type-check

# Run linting
npm run lint

# Test the upload flow manually
# 1. Visit http://localhost:3000/upload
# 2. Upload a test project
# 3. Verify the workflow completes successfully
```

### Backend Testing
```bash
# Test the upload API
curl -X POST http://127.0.0.1:8000/api/upload-site \
  -F "files=@test-project.zip"

# Test site listing
curl http://127.0.0.1:8000/api/sites/list
```

## 🚨 Troubleshooting

### Common Issues

#### 1. **Upload Fails**
- Check file size limits
- Verify allowed file extensions
- Ensure upload directories exist and are writable

#### 2. **API Connection Errors**
- Verify FastAPI server is running on port 8000
- Check CORS settings if needed
- Ensure Next.js proxy configuration is correct

#### 3. **Security Scan Issues**
- Check if security scanning is enabled
- Verify the scan configuration
- Review the security report for specific issues

#### 4. **Framework Detection Problems**
- Ensure the project has recognizable framework files
- Check the detection logic in `SiteUploadManager`
- Verify the confidence thresholds

### Debug Mode
```bash
# Enable debug logging
export DEBUG=upload:*
npm run dev

# Check browser console for frontend errors
# Check FastAPI logs for backend errors
```

## 📊 Performance Considerations

### Optimization Tips
1. **File Size Limits**: Set appropriate limits for your use case
2. **Progress Tracking**: Use streaming for large file uploads
3. **Caching**: Implement caching for site manifests and lists
4. **Lazy Loading**: Load site lists on demand
5. **Compression**: Enable gzip compression for API responses

### Monitoring
- Monitor upload success rates
- Track security scan results
- Monitor API response times
- Watch for disk space usage

## 🔒 Security Features

### Built-in Protections
- **File Type Validation**: Only allowed extensions
- **Size Limits**: Configurable file size restrictions
- **Security Scanning**: Automatic malware and suspicious file detection
- **Path Validation**: Prevents directory traversal attacks
- **Duplicate Detection**: Hash-based duplicate prevention

### Best Practices
- Regularly update security scanning rules
- Monitor upload logs for suspicious activity
- Implement rate limiting for upload endpoints
- Use HTTPS in production
- Regular security audits of uploaded content

## 🎉 Success Metrics

### Key Performance Indicators
- **Upload Success Rate**: Target >95%
- **Security Scan Accuracy**: Target >90%
- **Framework Detection Accuracy**: Target >85%
- **User Satisfaction**: Monitor feedback and usage patterns

### Monitoring Dashboard
Consider implementing a dashboard to track:
- Daily upload volumes
- Security scan results
- Framework detection statistics
- User engagement metrics

---

## 🚀 Next Steps

### Immediate Actions
1. ✅ Install dependencies
2. ✅ Start development servers
3. ✅ Test upload functionality
4. ✅ Customize styling to match your brand
5. ✅ Configure security settings

### Future Enhancements
- **Batch Upload**: Support for multiple projects at once
- **Advanced Security**: Integration with external security tools
- **Preview Server**: Built-in preview server for imported sites
- **Version Control**: Git integration for imported projects
- **Collaboration**: Multi-user upload and sharing features

---

**🎯 Ready to use!** Your IDE now has a complete, professional-grade upload system that seamlessly integrates with your existing workflow.
