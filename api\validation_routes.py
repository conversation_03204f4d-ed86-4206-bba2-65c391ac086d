"""
API routes for Validation Service.
Provides REST API endpoints for validation operations.
"""

import os
from typing import Any, Dict, List, Optional

import httpx
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from api.agent_dependency import get_agent

router = APIRouter(prefix="/api/validation", tags=["Validation"])

VALIDATION_HOST = os.getenv("VALIDATION_SERVICE_HOST", "http://localhost")
VALIDATION_PORT = os.getenv("VALIDATION_PORT", "8004")
BASE_URL = f"{VALIDATION_HOST}:{VALIDATION_PORT}"


class ValidationRequest(BaseModel):
    site_path: Optional[str] = None
    validation_type: Optional[str] = "full"
    detailed: bool = False
    rules: Optional[List[str]] = None


class ValidationResponse(BaseModel):
    success: bool
    job_id: Optional[str] = None
    status: str
    message: str
    results: Optional[Dict[str, Any]] = None
    timestamp: str


class ValidationStatusResponse(BaseModel):
    job_id: str
    status: str
    progress: float
    results: Optional[Dict[str, Any]] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None


class ValidationRule(BaseModel):
    name: str
    description: str
    enabled: bool = True
    severity: str = "medium"


@router.get("/health")
async def health(agent: Any = Depends(get_agent)) -> Dict[str, Any]:
    """Check validation service health"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/health")
            r.raise_for_status()
            return r.json()
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Validation service health check failed: {str(e)}"
        )


@router.get("/status")
async def status(agent: Any = Depends(get_agent)) -> Dict[str, Any]:
    """Get validation service status"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/status")
            r.raise_for_status()
            return r.json()
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Validation service status check failed: {str(e)}"
        )


@router.post("/start", response_model=ValidationResponse)
async def start_validation(
    req: ValidationRequest, agent: Any = Depends(get_agent)
) -> ValidationResponse:
    """Start a validation job"""
    try:
        payload = {
            "site_path": req.site_path,
            "validation_type": req.validation_type,
            "detailed": req.detailed,
            "rules": req.rules or [],
        }
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            if r.status_code == 409:
                raise HTTPException(
                    status_code=409, detail=r.json().get("detail", "Conflict")
                )
            r.raise_for_status()
            return ValidationResponse(**r.json())
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to start validation: {str(e)}"
        )


@router.post("/stop/{job_id}")
async def stop_validation(
    job_id: str, agent: Any = Depends(get_agent)
) -> Dict[str, Any]:
    """Stop a validation job"""
    try:
        async with httpx.AsyncClient(timeout=15) as client:
            r = await client.post(f"{BASE_URL}/stop/{job_id}")
            if r.status_code == 404:
                raise HTTPException(status_code=404, detail="Job not found")
            r.raise_for_status()
            return r.json()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to stop validation: {str(e)}"
        )


@router.get("/progress/{job_id}", response_model=ValidationStatusResponse)
async def get_progress(
    job_id: str, agent: Any = Depends(get_agent)
) -> ValidationStatusResponse:
    """Get validation job progress"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/progress/{job_id}")
            if r.status_code == 404:
                raise HTTPException(status_code=404, detail="Job not found")
            r.raise_for_status()
            return ValidationStatusResponse(**r.json())
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get progress: {str(e)}")


@router.get("/results/{job_id}")
async def get_results(job_id: str, agent: Any = Depends(get_agent)) -> Dict[str, Any]:
    """Get validation results for a job"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/results/{job_id}")
            if r.status_code == 404:
                raise HTTPException(status_code=404, detail="Job not found")
            elif r.status_code == 400:
                raise HTTPException(status_code=400, detail="Job not completed")
            r.raise_for_status()
            return r.json()
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get results: {str(e)}")


@router.get("/rules", response_model=List[ValidationRule])
async def get_rules(agent: Any = Depends(get_agent)) -> List[ValidationRule]:
    """Get available validation rules"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/rules")
            r.raise_for_status()
            rules_data = r.json()
            return [ValidationRule(**rule) for rule in rules_data]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get rules: {str(e)}")


@router.get("/metrics")
async def get_metrics(agent: Any = Depends(get_agent)) -> Dict[str, Any]:
    """Get quality metrics"""
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            r = await client.get(f"{BASE_URL}/metrics")
            r.raise_for_status()
            return r.json()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.post("/validate-site")
async def validate_site(
    site_path: str, detailed: bool = False, agent: Any = Depends(get_agent)
) -> ValidationResponse:
    """Validate a specific site"""
    try:
        payload = {
            "site_path": site_path,
            "validation_type": "full",
            "detailed": detailed,
            "rules": [],
        }
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return ValidationResponse(**r.json())
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate site: {str(e)}"
        )


@router.post("/validate-configuration")
async def validate_configuration(agent: Any = Depends(get_agent)) -> ValidationResponse:
    """Validate system configuration"""
    try:
        payload = {"validation_type": "configuration", "detailed": True, "rules": []}
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return ValidationResponse(**r.json())
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate configuration: {str(e)}"
        )


@router.post("/validate-dependencies")
async def validate_dependencies(agent: Any = Depends(get_agent)) -> ValidationResponse:
    """Validate system dependencies"""
    try:
        payload = {"validation_type": "dependencies", "detailed": True, "rules": []}
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return ValidationResponse(**r.json())
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate dependencies: {str(e)}"
        )


@router.post("/validate-security")
async def validate_security(agent: Any = Depends(get_agent)) -> ValidationResponse:
    """Validate security settings"""
    try:
        payload = {"validation_type": "security", "detailed": True, "rules": []}
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return ValidationResponse(**r.json())
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate security: {str(e)}"
        )


@router.post("/validate-performance")
async def validate_performance(agent: Any = Depends(get_agent)) -> ValidationResponse:
    """Validate performance settings"""
    try:
        payload = {"validation_type": "performance", "detailed": True, "rules": []}
        async with httpx.AsyncClient(timeout=60) as client:
            r = await client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            return ValidationResponse(**r.json())
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to validate performance: {str(e)}"
        )
