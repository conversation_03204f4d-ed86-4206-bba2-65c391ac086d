{"component_metrics": [{"component_name": "NormalComponent0", "render_time_ms": 10.0, "re_render_count": 1, "memory_usage_mb": 8.0, "bundle_size_kb": 20.0, "timestamp": "2025-07-24T20:34:50.279187", "props_count": 4, "state_count": 2, "hooks_count": 6, "error_count": 0}, {"component_name": "NormalComponent1", "render_time_ms": 11.0, "re_render_count": 2, "memory_usage_mb": 8.5, "bundle_size_kb": 21.0, "timestamp": "2025-07-24T20:34:50.379858", "props_count": 5, "state_count": 3, "hooks_count": 7, "error_count": 0}, {"component_name": "NormalComponent2", "render_time_ms": 12.0, "re_render_count": 1, "memory_usage_mb": 9.0, "bundle_size_kb": 22.0, "timestamp": "2025-07-24T20:34:50.480148", "props_count": 6, "state_count": 2, "hooks_count": 8, "error_count": 0}, {"component_name": "NormalComponent3", "render_time_ms": 13.0, "re_render_count": 2, "memory_usage_mb": 9.5, "bundle_size_kb": 23.0, "timestamp": "2025-07-24T20:34:50.580707", "props_count": 4, "state_count": 3, "hooks_count": 9, "error_count": 0}, {"component_name": "NormalComponent4", "render_time_ms": 14.0, "re_render_count": 1, "memory_usage_mb": 10.0, "bundle_size_kb": 24.0, "timestamp": "2025-07-24T20:34:50.681294", "props_count": 5, "state_count": 2, "hooks_count": 6, "error_count": 0}, {"component_name": "NormalComponent5", "render_time_ms": 15.0, "re_render_count": 2, "memory_usage_mb": 10.5, "bundle_size_kb": 25.0, "timestamp": "2025-07-24T20:34:50.781525", "props_count": 6, "state_count": 3, "hooks_count": 7, "error_count": 0}, {"component_name": "NormalComponent6", "render_time_ms": 16.0, "re_render_count": 1, "memory_usage_mb": 11.0, "bundle_size_kb": 26.0, "timestamp": "2025-07-24T20:34:50.882036", "props_count": 4, "state_count": 2, "hooks_count": 8, "error_count": 0}, {"component_name": "NormalComponent7", "render_time_ms": 17.0, "re_render_count": 2, "memory_usage_mb": 11.5, "bundle_size_kb": 27.0, "timestamp": "2025-07-24T20:34:50.982594", "props_count": 5, "state_count": 3, "hooks_count": 9, "error_count": 0}, {"component_name": "NormalComponent8", "render_time_ms": 18.0, "re_render_count": 1, "memory_usage_mb": 12.0, "bundle_size_kb": 28.0, "timestamp": "2025-07-24T20:34:51.083671", "props_count": 6, "state_count": 2, "hooks_count": 6, "error_count": 0}, {"component_name": "NormalComponent9", "render_time_ms": 19.0, "re_render_count": 2, "memory_usage_mb": 12.5, "bundle_size_kb": 29.0, "timestamp": "2025-07-24T20:34:51.203748", "props_count": 4, "state_count": 3, "hooks_count": 7, "error_count": 0}, {"component_name": "SlowComponent0", "render_time_ms": 30.0, "re_render_count": 8, "memory_usage_mb": 25.0, "bundle_size_kb": 60.0, "timestamp": "2025-07-24T20:34:51.376711", "props_count": 12, "state_count": 6, "hooks_count": 15, "error_count": 0}, {"component_name": "NormalComponent1", "render_time_ms": 13.0, "re_render_count": 3, "memory_usage_mb": 10.8, "bundle_size_kb": 26.5, "timestamp": "2025-07-24T20:34:51.512430", "props_count": 6, "state_count": 4, "hooks_count": 9, "error_count": 0}, {"component_name": "NormalComponent2", "render_time_ms": 14.0, "re_render_count": 4, "memory_usage_mb": 11.6, "bundle_size_kb": 28.0, "timestamp": "2025-07-24T20:34:51.612805", "props_count": 7, "state_count": 5, "hooks_count": 10, "error_count": 0}, {"component_name": "SlowComponent3", "render_time_ms": 39.0, "re_render_count": 11, "memory_usage_mb": 31.0, "bundle_size_kb": 72.0, "timestamp": "2025-07-24T20:34:51.713399", "props_count": 15, "state_count": 7, "hooks_count": 18, "error_count": 1}, {"component_name": "NormalComponent4", "render_time_ms": 16.0, "re_render_count": 3, "memory_usage_mb": 13.2, "bundle_size_kb": 31.0, "timestamp": "2025-07-24T20:34:51.815143", "props_count": 5, "state_count": 4, "hooks_count": 12, "error_count": 0}, {"component_name": "NormalComponent5", "render_time_ms": 17.0, "re_render_count": 4, "memory_usage_mb": 14.0, "bundle_size_kb": 32.5, "timestamp": "2025-07-24T20:34:51.915462", "props_count": 6, "state_count": 5, "hooks_count": 8, "error_count": 0}, {"component_name": "SlowComponent6", "render_time_ms": 48.0, "re_render_count": 14, "memory_usage_mb": 37.0, "bundle_size_kb": 84.0, "timestamp": "2025-07-24T20:34:52.016046", "props_count": 18, "state_count": 9, "hooks_count": 21, "error_count": 0}, {"component_name": "NormalComponent7", "render_time_ms": 19.0, "re_render_count": 3, "memory_usage_mb": 15.600000000000001, "bundle_size_kb": 35.5, "timestamp": "2025-07-24T20:34:52.117844", "props_count": 8, "state_count": 4, "hooks_count": 10, "error_count": 0}, {"component_name": "NormalComponent8", "render_time_ms": 20.0, "re_render_count": 4, "memory_usage_mb": 16.4, "bundle_size_kb": 37.0, "timestamp": "2025-07-24T20:34:52.218357", "props_count": 5, "state_count": 5, "hooks_count": 11, "error_count": 0}, {"component_name": "SlowComponent9", "render_time_ms": 57.0, "re_render_count": 17, "memory_usage_mb": 43.0, "bundle_size_kb": 96.0, "timestamp": "2025-07-24T20:34:52.319120", "props_count": 21, "state_count": 10, "hooks_count": 24, "error_count": 1}, {"component_name": "NormalComponent10", "render_time_ms": 22.0, "re_render_count": 3, "memory_usage_mb": 18.0, "bundle_size_kb": 40.0, "timestamp": "2025-07-24T20:34:52.420738", "props_count": 7, "state_count": 4, "hooks_count": 8, "error_count": 0}, {"component_name": "NormalComponent11", "render_time_ms": 23.0, "re_render_count": 4, "memory_usage_mb": 18.8, "bundle_size_kb": 41.5, "timestamp": "2025-07-24T20:34:52.521662", "props_count": 8, "state_count": 5, "hooks_count": 9, "error_count": 0}, {"component_name": "SlowComponent12", "render_time_ms": 66.0, "re_render_count": 20, "memory_usage_mb": 49.0, "bundle_size_kb": 108.0, "timestamp": "2025-07-24T20:34:52.622618", "props_count": 24, "state_count": 12, "hooks_count": 27, "error_count": 0}, {"component_name": "NormalComponent13", "render_time_ms": 25.0, "re_render_count": 3, "memory_usage_mb": 20.4, "bundle_size_kb": 44.5, "timestamp": "2025-07-24T20:34:52.725332", "props_count": 6, "state_count": 4, "hooks_count": 11, "error_count": 0}, {"component_name": "NormalComponent14", "render_time_ms": 26.0, "re_render_count": 4, "memory_usage_mb": 21.200000000000003, "bundle_size_kb": 46.0, "timestamp": "2025-07-24T20:34:52.826259", "props_count": 7, "state_count": 5, "hooks_count": 12, "error_count": 0}, {"component_name": "RecoveryComponent0", "render_time_ms": 8.0, "re_render_count": 1, "memory_usage_mb": 6.0, "bundle_size_kb": 15.0, "timestamp": "2025-07-24T20:34:52.928117", "props_count": 3, "state_count": 1, "hooks_count": 4, "error_count": 0}, {"component_name": "RecoveryComponent1", "render_time_ms": 8.5, "re_render_count": 2, "memory_usage_mb": 6.3, "bundle_size_kb": 15.8, "timestamp": "2025-07-24T20:34:53.028731", "props_count": 4, "state_count": 2, "hooks_count": 5, "error_count": 0}, {"component_name": "RecoveryComponent2", "render_time_ms": 9.0, "re_render_count": 1, "memory_usage_mb": 6.6, "bundle_size_kb": 16.6, "timestamp": "2025-07-24T20:34:53.129116", "props_count": 5, "state_count": 1, "hooks_count": 6, "error_count": 0}, {"component_name": "RecoveryComponent3", "render_time_ms": 9.5, "re_render_count": 2, "memory_usage_mb": 6.9, "bundle_size_kb": 17.4, "timestamp": "2025-07-24T20:34:53.230009", "props_count": 3, "state_count": 2, "hooks_count": 4, "error_count": 0}, {"component_name": "RecoveryComponent4", "render_time_ms": 10.0, "re_render_count": 1, "memory_usage_mb": 7.2, "bundle_size_kb": 18.2, "timestamp": "2025-07-24T20:34:53.330284", "props_count": 4, "state_count": 1, "hooks_count": 5, "error_count": 0}, {"component_name": "RecoveryComponent5", "render_time_ms": 10.5, "re_render_count": 2, "memory_usage_mb": 7.5, "bundle_size_kb": 19.0, "timestamp": "2025-07-24T20:34:53.430636", "props_count": 5, "state_count": 2, "hooks_count": 6, "error_count": 0}, {"component_name": "RecoveryComponent6", "render_time_ms": 11.0, "re_render_count": 1, "memory_usage_mb": 7.8, "bundle_size_kb": 19.8, "timestamp": "2025-07-24T20:34:53.531136", "props_count": 3, "state_count": 1, "hooks_count": 4, "error_count": 0}, {"component_name": "RecoveryComponent7", "render_time_ms": 11.5, "re_render_count": 2, "memory_usage_mb": 8.1, "bundle_size_kb": 20.6, "timestamp": "2025-07-24T20:34:53.631399", "props_count": 4, "state_count": 2, "hooks_count": 5, "error_count": 0}, {"component_name": "RecoveryComponent8", "render_time_ms": 12.0, "re_render_count": 1, "memory_usage_mb": 8.4, "bundle_size_kb": 21.4, "timestamp": "2025-07-24T20:34:53.731645", "props_count": 5, "state_count": 1, "hooks_count": 6, "error_count": 0}, {"component_name": "RecoveryComponent9", "render_time_ms": 12.5, "re_render_count": 2, "memory_usage_mb": 8.7, "bundle_size_kb": 22.2, "timestamp": "2025-07-24T20:34:53.832209", "props_count": 3, "state_count": 2, "hooks_count": 4, "error_count": 0}], "bundle_metrics": [{"timestamp": "2025-07-24T20:34:50.291633", "total_size_kb": 971.6982421875, "js_size_kb": 971.6982421875, "css_size_kb": 0.0, "image_size_kb": 0.0, "chunk_count": 27, "duplicate_modules": 0, "unused_modules": 0, "tree_shaking_efficiency": 85.0, "compression_ratio": 0.7, "load_time_ms": 1943.396484375}, {"timestamp": "2025-07-24T20:34:56.951189", "total_size_kb": 971.6982421875, "js_size_kb": 971.6982421875, "css_size_kb": 0.0, "image_size_kb": 0.0, "chunk_count": 27, "duplicate_modules": 0, "unused_modules": 0, "tree_shaking_efficiency": 85.0, "compression_ratio": 0.7, "load_time_ms": 1943.396484375}], "frontend_metrics": [{"timestamp": "2025-07-24T20:34:50.279225", "page_load_time_ms": 2000.0, "first_contentful_paint_ms": 800.0, "largest_contentful_paint_ms": 1200.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 75.0, "cpu_usage_percent": 12.0, "network_requests": 18, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.379889", "page_load_time_ms": 2050.0, "first_contentful_paint_ms": 825.0, "largest_contentful_paint_ms": 1235.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 76.0, "cpu_usage_percent": 12.5, "network_requests": 19, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.480170", "page_load_time_ms": 2100.0, "first_contentful_paint_ms": 850.0, "largest_contentful_paint_ms": 1270.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 77.0, "cpu_usage_percent": 13.0, "network_requests": 20, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.580730", "page_load_time_ms": 2150.0, "first_contentful_paint_ms": 875.0, "largest_contentful_paint_ms": 1305.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 78.0, "cpu_usage_percent": 13.5, "network_requests": 21, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.681317", "page_load_time_ms": 2200.0, "first_contentful_paint_ms": 900.0, "largest_contentful_paint_ms": 1340.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 79.0, "cpu_usage_percent": 14.0, "network_requests": 22, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.781549", "page_load_time_ms": 2250.0, "first_contentful_paint_ms": 925.0, "largest_contentful_paint_ms": 1375.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 80.0, "cpu_usage_percent": 14.5, "network_requests": 23, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.882058", "page_load_time_ms": 2300.0, "first_contentful_paint_ms": 950.0, "largest_contentful_paint_ms": 1410.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 81.0, "cpu_usage_percent": 15.0, "network_requests": 24, "error_count": 0}, {"timestamp": "2025-07-24T20:34:50.983091", "page_load_time_ms": 2350.0, "first_contentful_paint_ms": 975.0, "largest_contentful_paint_ms": 1445.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 82.0, "cpu_usage_percent": 15.5, "network_requests": 25, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.094348", "page_load_time_ms": 2400.0, "first_contentful_paint_ms": 1000.0, "largest_contentful_paint_ms": 1480.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 83.0, "cpu_usage_percent": 16.0, "network_requests": 26, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.226301", "page_load_time_ms": 2450.0, "first_contentful_paint_ms": 1025.0, "largest_contentful_paint_ms": 1515.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 84.0, "cpu_usage_percent": 16.5, "network_requests": 27, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.392684", "page_load_time_ms": 4000.0, "first_contentful_paint_ms": 1200.0, "largest_contentful_paint_ms": 1800.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 110.0, "cpu_usage_percent": 25.0, "network_requests": 35, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.512458", "page_load_time_ms": 2260.0, "first_contentful_paint_ms": 880.0, "largest_contentful_paint_ms": 1290.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 81.5, "cpu_usage_percent": 14.8, "network_requests": 23, "error_count": 1}, {"timestamp": "2025-07-24T20:34:51.612830", "page_load_time_ms": 2320.0, "first_contentful_paint_ms": 910.0, "largest_contentful_paint_ms": 1330.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 83.0, "cpu_usage_percent": 15.6, "network_requests": 24, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.713999", "page_load_time_ms": 4450.0, "first_contentful_paint_ms": 1425.0, "largest_contentful_paint_ms": 2100.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 119.0, "cpu_usage_percent": 31.0, "network_requests": 38, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.815167", "page_load_time_ms": 2440.0, "first_contentful_paint_ms": 970.0, "largest_contentful_paint_ms": 1410.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 86.0, "cpu_usage_percent": 17.2, "network_requests": 26, "error_count": 0}, {"timestamp": "2025-07-24T20:34:51.915835", "page_load_time_ms": 2500.0, "first_contentful_paint_ms": 1000.0, "largest_contentful_paint_ms": 1450.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 87.5, "cpu_usage_percent": 18.0, "network_requests": 27, "error_count": 1}, {"timestamp": "2025-07-24T20:34:52.016630", "page_load_time_ms": 4900.0, "first_contentful_paint_ms": 1650.0, "largest_contentful_paint_ms": 2400.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 128.0, "cpu_usage_percent": 37.0, "network_requests": 41, "error_count": 0}, {"timestamp": "2025-07-24T20:34:52.118254", "page_load_time_ms": 2620.0, "first_contentful_paint_ms": 1060.0, "largest_contentful_paint_ms": 1530.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 90.5, "cpu_usage_percent": 19.6, "network_requests": 29, "error_count": 1}, {"timestamp": "2025-07-24T20:34:52.218708", "page_load_time_ms": 2680.0, "first_contentful_paint_ms": 1090.0, "largest_contentful_paint_ms": 1570.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 92.0, "cpu_usage_percent": 20.4, "network_requests": 30, "error_count": 0}, {"timestamp": "2025-07-24T20:34:52.319893", "page_load_time_ms": 5350.0, "first_contentful_paint_ms": 1875.0, "largest_contentful_paint_ms": 2700.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 137.0, "cpu_usage_percent": 43.0, "network_requests": 44, "error_count": 0}, {"timestamp": "2025-07-24T20:34:52.421122", "page_load_time_ms": 2800.0, "first_contentful_paint_ms": 1150.0, "largest_contentful_paint_ms": 1650.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 95.0, "cpu_usage_percent": 22.0, "network_requests": 32, "error_count": 0}, {"timestamp": "2025-07-24T20:34:52.522057", "page_load_time_ms": 2860.0, "first_contentful_paint_ms": 1180.0, "largest_contentful_paint_ms": 1690.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 96.5, "cpu_usage_percent": 22.8, "network_requests": 33, "error_count": 1}, {"timestamp": "2025-07-24T20:34:52.624332", "page_load_time_ms": 5800.0, "first_contentful_paint_ms": 2100.0, "largest_contentful_paint_ms": 3000.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 146.0, "cpu_usage_percent": 49.0, "network_requests": 47, "error_count": 0}, {"timestamp": "2025-07-24T20:34:52.725729", "page_load_time_ms": 2980.0, "first_contentful_paint_ms": 1240.0, "largest_contentful_paint_ms": 1770.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 99.5, "cpu_usage_percent": 24.4, "network_requests": 35, "error_count": 1}, {"timestamp": "2025-07-24T20:34:52.826726", "page_load_time_ms": 3040.0, "first_contentful_paint_ms": 1270.0, "largest_contentful_paint_ms": 1810.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 101.0, "cpu_usage_percent": 25.200000000000003, "network_requests": 36, "error_count": 0}, {"timestamp": "2025-07-24T20:34:52.928143", "page_load_time_ms": 1800.0, "first_contentful_paint_ms": 700.0, "largest_contentful_paint_ms": 1100.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 70.0, "cpu_usage_percent": 10.0, "network_requests": 15, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.028766", "page_load_time_ms": 1830.0, "first_contentful_paint_ms": 720.0, "largest_contentful_paint_ms": 1125.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 70.8, "cpu_usage_percent": 10.3, "network_requests": 16, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.129142", "page_load_time_ms": 1860.0, "first_contentful_paint_ms": 740.0, "largest_contentful_paint_ms": 1150.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 71.6, "cpu_usage_percent": 10.6, "network_requests": 17, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.230035", "page_load_time_ms": 1890.0, "first_contentful_paint_ms": 760.0, "largest_contentful_paint_ms": 1175.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 72.4, "cpu_usage_percent": 10.9, "network_requests": 18, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.330323", "page_load_time_ms": 1920.0, "first_contentful_paint_ms": 780.0, "largest_contentful_paint_ms": 1200.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 73.2, "cpu_usage_percent": 11.2, "network_requests": 19, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.430681", "page_load_time_ms": 1950.0, "first_contentful_paint_ms": 800.0, "largest_contentful_paint_ms": 1225.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 74.0, "cpu_usage_percent": 11.5, "network_requests": 20, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.531192", "page_load_time_ms": 1980.0, "first_contentful_paint_ms": 820.0, "largest_contentful_paint_ms": 1250.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 74.8, "cpu_usage_percent": 11.8, "network_requests": 21, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.631425", "page_load_time_ms": 2010.0, "first_contentful_paint_ms": 840.0, "largest_contentful_paint_ms": 1275.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 75.6, "cpu_usage_percent": 12.1, "network_requests": 22, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.731672", "page_load_time_ms": 2040.0, "first_contentful_paint_ms": 860.0, "largest_contentful_paint_ms": 1300.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 76.4, "cpu_usage_percent": 12.4, "network_requests": 23, "error_count": 0}, {"timestamp": "2025-07-24T20:34:53.832237", "page_load_time_ms": 2070.0, "first_contentful_paint_ms": 880.0, "largest_contentful_paint_ms": 1325.0, "cumulative_layout_shift": 0.0, "first_input_delay_ms": 0.0, "total_blocking_time_ms": 0.0, "speed_index_ms": 0.0, "time_to_interactive_ms": 0.0, "memory_usage_mb": 77.2, "cpu_usage_percent": 12.7, "network_requests": 24, "error_count": 0}], "recommendations": [{"recommendation_id": "slow_component_1753410890", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent7", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent7\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent7 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent7;\n", "created_at": "2025-07-24T20:34:50.983061", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410891", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent8", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent8\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent8 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent8;\n", "created_at": "2025-07-24T20:34:51.094308", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410891", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent9", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent9\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent9 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent9;\n", "created_at": "2025-07-24T20:34:51.226266", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410891", "type": "component", "priority": "medium", "description": "Optimize slow component: SlowComponent0", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for SlowComponent0\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst SlowComponent0 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default SlowComponent0;\n", "created_at": "2025-07-24T20:34:51.392528", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_page_load_1753410891", "type": "performance", "priority": "high", "description": "Optimize page load performance", "impact": "high", "estimated_improvement": 50.0, "implementation_cost": "medium", "code_suggestion": "\n// Page load optimization\nimport dynamic from 'next/dynamic';\n\n// Lazy load heavy components\nconst HeavyComponent = dynamic(() => import('./HeavyComponent'), {{\n  loading: () => <div>Loading...</div>,\n  ssr: false\n}});\n\n// Use Next.js Image component for optimized images\nimport Image from 'next/image';\n\n// Implement proper loading strategies\nexport default function OptimizedPage() {\n  return (\n    <div>\n      <Image\n        src=\"/optimized-image.jpg\"\n        alt=\"Optimized image\"\n        width={{500}}\n        height={{300}}\n        priority={{true}}\n      />\n      <HeavyComponent />\n    </div>\n  );\n}\n", "created_at": "2025-07-24T20:34:51.411719", "implemented": false, "implemented_at": null}, {"recommendation_id": "high_memory_1753410891", "type": "performance", "priority": "medium", "description": "Reduce memory usage", "impact": "medium", "estimated_improvement": 25.0, "implementation_cost": "medium", "code_suggestion": "\n// Memory optimization\nimport { useEffect, useRef } from 'react';\n\n// Clean up event listeners and subscriptions\nuseEffect(() => {{\n  const subscription = someService.subscribe();\n\n  return () => {{\n    subscription.unsubscribe();\n  }};\n}}, []);\n\n// Use refs for values that don't need re-renders\nconst valueRef = useRef(initialValue);\n\n// Avoid creating objects in render\nconst memoizedObject = useMemo(() => ({{\n  key: 'value'\n}}), []);\n", "created_at": "2025-07-24T20:34:51.412156", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410891", "type": "component", "priority": "high", "description": "Optimize slow component: SlowComponent3", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for SlowComponent3\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst SlowComponent3 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default SlowComponent3;\n", "created_at": "2025-07-24T20:34:51.713743", "implemented": false, "implemented_at": null}, {"recommendation_id": "excessive_rerenders_1753410891", "type": "component", "priority": "medium", "description": "Reduce re-renders in component: SlowComponent3", "impact": "medium", "estimated_improvement": 30.0, "implementation_cost": "low", "code_suggestion": "\n// Re-render optimization for SlowComponent3\nimport React, { memo, useMemo, useCallback } from 'react';\n\nconst SlowComponent3 = memo(({ props }) => {\n  // Memoize props to prevent unnecessary re-renders\n  const memoizedProps = useMemo(() => ({\n    ...props,\n    // Only include necessary props\n  }), [props.essentialProp1, props.essentialProp2]);\n\n  return (\n    <div>\n      {/* Component with optimized re-renders */}\n    </div>\n  );\n});\n\nexport default SlowComponent3;\n", "created_at": "2025-07-24T20:34:51.713984", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_page_load_1753410891", "type": "performance", "priority": "high", "description": "Optimize page load performance", "impact": "high", "estimated_improvement": 50.0, "implementation_cost": "medium", "code_suggestion": "\n// Page load optimization\nimport dynamic from 'next/dynamic';\n\n// Lazy load heavy components\nconst HeavyComponent = dynamic(() => import('./HeavyComponent'), {{\n  loading: () => <div>Loading...</div>,\n  ssr: false\n}});\n\n// Use Next.js Image component for optimized images\nimport Image from 'next/image';\n\n// Implement proper loading strategies\nexport default function OptimizedPage() {\n  return (\n    <div>\n      <Image\n        src=\"/optimized-image.jpg\"\n        alt=\"Optimized image\"\n        width={{500}}\n        height={{300}}\n        priority={{true}}\n      />\n      <HeavyComponent />\n    </div>\n  );\n}\n", "created_at": "2025-07-24T20:34:51.714181", "implemented": false, "implemented_at": null}, {"recommendation_id": "high_memory_1753410891", "type": "performance", "priority": "medium", "description": "Reduce memory usage", "impact": "medium", "estimated_improvement": 25.0, "implementation_cost": "medium", "code_suggestion": "\n// Memory optimization\nimport { useEffect, useRef } from 'react';\n\n// Clean up event listeners and subscriptions\nuseEffect(() => {{\n  const subscription = someService.subscribe();\n\n  return () => {{\n    subscription.unsubscribe();\n  }};\n}}, []);\n\n// Use refs for values that don't need re-renders\nconst valueRef = useRef(initialValue);\n\n// Avoid creating objects in render\nconst memoizedObject = useMemo(() => ({{\n  key: 'value'\n}}), []);\n", "created_at": "2025-07-24T20:34:51.714408", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410891", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent5", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent5\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent5 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent5;\n", "created_at": "2025-07-24T20:34:51.915811", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "high", "description": "Optimize slow component: SlowComponent6", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for SlowComponent6\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst SlowComponent6 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default SlowComponent6;\n", "created_at": "2025-07-24T20:34:52.016383", "implemented": false, "implemented_at": null}, {"recommendation_id": "excessive_rerenders_1753410892", "type": "component", "priority": "medium", "description": "Reduce re-renders in component: SlowComponent6", "impact": "medium", "estimated_improvement": 30.0, "implementation_cost": "low", "code_suggestion": "\n// Re-render optimization for SlowComponent6\nimport React, { memo, useMemo, useCallback } from 'react';\n\nconst SlowComponent6 = memo(({ props }) => {\n  // Memoize props to prevent unnecessary re-renders\n  const memoizedProps = useMemo(() => ({\n    ...props,\n    // Only include necessary props\n  }), [props.essentialProp1, props.essentialProp2]);\n\n  return (\n    <div>\n      {/* Component with optimized re-renders */}\n    </div>\n  );\n});\n\nexport default SlowComponent6;\n", "created_at": "2025-07-24T20:34:52.016593", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_page_load_1753410892", "type": "performance", "priority": "high", "description": "Optimize page load performance", "impact": "high", "estimated_improvement": 50.0, "implementation_cost": "medium", "code_suggestion": "\n// Page load optimization\nimport dynamic from 'next/dynamic';\n\n// Lazy load heavy components\nconst HeavyComponent = dynamic(() => import('./HeavyComponent'), {{\n  loading: () => <div>Loading...</div>,\n  ssr: false\n}});\n\n// Use Next.js Image component for optimized images\nimport Image from 'next/image';\n\n// Implement proper loading strategies\nexport default function OptimizedPage() {\n  return (\n    <div>\n      <Image\n        src=\"/optimized-image.jpg\"\n        alt=\"Optimized image\"\n        width={{500}}\n        height={{300}}\n        priority={{true}}\n      />\n      <HeavyComponent />\n    </div>\n  );\n}\n", "created_at": "2025-07-24T20:34:52.016875", "implemented": false, "implemented_at": null}, {"recommendation_id": "high_memory_1753410892", "type": "performance", "priority": "medium", "description": "Reduce memory usage", "impact": "medium", "estimated_improvement": 25.0, "implementation_cost": "medium", "code_suggestion": "\n// Memory optimization\nimport { useEffect, useRef } from 'react';\n\n// Clean up event listeners and subscriptions\nuseEffect(() => {{\n  const subscription = someService.subscribe();\n\n  return () => {{\n    subscription.unsubscribe();\n  }};\n}}, []);\n\n// Use refs for values that don't need re-renders\nconst valueRef = useRef(initialValue);\n\n// Avoid creating objects in render\nconst memoizedObject = useMemo(() => ({{\n  key: 'value'\n}}), []);\n", "created_at": "2025-07-24T20:34:52.017098", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent7", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent7\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent7 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent7;\n", "created_at": "2025-07-24T20:34:52.118232", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent8", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent8\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent8 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent8;\n", "created_at": "2025-07-24T20:34:52.218691", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "high", "description": "Optimize slow component: SlowComponent9", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for SlowComponent9\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst SlowComponent9 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default SlowComponent9;\n", "created_at": "2025-07-24T20:34:52.319483", "implemented": false, "implemented_at": null}, {"recommendation_id": "excessive_rerenders_1753410892", "type": "component", "priority": "medium", "description": "Reduce re-renders in component: SlowComponent9", "impact": "medium", "estimated_improvement": 30.0, "implementation_cost": "low", "code_suggestion": "\n// Re-render optimization for SlowComponent9\nimport React, { memo, useMemo, useCallback } from 'react';\n\nconst SlowComponent9 = memo(({ props }) => {\n  // Memoize props to prevent unnecessary re-renders\n  const memoizedProps = useMemo(() => ({\n    ...props,\n    // Only include necessary props\n  }), [props.essentialProp1, props.essentialProp2]);\n\n  return (\n    <div>\n      {/* Component with optimized re-renders */}\n    </div>\n  );\n});\n\nexport default SlowComponent9;\n", "created_at": "2025-07-24T20:34:52.319869", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_page_load_1753410892", "type": "performance", "priority": "high", "description": "Optimize page load performance", "impact": "high", "estimated_improvement": 50.0, "implementation_cost": "medium", "code_suggestion": "\n// Page load optimization\nimport dynamic from 'next/dynamic';\n\n// Lazy load heavy components\nconst HeavyComponent = dynamic(() => import('./HeavyComponent'), {{\n  loading: () => <div>Loading...</div>,\n  ssr: false\n}});\n\n// Use Next.js Image component for optimized images\nimport Image from 'next/image';\n\n// Implement proper loading strategies\nexport default function OptimizedPage() {\n  return (\n    <div>\n      <Image\n        src=\"/optimized-image.jpg\"\n        alt=\"Optimized image\"\n        width={{500}}\n        height={{300}}\n        priority={{true}}\n      />\n      <HeavyComponent />\n    </div>\n  );\n}\n", "created_at": "2025-07-24T20:34:52.320235", "implemented": false, "implemented_at": null}, {"recommendation_id": "high_memory_1753410892", "type": "performance", "priority": "medium", "description": "Reduce memory usage", "impact": "medium", "estimated_improvement": 25.0, "implementation_cost": "medium", "code_suggestion": "\n// Memory optimization\nimport { useEffect, useRef } from 'react';\n\n// Clean up event listeners and subscriptions\nuseEffect(() => {{\n  const subscription = someService.subscribe();\n\n  return () => {{\n    subscription.unsubscribe();\n  }};\n}}, []);\n\n// Use refs for values that don't need re-renders\nconst valueRef = useRef(initialValue);\n\n// Avoid creating objects in render\nconst memoizedObject = useMemo(() => ({{\n  key: 'value'\n}}), []);\n", "created_at": "2025-07-24T20:34:52.320468", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent10", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent10\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent10 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent10;\n", "created_at": "2025-07-24T20:34:52.421094", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent11", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent11\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent11 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent11;\n", "created_at": "2025-07-24T20:34:52.522031", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "high", "description": "Optimize slow component: SlowComponent12", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for SlowComponent12\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst SlowComponent12 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default SlowComponent12;\n", "created_at": "2025-07-24T20:34:52.623360", "implemented": false, "implemented_at": null}, {"recommendation_id": "excessive_rerenders_1753410892", "type": "component", "priority": "medium", "description": "Reduce re-renders in component: SlowComponent12", "impact": "medium", "estimated_improvement": 30.0, "implementation_cost": "low", "code_suggestion": "\n// Re-render optimization for SlowComponent12\nimport React, { memo, useMemo, useCallback } from 'react';\n\nconst SlowComponent12 = memo(({ props }) => {\n  // Memoize props to prevent unnecessary re-renders\n  const memoizedProps = useMemo(() => ({\n    ...props,\n    // Only include necessary props\n  }), [props.essentialProp1, props.essentialProp2]);\n\n  return (\n    <div>\n      {/* Component with optimized re-renders */}\n    </div>\n  );\n});\n\nexport default SlowComponent12;\n", "created_at": "2025-07-24T20:34:52.624220", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_page_load_1753410892", "type": "performance", "priority": "high", "description": "Optimize page load performance", "impact": "high", "estimated_improvement": 50.0, "implementation_cost": "medium", "code_suggestion": "\n// Page load optimization\nimport dynamic from 'next/dynamic';\n\n// Lazy load heavy components\nconst HeavyComponent = dynamic(() => import('./HeavyComponent'), {{\n  loading: () => <div>Loading...</div>,\n  ssr: false\n}});\n\n// Use Next.js Image component for optimized images\nimport Image from 'next/image';\n\n// Implement proper loading strategies\nexport default function OptimizedPage() {\n  return (\n    <div>\n      <Image\n        src=\"/optimized-image.jpg\"\n        alt=\"Optimized image\"\n        width={{500}}\n        height={{300}}\n        priority={{true}}\n      />\n      <HeavyComponent />\n    </div>\n  );\n}\n", "created_at": "2025-07-24T20:34:52.624806", "implemented": false, "implemented_at": null}, {"recommendation_id": "high_memory_1753410892", "type": "performance", "priority": "medium", "description": "Reduce memory usage", "impact": "medium", "estimated_improvement": 25.0, "implementation_cost": "medium", "code_suggestion": "\n// Memory optimization\nimport { useEffect, useRef } from 'react';\n\n// Clean up event listeners and subscriptions\nuseEffect(() => {{\n  const subscription = someService.subscribe();\n\n  return () => {{\n    subscription.unsubscribe();\n  }};\n}}, []);\n\n// Use refs for values that don't need re-renders\nconst valueRef = useRef(initialValue);\n\n// Avoid creating objects in render\nconst memoizedObject = useMemo(() => ({{\n  key: 'value'\n}}), []);\n", "created_at": "2025-07-24T20:34:52.625050", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent13", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent13\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent13 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent13;\n", "created_at": "2025-07-24T20:34:52.725711", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_component_1753410892", "type": "component", "priority": "medium", "description": "Optimize slow component: NormalComponent14", "impact": "high", "estimated_improvement": 40.0, "implementation_cost": "medium", "code_suggestion": "\n// Optimization for NormalComponent14\nimport React, { memo, useMemo, useCallback } from 'react';\n\n// Use React.memo to prevent unnecessary re-renders\nconst NormalComponent14 = memo(({ props }) => {\n  // Use useMemo for expensive calculations\n  const expensiveValue = useMemo(() => {\n    // Expensive calculation here\n    return computedValue;\n  }, [dependencies]);\n\n  // Use useCallback for event handlers\n  const handleClick = useCallback(() => {\n    // Handler logic\n  }, [dependencies]);\n\n  return (\n    <div>\n      {/* Optimized component content */}\n    </div>\n  );\n});\n\nexport default NormalComponent14;\n", "created_at": "2025-07-24T20:34:52.826701", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_page_load_1753410892", "type": "performance", "priority": "high", "description": "Optimize page load performance", "impact": "high", "estimated_improvement": 50.0, "implementation_cost": "medium", "code_suggestion": "\n// Page load optimization\nimport dynamic from 'next/dynamic';\n\n// Lazy load heavy components\nconst HeavyComponent = dynamic(() => import('./HeavyComponent'), {{\n  loading: () => <div>Loading...</div>,\n  ssr: false\n}});\n\n// Use Next.js Image component for optimized images\nimport Image from 'next/image';\n\n// Implement proper loading strategies\nexport default function OptimizedPage() {\n  return (\n    <div>\n      <Image\n        src=\"/optimized-image.jpg\"\n        alt=\"Optimized image\"\n        width={{500}}\n        height={{300}}\n        priority={{true}}\n      />\n      <HeavyComponent />\n    </div>\n  );\n}\n", "created_at": "2025-07-24T20:34:52.826938", "implemented": false, "implemented_at": null}, {"recommendation_id": "high_memory_1753410892", "type": "performance", "priority": "medium", "description": "Reduce memory usage", "impact": "medium", "estimated_improvement": 25.0, "implementation_cost": "medium", "code_suggestion": "\n// Memory optimization\nimport { useEffect, useRef } from 'react';\n\n// Clean up event listeners and subscriptions\nuseEffect(() => {{\n  const subscription = someService.subscribe();\n\n  return () => {{\n    subscription.unsubscribe();\n  }};\n}}, []);\n\n// Use refs for values that don't need re-renders\nconst valueRef = useRef(initialValue);\n\n// Avoid creating objects in render\nconst memoizedObject = useMemo(() => ({{\n  key: 'value'\n}}), []);\n", "created_at": "2025-07-24T20:34:52.827123", "implemented": false, "implemented_at": null}], "optimization_history": [], "thresholds": {"slow_component_ms": 16.0, "large_bundle_kb": 500.0, "slow_page_load_ms": 3000.0, "high_memory_mb": 100.0, "max_re_renders": 10}}