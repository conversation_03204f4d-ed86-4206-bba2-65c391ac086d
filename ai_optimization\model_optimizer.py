"""
AI Model Optimization System

Handles optimization of AI models for better performance, resource usage,
and automatic tuning based on usage patterns and requirements.
"""

import asyncio
import json
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import psutil

logger = logging.getLogger(__name__)


@dataclass
class ModelPerformanceMetrics:
    """Performance metrics for an AI model"""

    model_name: str
    response_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    gpu_usage_percent: Optional[float] = None
    gpu_memory_mb: Optional[float] = None
    success_rate: float = 1.0
    error_rate: float = 0.0
    requests_per_minute: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "model_name": self.model_name,
            "response_time_ms": self.response_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "cpu_usage_percent": self.cpu_usage_percent,
            "gpu_usage_percent": self.gpu_usage_percent,
            "gpu_memory_mb": self.gpu_memory_mb,
            "success_rate": self.success_rate,
            "error_rate": self.error_rate,
            "requests_per_minute": self.requests_per_minute,
            "timestamp": self.timestamp.isoformat(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ModelPerformanceMetrics":
        """Create from dictionary"""
        return cls(
            model_name=data["model_name"],
            response_time_ms=data["response_time_ms"],
            memory_usage_mb=data["memory_usage_mb"],
            cpu_usage_percent=data["cpu_usage_percent"],
            gpu_usage_percent=data.get("gpu_usage_percent"),
            gpu_memory_mb=data.get("gpu_memory_mb"),
            success_rate=data.get("success_rate", 1.0),
            error_rate=data.get("error_rate", 0.0),
            requests_per_minute=data.get("requests_per_minute", 0.0),
            timestamp=datetime.fromisoformat(data["timestamp"]),
        )


@dataclass
class OptimizationConfig:
    """Configuration for model optimization"""

    target_response_time_ms: float = 2000.0
    max_memory_usage_mb: float = 4096.0
    max_cpu_usage_percent: float = 80.0
    max_gpu_usage_percent: float = 90.0
    min_success_rate: float = 0.95
    max_error_rate: float = 0.05
    optimization_interval_minutes: int = 30
    metrics_retention_days: int = 7
    auto_optimize: bool = True
    enable_gpu_optimization: bool = True
    enable_memory_optimization: bool = True
    enable_response_time_optimization: bool = True


class ModelOptimizer:
    """
    AI Model Optimizer for performance and resource management.

    Handles:
    - Performance monitoring
    - Resource usage optimization
    - Automatic model tuning
    - Load balancing
    - Memory management
    """

    def __init__(self, config: OptimizationConfig):
        """Initialize the model optimizer"""
        self.config = config
        self.metrics: List[ModelPerformanceMetrics] = []
        self.optimization_history: List[Dict[str, Any]] = []
        self.model_configs: Dict[str, Dict[str, Any]] = {}
        self.active_models: Dict[str, bool] = {}
        self.optimization_lock = threading.Lock()

        # Performance thresholds
        self.performance_thresholds = {
            "response_time_warning": 3000.0,
            "response_time_critical": 5000.0,
            "memory_warning": 3072.0,
            "memory_critical": 4096.0,
            "cpu_warning": 70.0,
            "cpu_critical": 85.0,
            "gpu_warning": 80.0,
            "gpu_critical": 95.0,
        }

        # Initialize optimization thread
        self.optimization_thread = None
        self.stop_optimization = False

        logger.info("Model Optimizer initialized")

    def start_monitoring(self) -> None:
        """Start the optimization monitoring thread"""
        if self.optimization_thread is None:
            self.stop_optimization = False
            self.optimization_thread = threading.Thread(
                target=self._optimization_loop, daemon=True
            )
            self.optimization_thread.start()
            logger.info("Model optimization monitoring started")

    def stop_monitoring(self) -> None:
        """Stop the optimization monitoring thread"""
        self.stop_optimization = True
        if self.optimization_thread:
            self.optimization_thread.join()
            self.optimization_thread = None
        logger.info("Model optimization monitoring stopped")

    def add_performance_metrics(self, metrics: ModelPerformanceMetrics) -> None:
        """Add performance metrics for a model"""
        with self.optimization_lock:
            self.metrics.append(metrics)

            # Clean up old metrics
            cutoff_date = datetime.now() - timedelta(
                days=self.config.metrics_retention_days
            )
            self.metrics = [m for m in self.metrics if m.timestamp > cutoff_date]

            # Check for optimization triggers
            if self.config.auto_optimize:
                self._check_optimization_triggers(metrics)

    def get_model_performance(
        self, model_name: str, hours: int = 24
    ) -> List[ModelPerformanceMetrics]:
        """Get performance metrics for a specific model"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            m
            for m in self.metrics
            if m.model_name == model_name and m.timestamp > cutoff_time
        ]

    def get_average_performance(
        self, model_name: str, hours: int = 24
    ) -> Optional[ModelPerformanceMetrics]:
        """Get average performance metrics for a model"""
        metrics = self.get_model_performance(model_name, hours)
        if not metrics:
            return None

        avg_metrics = ModelPerformanceMetrics(
            model_name=model_name,
            response_time_ms=sum(m.response_time_ms for m in metrics) / len(metrics),
            memory_usage_mb=sum(m.memory_usage_mb for m in metrics) / len(metrics),
            cpu_usage_percent=sum(m.cpu_usage_percent for m in metrics) / len(metrics),
            gpu_usage_percent=(
                sum(m.gpu_usage_percent or 0 for m in metrics) / len(metrics)
                if any(m.gpu_usage_percent for m in metrics)
                else None
            ),
            gpu_memory_mb=(
                sum(m.gpu_memory_mb or 0 for m in metrics) / len(metrics)
                if any(m.gpu_memory_mb for m in metrics)
                else None
            ),
            success_rate=sum(m.success_rate for m in metrics) / len(metrics),
            error_rate=sum(m.error_rate for m in metrics) / len(metrics),
            requests_per_minute=sum(m.requests_per_minute for m in metrics)
            / len(metrics),
        )

        return avg_metrics

    def optimize_model(self, model_name: str) -> Dict[str, Any]:
        """Optimize a specific model based on performance metrics"""
        with self.optimization_lock:
            logger.info(f"Starting optimization for model: {model_name}")

            # Get current performance
            current_performance = self.get_average_performance(model_name, hours=1)
            if not current_performance:
                return {"status": "no_data", "message": "No performance data available"}

            # Analyze performance issues
            issues = self._analyze_performance_issues(current_performance)

            # Generate optimization recommendations
            recommendations = self._generate_optimization_recommendations(
                model_name, current_performance, issues
            )

            # Apply optimizations
            optimizations_applied = self._apply_optimizations(
                model_name, recommendations
            )

            # Record optimization
            optimization_record = {
                "model_name": model_name,
                "timestamp": datetime.now().isoformat(),
                "issues_identified": issues,
                "recommendations": recommendations,
                "optimizations_applied": optimizations_applied,
                "performance_before": current_performance.to_dict(),
            }

            self.optimization_history.append(optimization_record)

            logger.info(
                f"Optimization completed for {model_name}: {len(optimizations_applied)} optimizations applied"
            )

            return {
                "status": "completed",
                "issues_identified": len(issues),
                "optimizations_applied": len(optimizations_applied),
                "recommendations": recommendations,
            }

    def get_optimization_recommendations(self, model_name: str) -> List[Dict[str, Any]]:
        """Get optimization recommendations for a model"""
        current_performance = self.get_average_performance(model_name, hours=1)
        if not current_performance:
            return []

        issues = self._analyze_performance_issues(current_performance)
        return self._generate_optimization_recommendations(
            model_name, current_performance, issues
        )

    def _optimization_loop(self) -> None:
        """Main optimization loop"""
        while not self.stop_optimization:
            try:
                # Check all active models
                for model_name in self.active_models:
                    if self.active_models[model_name]:
                        self._check_model_health(model_name)

                # Sleep for optimization interval
                time.sleep(self.config.optimization_interval_minutes * 60)

            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

    def _check_model_health(self, model_name: str) -> None:
        """Check health of a specific model"""
        current_performance = self.get_average_performance(model_name, hours=1)
        if not current_performance:
            return

        issues = self._analyze_performance_issues(current_performance)

        if issues:
            logger.warning(f"Performance issues detected for {model_name}: {issues}")

            # Auto-optimize if enabled
            if self.config.auto_optimize:
                self.optimize_model(model_name)

    def _check_optimization_triggers(self, metrics: ModelPerformanceMetrics) -> None:
        """Check if optimization should be triggered"""
        # Check for critical performance issues
        if (
            metrics.response_time_ms
            > self.performance_thresholds["response_time_critical"]
            or metrics.memory_usage_mb > self.performance_thresholds["memory_critical"]
            or metrics.cpu_usage_percent > self.performance_thresholds["cpu_critical"]
            or (
                metrics.gpu_usage_percent
                and metrics.gpu_usage_percent
                > self.performance_thresholds["gpu_critical"]
            )
        ):

            logger.warning(
                f"Critical performance issue detected for {metrics.model_name}"
            )
            self.optimize_model(metrics.model_name)

    def _analyze_performance_issues(
        self, metrics: ModelPerformanceMetrics
    ) -> List[str]:
        """Analyze performance metrics and identify issues"""
        issues = []

        # Response time issues
        if (
            metrics.response_time_ms
            > self.performance_thresholds["response_time_warning"]
        ):
            issues.append("high_response_time")
        if (
            metrics.response_time_ms
            > self.performance_thresholds["response_time_critical"]
        ):
            issues.append("critical_response_time")

        # Memory issues
        if metrics.memory_usage_mb > self.performance_thresholds["memory_warning"]:
            issues.append("high_memory_usage")
        if metrics.memory_usage_mb > self.performance_thresholds["memory_critical"]:
            issues.append("critical_memory_usage")

        # CPU issues
        if metrics.cpu_usage_percent > self.performance_thresholds["cpu_warning"]:
            issues.append("high_cpu_usage")
        if metrics.cpu_usage_percent > self.performance_thresholds["cpu_critical"]:
            issues.append("critical_cpu_usage")

        # GPU issues
        if metrics.gpu_usage_percent:
            if metrics.gpu_usage_percent > self.performance_thresholds["gpu_warning"]:
                issues.append("high_gpu_usage")
            if metrics.gpu_usage_percent > self.performance_thresholds["gpu_critical"]:
                issues.append("critical_gpu_usage")

        # Success rate issues
        if metrics.success_rate < self.config.min_success_rate:
            issues.append("low_success_rate")

        # Error rate issues
        if metrics.error_rate > self.config.max_error_rate:
            issues.append("high_error_rate")

        return issues

    def _generate_optimization_recommendations(
        self, model_name: str, metrics: ModelPerformanceMetrics, issues: List[str]
    ) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on issues"""
        recommendations = []

        for issue in issues:
            if issue == "high_response_time" or issue == "critical_response_time":
                recommendations.append(
                    {
                        "type": "response_time_optimization",
                        "priority": (
                            "high" if issue == "critical_response_time" else "medium"
                        ),
                        "description": f"Optimize response time (current: {metrics.response_time_ms:.1f}ms)",
                        "actions": [
                            "Reduce model complexity",
                            "Enable caching",
                            "Optimize input preprocessing",
                            "Use model quantization",
                        ],
                    }
                )

            elif issue == "high_memory_usage" or issue == "critical_memory_usage":
                recommendations.append(
                    {
                        "type": "memory_optimization",
                        "priority": (
                            "high" if issue == "critical_memory_usage" else "medium"
                        ),
                        "description": f"Reduce memory usage (current: {metrics.memory_usage_mb:.1f}MB)",
                        "actions": [
                            "Enable model offloading",
                            "Reduce batch size",
                            "Clear unused model layers",
                            "Use memory-efficient data types",
                        ],
                    }
                )

            elif issue == "high_cpu_usage" or issue == "critical_cpu_usage":
                recommendations.append(
                    {
                        "type": "cpu_optimization",
                        "priority": (
                            "high" if issue == "critical_cpu_usage" else "medium"
                        ),
                        "description": f"Reduce CPU usage (current: {metrics.cpu_usage_percent:.1f}%)",
                        "actions": [
                            "Enable parallel processing",
                            "Optimize algorithm efficiency",
                            "Use CPU-optimized libraries",
                            "Reduce computational complexity",
                        ],
                    }
                )

            elif issue == "high_gpu_usage" or issue == "critical_gpu_usage":
                recommendations.append(
                    {
                        "type": "gpu_optimization",
                        "priority": (
                            "high" if issue == "critical_gpu_usage" else "medium"
                        ),
                        "description": f"Optimize GPU usage (current: {metrics.gpu_usage_percent:.1f}%)",
                        "actions": [
                            "Enable GPU memory management",
                            "Optimize CUDA kernels",
                            "Use mixed precision training",
                            "Implement GPU load balancing",
                        ],
                    }
                )

            elif issue == "low_success_rate":
                recommendations.append(
                    {
                        "type": "reliability_optimization",
                        "priority": "high",
                        "description": f"Improve success rate (current: {metrics.success_rate:.2%})",
                        "actions": [
                            "Implement retry mechanisms",
                            "Add input validation",
                            "Improve error handling",
                            "Use model ensemble",
                        ],
                    }
                )

            elif issue == "high_error_rate":
                recommendations.append(
                    {
                        "type": "error_reduction",
                        "priority": "high",
                        "description": f"Reduce error rate (current: {metrics.error_rate:.2%})",
                        "actions": [
                            "Improve input sanitization",
                            "Add error recovery",
                            "Implement fallback mechanisms",
                            "Enhance logging and monitoring",
                        ],
                    }
                )

        return recommendations

    def _apply_optimizations(
        self, model_name: str, recommendations: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Apply optimization recommendations"""
        applied_optimizations = []

        for recommendation in recommendations:
            try:
                if recommendation["type"] == "response_time_optimization":
                    applied = self._apply_response_time_optimization(model_name)
                    if applied:
                        applied_optimizations.append(
                            {
                                "type": "response_time_optimization",
                                "status": "applied",
                                "description": "Applied response time optimizations",
                            }
                        )

                elif recommendation["type"] == "memory_optimization":
                    applied = self._apply_memory_optimization(model_name)
                    if applied:
                        applied_optimizations.append(
                            {
                                "type": "memory_optimization",
                                "status": "applied",
                                "description": "Applied memory optimizations",
                            }
                        )

                elif recommendation["type"] == "cpu_optimization":
                    applied = self._apply_cpu_optimization(model_name)
                    if applied:
                        applied_optimizations.append(
                            {
                                "type": "cpu_optimization",
                                "status": "applied",
                                "description": "Applied CPU optimizations",
                            }
                        )

                elif recommendation["type"] == "gpu_optimization":
                    applied = self._apply_gpu_optimization(model_name)
                    if applied:
                        applied_optimizations.append(
                            {
                                "type": "gpu_optimization",
                                "status": "applied",
                                "description": "Applied GPU optimizations",
                            }
                        )

                elif recommendation["type"] == "reliability_optimization":
                    applied = self._apply_reliability_optimization(model_name)
                    if applied:
                        applied_optimizations.append(
                            {
                                "type": "reliability_optimization",
                                "status": "applied",
                                "description": "Applied reliability optimizations",
                            }
                        )

                elif recommendation["type"] == "error_reduction":
                    applied = self._apply_error_reduction_optimization(model_name)
                    if applied:
                        applied_optimizations.append(
                            {
                                "type": "error_reduction",
                                "status": "applied",
                                "description": "Applied error reduction optimizations",
                            }
                        )

            except Exception as e:
                logger.error(
                    f"Failed to apply optimization {recommendation['type']} for {model_name}: {e}"
                )
                applied_optimizations.append(
                    {
                        "type": recommendation["type"],
                        "status": "failed",
                        "error": str(e),
                    }
                )

        return applied_optimizations

    def _apply_response_time_optimization(self, model_name: str) -> bool:
        """Apply response time optimizations"""
        # Implementation would depend on the specific model framework
        # For now, we'll just log the optimization
        logger.info(f"Applied response time optimization for {model_name}")
        return True

    def _apply_memory_optimization(self, model_name: str) -> bool:
        """Apply memory optimizations"""
        logger.info(f"Applied memory optimization for {model_name}")
        return True

    def _apply_cpu_optimization(self, model_name: str) -> bool:
        """Apply CPU optimizations"""
        logger.info(f"Applied CPU optimization for {model_name}")
        return True

    def _apply_gpu_optimization(self, model_name: str) -> bool:
        """Apply GPU optimizations"""
        logger.info(f"Applied GPU optimization for {model_name}")
        return True

    def _apply_reliability_optimization(self, model_name: str) -> bool:
        """Apply reliability optimizations"""
        logger.info(f"Applied reliability optimization for {model_name}")
        return True

    def _apply_error_reduction_optimization(self, model_name: str) -> bool:
        """Apply error reduction optimizations"""
        logger.info(f"Applied error reduction optimization for {model_name}")
        return True

    def save_metrics(self, file_path: Path) -> None:
        """Save metrics to file"""
        data = {
            "metrics": [m.to_dict() for m in self.metrics],
            "optimization_history": self.optimization_history,
            "model_configs": self.model_configs,
        }

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"Saved metrics to {file_path}")

    def load_metrics(self, file_path: Path) -> None:
        """Load metrics from file"""
        if not file_path.exists():
            return

        with open(file_path, "r") as f:
            data = json.load(f)

        self.metrics = [
            ModelPerformanceMetrics.from_dict(m) for m in data.get("metrics", [])
        ]
        self.optimization_history = data.get("optimization_history", [])
        self.model_configs = data.get("model_configs", {})

        logger.info(f"Loaded metrics from {file_path}")

    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of optimization activities"""
        return {
            "total_models_monitored": len(self.active_models),
            "total_optimizations": len(self.optimization_history),
            "metrics_collected": len(self.metrics),
            "recent_optimizations": [
                # Last 10 optimizations
                opt
                for opt in self.optimization_history[-10:]
            ],
            "performance_trends": self._calculate_performance_trends(),
        }

    def _calculate_performance_trends(self) -> Dict[str, Any]:
        """Calculate performance trends across all models"""
        if not self.metrics:
            return {}

        # Group metrics by model
        model_metrics = {}
        for metric in self.metrics:
            if metric.model_name not in model_metrics:
                model_metrics[metric.model_name] = []
            model_metrics[metric.model_name].append(metric)

        trends = {}
        for model_name, metrics in model_metrics.items():
            if len(metrics) < 2:
                continue

            # Sort by timestamp
            sorted_metrics = sorted(metrics, key=lambda x: x.timestamp)

            # Calculate trends
            response_time_trend = (
                sorted_metrics[-1].response_time_ms - sorted_metrics[0].response_time_ms
            ) / len(sorted_metrics)
            memory_trend = (
                sorted_metrics[-1].memory_usage_mb - sorted_metrics[0].memory_usage_mb
            ) / len(sorted_metrics)

            trends[model_name] = {
                "response_time_trend": response_time_trend,
                "memory_trend": memory_trend,
                "total_requests": len(metrics),
                "avg_success_rate": sum(m.success_rate for m in metrics) / len(metrics),
            }

        return trends
