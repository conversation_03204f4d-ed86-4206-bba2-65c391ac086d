{"enabled": true, "auto_cleanup": false, "backup_before_delete": true, "dry_run_by_default": true, "exclude_patterns": [".git", ".venv", "node_modules", "dist", "build", "coverage", "htmlcov", ".pytest_cache", "__pycache__", "*.egg-info", "*.pyc", "*.pyo"], "cleanup_rules": {"duplicates": true, "obsolete": true, "legacy": true, "temp": true, "build_artifacts": true}, "duplicate_patterns": ["(.+)_v\\d+\\.py$", "(.+)_new\\.py$", "(.+)_updated\\.py$", "(.+)_copy\\.py$", "(.+)_backup\\.py$"], "obsolete_patterns": [".*\\.bak$", ".*\\.backup$", ".*_old\\.py$", ".*_deprecated\\.py$", ".*_legacy\\.py$"], "temp_patterns": [".*\\.tmp$", ".*\\.temp$", "test_.*\\.py$", ".*\\.log$", ".*\\.out$"], "build_artifacts": ["__pycache__", "*.pyc", "*.pyo", ".pytest_cache", "htmlcov", ".coverage", "dist", "build", "*.egg-info"], "cleanup_schedule": {"enabled": false, "frequency": "daily", "time": "02:00"}, "notifications": {"enabled": true, "email": false, "slack": false, "log_level": "INFO"}, "backup_settings": {"enabled": true, "backup_dir": "backups/cleanup", "retention_days": 30, "compress_backups": true}}