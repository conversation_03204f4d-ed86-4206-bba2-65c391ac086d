# Standardized Nginx Static Site Dockerfile Template
# Follows cursorrules.md Docker-First Policy requirements
# Security hardened with non-root user

# Stage 1: Builder (for static sites that need building)
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files if building is needed
COPY package.json package-lock.json* ./

# Install dependencies if package.json exists
RUN if [ -f package.json ]; then npm ci --frozen-lockfile; fi

# Copy source files
COPY . .

# Build if build script exists
RUN if [ -f package.json ] && npm run build --if-present; then echo "Build completed"; fi

# Stage 2: Runtime
FROM nginx:alpine AS runtime

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user for security
RUN addgroup --system --gid 1000 nginx-user \
    && adduser --system --uid 1000 --ingroup nginx-user nginx-user

# Copy built files or static files
COPY --from=builder /app/dist/ /usr/share/nginx/html/ 2>/dev/null || \
COPY --from=builder /app/build/ /usr/share/nginx/html/ 2>/dev/null || \
COPY --from=builder /app/public/ /usr/share/nginx/html/ 2>/dev/null || \
COPY . /usr/share/nginx/html/

# Copy custom nginx configuration if exists
COPY nginx.conf /etc/nginx/conf.d/default.conf 2>/dev/null || echo "Using default nginx config"

# Set proper permissions
RUN chown -R nginx-user:nginx-user /usr/share/nginx/html \
    && chown -R nginx-user:nginx-user /var/cache/nginx \
    && chown -R nginx-user:nginx-user /var/log/nginx \
    && chown -R nginx-user:nginx-user /etc/nginx/conf.d \
    && touch /var/run/nginx.pid \
    && chown nginx-user:nginx-user /var/run/nginx.pid

# Create nginx configuration that runs as non-root
RUN echo 'user nginx-user;' > /etc/nginx/nginx.conf && \
    echo 'worker_processes auto;' >> /etc/nginx/nginx.conf && \
    echo 'error_log /var/log/nginx/error.log warn;' >> /etc/nginx/nginx.conf && \
    echo 'pid /var/run/nginx.pid;' >> /etc/nginx/nginx.conf && \
    echo 'events { worker_connections 1024; }' >> /etc/nginx/nginx.conf && \
    echo 'http {' >> /etc/nginx/nginx.conf && \
    echo '    include /etc/nginx/mime.types;' >> /etc/nginx/nginx.conf && \
    echo '    default_type application/octet-stream;' >> /etc/nginx/nginx.conf && \
    echo '    sendfile on;' >> /etc/nginx/nginx.conf && \
    echo '    keepalive_timeout 65;' >> /etc/nginx/nginx.conf && \
    echo '    include /etc/nginx/conf.d/*.conf;' >> /etc/nginx/nginx.conf && \
    echo '}' >> /etc/nginx/nginx.conf

# Switch to non-root user
USER nginx-user

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
