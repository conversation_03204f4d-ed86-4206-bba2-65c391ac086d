import sys
import pathlib

p = pathlib.Path('docker-compose.yml')

if not p.exists():
    print("❌ docker-compose.yml not found.")
    sys.exit(1)

lines = p.read_text(encoding='utf-8', errors='replace').splitlines()

start = next((i for i, l in enumerate(lines) if l.strip().startswith('cursor_monitor:')), None)
if start is None:
    print("❌ cursor_monitor service not found.")
    sys.exit(0)

end = next((i for i in range(start+1, len(lines)) if lines[i] and not lines[i].startswith((' ', '\t'))), len(lines))

print("\n📄 Showing docker-compose section around 'cursor_monitor':\n")
for i in range(max(0, start - 10), min(len(lines), end + 40)):
    print(f"{i+1:05d}: {lines[i]}")
