# AI Coding Agent Fine-tuning System

A complete fine-tuning pipeline for customizing the AI Coding Agent with your own data.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements-fine-tuning.txt
```

### 2. Run the Test Suite
```bash
python scripts/test_fine_tuning.py
```

### 3. Prepare Your Dataset
Create a JSONL file with conversation format:
```jsonl
{"messages": [{"role": "user", "content": "How do I...?"}, {"role": "assistant", "content": "Here's how..."}]}
```

### 4. Run Complete Pipeline
```bash
python src/fine_tuning/pipeline.py run --config config/fine_tuning_config.json
```

## 📁 System Architecture

```
src/fine_tuning/
├── data_preprocessor.py    # Data cleaning and preparation
├── trainer.py              # Model training with LoRA
├── evaluator.py            # Model evaluation and metrics
└── pipeline.py             # Unified CLI interface
```

## 🎯 Features

- **LoRA Fine-tuning**: Efficient parameter-efficient training
- **Multiple Metrics**: BLEU, ROUGE, perplexity, and code quality
- **Memory Optimization**: Gradient checkpointing, 8-bit training
- **W&B Integration**: Experiment tracking and visualization
- **CLI Interface**: Easy-to-use command-line tools
- **Flexible Configuration**: JSON-based configuration
- **Sample Data**: Ready-to-use test datasets

## 📊 Dataset Format

### Conversation Format
```jsonl
{"messages": [{"role": "user", "content": "How do I create a Python function?"}, {"role": "assistant", "content": "def my_function():\n    pass"}]}
```

### Code Examples
```jsonl
{"messages": [{"role": "user", "content": "Write a function to reverse a string"}, {"role": "assistant", "content": "def reverse_string(s):\n    return s[::-1]"}]}
```

## 🔧 Configuration

Edit `config/fine_tuning_config.json`:

```json
{
  "model_name": "microsoft/DialoGPT-medium",
  "max_length": 512,
  "num_epochs": 3,
  "batch_size": 4,
  "learning_rate": 2e-4,
  "use_lora": true,
  "lora_r": 16,
  "lora_alpha": 32,
  "lora_dropout": 0.1,
  "metrics": ["bleu", "rouge", "perplexity", "code_quality"]
}
```

## 🛠️ Usage Examples

### Basic Training
```bash
# Preprocess data
python src/fine_tuning/pipeline.py preprocess \
    --input data/my_dataset.jsonl \
    --output data/processed

# Train model
python src/fine_tuning/pipeline.py train \
    --train-file data/processed/train.jsonl \
    --val-file data/processed/val.jsonl \
    --output-dir models/my_model

# Evaluate model
python src/fine_tuning/pipeline.py evaluate \
    --model-path models/my_model \
    --test-file data/processed/test.jsonl
```

### Advanced Usage
```bash
# Run complete pipeline
python src/fine_tuning/pipeline.py run \
    --config config/fine_tuning_config.json \
    --wandb-project my-ai-coding-agent

# Resume training
python src/fine_tuning/pipeline.py train \
    --resume-from models/my_model/checkpoint-1000
```

## 📈 Performance Guide

### Memory Requirements
| GPU Memory | Max Batch Size | Max Length | Notes |
|------------|----------------|------------|--------|
| 8GB        | 1-2            | 256        | Use gradient accumulation |
| 16GB       | 4-8            | 512        | Standard configuration |
| 24GB+      | 8-16           | 1024       | Full capabilities |

### Training Times (DialoGPT-medium)
| Dataset Size | 1x GPU | 4x GPU | 8x GPU |
|--------------|--------|--------|--------|
| 1K examples  | 30min  | 10min  | 5min   |
| 10K examples | 4hrs   | 1hr    | 30min  |
| 100K examples| 40hrs  | 10hrs  | 5hrs   |

## 🔍 Troubleshooting

### Common Issues

**Out of Memory**
```bash
# Reduce batch size
python src/fine_tuning/pipeline.py train --batch-size 1 --gradient-accumulation-steps 8

# Enable CPU offloading
python src/fine_tuning/pipeline.py train --cpu-offload
```

**Slow Training**
```bash
# Use mixed precision
python src/fine_tuning/pipeline.py train --fp16

# Multi-GPU training
accelerate launch src/fine_tuning/pipeline.py train --multi-gpu
```

**Poor Results**
- Increase training epochs
- Adjust learning rate (try 1e-4 to 5e-4)
- Check data quality and format
- Increase LoRA rank (r=32 or r=64)

## 🧪 Testing

### Run All Tests
```bash
python scripts/test_fine_tuning.py
```

### Individual Component Tests
```bash
# Test data preprocessing
python -m pytest tests/test_data_preprocessor.py -v

# Test trainer
python -m pytest tests/test_trainer.py -v

# Test evaluator
python -m pytest tests/test_evaluator.py -v
```

## 📋 Checklist

Before starting fine-tuning:
- [ ] Install dependencies: `pip install -r requirements-fine-tuning.txt`
- [ ] Prepare dataset in JSONL format
- [ ] Review configuration in `config/fine_tuning_config.json`
- [ ] Check GPU availability: `nvidia-smi`
- [ ] Run test suite: `python scripts/test_fine_tuning.py`
- [ ] Ensure sufficient disk space (10GB+ for models)

## 🔄 Model Deployment

After fine-tuning, deploy your model:

```python
from model_router import ModelRouter

router = ModelRouter()
router.load_fine_tuned_model("models/my_fine_tuned_model")
```

## 📚 Additional Resources

- [Fine-tuning Guide](docs/FINE_TUNING_GUIDE.md) - Detailed usage instructions
- [API Reference](docs/API_REFERENCE.md) - Complete API documentation
- [Sample Dataset](data/fine_tuning/sample_dataset.jsonl) - Example training data
- [Configuration Examples](config/fine_tuning_config.json) - Ready-to-use configs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Run the test suite
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
