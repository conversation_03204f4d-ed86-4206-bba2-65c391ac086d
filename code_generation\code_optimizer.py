"""
Code Optimizer Mo<PERSON>le

Analyzes code performance, suggests optimizations, and provides
refactoring recommendations for improved efficiency and maintainability.

Phase 19 Implementation - Enhanced Code Generation
"""

import ast
import logging
import re
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class OptimizationSuggestion:
    """Represents a code optimization suggestion."""

    id: str
    type: str  # "performance", "memory", "readability", "maintainability"
    priority: str  # "high", "medium", "low"
    title: str
    description: str
    current_code: str
    suggested_code: str
    improvement_estimate: str
    confidence: float
    line_numbers: List[int]
    category: str


@dataclass
class OptimizationResult:
    """Result of code optimization analysis."""

    suggestions: List[OptimizationSuggestion]
    performance_score: float
    memory_score: float
    readability_score: float
    maintainability_score: float
    overall_score: float
    improvements: Dict[str, Any]


class CodeOptimizer:
    """
    Advanced code optimization system for multiple programming languages.

    Provides:
    - Performance analysis and suggestions
    - Memory usage optimization
    - Code readability improvements
    - Maintainability enhancements
    - Refactoring recommendations
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the code optimizer.

        Args:
            config: Configuration dictionary for optimization settings
        """
        self.config = config or {}
        self.supported_languages = ["python", "javascript", "typescript", "java", "cpp"]
        self.health_status = "healthy"

        # Load optimization patterns
        self.performance_patterns = self._load_performance_patterns()
        self.memory_patterns = self._load_memory_patterns()
        self.readability_patterns = self._load_readability_patterns()

        logger.info("Code Optimizer initialized successfully")

    async def optimize_code(
        self,
        code: str,
        language: str,
        analysis_results: Optional[Dict[str, Any]] = None,
        file_path: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Analyze code and provide optimization suggestions.

        Args:
            code: Source code to optimize
            language: Programming language
            analysis_results: Optional results from code analysis
            file_path: Optional file path for context

        Returns:
            Dictionary with optimization results
        """
        try:
            if language not in self.supported_languages:
                raise ValueError(f"Unsupported language: {language}")

            # Generate optimization suggestions
            suggestions = await self._generate_optimization_suggestions(
                code, language, analysis_results
            )

            # Calculate optimization scores
            performance_score = self._calculate_performance_score(
                code, language, suggestions
            )
            memory_score = self._calculate_memory_score(code, language, suggestions)
            readability_score = self._calculate_readability_score(
                code, language, suggestions
            )
            maintainability_score = self._calculate_maintainability_score(
                code, language, suggestions
            )

            # Calculate overall score
            overall_score = self._calculate_overall_score(
                performance_score,
                memory_score,
                readability_score,
                maintainability_score,
            )

            # Prepare improvements summary
            improvements = self._prepare_improvements_summary(suggestions)

            result = OptimizationResult(
                suggestions=suggestions,
                performance_score=performance_score,
                memory_score=memory_score,
                readability_score=readability_score,
                maintainability_score=maintainability_score,
                overall_score=overall_score,
                improvements=improvements,
            )

            return {
                "suggestions": [self._suggestion_to_dict(s) for s in suggestions],
                "performance_score": result.performance_score,
                "memory_score": result.memory_score,
                "readability_score": result.readability_score,
                "maintainability_score": result.maintainability_score,
                "overall_score": result.overall_score,
                "improvements": result.improvements,
                "total_suggestions": len(suggestions),
                "high_priority_count": len(
                    [s for s in suggestions if s.priority == "high"]
                ),
                "medium_priority_count": len(
                    [s for s in suggestions if s.priority == "medium"]
                ),
                "low_priority_count": len(
                    [s for s in suggestions if s.priority == "low"]
                ),
                "language": language,
                "file_path": file_path,
            }

        except Exception as e:
            logger.error(f"Error optimizing code: {e}")
            raise

    async def _generate_optimization_suggestions(
        self, code: str, language: str, analysis_results: Optional[Dict[str, Any]]
    ) -> List[OptimizationSuggestion]:
        """Generate optimization suggestions for the code."""
        suggestions = []

        # Generate language-specific suggestions
        if language == "python":
            suggestions.extend(
                await self._generate_python_suggestions(code, analysis_results)
            )
        elif language in ["javascript", "typescript"]:
            suggestions.extend(
                await self._generate_javascript_suggestions(
                    code, language, analysis_results
                )
            )
        elif language == "java":
            suggestions.extend(
                await self._generate_java_suggestions(code, analysis_results)
            )
        elif language == "cpp":
            suggestions.extend(
                await self._generate_cpp_suggestions(code, analysis_results)
            )

        # Generate generic suggestions
        suggestions.extend(
            await self._generate_generic_suggestions(code, language, analysis_results)
        )

        return suggestions

    async def _generate_python_suggestions(
        self, code: str, analysis_results: Optional[Dict[str, Any]]
    ) -> List[OptimizationSuggestion]:
        """Generate Python-specific optimization suggestions."""
        suggestions = []
        lines = code.split("\n")

        # Check for list comprehensions vs loops
        if "for " in code and "append" in code:
            suggestion = OptimizationSuggestion(
                id="list_comprehension",
                type="performance",
                priority="medium",
                title="Use list comprehension instead of loop with append",
                description="List comprehensions are generally faster than loops with append",
                current_code="for item in items:\n    result.append(process(item))",
                suggested_code="result = [process(item) for item in items]",
                improvement_estimate="20-30% performance improvement",
                confidence=0.8,
                line_numbers=[1],
                category="performance",
            )
            suggestions.append(suggestion)

        # Check for string concatenation
        if code.count("+") > 5 and "str" in code:
            suggestion = OptimizationSuggestion(
                id="string_join",
                type="performance",
                priority="medium",
                title="Use str.join() for string concatenation",
                description="str.join() is more efficient than + for multiple strings",
                current_code="result = str1 + str2 + str3",
                suggested_code="result = ''.join([str1, str2, str3])",
                improvement_estimate="15-25% performance improvement",
                confidence=0.7,
                line_numbers=[1],
                category="performance",
            )
            suggestions.append(suggestion)

        # Check for dictionary comprehensions
        if "for " in code and "dict[" in code:
            suggestion = OptimizationSuggestion(
                id="dict_comprehension",
                type="performance",
                priority="low",
                title="Use dictionary comprehension",
                description="Dictionary comprehensions are more readable and efficient",
                current_code="for key, value in items:\n    result[key] = process(value)",
                suggested_code="result = {key: process(value) for key, value in items}",
                improvement_estimate="10-15% performance improvement",
                confidence=0.6,
                line_numbers=[1],
                category="performance",
            )
            suggestions.append(suggestion)

        # Check for generator expressions
        if "sum(" in code and "for " in code:
            suggestion = OptimizationSuggestion(
                id="generator_expression",
                type="memory",
                priority="medium",
                title="Use generator expression for large datasets",
                description="Generator expressions use less memory than list comprehensions",
                current_code="sum([x**2 for x in large_list])",
                suggested_code="sum(x**2 for x in large_list)",
                improvement_estimate="50-80% memory reduction",
                confidence=0.9,
                line_numbers=[1],
                category="memory",
            )
            suggestions.append(suggestion)

        return suggestions

    async def _generate_javascript_suggestions(
        self, code: str, language: str, analysis_results: Optional[Dict[str, Any]]
    ) -> List[OptimizationSuggestion]:
        """Generate JavaScript/TypeScript-specific optimization suggestions."""
        suggestions = []

        # Check for array methods
        if "for " in code and "push" in code:
            suggestion = OptimizationSuggestion(
                id="array_methods",
                type="performance",
                priority="medium",
                title="Use array methods instead of loops",
                description="Array methods like map, filter, reduce are more functional and often faster",
                current_code="for (let i = 0; i < items.length; i++) {\n    result.push(process(items[i]));\n}",
                suggested_code="const result = items.map(process);",
                improvement_estimate="10-20% performance improvement",
                confidence=0.7,
                line_numbers=[1],
                category="performance",
            )
            suggestions.append(suggestion)

        # Check for template literals
        if code.count("+") > 3 and '"' in code:
            suggestion = OptimizationSuggestion(
                id="template_literals",
                type="readability",
                priority="low",
                title="Use template literals for string interpolation",
                description="Template literals are more readable than string concatenation",
                current_code='const message = "Hello " + name + ", you are " + age + " years old";',
                suggested_code="const message = `Hello ${name}, you are ${age} years old`;",
                improvement_estimate="Improved readability",
                confidence=0.8,
                line_numbers=[1],
                category="readability",
            )
            suggestions.append(suggestion)

        # Check for destructuring
        if "obj." in code and code.count("obj.") > 2:
            suggestion = OptimizationSuggestion(
                id="destructuring",
                type="readability",
                priority="low",
                title="Use destructuring for object properties",
                description="Destructuring makes code more readable and concise",
                current_code="const name = obj.name;\nconst age = obj.age;\nconst city = obj.city;",
                suggested_code="const { name, age, city } = obj;",
                improvement_estimate="Improved readability",
                confidence=0.8,
                line_numbers=[1],
                category="readability",
            )
            suggestions.append(suggestion)

        return suggestions

    async def _generate_java_suggestions(
        self, code: str, analysis_results: Optional[Dict[str, Any]]
    ) -> List[OptimizationSuggestion]:
        """Generate Java-specific optimization suggestions."""
        suggestions = []

        # Check for StringBuilder usage
        if "String" in code and "for" in code and "+" in code:
            suggestion = OptimizationSuggestion(
                id="stringbuilder",
                type="performance",
                priority="high",
                title="Use StringBuilder for string concatenation in loops",
                description="StringBuilder is much more efficient than String concatenation in loops",
                current_code='String result = "";\nfor (String item : items) {\n    result += item;\n}',
                suggested_code="StringBuilder result = new StringBuilder();\nfor (String item : items) {\n    result.append(item);\n}\nString finalResult = result.toString();",
                improvement_estimate="80-90% performance improvement",
                confidence=0.9,
                line_numbers=[1],
                category="performance",
            )
            suggestions.append(suggestion)

        # Check for enhanced for loops
        if "for (int i = 0; i <" in code:
            suggestion = OptimizationSuggestion(
                id="enhanced_for_loop",
                type="readability",
                priority="low",
                title="Use enhanced for loop when possible",
                description="Enhanced for loops are more readable and less error-prone",
                current_code="for (int i = 0; i < items.length; i++) {\n    process(items[i]);\n}",
                suggested_code="for (Item item : items) {\n    process(item);\n}",
                improvement_estimate="Improved readability",
                confidence=0.8,
                line_numbers=[1],
                category="readability",
            )
            suggestions.append(suggestion)

        return suggestions

    async def _generate_cpp_suggestions(
        self, code: str, analysis_results: Optional[Dict[str, Any]]
    ) -> List[OptimizationSuggestion]:
        """Generate C++-specific optimization suggestions."""
        suggestions = []

        # Check for const references
        if "std::string" in code and "=" in code:
            suggestion = OptimizationSuggestion(
                id="const_reference",
                type="performance",
                priority="medium",
                title="Use const references to avoid copying",
                description="Passing by const reference avoids unnecessary copying",
                current_code="void process(std::string data) { ... }",
                suggested_code="void process(const std::string& data) { ... }",
                improvement_estimate="Avoid copying overhead",
                confidence=0.8,
                line_numbers=[1],
                category="performance",
            )
            suggestions.append(suggestion)

        # Check for range-based for loops
        if "for (int i = 0; i <" in code:
            suggestion = OptimizationSuggestion(
                id="range_based_for",
                type="readability",
                priority="low",
                title="Use range-based for loop",
                description="Range-based for loops are more readable and less error-prone",
                current_code="for (int i = 0; i < items.size(); i++) {\n    process(items[i]);\n}",
                suggested_code="for (const auto& item : items) {\n    process(item);\n}",
                improvement_estimate="Improved readability",
                confidence=0.8,
                line_numbers=[1],
                category="readability",
            )
            suggestions.append(suggestion)

        return suggestions

    async def _generate_generic_suggestions(
        self, code: str, language: str, analysis_results: Optional[Dict[str, Any]]
    ) -> List[OptimizationSuggestion]:
        """Generate generic optimization suggestions for any language."""
        suggestions = []

        # Check for long functions
        lines = code.split("\n")
        if len(lines) > 30:
            suggestion = OptimizationSuggestion(
                id="long_function",
                type="maintainability",
                priority="medium",
                title="Break down long function",
                description="Long functions are harder to understand and maintain",
                current_code="// Function with many lines...",
                suggested_code="// Split into smaller, focused functions",
                improvement_estimate="Improved maintainability",
                confidence=0.7,
                line_numbers=list(range(1, len(lines) + 1)),
                category="maintainability",
            )
            suggestions.append(suggestion)

        # Check for deep nesting
        max_nesting = self._calculate_max_nesting(code)
        if max_nesting > 4:
            suggestion = OptimizationSuggestion(
                id="deep_nesting",
                type="readability",
                priority="medium",
                title="Reduce deep nesting",
                description="Deep nesting makes code harder to read and understand",
                current_code="// Deeply nested code...",
                suggested_code="// Use early returns or extract methods",
                improvement_estimate="Improved readability",
                confidence=0.8,
                line_numbers=[1],
                category="readability",
            )
            suggestions.append(suggestion)

        # Check for magic numbers
        magic_numbers = re.findall(r"\b\d{3,}\b", code)
        if magic_numbers:
            suggestion = OptimizationSuggestion(
                id="magic_numbers",
                type="maintainability",
                priority="low",
                title="Replace magic numbers with named constants",
                description="Magic numbers make code harder to understand and maintain",
                current_code="if (value > 1000) { ... }",
                suggested_code="const int MAX_VALUE = 1000;\nif (value > MAX_VALUE) { ... }",
                improvement_estimate="Improved maintainability",
                confidence=0.6,
                line_numbers=[1],
                category="maintainability",
            )
            suggestions.append(suggestion)

        return suggestions

    def _calculate_max_nesting(self, code: str) -> int:
        """Calculate the maximum nesting level in the code."""
        max_nesting = 0
        current_nesting = 0

        for char in code:
            if char in "{([":
                current_nesting += 1
                max_nesting = max(max_nesting, current_nesting)
            elif char in "})]":
                current_nesting = max(0, current_nesting - 1)

        return max_nesting

    def _calculate_performance_score(
        self, code: str, language: str, suggestions: List[OptimizationSuggestion]
    ) -> float:
        """Calculate performance optimization score."""
        performance_suggestions = [s for s in suggestions if s.type == "performance"]

        if not performance_suggestions:
            return 0.8  # Good performance if no suggestions

        # Weight by priority
        high_priority = len(
            [s for s in performance_suggestions if s.priority == "high"]
        )
        medium_priority = len(
            [s for s in performance_suggestions if s.priority == "medium"]
        )
        low_priority = len([s for s in performance_suggestions if s.priority == "low"])

        # Calculate score (fewer suggestions = better score)
        score = 1.0 - (
            high_priority * 0.1 + medium_priority * 0.05 + low_priority * 0.02
        )
        return max(0.0, min(1.0, score))

    def _calculate_memory_score(
        self, code: str, language: str, suggestions: List[OptimizationSuggestion]
    ) -> float:
        """Calculate memory optimization score."""
        memory_suggestions = [s for s in suggestions if s.type == "memory"]

        if not memory_suggestions:
            return 0.8  # Good memory usage if no suggestions

        # Weight by priority
        high_priority = len([s for s in memory_suggestions if s.priority == "high"])
        medium_priority = len([s for s in memory_suggestions if s.priority == "medium"])
        low_priority = len([s for s in memory_suggestions if s.priority == "low"])

        score = 1.0 - (
            high_priority * 0.1 + medium_priority * 0.05 + low_priority * 0.02
        )
        return max(0.0, min(1.0, score))

    def _calculate_readability_score(
        self, code: str, language: str, suggestions: List[OptimizationSuggestion]
    ) -> float:
        """Calculate readability score."""
        readability_suggestions = [s for s in suggestions if s.type == "readability"]

        if not readability_suggestions:
            return 0.9  # Good readability if no suggestions

        # Weight by priority
        high_priority = len(
            [s for s in readability_suggestions if s.priority == "high"]
        )
        medium_priority = len(
            [s for s in readability_suggestions if s.priority == "medium"]
        )
        low_priority = len([s for s in readability_suggestions if s.priority == "low"])

        score = 1.0 - (
            high_priority * 0.08 + medium_priority * 0.04 + low_priority * 0.02
        )
        return max(0.0, min(1.0, score))

    def _calculate_maintainability_score(
        self, code: str, language: str, suggestions: List[OptimizationSuggestion]
    ) -> float:
        """Calculate maintainability score."""
        maintainability_suggestions = [
            s for s in suggestions if s.type == "maintainability"
        ]

        if not maintainability_suggestions:
            return 0.8  # Good maintainability if no suggestions

        # Weight by priority
        high_priority = len(
            [s for s in maintainability_suggestions if s.priority == "high"]
        )
        medium_priority = len(
            [s for s in maintainability_suggestions if s.priority == "medium"]
        )
        low_priority = len(
            [s for s in maintainability_suggestions if s.priority == "low"]
        )

        score = 1.0 - (
            high_priority * 0.1 + medium_priority * 0.05 + low_priority * 0.02
        )
        return max(0.0, min(1.0, score))

    def _calculate_overall_score(
        self,
        performance_score: float,
        memory_score: float,
        readability_score: float,
        maintainability_score: float,
    ) -> float:
        """Calculate overall optimization score."""
        # Weighted average of all scores
        weights = {
            "performance": 0.3,
            "memory": 0.2,
            "readability": 0.25,
            "maintainability": 0.25,
        }

        overall_score = (
            performance_score * weights["performance"]
            + memory_score * weights["memory"]
            + readability_score * weights["readability"]
            + maintainability_score * weights["maintainability"]
        )

        return round(overall_score, 3)

    def _prepare_improvements_summary(
        self, suggestions: List[OptimizationSuggestion]
    ) -> Dict[str, Any]:
        """Prepare a summary of potential improvements."""
        performance_improvements = [s for s in suggestions if s.type == "performance"]
        memory_improvements = [s for s in suggestions if s.type == "memory"]
        readability_improvements = [s for s in suggestions if s.type == "readability"]
        maintainability_improvements = [
            s for s in suggestions if s.type == "maintainability"
        ]

        return {
            "performance": {
                "count": len(performance_improvements),
                "high_priority": len(
                    [s for s in performance_improvements if s.priority == "high"]
                ),
                "estimated_improvement": "10-30% performance gain",
            },
            "memory": {
                "count": len(memory_improvements),
                "high_priority": len(
                    [s for s in memory_improvements if s.priority == "high"]
                ),
                "estimated_improvement": "20-80% memory reduction",
            },
            "readability": {
                "count": len(readability_improvements),
                "high_priority": len(
                    [s for s in readability_improvements if s.priority == "high"]
                ),
                "estimated_improvement": "Improved code clarity",
            },
            "maintainability": {
                "count": len(maintainability_improvements),
                "high_priority": len(
                    [s for s in maintainability_improvements if s.priority == "high"]
                ),
                "estimated_improvement": "Easier to maintain and extend",
            },
        }

    def _suggestion_to_dict(self, suggestion: OptimizationSuggestion) -> Dict[str, Any]:
        """Convert OptimizationSuggestion object to dictionary."""
        return {
            "id": suggestion.id,
            "type": suggestion.type,
            "priority": suggestion.priority,
            "title": suggestion.title,
            "description": suggestion.description,
            "current_code": suggestion.current_code,
            "suggested_code": suggestion.suggested_code,
            "improvement_estimate": suggestion.improvement_estimate,
            "confidence": suggestion.confidence,
            "line_numbers": suggestion.line_numbers,
            "category": suggestion.category,
        }

    def _load_performance_patterns(self) -> Dict[str, List[str]]:
        """Load performance optimization patterns."""
        return {
            "python": ["list_comprehension", "string_join", "generator_expression"],
            "javascript": ["array_methods", "template_literals"],
            "java": ["stringbuilder", "enhanced_for_loop"],
            "cpp": ["const_reference", "range_based_for"],
        }

    def _load_memory_patterns(self) -> Dict[str, List[str]]:
        """Load memory optimization patterns."""
        return {
            "python": ["generator_expression", "lazy_evaluation"],
            "javascript": ["weak_map", "weak_set"],
            "java": ["stream_api", "lazy_evaluation"],
            "cpp": ["smart_pointers", "move_semantics"],
        }

    def _load_readability_patterns(self) -> Dict[str, List[str]]:
        """Load readability improvement patterns."""
        return {
            "python": ["type_hints", "docstrings", "early_returns"],
            "javascript": ["destructuring", "template_literals", "arrow_functions"],
            "java": ["enhanced_for_loop", "lambda_expressions"],
            "cpp": ["range_based_for", "auto_keyword"],
        }

    def get_health_status(self) -> Dict[str, Any]:
        """
        Get the health status of the code optimizer.

        Returns:
            Dictionary with health status information
        """
        return {
            "status": self.health_status,
            "supported_languages": self.supported_languages,
            "performance_patterns_count": sum(
                len(patterns) for patterns in self.performance_patterns.values()
            ),
            "memory_patterns_count": sum(
                len(patterns) for patterns in self.memory_patterns.values()
            ),
            "readability_patterns_count": sum(
                len(patterns) for patterns in self.readability_patterns.values()
            ),
            "config": self.config,
        }
