{
  "_comment": "YAML/Docker Auto-Fix Configuration - Comprehensive YAML and Docker fixing system",
  "_version": "1.0.0",
  "_last_updated": "2025-08-06",

  "yaml_docker_fixer": {
    "enabled": true,
    "strict_mode": true,
    "aggressive_mode": true,
    "max_retries": 3,
    "cooldown_seconds": 30,
    "verify_fixes": true,
    "backup_before_fix": false,
    "parallel_fixes": false,
    "stop_on_first_failure": false,
    "retry_failed_fixes": true,
    "log_all_attempts": true
  },

  "dependencies": {
    "required_packages": [
      "PyYAML>=5.4,<7.0",
      "ruamel.yaml>=0.17.0"
    ],
    "optional_packages": [
      "kubernetes>=26.0.0",
      "docker-compose>=1.29.0"
    ],
    "auto_install": true,
    "install_timeout": 300
  },

  "encoding": {
    "default_encoding": "utf-8",
    "fallback_encodings": [
      "utf-8-sig",
      "latin-1",
      "cp1252"
    ],
    "force_encoding": false,
    "detect_encoding": true
  },

  "file_patterns": {
    "yaml_files": [
      "*.yml",
      "*.yaml",
      "*.yml.j2",
      "*.yaml.j2"
    ],
    "docker_files": [
      "Dockerfile*",
      "docker-compose*.yml",
      "docker-compose*.yaml",
      "compose*.yml",
      "compose*.yaml"
    ],
    "kubernetes_files": [
      "*.k8s.yml",
      "*.k8s.yaml",
      "deployment*.yml",
      "deployment*.yaml",
      "service*.yml",
      "service*.yaml",
      "ingress*.yml",
      "ingress*.yaml",
      "configmap*.yml",
      "configmap*.yaml",
      "secret*.yml",
      "secret*.yaml"
    ],
    "cicd_files": [
      ".github/workflows/*.yml",
      ".github/workflows/*.yaml",
      ".gitlab-ci.yml",
      "azure-pipelines.yml",
      "azure-pipelines.yaml"
    ]
  },

  "fixes": {
    "docker_compose": {
      "enabled": true,
      "priority": "high",
      "max_attempts": 3,
      "actions": [
        "fix_missing_version",
        "fix_missing_services",
        "fix_service_config",
        "fix_volumes_section",
        "fix_networks_section",
        "fix_port_formats",
        "fix_restart_policies",
        "fix_environment_variables",
        "fix_health_checks"
      ],
      "defaults": {
        "version": "3.8",
        "restart_policy": "unless-stopped",
        "health_check_interval": "30s",
        "health_check_timeout": "10s",
        "health_check_retries": 3
      }
    },

    "kubernetes": {
      "enabled": true,
      "priority": "high",
      "max_attempts": 3,
      "actions": [
        "fix_missing_api_version",
        "fix_missing_kind",
        "fix_missing_metadata",
        "fix_missing_spec",
        "fix_resource_limits",
        "fix_security_context",
        "fix_liveness_probe",
        "fix_readiness_probe"
      ],
      "defaults": {
        "api_version": "apps/v1",
        "kind": "Deployment",
        "replicas": 1,
        "strategy_type": "RollingUpdate"
      }
    },

    "cicd_pipelines": {
      "enabled": true,
      "priority": "medium",
      "max_attempts": 2,
      "actions": [
        "fix_missing_name",
        "fix_missing_triggers",
        "fix_missing_jobs",
        "fix_job_configuration",
        "fix_step_configuration",
        "fix_environment_variables"
      ],
      "defaults": {
        "name": "CI/CD Pipeline",
        "triggers": ["push", "pull_request"],
        "runs_on": "ubuntu-latest"
      }
    },

    "generic_yaml": {
      "enabled": true,
      "priority": "low",
      "max_attempts": 2,
      "actions": [
        "fix_syntax_errors",
        "fix_indentation",
        "fix_line_endings",
        "fix_encoding_issues",
        "fix_quotes",
        "fix_anchors_aliases"
      ]
    }
  },

  "performance": {
    "cache_enabled": true,
    "cache_ttl": 3600,
    "parallel_processing": false,
    "max_workers": 4,
    "chunk_size": 100,
    "memory_limit_mb": 512,
    "timeout_seconds": 300
  },

  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file_logging": true,
    "log_file": "logs/yaml_docker_fixer.log",
    "max_log_size_mb": 10,
    "backup_count": 5
  },

  "backup": {
    "enabled": false,  // Disabled - using Git for version control
    "backup_before_fix": false,  // Disabled - using Git for version control
    "backup_directory": ".yaml_docker_backups",
    "max_backups": 10,
    "compress_backups": true,
    "cleanup_old_backups": true
  },

  "validation": {
    "validate_before_fix": true,
    "validate_after_fix": true,
    "strict_validation": false,
    "custom_validators": [],
    "ignore_validation_errors": []
  },

  "rollback": {
    "enabled": true,
    "auto_rollback_on_failure": true,
    "rollback_points": true,
    "max_rollback_points": 5,
    "rollback_on_critical_errors": true
  },

  "notifications": {
    "enabled": true,
    "notify_on_success": false,
    "notify_on_failure": true,
    "notify_on_critical_errors": true,
    "notification_channels": ["log", "console"]
  },

  "monitoring": {
    "enabled": true,
    "metrics_collection": true,
    "performance_tracking": true,
    "error_tracking": true,
    "success_rate_tracking": true
  }
}
