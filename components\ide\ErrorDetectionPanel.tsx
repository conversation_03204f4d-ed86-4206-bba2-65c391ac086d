import React, { useState, useEffect, useCallback } from 'react';
import {
  Search,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Play,
  Square,
  Download,
  History,
  Settings,
  Eye,
  Wrench,
  Shield,
  Zap,
  Globe,
  FileText
} from 'lucide-react';
import {
  errorDetectionService,
  WebError,
  ScanReport,
  FixReport,
  ErrorDetectionStatus
} from '@/services/ErrorDetectionService';
import toast from 'react-hot-toast';

interface ErrorDetectionPanelProps {
  projectPath?: string;
  onErrorSelect?: (error: WebError) => void;
}

export const ErrorDetectionPanel: React.FC<ErrorDetectionPanelProps> = ({
  projectPath = '.',
  onErrorSelect
}) => {
  const [status, setStatus] = useState<ErrorDetectionStatus | null>(null);
  const [scanReport, setScanReport] = useState<ScanReport | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isFixing, setIsFixing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'errors' | 'history' | 'settings'>('overview');

  // Load initial status
  useEffect(() => {
    loadStatus();
  }, [projectPath]);

  const loadStatus = useCallback(async () => {
    try {
      const currentStatus = await errorDetectionService.getStatus(projectPath);
      setStatus(currentStatus);
    } catch (error) {
      toast.error('Error loading error detection status.');
    }
  }, [projectPath]);

  const handleScan = useCallback(async () => {
    setIsScanning(true);
    try {
      const report = await errorDetectionService.scanProject(projectPath);
      setScanReport(report);
      await loadStatus(); // Refresh status after scan
      toast.success('Project scan complete.');
    } catch (error) {
      toast.error('Error scanning project.');
    } finally {
      setIsScanning(false);
    }
  }, [projectPath, loadStatus]);

  const handleAutoFix = useCallback(async (riskLevel: 'safe' | 'moderate' | 'risky' = 'moderate') => {
    setIsFixing(true);
    try {
      const report: FixReport = await errorDetectionService.autoFixErrors(projectPath, riskLevel);
      if (report.success) {
        // Refresh data after successful fix
        await loadStatus();
        if (scanReport) {
          await handleScan(); // Re-scan to get updated errors
        }
        toast.success('Auto-fix completed.');
      }
      // Show success/error message
      // console.log(report.message);
    } catch (error) {
      toast.error('Error auto-fixing errors.');
    } finally {
      setIsFixing(false);
    }
  }, [projectPath, loadStatus, scanReport, handleScan]);

  const handleErrorClick = useCallback((error: WebError) => {
    if (onErrorSelect) {
      onErrorSelect(error);
    }
  }, [onErrorSelect]);

  const getFilteredErrors = useCallback(() => {
    if (!scanReport?.errors) return [];

    return scanReport.errors.filter(error => {
      // Category filter
      if (selectedCategory !== 'all' && error.category !== selectedCategory) {
        return false;
      }

      // Severity filter
      if (selectedSeverity !== 'all' && error.severity !== selectedSeverity) {
        return false;
      }

      // Search filter
      if (searchTerm && !error.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !error.description.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [scanReport, selectedCategory, selectedSeverity, searchTerm]);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'high': return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'medium': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'low': return <CheckCircle className="w-4 h-4 text-green-500" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'accessibility': return <Eye className="w-4 h-4" />;
      case 'seo': return <Search className="w-4 h-4" />;
      case 'performance': return <Zap className="w-4 h-4" />;
      case 'security': return <Shield className="w-4 h-4" />;
      case 'compatibility': return <Globe className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const renderOverview = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {status?.total_errors || 0}
          </div>
          <div className="text-sm text-blue-600">Total Errors</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {status?.auto_fixable_count || 0}
          </div>
          <div className="text-sm text-green-600">Auto-Fixable</div>
        </div>
      </div>

              {status?.errors_by_category && status.errors_by_category && Object.keys(status.errors_by_category).length > 0 && (
        <div>
          <h3 className="font-semibold mb-2">Errors by Category</h3>
          <div className="space-y-2">
            {Object.entries(status.errors_by_category).map(([category, count]) => (
              <div key={category} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <span className="capitalize">{category}</span>
                </div>
                <span className="font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

              {status?.errors_by_severity && status.errors_by_severity && Object.keys(status.errors_by_severity).length > 0 && (
        <div>
          <h3 className="font-semibold mb-2">Errors by Severity</h3>
          <div className="space-y-2">
            {Object.entries(status.errors_by_severity).map(([severity, count]) => (
              <div key={severity} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div className="flex items-center space-x-2">
                  {getSeverityIcon(severity)}
                  <span className="capitalize">{severity}</span>
                </div>
                <span className="font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex space-x-2">
        <button
          onClick={handleScan}
          disabled={isScanning}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isScanning ? <Square className="w-4 h-4" /> : <Search className="w-4 h-4" />}
          <span>{isScanning ? 'Scanning...' : 'Scan Project'}</span>
        </button>

        {status?.auto_fixable_count && status.auto_fixable_count > 0 && (
          <button
            onClick={() => handleAutoFix('safe')}
            disabled={isFixing}
            className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            <Wrench className="w-4 h-4" />
            <span>{isFixing ? 'Fixing...' : 'Auto-Fix'}</span>
          </button>
        )}
      </div>
    </div>
  );

  const renderErrors = () => (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex space-x-2">
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-1 border rounded text-sm"
        >
          <option value="all">All Categories</option>
          <option value="accessibility">Accessibility</option>
          <option value="seo">SEO</option>
          <option value="performance">Performance</option>
          <option value="security">Security</option>
          <option value="compatibility">Compatibility</option>
        </select>

        <select
          value={selectedSeverity}
          onChange={(e) => setSelectedSeverity(e.target.value)}
          className="px-3 py-1 border rounded text-sm"
        >
          <option value="all">All Severities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>

        <input
          type="text"
          placeholder="Search errors..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-3 py-1 border rounded text-sm flex-1"
        />
      </div>

      {/* Error List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {getFilteredErrors().map((error) => (
          <div
            key={error.error_id}
            onClick={() => handleErrorClick(error)}
            className="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-start space-x-3">
              {getSeverityIcon(error.severity)}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h4 className="font-medium text-sm">{error.title}</h4>
                  <div className="flex items-center space-x-1">
                    {getCategoryIcon(error.category)}
                    <span className="text-xs text-gray-500 capitalize">{error.category}</span>
                  </div>
                  {error.auto_fixable && (
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                      Auto-fixable
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-2">{error.description}</p>
                {error.file_path && (
                  <div className="text-xs text-gray-500">
                    {error.file_path}
                    {error.line_number && `:${error.line_number}`}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}

        {getFilteredErrors().length === 0 && (
          <div className="text-center py-8 text-gray-500">
            {scanReport ? 'No errors found matching the current filters.' : 'Run a scan to detect errors.'}
          </div>
        )}
      </div>
    </div>
  );

  const renderHistory = () => (
    <div className="space-y-4">
      <div className="text-center py-8 text-gray-500">
        Fix history will be displayed here.
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-4">
      <div>
        <h3 className="font-semibold mb-2">Auto-Fix Settings</h3>
        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Enable automatic backups before fixes</span>
          </label>
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Validate fixes after application</span>
          </label>
          <label className="flex items-center space-x-2">
            <input type="checkbox" className="rounded" />
            <span className="text-sm">Auto-rollback on validation failure</span>
          </label>
        </div>
      </div>

      <div>
        <h3 className="font-semibold mb-2">Scan Settings</h3>
        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Include accessibility checks</span>
          </label>
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Include SEO checks</span>
          </label>
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Include performance checks</span>
          </label>
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Include security checks</span>
          </label>
          <label className="flex items-center space-x-2">
            <input type="checkbox" defaultChecked className="rounded" />
            <span className="text-sm">Include compatibility checks</span>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col" role="region" aria-label="Error Detection Panel">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold" id="error-detection-title">Error Detection</h2>
          <div className="flex items-center space-x-2" role="tablist" aria-labelledby="error-detection-title">
            <button
              onClick={() => setActiveTab('overview')}
              className={`p-2 rounded ${activeTab === 'overview' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              title="Overview"
              role="tab"
              aria-selected={activeTab === 'overview'}
              aria-controls="overview-panel"
              aria-label="Overview tab"
            >
              <Eye className="w-4 h-4" aria-hidden="true" />
            </button>
            <button
              onClick={() => setActiveTab('errors')}
              className={`p-2 rounded ${activeTab === 'errors' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              title="Errors"
              role="tab"
              aria-selected={activeTab === 'errors'}
              aria-controls="errors-panel"
              aria-label="Errors tab"
            >
              <AlertTriangle className="w-4 h-4" aria-hidden="true" />
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`p-2 rounded ${activeTab === 'history' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              title="History"
              role="tab"
              aria-selected={activeTab === 'history'}
              aria-controls="history-panel"
              aria-label="History tab"
            >
              <History className="w-4 h-4" aria-hidden="true" />
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`p-2 rounded ${activeTab === 'settings' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
              title="Settings"
              role="tab"
              aria-selected={activeTab === 'settings'}
              aria-controls="settings-panel"
              aria-label="Settings tab"
            >
              <Settings className="w-4 h-4" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 overflow-y-auto">
        {activeTab === 'overview' && (
          <div role="tabpanel" id="overview-panel" aria-labelledby="error-detection-title">
            {renderOverview()}
          </div>
        )}
        {activeTab === 'errors' && (
          <div role="tabpanel" id="errors-panel" aria-labelledby="error-detection-title">
            {renderErrors()}
          </div>
        )}
        {activeTab === 'history' && (
          <div role="tabpanel" id="history-panel" aria-labelledby="error-detection-title">
            {renderHistory()}
          </div>
        )}
        {activeTab === 'settings' && (
          <div role="tabpanel" id="settings-panel" aria-labelledby="error-detection-title">
            {renderSettings()}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="border-t p-4">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span role="status" aria-live="polite">
            {status?.last_scan ? `Last scan: ${new Date(status.last_scan).toLocaleString()}` : 'No scans yet'}
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {/* Export functionality */}}
              className="flex items-center space-x-1 text-gray-600 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              aria-label="Export error report"
            >
              <Download className="w-4 h-4" aria-hidden="true" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
