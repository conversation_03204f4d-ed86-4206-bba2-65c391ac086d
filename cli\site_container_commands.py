# cli/site_container_commands.py
"""
Site Container Management CLI Commands
Provides command-line interface for managing site containers.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from core.site_container_manager import SiteContainerManager

logger = logging.getLogger(__name__)


class SiteContainerCommands:
    """CLI commands for site container management"""

    def __init__(self, agent):
        self.agent = agent
        self.container_manager = SiteContainerManager()

    async def create_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Create a Docker container for a website"""
        try:
            site_config = {
                "name": site_name,
                "port": kwargs.get("port"),
                "environment": kwargs.get("environment", "production"),
            }

            result = await self.container_manager.create_site_container(
                site_name, site_config
            )

            if result["success"]:
                logger.info(f"✅ Container created for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container created for site {site_name}",
                    "container": result["container"],
                    "port": result["port"],
                }
            else:
                logger.error(f"❌ Failed to create container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error creating site container: {e}")
            return {"success": False, "error": str(e)}

    async def start_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Start a site container"""
        try:
            result = await self.container_manager.start_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container started for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container started for site {site_name}",
                    "url": result["url"],
                }
            else:
                logger.error(f"❌ Failed to start container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error starting site container: {e}")
            return {"success": False, "error": str(e)}

    async def stop_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Stop a site container"""
        try:
            result = await self.container_manager.stop_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container stopped for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container stopped for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to stop container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error stopping site container: {e}")
            return {"success": False, "error": str(e)}

    async def delete_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Delete a site container"""
        try:
            result = await self.container_manager.delete_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container deleted for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container deleted for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to delete container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error deleting site container: {e}")
            return {"success": False, "error": str(e)}

    async def list_site_containers(self, **kwargs) -> Dict[str, Any]:
        """List all site containers"""
        try:
            result = await self.container_manager.list_containers()

            if result["success"]:
                containers = result["containers"]
                logger.info(f"�� Found {len(containers)} site containers")

                container_list = []
                for container in containers:
                    container_list.append(
                        {
                            "site_name": container["site_name"],
                            "status": container["status"],
                            "port": container["port"],
                            "url": f"http://localhost:{container['port']}",
                            "health": container["health_status"],
                        }
                    )

                return {
                    "success": True,
                    "containers": container_list,
                    "total": len(container_list),
                }
            else:
                logger.error(f"❌ Failed to list containers: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error listing site containers: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_status(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Get status of a site container"""
        try:
            result = await self.container_manager.get_container_status(site_name)

            if result["success"]:
                container = result["container"]
                logger.info(
                    f"📊 Container status for {site_name}: {container['status']}"
                )

                return {
                    "success": True,
                    "container": {
                        "site_name": container["site_name"],
                        "status": container["status"],
                        "port": container["port"],
                        "url": f"http://localhost:{container['port']}",
                        "health": container["health_status"],
                        "resource_usage": container["resource_usage"],
                    },
                }
            else:
                logger.error(f"❌ Failed to get container status: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return {"success": False, "error": str(e)}

    async def rebuild_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Rebuild a site container"""
        try:
            logger.info(f"🔨 Rebuilding container for site {site_name}")

            result = await self.container_manager.rebuild_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container rebuilt for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container rebuilt for site {site_name}",
                    "url": result.get("url"),
                }
            else:
                logger.error(f"❌ Failed to rebuild container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error rebuilding site container: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_logs(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Get logs from a site container"""
        try:
            lines = kwargs.get("lines", 100)
            result = await self.container_manager.get_container_logs(site_name, lines)

            if result["success"]:
                logs = result["logs"]
                logger.info(f"�� Retrieved {len(logs)} log lines for {site_name}")

                return {
                    "success": True,
                    "logs": logs,
                    "container_name": result["container_name"],
                }
            else:
                logger.error(f"❌ Failed to get container logs: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting container logs: {e}")
            return {"success": False, "error": str(e)}
