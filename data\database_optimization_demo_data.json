{"query_metrics": [{"query_id": "query_1753410159967", "query_text": "SELECT * FROM users WHERE id = 0", "execution_time_ms": 50.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:39.967359", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160067", "query_text": "SELECT * FROM users WHERE id = 1", "execution_time_ms": 55.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.067539", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160168", "query_text": "SELECT * FROM users WHERE id = 2", "execution_time_ms": 60.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.168038", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160285", "query_text": "SELECT * FROM users WHERE id = 3", "execution_time_ms": 65.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.285995", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160387", "query_text": "SELECT * FROM users WHERE id = 4", "execution_time_ms": 70.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.387283", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160492", "query_text": "SELECT * FROM users WHERE id = 5", "execution_time_ms": 75.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.492641", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160593", "query_text": "SELECT * FROM users WHERE id = 6", "execution_time_ms": 80.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.593215", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160693", "query_text": "SELECT * FROM users WHERE id = 7", "execution_time_ms": 85.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.693459", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160793", "query_text": "SELECT * FROM users WHERE id = 8", "execution_time_ms": 90.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.793749", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160894", "query_text": "SELECT * FROM users WHERE id = 9", "execution_time_ms": 95.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.894222", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410160995", "query_text": "SELECT * FROM large_table WHERE category = 'important' AND id = 0", "execution_time_ms": 3000.0, "rows_returned": 200, "rows_affected": 0, "timestamp": "2025-07-24T20:22:40.995094", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161095", "query_text": "SELECT * FROM projects WHERE user_id = 1", "execution_time_ms": 160.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.095789", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161196", "query_text": "SELECT * FROM projects WHERE user_id = 2", "execution_time_ms": 170.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.196261", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161296", "query_text": "SELECT * FROM large_table WHERE category = 'important' AND id = 3", "execution_time_ms": 3300.0, "rows_returned": 200, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.296579", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161397", "query_text": "SELECT * FROM projects WHERE user_id = 4", "execution_time_ms": 190.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.397299", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161498", "query_text": "SELECT * FROM projects WHERE user_id = 5", "execution_time_ms": 200.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.498214", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161598", "query_text": "SELECT * FROM large_table WHERE category = 'important' AND id = 6", "execution_time_ms": 3600.0, "rows_returned": 200, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.598799", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161700", "query_text": "SELECT * FROM projects WHERE user_id = 7", "execution_time_ms": 220.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.700150", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161800", "query_text": "SELECT * FROM projects WHERE user_id = 8", "execution_time_ms": 230.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.800296", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410161900", "query_text": "SELECT * FROM large_table WHERE category = 'important' AND id = 9", "execution_time_ms": 3900.0, "rows_returned": 200, "rows_affected": 0, "timestamp": "2025-07-24T20:22:41.900671", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162001", "query_text": "SELECT * FROM projects WHERE user_id = 10", "execution_time_ms": 250.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.001285", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162101", "query_text": "SELECT * FROM projects WHERE user_id = 11", "execution_time_ms": 260.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.101689", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162201", "query_text": "SELECT * FROM large_table WHERE category = 'important' AND id = 12", "execution_time_ms": 4200.0, "rows_returned": 200, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.201814", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162302", "query_text": "SELECT * FROM projects WHERE user_id = 13", "execution_time_ms": 280.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.302425", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162402", "query_text": "SELECT * FROM projects WHERE user_id = 14", "execution_time_ms": 290.0, "rows_returned": 5, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.402727", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162503", "query_text": "SELECT * FROM users WHERE id = 0", "execution_time_ms": 60.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.503474", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162603", "query_text": "SELECT * FROM users WHERE id = 1", "execution_time_ms": 62.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.603767", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162704", "query_text": "SELECT * FROM users WHERE id = 2", "execution_time_ms": 64.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.704318", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162804", "query_text": "SELECT * FROM users WHERE id = 3", "execution_time_ms": 66.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.804930", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410162905", "query_text": "SELECT * FROM users WHERE id = 4", "execution_time_ms": 68.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:42.905426", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410163005", "query_text": "SELECT * FROM users WHERE id = 5", "execution_time_ms": 70.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:43.005769", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410163106", "query_text": "SELECT * FROM users WHERE id = 6", "execution_time_ms": 72.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:43.106391", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410163207", "query_text": "SELECT * FROM users WHERE id = 7", "execution_time_ms": 74.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:43.207285", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410163307", "query_text": "SELECT * FROM users WHERE id = 8", "execution_time_ms": 76.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:43.307536", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}, {"query_id": "query_1753410163408", "query_text": "SELECT * FROM users WHERE id = 9", "execution_time_ms": 78.0, "rows_returned": 1, "rows_affected": 0, "timestamp": "2025-07-24T20:22:43.408101", "table_name": null, "index_used": null, "cache_hit": false, "error_message": null}], "database_metrics": [{"timestamp": "2025-07-24T20:22:39.969952", "total_queries": 1, "slow_queries": 0, "avg_query_time_ms": 50.0, "max_query_time_ms": 50.0, "cache_hit_rate": 0.0, "active_connections": 1, "database_size_mb": 0.03515625, "index_count": 2, "table_count": 6, "fragmentation_percent": 0.0}], "recommendations": [{"recommendation_id": "slow_query_1753410160", "type": "query", "priority": "medium", "description": "Optimize slow query: SELECT * FROM large_table WHERE category = 'important' AND id = 0...", "impact": "high", "estimated_improvement": 60.0, "implementation_cost": "medium", "sql_statement": null, "created_at": "2025-07-24T20:22:40.995613", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_query_1753410161", "type": "query", "priority": "medium", "description": "Optimize slow query: SELECT * FROM large_table WHERE category = 'important' AND id = 3...", "impact": "high", "estimated_improvement": 60.0, "implementation_cost": "medium", "sql_statement": null, "created_at": "2025-07-24T20:22:41.296885", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_query_1753410161", "type": "query", "priority": "medium", "description": "Optimize slow query: SELECT * FROM large_table WHERE category = 'important' AND id = 6...", "impact": "high", "estimated_improvement": 60.0, "implementation_cost": "medium", "sql_statement": null, "created_at": "2025-07-24T20:22:41.599310", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_query_1753410161", "type": "query", "priority": "medium", "description": "Optimize slow query: SELECT * FROM large_table WHERE category = 'important' AND id = 9...", "impact": "high", "estimated_improvement": 60.0, "implementation_cost": "medium", "sql_statement": null, "created_at": "2025-07-24T20:22:41.901052", "implemented": false, "implemented_at": null}, {"recommendation_id": "slow_query_1753410162", "type": "query", "priority": "medium", "description": "Optimize slow query: SELECT * FROM large_table WHERE category = 'important' AND id = 12...", "impact": "high", "estimated_improvement": 60.0, "implementation_cost": "medium", "sql_statement": null, "created_at": "2025-07-24T20:22:42.202113", "implemented": false, "implemented_at": null}], "optimization_history": [], "thresholds": {"slow_query_ms": 1000.0, "critical_query_ms": 5000.0, "max_cache_size": 1000, "maintenance_interval_hours": 1}}