# Local Git Repository Setup - G:\ Drive

## 🎯 Overview

This document details the local git repository setup for the AI Coding Agent project on the G:\ drive, including configuration, workflow guidelines, and best practices for development.

## 📍 Repository Location

**Primary Repository**: `G:\AICodingAgent`
**Status**: ✅ Fully initialized and operational
**Initial Commit**: ✅ Complete with Phase 3.2 implementation

## 🏗️ Repository Structure

```
G:\AICodingAgent\
├── .git/                           # Git repository
├── src/                            # Source code
│   ├── home_server_pipeline.py     # Phase 3.2 pipeline
│   ├── core/agent.py                    # Phase 3.1 CLI/Web workflow
│   ├── deployment.py               # Deployment manager
│   ├── website_generator.py        # Website generator
│   ├── cli/                        # CLI components
│   ├── db/                         # Database components
│   └── ...
├── config/                         # Configuration files
│   ├── pipeline_config.json        # Phase 3.2 pipeline config
│   ├── cli_web_config.json         # Phase 3.1 CLI config
│   └── ...
├── scripts/                        # Scripts and demos
│   ├── pipeline/                   # Idempotent scripts
│   ├── demo_phase_3_2.py           # Phase 3.2 demo
│   └── ...
├── docs/                           # Documentation
│   ├── LOCAL_GIT_REPOSITORY_SETUP.md  # This document
│   ├── PHASE_3_2_HOME_SERVER_PIPELINE_SUMMARY.md
│   ├── ProjectRoadmap.md
│   └── ...
├── tests/                          # Test files
├── templates/                      # Website templates
├── themes/                         # Theme configurations
├── .gitignore                      # Git ignore rules
├── cursorrules.md                  # Cursor AI rules
├── requirements.txt                # Python dependencies
├── requirements-dev.txt            # Development dependencies
└── README.md                       # Project overview
```

## 🔧 Git Configuration

### Safe Directory Configuration
```bash
# Configure git to trust the G:\ drive repository
git config --global --add safe.directory G:/AICodingAgent
```

### User Configuration (if not already set)
```bash
# Set your git user information
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### Repository-Specific Configuration
```bash
# Navigate to repository
cd G:\AICodingAgent

# Set repository-specific configurations
git config core.autocrlf true          # Handle line endings on Windows
git config core.filemode false         # Ignore file mode changes
```

## 📋 .gitignore Configuration

The repository uses a comprehensive `.gitignore` file that excludes:

### 🔒 Security & Privacy
- `backups/` - Deployment backups (contain sensitive data)
- `logs/` - Runtime logs
- `data/pipeline_state.json` - Pipeline state (runtime data)
- `*.pem`, `*.key`, `*.crt` - SSL certificates
- `config/*_secrets.json` - Secret configuration files

### 🗂️ Generated Content
- `test_reports/` - Generated test reports
- `sites/*/` - Deployed sites (generated content)
- `*.html` - Generated HTML files
- `*.zip`, `*.tar.gz` - Backup and archive files

### 🐍 Python Environment
- `.venv/` - Virtual environment
- `__pycache__/` - Python cache
- `*.pyc`, `*.pyo` - Compiled Python files
- `.pytest_cache/` - Test cache
- `.mypy_cache/` - Type checking cache

### 🔧 Development Tools
- `.vscode/`, `.idea/` - IDE configurations
- `node_modules/` - Node.js dependencies
- `*.tmp`, `*.temp` - Temporary files

## 🚀 Git Workflow Guidelines

### 1. **Before Making Changes**
```bash
# Check current status
git status

# Pull latest changes (if working with others)
git pull origin main

# Create feature branch (for major changes)
git checkout -b feature/phase-3-3-documentation
```

### 2. **During Development**
```bash
# Stage changes frequently
git add src/home_server_pipeline.py
git add config/pipeline_config.json

# Commit with descriptive messages
git commit -m "feat(pipeline): add rollback validation feature

- Add pre-rollback validation checks
- Implement rollback safety measures
- Update configuration for validation rules"
```

### 3. **After Implementation**
```bash
# Stage all changes
git add .

# Commit with comprehensive message
git commit -m "feat(phase-3-2): complete home server pipeline implementation

✅ Automated deployment workflow
✅ Idempotent script management
✅ Rollback capabilities
✅ State management system
✅ 100% test success rate achieved

Files changed:
- src/home_server_pipeline.py
- config/pipeline_config.json
- scripts/demo_phase_3_2.py
- docs/PHASE_3_2_HOME_SERVER_PIPELINE_SUMMARY.md"
```

## 📝 Commit Message Standards

### Conventional Commit Format
```
type(scope): description

[optional body]

[optional footer]
```

### Commit Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples
```bash
# Feature implementation
git commit -m "feat(pipeline): implement automated deployment workflow"

# Bug fix
git commit -m "fix(validation): resolve disk space check error"

# Documentation update
git commit -m "docs(phase-3-2): add comprehensive implementation summary"

# Configuration change
git commit -m "config(pipeline): update rollback conditions"

# Test addition
git commit -m "test(pipeline): add deployment validation tests"
```

## 🔄 Branch Strategy

### Main Branch
- `master` - Production-ready code
- Always stable and deployable
- Requires pull request for changes

### Feature Branches
- `feature/phase-3-3` - Phase 3.3 implementation
- `feature/gui-dashboard` - GUI dashboard feature
- `feature/ai-integration` - AI model integration

### Development Workflow
```bash
# Create feature branch
git checkout -b feature/phase-3-3

# Make changes and commit
git add .
git commit -m "feat(phase-3-3): implement documentation system"

# Push to remote (if using remote repository)
git push origin feature/phase-3-3

# Merge back to master when complete
git checkout master
git merge feature/phase-3-3
```

## 🧪 Testing Integration

### Pre-Commit Testing
```bash
# Run tests before committing
python -m pytest tests/

# Run specific test suite
python scripts/demo_phase_3_2.py

# Check code quality
python scripts/quality_check.py
```

### Commit Only After 100% Test Success
```bash
# ✅ Good: All tests pass
python scripts/demo_phase_3_2.py
# Output: Success Rate: 100.0%
git add .
git commit -m "feat(pipeline): add new validation feature - 100% test success"

# ❌ Bad: Tests fail
python scripts/demo_phase_3_2.py
# Output: Success Rate: 85.0%
# DO NOT COMMIT - Fix tests first!
```

## 📊 Repository Health Monitoring

### Regular Maintenance
```bash
# Check repository status
git status
git log --oneline -10

# Clean up old branches
git branch --merged | grep -v master | xargs git branch -d

# Check for large files
git rev-list --objects --all | git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | sed -n 's/^blob //p' | sort -nr -k 2 | head -10
```

### Backup Strategy
```bash
# Create backup of repository
robocopy "G:\AICodingAgent" "G:\AICodingAgent_Backup_$(Get-Date -Format 'yyyyMMdd')" /E /XD .git

# Or use git bundle for portable backup
git bundle create "G:\AICodingAgent_$(Get-Date -Format 'yyyyMMdd').bundle" --all
```

## 🔗 Integration with Phase 3.2 Pipeline

### Pipeline-Git Integration
The Phase 3.2 Home Server Pipeline includes git integration:

```json
{
  "scripts": {
    "version_control": {
      "enabled": true,
      "git_integration": true,
      "auto_commit": true,
      "commit_message_template": "Pipeline: {script_name} - {action}"
    }
  }
}
```

### Automated Git Operations
```python
# Example: Pipeline automatically commits successful deployments
import subprocess

def commit_deployment(deployment_id, site_name):
    """Commit successful deployment to git"""
    commit_message = f"deploy({site_name}): successful deployment {deployment_id}"
    subprocess.run(["git", "add", "."])
    subprocess.run(["git", "commit", "-m", commit_message])
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. **Safe Directory Error**
```bash
# Error: fatal: detected dubious ownership in repository
# Solution:
git config --global --add safe.directory G:/AICodingAgent
```

#### 2. **Line Ending Issues**
```bash
# Error: warning: LF will be replaced by CRLF
# Solution:
git config core.autocrlf true
```

#### 3. **Large File Issues**
```bash
# Error: File too large for git
# Solution: Add to .gitignore or use git-lfs
echo "large_file.zip" >> .gitignore
```

#### 4. **Permission Issues**
```bash
# Error: Permission denied
# Solution: Run as administrator or fix permissions
icacls "G:\AICodingAgent" /grant "Users":(OI)(CI)F
```

## 📈 Best Practices

### 1. **Frequent Commits**
- Commit small, logical changes
- Use descriptive commit messages
- Include test results in commit messages

### 2. **Branch Management**
- Use feature branches for major changes
- Keep branches focused and small
- Delete merged branches regularly

### 3. **Documentation**
- Update documentation with code changes
- Include setup instructions in commits
- Document configuration changes

### 4. **Testing**
- Always run tests before committing
- Only commit with 100% test success
- Include test results in commit messages

### 5. **Security**
- Never commit sensitive data
- Use .gitignore for private files
- Review commits before pushing

## 🎯 Success Metrics

### Repository Health Indicators
- ✅ **Clean working directory** - No uncommitted changes
- ✅ **100% test success rate** - All tests pass
- ✅ **Comprehensive documentation** - All features documented
- ✅ **Proper commit messages** - Following conventional format
- ✅ **No sensitive data** - Proper .gitignore usage

### Monitoring Commands
```bash
# Check repository health
git status
git log --oneline -5
python scripts/demo_phase_3_2.py

# Expected output:
# - Working directory clean
# - Recent commits with proper messages
# - Test success rate: 100.0%
```

## 📞 Support and Maintenance

### Regular Tasks
- [ ] Weekly repository health check
- [ ] Monthly backup creation
- [ ] Quarterly branch cleanup
- [ ] Annual documentation review

### Contact Information
- **Repository Location**: `G:\AICodingAgent`
- **Documentation**: `docs/LOCAL_GIT_REPOSITORY_SETUP.md`
- **Configuration**: `config/pipeline_config.json`
- **Rules**: `cursorrules.md`

---

**Last Updated**: July 19, 2025
**Version**: 1.0.0
**Status**: ✅ Active and Operational
