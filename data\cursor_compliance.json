{"last_check": "2025-08-07T16:51:49.762752", "violations": ["Test failures detected - fix before proceeding"], "compliance_score": 81.81818181818183, "rule_checks": {"file_organization": {"status": "failed", "violations": ["File check_compliance.py should not be in project root", "File check_compliance_test.py should not be in project root", "File fix_imports.py should not be in project root", "File fix_imports_test.py should not be in project root", "File fix_sqlalchemy_models.py should not be in project root", "File fix_sqlalchemy_models_test.py should not be in project root", "File gpu_manager.py should not be in project root", "File gpu_manager_test.py should not be in project root", "File import_analysis.py should not be in project root", "File import_analysis_test.py should not be in project root", "File test_api_key_system.py should not be in project root", "File test_cursor_compliance.py should not be in project root", "File test_cursor_rules.py should not be in project root", "File test_model_names.py should not be in project root", "File test_modern_model_system.py should not be in project root", "File test_ollama_api.py should not be in project root", "File test_requirements.py should not be in project root", "File __init__.py should not be in project root"], "warnings": ["Config file pytest.ini should be in config/ directory", "Script file check_compliance.py should be in scripts/ directory", "Script file check_compliance_test.py should be in scripts/ directory", "Script file fix_imports.py should be in scripts/ directory", "Script file fix_imports_test.py should be in scripts/ directory", "Script file fix_sqlalchemy_models.py should be in scripts/ directory", "Script file fix_sqlalchemy_models_test.py should be in scripts/ directory", "Script file gpu_manager.py should be in scripts/ directory", "Script file gpu_manager_test.py should be in scripts/ directory", "Script file import_analysis.py should be in scripts/ directory", "Script file import_analysis_test.py should be in scripts/ directory", "Script file test_api_key_system.py should be in scripts/ directory", "Script file test_cursor_compliance.py should be in scripts/ directory", "Script file test_cursor_rules.py should be in scripts/ directory", "Script file test_model_names.py should be in scripts/ directory", "Script file test_modern_model_system.py should be in scripts/ directory", "Script file test_ollama_api.py should be in scripts/ directory", "Script file test_requirements.py should be in scripts/ directory", "Script file validation_service.py should be in scripts/ directory", "Script file __init__.py should be in scripts/ directory"]}, "todo_completion": {"status": "passed", "violations": [], "warnings": [], "incomplete_todos": []}, "test_success": {"status": "failed", "violations": ["Tests are failing - 100% success rate required"], "warnings": [], "test_results": {}}, "dependency_management": {"status": "passed", "violations": [], "warnings": ["Dependency pydantic>=2.11.7,<3.0.0 should use exact version pinning (==)", "Dependency pydantic-settings>=2.8.0 should use exact version pinning (==)", "Dependency pydantic_core>=2.33.2 should use exact version pinning (==)", "Dependency realtime>=2.6.0 should use exact version pinning (==)"]}, "cli_api_compliance": {"status": "passed", "violations": [], "warnings": []}, "ai_model_compliance": {"status": "passed", "violations": [], "warnings": ["Cloud model usage detected in api\\main.py (future mode will allow this)", "Cloud model usage detected in api\\main.py (future mode will allow this)", "Cloud model usage detected in api\\main.py (future mode will allow this)", "Cloud model usage detected in content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\evaluator.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\pipeline.py (future mode will allow this)", "Cloud model usage detected in fine_tuning\\trainer.py (future mode will allow this)", "Cloud model usage detected in scripts\\train_model.py (future mode will allow this)", "Cloud model usage detected in tests\\test_fine_tuning.py (future mode will allow this)", "Cloud model usage detected in tests\\test_ollama_integration.py (future mode will allow this)", "Cloud model usage detected in tests\\test_ollama_integration.py (future mode will allow this)", "Cloud model usage detected in tests\\test_pippy.py (future mode will allow this)"], "current_mode": "local_only"}, "git_workflow": {"status": "passed", "violations": [], "warnings": ["Repository should be in G:\\AICodingAgent, found in F:/NasShare/AICodingAgent", "There are uncommitted changes"]}, "security_compliance": {"status": "passed", "violations": [], "warnings": ["Potential hardcoded secret in api\\hf_api.py"]}, "file_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 19 potential duplicate files", "Found 5 potentially obsolete files"], "duplicates": 19, "obsolete_files": 5}, "mock_data_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 282 potential mock data files"], "mock_files": 282}, "virtual_environment": {"status": "passed", "violations": [], "warnings": []}}, "warnings": []}