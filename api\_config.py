"""
Configuration management for array API compatibility.

This module provides a comprehensive configuration system for managing
array API dispatch settings and other configuration options.
"""

import os
import threading
from contextlib import contextmanager
from typing import Any, Dict, Optional, Union


class ConfigManager:
    """Thread-safe configuration manager for array API settings."""

    def __init__(self):
        self._config = {
            "array_api_dispatch": False,
            "array_api_strict": False,
            "array_api_compat": True,
            "numpy_compat": True,
            "device_default": "cpu",
            "dtype_default": "float64",
            "enable_warnings": True,
            "log_level": "INFO",
            "cache_size": 1000,
            "timeout": 30.0,
        }
        self._lock = threading.RLock()
        self._callbacks = []

    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        with self._lock:
            return self._config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set a configuration value."""
        with self._lock:
            old_value = self._config.get(key)
            self._config[key] = value
            if old_value != value:
                self._notify_callbacks(key, old_value, value)

    def update(self, **kwargs: Any) -> None:
        """Update multiple configuration values."""
        with self._lock:
            for key, value in kwargs.items():
                old_value = self._config.get(key)
                self._config[key] = value
                if old_value != value:
                    self._notify_callbacks(key, old_value, value)

    def get_all(self) -> Dict[str, Any]:
        """Get all configuration values."""
        with self._lock:
            return self._config.copy()

    def reset(self) -> None:
        """Reset configuration to defaults."""
        with self._lock:
            self._config = {
                "array_api_dispatch": False,
                "array_api_strict": False,
                "array_api_compat": True,
                "numpy_compat": True,
                "device_default": "cpu",
                "dtype_default": "float64",
                "enable_warnings": True,
                "log_level": "INFO",
                "cache_size": 1000,
                "timeout": 30.0,
            }
            self._notify_callbacks("__all__", None, self._config)

    def register_callback(self, callback: callable) -> None:
        """Register a callback to be called when configuration changes."""
        with self._lock:
            if callback not in self._callbacks:
                self._callbacks.append(callback)

    def unregister_callback(self, callback: callable) -> None:
        """Unregister a callback."""
        with self._lock:
            if callback in self._callbacks:
                self._callbacks.remove(callback)

    def _notify_callbacks(self, key: str, old_value: Any, new_value: Any) -> None:
        """Notify all registered callbacks of configuration changes."""
        for callback in self._callbacks:
            try:
                callback(key, old_value, new_value)
            except Exception:
                # Don't let callback errors break the configuration system
                pass


# Global configuration manager instance
_config_manager = ConfigManager()


def get_config() -> Dict[str, Any]:
    """
    Get the current configuration for array API dispatch.

    Returns
    -------
    dict
        Configuration dictionary with all current settings

    Examples
    --------
    >>> config = get_config()
    >>> print(config["array_api_dispatch"])
    False
    """
    return _config_manager.get_all()


def set_config(**kwargs: Any) -> None:
    """
    Set configuration options.

    Parameters
    ----------
    **kwargs
        Configuration options to set

    Examples
    --------
    >>> set_config(array_api_dispatch=True, device_default="cuda")
    >>> config = get_config()
    >>> print(config["array_api_dispatch"])
    True
    """
    _config_manager.update(**kwargs)


def get_config_value(key: str, default: Any = None) -> Any:
    """
    Get a specific configuration value.

    Parameters
    ----------
    key : str
        Configuration key to retrieve
    default : any, optional
        Default value if key doesn't exist

    Returns
    -------
    any
        Configuration value

    Examples
    --------
    >>> dispatch_enabled = get_config_value("array_api_dispatch", False)
    >>> print(dispatch_enabled)
    False
    """
    return _config_manager.get(key, default)


def set_config_value(key: str, value: Any) -> None:
    """
    Set a specific configuration value.

    Parameters
    ----------
    key : str
        Configuration key to set
    value : any
        Value to set

    Examples
    --------
    >>> set_config_value("array_api_dispatch", True)
    >>> print(get_config_value("array_api_dispatch"))
    True
    """
    _config_manager.set(key, value)


@contextmanager
def config_context(**kwargs: Any):
    """
    Context manager for temporary configuration changes.

    Parameters
    ----------
    **kwargs
        Configuration options to set temporarily

    Examples
    --------
    >>> with config_context(array_api_dispatch=True):
    ...     # Configuration is temporarily changed
    ...     config = get_config()
    ...     print(config["array_api_dispatch"])
    True
    >>> # Configuration is restored outside the context
    >>> config = get_config()
    >>> print(config["array_api_dispatch"])
    False
    """
    # Store current values
    current_config = _config_manager.get_all()
    keys_to_restore = {k: current_config.get(k) for k in kwargs.keys()}

    try:
        # Set new values
        _config_manager.update(**kwargs)
        yield
    finally:
        # Restore original values
        _config_manager.update(**keys_to_restore)


def reset_config() -> None:
    """
    Reset configuration to default values.

    Examples
    --------
    >>> set_config(array_api_dispatch=True)
    >>> reset_config()
    >>> print(get_config_value("array_api_dispatch"))
    False
    """
    _config_manager.reset()


def register_config_callback(callback: callable) -> None:
    """
    Register a callback to be called when configuration changes.

    Parameters
    ----------
    callback : callable
        Function to call when configuration changes.
        Should accept (key, old_value, new_value) parameters.

    Examples
    --------
    >>> def config_changed(key, old_value, new_value):
    ...     print(f"Config changed: {key} = {new_value}")
    >>> register_config_callback(config_changed)
    >>> set_config(array_api_dispatch=True)
    Config changed: array_api_dispatch = True
    """
    _config_manager.register_callback(callback)


def unregister_config_callback(callback: callable) -> None:
    """
    Unregister a configuration callback.

    Parameters
    ----------
    callback : callable
        Callback function to unregister
    """
    _config_manager.unregister_callback(callback)


# Environment variable support
def _load_from_env() -> None:
    """Load configuration from environment variables."""
    env_mapping = {
        "ARRAY_API_DISPATCH": ("array_api_dispatch", bool),
        "ARRAY_API_STRICT": ("array_api_strict", bool),
        "DEVICE_DEFAULT": ("device_default", str),
        "DTYPE_DEFAULT": ("dtype_default", str),
        "ENABLE_WARNINGS": ("enable_warnings", bool),
        "LOG_LEVEL": ("log_level", str),
        "CACHE_SIZE": ("cache_size", int),
        "TIMEOUT": ("timeout", float),
    }

    for env_var, (config_key, value_type) in env_mapping.items():
        env_value = os.environ.get(env_var)
        if env_value is not None:
            try:
                if value_type == bool:
                    # Handle boolean values
                    if env_value.lower() in ('true', '1', 'yes', 'on'):
                        value = True
                    elif env_value.lower() in ('false', '0', 'no', 'off'):
                        value = False
                    else:
                        continue
                elif value_type == int:
                    value = int(env_value)
                elif value_type == float:
                    value = float(env_value)
                else:
                    value = env_value

                _config_manager.set(config_key, value)
            except (ValueError, TypeError):
                # Skip invalid environment variables
                continue


# Load configuration from environment variables on module import
_load_from_env()


# Configuration validation
def validate_config(config: Dict[str, Any]) -> bool:
    """
    Validate configuration values.

    Parameters
    ----------
    config : dict
        Configuration dictionary to validate

    Returns
    -------
    bool
        True if configuration is valid, False otherwise
    """
    validators = {
        "array_api_dispatch": lambda x: isinstance(x, bool),
        "array_api_strict": lambda x: isinstance(x, bool),
        "array_api_compat": lambda x: isinstance(x, bool),
        "numpy_compat": lambda x: isinstance(x, bool),
        "device_default": lambda x: isinstance(x, str) and x in ("cpu", "cuda", "mps"),
        "dtype_default": lambda x: isinstance(x, str) and x in ("float32", "float64", "int32", "int64"),
        "enable_warnings": lambda x: isinstance(x, bool),
        "log_level": lambda x: isinstance(x, str) and x.upper() in ("DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"),
        "cache_size": lambda x: isinstance(x, int) and x > 0,
        "timeout": lambda x: isinstance(x, (int, float)) and x > 0,
    }

    for key, validator in validators.items():
        if key in config and not validator(config[key]):
            return False

    return True


# Export all public functions and classes
__all__ = [
    "get_config",
    "set_config",
    "get_config_value",
    "set_config_value",
    "config_context",
    "reset_config",
    "register_config_callback",
    "unregister_config_callback",
    "validate_config",
    "ConfigManager",
]
