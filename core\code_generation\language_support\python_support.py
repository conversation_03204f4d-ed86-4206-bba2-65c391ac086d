#!/usr/bin/env python3
"""
Python AST Support for Code Generation

This module provides Python-specific AST manipulation and code generation support.
"""

import ast
import logging
import re
import warnings
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Union

import astor

from core.code_generation.language_support.base_language_support import (
    ASTNodeInfo,
    BaseLanguageSupport,
    LanguageType,
)

# Suppress astor deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="astor")


logger = logging.getLogger(__name__)


@dataclass
class PythonFunctionInfo:
    """Information about a Python function"""

    name: str
    parameters: List[str]
    return_type: Optional[str] = None
    docstring: Optional[str] = None
    decorators: List[str] = None
    is_async: bool = False


@dataclass
class PythonClassInfo:
    """Information about a Python class"""

    name: str
    bases: List[str] = None
    methods: List[PythonFunctionInfo] = None
    docstring: Optional[str] = None
    decorators: List[str] = None


class PythonASTSupport(BaseLanguageSupport):
    """
    Python-specific AST support implementation.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(LanguageType.PYTHON, config)

    async def create_base_ast(self) -> ast.AST:
        """Create a base Python AST"""
        return ast.Module(body=[], type_ignores=[])

    async def parse_code(self, code: str) -> ast.AST:
        """Parse Python code string into AST"""
        try:
            return ast.parse(code)
        except SyntaxError as e:
            logger.error(f"Failed to parse Python code: {e}")
            raise

    async def ast_to_code(self, ast_tree: ast.AST) -> str:
        """Convert Python AST back to code string"""
        try:
            return astor.to_source(ast_tree)
        except Exception as e:
            logger.error(f"Failed to convert AST to Python code: {e}")
            raise

    async def add_import(self, ast_tree: ast.AST, import_statement: str) -> ast.AST:
        """Add import statement to Python AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse the import statement
        try:
            import_ast = ast.parse(import_statement)
            if isinstance(import_ast, ast.Module) and import_ast.body:
                import_node = import_ast.body[0]

                # Check if import already exists
                for existing_node in ast_tree.body:
                    if isinstance(existing_node, type(import_node)):
                        if self._imports_are_equivalent(existing_node, import_node):
                            return ast_tree

                # Add import at the beginning
                ast_tree.body.insert(0, import_node)

        except SyntaxError as e:
            logger.error(f"Invalid import statement: {import_statement}, error: {e}")
            raise

        return ast_tree

    async def add_function(
        self, ast_tree: ast.AST, function_name: str, parameters: List[str], body: str
    ) -> ast.AST:
        """Add function to Python AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse function body
        try:
            body_ast = ast.parse(body)
            if isinstance(body_ast, ast.Module):
                function_body = body_ast.body
            else:
                function_body = [body_ast]
        except SyntaxError:
            # If body parsing fails, create a simple pass statement
            function_body = [ast.Pass()]

        # Create function arguments
        args = []
        for param in parameters:
            if ":" in param:
                name, annotation = param.split(":", 1)
                args.append(
                    ast.arg(
                        arg=name.strip(), annotation=ast.Name(id=annotation.strip())
                    )
                )
            else:
                args.append(ast.arg(arg=param.strip()))

        # Create function definition
        function_def = ast.FunctionDef(
            name=function_name,
            args=ast.arguments(
                posonlyargs=[], args=args, kwonlyargs=[], defaults=[], kw_defaults=[]
            ),
            body=function_body,
            decorator_list=[],
            returns=None,
        )

        ast_tree.body.append(function_def)
        return ast_tree

    async def add_class(
        self, ast_tree: ast.AST, class_name: str, methods: List[Dict[str, Any]]
    ) -> ast.AST:
        """Add class to Python AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Create class body
        class_body = []

        # Add methods
        for method_info in methods:
            method_name = method_info.get("name", "")
            parameters = method_info.get("parameters", [])
            body = method_info.get("body", "pass")

            # Parse method body
            try:
                body_ast = ast.parse(body)
                if isinstance(body_ast, ast.Module):
                    method_body = body_ast.body
                else:
                    method_body = [body_ast]
            except SyntaxError:
                method_body = [ast.Pass()]

            # Create method arguments
            args = [ast.arg(arg="self")]  # Always include self for methods
            for param in parameters:
                if ":" in param:
                    name, annotation = param.split(":", 1)
                    args.append(
                        ast.arg(
                            arg=name.strip(), annotation=ast.Name(id=annotation.strip())
                        )
                    )
                else:
                    args.append(ast.arg(arg=param.strip()))

            # Create method definition
            method_def = ast.FunctionDef(
                name=method_name,
                args=ast.arguments(
                    posonlyargs=[],
                    args=args,
                    kwonlyargs=[],
                    defaults=[],
                    kw_defaults=[],
                ),
                body=method_body,
                decorator_list=[],
                returns=None,
            )

            class_body.append(method_def)

        # Create class definition
        class_def = ast.ClassDef(
            name=class_name, bases=[], keywords=[], body=class_body, decorator_list=[]
        )

        ast_tree.body.append(class_def)
        return ast_tree

    async def add_variable(
        self,
        ast_tree: ast.AST,
        variable_name: str,
        value: str,
        var_type: Optional[str] = None,
    ) -> ast.AST:
        """Add variable declaration to Python AST"""
        if not isinstance(ast_tree, ast.Module):
            raise ValueError("AST must be a Module")

        # Parse the value
        try:
            value_ast = ast.parse(value, mode="eval")
        except SyntaxError:
            # If parsing fails, use the string as-is
            value_ast = ast.Constant(value=value)

        # Create assignment
        if var_type:
            # With type annotation
            annotation = ast.Name(id=var_type)
            assign = ast.AnnAssign(
                target=ast.Name(id=variable_name),
                annotation=annotation,
                value=value_ast,
                simple=1,
            )
        else:
            # Without type annotation
            assign = ast.Assign(targets=[ast.Name(id=variable_name)], value=value_ast)

        ast_tree.body.append(assign)
        return ast_tree

    async def merge_asts(self, existing_ast: ast.AST, new_ast: ast.AST) -> ast.AST:
        """Merge two Python ASTs"""
        if not isinstance(existing_ast, ast.Module) or not isinstance(
            new_ast, ast.Module
        ):
            raise ValueError("Both ASTs must be Modules")

        # Merge imports first
        existing_imports = []
        existing_others = []

        for node in existing_ast.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                existing_imports.append(node)
            else:
                existing_others.append(node)

        new_imports = []
        new_others = []

        for node in new_ast.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                new_imports.append(node)
            else:
                new_others.append(node)

        # Merge imports, avoiding duplicates
        merged_imports = existing_imports.copy()
        for new_import in new_imports:
            if not any(
                self._imports_are_equivalent(existing, new_import)
                for existing in merged_imports
            ):
                merged_imports.append(new_import)

        # Create merged AST
        merged_ast = ast.Module(
            body=merged_imports + existing_others + new_others, type_ignores=[]
        )

        return merged_ast

    async def validate_ast(self, ast_tree: ast.AST) -> bool:
        """Validate Python AST structure"""
        try:
            # Try to convert back to code to validate
            astor.to_source(ast_tree)
            return True
        except Exception as e:
            logger.error(f"AST validation failed: {e}")
            return False

    async def extract_node_info(self, ast_tree: ast.AST) -> List[ASTNodeInfo]:
        """Extract information about Python AST nodes"""
        node_info_list = []

        for node in ast.walk(ast_tree):
            info = ASTNodeInfo(
                node_type=type(node).__name__,
                line_number=getattr(node, "lineno", None),
                column=getattr(node, "col_offset", None),
            )

            # Extract name for named nodes
            if hasattr(node, "name"):
                info.name = node.name
            elif hasattr(node, "id"):
                info.name = node.id
            elif hasattr(node, "attr"):
                info.name = node.attr

            # Add specific metadata
            if isinstance(node, ast.FunctionDef):
                info.metadata = {
                    "is_async": isinstance(node, ast.AsyncFunctionDef),
                    "decorator_count": len(node.decorator_list),
                    "parameter_count": len(node.args.args),
                }
            elif isinstance(node, ast.ClassDef):
                info.metadata = {
                    "base_count": len(node.bases),
                    "decorator_count": len(node.decorator_list),
                    "method_count": len(
                        [n for n in node.body if isinstance(n, ast.FunctionDef)]
                    ),
                }
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                info.metadata = {
                    "import_type": (
                        "import" if isinstance(node, ast.Import) else "from_import"
                    )
                }

            node_info_list.append(info)

        return node_info_list

    def _imports_are_equivalent(self, import1: ast.AST, import2: ast.AST) -> bool:
        """Check if two import statements are equivalent"""
        if type(import1) != type(import2):
            return False

        if isinstance(import1, ast.Import):
            return import1.names == import2.names
        elif isinstance(import1, ast.ImportFrom):
            return (
                import1.module == import2.module
                and import1.names == import2.names
                and import1.level == import2.level
            )

        return False

    async def _apply_naming_conventions(
        self, ast_tree: ast.AST, naming_style: str
    ) -> ast.AST:
        """Apply Python naming conventions to AST"""
        if naming_style == "snake_case":
            # Convert function and variable names to snake_case
            for node in ast.walk(ast_tree):
                if isinstance(node, ast.FunctionDef) and not self._is_snake_case(
                    node.name
                ):
                    node.name = self._to_snake_case(node.name)
                elif isinstance(node, ast.Name) and not self._is_snake_case(node.id):
                    node.id = self._to_snake_case(node.id)

        return ast_tree

    async def _apply_line_length_conventions(
        self, ast_tree: ast.AST, max_length: int
    ) -> ast.AST:
        """Apply line length conventions to Python AST"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated line breaking logic
        return ast_tree

    def _is_snake_case(self, name: str) -> bool:
        """Check if a name follows snake_case convention"""
        return bool(re.match(r"^[a-z][a-z0-9_]*$", name))

    def _to_snake_case(self, name: str) -> str:
        """Convert a name to snake_case"""
        # Convert camelCase or PascalCase to snake_case
        name = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
        name = re.sub("([a-z0-9])([A-Z])", r"\1_\2", name)
        return name.lower()

    async def add_type_hints(self, ast_tree: ast.AST) -> ast.AST:
        """Add type hints to Python AST"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated type inference
        return ast_tree

    async def add_docstrings(self, ast_tree: ast.AST) -> ast.AST:
        """Add docstrings to Python AST"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated docstring generation
        return ast_tree

    async def add_error_handling(self, ast_tree: ast.AST) -> ast.AST:
        """Add error handling to Python AST"""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated error handling logic
        return ast_tree
