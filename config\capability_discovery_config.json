{"discovery_threshold": 0.8, "confidence_threshold": 0.7, "minimum_trials": 5, "capability_expiry": 86400, "auto_routing_enabled": true, "output_quality_analysis": {"text_quality": {"length_score_weight": 0.2, "structure_score_weight": 0.3, "content_score_weight": 0.3, "completeness_score_weight": 0.2}, "structured_quality": {"completeness_weight": 0.4, "content_quality_weight": 0.4, "status_weight": 0.2}}, "task_classification": {"code_generation_keywords": ["def ", "function", "class ", "import ", "return"], "instruction_following_keywords": ["instruct", "follow", "execute", "perform"], "conversation_keywords": ["chat", "conversation", "dialogue", "discussion"]}, "performance_estimation": {"response_time_estimates": {"6.7b": 3.0, "3b": 2.0, "1.5b": 1.5, "default": 2.0}, "resource_usage_estimates": {"6.7b": 0.8, "3b": 0.5, "1.5b": 0.3, "default": 0.5}}, "routing_integration": {"auto_update_rules": true, "rule_priority": "discovered", "confidence_weight": 0.8}}