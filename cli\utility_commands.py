#!/usr/bin/env python3
"""
Utility Commands - CLI interface for utility modules
Provides CLI access to JSON, config, logging, and async utilities for local LLM integration.
"""

import asyncio
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from utils import (
    AsyncUtils,
    ConfigUtils,
    JSONUtils,
    LoggingUtils,
    batch_process,
    get_config_value,
    get_logger,
    load_config_file,
    load_json_file,
    retry_with_backoff,
    save_config_file,
    save_json_file,
    setup_logging,
    validate_json_data,
)


class UtilityCommands:
    """CLI commands for utility operations"""

    def __init__(self, agent):
        self.agent = agent
        self.logger = get_logger(__name__)

    async def json_load(
        self, file_path: str, default: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Load JSON from file"""
        try:
            result = load_json_file(file_path, default)
            return {"success": True, "data": result, "file_path": file_path}
        except Exception as e:
            return {"success": False, "error": str(e), "file_path": file_path}

    async def json_save(
        self, data: Dict[str, Any], file_path: str, indent: int = 2
    ) -> Dict[str, Any]:
        """Save data to JSON file"""
        try:
            success = save_json_file(data, file_path, indent)
            return {
                "success": success,
                "file_path": file_path,
                "message": (
                    "JSON saved successfully" if success else "Failed to save JSON"
                ),
            }
        except Exception as e:
            return {"success": False, "error": str(e), "file_path": file_path}

    async def json_validate(self, data: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Validate JSON data"""
        try:
            is_valid = validate_json_data(data)
            return {
                "success": True,
                "is_valid": is_valid,
                "message": "JSON is valid" if is_valid else "JSON is invalid",
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def json_merge(
        self, target: Dict[str, Any], source: Dict[str, Any], deep_merge: bool = True
    ) -> Dict[str, Any]:
        """Merge two JSON objects"""
        try:
            result = JSONUtils.merge_json(target, source, deep_merge)
            return {"success": True, "merged_data": result, "deep_merge": deep_merge}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def config_load(
        self, config_path: str, env_prefix: str = ""
    ) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            result = load_config_file(config_path, env_prefix=env_prefix)
            return {
                "success": True,
                "config": result,
                "config_path": config_path,
                "env_prefix": env_prefix,
            }
        except Exception as e:
            return {"success": False, "error": str(e), "config_path": config_path}

    async def config_save(
        self, config: Dict[str, Any], config_path: str
    ) -> Dict[str, Any]:
        """Save configuration to file"""
        try:
            success = save_config_file(config, config_path)
            return {
                "success": success,
                "config_path": config_path,
                "message": (
                    "Configuration saved successfully"
                    if success
                    else "Failed to save configuration"
                ),
            }
        except Exception as e:
            return {"success": False, "error": str(e), "config_path": config_path}

    async def config_get_value(
        self, config: Dict[str, Any], key_path: str, default: Any = None
    ) -> Dict[str, Any]:
        """Get configuration value using dot notation"""
        try:
            value = get_config_value(config, key_path, default)
            return {
                "success": True,
                "value": value,
                "key_path": key_path,
                "found": value is not None,
            }
        except Exception as e:
            return {"success": False, "error": str(e), "key_path": key_path}

    async def config_validate(
        self, config: Dict[str, Any], required_keys: List[str]
    ) -> Dict[str, Any]:
        """Validate configuration has required keys"""
        try:
            missing_keys = ConfigUtils.validate_config(config, required_keys)
            return {
                "success": True,
                "is_valid": len(missing_keys) == 0,
                "missing_keys": missing_keys,
                "required_keys": required_keys,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def logging_setup(
        self, log_level: str = "INFO", log_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """Setup logging configuration"""
        try:
            logger = setup_logging(log_level, log_file)
            return {
                "success": True,
                "log_level": log_level,
                "log_file": log_file,
                "message": "Logging setup successfully",
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def logging_get_logger(
        self, name: str, log_level: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get a logger with optional custom level"""
        try:
            logger = get_logger(name, log_level)
            return {
                "success": True,
                "logger_name": name,
                "log_level": log_level or "INFO",
                "message": "Logger created successfully",
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def async_retry(
        self, func_name: str, max_retries: int = 3, base_delay: float = 1.0
    ) -> Dict[str, Any]:
        """Retry async function with exponential backoff"""
        try:
            # This is a demonstration - in practice, you'd pass the actual function
            async def demo_func():
                # Simulate some async operation
                await asyncio.sleep(0.1)
                return {"result": "success"}

            result = await retry_with_backoff(demo_func, max_retries, base_delay)
            return {
                "success": True,
                "result": result,
                "max_retries": max_retries,
                "base_delay": base_delay,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def async_batch_process(
        self, items: List[Any], batch_size: int = 10, max_concurrent: int = 5
    ) -> Dict[str, Any]:
        """Process items in batches with concurrency control"""
        try:

            async def demo_processor(item):
                # Simulate processing
                await asyncio.sleep(0.1)
                return f"processed_{item}"

            results = await batch_process(
                items, demo_processor, batch_size, max_concurrent
            )
            return {
                "success": True,
                "results": results,
                "total_items": len(items),
                "batch_size": batch_size,
                "max_concurrent": max_concurrent,
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def utility_status(self) -> Dict[str, Any]:
        """Get utility modules status"""
        try:
            return {
                "success": True,
                "modules": {
                    "json_utils": "available",
                    "config_utils": "available",
                    "logging_utils": "available",
                    "async_utils": "available",
                },
                "version": "1.0.0",
                "message": "All utility modules are available",
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def utility_test(self) -> Dict[str, Any]:
        """Test all utility modules"""
        try:
            test_results = {}

            # Test JSON utilities
            test_data = {"test": "data", "number": 42}
            test_results["json_save"] = await self.json_save(test_data, "test.json")
            test_results["json_load"] = await self.json_load("test.json")
            test_results["json_validate"] = await self.json_validate(test_data)

            # Test config utilities
            test_config = {"database": {"host": "localhost", "port": 5432}}
            test_results["config_save"] = await self.config_save(
                test_config, "test_config.json"
            )
            test_results["config_load"] = await self.config_load("test_config.json")
            test_results["config_get_value"] = await self.config_get_value(
                test_config, "database.host"
            )

            # Test logging utilities
            test_results["logging_setup"] = await self.logging_setup("DEBUG")
            test_results["logging_get_logger"] = await self.logging_get_logger(
                "test_logger"
            )

            # Test async utilities
            test_results["async_retry"] = await self.async_retry("demo_func")
            test_results["async_batch_process"] = await self.async_batch_process(
                [1, 2, 3, 4, 5]
            )

            # Cleanup test files
            for test_file in ["test.json", "test_config.json"]:
                if Path(test_file).exists():
                    Path(test_file).unlink()

            return {
                "success": True,
                "test_results": test_results,
                "message": "All utility modules tested successfully",
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
