#!/usr/bin/env python3
"""
ShellOpsAgent - Specialized agent for shell operations and file management tasks

Handles:
- File system operations
- CLI command execution
- File creation and modification
- Directory management
- File validation and safety checks
- Backup and restore operations
"""

import asyncio
import json
import logging
import os
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from core.agents.specialized_base_agent import BaseAgent
from core.validators.safety_validator import SafetyValidator
from models.ollama_manager import OllamaModelManager
from utils.file_manager import FileManager

logger = logging.getLogger(__name__)


class ShellOpsAgent(BaseAgent):
    """Specialized agent for shell operations and file management tasks"""

    def __init__(self, config_path: str = "config/shell_ops_agent_config.json"):
        """Initialize the ShellOpsAgent"""
        default_config = {
            "safe_directories": ["sites", "uploads", "data", "logs", "backups"],
            "allowed_commands": ["mkdir", "cp", "mv", "rm", "touch", "chmod", "chown"],
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "backup_enabled": True,
            "validation_enabled": True,
            "timeout_seconds": 30,
        }
        super().__init__(config_path, default_config)

        self.file_manager = FileManager()
        self.safety_validator = SafetyValidator()

        logger.info("ShellOpsAgent initialized successfully")

    async def execute_task(self, task_description: str, task_id: str) -> Dict[str, Any]:
        """
        Execute a shell operations task

        Args:
            task_description: Description of the shell operations task
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        try:
            logger.info(f"Executing shell operations task: {task_description}")

            # Parse task requirements
            requirements = await self._parse_shell_requirements(task_description)

            # Execute shell operations based on requirements
            results = {}

            if requirements.get("file_operations", False):
                results["file_operations"] = await self._execute_file_operations(
                    requirements, task_id
                )

            if requirements.get("directory_operations", False):
                results["directory_operations"] = (
                    await self._execute_directory_operations(requirements, task_id)
                )

            if requirements.get("cli_commands", False):
                results["cli_commands"] = await self._execute_cli_commands(
                    requirements, task_id
                )

            if requirements.get("backup_operations", False):
                results["backup_operations"] = await self._execute_backup_operations(
                    requirements, task_id
                )

            if requirements.get("validation_operations", False):
                results["validation_operations"] = (
                    await self._execute_validation_operations(requirements, task_id)
                )

            return {
                "success": True,
                "task_id": task_id,
                "shell_operations": results,
                "requirements": requirements,
                "message": f"Shell operations task completed successfully - {len(results)} subtasks executed",
            }

        except Exception as e:
            logger.error(f"Error executing shell operations task: {e}")
            return {
                "success": False,
                "task_id": task_id,
                "error": str(e),
                "message": "Shell operations task failed",
            }

    async def _parse_shell_requirements(self, task_description: str) -> Dict[str, Any]:
        """Parse shell operations requirements from task description"""
        prompt = f"""
        {self.system_prompt}

        Parse the following shell operations task and extract the requirements:

        Task: {task_description}

        Extract:
        1. File operations (e.g., create, delete, move, copy) and target file paths.
        2. Directory operations (e.g., create, delete, move) and target directory paths.
        3. CLI commands to be executed.
        4. Backup operations and target paths.
        5. Validation operations required.

        Return as a JSON object with keys: "file_operations", "directory_operations", "cli_commands", "backup_operations", "validation_operations", "files", "directories", "commands", "backup_targets".
        """

        response_dict = await self.ollama_manager.generate_response(
            prompt, model_name=self.model_name
        )
        response = response_dict.get("response", "")

        try:
            # Try to parse JSON from response
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
                return json.loads(json_str)
            else:
                # Fallback parsing
                return self._fallback_parse_requirements(task_description)
        except Exception as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            return self._fallback_parse_requirements(task_description)

    def _fallback_parse_requirements(self, task_description: str) -> Dict[str, Any]:
        """Fallback parsing for requirements"""
        # Simple keyword-based parsing for shell operations
        requirements = {
            "file_operations": False,
            "directory_operations": False,
            "cli_commands": False,
            "backup_operations": False,
            "validation_operations": False,
            "files": [],
            "directories": [],
            "commands": [],
            "backup_targets": [],
        }

        # Simple keyword-based parsing
        description_lower = task_description.lower()

        if "file" in description_lower or "create" in description_lower:
            requirements["file_operations"] = True
            requirements["files"] = self._extract_file_paths(description_lower)

        if (
            "directory" in description_lower
            or "folder" in description_lower
            or "mkdir" in description_lower
        ):
            requirements["directory_operations"] = True
            requirements["directories"] = self._extract_directory_paths(
                description_lower
            )

        if (
            "command" in description_lower
            or "cli" in description_lower
            or "shell" in description_lower
        ):
            requirements["cli_commands"] = True
            requirements["commands"] = self._extract_commands(description_lower)

        if "backup" in description_lower or "save" in description_lower:
            requirements["backup_operations"] = True
            requirements["backup_targets"] = self._extract_backup_targets(
                description_lower
            )

        if "validate" in description_lower or "check" in description_lower:
            requirements["validation_operations"] = True

        return requirements

    def _extract_file_paths(self, description: str) -> List[str]:
        """Extract file paths from description"""
        # Simple extraction - look for common file patterns
        import re

        file_patterns = [
            r"(\w+\.(py|js|ts|tsx|jsx|json|yml|yaml|md|txt|html|css))",
            r"create\s+(\w+\.\w+)",
            r"file\s+(\w+\.\w+)",
        ]

        files = []
        for pattern in file_patterns:
            matches = re.findall(pattern, description)
            files.extend(
                [match[0] if isinstance(match, tuple) else match for match in matches]
            )

        return list(set(files))

    def _extract_directory_paths(self, description: str) -> List[str]:
        """Extract directory paths from description"""
        # Simple extraction - look for directory patterns
        import re

        dir_patterns = [
            r"(\w+/+\w*)",
            r"directory\s+(\w+)",
            r"folder\s+(\w+)",
            r"mkdir\s+(\w+)",
        ]

        directories = []
        for pattern in dir_patterns:
            matches = re.findall(pattern, description)
            directories.extend(matches)

        return list(set(directories))

    def _extract_commands(self, description: str) -> List[str]:
        """Extract CLI commands from description"""
        # Simple extraction - look for command patterns
        import re

        command_patterns = [
            r"(mkdir|cp|mv|rm|touch|chmod|chown|ls|cat|echo)\s+\w+",
            r"command\s+(\w+)",
            r"run\s+(\w+)",
        ]

        commands = []
        for pattern in command_patterns:
            matches = re.findall(pattern, description)
            commands.extend(matches)

        return list(set(commands))

    def _extract_backup_targets(self, description: str) -> List[str]:
        """Extract backup targets from description"""
        # Simple extraction - look for backup patterns
        import re

        backup_patterns = [r"backup\s+(\w+)", r"save\s+(\w+)", r"(\w+)\s+backup"]

        targets = []
        for pattern in backup_patterns:
            matches = re.findall(pattern, description)
            targets.extend(matches)

        return list(set(targets))

    async def _execute_file_operations(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """Execute file operations"""
        try:
            logger.info(f"Executing file operations for task {task_id}")

            results = []
            files = requirements.get("files", [])

            for file_path in files:
                try:
                    # Validate file path safety
                    if not self.safety_validator.is_safe_path(file_path):
                        results.append(
                            {
                                "file": file_path,
                                "status": "failed",
                                "error": "Unsafe file path",
                            }
                        )
                        continue

                    # Create file if it doesn't exist
                    file_obj = Path(file_path)
                    if not file_obj.exists():
                        file_obj.parent.mkdir(parents=True, exist_ok=True)
                        file_obj.touch()
                        results.append(
                            {
                                "file": file_path,
                                "status": "created",
                                "timestamp": datetime.now().isoformat(),
                            }
                        )
                    else:
                        results.append(
                            {
                                "file": file_path,
                                "status": "exists",
                                "timestamp": datetime.now().isoformat(),
                            }
                        )

                except Exception as e:
                    results.append(
                        {"file": file_path, "status": "failed", "error": str(e)}
                    )

            return {
                "type": "file_operations",
                "status": "completed",
                "files_processed": len(files),
                "results": results,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error in file operations: {e}")
            return {
                "type": "file_operations",
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    async def _execute_directory_operations(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """Execute directory operations"""
        try:
            logger.info(f"Executing directory operations for task {task_id}")

            results = []
            directories = requirements.get("directories", [])

            for dir_path in directories:
                try:
                    # Validate directory path safety
                    if not self.safety_validator.is_safe_path(dir_path):
                        results.append(
                            {
                                "directory": dir_path,
                                "status": "failed",
                                "error": "Unsafe directory path",
                            }
                        )
                        continue

                    # Create directory if it doesn't exist
                    dir_obj = Path(dir_path)
                    if not dir_obj.exists():
                        dir_obj.mkdir(parents=True, exist_ok=True)
                        results.append(
                            {
                                "directory": dir_path,
                                "status": "created",
                                "timestamp": datetime.now().isoformat(),
                            }
                        )
                    else:
                        results.append(
                            {
                                "directory": dir_path,
                                "status": "exists",
                                "timestamp": datetime.now().isoformat(),
                            }
                        )

                except Exception as e:
                    results.append(
                        {"directory": dir_path, "status": "failed", "error": str(e)}
                    )

            return {
                "type": "directory_operations",
                "status": "completed",
                "directories_processed": len(directories),
                "results": results,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error in directory operations: {e}")
            return {
                "type": "directory_operations",
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    async def _execute_cli_commands(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """Execute CLI commands"""
        try:
            logger.info(f"Executing CLI commands for task {task_id}")

            results = []
            commands = requirements.get("commands", [])

            for command in commands:
                try:
                    # Validate command safety
                    if not self._is_safe_command(command):
                        results.append(
                            {
                                "command": command,
                                "status": "failed",
                                "error": "Unsafe command",
                            }
                        )
                        continue

                    # Execute command
                    result = await self._execute_safe_command(command)
                    results.append(
                        {
                            "command": command,
                            "status": "completed",
                            "result": result,
                            "timestamp": datetime.now().isoformat(),
                        }
                    )

                except Exception as e:
                    results.append(
                        {"command": command, "status": "failed", "error": str(e)}
                    )

            return {
                "type": "cli_commands",
                "status": "completed",
                "commands_executed": len(commands),
                "results": results,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error in CLI commands: {e}")
            return {
                "type": "cli_commands",
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    def _is_safe_command(self, command: str) -> bool:
        """Check if a command is safe to execute"""
        allowed_commands = self.config.get("allowed_commands", [])
        command_parts = command.split()

        if not command_parts:
            return False

        base_command = command_parts[0]
        return base_command in allowed_commands

    async def _execute_safe_command(self, command: str) -> Dict[str, Any]:
        """Execute a safe CLI command"""
        try:
            # Execute command with timeout
            timeout = self.config.get("timeout_seconds", 30)
            process = await asyncio.wait_for(
                asyncio.create_subprocess_exec(
                    *command.split(),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                ),
                timeout=timeout,
            )

            stdout, stderr = await process.communicate()

            return {
                "return_code": process.returncode,
                "stdout": stdout.decode() if stdout else "",
                "stderr": stderr.decode() if stderr else "",
            }

        except asyncio.TimeoutError:
            return {
                "return_code": -1,
                "stdout": "",
                "stderr": f"Command timed out after {timeout} seconds",
            }
        except Exception as e:
            return {"return_code": -1, "stdout": "", "stderr": str(e)}

    async def _execute_backup_operations(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """Execute backup operations"""
        try:
            logger.info(f"Executing backup operations for task {task_id}")

            results = []
            backup_targets = requirements.get("backup_targets", [])

            for target in backup_targets:
                try:
                    # Create backup
                    backup_result = await self.file_manager.create_backup(
                        target, task_id
                    )
                    results.append(
                        {
                            "target": target,
                            "status": "completed",
                            "backup_path": backup_result.get("backup_path"),
                            "timestamp": datetime.now().isoformat(),
                        }
                    )

                except Exception as e:
                    results.append(
                        {"target": target, "status": "failed", "error": str(e)}
                    )

            return {
                "type": "backup_operations",
                "status": "completed",
                "backups_created": len(backup_targets),
                "results": results,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error in backup operations: {e}")
            return {
                "type": "backup_operations",
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    async def _execute_validation_operations(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """Execute validation operations"""
        try:
            logger.info(f"Executing validation operations for task {task_id}")

            # Validate file system safety
            validation_result = await self.safety_validator.validate_file_system()

            return {
                "type": "validation_operations",
                "status": "completed",
                "validation_result": validation_result,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error in validation operations: {e}")
            return {
                "type": "validation_operations",
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
            }

    async def shutdown(self):
        """Shutdown the ShellOpsAgent"""
        logger.info("Shutting down ShellOpsAgent...")
        logger.info("ShellOpsAgent shutdown complete")
