"""
Bonus Features CLI Commands
Provides CLI interface for deferred tasks, clarifications, and learning features
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict

import click

from core.agents import ArchitectAgent


class BonusFeaturesCommands:
    """CLI commands for bonus features"""

    def __init__(self, agent: ArchitectAgent):
        self.agent = agent

    async def schedule_task(
        self, command: str, delay_minutes: int = 0, priority: str = "medium"
    ) -> Dict[str, Any]:
        """Schedule a task for deferred execution"""
        scheduled_at = datetime.now() + timedelta(minutes=delay_minutes)

        # Convert priority string to enum
        from core.agents import TaskPriority

        priority_enum = TaskPriority(priority.lower())

        return await self.agent.schedule_deferred_task(
            user_command=command, scheduled_at=scheduled_at, priority=priority_enum
        )

    async def get_queue_status(self) -> Dict[str, Any]:
        """Get task queue status"""
        return await self.agent.get_queue_status()

    async def get_clarifications(self) -> Dict[str, Any]:
        """Get pending clarification requests"""
        return await self.agent.get_pending_clarifications()

    async def provide_clarification(
        self, request_id: str, response: str
    ) -> Dict[str, Any]:
        """Provide clarification for a request"""
        # Parse response as JSON or use as simple string
        try:
            import json

            response_data = json.loads(response)
        except:
            response_data = {"response": response}

        return await self.agent.provide_clarification(request_id, response_data)

    async def get_learning_insights(self) -> Dict[str, Any]:
        """Get learning insights and patterns"""
        return await self.agent.get_learning_insights()

    async def get_recommendations(self, task_description: str) -> Dict[str, Any]:
        """Get pattern recommendations for a task"""
        return await self.agent.get_pattern_recommendations(task_description)

    async def provide_feedback(
        self, task_id: str, satisfaction: int, feedback_text: str = ""
    ) -> Dict[str, Any]:
        """Provide feedback for a completed task"""
        feedback = {"satisfaction": satisfaction, "feedback": feedback_text}

        return await self.agent.learn_from_feedback(task_id, feedback)


# CLI Commands
@click.group()
def bonus():
    """Bonus features for ArchitectAgent"""
    pass


@bonus.command()
@click.argument("command")
@click.option("--delay", "-d", default=0, help="Delay in minutes before execution")
@click.option(
    "--priority",
    "-p",
    default="medium",
    type=click.Choice(["low", "medium", "high", "critical"]),
    help="Task priority",
)
def schedule(command, delay, priority):
    """Schedule a task for deferred execution"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.schedule_task(command, delay, priority)

        if result["success"]:
            click.echo(f"✅ Task scheduled successfully!")
            click.echo(f"   Task ID: {result['task_id']}")
            click.echo(f"   Queue ID: {result['queue_task_id']}")
            click.echo(f"   Scheduled: {result['scheduled_at']}")
            click.echo(f"   Priority: {result['priority']}")
        else:
            click.echo(f"❌ Failed to schedule task: {result['error']}")

    asyncio.run(run())


@bonus.command()
def queue_status():
    """Get task queue status"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.get_queue_status()

        click.echo("📊 Task Queue Status:")
        click.echo(f"   Queued tasks: {result['queued_tasks']}")
        click.echo(f"   Running tasks: {result['running_tasks']}")
        click.echo(f"   Completed tasks: {result['completed_tasks']}")
        click.echo(f"   Max concurrent: {result['max_concurrent_tasks']}")
        click.echo(f"   Queue running: {'✅' if result['is_running'] else '❌'}")

    asyncio.run(run())


@bonus.command()
def clarifications():
    """Get pending clarification requests"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.get_clarifications()

        if result["count"] == 0:
            click.echo("✅ No pending clarification requests")
        else:
            click.echo(f"❓ {result['count']} pending clarification requests:")
            for clarification in result["clarifications"]:
                click.echo(f"\n   Request ID: {clarification['id']}")
                click.echo(f"   Task ID: {clarification['task_id']}")
                click.echo(f"   Type: {clarification['type']}")
                click.echo(f"   Message: {clarification['message']}")
                if clarification["options"]:
                    click.echo(f"   Options: {', '.join(clarification['options'])}")
                if clarification["required_fields"]:
                    click.echo(
                        f"   Required: {', '.join(clarification['required_fields'])}"
                    )

    asyncio.run(run())


@bonus.command()
@click.argument("request_id")
@click.argument("response")
def clarify(request_id, response):
    """Provide clarification for a request"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.provide_clarification(request_id, response)

        if result["success"]:
            click.echo(f"✅ Clarification provided successfully!")
        else:
            click.echo(f"❌ Failed to provide clarification: {result['message']}")

    asyncio.run(run())


@bonus.command()
def insights():
    """Get learning insights and patterns"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.get_learning_insights()

        if "message" in result:
            click.echo(f"ℹ️  {result['message']}")
        else:
            click.echo("🧠 Learning Insights:")
            click.echo(f"   Total tasks: {result['total_tasks']}")
            click.echo(f"   Success rate: {result['success_rate']:.1%}")

            if result["agent_performance"]:
                click.echo("\n   Agent Performance:")
                for agent, stats in result["agent_performance"].items():
                    click.echo(
                        f"     {agent}: {stats['success_rate']:.1%} success rate, {stats['avg_duration']:.1f} min avg"
                    )

            if result["top_patterns"]:
                click.echo("\n   Top Patterns:")
                for pattern in result["top_patterns"][:3]:
                    click.echo(
                        f"     {pattern['type']}: {pattern['success_rate']:.1%} success rate ({pattern['usage_count']} uses)"
                    )

    asyncio.run(run())


@bonus.command()
@click.argument("task_description")
def recommend(task_description):
    """Get pattern recommendations for a task"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.get_recommendations(task_description)

        if result["count"] == 0:
            click.echo("ℹ️  No pattern recommendations available for this task")
        else:
            click.echo(f"💡 {result['count']} pattern recommendations:")
            for i, rec in enumerate(result["recommendations"], 1):
                click.echo(f"\n   {i}. Pattern: {rec['pattern_id']}")
                click.echo(f"      Confidence: {rec['confidence']:.1%}")
                click.echo(f"      Description: {rec['description']}")
                click.echo(f"      Usage count: {rec['usage_count']}")

    asyncio.run(run())


@bonus.command()
@click.argument("task_id")
@click.argument("satisfaction", type=click.IntRange(1, 5))
@click.option("--feedback", "-f", help="Additional feedback text")
def feedback(task_id, satisfaction, feedback):
    """Provide feedback for a completed task"""

    async def run():
        agent = ArchitectAgent()
        commands = BonusFeaturesCommands(agent)
        result = await commands.provide_feedback(task_id, satisfaction, feedback or "")

        if result["success"]:
            click.echo(f"✅ Feedback recorded successfully!")
            click.echo(f"   Task ID: {result['task_id']}")
            click.echo(f"   Satisfaction: {satisfaction}/5")
            if feedback:
                click.echo(f"   Feedback: {feedback}")
        else:
            click.echo(f"❌ Failed to record feedback: {result['message']}")

    asyncio.run(run())


if __name__ == "__main__":
    bonus()
