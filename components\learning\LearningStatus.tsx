/**
 * Learning Status Component
 * Displays system status and provides controls for learning features
 */

import React, { useState } from 'react';
import { useLearningStore } from '../../store/learningStore';

export const LearningStatus: React.FC = () => {
  const {
    status,
    loading,
    runEnhancementCycle,
    enableEnhancement,
    disableEnhancement,
  } = useLearningStore();

  const [isRunningCycle, setIsRunningCycle] = useState(false);

  const handleRunCycle = async () => {
    setIsRunningCycle(true);
    try {
      await runEnhancementCycle();
    } catch (error) {
      console.error('Failed to run enhancement cycle:', error);
    } finally {
      setIsRunningCycle(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          System Status
        </h3>

        {/* Status Overview */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-500">System Status</span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              status?.data?.status === 'active'
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {status?.data?.status || 'Unknown'}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-500">Active Models</span>
            <span className="text-sm text-gray-900">
              {status?.data?.active_models || 0}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-500">Total Patterns</span>
            <span className="text-sm text-gray-900">
              {status?.data?.total_patterns || 0}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-500">Last Updated</span>
            <span className="text-sm text-gray-900">
              {status?.data?.last_updated
                ? new Date(status.data.last_updated).toLocaleString()
                : 'Never'
              }
            </span>
          </div>
        </div>

        {/* Performance Metrics */}
        {status?.data?.performance_metrics && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-500 mb-3">Performance Metrics</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Accuracy</span>
                <span className="text-sm font-medium text-gray-900">
                  {(status.data.performance_metrics.accuracy * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Response Time</span>
                <span className="text-sm font-medium text-gray-900">
                  {status.data.performance_metrics.response_time.toFixed(2)}s
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">User Satisfaction</span>
                <span className="text-sm font-medium text-gray-900">
                  {(status.data.performance_metrics.user_satisfaction * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-6 space-y-3">
          <button
            onClick={handleRunCycle}
            disabled={isRunningCycle || loading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunningCycle ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Running Enhancement Cycle...
              </>
            ) : (
              'Run Enhancement Cycle'
            )}
          </button>

          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => enableEnhancement('meta-learning')}
              disabled={loading}
              className="flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Enable Meta-Learning
            </button>
            <button
              onClick={() => disableEnhancement('meta-learning')}
              disabled={loading}
              className="flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Disable Meta-Learning
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
