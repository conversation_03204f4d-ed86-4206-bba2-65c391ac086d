import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, Optional

import requests
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse

from api.config import get_config
from api.error_routes import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from api.models import ChatRequest, ChatResponse, ErrorResponse, ModelInfo

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/chat", tags=["chat"])

# Get configuration
config = get_config()


async def call_ollama_model(prompt: str, model: str = None) -> tuple[str, float]:
    """Call Ollama model for AI responses"""
    if model is None:
        model = config.default_model

    start_time = time.time()
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={"model": model, "prompt": prompt, "stream": False},
            timeout=config.model_timeout_seconds,
        )
        processing_time = time.time() - start_time
        if response.ok:
            return response.json()["response"], processing_time
        else:
            return f"Error: {response.status_code}", processing_time
    except Exception as e:
        logger.error(f"Ollama model error: {e}")
        processing_time = time.time() - start_time
        return f"Model unavailable: {str(e)}", processing_time


def select_model_by_intent(intent: str) -> str:
    """Select appropriate model based on intent"""
    intent_lower = intent.lower()

    if "code" in intent_lower or "generation" in intent_lower:
        return "deepseek-coder:1.3b"
    elif "review" in intent_lower or "analysis" in intent_lower:
        return "yi-coder:1.5b"
    elif "content" in intent_lower or "documentation" in intent_lower:
        return "qwen2.5-coder:3b"
    elif "complex" in intent_lower or "advanced" in intent_lower:
        return "starcoder2:3b"
    elif "general" in intent_lower or "assistance" in intent_lower:
        return "mistral:7b-instruct-q4_0"
    else:
        return config.default_model


def build_enhanced_prompt(
    prompt: str, context: Dict[str, Any], intent: str, history: list
) -> str:
    """Build an enhanced prompt with context and history"""
    enhanced_prompt = f"Task: {prompt}\n"

    if intent:
        enhanced_prompt += f"Intent: {intent}\n"

    if context:
        if "file" in context:
            enhanced_prompt += f"File Context: {context['file']}\n"
        if "code" in context:
            # Preserve semantic meaning by keeping complete code blocks
            code = context["code"]
            max_length = 2000  # Increased from 1000 for better context preservation
            if len(code) > max_length:
                # Find last complete code block within limit
                truncated = code[:max_length]
                # Look for last balanced pair of braces/brackets
                last_balanced = max(
                    truncated.rfind("}"), truncated.rfind(")"), truncated.rfind("]")
                )
                if last_balanced > 0:
                    truncated = truncated[: last_balanced + 1]
                else:
                    # Fallback to line break if no balanced blocks found
                    last_newline = truncated.rfind("\n")
                    if last_newline > 0:
                        truncated = truncated[:last_newline]
                truncated += (
                    f"\n[Truncated from {len(code)} chars to {len(truncated)} chars]"
                )
            else:
                truncated = code
            enhanced_prompt += f"Code Context:\n```\n{truncated}\n```\n"

    if history:
        # Include last 3 messages for context
        recent_history = history[-3:] if len(history) > 3 else history
        enhanced_prompt += "Recent Conversation:\n"
        for msg in recent_history:
            enhanced_prompt += (
                f"- {msg.get('role', 'user')}: {msg.get('content', '')}\n"
            )

    enhanced_prompt += "\nPlease provide a helpful and accurate response."
    return enhanced_prompt


async def check_model_available(model_name: str) -> bool:
    """Check if a model is available"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.ok:
            models = response.json().get("models", [])
            return any(model["name"] == model_name for model in models)
        return False
    except Exception:
        return False


@router.post("/", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """
    Chat endpoint for AI-powered coding assistance

    - **prompt**: User's message or question
    - **context**: Additional context for the request
    - **intent**: User's intent or goal
    - **history**: Conversation history
    - **model**: AI model to use
    - **complexity_hint**: Task complexity hint
    """
    start_time = time.time()

    try:
        # Extract request data
        prompt = request.prompt
        context = request.context or {}
        intent = request.intent or ""
        history = request.history or []
        model_name = request.model or config.default_model
        complexity_hint = request.complexity_hint

        # Validate prompt
        if not prompt.strip():
            raise HTTPException(status_code=400, detail="Prompt cannot be empty")

        # Calculate complexity if not provided
        complexity_score = None
        if not complexity_hint:
            from models.model_router import TaskComplexityScorer

            complexity_level, complexity_score = (
                TaskComplexityScorer.calculate_complexity(prompt, context)
            )
            complexity_hint = complexity_level.value

        # Select appropriate model based on intent and complexity
        # Integrate with ModelRouter to use complexity hints for model selection
        from models.model_router import ModelRouter

        model_router = ModelRouter()
        model_name = model_router.select_model("general", complexity_hint)

        # Log the complexity hint
        logger.info(
            f"Task complexity: {complexity_hint} (score: {complexity_score if complexity_score is not None else 'N/A'})"
        )

        # Check model availability before proceeding
        model_available = await check_model_available(model_name)
        if not model_available:
            logger.warning(f"Model {model_name} not available, using fallback")
            model_name = config.default_model

        # Build enhanced prompt with context
        enhanced_prompt = build_enhanced_prompt(prompt, context, intent, history)

        # Call AI model
        model_start_time = time.time()
        try:
            ai_content, model_processing_time = await call_ollama_model(
                enhanced_prompt, model_name
            )
            model_used = model_name
            success = True
        except Exception as e:
            logger.warning(f"Primary model {model_name} failed, trying fallback: {e}")
            # Fallback to default model
            try:
                ai_content, model_processing_time = await call_ollama_model(
                    prompt, config.default_model
                )
                model_used = f"{config.default_model} (fallback)"
                success = True
            except Exception as fallback_error:
                logger.error(f"All models failed: {fallback_error}")
                ai_content = f"I understand you want to: {prompt}. This is a placeholder response. The AI model integration is being implemented."
                model_used = "placeholder"
                model_processing_time = 0.0
                success = False

        # Calculate total processing time
        total_processing_time = time.time() - start_time

        # Prepare response
        ai_response = {
            "content": ai_content,
            "model": model_used,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "intent": intent,
            "context_used": bool(context),
            "history_length": len(history),
            "complexity_hint": complexity_hint,  # Include complexity hint in response
        }

        return ChatResponse(
            success=True,
            response=ai_response,
            metadata={
                "model_selected": model_name,
                "prompt_length": len(prompt),
                "enhanced_prompt_length": len(enhanced_prompt),
                "processing_time": total_processing_time,  # Total endpoint processing time
                "model_processing_time": model_processing_time,  # Time spent in AI model
                "model_available": model_available,
            },
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except requests.exceptions.RequestException as req_exc:
        logger.error(f"Network error in chat endpoint: {req_exc}", exc_info=True)
        raise HTTPException(
            status_code=503,
            detail={
                "success": False,
                "error": {
                    "code": "network_error",
                    "message": "Failed to communicate with AI service",
                    "details": {
                        "exception_type": type(req_exc).__name__,
                        "exception_message": str(req_exc),
                        "endpoint": "Ollama API",
                    },
                },
                "timestamp": datetime.now().isoformat(),
                "processing_time": time.time() - start_time,
            },
        )
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": {
                    "code": "internal_server_error",
                    "message": "An unexpected error occurred",
                    "type": type(e).__name__,
                    "timestamp": datetime.now().isoformat(),
                },
                "processing_time": time.time() - start_time,
            },
        )


@router.get("/models", response_model=list[ModelInfo])
async def get_available_models():
    """
    Get list of available AI models

    Returns information about all available models including their status and capabilities.
    """
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)

        if response.ok:
            models_data = response.json().get("models", [])
            models = []

            for model_data in models_data:
                model_name = model_data.get("name", "")
                model_info = ModelInfo(
                    name=model_name,
                    type="local",
                    status=(
                        "available"
                        if model_name in config.available_models
                        else "unavailable"
                    ),
                    description=f"Local Ollama model: {model_name}",
                    capabilities=["text-generation", "code-generation"],
                )
                models.append(model_info)

            return models
        else:
            logger.error(f"Failed to get models from Ollama: {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        return []


@router.post("/test")
async def test_chat_endpoint(request: Request):
    """
    Test endpoint for chat functionality

    Simple test endpoint to verify chat service is working.
    """
    try:
        return {
            "success": True,
            "message": "Chat service is working",
            "timestamp": datetime.now().isoformat(),
            "model": config.default_model,
        }
    except Exception as e:
        logger.error(f"Test endpoint error: {e}")
        raise HTTPException(status_code=500, detail="Test endpoint failed")
