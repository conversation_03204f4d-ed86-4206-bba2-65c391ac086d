# npm PATH Troubleshooting Guide

## Issue: npm not found in PATH

If you encounter the error "npm not found" or "npm not recognized as an internal or external command", follow these steps:

## Quick Fixes

### 1. Restart Your Terminal
```bash
# Close and reopen your terminal/PowerShell
# Then try again:
npm --version
```

### 2. Check if Node.js and npm are installed
```bash
# Check Node.js
node --version

# Check npm
npm --version
```

### 3. Verify PATH Environment Variable

#### Windows:
1. Open System Properties (Win + Pause/Break)
2. Click "Advanced system settings"
3. Click "Environment Variables"
4. Under "System variables", find "Path"
5. Click "Edit"
6. Check if these paths exist:
   - `C:\Program Files\nodejs\`
   - `%AppData%\npm`
7. If missing, add them and restart your terminal

#### PowerShell (Check current PATH):
```powershell
$env:PATH -split ';' | Where-Object { $_ -like '*node*' }
```

### 4. Reinstall Node.js
1. Download from: https://nodejs.org/
2. Choose LTS version
3. Run installer as Administrator
4. Make sure "Add to PATH" is checked during installation
5. Restart your computer

## Alternative Solutions

### Use the Simplified Startup Script
```bash
# Instead of start_dev.py, use:
python start_dev_simple.py
```

### Manual Setup (Bypass npm check)
```bash
# 1. Activate virtual environment
.\.venv\Scripts\Activate.ps1

# 2. Start mock API server
python scripts/mock_api_server.py

# 3. In another terminal, try npm commands
npm install
npm run dev
```

### Use npx (if available)
```bash
# Try using npx instead of npm
npx --version
npx install
```

## Common Issues

### Issue: npm installed but not in PATH
**Solution**: Add npm to PATH manually
```bash
# Find npm location
where npm

# Add to PATH temporarily
set PATH=%PATH%;C:\path\to\npm

# Or add permanently via Environment Variables
```

### Issue: Multiple Node.js installations
**Solution**: Remove all Node.js installations and reinstall
1. Uninstall Node.js from Control Panel
2. Delete remaining folders:
   - `C:\Program Files\nodejs`
   - `C:\Program Files (x86)\nodejs`
   - `%AppData%\npm`
   - `%AppData%\npm-cache`
3. Reinstall Node.js

### Issue: PowerShell execution policy
**Solution**: Allow script execution
```powershell
# Run as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## Verification Steps

After fixing, verify everything works:

```bash
# 1. Check versions
node --version
npm --version

# 2. Test installation
npm install -g npm@latest

# 3. Test project setup
npm install
npm test

# 4. Start development
npm run dev
```

## Getting Help

If issues persist:

1. **Check Node.js documentation**: https://nodejs.org/en/docs/
2. **Check npm documentation**: https://docs.npmjs.com/
3. **Windows-specific issues**: https://github.com/nodejs/node/wiki/Installation
4. **PowerShell issues**: https://docs.microsoft.com/en-us/powershell/

## Environment Information

When reporting issues, include:
- Operating System: Windows 10/11
- Node.js version: `node --version`
- npm version: `npm --version`
- Terminal: PowerShell/Command Prompt
- Error messages: Exact text
- PATH contents: `echo $env:PATH` (PowerShell)

---

**Note**: The development environment will work even if npm is not found, as long as you can manually run npm commands in a separate terminal.
