#!/usr/bin/env python3
"""
Markdown Loader Utility
Provides functions for loading and processing markdown files
"""

import logging
import os
from pathlib import Path
from typing import List, Optional

logger = logging.getLogger(__name__)


def load_markdown(file_path: str, encoding: str = "utf-8") -> str:
    """
    Load markdown content from a file.

    Args:
        file_path: Path to the markdown file
        encoding: File encoding (default: utf-8)

    Returns:
        Content of the markdown file as string

    Raises:
        FileNotFoundError: If the file doesn't exist
        IOError: If the file can't be read
    """
    try:
        # Convert to Path object for better handling
        path = Path(file_path)

        # Check if file exists
        if not path.exists():
            raise FileNotFoundError(f"Markdown file not found: {file_path}")

        # Read the file
        with open(path, "r", encoding=encoding) as f:
            content = f.read()

        logger.info(f"Successfully loaded markdown from: {file_path}")
        return content

    except Exception as e:
        logger.error(f"Failed to load markdown from {file_path}: {e}")
        raise


def load_markdown_with_fallback(
    file_path: str, fallback_paths: Optional[List[str]] = None, encoding: str = "utf-8"
) -> str:
    """
    Load markdown content with fallback paths.

    Args:
        file_path: Primary path to the markdown file
        fallback_paths: List of fallback paths to try if primary fails
        encoding: File encoding (default: utf-8)

    Returns:
        Content of the markdown file as string

    Raises:
        FileNotFoundError: If none of the files exist
    """
    # Try primary path first
    try:
        return load_markdown(file_path, encoding)
    except FileNotFoundError:
        logger.warning(f"Primary markdown file not found: {file_path}")

    # Try fallback paths
    if fallback_paths:
        for fallback_path in fallback_paths:
            try:
                return load_markdown(fallback_path, encoding)
            except FileNotFoundError:
                logger.debug(f"Fallback markdown file not found: {fallback_path}")
                continue

    # If all paths fail, raise error
    raise FileNotFoundError(
        f"Markdown file not found at primary path {file_path} or any fallback paths"
    )


def validate_markdown_content(content: str) -> bool:
    """
    Validate markdown content for basic structure.

    Args:
        content: Markdown content to validate

    Returns:
        True if content appears to be valid markdown
    """
    if not content or not isinstance(content, str):
        return False

    # Check for basic markdown elements
    has_headers = any(line.startswith("#") for line in content.split("\n"))
    has_content = len(content.strip()) > 0

    return has_headers and has_content


def extract_markdown_sections(content: str) -> dict:
    """
    Extract sections from markdown content based on headers.

    Args:
        content: Markdown content to parse

    Returns:
        Dictionary of section names and their content
    """
    sections = {}
    current_section = "root"
    current_content = []

    for line in content.split("\n"):
        # Check if line is a header
        if line.startswith("#"):
            # Save previous section
            if current_content:
                sections[current_section] = "\n".join(current_content).strip()

            # Start new section
            current_section = line.strip("#").strip()
            current_content = []
        else:
            current_content.append(line)

    # Save last section
    if current_content:
        sections[current_section] = "\n".join(current_content).strip()

    return sections
